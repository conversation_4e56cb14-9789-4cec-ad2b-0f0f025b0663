<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>api-logic-rds-root</artifactId>
    <packaging>pom</packaging>
    <version>${api-logic-rds-version}</version>
    <modules>
        <module>api-logic-rds-module</module>
        <module>api-logic-rds-service</module>
        <module>api-logic-rds-sdk</module>
        <module>api-logic-rds</module>
        <module>api-logic-rds-internalsdk</module>
        <module>api-logic-rds-dao</module>
    </modules>

    <properties>
        <api-logic-rds-version>version</api-logic-rds-version>
    </properties>

    <parent>
        <groupId>com.baidu.bce</groupId>
        <artifactId>bce-plat-web-framework-parent</artifactId>
        <version>1.0.8.5</version>
    </parent>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.17</version>
                <configuration>
                    <forkCount>1</forkCount>
                    <reuseForks>false</reuseForks>
                    <argLine>-Dfile.encoding=UTF-8 -Xms512m -Xmx1024m -XX:MaxPermSize=512m</argLine>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <instrumentation>
                        <includes>
                            <include>com/baidu/bce/logic/rds/service/*Service.class</include>
                        </includes>
                    </instrumentation>
                    <formats>
                        <format>xml</format>
                        <!--<format>html</format>-->
                    </formats>
                    <check></check>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>baidu-nexus</id>
            <url>http://maven.scm.baidu.com:8081/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>