# 简介
1. 本模块基于公司内 WebFramework（即 bce-plat-web-framework-parent） 构建，模块和脚本文件作用参考： [WebFramework 用户指南](http://wiki.baidu.com/pages/viewpage.action?pageId=*********) 。
2. 该模块不仅需要提供 RDS 控制台 Console API，还要提供 RDS OpenAPI，而这两套接口风格差异较大，但基本业务逻辑是一样的。 
   早期，该模块仅提供一套 API（例如：InstanceController.java、AccountController.java 等）。console-rds 模块使用这套 API，
   并做一些请求、响应参数格式转换逻辑提供 RDS 控制台 Console API；OpenAPI 网关上通过 YAML 配置文件做一些请求、响应参数格式转换
   逻辑提供 RDS OpenAPI。现在，OpenAPI 网关上不做任何请求、响应参数格式转换逻辑，而在代码中增加一套 API
  （例如：OpenApiInstanceController.java、OpenApiAccountController.java 等）做转换逻辑，该项工作已完成；至于 Console API，后续会
   逐步增加一套转换 API（例如：ConsoleApiInstanceController.java、ConsoleApiAccountController.java 等）实现请求、响应参数格式转换
   逻辑，然后去掉 console-rds 模块，该项工作进行中。
3. todo