package com.baidu.bce.logic.rds.util;

import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class SignatureUtil {

    public static final String defaultEncode = "UTF-8";

    public static void attachAuthorization(String ak, String sk, HttpRequest request) {
        String timestamp = getTimestamp();
        request.getHeaders().add("x-bce-date", timestamp);

        URI uri = request.getURI();
        String path = uri.getPath();
        String query = uri.getQuery();

        Map<String, String> queryParamMap = new HashMap<>();
        if (StringUtils.isNotEmpty(query)) {
            String[] pieces = query.split("&", -1);
            for (String piece : pieces) {
                if (piece.contains("=")) {
                    String[] items = piece.split("=", -1);
                    queryParamMap.put(items[0], items[1]);
                } else {
                    queryParamMap.put(piece, "");
                }
            }
        }

        Map<String, String> headerMap = new HashMap<>();
        if (uri.getPort() == 80 || uri.getPort() == -1) {
            headerMap.put("Host", uri.getHost());
        } else {
            headerMap.put("Host", uri.getHost() + ":" + uri.getPort());
        }
        headerMap.put("x-bce-date", timestamp);

        request.getHeaders().add("Authorization", generateAuthorization(ak, sk,
                request.getMethod().toString(), path, queryParamMap, headerMap, timestamp));
    }

    public static String generateAuthorization(String accessKey, String secretKey, String method, String path,
                                               Map<String, String> queryParamMap, Map<String, String> headerMap,
                                               String timestamp) {
        String authVersion = "1";
        long expirationInSeconds = 1800; // 签名有效时间，单位：秒数

        String signingKeyStr = "bce-auth-v" + authVersion + "/" + accessKey.trim() + "/" + timestamp + "/"
                + expirationInSeconds;
        String signingKey = HmacSHA256(signingKeyStr, secretKey.trim());

        String canonicalUri = generateCanonicalUri(path);

        String canonicalQueryString = generateCanonicalQueryString(queryParamMap);

        List<String> usedHeaderStrList = generateCanonicalHeaders(headerMap);
        String canonicalHeaders = StringUtils.join(usedHeaderStrList, "\n");
        List<String> signedHeaderKeys = new ArrayList<>();
        for (String header : usedHeaderStrList) {
            signedHeaderKeys.add(header.split(":", -1)[0]);
        }

        String canonicalRequest = method.toUpperCase() + "\n" + canonicalUri + "\n" + canonicalQueryString + "\n"
                + canonicalHeaders;

        String signature = HmacSHA256(canonicalRequest, signingKey);

        return signingKeyStr + "/" + StringUtils.join(signedHeaderKeys, ";") + "/" + signature;
    }

    private static String HmacSHA256(String data, String key) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(defaultEncode), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] bytes = mac.doFinal(data.getBytes(defaultEncode));
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(Integer.toHexString((b & 0xFF) | 0x100), 1, 3);
            }
            return sb.toString().toLowerCase();
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException | InvalidKeyException e) {
            throw new RuntimeException("Occur error.", e);
        }
    }

    public static String getTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        TimeZone tz = TimeZone.getTimeZone("UTC");
        sdf.setTimeZone(tz);
        return sdf.format(new Date());
    }

    private static String normalize(String str) {
        try {
            return URLEncoder.encode(str, defaultEncode);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("Occur error.", e);
        }
    }


    private static String generateCanonicalUri(String path) {
        return path;
    }

    private static String generateCanonicalQueryString(Map<String, String> queryParamMap) {
        List<String> normalizedQueryList = new ArrayList<>();
        for (Map.Entry<String, String> entry : queryParamMap.entrySet()) {
            normalizedQueryList.add(normalize(entry.getKey()) + "=" + normalize(entry.getValue()));
        }
        Collections.sort(normalizedQueryList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.compareTo(o2);
            }
        });
        return StringUtils.join(normalizedQueryList, "&");
    }

    private static List<String> generateCanonicalHeaders(Map<String, String> headerMap) {
        List<String> usedHeaderStrList = new ArrayList<>();
        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            usedHeaderStrList.add(normalize(entry.getKey().toLowerCase()) + ":" + normalize(entry.getValue()));
        }
        Collections.sort(usedHeaderStrList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.compareTo(o2);
            }
        });
        return usedHeaderStrList;
    }

    public static Map<String, String> getAuthHeaderMap(HttpMethod method, String path, String ak, String sk,
                                                       String host, int port, Map<String, String> queryParamMap) {
        Map<String, String> headerMap = new HashMap<>();

        String timestamp = getTimestamp();
        headerMap.put("x-bce-date", timestamp);
        headerMap.put("Host", host + ":" + port);
        String authorization = generateAuthorization(ak, sk,
                method.name(), path, queryParamMap, headerMap, timestamp);
        headerMap.put("Authorization", authorization);
        return headerMap;
    }
}
