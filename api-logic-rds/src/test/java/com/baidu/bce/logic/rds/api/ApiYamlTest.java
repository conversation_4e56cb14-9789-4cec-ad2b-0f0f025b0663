package com.baidu.bce.logic.rds.api;

import com.baidu.bce.internalsdk.rds.model.performance.ProcessListResponse;
import com.baidu.gson.Gson;
import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;

import java.io.IOException;
import java.io.StringWriter;

/**
 * Created by yangxin on 2022/1/18
 */
public class ApiYamlTest {

    @Test
    public void testApiYaml() throws IOException {
        String json = "{\n" +
                "    \"datetime\": \"2021-08-23 17:54:49\",\n" +
                "    \"processList\": [{\n" +
                "        \"sql\": \"select * from test\",\n" +
                "        \"db\": \"test\",\n" +
                "        \"state\": \"Waiting on empty queue\",\n" +
                "        \"host\": \"localhost\",\n" +
                "        \"command\": \"Daemon\",\n" +
                "        \"user\": \"event_scheduler\",\n" +
                "        \"time\": 3635,\n" +
                "        \"id\": 1,\n" +
                "        \"lockHold\": {\n" +
                "            \"12757825:43:10:2\": [\n" +
                "                20151179, 2088\n" +
                "            ]\n" +
                "\n" +
                "        },\n" +
                "        \"lockWait\": [{\n" +
                "            \"lockId\": \"12757825:43:10:2\",\n" +
                "            \"id\": 20151178\n" +
                "        }]\n" +
                "\n" +
                "    }]\n" +
                "}";
        ProcessListResponse processListResp = new Gson().fromJson(json, ProcessListResponse.class);

        StringWriter writer = getMustacheWriter(processListResp);

        String html = writer.toString();
        System.out.println(html);
    }

    @NotNull
    private StringWriter getMustacheWriter(ProcessListResponse processListResp) throws IOException {
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache m = mf.compile("api.yml");
        StringWriter writer = new StringWriter();
        m.execute(writer, processListResp).flush();
        return writer;
    }
}
