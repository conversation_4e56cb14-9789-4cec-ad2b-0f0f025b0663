package com.baidu.bce.logic.rds.service;

import com.alibaba.fastjson.JSON;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceInnodbStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceProcesslistResponse;
import com.baidu.bce.internalsdk.rds.model.performance.ConnectionListResponse;
import com.baidu.bce.internalsdk.rds.model.performance.KillProcessRequest;
import com.baidu.bce.internalsdk.rds.model.performance.TransactionListResponse;
import com.baidu.bce.logic.rds.AbstractSpringTest;
import static com.baidu.bce.logic.rds.util.SignatureUtil.attachAuthorization;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.util.Arrays;

/**
 * 本地环境测试用例，在办公网络运行；
 * 因为有些下游依赖的接口 BSG 网关设置中不允许办公网络访问，所以有些测试用例需要使用沙盒环境接口；
 */
public class PerformanceServiceTest extends AbstractSpringTest {

    private static final String instanceId = "rdsmhs8v2hru7he";

    static {
        restTemplate.getInterceptors().add(new ClientHttpRequestInterceptor() {
            @Override
            public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
                    throws IOException {
                attachAuthorization(zxrAccessKey, zxrSecretKey, request);
                return execution.execute(request, body);
            }
        });

    }

    @Autowired
    private PerformanceService performanceService;

    @Test
    public void testProcesslist() {
        InstanceProcesslistResponse processlist = performanceService.getProcesslist(instanceId);
        assertNotNull(processlist.getProcessList());
        assertNotNull(processlist.getProcessList());
        assertTrue(processlist.getProcessList().size() > 0);
        System.out.println(JSON.toJSONString(processlist));
    }

    @Test
    public void testKillProcess() {
        KillProcessRequest request = new KillProcessRequest();
        request.setIds(Arrays.asList(110325, 110327));
        performanceService.killProcess(instanceId, request);
    }

    @Test
    public void testGetInnodbStatus() {
        InstanceInnodbStatusResponse innodbstatus = performanceService.getInnodbstatus(instanceId);
        assertNotNull(innodbstatus.getStatus());
        assertNotNull(innodbstatus.getDatetime());
        assertNotNull(innodbstatus.getName());
        assertNotNull(innodbstatus.getType());
    }

    @Test
    public void testGetConnectionList() {
        ConnectionListResponse connectionList = performanceService.getConnectionList(instanceId);
        assertNotNull(connectionList.getDatetime());
        assertNotNull(connectionList.getConnectList());
        assertTrue(connectionList.getConnectList().size() > 0);
    }

    @Test
    public void testGetTransactionList() {
        TransactionListResponse transactionList = performanceService.getTransactionList(instanceId);
        assertNotNull(transactionList.getDatetime());
        assertNotNull(transactionList.getInnodbTrxList());
        assertTrue(transactionList.getInnodbTrxList().size() > 0);
        System.out.println(JSON.toJSONString(transactionList));
    }

}
