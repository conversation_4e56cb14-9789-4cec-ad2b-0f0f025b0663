package com.baidu.bce.logic.rds;

import com.alibaba.fastjson.JSON;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.util.SignatureUtil;
import com.baidu.bce.logic.rds.util.TestConstant;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.IntegrationTest;
import org.springframework.boot.test.SpringApplicationConfiguration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.WebApplicationContext;

import java.lang.reflect.Type;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringApplicationConfiguration(classes = LogicRdsApplication.class)
@WebAppConfiguration
@IntegrationTest
public class AbstractSpringTest {
    protected static final Logger LOGGER = LoggerFactory.getLogger(AbstractSpringTest.class);
    // zxr account
    protected static final String zxrAccessKey = "90fe9f5e10044bf5906ea7d0e5277039";
    protected static final String zxrSecretKey = "3ad6341a59b1497ab9f90a683bad8d8f";
    protected static final String zxrAccountId = "399d119edd1d4c748c2185714636db20";
    protected static final RestTemplate restTemplate = new RestTemplate();
    // gxl account
    private static final String rootUserAccessKey = "27ea5e62ec81405cb2d5d16ec927ec3d";
    private static final String rootUserSecretKey = "497d4a7de0124887966d948bbfc25b3f";
    // zyh account
    private static final String accessKey = "e5207f9d1fa2477da9c059d30abb42cf";
    private static final String secretKey = "78876e06041f4cfe816b8731032bd5da";
    // syx account
    private static final String syxAccessKey = "9244dd455c9e42e7b153ce9eac2c6a30";
    private static final String syxSecretKey = "ac46679d80aa48c88b034b6cf8ecbabf";
    private static final String testServerHost = "*************:8796";

    protected static String serverHost = "localhost:8796";

    private MockMvc mockMvc;
    private ClientHttpRequestInterceptor authInterceptor;
    @Autowired
    private WebApplicationContext webApplicationContext;

    @Before
    public void setup() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .build();

        MockedStatic<LogicUserService> mockUserSerivce = Mockito.mockStatic(LogicUserService.class);
        // mockUserSerivce.when(LogicUserService::getAccountId).thenReturn(zxrAccountId);
        assert LogicUserService.getAccountId().equals(zxrAccountId);
    }

    protected <T> T get(String url, Type clazz) throws Exception {
        HashMap<String, String> params = new HashMap<>(1);
        params.put("manner", "page");
        String respContent = mockMvc.perform(MockMvcRequestBuilders
                        .get(url)
                        .param("manner", "page")
                        .headers(getAuthHeaders(HttpMethod.GET, url, params))
                        // 设置返回值类型为utf-8，否则默认为ISO-8859-1
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        return JSON.parseObject(respContent, clazz);
    }

    protected String get(String url) throws Exception {
        HashMap<String, String> params = new HashMap<>(1);
        params.put("manner", "page");
        return mockMvc.perform(MockMvcRequestBuilders
                        .get(url)
                        .param("manner", "page")
                        .headers(getAuthHeaders(HttpMethod.GET, url, params))
                        // 设置返回值类型为utf-8，否则默认为ISO-8859-1
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
    }

    protected String get(String url, String body) throws Exception {
        HashMap<String, String> params = new HashMap<>(1);
        params.put("manner", "page");
        params.put("poolId", TestConstant.POOL_ID_MOCK);
        return mockMvc.perform(MockMvcRequestBuilders
                        .get(url)
                        .param("manner", "page")
                        .param("poolId", TestConstant.POOL_ID_MOCK)
                        .headers(getAuthHeaders(HttpMethod.GET, url, params))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(body)
                        // 设置返回值类型为utf-8，否则默认为ISO-8859-1
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
    }

    protected <T> T post(String url, String json, Class<T> clazz) throws Exception {
        String resp = mockMvc.perform(MockMvcRequestBuilders
                        .post(url)
                        .headers(getAuthHeaders(HttpMethod.POST, url, null))
                        // 设置返回值类型为utf-8，否则默认为ISO-8859-1
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(json))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn()
                .getResponse()
                .getContentAsString();
        return JSON.parseObject(resp, clazz);
    }

    protected String post(String url, String json) throws Exception {
        return mockMvc.perform(MockMvcRequestBuilders
                        .post(url)
                        .headers(getAuthHeaders(HttpMethod.POST, url, null))
                        // 设置返回值类型为utf-8，否则默认为ISO-8859-1
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(json))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn()
                .getResponse()
                .getContentAsString();
    }

    protected void put(String url, String json) throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                        .put(url)
                        .headers(getAuthHeaders(HttpMethod.PUT, url, null))
                        // 设置返回值类型为utf-8，否则默认为ISO-8859-1
                        .accept(MediaType.APPLICATION_JSON_VALUE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(json))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print());
    }

    protected void delete(String url) throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                        .delete(url)
                        .headers(getAuthHeaders(HttpMethod.DELETE, url, null))
                        // 设置返回值类型为utf-8，否则默认为ISO-8859-1
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print());
    }

    private HttpHeaders getAuthHeaders(HttpMethod method, String url, Map<String, String> params) {
        HttpHeaders headers = new HttpHeaders();
        if (params == null) {
            params = Collections.emptyMap();
        }
        Map<String, String> headerMap = SignatureUtil.getAuthHeaderMap(
                method,
                url,
                TestConstant.ACCESS_KEY,
                TestConstant.SECRET_KEY,
                TestConstant.HOST,
                TestConstant.PORT,
                params
        );
        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            headers.add(entry.getKey(), entry.getValue());
        }
        return headers;
    }
}
