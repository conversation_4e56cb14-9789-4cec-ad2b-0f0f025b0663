server-host=qasandbox.bcetest.baidu.com
server.port=8796
swagger.start=false
swagger.app.docs=http://${server-host}:${server.port}
bce_plat_web_framework.is_web_application=false
iam.signature.headers=host;x-bce-accesskey;x-bce-console-rpc-id;x-bce-date;x-bce-request-id;x-bce-secretkey;
rsa.public.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI8qaFHtgi9yltIA3qYy2OU/91AC+yLVJlMLa0AR6eUae1V8hXjXFocWwWSreN9pzjFoEJXiGPj9jsE6Bh5FXpcCAwEAAQ==
rsa.private.key=MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAjypoUe2CL3KW0gDepjLY5T/3UAL7ItUmUwtrQBHp5Rp7VXyFeNcWhxbBZKt432nOMWgQleIY+P2OwToGHkVelwIDAQABAkAqLsaaDS8kp9DEg7kWozjBI33NN3OjqcYnBeBN+nk+JYRGtUWVYEhmR7b2GgTeXcolTuLmB00v9JsmbfvYDoKRAiEAzTqLkcj94YLjLPRilXODpz7F72QbLqFW3RU03279ET0CIQCylVAVPvoWn381wcBnbvCDOyn9EbfP+FVVBAHwb1nEYwIgDFTyQgZTyxM0V2Uv708LoCcTebkIMRscvxghHzPqHPkCIFYO+j6i0KXiSs0/B1dQ8Ppsonlf9nJ0O7ryaXTSVDH9AiB8DU3YJbKE1pY0jabmvO127JTbs52m3LVdmSxVddG8Og==
#=================== login & access ===================#
login.url=http://localhost:8088/uc/login
cookie.domain=localhost
login.cookie.md5.key=19920908
login.urls.not.need.auth=/asset/**;/dep/**;/esl.js;/swagger/**
login.urls.need.auth=/**
passport.appid=1240
passport.session.endpoint=http://localhost:8088/passport
uc.app.id=285
uc.server=localhost:8888
iam.access.failed.jump.url=http://localhost:8088/user/mockaccess?redirect=http://localhost:8080
iam.console.username=logic_rds
iam.console.password=B1g0Fna3IO7R80nczgxrF1Uz3uyajqgp
iam.access.paths=/**
iam.access.exclude.paths=
iam.permission.is.need.check.valid=false
bce.plat.iam.access=false
#======================================================#
#=================== logging config ===================#
logging.requestId_urlPattern=/*
logging.log_pattern=%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID:- } [%t] --- %logger [%F:%L] : [%X{x-bce-request-id}][%X{currentUser}] %m%n}
logging.has_console_appender=true
logging.has_web_debug_appender=true
logging.web_debug_path=/debug
logging.web_debug.level=INFO
### logback rolling log, uncomment to open appender ###
logging.info_log_file_path=
logging.error_log_file_path=../log/error/api-logic-rds.error.log
logging.warn_log_file_path=../log/warn/api-logic-rds.warn.log
logging.debug_log_file_path=../log/debug/api-logic-rds.debug.log
### access log, uncomment to open appender ###
logging.access_debug_uri_prefix=/
logging.access_log_file_path=../log/access/api-logic-rds.access.log
logging.access_debug_log_file_path=../log/access_debug/api-logic-rds.access_debug.log
#======================================================#
#=================== logical database config begin ===================#
database.enable=false
database.logic.enable=true
database.mybatis.enable=true
database.isEmbedded=false
database.logic.url=*********************************************************************************************************
database.logic.username=bce_logical
database.logic.password=p+K@H,pH$x
database.script=
database.logic.mapper.locations=classpath*:*Mapper.xml
#=================== database config end ===================#
region.currentRegion=bj
#================log monitor=================#
monitor.latencyRecorder.enable=true
bce.logic.enable.debug=true
#=================zone init==========================#
bce.logical.pz.default=AZONE-nmg02
bcc.postpay.quota=20
instance.postpay.bj.quota=400
instance.postpay.gz.quota=500
#=================== httpClient  ===================#
httpClient.maxConnTotal=800
httpClient.maxConnPerRoute=800
#=================== tomcat thread pool  ===================#
tomcat.maxThreads=300
tomcat.minSpareThreads=300
#=================== function enable  ===================#
snapshot.create.enable=true
#=================== status read from backend or local enable  ===================#
bce.status.read.local=true
spring.velocity.checkTemplateLocation=false
#========================== rds   ==========================#
rds.unavailable.physicalzone:
#AZONE-szth,AZONE-cq02,AZONE-bjyz,AZONE-gzns,AZONE-gzhxy,AZONE-gzhk,AZONE-fsh#线上＃
#AZONE-nmg02,AZONE-dbl#沙盒#
nonOrder.dataSync.scheduler.switch:true
rds.service.accountId=f3e8b480040848a9b9877c74a923ddd8
rds.service.policyId=d1f074c112174c35b88937bd6a33395d
console.rds.service.accountId=690e9e23fb1c4b6ca7ace2b027d05a61
console.rds.service.policyId=8be37f469d594738961a26ecb528a305
#======================plat log ============================#
plat.log.module.name:logic-rds
plat.log.format.request.in:true
plat.log.format.request.out:true
#===========================================================#
#======================= permission============================#
bce.logical.permissionvertify.enabled:false
#=======================financial support=====================#
rds.financial.region:bj,fwh
resource.kafka.server=kafka.bj.baidubce.com:9091
resource.ssl.truststore.password=kafka
resource.ssl.truststore.location=../conf/jks/client.truststore.jks
resource.ssl.keystore.password=hklmfd1i
resource.ssl.keystore.location=../conf/jks/client.keystore.jks
resource.kafka.topic=kafka.topic=c42867cb52124ef4bfe51d315eea6f68__res_manager_sandbox
#======================================= API Idempotent
bce.logical.vpc.common.userToken.enable=true
bce.vpc.logical.authentication.paths=/api/**;/v1/**

logic.rds.data.sync=false