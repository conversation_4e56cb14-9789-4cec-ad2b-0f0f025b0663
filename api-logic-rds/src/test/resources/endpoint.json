{"regions": [{"region": "default", "services": [{"service": "IAM", "endpoint": "http://iam.bj.internal-qasandbox.baidu-int.com/v3"}, {"service": "STS", "endpoint": "http://sts.bj.internal-qasandbox.baidu-int.com:8586/v1"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://pricing.internal-qasandbox.bce-internal.baidu-int.com"}, {"service": "Order", "endpoint": "http://order.internal-qasandbox.bce-internal.baidu-int.com/orders"}, {"service": "OrderV2", "endpoint": "http://order.internal-qasandbox.bce-internal.baidu-int.com/v2"}, {"service": "Resource", "endpoint": "http://order.internal-qasandbox.bce-internal.baidu-int.com/resources"}, {"service": "Finance", "endpoint": "http://bjyz-y22-sandbox002.bjyz.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://logic-bcc.internal-qasandbox.baidu-int.com"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://blb.bj.qasandbox.baidu-int.com/v1"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://gzhxy-y32-sandbox019.gzhxy.baidu.com:8099/internal-api/v1"}, {"service": "RDS", "endpoint": "http://gzhxy-y32-sandbox019.gzhxy.baidu.com:8099/json-api/v1"}, {"service": "RDS2", "endpoint": "http://gzhxy-y32-sandbox019.gzhxy.baidu.com:8099/json-api/v2"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://qasandbox.bcetest.baidu.com"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://osp.bcetest.baidu.com/product/v1"}, {"service": "Quota_Logical", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://user-config.internal-qasandbox.baidu-int.com:8690/v1"}, {"service": "Discount", "endpoint": "http://bjyz-y22-sandbox001.bjyz.baidu.com:8663/v1"}, {"service": "DiscountV2", "endpoint": "http://bjyz-y22-sandbox001.bjyz.baidu.com:8663/v2"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://blb.bce-new-sandbox.baidu.com:80/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://order-sandbox.baidu-int.com"}, {"service": "ServiceCatalog", "endpoint": "http://facade.internal-qasandbox.baidu-int.com:8993/v1"}, {"service": "Risk", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8611"}, {"service": "zone", "endpoint": "http://logic-zone.bj.internal-qasandbox.baidu-int.com:8795"}, {"service": "LOGICAL_VPC", "endpoint": "http://logical-vpc.internal-qasandbox.baidu-int.com"}, {"service": "EIP_API", "endpoint": "http://blb.bce-new-sandbox.baidu.com:80/meta-server/index.php/open-api/v1"}, {"service": "CRM", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8531/v1"}, {"service": "BccProxy", "endpoint": "http://*************:20001"}, {"service": "auto-renew", "endpoint": "http://renew.internal-qasandbox.baidu-int.com:8986"}, {"service": "MKT", "endpoint": "http://cp01-bce-cloud-market.epc.baidu.com:8078"}, {"service": "LogicalTag", "endpoint": "http://logic-tag.internal-qasandbox.baidu-int.com:8777"}, {"service": "ApiService", "endpoint": "http://api.bcetest.baidu.com/v1"}, {"service": "RDSV2", "endpoint": "http://gzhxy-y32-sandbox016.gzhxy.baidu.com:8085/api/v1"}, {"service": "<PERSON><PERSON><PERSON>y", "endpoint": "http://*************:8868"}, {"service": "BpResourceManager", "endpoint": "http://cq02-bce-offline-sandbox.cq02.baidu.com:8671"}, {"service": "RDS-san_box", "endpoint": "http://*************:8796"}, {"service": "ResourceManager", "endpoint": "http://bjhw-sys-rpm8059.bjhw.baidu.com:8848"}]}, {"region": "bj", "services": [{"service": "IAM", "endpoint": "http://iam.bj.internal-qasandbox.baidu-int.com/v3"}, {"service": "STS", "endpoint": "http://sts.bj.internal-qasandbox.baidu-int.com:8586/v1"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://pricing.internal-qasandbox.bce-internal.baidu-int.com"}, {"service": "Order", "endpoint": "http://order.internal-qasandbox.bce-internal.baidu-int.com/orders"}, {"service": "OrderV2", "endpoint": "http://order.internal-qasandbox.bce-internal.baidu-int.com/v2"}, {"service": "Resource", "endpoint": "http://order.internal-qasandbox.bce-internal.baidu-int.com/resources"}, {"service": "Finance", "endpoint": "http://bjyz-y22-sandbox002.bjyz.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://logic-bcc.internal-qasandbox.baidu-int.com"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://blb.bj.qasandbox.baidu-int.com/v1"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://gzhxy-y32-sandbox019.gzhxy.baidu.com:8099/internal-api/v1"}, {"service": "RDS", "endpoint": "http://gzhxy-y32-sandbox019.gzhxy.baidu.com:8099/json-api/v1"}, {"service": "RDS2", "endpoint": "http://gzhxy-y32-sandbox019.gzhxy.baidu.com:8099/json-api/v2"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://qasandbox.bcetest.baidu.com"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://osp.bcetest.baidu.com/product/v1"}, {"service": "Quota_Logical", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://user-config.internal-qasandbox.baidu-int.com:8690/v1"}, {"service": "Discount", "endpoint": "http://bjyz-y22-sandbox001.bjyz.baidu.com:8663/v1"}, {"service": "DiscountV2", "endpoint": "http://bjyz-y22-sandbox001.bjyz.baidu.com:8663/v2"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://blb.bce-new-sandbox.baidu.com:80/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://order-sandbox.baidu-int.com"}, {"service": "ServiceCatalog", "endpoint": "http://facade.internal-qasandbox.baidu-int.com:8993/v1"}, {"service": "Risk", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8611"}, {"service": "zone", "endpoint": "http://logic-zone.bj.internal-qasandbox.baidu-int.com:8795"}, {"service": "LOGICAL_VPC", "endpoint": "http://logical-vpc.internal-qasandbox.baidu-int.com"}, {"service": "EIP_API", "endpoint": "http://blb.bce-new-sandbox.baidu.com:80/meta-server/index.php/open-api/v1"}, {"service": "CRM", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8531/v1"}, {"service": "BccProxy", "endpoint": "http://*************:20001"}, {"service": "auto-renew", "endpoint": "http://renew.internal-qasandbox.baidu-int.com:8986"}, {"service": "MKT", "endpoint": "http://cp01-bce-cloud-market.epc.baidu.com:8078"}, {"service": "LogicalTag", "endpoint": "http://logic-tag.internal-qasandbox.baidu-int.com:8777"}, {"service": "ApiService", "endpoint": "http://api.bcetest.baidu.com/v1"}, {"service": "RDSV2", "endpoint": "http://gzhxy-y32-sandbox016.gzhxy.baidu.com:8085/api/v1"}, {"service": "<PERSON><PERSON><PERSON>y", "endpoint": "http://*************:8868"}, {"service": "BpResourceManager", "endpoint": "http://cq02-bce-offline-sandbox.cq02.baidu.com:8671"}, {"service": "RDS-san_box", "endpoint": "http://*************:8796"}, {"service": "ResourceManager", "endpoint": "http://bjhw-sys-rpm8059.bjhw.baidu.com:8848"}]}, {"region": "gz", "services": [{"service": "IAM", "endpoint": "http://10.58.144.15:35357/v3"}, {"service": "STS", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8586/v1"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8006"}, {"service": "Order", "endpoint": "http://order-sandbox.baidu-int.com/orders"}, {"service": "OrderV2", "endpoint": "http://order-sandbox.baidu-int.com/v2"}, {"service": "Resource", "endpoint": "http://order-sandbox.baidu-int.com/resources"}, {"service": "Finance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://nmg02-bce-test92.nmg02.baidu.com:8774/v2"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://blb.bj.qasandbox.baidu-int.com/v1"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/internal-api/v1"}, {"service": "RDS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v1"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://qasandbox.bcetest.baidu.com"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://osp.bcetest.baidu.com/product/v1"}, {"service": "Quota_Logical", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://************:8690/v1"}, {"service": "Discount", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v1"}, {"service": "DiscountV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v2"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://mockserver.baidu.com:8888/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://order-sandbox.baidu-int.com"}, {"service": "ServiceCatalog", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8993/v1"}, {"service": "Risk", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8611"}, {"service": "zone", "endpoint": "http://zone.api-gz-sandbox.baidu.com"}, {"service": "LOGICAL_VPC", "endpoint": "http://vpc.bce-testinternal.baidu.com"}, {"service": "EIP_API", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8087/meta-server/index.php/open-api/v1"}, {"service": "CRM", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8531/v1"}, {"service": "BccProxy", "endpoint": "http://*************:20001"}, {"service": "auto-renew", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8986"}, {"service": "MKT", "endpoint": "http://cp01-bce-cloud-market.epc.baidu.com:8078"}, {"service": "LogicalTag", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8777"}, {"service": "ApiService", "endpoint": "http://api.bcetest.baidu.com/v1"}, {"service": "RDSV2", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8085/api/v1"}, {"service": "<PERSON><PERSON><PERSON>y", "endpoint": "http://*************:8868"}, {"service": "BpResourceManager", "endpoint": "http://cq02-bce-offline-sandbox.cq02.baidu.com:8671"}]}, {"region": "fwh", "services": [{"service": "IAM", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:35357/v3"}, {"service": "STS", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8586/v1"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8006"}, {"service": "Order", "endpoint": "http://order-sandbox.baidu-int.com/orders"}, {"service": "OrderV2", "endpoint": "http://order-sandbox.baidu-int.com/v2"}, {"service": "Resource", "endpoint": "http://order-sandbox.baidu-int.com/resources"}, {"service": "Finance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://nmg02-bce-test92.nmg02.baidu.com:8774/v2"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://blb.bj.qasandbox.baidu-int.com/v1"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/internal-api/v1"}, {"service": "RDS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v1"}, {"service": "RDS2", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v2"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://qasandbox.bcetest.baidu.com"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://osp.bcetest.baidu.com/product/v1"}, {"service": "Quota_Logical", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://************:8690/v1"}, {"service": "Discount", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v1"}, {"service": "DiscountV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v2"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://blb.bce-new-sandbox.baidu.com:80/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://order-sandbox.baidu-int.com"}, {"service": "ServiceCatalog", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8993/v1"}, {"service": "Risk", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8611"}, {"service": "zone", "endpoint": "http://zone.api-sandbox.baidu.com"}, {"service": "LOGICAL_VPC", "endpoint": "http://vpc.bce-testinternal.baidu.com"}, {"service": "EIP_API", "endpoint": "http://blb.bce-new-sandbox.baidu.com:80/meta-server/index.php/open-api/v1"}, {"service": "CRM", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8531/v1"}, {"service": "BccProxy", "endpoint": "http://*************:20001"}, {"service": "auto-renew", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8986"}, {"service": "MKT", "endpoint": "http://cp01-bce-cloud-market.epc.baidu.com:8078"}, {"service": "LogicalTag", "endpoint": "http://logic-tag.internal-qasandbox.baidu-int.com:8777"}, {"service": "ApiService", "endpoint": "http://api.bcetest.baidu.com/v1"}, {"service": "RDSV2", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8085/api/v1"}, {"service": "<PERSON><PERSON><PERSON>y", "endpoint": "http://*************:8868"}, {"service": "BpResourceManager", "endpoint": "http://cq02-bce-offline-sandbox.cq02.baidu.com:8671"}]}]}