exception.other.service.MissingParameter=缺少必要参数
exception.other.service.AccessDenied=您暂无此实例查看和操作权限
exception.other.service.ResourceInTask=该资源有订单未完成支付，请先取消相关订单或稍后再试!
exception.other.service.InvalidInstanceStatus=实例状态异常，无法执行该操作
exception.other.service.RDSGroupNotFound=RDS实例组未找到.
exception.other.service.InstanceNotFound=找不到实例或状态异常
exception.other.service.InternalDBError=内部数据库错误
exception.other.service.OrderExceptions.ResourceInTaskException=该资源有订单未完成支付，请先取消相关订单或稍后再试!
exception.other.service.InstanceContainsUppercaseDb=实例存在大写的库名或表名, 请将其改为小写后再修改lower_case_table_names
exception.other.service.RDSInstanceNotFound=实例不存在
exception.other.service.GroupNameValidationError=白名单分组名称重复，请检查后重试！
exception.other.service.InvalidParameterValue=参数无效
exception.other.service.InvalidAction=无效操作
exception.other.service.AlreadyMaxLimit=白名单分组已经到达上限
exception.other.service.DBINSTANCE_NOT_FOUND=实例不存在
exception.other.service.DBINSTANCE_STATE_CHANGE=实例状态不允许
exception.other.service.ValidationError=参数格式错误
exception.other.service.PreconditionFailed=版本不匹配
exception.other.service.InternalFailure =内部服务错误
exception.other.service.SubnetNotFoundException=当前子网不存在
exception.other.service.DBError=数据库错误
exception.other.service.ServiceInternalError=内部服务错误
exception.other.service.NotEnoughBalanceForPayOrder=当前账户余额不足
exception.other.service.InsufficientBalance=当前账户余额不足
# RDS 后端 账号管理
exception.other.service.AccountDependent=检查该账号是否被依赖！
# RDS 后端 数据库管理
exception.other.service.DatabaseAlreadyExists=数据库已存在
exception.other.service.InsufficientInstanceDiskSizeToRestoreSnapshot=实例磁盘容量不足以恢复此备份集，请先扩容磁盘
exception.other.service.ParamValidationFailed=参数校验失败
exception.other.service.CpuCountValidationException=当前所选计算资源配置校验失败
exception.other.service.EngineValidationException=引擎类型校验失败
exception.other.service.InternalError=内部服务错误
exception.other.service.EngineVersionValidationException=引擎类型校验失败
exception.other.service.DatabaseNameReserved=数据库名称已保留
exception.other.service.JoinRDSGroupPreCheckFailed=加入热活组前置检查失败，当前实例存在数据
exception.other.service.RdsGroupMinorVersionCheckException=实例加入热活组小版本前置检查失败
exception.other.service.HttpMessageNotReadable=请求数据格式异常
exception.other.service.DilatationExpireException=资源将在24小时后过期，无法进行该操作
exception.other.service.InstanceStatusErrorException=实例状态非运行中
exception.other.service.AccountAlreadyExists=当前账号已存在
# 公共服务
exception.other.service.SessionTokenExpired=登录状态已过期，请重新登录
exception.other.service.PriceQueryError=资源已过期
exception.other.service.NotSupportOperation=当前不允许执行该操作
exception.other.service.InstanceAlreadyDeleted=实例已删除
exception.other.service.InvalidRequest=无效请求
exception.rds.InvalidAction=无效操作
exception.rds.InstanceNotExist=实例不存在
exception.rds.ResourceNotExistException=资源不存在，请稍后重试或联系管理员
exception.rds.InvalidInstanceStatus=实例状态异常
exception.rds.HasChangeBillingException=该实例正在进行计费变更，请取消后重试！
exception.rds.ParamValidationException=参数检验失败
exception.rds.PrecheckResourceFailed=子网IP剩余不足
exception.rds.PrecheckResourceException=子网IP剩余不足
exception.rds.MissingParameterException=缺少参数
exception.rds.InstanceNotSatisfiableException=实例状态不满足，无法执行该操作
exception.rds.InvalidParameterException=无效的参数
exception.rds.InstanceStatusErrorException=实例状态非available
exception.rds.InternalDBErrorException=内部更新DB错误
exception.rds.MinorVersionMissException=未找到小版本的映射关系
exception.rds.RdsGroupMinorVersionCheckException=实例加入热活组小版本前置检查失败
exception.rds.InsufficientBalanceException=该账户余额不足或已欠费，无法进行该操作！
exception.rds.WrongFormatUrlExcption=url格式错误
exception.rds.RegionNotExistException=当前请求的区域不存在，请检查后重试！
exception.rds.AccessDenied=您暂无此实例查看和操作权限
exception.rds.RDSInstanceNotFound=实例不存在
exception.rds.PermissionDeny=请求被拒绝，您暂无权限
exception.rds.RdsPermissionDenyException=请求被拒绝，您暂无权限
exception.rds.InstanceShrinkageEngineException=当前引擎类型不支持该操作
exception.rds.InstanceShrinkageSizeException=存储降配时，目标存储容量必须大于等于使用量 * 1.2
exception.rds.InstanceShrinkageUpperReplicaException=双机主实例存储降配时，目标存储容量必须小于等于只读实例存储容量
exception.rds.InstanceShrinkageLowerMasterException=只读实例存储降配时，目标存储容量必须大于等于主实例存储容量
exception.rds.ResizeMasterReplicaDiskException=主实例存储容量应小于等于只读实例存储容量
exception.rds.ResizeMasterMemoryException=PG主实例内存应小于等于只读实例内存
exception.rds.ResizeReplicaMemoryException=PG只读实例内存应大于等于主实例内存
exception.rds.DeleteAutomatedBackupException=禁止删除自动备份的实例数据
exception.rds.ApplicationTypeException=当前数据库系列类型不支持该操作
exception.rds.MultiMasterResizeException=当前批量变配操作存在多个主实例
exception.rds.ApiDirectPayException=批量变配时预付费订单直接支付失败
exception.rds.DiskSizeInvalidException=实例（{0}）存储大小参数设置无效
exception.rds.ParameterTopoErrorException=实例（{0}）不属于此集群
exception.rds.BackupParameterException=当前操作不支持逻辑备份
exception.rds.GeneralInstanceNotSupportException=当前通用型实例不支持该操作
exception.rds.UnsupportRegionException=当前地域（{0}）不支持该操作
exception.rds.NotSupportDiskTypeException=单机转双机时，当前磁盘类型（{0}）不支持
exception.rds.InstanceTypeNotSupportException=当前实例类型（{0}）不支持该操作
exception.rds.MissingRequiredKeyException=实体类参数（{0}）中缺少必填的属性
exception.rds.ResizeUnsupportInGroupException=当前实例在热活组中且所处的热活组内有实例（{0}）处于（{1}）状态，不允许发起变配
exception.rds.InstanceStatusUnsatisfiedException=当前代理实例（{0}）不允许发起变配，因为其主实例（{1}）状态不允许（{2}）
#通用异常
exception.other.service.InternalServerErrorException=内部服务错误
exception.InternalServerError=内部服务错误
exception.rds.BccInstanceStatusException=当前BCC实例 ({0}) 其状态 ({1}) 异常，无法进行当前操作
exception.rds.InstanceNotBelongRecycleException=当前实例（{0}）不在回收站中，无法执行当前操作
exception.rds.AutoRenewToPostPayException=当前实例 ({0}) 已开通自动续费，若要发起预付费转后付费操作需取消自动续费
exception.rds.ShrinkageComputeAndStorageException=不允许同时降配计算资源和存储资源
exception.rds.ProductExceedQuotaException=当前产品 ({0}) 已达到配额上限
exception.rds.InstanceReleaseFailedException=实例 ({0}) 释放失败
exception.rds.SubnetPrecheckResourceException=子网不足
exception.rds.RDSSuperUserExistException=当前已存在超级特权账户，只允许创建一个
exception.rds.InstanceDetailException=当前实例详情接口返回结果异常，实例ID为 ({0})
exception.rds.ResourceUnsatisfiedException=当前资源状态无法进行该操作，实例ID为 ({0})
exception.rds.ReleaseInstanceException=释放实例报错，实例ID为 ({0})
exception.rds.PrecheckGroupVersionException=版本前置检查不通过，主实例ID为 ({0}), 从实例ID为 ({1})