exception.rds.InstanceNotSatisfiableException=Instance status is not allowed.
exception.rds.InvalidParameterException=Invalid parameter.
exception.other.service.InvalidInstanceStatus=Instance status is invalid.
exception.other.service.InsufficientInstanceDiskSizeToRestoreSnapshot=The capacity of the instance disk is not\
   sufficient to restore this backup set, please expand the disk first.
exception.rds.MinorVersionMissException=Miss minor version.
exception.rds.MissingParameterException=Miss parameter.
exception.other.service.InternalServerErrorException=Internal server error.
exception.other.service.RDSInstanceNotFound=Instance not found.
exception.other.service.SessionTokenExpired=Login status has expired, please log in again.
exception.rds.InvalidAction=Invalid action.
exception.rds.PrecheckResourceException=Subnet ip is not enough.
exception.rds.ParamValidationException=Parameter validation.
exception.other.service.InstanceEngineException=Instance engine is not allowed.
exception.rds.PermissionDeny=Permission denied.
exception.rds.InstanceShrinkageEngineException=The engine type not support this operation.
exception.rds.DiskSizeInvalidException=The instance（{0}）disk size is invalid.
exception.rds.ParameterTopoErrorException=The instance（{0}）is not allowed this cluster.
exception.rds.InstanceShrinkageSizeException=The target storage size must be greater than or equal to 120% of the usage.
exception.rds.InstanceShrinkageUpperReplicaException=The target storage size must be less than or equal to the storage \
  size of read-only instances within the cluster.
exception.rds.InstanceShrinkageLowerMasterException=The target storage size must be greater than or equal to the \
  main instance's storage size.
exception.rds.ResizeMasterReplicaDiskException=The size of the primary instance disk should be less than or equal\
   to the size of the read-only instance disk.
exception.rds.ResizeMasterMemoryException=The size of the PG primary instance memory should be less than or equal\
   to the size of the read-only instance memory.
exception.rds.ResizeReplicaMemoryException=The size of the PG read-only instance memory should be greater than or equal\
   to the size of the primary instance memory.
exception.rds.DeleteAutomatedBackupException=Prohibit deleting instance data from automatic backups.
exception.rds.ApplicationTypeException=The current application type does not support this operation.
exception.rds.GeneralInstanceNotSupportException=The current general instance does not support this operation.
exception.rds.MultiMasterResizeException=There are multiple master instances in the current batch resize operation.
exception.rds.ApiDirectPayException=Failed to pay directly for prepaid orders when resizing in batches.
exception.rds.UnsupportRegionException=The current region（{0}）is not support this operation.
exception.rds.NotSupportDiskTypeException=Single to normal diskIoType（{0}）disabled.
exception.rds.InstanceTypeNotSupportException=The current instance type（{0}）is not support this operation.
exception.rds.MissingRequiredKeyException=The required properties are missing in the entity class parameters ({0}).
exception.other.service.InvalidRequest=Invalid request.
exception.other.service.InstanceAlreadyDeleted=RDS instance is already deleted.
exception.other.service.NotSupportOperation=You are not allowed to execute this operation.
exception.other.service.PriceQueryError=The resource is expired.
exception.other.service.ParamValidationFailed=Parameter validate error.
exception.other.service.CpuCountValidationException=Instance Cpu Size Validate Error.
exception.other.service.EngineValidationException=Instance Engine Validate Error.
exception.other.service.ServiceInternalError=Internal server error.
exception.other.service.NotEnoughBalanceForPayOrder=Current account balance is insufficient.
exception.other.service.HttpMessageNotReadable=Http Message Not Readable: Could not read JSON.
exception.InternalServerError=Internal server error.
exception.other.service.InternalError=Internal server error.
exception.other.service.EngineVersionValidationException=Instance Engine Validate Error.
exception.other.service.DatabaseNameReserved=Database name reserved.
exception.other.service.JoinRDSGroupPreCheckFailed=The application has some data remaining.
exception.other.service.SubnetNotFoundException=Subnet not found.
exception.other.service.DBError=DB occurs error.
exception.other.service.InsufficientBalance=Insufficient balance.
exception.rds.BackupParameterException=Logical backups are not supported for the current operation.
exception.rds.ResizeUnsupportInGroupException=The instance in a hot activity group are not allowed to resize \
   if there are instance ({0}) in the hot activity group that are non-available status({1}).
exception.rds.InstanceStatusUnsatisfiedException=The proxy instance ({0}}) is not allowed to resize that the master\
   instance ({1}) non-available status({2}).
exception.rds.BccInstanceStatusException=The bcc instance ({0}) status is ({1}) , it is unsatisfied.
exception.rds.InstanceNotBelongRecycleException=The current instance ({0}) does not belong to recycle list.
exception.rds.AutoRenewToPostPayException=The current instance ({0}) has been enabled for automatic renew, please cancel.
exception.rds.ShrinkageComputeAndStorageException=Downgrading of computing resources and storage resources \
  at the same time is not allowed.
exception.rds.ProductExceedQuotaException=The ({0}) instance you want to create exceeded the system limit.
exception.rds.InstanceReleaseFailedException=Instance ({0}) delete failed.
exception.other.service.DilatationExpireException=Resource expires in 24h, You \
  are not allowed to execute this operation.
exception.rds.SubnetPrecheckResourceException=Subnet ip is not enough.
exception.rds.RDSSuperUserExistException=Super privilege account allowed to create only one.
exception.other.service.InstanceStatusErrorException=Instance status is not available.
exception.other.service.AccountAlreadyExists=This account is already exists.
exception.rds.InstanceDetailException=The current instance details interface returns an abnormal result, \
  instance id is ({0})
exception.rds.ResourceUnsatisfiedException=The current resource status is not allowed this operation, \
  instance id is ({0})
exception.rds.ReleaseInstanceException=Release instance occures error, instane id is ({0})
exception.rds.PrecheckGroupVersionException=Precheck version failed，leader id is ({0}), follower id is ({1})