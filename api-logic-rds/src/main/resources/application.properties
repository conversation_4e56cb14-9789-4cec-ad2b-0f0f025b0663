server-host=0.0.0.0
server.port=8796
swagger.start:true
bce_plat_web_framework.is_web_application:false
iam.signature.headers:host;x-bce-accesskey;x-bce-console-rpc-id;x-bce-date;x-bce-request-id;x-bce-secretkey;
rsa.public.key:MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI8qaFHtgi9yltIA3qYy2OU/91AC+yLVJlMLa0AR6eUae1V8hXjXFocWwWSreN9pzjFoEJXiGPj9jsE6Bh5FXpcCAwEAAQ==
rsa.private.key:MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAjypoUe2CL3KW0gDepjLY5T/3UAL7ItUmUwtrQBHp5Rp7VXyFeNcWhxbBZKt432nOMWgQleIY+P2OwToGHkVelwIDAQABAkAqLsaaDS8kp9DEg7kWozjBI33NN3OjqcYnBeBN+nk+JYRGtUWVYEhmR7b2GgTeXcolTuLmB00v9JsmbfvYDoKRAiEAzTqLkcj94YLjLPRilXODpz7F72QbLqFW3RU03279ET0CIQCylVAVPvoWn381wcBnbvCDOyn9EbfP+FVVBAHwb1nEYwIgDFTyQgZTyxM0V2Uv708LoCcTebkIMRscvxghHzPqHPkCIFYO+j6i0KXiSs0/B1dQ8Ppsonlf9nJ0O7ryaXTSVDH9AiB8DU3YJbKE1pY0jabmvO127JTbs52m3LVdmSxVddG8Og==
#=================== login & access ===================#
login.url:http://localhost:8088/uc/login
cookie.domain:localhost
login.cookie.md5.key:19920908
login.urls.not.need.auth:/asset/**;/dep/**;/esl.js;/swagger/**;/api-docs;/api-docs/**
login.urls.need.auth:/**
login.dmsUrl:nmg02-bce-test18.nmg02.baidu.com:8896
passport.appid:1240
passport.session.endpoint:http://localhost:8088/passport
uc.app.id:285
uc.server:localhost:8888
iam.access.failed.jump.url:http://localhost:8088/user/mockaccess?redirect=http://localhost:8080
iam.console.username=console
iam.console.password=console
iam.access.paths:/**
iam.access.exclude.paths:/api-docs;/api-docs/**
iam.csrf.paths:/api/**
iam.csrf.exclude.paths:/api-docs;/api-docs/**
iam.permission.is.need.check.valid=false
bce.plat.iam.access=false
#======================================================#
#=================== logging config ===================#
logging.requestId_urlPattern:/*
logging.log_pattern:%d{yyyy-MM-dd HH:mm :ss.SSS} %5p ${PID:- } [%t] --- %logger [%F:%L] : [%X{x-bce-request-id}][%X{currentUser}] %m%n}
logging.has_console_appender:true
logging.has_web_debug_appender:true
logging.web_debug_path:/debug
logging.web_debug.level:INFO
### logback rolling log, uncomment to open appender ###
logging.info_log_file_path:../log/info/api-logic-rds.info.log
logging.error_log_file_path:../log/error/api-logic-rds.error.log
logging.warn_log_file_path:../log/warn/api-logic-rds.warn.log
logging.debug_log_file_path:../log/debug/api-logic-rds.debug.log
### access log, uncomment to open appender ###
logging.access_debug_uri_prefix:/
logging.access_log_file_path:../log/access/api-logic-rds.access.log
logging.access_debug_log_file_path:../log/access_debug/api-logic-rds.access_debug.log
#======================================================#
#=================== logical database config begin ===================#
database.mybatis.enable=true
database.enable=false
database.isEmbedded:false
database.script=
database.url:jdbc:mysql://************:6666/bce_logical?useUnicode=true&amp;characterEncoding=UTF-8
database.username:bcetest
database.password:bcetest
database.logic.enable=true
database.logic.url:*********************************************************************************;\
  characterEncoding=UTF-8
database.logic.username:root
database.logic.password:bcetest
db.logical.bcc.url:*********************************************************************************;\
  characterEncoding=UTF-8
db.logical.bcc.username:root
db.logical.bcc.password:bcetest
#=================== billing database config begin ===================#
db.quota.table.name=quota
db.quota.url=**********************************************************************************
db.quota.username=bcetest
db.quota.password=bcetest
db.quota.timeout.second=10
#=================== finance ===================#
finance.account.type.available:100
finance.account.type.freeze:101
institution_id=1
secret_code=d21taUQ0N0tNZ0o2RllvbElObkV6VzFZdFhGd1dK
secret_code_institution_id_1=mUphTrGslz6JZtgAj0eqEKRBI9oxa4bi1M3wHcLF
secret_code_institution_id_2=d21taUQ0N0tNZ0o2RllvbElObkV6VzFZdFhGd1dK
finance.pay.url.prefix=http://cp01-testing-fengkong04.cp01.baidu.com:8118/process/pay_order_bce
finance.pay.pay_order.forward_url.prefix=https://${server-host}/billing/#/order/success
finance.pay.pay_order.feedback_url.prefix=http://nmg02-bce-test6.nmg02.baidu.com:8003/orderPurchaseCallback
finance.pay.recharge.forward_url=https://${server-host}/billing/#/account/history
#================log monitor=================#
monitor.latencyRecorder.enable:true
bce.enable.debug:true
#=================zone init==========================#
bce.logical.pz.default:AZONE-nmg02
bcc.postpay.quota:20
instance.postpay.bj.quota=400
instance.postpay.gz.quota=500
bce.logical.region:bj
#=================== system disk status promise operation  ===================#
system.disk.snapshot.status=available
system.disk.template.status=available
#=================== snapshot status promise operation  ===================#
snapshot.system.rename.status=active,error
snapshot.system.delete.status=active,error
snapshot.system.template.status=active
snapshot.system.rollback.status=active
snapshot.cds.rename.status=available,error_deleting,error
snapshot.cds.delete.status=available,error_deleting,error
snapshot.cds.create_cds.status=available
snapshot.cds.rollback.status=available
#=================== image status promise operation  ===================#
image.rename.status=active,error
image.create_instance.status=active
image.delete.status=active.error
#=================== dedicated server status promise operation  ===================#
dedicate.rename.status=up,expired
dedicate.modify_desc.status=up,expired
dedicate.create_instance.status=up
dedicate.delete_instance.status=up
dedicate.detail.status=up,down,expired
dedicate.create.same.status=up,down,expired
dedicate.delete.status=expired
dedicate.renew.status=up,expired
#=================== httpClient  ===================#
httpClient.maxConnTotal=800
httpClient.maxConnPerRoute=800
#=================== tomcat thread pool  ===================#
tomcat.maxThreads=300
tomcat.minSpareThreads=300
#=================== function enable  ===================#
snapshot.create.enable=true
#=================== status read from backend or local enable  ===================#
bce.status.read.local:true
bce.logic.enable.debug=true
region.currentRegion=bj
bce.logical.authentication.paths:/api/**
database.logic.mapper.locations=classpath*:*Mapper.xml
spring.velocity.checkTemplateLocation=false
#========================== rds   ==========================#
rds.autoRenew.url:https://${server-host}/billing/?_=*************#/renew/list~serviceType=RDS
rds.unavailable.physicalzone:AZONE-dbl
#AZONE-szth,AZONE-cq02,AZONE-bjyz,AZONE-gzns,AZONE-gzhxy,AZONE-gzhk,AZONE-fsh#线上＃
#AZONE-nmg02,AZONE-dbl#沙盒#
month.refund.limit:100

# ========================================= ServiceExceptionHandler
exception.has_service_exception_handler=false
# account.ignoreStorageSizeAccountIds=8ba8fe2d049948d88d04918b78ba820c,73d9670d55cf4b2a80315e2a4c03dbcb
rds.unavailable.localdisk.physicalzone=AZONE-cdsx

rds.su.whitelist.physicalzone=AZONE-sulcc01,AZONE-sulcc02
rds.whitelist.physicalzone.account=f7ac4b5b395846389a889f7b89e9f030,b9c676e017c84b0ab1a56e51fa9b1381
