{"regions": [{"region": "default", "services": [{"service": "IAM", "endpoint": "http://iam.bj.internal-qasandbox.baidu-int.com/v3"}, {"service": "STS", "endpoint": "http://sts.bj.internal-qasandbox.baidu-int.com:8586/v1"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8006"}, {"service": "Order", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/orders"}, {"service": "Resource", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/resources"}, {"service": "Finance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://nmg02-bce-test92.nmg02.baidu.com:8774/v2"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://blb.bj.qasandbox.baidu-int.com/v1"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/internal-api/v1"}, {"service": "RDS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v1"}, {"service": "RDS2", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v2"}, {"service": "RDSV2", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8085/api/v1"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://nmg02-bce-test25.nmg02.baidu.com:8869/json-api/v1"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://************:8690/v1"}, {"service": "Discount", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v1"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003"}, {"service": "Campaign", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8535/v1"}, {"service": "LOGICAL_BCC", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ServiceCatalog", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8993/v1"}, {"service": "InvoiceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8664/v2"}, {"service": "FinanceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v2"}, {"service": "DiscountV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v2"}, {"service": "CouponV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v2"}, {"service": "LogicalTag", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8777"}, {"service": "QualifyV2", "endpoint": "http://gzns-store-sandbox089.gzns:8291/qualify/v2"}]}, {"region": "bj", "services": [{"service": "IAM", "endpoint": "http://iam.bj.internal-qasandbox.baidu-int.com/v3"}, {"service": "STS", "endpoint": "http://sts.bj.internal-qasandbox.baidu-int.com:8586/v1"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8006"}, {"service": "Order", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/orders"}, {"service": "Resource", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/resources"}, {"service": "Finance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://nmg02-bce-test92.nmg02.baidu.com:8774/v2"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://blb.bj.qasandbox.baidu-int.com/v1"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/internal-api/v1"}, {"service": "RDS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v1"}, {"service": "RDS2", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v2"}, {"service": "RDSV2", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8085/api/v1"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://nmg02-bce-test25.nmg02.baidu.com:8869/json-api/v1"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://************:8690/v1"}, {"service": "Discount", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v1"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003"}, {"service": "Campaign", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8535/v1"}, {"service": "LOGICAL_BCC", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ApiService", "endpoint": "http://api.bcetest.baidu.com/v1"}, {"service": "ServiceCatalog", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8993/v1"}, {"service": "InvoiceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8664/v2"}, {"service": "FinanceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v2"}, {"service": "DiscountV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v2"}, {"service": "CouponV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v2"}, {"service": "LogicalTag", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8777"}, {"service": "RDS-szfsg", "endpoint": "http://rds.szfsg.bce.baidu-int.com:80/json-api/v1"}, {"service": "RDS-su", "endpoint": "http://rds.su.bce-internal.baidu.com/json-api/v1"}, {"service": "RDS-sin", "endpoint": "http://rds.sin.bce.baidu-int.com:80/json-api/v1"}, {"service": "RDS-hkg", "endpoint": "http://rds.hkg.bce.baidu-int.com:80/json-api/v1"}, {"service": "RDS-hb-fsg", "endpoint": "http://rds.fsg.bdbl.bce.baidu-int.com:80/json-api/v1"}, {"service": "RDS-hk02", "endpoint": "http://rds.hk02.bce-internal.baidu.com/json-api/v1"}, {"service": "RDS-gz", "endpoint": "http://rds.gz.bce-internal.baidu.com/json-api/v1"}, {"service": "RDS-fwh", "endpoint": "http://rds.fwh.bce.baidu-int.com:80/json-api/v1"}, {"service": "RDS-fsh", "endpoint": "http://rds.fsh.bce.baidu-int.com:80/json-api/v1"}, {"service": "RDS-bjfsg", "endpoint": "http://rds.bjfsg.bce.baidu-int.com:80/json-api/v1"}, {"service": "RDS-bd", "endpoint": "http://rds.bdbl.bce.baidu-int.com:80/json-api/v1"}, {"service": "RDS-bj", "endpoint": "http://rds.bj.bce-internal.baidu.com/json-api/v1"}, {"service": "QualifyV2", "endpoint": "http://gzns-store-sandbox089.gzns:8291/qualify/v2"}]}, {"region": "gz", "services": [{"service": "IAM", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:35357/v3"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8006"}, {"service": "Order", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/orders"}, {"service": "Resource", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/resources"}, {"service": "Finance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://nmg02-bce-test92.nmg02.baidu.com:8774/v2"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://blb.bj.qasandbox.baidu-int.com/v1"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/internal-api/v1"}, {"service": "RDS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v1"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://nmg02-bce-test25.nmg02.baidu.com:8869/json-api/v1"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://************:8690/v1"}, {"service": "Discount", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v1"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003"}, {"service": "Campaign", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8535/v1"}, {"service": "LOGICAL_BCC", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ServiceCatalog", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8993/v1"}, {"service": "InvoiceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8664/v2"}, {"service": "FinanceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v2"}, {"service": "DiscountV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v2"}, {"service": "CouponV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v2"}, {"service": "LogicalTag", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8777"}]}, {"region": "hk", "services": [{"service": "IAM", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:35357/v3"}, {"service": "InvitedCode", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "ServiceType", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8110/v1"}, {"service": "Price", "endpoint": "http://nmg02-bce-test7.nmg02.baidu.com:8006"}, {"service": "Order", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/orders"}, {"service": "Resource", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003/resources"}, {"service": "Finance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8666/v1"}, {"service": "BCC", "endpoint": "http://nmg02-bce-test92.nmg02.baidu.com:8774/v2"}, {"service": "CDS", "endpoint": "http://localhost:8088/cds/json-api/v1"}, {"service": "SECURITYGROUP", "endpoint": "http://localhost:8088/security/json-api/v1"}, {"service": "BLB", "endpoint": "http://blb.bj.qasandbox.baidu-int.com/v1"}, {"service": "BOS", "endpoint": "http://bos.qasandbox.bcetest.baidu.com"}, {"service": "SCS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com:8456"}, {"service": "RDS.migration", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/internal-api/v1"}, {"service": "RDS", "endpoint": "http://nmg02-bce-test90.nmg02.baidu.com/json-api/v1"}, {"service": "BMR", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com:8079"}, {"service": "BCM2", "endpoint": "http://nmg02-bce-test25.nmg02.baidu.com:8869/json-api/v1"}, {"service": "CDN", "endpoint": "http://nmg02-bce-test98.nmg02.baidu.com:8080/json-api/v1"}, {"service": "SES", "endpoint": "http://nmg02-bce-test9.nmg02.baidu.com:8886/v1"}, {"service": "SMS", "endpoint": "http://nmg02-bce-test8.nmg02.baidu.com:8887/v1"}, {"service": "BSS", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/bss/index.php/json-api/v1"}, {"service": "POSTMSG", "endpoint": "http://nmg02-bce-test13.nmg02.baidu.com:8580/v2/postmsg"}, {"service": "COUPON", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v1"}, {"service": "Qualify", "endpoint": "http://nmg02-bce-test14.nmg02.baidu.com:8291/qualify/v1"}, {"service": "<PERSON><PERSON><PERSON>", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8990/v1"}, {"service": "Executor", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8320/v1"}, {"service": "TrailEvent", "endpoint": "http://tidedb.bce-sandbox.baidu.com:8082"}, {"service": "TrailServer", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8800/trail/v1"}, {"service": "UserSettings", "endpoint": "http://************:8690/v1"}, {"service": "Discount", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v1"}, {"service": "BceFinance", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v1"}, {"service": "EIP", "endpoint": "http://nmg02-bce-test91.nmg02.baidu.com/meta-server/index.php/json-api/v2"}, {"service": "Package", "endpoint": "http://nmg02-bce-test6.nmg02.baidu.com:8003"}, {"service": "Campaign", "endpoint": "http://nmg02-bce-test11.nmg02.baidu.com:8535/v1"}, {"service": "LOGICAL_BCC", "endpoint": "http://bcc.logic.baidu.com:80"}, {"service": "ServiceCatalog", "endpoint": "http://nmg02-bce-test10.nmg02.baidu.com:8993/v1"}, {"service": "InvoiceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8664/v2"}, {"service": "FinanceV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8662/v2"}, {"service": "DiscountV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8663/v2"}, {"service": "CouponV2", "endpoint": "http://nmg02-bce-test12.nmg02.baidu.com:8661/v2"}]}]}