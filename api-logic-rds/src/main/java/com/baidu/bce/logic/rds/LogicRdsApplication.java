package com.baidu.bce.logic.rds;

import com.baidu.bce.internalsdk.core.BceInternalClient;
import com.baidu.bce.plat.webframework.BceServiceApplication;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.joda.JodaModule;
import endpoint.configuration.RegionConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.lang.reflect.Field;

/**
 * Created by luping03 on 17/10/9.
 */
@Configuration
@EnableAsync
@EnableAutoConfiguration
@ComponentScan(basePackages = {"com.baidu.bce.*"}, basePackageClasses = {RegionConfiguration.class})
public class LogicRdsApplication {
    public static void main(String[] args) throws Exception {
        Field field = BceInternalClient.class.getDeclaredField("DEFAULT_MAPPER");
        field.setAccessible(true);
        ObjectMapper bceObjectMapper = (ObjectMapper) field.get(null);
        bceObjectMapper.registerModule(new JodaModule());
        BceServiceApplication.run(LogicRdsApplication.class, args);
    }
}
