#!/bin/sh
APPLICATION=../bin/api-logic-rds.jar
SPRING_CONFIG_FILE=../conf/application.properties
ENDPOINT_FILE=file:../conf/endpoint.json
PHPADMINURL_CONFIG_FILE=file:../conf/phpadmin.conf
MONITOR_DUMP_FILE=../monitor/logic_rds.data.bvar
MAX_MEMORY=2048M
MAX_PERM_MEMORY=512M

function checkApplicationExist() {
    if [ ! -f $APPLICATION ]; then
        echo "Cannot find $APPLICATION"
        echo "The file is absent or does not have execute permission"
        echo "This file is needed to run this program"
        exit 1
    fi
}

function checkJDKEnv() {
    if [ -z "$JAVA_HOME" -a -z "$JRE_HOME" ]; then
        echo "Neither the JAVA_HOME nor the JRE_HOME environment variable is defined"
        echo "At least one of these environment variable is needed to run this program"
        exit 1
    fi
}

function getPorts() {
    IFS=$' \t\n\r' read -d '' -r -a tmp < debug.conf
    SERVER_PORT=${tmp[0]}
    DEBUG_PORT=${tmp[1]}
}

function main() {

    checkApplicationExist

    JAVA="java"
    if [ -f ../bin/java8/bin/java ]; then
         ../bin/java8/bin/java -version >/dev/null 2>&1
        if [ $? -eq 0 ]; then
          JAVA="../bin/java8/bin/java"
        fi
    fi
    if [ -f /home/<USER>/.jumbo/opt/sun-java8/bin/java ]; then
        JAVA="/home/<USER>/.jumbo/opt/sun-java8/bin/java"
    fi
    if [ -f /home/<USER>/.deck/1.0/oraclejdk/1.8.0/bin/java ]; then
        JAVA="/home/<USER>/.deck/1.0/oraclejdk/1.8.0/bin/java"
    fi

    if [[ ${isDebug} ]]; then
        getPorts

        nohup ${JAVA} -Dserver.port=${SERVER_PORT} -Dbvar.dump.file=$MONITOR_DUMP_FILE -Dspring.config.location=$SPRING_CONFIG_FILE -Dendpoint.config=$ENDPOINT_FILE -Dphpadmin.configuration.path=$PHPADMINURL_CONFIG_FILE -Dfile.encoding=UTF-8 \
        -Xmx$MAX_MEMORY -XX:MaxPermSize=$MAX_PERM_MEMORY -XX:+UseConcMarkSweepGC -XX:+PrintGCDetails -XX:+PrintGCDateStamps \
        -Xloggc:gc.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=20M -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=${DEBUG_PORT} -jar $APPLICATION > /dev/null 2>&1 &
    else
        nohup ${JAVA} -Dbvar.dump.file=$MONITOR_DUMP_FILE -Dspring.config.location=$SPRING_CONFIG_FILE -Dendpoint.config=$ENDPOINT_FILE -Dphpadmin.configuration.path=$PHPADMINURL_CONFIG_FILE -Dfile.encoding=UTF-8 \
        -Xmx$MAX_MEMORY -XX:MaxPermSize=$MAX_PERM_MEMORY -XX:+UseConcMarkSweepGC -XX:+PrintGCDetails -XX:+PrintGCDateStamps \
        -Xloggc:gc.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=20M -jar $APPLICATION > /dev/null 2>&1 &
    fi
}
while getopts "d" arg; do
case "${arg}" in
    d)
        isDebug=true
        ;;
    ?)
        echo "Unknown option"
        exit 1
        ;;
    esac
done
main