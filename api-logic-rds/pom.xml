<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>api-logic-rds-root</artifactId>
        <groupId>com.baidu.bce</groupId>
        <version>${api-logic-rds-version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>api-logic-rds</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-rds-module</artifactId>
            <version>${api-logic-rds-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-plat-web-framework-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-rds-service</artifactId>
            <version>${api-logic-rds-version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.mockito/mockito-core -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.8.0</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.mockito/mockito-inline -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>3.8.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.github.spullara.mustache.java</groupId>
            <artifactId>compiler</artifactId>
            <version>0.8.18</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>