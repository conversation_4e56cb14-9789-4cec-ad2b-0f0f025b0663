package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.DiskInfoResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSlowSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.PostSqlflowRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlExplainListResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlStatsDurationList;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTableList;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTemplateResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTrend;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaInstanceResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaPageRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaPageResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaResultResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaTopoResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SqlIdRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SqlIdResponse;
import com.baidu.bce.logic.rds.service.SmartDbaService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@RestController
@RequestMapping(value = "/api/rds/smartDba")
public class ConsoleApiSmartDbaController {

    @Autowired
    private SmartDbaService smartDbaService;

    /**
     * 获取实例接口
     * 根据请求中的实例ID，获取对应的实例信息
     * 
     * @param smartDbaRequest 请求体，包含实例ID
     * @return SmartDbaInstanceResponse 实例信息
     */
    @RequestMapping(value = "/instance", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SmartDbaInstanceResponse getInstance(@IdPermission @Valid @RequestBody SmartDbaRequest smartDbaRequest) {
        return smartDbaService.instance(smartDbaRequest.getInstanceId());
    }
    /**
      * 获取拓扑列表
      * 该函数用于处理获取数据库拓扑列表的请求
      * 
      * @param smartDbaRequest 请求参数，包含实例ID等信息
      * @return SmartDbaTopoResponse 响应结果，包含拓扑列表信息
      */

    @RequestMapping(value = "/topo/list", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SmartDbaTopoResponse getTopoList(@IdPermission @Valid @RequestBody SmartDbaRequest smartDbaRequest) {
        return smartDbaService.getTopoList(smartDbaRequest.getInstanceId());
    }
    /**
      * 设置慢SQL流
      * 该函数用于设置慢SQL流信息
      * 
      * @param request 请求体，包含实例ID
      * @return EdpResultResponse<GetSlowSqlflowResponse> 返回设置慢SQL流的结果
      */

    @RequestMapping(value = "/putslowsqlflow", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<GetSlowSqlflowResponse> putSlowSqlflow(
            @IdPermission @RequestBody InstanceIdRequest request) {
        EdpResultResponse<GetSlowSqlflowResponse> edpResultResponse = new EdpResultResponse<>();
        GetSlowSqlflowResponse getSlowSqlflowResponse = smartDbaService.putSlowSqlflow(request.getInstanceId());
        edpResultResponse.setResult(getSlowSqlflowResponse);
        return edpResultResponse;
    }
    /**
      * 删除慢SQL流接口
      * 该接口用于删除指定的慢SQL流信息。
      * 
      * @param request 包含实例ID的请求体
      * @return 删除操作的结果
      */

    @RequestMapping(value = "/delslowsqlflow", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<GetSlowSqlflowResponse> deleteSlowSqlflow(
            @IdPermission @RequestBody InstanceIdRequest request) {
        EdpResultResponse<GetSlowSqlflowResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(smartDbaService.deleteSlowSqlflow(request.getInstanceId()));
        return edpResultResponse;
    }
    /**
      * 获取慢SQL流信息
      * 根据提供的实例ID，通过调用smartDbaService的getSlowSqlflow方法，获取慢SQL流信息，并封装到EdpResultResponse中返回
      * 
      * @param request 包含实例ID的请求体
      * @return 封装了慢SQL流信息的EdpResultResponse对象
      */

    @RequestMapping(value = "/getslowsqlflow", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<GetSlowSqlflowResponse> getSlowSqlflow(
            @IdPermission @RequestBody InstanceIdRequest request) {
        EdpResultResponse<GetSlowSqlflowResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(smartDbaService.getSlowSqlflow(request.getInstanceId()));
        return edpResultResponse;
    }
    /**
      * 获取慢SQL趋势
      * 根据请求中的实例ID和参数，获取慢SQL趋势信息
      *
      * @param request 请求参数，包含实例ID和查询条件
      * @return 慢SQL趋势信息
      */

    @RequestMapping(value = "/slowsql/table/trend", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTrend getSlowSqlTrend(@IdPermission @Valid @RequestBody SlowsqlListRequest request) {
        return smartDbaService.getSlowSqlTrend(request.getInstanceId(), request);
    }
    /**
      * 获取慢SQL模板
      * 根据传入的请求参数获取对应的慢SQL模板信息
      * 
      * @param request 请求参数，包含instanceId和查询条件
      * @return 慢SQL模板信息
      */

    @RequestMapping(value = "/slowsql/stats/digest", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTemplateResponse getSlowSqlTemplate(@IdPermission @Valid @RequestBody SlowsqlListRequest request) {
        return smartDbaService.getSlowSqlTemplate(request.getInstanceId(), request);
    }
    /**
      * 获取慢查询SQL列表
      * 根据请求参数获取数据库慢查询SQL列表信息
      * 
      * @param request 慢查询SQL列表请求参数，包含实例ID和其他查询条件
      * @return 返回慢查询SQL列表响应结果
      */

    @RequestMapping(value = "/slowsql/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SlowsqlListResponse>  getSlowSqlList(
            @IdPermission @Valid @RequestBody SlowsqlListRequest request) {
        EdpResultResponse<SlowsqlListResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(smartDbaService.getSlowSqlList(request.getInstanceId(), request));
        return edpResultResponse;
    }
    /**
      * 获取慢查询SQL表
      * 根据请求参数获取指定实例的慢查询SQL表
      * 
      * @param request 请求参数，包括实例ID和其他查询条件
      * @return 慢查询SQL表列表
      */

    @RequestMapping(value = "/slowsql/table", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTableList getSlowSqlTable(@IdPermission @Valid @RequestBody SlowsqlListRequest request) {
        return smartDbaService.getSlowSqlTable(request.getInstanceId(), request);
    }
    /**
     * 获取慢查询SQL表的列信息
     * 根据传入的实例ID和请求参数，获取指定慢查询SQL表的列信息
     * 
     * @param request 包含实例ID和请求参数的SlowsqlListRequest对象
     * @return 慢查询SQL表的列信息列表
     */

    @RequestMapping(value = "/slowsql/table/column", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTableList getSlowSqlTableColumn(@IdPermission @Valid @RequestBody SlowsqlListRequest request) {
        return smartDbaService.getSlowSqlTableColumn(request.getInstanceId(), request);
    }
    /**
      * 获取慢查询表索引
      * 根据请求参数获取慢查询表的索引信息
      * 
      * @param request 请求参数，包含instanceId和查询条件
      * @return 慢查询表索引列表
      */

    @RequestMapping(value = "/slowsql/table/index", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTableList getSlowSqlTableIndex(@IdPermission @Valid @RequestBody SlowsqlListRequest request) {
        return smartDbaService.getSlowSqlTableIndex(request.getInstanceId(), request);
    }
    /**
      * 获取慢查询解释信息接口
      * 根据请求参数获取数据库慢查询的解释信息
      * 
      * @param request 包含实例ID和查询条件的请求体
      * @return 慢查询解释信息的响应体
      */

    @RequestMapping(value = "/slowsql/explain", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlExplainListResponse getSlowSqlExplain(@IdPermission @Valid @RequestBody SlowsqlListRequest request) {
        return smartDbaService.getSlowSqlExplain(request.getInstanceId(), request);
    }
    /**
      * 获取慢SQL调优信息
      * 根据传入的实例ID和慢SQL列表请求，获取对应的慢SQL调优信息
      * 
      * @param request 包含实例ID和慢SQL列表的请求信息
      * @return 慢SQL调优信息
      */

    @RequestMapping(value = "/slowsql/table/tuning", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTrend getSlowSqlTuning(@IdPermission @Valid @RequestBody SlowsqlListRequest request) {
        return smartDbaService.getSlowSqlTuning(request.getInstanceId(), request);
    }
    /**
      * 获取慢SQL统计时长接口
      * 根据实例ID和请求参数获取慢SQL的统计时长信息
      * 
      * @param request 包含实例ID和其他请求参数的请求体
      * @return 慢SQL统计时长列表
      */

    @RequestMapping(value = "/slowsql/stats/duration", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlStatsDurationList getSlowSqlStatsDuration(
            @IdPermission @Valid @RequestBody SlowsqlListRequest request) {
        return smartDbaService.getSlowSqlStatsDuration(request.getInstanceId(), request);
    }
    /**
      * 获取慢SQL统计信息接口
      * 根据请求参数获取对应的慢SQL统计信息
      * 
      * @param request 请求参数，包含实例ID和其他查询条件
      * @return 慢SQL统计信息列表
      */

    @RequestMapping(value = "/slowsql/stats/source", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlStatsDurationList getSlowSqlStatsSource(
            @IdPermission @Valid @RequestBody SlowsqlListRequest request) {
        return smartDbaService.getSlowSqlStatsSource(request.getInstanceId(), request);
    }
    /**
      * 获取实例磁盘信息
      * 通过实例ID获取对应的磁盘信息
      * 
      * @param smartDbaRequest 包含实例ID的请求体
      * @return DiskInfoResponse 实例磁盘信息
      */

    @RequestMapping(value = "/disk/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public DiskInfoResponse getInstanceDisk(@IdPermission @Valid @RequestBody SmartDbaRequest smartDbaRequest) {
        return smartDbaService.instanceDisk(smartDbaRequest.getInstanceId());
    }
    /**
      * 获取数据库列表
      * 根据请求中的实例ID和列表编号，获取对应的数据库列表信息
      * 
      * @param smartDbaRequest 包含实例ID和列表编号的请求体
      * @return 数据库列表信息
      */

    @RequestMapping(value = "/db/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SmartDbaResultResponse getDbList(@IdPermission @Valid @RequestBody SmartDbaRequest smartDbaRequest) {
        return smartDbaService.getDbList(smartDbaRequest.getInstanceId(), smartDbaRequest.getListNo());
    }
    /**
      * 获取SmartDba分页列表
      * 根据传入的分页请求参数，获取SmartDba的分页数据
      * 
      * @param smartDbaPageRequest 分页请求参数，包含实例ID等
      * @return SmartDba分页响应数据
      */

    @RequestMapping(value = "/tb/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SmartDbaPageResponse getSmartDbaPage(
            @IdPermission @Valid @RequestBody SmartDbaPageRequest smartDbaPageRequest) {
        return smartDbaService.getSmartDbaPage(smartDbaPageRequest.getInstanceId(), smartDbaPageRequest);
    }
    /**
     * 获取连接列表
     * 根据请求中的实例ID获取数据库连接列表
     * 
     * @param smartDbaRequest 请求参数，包含实例ID等信息
     * @return 数据库连接列表
     */

    @RequestMapping(value = "/conn/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SmartDbaInstanceResponse getConnList(@IdPermission @Valid @RequestBody SmartDbaRequest smartDbaRequest) {
        return smartDbaService.getConnList(smartDbaRequest.getInstanceId());
    }
    /**
      * 获取连接状态接口
      * 根据实例ID获取数据库连接状态
      * 
      * @param smartDbaRequest 包含实例ID的请求体
      * @return 数据库连接状态信息
      */

    @RequestMapping(value = "/conn/stati", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SmartDbaInstanceResponse getConnStati(@IdPermission @Valid @RequestBody SmartDbaRequest smartDbaRequest) {
        return smartDbaService.getConnStati(smartDbaRequest.getInstanceId());
    }
    /**
      * 获取SQL流接口
      * 根据实例ID获取SQL流信息
      * 
      * @param instanceId 实例ID
      * @return SQL流信息封装结果
      */

    @RequestMapping(value = "/sqlflow", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<GetSqlflowResponse> getSqlflow(@IdPermission @RequestParam String instanceId) {
        EdpResultResponse<GetSqlflowResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(smartDbaService.getSqlflow(instanceId));
        return edpResultResponse;
    }
    /**
      * 提交Sqlflow请求
      * 通过POST请求向/sqlflow接口提交Sqlflow任务，并获取执行结果
      * 
      * @param instanceId 实例ID，用于权限验证和指定操作目标
      * @return EdpResultResponse<GetSqlflowResponse> 返回Sqlflow任务的执行结果
      */

    @RequestMapping(value = "/sqlflow", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<GetSqlflowResponse> postSqlflow(@IdPermission @RequestParam String instanceId) {
        EdpResultResponse<GetSqlflowResponse> edpResultResponse = new EdpResultResponse<>();
        PostSqlflowRequest postSqlflowRequest = new PostSqlflowRequest();
        postSqlflowRequest.setInstanceId(instanceId);
        edpResultResponse.setResult(smartDbaService.postSqlflow(postSqlflowRequest));
        return edpResultResponse;
    }
    /**
      * 删除Sqlflow接口
      * 根据instanceId删除指定的Sqlflow配置
      * 
      * @param instanceId 实例ID，用于指定要删除的Sqlflow配置
      * @return EdpResultResponse<GetSqlflowResponse> 删除操作的结果
      */

    @RequestMapping(value = "/sqlflow", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<GetSqlflowResponse> deleteSqlflow(@IdPermission @RequestParam String instanceId) {
        EdpResultResponse<GetSqlflowResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(smartDbaService.deleteSqlflow(instanceId));
        return edpResultResponse;
    }
    /**
      * 获取SQL接口
      * 根据请求获取对应的SQL语句
      * 
      * @param getSqlRequest 获取SQL的请求参数
      * @return 返回SQL语句的响应结果
      */

    @RequestMapping(value = "/full-sql", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<GetSqlResponse> getSql(@IdPermission @RequestBody GetSqlRequest getSqlRequest) {
        EdpResultResponse<GetSqlResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(smartDbaService.getSql(getSqlRequest));
        return edpResultResponse;
    }
    /**
      * 根据SQL ID获取慢SQL信息
      * 通过提供的SQL ID，获取对应的慢SQL信息，并以EdpResultResponse<SqlIdResponse>形式返回
      * 
      * @param request 包含SQL ID的请求体
      * @return 包含慢SQL信息的EdpResultResponse<SqlIdResponse>对象
      */

    @RequestMapping(value = "/slowsqlid", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SqlIdResponse> getSlowSqlBySqlId(@IdPermission @Valid @RequestBody SqlIdRequest request) {
        EdpResultResponse<SqlIdResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(smartDbaService.getSlowSqlBySqlId(request.getInstanceId(), request));
        return edpResultResponse;
    }


}
