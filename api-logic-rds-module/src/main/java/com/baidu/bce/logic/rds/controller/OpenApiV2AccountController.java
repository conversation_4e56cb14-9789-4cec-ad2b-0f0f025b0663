package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountCheckRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelAccountDetailRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelDeleteAccountRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelListResponse;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelPrivilegeScopeResponse;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelUpdateDescRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelUpdatePrivilegeRequest;
import com.baidu.bce.internalsdk.rds.model.account.V2AccountListResponse;
import com.baidu.bce.internalsdk.rds.model.account.V2AccountUpdatePrivilegesRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.V2Account;
import com.baidu.bce.internalsdk.rds.model.database.ListTableRequest;
import com.baidu.bce.internalsdk.rds.model.database.TableLevelListDatabaseResponse;
import com.baidu.bce.internalsdk.rds.model.instance.AccountShowResponse;
import com.baidu.bce.logic.rds.service.AccountService;
import com.baidu.bce.logic.rds.service.DatabaseService;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.V2AccountService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.IsExistResponse;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/v2/instance")
public class OpenApiV2AccountController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private AccountService accountService;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private V2AccountService v2AccountService;


    @Autowired
    private DatabaseService databaseService;

    @Autowired
    private IdMapperService idMapperService;


    /**
     * 获取帐号列表
     * 通过API获取指定实例下的帐号列表，并对返回结果进行格式化处理
     *
     * @param instanceId 实例ID，用于指定查询的实例
     * @return V2AccountListResponse 帐号列表响应对象，包含帐号信息列表
     */
    @ApiOperation(value = "获取帐号列表")
    @RequestMapping(value = "/{instanceId}/account", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public V2AccountListResponse getList(@IdPermission @IdMapper @PathVariable String instanceId) {
        LOGGER.debug("get account list. instanceId is {}", instanceId);
        String from = "api";
        V2AccountListResponse response = null;
        response = v2AccountService.list(instanceId, from);
        if (response.getAccounts() != null) {
            for (V2Account account : response.getAccounts()) {
                account.setStatus(account.getAccountStatus());
                account.setAccountType(account.getSuperUserFlag());
                account.setDesc(account.getRemark());
            }
        }

        return response;
    }
    /**
      * 创建帐号
      * 创建帐号的接口，支持通过POST请求方式调用，需要传入instanceId, Account对象和HttpServletRequest对象。
      *
      * @param instanceId 实例ID
      * @param request 帐号对象，包含创建帐号所需的各种信息
      * @param httpServletRequest HttpServletRequest对象，用于获取请求头中的访问密钥
      */

    @ApiOperation(value = "创建帐号")
    @RequestMapping(value = "/{instanceId}/account", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void create(@IdPermission @IdMapper @PathVariable String instanceId,
                       @Valid @RequestBody Account request,
                       HttpServletRequest httpServletRequest) {
        LOGGER.debug("create account. instanceId is {}", instanceId);
        String from = "api";
        request.setSuperUserFlag(request.getAccountType());
        request.setRemark(request.getDesc());
        String ak = httpServletRequest.getHeader(RDSConstant.X_BCE_ACCESS_KEY);
        v2AccountService.create(instanceId, request, from, ak);

    }
    /**
      * 查看账户详情权限
      * 通过accountName获取账户详情，并验证权限
      *
      * @param instanceId 实例ID
      * @param accountName 账户名称
      * @return 账户详情
      */

    @ApiOperation(value = "查看账户详情权限")
    @RequestMapping(value = "/{instanceId}/account/{accountName}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public AccountShowResponse detail(@IdPermission @IdMapper @PathVariable String instanceId,
                                      @PathVariable String accountName) {
        LOGGER.debug("create account. instanceId: {}, accountName: {}", instanceId, accountName);
        String from = "api";
        AccountShowResponse response = null;
        response = v2AccountService.detail(instanceId, accountName, from);
        return response;
    }
    /**
     * 修改权限
     * 根据提供的accountName和instanceId，更新对应的权限信息。
     *
     * @param instanceId 实例ID
     * @param accountName 账户名称
     * @param request 更新权限的请求体，包含更新后的权限信息
     */

    @ApiOperation(value = "修改权限")
    @RequestMapping(value = "/{instanceId}/account/{accountName}/privileges", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePrivileges(@IdPermission @IdMapper @PathVariable String instanceId,
                                 @PathVariable String accountName,
                                 @RequestBody @Valid V2AccountUpdatePrivilegesRequest request) {
        LOGGER.debug("update privilege. instanceId: {}, accountName: {}", instanceId, request.getAccountName());

        String from = "api";
        request.setAccountName(accountName);
        v2AccountService.updatePrivileges(instanceId, request, from);

    }
    /**
      * 删除帐号
      * 根据实例ID和帐号名称删除指定的帐号
      *
      * @param instanceId 实例ID
      * @param accountName 帐号名称
      * @param from 来源，默认为空字符串
      */

    @ApiOperation(value = "删除帐号")
    @RequestMapping(value = "/{instanceId}/account/{accountName}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void delete(@IdPermission @IdMapper @PathVariable String instanceId,
                       @PathVariable String accountName,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. instanceId: {}, accountName: {}", instanceId, accountName);
        try {
            accountService.deleteAccount(instanceId, accountName, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 修改密码
     * 该函数用于修改指定账户的密码
     *
     * @param instanceId 实例ID
     * @param accountName 账户名称
     * @param pwRequest 密码修改请求体
     * @param httpServletRequest HttpServletRequest对象，用于获取请求头中的信息
     */

    @ApiOperation(value = "修改密码")
    @RequestMapping(value = "/{instanceId}/account/{accountName}/password", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePW(@IdPermission @IdMapper @PathVariable String instanceId,
                         @PathVariable String accountName,
                         @RequestBody @Valid AccountUpdatePasswordRequest pwRequest,
                         HttpServletRequest httpServletRequest) {
        LOGGER.debug("update password. instanceId: {}, accountName: {}", instanceId, accountName);

        String from = "api";
        String ak = httpServletRequest.getHeader(RDSConstant.X_BCE_ACCESS_KEY);
        v2AccountService.updatePW(instanceId, accountName, pwRequest, from, ak);

    }

    @ApiOperation("表粒度：新增用户")
    @RequestMapping(value = "/{instanceId}/account/tableLevel/create", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void createTableLevel(@PathVariable("instanceId") @IdPermission @IdMapper String instanceId,
                                 @Valid @RequestBody TableLevelAccountDetailRequest account,
                                 HttpServletRequest httpServletRequest) {
        String ak = httpServletRequest.getHeader(RDSConstant.X_BCE_ACCESS_KEY);
        v2AccountService.createTableLevel(instanceId, account, "api", ak);
    }

    @ApiOperation("表粒度：查询用户列表")
    @RequestMapping(value = "/{instanceId}/account/tableLevel/list", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public TableLevelListResponse listTableLevel(@PathVariable("instanceId") @IdPermission @IdMapper String instanceId) {
        EdpResultResponse<TableLevelListResponse> response = new EdpResultResponse<>();
        return v2AccountService.listTableLevel(instanceId, "");
    }

    @ApiOperation("表粒度：查询用户详情")
    @RequestMapping(value = "/account/tableLevel/detail", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public TableLevelAccountDetailRequest detailTableLevel(
            @IdPermission @Valid @RequestBody TableLevelDeleteAccountRequest request) {
        if (BasisUtils.isShortId(request.getInstanceId())) {
            String instanceUuid = idMapperService.getInstanceUuid(request.getInstanceId());
            request.setInstanceId(instanceUuid);
        }
        return v2AccountService.detailTableLevel(request, "");
    }

    @ApiOperation("表粒度：更新用户描述信息")
    @RequestMapping(value = "/account/tableLevel/update_desc", method = RequestMethod.PUT)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateDescTableLevel(@IdPermission @Valid @RequestBody TableLevelUpdateDescRequest request) {
        if (BasisUtils.isShortId(request.getInstanceId())) {
            String instanceUuid = idMapperService.getInstanceUuid(request.getInstanceId());
            request.setInstanceId(instanceUuid);
        }
        v2AccountService.updateRemarkTableLevel(request);
    }

    @ApiOperation("表粒度：更新用户密码")
    @RequestMapping(value = "/account/tableLevel/update_password", method = RequestMethod.PUT)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePasswordTableLevel(
            @IdPermission @Valid @RequestBody TableLevelUpdatePasswordRequest request,
            HttpServletRequest httpServletRequest) {
        if (BasisUtils.isShortId(request.getInstanceId())) {
            String instanceUuid = idMapperService.getInstanceUuid(request.getInstanceId());
            request.setInstanceId(instanceUuid);
        }
        String ak = httpServletRequest.getHeader(RDSConstant.X_BCE_ACCESS_KEY);
        v2AccountService.updatePWTableLevel(request, "api", ak);
    }

    @ApiOperation("表粒度：更新用户权限")
    @RequestMapping(value = "/account/tableLevel/update_permission", method = RequestMethod.PUT)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePermissionTableLevel(
            @IdPermission @Valid @RequestBody TableLevelUpdatePrivilegeRequest request) {
        if (BasisUtils.isShortId(request.getInstanceId())) {
            String instanceUuid = idMapperService.getInstanceUuid(request.getInstanceId());
            request.setInstanceId(instanceUuid);
        }
        v2AccountService.updatePrivilegesTableLevel(request, "");
    }

    @ApiOperation("表粒度：删除用户")
    @RequestMapping(value = "/account/tableLevel/delete", method = RequestMethod.PUT)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void deleteTableLevel(@IdPermission @Valid @RequestBody TableLevelDeleteAccountRequest request) {
        if (BasisUtils.isShortId(request.getInstanceId())) {
            String instanceUuid = idMapperService.getInstanceUuid(request.getInstanceId());
            request.setInstanceId(instanceUuid);
        }
        v2AccountService.deleteAccountTableLevel(request, "");
    }

    @RequestMapping(value = "/account/check", method = RequestMethod.POST)
    @ResponseBody
    public void check(@Valid @RequestBody DashboardAccountCheckRequest request) {
        if (BasisUtils.isShortId(request.getInstanceId())) {
            String instanceUuid = idMapperService.getInstanceUuid(request.getInstanceId());
            request.setInstanceId(instanceUuid);
        }
        IsExistResponse isExistResponse =
                accountService.accountCheck(request.getInstanceId(), request.getAccountName());
    }

    @RequestMapping(value = "/{instanceId}/account/scope", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public TableLevelPrivilegeScopeResponse permissionScope(
            @IdPermission @PathVariable("instanceId") @IdMapper String instanceId) {
        return v2AccountService.permissionScope(instanceId, "");
    }

    @ApiOperation(value = "表粒度：查询数据库列表v2接口")
    @RequestMapping(value = "/account/v2/tablelevel/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public TableLevelListDatabaseResponse tableLevelListdatabasesV2(
            @IdPermission @Valid @RequestBody ListTableRequest requestBody) {
        if (BasisUtils.isShortId(requestBody.getInstanceId())) {
            String instanceUuid = idMapperService.getInstanceUuid(requestBody.getInstanceId());
            requestBody.setInstanceId(instanceUuid);
        }
        return databaseService.tableLevelListdatabasesV2(requestBody);
    }
}
