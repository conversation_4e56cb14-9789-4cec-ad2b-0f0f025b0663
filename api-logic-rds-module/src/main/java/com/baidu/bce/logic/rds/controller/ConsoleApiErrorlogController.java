package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.core.BceFormat;
import com.baidu.bce.internalsdk.rds.model.errorlog.DashboardBinlogListRequest;
import com.baidu.bce.internalsdk.rds.model.errorlog.DashboardErrorlogUrlRequest;
import com.baidu.bce.internalsdk.rds.model.errorlog.Errorlog;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogDetailRequest;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogErrorDetails;
import com.baidu.bce.internalsdk.rds.model.errorlog.Url;
import com.baidu.bce.logic.rds.service.ErrorlogService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

/**
 * Created by hejianbin on 2014/6/20.
 */
@Controller
@RequestMapping(value = "/api/rds/errorlog", produces = {"application/json"})
@Api(value = "RDS Dashboard Errorlog管理API")
public class ConsoleApiErrorlogController {

    @Autowired
    private ErrorlogService errorlogService;
    /**
     * 获取错误日志列表
     * 根据请求参数，获取对应实例和日期的错误日志列表
     * 
     * @param request 包含实例ID和日期的请求体
     * @return 错误日志列表的响应对象
     */

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpPageResultResponse<Errorlog> list(@IdPermission @Valid @RequestBody DashboardBinlogListRequest request) {
        EdpPageResultResponse<Errorlog> listResponse = new EdpPageResultResponse<>();
        listResponse.getPage().setResult(errorlogService.list(request.getInstanceId(),
                BceFormat.getDateTimeFormat().format(request.getDate())).getErrorlogs());
        return listResponse;
    }
    /**
      * 下载错误日志URL接口
      * 根据实例ID、错误日志ID和下载有效期获取错误日志的下载URL
      * 
      * @param request 包含实例ID、错误日志ID和下载有效期的请求体
      * @return 包含下载URL和过期时间的响应体
      */

    @RequestMapping(value = "download_url", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Url> downloadUrl(@IdPermission @Valid @RequestBody DashboardErrorlogUrlRequest request) {
        EdpResultResponse<Url> response = new EdpResultResponse<>();
        Errorlog errorlog = errorlogService.detail(request.getInstanceId(), request.getErrorlogId(),
                request.getDownloadValidTime() * 60).getErrorlog();
        Url url = new Url().url(errorlog.getDownloadUrl()).expire(errorlog.getDownloadExpires());
        response.withResult(url);
        return response;
    }
    /**
      * 获取错误日志详情
      * 通过接收前端传来的日志详情请求参数，查询并返回错误日志的详细信息
      * 
      * @param logDetailRequest 日志详情请求参数，包含查询所需的条件
      * @return 返回错误日志的详细信息
      */

    @RequestMapping(value = "/getErrorLogDetails", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogErrorDetails getErrorLogDetails(@RequestBody @Valid LogDetailRequest logDetailRequest) {
        return errorlogService.getErrorLogDetails(logDetailRequest);
    }

}
