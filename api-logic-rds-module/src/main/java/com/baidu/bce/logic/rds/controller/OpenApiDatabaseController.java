package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import com.baidu.bce.internalsdk.rds.model.database.Database;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseChecksizeResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListV2Response;
import com.baidu.bce.internalsdk.rds.model.database.TableListResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCheckExistRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseOwnerRequest;
import com.baidu.bce.logic.rds.service.DatabaseService;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.model.IsExistResponse;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;


import javax.validation.Valid;

/**
 * Created by luping03 on 17/10/10.
 */
@RestController
@RequestMapping("/v1/instance/{instanceId}/databases")
public class OpenApiDatabaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiDatabaseController.class);

    @Autowired
    private DatabaseService databaseService;

    @Autowired
    private InstanceService instanceService;
    
    /**
      * 获取数据库列表
      * 通过instanceId和from参数获取数据库列表信息
      *
      * @param instanceId 数据库实例ID
      * @param from 请求来源，默认为"api"
      * @return 数据库列表响应对象
      */            
    @ApiOperation(value = "获取数据库列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public DatabaseListResponse getList(@IdPermission @IdMapper @PathVariable String instanceId,
                                        @RequestParam(required = false, defaultValue = "api") String from) {
        LOGGER.debug("get database list. instanceId: {}", instanceId);
        DatabaseListResponse response = databaseService.list(instanceId, from);
        return response;
    }
    
    /**
      * 创建数据库
      * 根据提供的instanceId和database信息，创建数据库。支持通过from参数指定创建来源，默认为"api"。
      * 
      * @param instanceId 数据库实例ID
      * @param database 数据库信息
      * @param from 创建来源，默认为"api"
      */
    @ApiOperation(value = "创建数据库")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void create(@IdPermission @IdMapper @PathVariable String instanceId,
                       @Valid @RequestBody Database database,
                       @RequestParam(required = false, defaultValue = "api") String from) {
        LOGGER.debug("create database. instanceId: {}", instanceId);
        databaseService.create(instanceId, database, from);
    }
    /**
      * 修改描述信息
      * 根据instanceId和dbName修改数据库的描述信息
      * 
      * @param instanceId 实例ID
      * @param dbName 数据库名称
      * @param remarkRequest 数据库描述信息请求体
      * @param from 请求来源
      */

    @ApiOperation(value = "修改描述信息")
    @RequestMapping(value = "/{dbName}/remark", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateRemark(@IdPermission @PathVariable String instanceId,
                             @PathVariable String dbName,
                             @RequestBody @Valid DatabaseUpdateRemarkRequest remarkRequest,
                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update remark. instanceId: {},dbName: {}", instanceId, dbName);
        try {
            if (BasisUtils.isShortId(instanceId)) {
                instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
            }
            databaseService.updateRemark(instanceId, dbName, remarkRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 删除数据库
      * 根据实例ID和数据库名删除数据库
      * 
      * @param instanceId 实例ID
      * @param dbName 数据库名
      * @param from 请求来源，默认为空
      */

    @ApiOperation(value = "删除数据库")
    @RequestMapping(value = "/{dbName}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void delete(@IdPermission @PathVariable String instanceId,
                       @PathVariable String dbName,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. instanceId: {},dbName: {}", instanceId, dbName);
        try {
            if (BasisUtils.isShortId(instanceId)) {
                instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
            }
            databaseService.delete(instanceId, dbName);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 查询数据库是否已存在
      * 验证指定数据库是否存在于系统中
      * 
      * @param instanceId 实例ID
      * @param dbName 数据库名称
      * @param databaseUpdateRemarkRequest 数据库更新备注请求
      * @param from 请求来源
      * @return 数据库存在验证结果
      */

    @ApiOperation(value = "查询数据库是否已存在")
    @RequestMapping(value = "/{dbName}/isExist", method = RequestMethod.GET)
    public IsExistResponse check(@PathVariable String instanceId,
                                 @PathVariable String dbName,
                                 @RequestBody DatabaseUpdateRemarkRequest databaseUpdateRemarkRequest,
                                 @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. instanceId: {},dbName: {}", instanceId, dbName);
        IsExistResponse response = null;
        try {
            if (!RDSConstant.FROM_API.equals(from)) {
                dbName = databaseUpdateRemarkRequest.getDbName();
            }
            if (BasisUtils.isShortId(instanceId)) {
                instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
            }
            response = databaseService.databaseCheck(instanceId, dbName);

        } catch (Exception ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return response;
    }
    /**
     * 获取实例下的库/表及表大小
     * 根据实例ID获取实例下的库/表及表大小
     * 
     * @param instanceId 实例ID
     * @param from 请求参数，默认值为空字符串
     * @return DatabaseChecksizeResponse 实例下的库/表及表大小信息
     */

    @ApiOperation(value = "获取实例下的库/表及表大小")
    @RequestMapping(value = "/checksize", method = RequestMethod.GET)
    public DatabaseChecksizeResponse checksize(@PathVariable String instanceId,
                                               @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("checksize. instanceId: {},dbName: {}", instanceId);
        DatabaseChecksizeResponse response = null;
        try {
            response = databaseService.checksize(instanceId);

        } catch (Exception ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return response;
    }
    /**
      * 获取实例的数据占用的磁盘空间、剩余磁盘空间和数据库大小
      * 通过实例ID和可选的起始位置参数，获取数据库的列表信息
      * 
      * @param instanceId 实例ID
      * @param from 起始位置，可选参数
      * @return 数据库列表信息
      */

    @ApiOperation(value = "获取实例的数据占用的磁盘空间、剩余磁盘空间和数据库大小")
    @RequestMapping(value = "/listdatabases", method = RequestMethod.GET)
    public DatabaseListV2Response listdatabases(@PathVariable String instanceId,
                                               @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("checksize. instanceId: {},dbName: {}", instanceId);
        DatabaseListV2Response response = null;
        try {
            response = databaseService.listdatabases(instanceId);

        } catch (Exception ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return response;
    }

    /**
     * 获取实例指定库下满足条件的表的大小
     * 通过instanceId, pattern, dbName获取数据库下满足条件的表的大小
     * 
     * @param instanceId 实例ID
     * @param pattern 表名模式，默认为空
     * @param dbName 数据库名称，默认为空
     * @param from 起始位置，默认为空
     * @return 返回满足条件的表的大小列表
     */
    @ApiOperation(value = "获取实例指定库下满足条件的表的大小")
    @RequestMapping(value = "/listtables", method = RequestMethod.GET)
    public TableListResponse listtables(@PathVariable String instanceId,
                                           @RequestParam(required = false, defaultValue = "") String pattern,
                                           @RequestParam(required = false, defaultValue = "") String dbName,
                                              @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("checksize. instanceId: {},dbName: {}", instanceId);
        TableListResponse response = null;
        try {
            response = databaseService.listtables(instanceId, pattern, dbName);

        } catch (Exception ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return response;
    }
    /**
     * 获取实例指定库下满足条件的表的大小
     * 根据实例ID和请求参数，检查数据库是否存在并返回满足条件的表的大小
     * 
     * @param instanceId 实例ID
     * @param request 请求参数，包含数据库名和其他条件
     * @return 返回检查结果
     */

    @ApiOperation(value = "获取实例指定库下满足条件的表的大小")
    @RequestMapping(value = "/checkdbexist", method = RequestMethod.POST)
    public DatabaseCheckExistResponse checkdbexist(@PathVariable String instanceId,
                                          @RequestBody @Valid DatabaseCheckExistRequest request) {
        LOGGER.debug("checkdbexist. instanceId: {},dbName: {}", instanceId);
        DatabaseCheckExistResponse response = null;
        try {
            response = databaseService.checkdbexist(instanceId, request);

        } catch (Exception ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return response;
    }
    /**
     * 更改数据库属主
     * 更新数据库所属的用户信息
     * 
     * @param instanceId 实例ID
     * @param dbName 数据库名称
     * @param request 新的数据库属主信息
     */

    @ApiOperation(value = "更改数据库属主")
    @RequestMapping(value = "/{dbName}/owner", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void databaseModifyOwner(@IdPermission @PathVariable @IdMapper String instanceId,
                       @PathVariable String dbName, @RequestBody @Valid DatabaseOwnerRequest request) {
        LOGGER.debug("update owner. instanceId: {},dbName: {}", instanceId, dbName);
        try {
            request.setDbName(dbName);
            databaseService.databaseModifyOwner(instanceId, dbName, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }


}
