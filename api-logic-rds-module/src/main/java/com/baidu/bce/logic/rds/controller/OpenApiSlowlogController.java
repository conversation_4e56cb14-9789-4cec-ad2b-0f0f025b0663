package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogDetailRequest;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogSlowDetails;
import com.baidu.bce.internalsdk.rds.model.slowlog.Slowlog;
import com.baidu.bce.internalsdk.rds.model.slowlog.OpenapiSlowlogListResponse;
import com.baidu.bce.logic.rds.model.Url;
import com.baidu.bce.logic.rds.service.ErrorlogService;
import com.baidu.bce.logic.rds.service.SlowlogService;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.logic.rds.model.DownloadUrlResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

@RestController
@RequestMapping("/v1/instance/{instanceId}/slowlogs")
public class OpenApiSlowlogController extends BaseController{
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiSlowlogController.class);

    @Autowired
    private ErrorlogService errorlogService;

    @Autowired
    private SlowlogService slowlogService;
    /**
     * 获取慢日志详情
     * 根据提供的实例ID和日志详情请求，获取对应的慢日志详情信息。
     * 
     * @param instanceId 实例ID
     * @param logDetailRequest 日志详情请求对象，包含获取慢日志所需的参数
     * @return 慢日志详情对象
     */

    @ApiOperation(value = "获取慢日志详情")
    @RequestMapping(value = "/details", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogSlowDetails getSlowLogDetails(@IdPermission @IdMapper @PathVariable String instanceId
            , @RequestBody @Valid LogDetailRequest logDetailRequest) {
        LOGGER.debug("log slowLogDetail.");
        LogSlowDetails response = new LogSlowDetails();
        try {
            logDetailRequest.setInstanceId(instanceId);
            response = errorlogService.getSlowLogDetails(logDetailRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取慢日志列表
     * 通过实例ID和日期时间获取慢日志列表
     * 
     * @param instanceId 实例ID
     * @param datetime 日期时间
     * @param from 起始位置（可选，默认为空）
     * @return 慢日志列表响应对象
     */

    @ApiOperation(value = "获取慢日志列表")
    @RequestMapping(value = "/logList/{datetime}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenapiSlowlogListResponse getList(@IdPermission @IdMapper @PathVariable String instanceId,
                                       @PathVariable String datetime,
                                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log list.");
        OpenapiSlowlogListResponse response = null;
        try {
            response = slowlogService.list2(instanceId, datetime);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取慢日志下载详情
      * 根据实例ID、日志ID和下载有效时间，获取慢日志的下载详情
      * 
      * @param instanceId 实例ID
      * @param logId 日志ID
      * @param downloadValidTimeInSec 下载有效时间
      * @param from 请求来源，非必填
      * @return 返回慢日志的下载URL和过期时间
      * @throws RDSExceptions.ParamValidationException 当downloadValidTimeInSec不是正整数时抛出异常
      */

    @ApiOperation(value = "获取慢日志下载详情")
    @RequestMapping(value = "/download_url/{logId:.+}/{downloadValidTimeInSec}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public Url detail(@IdPermission @IdMapper @PathVariable String instanceId,
                                     @PathVariable String logId,
                                     @PathVariable Integer downloadValidTimeInSec,
                                     @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log detail.");
        DownloadUrlResponse response = new DownloadUrlResponse();
        Url url = null;
        if (downloadValidTimeInSec < 0){
            throw new RDSExceptions.ParamValidationException("downloadValidTimeInSec not positive integer");
        }
        try {
            Slowlog slowlog = slowlogService.detail(instanceId, logId, downloadValidTimeInSec).getSlowlog();
            url = new Url().url(slowlog.getDownloadUrl()).expire(slowlog.getDownloadExpires());
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return url;
    }
}
