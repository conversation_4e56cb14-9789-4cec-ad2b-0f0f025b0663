package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.idempotent.annotation.Idempotent;
import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.AccountGetResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountListResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateLockRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePrivilegesRequest;
import com.baidu.bce.logic.rds.service.AccountService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * Created by luping03 on 17/10/10.
 */
@RestController
@RequestMapping("/v1/instance/{instanceId}/account")
public class OpenApiAccountController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiAccountController.class);

    @Autowired
    private AccountService accountService;
    /**
     * 创建帐号
     * 用于创建新的帐号，并通过服务进行帐号信息的设置
     * 
     * @param instanceId 实例ID，用于标识在哪个实例下创建帐号
     * @param request 帐号创建请求体，包含创建帐号所需的信息
     * @param httpServletRequest HttpServletRequest对象，用于获取请求头等信息
     * @return 无返回结果
     */

    @ApiOperation(value = "创建帐号")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void create(@IdPermission @IdMapper @PathVariable String instanceId,
                       @Valid @RequestBody Account request,
                       HttpServletRequest httpServletRequest) {
        String from = "api";
        request.setSuperUserFlag(request.getAccountType());
        request.setRemark(request.getDesc());

        LOGGER.debug("create account. instanceId is {}", instanceId);
        AccountListResponse response = null;
        try {
            String ak = httpServletRequest.getHeader(RDSConstant.X_BCE_ACCESS_KEY);
            accountService.create(instanceId, request, from, ak);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    
    /**
      * 获取帐号列表
      * 获取指定实例下的帐号列表，并对帐号的状态、类型、描述进行处理
      * 
      * @param instanceId 实例ID
      * @return 帐号列表响应对象
      */
    @ApiOperation(value = "获取帐号列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public AccountListResponse getList(@IdPermission @IdMapper @PathVariable String instanceId) {
        String from = "api";

        LOGGER.debug("get account list. instanceId is {}", instanceId);
        AccountListResponse response = null;
        try {
            response = accountService.list(instanceId, from);

            if (response.getAccounts() != null) {
                for (Account account : response.getAccounts()) {
                    account.setStatus(account.getAccountStatus());
                    account.setAccountType(account.getSuperUserFlag());
                    account.setDesc(account.getRemark());
                }
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取帐号详情
      * 根据实例ID和帐号名称获取帐号详情信息
      *
      * @param instanceId 实例ID
      * @param accountName 帐号名称
      * @return 帐号详情对象
      */

    @ApiOperation(value = "帐号详情")
    @RequestMapping(value = "/{accountName}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public Account detail(@IdPermission @IdMapper @PathVariable String instanceId,
                                     @PathVariable String accountName) {
        String from = "api";

        LOGGER.debug("create account. instanceId: {}, accountName: {}", instanceId, accountName);
        AccountGetResponse response = null;
        Account account = null;
        try {
            response = accountService.detail(instanceId, accountName, from);
            account = response.getAccount();
            account.setStatus(account.getAccountStatus());
            account.setAccountType(account.getSuperUserFlag());
            account.setDesc(account.getRemark());
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return account;
    }
    /**
      * 删除帐号
      * 根据instanceId和accountName删除指定的帐号
      * 
      * @param instanceId 实例ID
      * @param accountName 帐号名称
      */

    @ApiOperation(value = "删除帐号")
    @RequestMapping(value = "/{accountName}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void delete(@IdPermission @PathVariable String instanceId,
                       @PathVariable String accountName) {
        String from = "api";

        LOGGER.debug("update privilege. instanceId: {}, accountName: {}", instanceId, accountName);
        try {
            accountService.deleteAccount(instanceId, accountName, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 更改帐号
      * 更改指定帐号的锁定状态
      * 
      * @param instanceId 实例ID
      * @param accountName 帐号名称
      * @param request 帐号更新锁定请求体
      */

    @ApiOperation(value = "更改帐号")
    @RequestMapping(value = "/{accountName}/lock", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void delete(@IdPermission @PathVariable String instanceId,
                       @PathVariable String accountName, @Valid @RequestBody AccountUpdateLockRequest request) {
        String from = "api";

        LOGGER.debug("update lock. instanceId: {}, accountName: {}", instanceId, accountName);
        try {
            accountService.updateLock(instanceId, accountName, request.getLockMode());
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 更改备注
      * 用于更新账户的备注信息。通过PUT方法请求，需要传入实例ID、账户名称以及更新备注的请求体。
      * 
      * @param instanceId 实例ID，用于标识要操作的账户实例
      * @param accountName 账户名称，用于指定要更新备注的账户
      * @param request 更新备注的请求体，包含要更新的备注信息
      */

    @ApiOperation(value = "更改备注")
    @RequestMapping(value = "/{accountName}/desc", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateDesc(@IdPermission @IdMapper @PathVariable String instanceId,
                           @PathVariable String accountName,
                           @RequestBody AccountUpdateRemarkRequest request) {
        accountService.updateRemark(instanceId, accountName, request);
    }
    /**
      * 修改密码
      * 根据accountName和请求体中的新密码信息，更新用户的密码。
      * 
      * @param instanceId 实例ID，用于标识要操作的数据库实例
      * @param accountName 账户名称，标识要修改密码的用户
      * @param request 请求体，包含新密码等信息
      * @param httpServletRequest HttpServletRequest对象，用于获取请求头信息
      */

    @ApiOperation(value = "修改密码")
    @RequestMapping(value = "/{accountName}/password", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePassword(@IdPermission @IdMapper @PathVariable String instanceId,
                               @PathVariable String accountName,
                               @RequestBody AccountUpdatePasswordRequest request,
                               HttpServletRequest httpServletRequest) {
        String from = "api";
        String ak = httpServletRequest.getHeader(RDSConstant.X_BCE_ACCESS_KEY);
        accountService.updatePW(instanceId, accountName, request, from, ak);
    }
    /**
      * 修改权限
      * 用于修改指定accountName的权限信息
      * 
      * @param instanceId 实例ID，用于标识要修改权限的目标实例
      * @param accountName 账户名称，用于标识要修改权限的目标账户
      * @param eTag 请求头中的eTag，用于并发控制
      * @param request 请求体，包含了要修改的权限信息
      */

    @ApiOperation(value = "修改权限")
    @RequestMapping(value = "/{accountName}/privileges", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePermission(@IdPermission @IdMapper @PathVariable String instanceId,
                                 @PathVariable String accountName,
                                 @RequestHeader("x-bce-if-match") String eTag,
                                 @RequestBody AccountUpdatePrivilegesRequest request) {
        String from = "api";
        accountService.updatePrivileges(instanceId, accountName,
                request, eTag, from);
    }

}
