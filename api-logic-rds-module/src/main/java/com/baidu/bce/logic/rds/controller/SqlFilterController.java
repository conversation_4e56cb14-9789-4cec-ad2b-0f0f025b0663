package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.SqlFilterAllowedResponse;
import com.baidu.bce.internalsdk.rds.model.SqlFilterList;
import com.baidu.bce.internalsdk.rds.model.SqlFilterRequest;
import com.baidu.bce.internalsdk.rds.model.SqlFilterResponse;
import com.baidu.bce.logic.rds.service.SqlFilterService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@RestController
@RequestMapping("/v1/rds/instances/{instanceId}/sqlfilter")
public class SqlFilterController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SqlFilterController.class);

    @Autowired
    private SqlFilterService sqlFilterService;
    
    /**
     * 获取实例限流规则列表
     * 根据实例ID获取SQL过滤规则列表
     *
     * @param instanceId 实例ID
     * @return SQL过滤规则列表
     */    
    @ApiOperation(value = "获取实例限流规则列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlFilterList sqlFilterList(@IdPermission @PathVariable String instanceId) {
        LOGGER.debug("get sqlfilter list.");
        SqlFilterList response = null;
        try {
            response = sqlFilterService.sqlFilterList(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取限流规则详情
      * 根据实例ID和限流规则ID获取对应限流规则的详细信息
      * 
      * @param instanceId 实例ID
      * @param sqlFilterId 限流规则ID
      * @return SqlFilterResponse 返回限流规则的详细信息
      */

    @ApiOperation(value = "获取某个限流规则详情")
    @RequestMapping(value = "/{sqlFilterId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlFilterResponse sqlFilterDetail(@IdPermission @PathVariable String instanceId,
                                         @PathVariable String sqlFilterId) {
        LOGGER.debug("get sqlfilter detail.");
        SqlFilterResponse response = null;
        try {
            response = sqlFilterService.sqlFilterDetail(instanceId, sqlFilterId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    
    /**
     * 添加一条限流规则
     * 该函数用于添加一条限流规则，并通过POST方法请求
     * 
     * @param instanceId 实例ID，用于指定要操作的实例
     * @param request 包含限流规则详细信息的请求体
     */
    @ApiOperation(value = "添加一条限流规则")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void addSqlFilter(@IdPermission @PathVariable String instanceId,
                             @Valid @RequestBody SqlFilterRequest request) {
        LOGGER.debug("get sqlfilter add.");
        try {
            sqlFilterService.addSqlFilter(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 更新限流规则
      * 更新一条限流规则
      *
      * @param instanceId 实例ID
      * @param sqlFilterId SQL过滤ID
      * @param request SQL过滤请求
      */

    @ApiOperation(value = "更新一条限流规则")
    @RequestMapping(value = "/{sqlFilterId}", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateSqlFilter(@IdPermission @PathVariable String instanceId,
                                @PathVariable String sqlFilterId, @Valid @RequestBody SqlFilterRequest request) {
        LOGGER.debug("get sqlfilter update.");
        try {
            sqlFilterService.updateSqlFilter(instanceId, sqlFilterId , request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 开启或关闭某个限流规则
     * 根据实例ID和限流规则ID开启或关闭对应的限流规则
     * 
     * @param instanceId 实例ID
     * @param sqlFilterId 限流规则ID
     * @param request 限流规则请求体
     */

    @ApiOperation(value = "开启|关闭某个限流规则")
    @RequestMapping(value = "/{sqlFilterId}", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void actionSqlFilter(@IdPermission @PathVariable String instanceId,
                                @PathVariable String sqlFilterId, @Valid @RequestBody SqlFilterRequest request) {
        LOGGER.debug("get sqlfilter action.");
        try {
            sqlFilterService.actionSqlFilter(instanceId, sqlFilterId , request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 删除某个限流规则
     * 删除指定实例下的限流规则
     *
     * @param instanceId 实例ID
     * @param sqlFilterId 限流规则ID
     */

    @ApiOperation(value = "删除某个限流规则")
    @RequestMapping(value = "/{sqlFilterId}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void deleteSqlFilter(@IdPermission @PathVariable String instanceId,
                                @PathVariable String sqlFilterId) {
        LOGGER.debug("get sqlfilter delete.");
        try {
            sqlFilterService.deleteSqlFilter(instanceId, sqlFilterId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 实例是否支持限流
     * 该函数用于判断实例是否支持SQL过滤功能。
     * 
     * @param instanceId 实例ID
     * @return SqlFilterAllowedResponse 返回实例是否支持SQL过滤的结果
     */

    @ApiOperation(value = "实例是否支持限流")
    @RequestMapping(value = "/allowed", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlFilterAllowedResponse allowedSqlFilter(@IdPermission @PathVariable String instanceId) {
        SqlFilterAllowedResponse response = null;
        LOGGER.debug("get sqlfilter allowed.");
        try {
            response = sqlFilterService.allowedSqlFilter(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }


}
