package com.baidu.bce.logic.rds.controller.group;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.CheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.database.Database;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseGetResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseUpdateRemarkRequest;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.group.InstanceGroupService;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Created by shiyuxin on 19/5/20.
 */
@RestController
@RequestMapping("/v1/rds/group/{groupId}/databases")
public class GroupDatabaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(GroupDatabaseController.class);

    @Autowired
    private InstanceGroupService instanceGroupService;
    
    /**
      * 获取数据库列表
      * 根据实例组ID获取数据库列表
      *
      * @param groupId 实例组ID
      * @return 数据库列表响应
      */    
    @ApiOperation(value = "实例组：获取数据库列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public DatabaseListResponse getList(@IdPermission @PathVariable String groupId) {
        LOGGER.debug("get database list. groupId: {}", groupId);
        DatabaseListResponse response = null;
        try {
            response = instanceGroupService.databaseList(groupId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 实例组: 数据库详情
     * 根据groupId和dbName获取数据库详情
     * 
     * @param groupId 实例组ID
     * @param dbName 数据库名称
     * @return 数据库详情
     */

    @ApiOperation(value = "实例组: 数据库详情")
    @RequestMapping(value = "/{dbName}", method = RequestMethod.GET)
    public DatabaseGetResponse detail(@PathVariable String groupId,
                                      @PathVariable String dbName) {

        LOGGER.debug("get database list. groupId: {}", groupId);
        DatabaseGetResponse response = null;
        try {
            response = instanceGroupService.databaseDetail(groupId, dbName);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 检查数据库名称是否存在
     * 该函数用于检查指定实例组下的数据库名称是否已经存在
     * 
     * @param groupId 实例组ID
     * @param dbName 数据库名称
     * @return 返回检查结果，CheckExistResponse对象
     */

    @ApiOperation(value = "实例组：检查数据库名称是否存在")
    @RequestMapping(value = "/{dbName}/isExist", method = RequestMethod.GET)
    public CheckExistResponse checkDbNameExist(@PathVariable String groupId,
                                               @PathVariable String dbName) {
        LOGGER.debug("update privilege. instanceId: {},dbName: {}", groupId, dbName);
        CheckExistResponse response = null;
        try {
            response = instanceGroupService.databaseCheck(groupId, dbName);

        } catch (Exception ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return response;
    }

    /**
     * 实例组：创建数据库
     * 实例组：创建数据库
     *
     * @param groupId 实例组ID
     * @param database 数据库对象
     * @return 无返回值
     */
    @RequestMapping(method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 创建数据库")
    public void create(@PathVariable String groupId,
                       @Valid @RequestBody Database database) {
        LOGGER.debug("create database. groupId: {}", groupId);
        try {
            instanceGroupService.databaseCreate(groupId, database);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 修改数据库备注
      * 根据提供的groupId、dbName和remarkRequest，修改指定数据库的备注信息
      * 
      * @param groupId 实例组ID
      * @param dbName 数据库名称
      * @param remarkRequest 修改备注的请求体
      */

    @RequestMapping(value = "/{dbName}/remark", method = RequestMethod.PUT)
    @ApiOperation(value = "实例组: 修改数据库备注")
    public void updateRemark(@PathVariable String groupId,
                             @PathVariable String dbName,
                             @RequestBody @Valid DatabaseUpdateRemarkRequest remarkRequest) {
        LOGGER.debug("update remark. groupId: {},dbName: {}", groupId, dbName);
        try {
            instanceGroupService.databaseUpdateRemark(groupId, dbName, remarkRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
      * 删除数据库
      * 根据groupId和dbName删除数据库实例
      * 
      * @param groupId 组ID
      * @param dbName 数据库名称
      */
    @RequestMapping(value = "/{dbName}", method = RequestMethod.DELETE)
    @ApiOperation(value = "实例列表: 删除数据库")
    public void delete(@PathVariable String groupId,
                       @PathVariable String dbName) {
        LOGGER.debug("update privilege. groupId: {},dbName: {}", groupId, dbName);
        try {
            instanceGroupService.databaseDelete(groupId, dbName);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }



}
