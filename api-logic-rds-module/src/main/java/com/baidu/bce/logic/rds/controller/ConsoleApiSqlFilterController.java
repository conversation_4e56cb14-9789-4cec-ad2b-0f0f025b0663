package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.rds.model.SqlFilterAllowedResponse;
import com.baidu.bce.internalsdk.rds.model.SqlFilterList;
import com.baidu.bce.internalsdk.rds.model.SqlFilterRequest;
import com.baidu.bce.internalsdk.rds.model.SqlFilterResponse;
import com.baidu.bce.logic.rds.service.SqlFilterService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/rds/sqlfilter")
public class ConsoleApiSqlFilterController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConsoleApiSqlFilterController.class);

    @Autowired
    private SqlFilterService sqlFilterService;
    /**
      * 实例是否支持限流
      * 判断指定实例是否支持限流
      * 
      * @param request 请求参数，包含instanceId
      * @return EdpResultResponse<SqlFilterAllowedResponse> 返回结果，包含是否支持限流
      */

    @ApiOperation(value = "实例是否支持限流")
    @RequestMapping(value = "/allowed", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SqlFilterAllowedResponse> allowed(
            @IdPermission @Valid @RequestBody SqlFilterRequest request) {
        EdpResultResponse<SqlFilterAllowedResponse> response = new EdpResultResponse<>();
        response.setResult(sqlFilterService.allowedSqlFilter(request.getInstanceId()));
        return response;
    }
    /**
      * 获取实例限流规则列表
      * 根据请求体中的实例ID获取对应的限流规则列表
      * 
      * @param request 包含实例ID的请求体
      * @return EdpResultResponse<SqlFilterList> 包含限流规则列表的响应体
      */

    @ApiOperation(value = "获取实例限流规则列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SqlFilterList> list(
            @IdPermission @Valid @RequestBody SqlFilterRequest request) {
        EdpResultResponse<SqlFilterList> response = new EdpResultResponse<>();
        response.setResult(sqlFilterService.sqlFilterList(request.getInstanceId()));
        return response;
    }
    /**
      * 添加限流规则
      * 添加一条限流规则，接口通过POST方法请求，需要校验权限
      * 
      * @param request 限流规则请求体，包含实例ID和限流规则信息
      * @return EdpResultResponse<Boolean> 返回结果，包含操作是否成功
      */

    @ApiOperation(value = "添加一条限流规则")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> create(@IdPermission @Valid @RequestBody SqlFilterRequest request) {
        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        sqlFilterService.addSqlFilter(request.getInstanceId(), request);
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }
    /**
      * 更新限流规则
      * 通过POST请求更新一条限流规则
      * 
      * @param request 限流规则请求体，包含实例ID、限流规则ID及限流规则内容
      * @return EdpResultResponse<Boolean> 操作结果，包含操作是否成功
      */

    @ApiOperation(value = "更新一条限流规则")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> update(@IdPermission @Valid @RequestBody SqlFilterRequest request) {
        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        sqlFilterService.updateSqlFilter(request.getInstanceId(), request.getSqlFilterId(), request);
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }
    /**
      * 开启或关闭某个限流规则
      * 通过POST请求，根据提供的实例ID和限流规则ID，对指定的限流规则进行开启或关闭操作
      *
      * @param request 包含实例ID和限流规则ID的请求体
      * @return EdpResultResponse<Boolean> 操作结果，包含是否成功执行
      */

    @ApiOperation(value = "开启|关闭某个限流规则")
    @RequestMapping(value = "/action", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> action(@IdPermission @Valid @RequestBody SqlFilterRequest request) {
        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        sqlFilterService.actionSqlFilter(request.getInstanceId(), request.getSqlFilterId() , request);
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }
    /**
      * 获取限流规则详情
      * 根据实例ID和限流规则ID获取某个限流规则的详细信息
      * 
      * @param request 包含实例ID和限流规则ID的请求体
      * @return 限流规则详情结果
      */

    @ApiOperation(value = "获取某个限流规则详情")
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SqlFilterResponse> detail(@IdPermission @Valid @RequestBody SqlFilterRequest request) {
        EdpResultResponse<SqlFilterResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(sqlFilterService.sqlFilterDetail(request.getInstanceId(), request.getSqlFilterId()));
        return edpResultResponse;
    }
    /**
     * 删除限流规则
     * 删除指定的限流规则
     * 
     * @param request 包含instanceId和sqlFilterId的请求体
     * @return EdpResultResponse<Boolean> 返回操作结果
     */

    @ApiOperation(value = "删除某个限流规则")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> delete(@IdPermission @Valid @RequestBody SqlFilterRequest request) {
        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        sqlFilterService.deleteSqlFilter(request.getInstanceId(), request.getSqlFilterId());
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }

}
