package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogDateTime;
import com.baidu.bce.internalsdk.rds.model.binlog.OpenapiBinlogListResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.OpenapiBinlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.PgLogListResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.PglogDownloadResponse;
import com.baidu.bce.logic.rds.service.BinlogService;
import com.baidu.bce.logic.rds.service.SlowlogService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * Created by luping03 on 17/11/3.
 */
@RestController
@RequestMapping("/v1/instance/{instanceId}")
public class OpenApiBinlogController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private BinlogService binlogService;

    @Autowired
    private SlowlogService slowlogService;
    /**
      * 获取日志列表
      * 根据实例ID和日期获取日志列表
      *
      * @param instanceId 实例ID
      * @param datetime 日期
      * @param from 开始位置
      * @return 日志列表
      */

    @ApiOperation(value = "获取日志列表")
    @RequestMapping(value = "/binlogs/{datetime}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenapiBinlogListResponse getList(@IdPermission @PathVariable @IdMapper String instanceId,
                                             @PathVariable String datetime,
                                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log list. instanceId: {}", instanceId);
        OpenapiBinlogListResponse response = null;
        try {
            response = binlogService.list2(instanceId, datetime);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取日志详情
      * 根据提供的实例ID、日志ID和下载有效时间，获取日志的详细信息。
      * 
      * @param instanceId 实例ID
      * @param logId 日志ID
      * @param downloadValidTimeInSec 下载有效时间
      * @param from 请求来源，默认为空字符串
      * @return 日志详情
      */

    @ApiOperation(value = "获取日志详情")
    @RequestMapping(value = "/binlogs/{logId:.+}/{downloadValidTimeInSec}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenapiBinlogGetResponse detail(@IdPermission @PathVariable @IdMapper String instanceId,
                                           @PathVariable String logId,
                                           @PathVariable Integer downloadValidTimeInSec,
                                           @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log detail. instanceId: {}, logId: {}", instanceId, logId);
        OpenapiBinlogGetResponse response = null;
        try {
            response = binlogService.detail2(instanceId, logId, downloadValidTimeInSec);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 检查时间是否可以作为克隆实例的备份恢复时间点
     * 该函数用于检查传入的instanceId和dateTime是否可以作为克隆实例的备份恢复时间点
     * 
     * @param instanceId 实例ID，用于标识具体的实例
     * @param dateTime 备份恢复时间点，用于检查该时间点是否可以作为备份恢复时间点
     * @param from 请求来源，用于标识请求的来源（可选）
     */

    @ApiOperation(value = "检查时间是否可以作为克隆实例的备份恢复时间点")
    @RequestMapping(value = "/binlogs/check", method = RequestMethod.POST)
    public void check(@PathVariable @IdMapper @IdPermission String instanceId,
                      @RequestBody @Valid BinlogDateTime dateTime,
                      @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log detail. instanceId: {}", instanceId);
        try {
            binlogService.check(instanceId, dateTime);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 刷新Binlog接口
      * 该接口用于刷新指定RDS实例的binlog
      *
      * @param instanceId RDS实例ID
      */

    @ApiOperation(value = "Flush Binlog")
    @RequestMapping(value = "/binlogs/flush", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void check(@IdPermission @IdMapper @PathVariable  String instanceId) {
        LOGGER.debug("get log flush. instanceId: {}", instanceId);
        try {
            binlogService.binlogFlush(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 获取pg日志列表
      * 根据instanceId和date获取pg日志列表
      * 
      * @param instanceId 实例ID
      * @param date 日期
      * @return PgLogListResponse对象，包含pg日志列表信息
      */

    @ApiOperation("pg 日志列表")
    @RequestMapping(value = "/pg/list", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public PgLogListResponse pgList(
            @IdPermission @IdMapper @PathVariable(value = "instanceId")  String instanceId,
            @RequestParam(required = true, value = "date") String date) {
        return slowlogService.pgList(instanceId, date);
    }
    /**
      * 下载pg日志
      * 通过instanceId,pglogId,downloadValidTimeInSec参数下载pg日志
      * 
      * @param instanceId 实例ID
      * @param pglogId pg日志ID
      * @param downloadValidTimeInSec 下载有效时间
      * @return PglogDownloadResponse 下载pg日志的响应
      */

    @ApiOperation("pg 日志下载")
    @RequestMapping(value = "/pg/download", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public PglogDownloadResponse downloadPglog(
            @IdPermission @IdMapper @PathVariable(value = "instanceId")  String instanceId,
            @RequestParam(required = true, value = "pglogId") String pglogId,
            @RequestParam(value = "downloadValidTimeInSec") Integer downloadValidTimeInSec) {
        return slowlogService.downloadPglog(instanceId, pglogId, downloadValidTimeInSec);
    }
}
