package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.AccountGetResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateLockRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePrivilegesRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.account.BnsListResponse;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountCheckRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountCreateRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountDeleteRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountDetailRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountListRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountUpdateDescRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountUpdatePermissionRequest;
import com.baidu.bce.internalsdk.rds.model.account.UpdateBnsRequest;
import com.baidu.bce.internalsdk.rds.model.account.UpdateBnsResponse;
import com.baidu.bce.logic.rds.service.AccountService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.model.IsExistResponse;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/rds/account")
public class ConsoleApiAccountController {
    @Autowired
    private AccountService accountService;
    /**
      * 更改账号
      * 根据传入的账号信息更新账号锁定状态
      * 
      * @param request 账号更新锁定请求对象，包含instanceId、accountName、lockMode等字段
      * @return EdpResultResponse 返回处理结果
      * @throws Exception 可能抛出异常，由LogicRdsExceptionHandler处理
      */

    @ApiOperation(value = "更改账号")
    @RequestMapping(value = "/lock", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse getCreateOrderParam(
            @RequestBody @Valid AccountUpdateLockRequest request) {
        try {
            EdpResultResponse response = new EdpResultResponse();
            accountService.updateLock(request.getInstanceId(), request.getAccountName(), request.getLockMode());
            return response;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }
    /**
      * 获取账户列表
      * 根据请求参数获取对应的账户列表信息
      * 
      * @param requestBody 请求参数，包含账户查询条件
      * @return 账户列表的响应结果
      */

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpPageResultResponse<Account> list(
            @IdPermission @Valid @RequestBody DashboardAccountListRequest requestBody) {
        EdpPageResultResponse<Account> response = new EdpPageResultResponse<>();
        response.getPage().setResult(accountService.list(requestBody.getInstanceId(), "").getAccounts());
        return response;
    }
    /**
      * 创建账户
      * 根据提供的请求参数创建账户，并返回创建结果
      * 
      * @param request 包含创建账户所需参数的请求体
      * @return 创建结果，成功则返回EdpResultResponse对象，包含创建成功的标志
      */

    @RequestMapping(value = "create", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> create(@IdPermission @Valid @RequestBody DashboardAccountCreateRequest request) {
        accountService.create(request.getInstanceId(), request.getAccount(), "", null);
        return new EdpResultResponse<>();
    }
    /**
      * 获取账户详情
      * 根据实例ID和账户名获取账户的详细信息
      * 
      * @param request 包含实例ID和账户名的请求体
      * @return 包含账户详情的响应体
      */

    @RequestMapping(value = "detail", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Account> detail(@IdPermission @Valid @RequestBody DashboardAccountDetailRequest request) {
        AccountGetResponse accountGetResponse =
                accountService.detail(request.getInstanceId(), request.getAccountName(), "");
        EdpResultResponse<Account> accountDetailResponse = new EdpResultResponse<>();
        accountDetailResponse.withResult(accountGetResponse.getAccount());
        return accountDetailResponse;
    }
    /**
      * 更新描述信息
      * 根据请求中的实例ID、账户名称和描述信息，更新账户的描述信息。
      * 
      * @param request 包含实例ID、账户名称和描述信息的请求体
      * @return 返回操作结果
      */

    @RequestMapping(value = "update_desc", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateDesc(
            @IdPermission @Valid @RequestBody DashboardAccountUpdateDescRequest request) {
        AccountUpdateRemarkRequest accountUpdateRemarkRequest = new AccountUpdateRemarkRequest();
        accountUpdateRemarkRequest.setRemark(request.getRemark());
        accountService.updateRemark(request.getInstanceId(), request.getAccountName(), accountUpdateRemarkRequest);
        return new EdpResultResponse<>();
    }
    /**
      * 更新密码
      * 用于更新用户密码，通过接收DashboardAccountUpdatePasswordRequest请求对象，调用accountService.updatePW方法更新密码
      * 
      * @param request 请求对象，包含密码相关信息
      * @return EdpResultResponse对象，表示操作结果
      */

    @RequestMapping(value = "update_password", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updatePassword(
            @IdPermission @Valid @RequestBody DashboardAccountUpdatePasswordRequest request) {
        AccountUpdatePasswordRequest accountUpdatePasswordRequest = new AccountUpdatePasswordRequest();
        accountUpdatePasswordRequest.setPassword(request.getPassword());
        accountUpdatePasswordRequest.setEncryptedPassword(request.getEncryptedPassword());
        accountService.updatePW(request.getInstanceId(), request.getAccountName(), accountUpdatePasswordRequest,
                "", null);
        return new EdpResultResponse<>();
    }
    /**
      * 更新权限
      * 通过传入的数据更新数据库的权限设置
      * 
      * @param request 更新权限的请求参数，包括数据库权限、实例ID、账户名等信息
      * @return 返回更新权限的结果
      */

    @RequestMapping(value = "update_permission", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updatePermission(
            @IdPermission @Valid @RequestBody DashboardAccountUpdatePermissionRequest request) {
        AccountUpdatePrivilegesRequest accountUpdatePrivilegesRequest = new AccountUpdatePrivilegesRequest();
        accountUpdatePrivilegesRequest.setDatabasePrivileges(request.getDatabasePrivileges());
        accountService.updatePrivileges(request.getInstanceId(), request.getAccountName(),
                accountUpdatePrivilegesRequest, request.getETag(), "");
        return new EdpResultResponse<>();
    }
    /**
      * 删除账户接口
      * 删除指定的账户
      * 
      * @param request 删除账户请求对象，包含instanceId（实例ID）、accountName（账户名称）
      * @return EdpResultResponse<Boolean> 返回结果对象，包含操作是否成功
      */

    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> delete(@IdPermission @Valid @RequestBody DashboardAccountDeleteRequest request) {
        accountService.deleteAccount(request.getInstanceId(), request.getAccountName(), "");
        return new EdpResultResponse<>();
    }
    /**
      * 检查账户是否存在接口
      * 根据提供的实例ID和账户名称，检查账户是否已存在
      * 
      * @param request 包含实例ID和账户名称的请求体
      * @return 检查结果，返回EdpResultResponse<Boolean>对象，包含是否存在的结果
      */

    @RequestMapping(value = "check", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> check(@Valid @RequestBody DashboardAccountCheckRequest request) {
        IsExistResponse isExistResponse =
                accountService.accountCheck(request.getInstanceId(), request.getAccountName());
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.withResult(isExistResponse.isExist());
        return response;
    }

    @ApiOperation("获取 bns 列表")
    @RequestMapping(value = "/bnsList", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<BnsListResponse> getAccountBnsList(
            @IdPermission @RequestParam String instanceId,
            @RequestParam String accountName) {
        EdpResultResponse<BnsListResponse> result = new EdpResultResponse<>();
        result.setResult(accountService.accountBnsList(instanceId, accountName));
        return result;
    }

    @ApiOperation("更新 bns 列表")
    @RequestMapping(value = "/bnsList", method = RequestMethod.PUT)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<UpdateBnsResponse> accountBns(
            @IdPermission @Valid @RequestBody UpdateBnsRequest request) {
        EdpResultResponse<UpdateBnsResponse> result = new EdpResultResponse<>();
        result.setResult(accountService.accountBns(request));
        return result;
    }
}
