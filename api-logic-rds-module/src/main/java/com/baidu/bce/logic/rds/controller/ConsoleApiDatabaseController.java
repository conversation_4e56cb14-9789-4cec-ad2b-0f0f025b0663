package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.database.CommonListRequest;
import com.baidu.bce.internalsdk.rds.model.database.DashboardDataBaseCheckRequest;
import com.baidu.bce.internalsdk.rds.model.database.DashboardDatabaseCreateRequest;
import com.baidu.bce.internalsdk.rds.model.database.DashboardDatabaseDeleteRequest;
import com.baidu.bce.internalsdk.rds.model.database.DashboardDatabaseModifyDescRequest;
import com.baidu.bce.internalsdk.rds.model.database.Database;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCheckExistRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseChecksizeResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListV2Response;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseOwnerRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.database.GetTableSizeRequest;
import com.baidu.bce.internalsdk.rds.model.database.GetTableSizeResponse;
import com.baidu.bce.internalsdk.rds.model.database.ListDatabasesRequest;
import com.baidu.bce.internalsdk.rds.model.database.ListDatabasesResponse;
import com.baidu.bce.internalsdk.rds.model.database.ListTableRequest;
import com.baidu.bce.internalsdk.rds.model.database.ListTableResponse;
import com.baidu.bce.internalsdk.rds.model.database.TableLevelListDatabaseResponse;
import com.baidu.bce.internalsdk.rds.model.database.TableListResponse;

import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.logic.rds.service.DatabaseService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.model.IsExistResponse;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/rds/database")
public class ConsoleApiDatabaseController {

    @Autowired
    private DatabaseService databaseService;
    
    /**
     * 列出数据库接口
     * 根据提供的实例ID请求，列出所有数据库信息
     * 
     * @param requestBody 实例ID请求体，包含实例ID
     * @return EdpResultResponse对象，包含数据库列表信息
     */
    @RequestMapping(value = "/listdatabases", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<DatabaseListV2Response> listdatabases(@Valid @RequestBody InstanceIdRequest requestBody) {
        EdpResultResponse<DatabaseListV2Response> resResponse = new EdpResultResponse<>();
        resResponse.setResult(databaseService.listdatabases(requestBody.getInstanceId()));
        return resResponse;
    }
    
    /**
      * 列出表信息
      * 根据提供的实例ID、模式、数据库名称来列出符合条件的表信息
      * 
      * @param requestBody 包含实例ID、模式、数据库名称的请求体
      * @return 返回包含表信息的响应体
      */
    @RequestMapping(value = "/listtables", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<TableListResponse> listtables(@Valid @RequestBody ListTableRequest requestBody) {
        EdpResultResponse<TableListResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(databaseService.listtables(requestBody.getInstanceId(), requestBody.getPattern(),
                requestBody.getDbName()));
        return edpResultResponse;
    }
    /**
     * 查询数据库列表v2接口
     * 该接口用于查询数据库列表，返回查询结果。
     * 
     * @param requestBody 请求参数，包含查询数据库所需的信息
     * @return EdpResultResponse<ListDatabasesResponse> 返回查询结果
     */
    // 为库表恢复提供，v1接口有1000个数量的限制，超过会有性能影响，v2接口进行了优化不再计算size，通过单独的接口请求size
    @ApiOperation(value = "查询数据库列表v2接口")
    @RequestMapping(value = "/v2/listdatabases", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<ListDatabasesResponse> listdatabasesV2(
            @IdPermission @Valid @RequestBody ListDatabasesRequest requestBody) {
        EdpResultResponse<ListDatabasesResponse> resResponse = new EdpResultResponse<>();
        resResponse.setResult(databaseService.listDatabasesv2(requestBody));
        return resResponse;
    }
    /**
     * 细粒度权限管理下查询数据库列表v2接口
     * 该接口用于在细粒度权限管理下，查询数据库列表的版本2。
     * 
     * @param requestBody 请求体，包含查询数据库所需的参数信息
     * @return EdpResultResponse<TableLevelListDatabaseResponse> 返回查询数据库列表的结果
     */

    @ApiOperation(value = "细粒度权限管理下查询数据库列表v2接口")
    @RequestMapping(value = "/v2/tablelevel/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<TableLevelListDatabaseResponse> tableLevelListdatabasesV2(
            @IdPermission @Valid @RequestBody ListTableRequest requestBody) {
        EdpResultResponse<TableLevelListDatabaseResponse> resResponse = new EdpResultResponse<>();
        resResponse.setResult(databaseService.tableLevelListdatabasesV2(requestBody));
        return resResponse;
    }
    /**
      * 查询数据库数据表列表v2接口
      * 该接口用于查询数据库的数据表列表，是版本2的接口。
      * 
      * @param requestBody 请求体，包含查询数据库数据表所需的参数
      * @return 返回查询结果，包含数据表列表
      */
    // 为库表恢复提供，v1接口有1000个数量的限制，超过会有性能影响，v2接口进行了优化不再计算size，通过单独的接口请求size
    @ApiOperation(value = "查询数据库数据表列表v2接口")
    @RequestMapping(value = "/v2/listtables", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<ListTableResponse> listtablesV2(
            @IdPermission @Valid @RequestBody ListTableRequest requestBody) {
        EdpResultResponse<ListTableResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(databaseService.listTablesV2(requestBody));
        return edpResultResponse;
    }
    /**
     * 获取库表的占用空间
     * 该函数用于获取数据库表的占用空间信息
     * 
     * @param request 获取库表占用空间请求体
     * @return EdpResultResponse<GetTableSizeResponse> 库表占用空间响应结果
     */
    // 为库表恢复提供，计算表size
    @ApiOperation(value = "获取库表的占用空间")
    @RequestMapping(value = "/v2/getTableSize", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<GetTableSizeResponse> getTableSize(
            @IdPermission @Valid @RequestBody GetTableSizeRequest request) {
        EdpResultResponse<GetTableSizeResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(databaseService.getTableSize(request));
        return edpResultResponse;
    }
    /**
      * 检查数据库是否存在
      * 根据请求体中的实例ID和请求参数，检查数据库是否存在
      * 
      * @param requestBody 请求体，包含实例ID和请求参数
      * @return 返回检查结果
      */

    @RequestMapping(value = "/checkdbexist", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<DatabaseCheckExistResponse> checkdbexist(
            @Valid @RequestBody DatabaseCheckExistRequest requestBody) {
        EdpResultResponse<DatabaseCheckExistResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(databaseService.checkdbexist(requestBody.getInstanceId(), requestBody));
        return edpResultResponse;
    }
    /**
      * 获取数据库列表
      * 根据请求参数获取数据库列表信息
      * 
      * @param request 请求参数，包含排序字段、排序方式、实例ID等
      * @return EdpPageResultResponse<Database> 数据库列表响应对象
      */

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获阳数据库列表")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    EdpPageResultResponse<Database> list(@IdPermission @Valid @RequestBody CommonListRequest request) {
        EdpPageResultResponse<Database> response = new EdpPageResultResponse<>();
        response.getPage().setOrderBy(request.getOrderBy());
        response.getPage().setOrder(request.getOrder());
        response.getPage().setResult(databaseService.list(request.getInstanceId(), "").getDatabases());
        return response;
    }

    @RequestMapping(value = "update_desc", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    EdpResultResponse<Boolean> updateDesc(
            @IdPermission @Valid @RequestBody DashboardDatabaseModifyDescRequest request) {
        DatabaseUpdateRemarkRequest updateRemarkRequest = new DatabaseUpdateRemarkRequest();
        updateRemarkRequest.setRemark(request.getRemark());
        databaseService.updateRemark(request.getInstanceId(), request.getDbName(), updateRemarkRequest);
        return new EdpResultResponse<>();
    }

    @RequestMapping(value = "create", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    EdpResultResponse<Boolean> create(
            @IdPermission @Valid @RequestBody DashboardDatabaseCreateRequest request) {
        Database database = request.getDatabase();
        databaseService.create(request.getInstanceId(), database, "");
        return new EdpResultResponse<>();
    }

    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    EdpResultResponse<Boolean> delete(@IdPermission @Valid @RequestBody DashboardDatabaseDeleteRequest request) {
        databaseService.delete(request.getInstanceId(), request.getDbName());
        return new EdpResultResponse<>();
    }
    /**
     * 检查数据库接口
     * 检查指定的数据库实例和数据库名称是否存在
     * 
     * @param request 包含数据库实例ID和数据库名称的请求体
     * @return 返回检查结果，包含是否存在的信息
     */

    @RequestMapping(value = "check", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> check(@Valid @RequestBody DashboardDataBaseCheckRequest request) {
        IsExistResponse existResponse = databaseService.databaseCheck(request.getInstanceId(), request.getDbName());
        EdpResultResponse<Boolean> response = new EdpResultResponse<Boolean>();
        response.withResult(existResponse.isExist());
        return response;
    }

    @RequestMapping(value = "checksize", method = RequestMethod.POST)
    @ResponseBody
    EdpResultResponse<DatabaseChecksizeResponse> checksize(@Valid @RequestBody InstanceIdRequest request) {
        DatabaseChecksizeResponse res = databaseService.checksize(request.getInstanceId());
        EdpResultResponse<DatabaseChecksizeResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(res);
        return edpResultResponse;
    }
    /**
      * 更改数据库属主
      * 更改数据库属主的接口函数
      * 
      * @param request 数据库属主更改请求参数，包含实例ID、数据库名称等
      * @return 执行结果
      * @throws Exception 如果执行过程中发生异常，则抛出
      */

    @ApiOperation(value = "更改数据库属主")
    @RequestMapping(value = "/owner", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse getCreateOrderParam(
            @IdPermission @RequestBody @Valid @IdMapper DatabaseOwnerRequest request) {
        try {
            EdpResultResponse response = new EdpResultResponse();
            databaseService.databaseModifyOwner(request.getInstanceId(), request.getDbName(), request);
            return response;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }
}
