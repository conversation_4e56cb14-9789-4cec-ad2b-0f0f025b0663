package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.snapshot.Snapshot;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotGetResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotListWithTimeResponse;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.rds.service.BackupService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by luping03 on 17/10/14.
 */
@RestController
@RequestMapping("/v1/rds/instances/{instanceId}/snapshots")
public class SnapshotController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private BackupService backupService;
    
    /**
     * 获取备份列表
     * 根据instanceId获取备份列表，如果传入了startDatetime和stopDatetime则获取该时间段内的备份列表
     * 
     * @param instanceId 实例ID
     * @param startDatetime 开始时间，raft使用
     * @param stopDatetime 结束时间，raft使用
     * @param from 保留字段，目前未使用
     * @return 备份列表响应对象
     */
    @ApiOperation(value = "获取备份列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SnapshotListWithTimeResponse getList(@IdPermission @PathVariable String instanceId,
                                                @RequestParam(required = false) String startDatetime, // raft使用
                                                @RequestParam(required = false) String stopDatetime,  // raft使用
                                                @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get backup list.");
        SnapshotListWithTimeResponse response = null;
        try {
            if (StringUtils.isEmpty(startDatetime) && StringUtils.isEmpty(startDatetime)) {
                response = backupService.list(instanceId);
            }
            if (StringUtils.isNotEmpty(startDatetime) && StringUtils.isNotEmpty(startDatetime)) {
                response = backupService.list(instanceId, startDatetime, stopDatetime);
            }

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    @RequestMapping(method = RequestMethod.GET, params = {"manner=marker"})
    @ApiOperation(value = "获取备份列表: 通过marker方式分页")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogicMarkerResultResponse listInstanceByMarker(
            @IdPermission @IdMapper @PathVariable String instanceId,
            @RequestParam(required = false, defaultValue = "-1") String marker,
            @RequestParam(required = false, defaultValue = "1000") Integer maxKeys) {
        LOGGER.debug("list instance[marker], marker:{}, maxKeys:{}.", marker, maxKeys);
        LogicMarkerResultResponse<Snapshot> pageResultResponse = null;
        try {
            pageResultResponse = backupService.listWithMarker(instanceId, marker, maxKeys);

            // API 会多返回一个转换为MB的备份大小字段
            for (Snapshot eachSnapshots : pageResultResponse.getResult()) {
                // 转换大小从Byte为MB
                String snapshotSizeInBytesStr = eachSnapshots.getSnapshotSizeInBytes();

                eachSnapshots.setSnapshotSizeInMBytes(
                        String.valueOf(BasisUtils.transforByte2MByte(snapshotSizeInBytesStr)));
            }

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
//        LOGGER.debug("list instance[marker], response data is {}", pageResultResponse);
        return pageResultResponse;
    }
    /**
      * 获取备份详情
      * 根据instanceId和snapshotId获取备份详情，支持API返回的备份大小字段转换为MB
      * 
      * @param instanceId 实例ID
      * @param snapshotId 备份ID
      * @param downloadValidTimeInSec 下载有效期时间，单位秒，默认43200秒
      * @param from 来源标识，API来源时转换备份大小为MB
      * @return 备份详情响应对象
      */

    @ApiOperation(value = "获取备份详情")
    @RequestMapping(value = "/{snapshotId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SnapshotGetResponse getDetail(@IdPermission @IdMapper @PathVariable String instanceId,
                                         @PathVariable String snapshotId,
                                         @RequestParam(required = false, defaultValue = "43200")
                                                     Integer downloadValidTimeInSec,
                                         @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get backup detail.");
        SnapshotGetResponse response = null;
        try {

            response = backupService.detail(instanceId, snapshotId, Integer.valueOf(downloadValidTimeInSec));

            // API 会多返回一个转换为MB的备份大小字段
            if (response != null && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
                // 转换大小从Byte为MB
                String snapshotSizeInBytesStr = response.getSnapshot().getSnapshotSizeInBytes();

                response.getSnapshot().setSnapshotSizeInMBytes(
                        String.valueOf(BasisUtils.transforByte2MByte(snapshotSizeInBytesStr)));
            }

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    // 不清楚厂内是否有用户使用，暂时注释
//    @ApiOperation(value = "创建备份")
//    @RequestMapping(value = "", method = RequestMethod.POST)
//    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
//            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    public void snapshotCreate(@IdPermission @PathVariable String instanceId,
//                               @RequestParam(required = false, defaultValue = "") String from) {
//        LOGGER.debug("get snapshot create.");
//        try {
//            backupService.create(instanceId);
//        } catch (Exception e) {
//            LogicRdsExceptionHandler.handle(e);
//        }
//    }
    /**
      * 删除帐号
      * 根据instanceId和snapshotId删除对应的快照
      * 
      * @param instanceId 实例ID
      * @param snapshotId 快照ID
      */

    @ApiOperation(value = "删除帐号")
    @RequestMapping(value = "/{snapshotId}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void snapshotDelete(@IdPermission @PathVariable String instanceId,
                       @PathVariable String snapshotId) {
        LOGGER.debug("instanceId: {}, snapshotId: {}", instanceId, snapshotId);
        try {
            backupService.snapshotDelete(instanceId, snapshotId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

}
