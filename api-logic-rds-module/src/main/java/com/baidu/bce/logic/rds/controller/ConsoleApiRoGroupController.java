package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.rogroup.CreateRoGroupRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.CreateRoGroupResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.DashboardRoGroupLeaveRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.DashboardRoGroupUpdateRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.DashboardRoGroupUpdateWeightRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupDetailRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupDetailResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupLeaveRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupListResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateEnableDelayOffRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateIsBalanceReloadRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdatePubliclyAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateWeightRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.UpdateRoGroupPropertyRequest;
import com.baidu.bce.logic.rds.service.RoGroupService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

@Controller
@RequestMapping(value = "/api/rds/rogroup", produces = {"application/json"})
@Api(value = "RDS Dashboard RoGroup管理API")
public class ConsoleApiRoGroupController {

    @Autowired
    private RoGroupService roGroupService;
    /**
      * 创建只读组接口
      * 用于创建只读组，包含权限验证和业务逻辑处理
      * 
      * @param request 创建只读组的请求参数
      * @return 创建只读组的结果响应
      */

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<CreateRoGroupResponse> roGroupCreate(
            @IdPermission @Valid @RequestBody CreateRoGroupRequest request) {
        EdpResultResponse<CreateRoGroupResponse> response = new EdpResultResponse<>();
        response.setResult(roGroupService.roGroupCreate(request));
        return response;
    }
    /**
      * 查询RO组详情
      * 通过POST请求调用接口，根据提供的sourceAppId和roGroupId查询RO组详情
      * 
      * @param request 包含sourceAppId和roGroupId的请求体
      * @return EdpResultResponse<RoGroupDetailResponse> 返回查询结果
      */

    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<RoGroupDetailResponse> roGroupDetail(
            @IdPermission @Valid @RequestBody RoGroupDetailRequest request) {
        EdpResultResponse<RoGroupDetailResponse> response = new EdpResultResponse<>();
        response.setResult(roGroupService.roGroupDetail(request.getSourceAppId(), request.getRoGroupId()));
        return response;
    }
    /**
      * 获取RO组列表
      * 根据传入的instanceId获取对应的RO组列表信息
      * 
      * @param request instanceId请求体
      * @return RO组列表响应
      */

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<RoGroupListResponse> roGroupList(
            @IdPermission @Valid @RequestBody InstanceIdRequest request) {
        // TODO: instanceId 鉴权问题
        EdpResultResponse<RoGroupListResponse> response = new EdpResultResponse<>();
        response.setResult(roGroupService.roGroupList(request.getInstanceId()));
        return response;
    }
    /**
      * 删除只读组
      * 根据传入的请求参数，删除指定的只读组，并返回操作结果
      * 
      * @param request 请求参数，包含源应用ID和只读组ID
      * @return EdpResultResponse<Boolean> 删除操作的结果
      */

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> deleteRoGroup(@IdPermission @Valid @RequestBody InstanceIdRequest request) {
        // TODO: instanceId 鉴权问题
        roGroupService.deleteRoGroup(request.getSourceAppId(), request.getRoGroupId());
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 更新RO组名称
      * 根据传入的RO组ID和新的RO组名称，更新对应RO组的名称信息
      * 
      * @param request 包含RO组ID、新的RO组名称以及来源应用ID的请求体
      * @return 操作结果，包含是否成功更新的标识
      */

    @RequestMapping(value = "/updateName", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdateName(
            @IdPermission @Valid @RequestBody DashboardRoGroupUpdateRequest request) {
        RoGroupUpdateRequest requestbody = new RoGroupUpdateRequest();
        requestbody.setRoGroupName(request.getRoGroupName());
        roGroupService.roGroupUpdateName(request.getSourceAppId(), request.getRoGroupId(), requestbody);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
     * 更新RO组接口
     * 根据传入的RO组更新请求，更新RO组的endpoint信息
     * 
     * @param request 包含RO组更新请求的实体
     * @return EdpResultResponse<Boolean> 返回更新结果
     */

    @RequestMapping(value = "/updateEndpoint", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdateEndpoint(
            @IdPermission @Valid @RequestBody DashboardRoGroupUpdateRequest request) {
        RoGroupUpdateRequest requestbody = new RoGroupUpdateRequest();
        requestbody.setEndpoint(request.getEndpoint());
        roGroupService.roGroupUpdateEndpoint(request.getSourceAppId(), request.getRoGroupId(), requestbody);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 更新RO组公开访问权限
      * 通过POST请求，更新指定RO组的公开访问权限
      * 
      * @param request 包含RO组ID、应用ID和是否公开访问的请求体
      * @return 操作结果，成功返回true
      */

    @RequestMapping(value = "/updatePubliclyAccessible", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdatePubliclyAccessible(
            @IdPermission @Valid @RequestBody DashboardRoGroupUpdateRequest request) {
        RoGroupUpdatePubliclyAccessibleRequest requestbody = new RoGroupUpdatePubliclyAccessibleRequest();
        requestbody.setPubliclyAccessible(request.isPubliclyAccessible()); // TODO: 注意 isPubliclyAccessible
        roGroupService.roGroupUpdatePubliclyAccessible(request.getSourceAppId(), request.getRoGroupId(), requestbody);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 更新只读组属性
      * 根据请求中的源应用ID、只读组ID和属性信息，更新只读组属性
      * 
      * @param request 包含源应用ID、只读组ID和属性信息的请求体
      * @return 更新操作的结果
      */

    @RequestMapping(value = "/updateRoGroupProperty", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdateRoGroupProperty(
            @IdPermission @Valid @RequestBody UpdateRoGroupPropertyRequest request) {
        roGroupService.roGroupUpdateRoGroupProperty(request.getSourceAppId(), request.getRoGroupId(), request);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 更新只读组延迟下线功能
      * 根据请求参数更新指定只读组的延迟下线状态
      * 
      * @param request 包含只读组ID、源应用ID以及是否启用延迟下线的请求体
      * @return 操作结果响应，包含操作是否成功
      */

    @RequestMapping(value = "/updateEnableDelayOff", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdateEnableDelayOff(
            @IdPermission @Valid @RequestBody DashboardRoGroupUpdateRequest request) {
        RoGroupUpdateEnableDelayOffRequest requestbody = new RoGroupUpdateEnableDelayOffRequest();
        requestbody.setEnableDelayOff(request.isEnableDelayOff()); // TODO: 注意 isPubliclyAccessible
        roGroupService.roGroupUpdateEnableDelayOff(request.getSourceAppId(), request.getRoGroupId(), requestbody);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 更新是否平衡重载接口
      * 该接口用于更新数据源的平衡重载状态
      * 
      * @param request 更新请求体，包含是否平衡重载、来源应用ID、只读组ID等信息
      * @return EdpResultResponse<Boolean> 操作结果
      */

    @RequestMapping(value = "/updateIsBalanceReload", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdateIsBalanceReload(
            @IdPermission @Valid @RequestBody DashboardRoGroupUpdateRequest request) {
        RoGroupUpdateIsBalanceReloadRequest requestbody = new RoGroupUpdateIsBalanceReloadRequest();
        requestbody.setIsBalanceReload(request.isBalanceReload()); // TODO: 注意 isPubliclyAccessible
        roGroupService.roGroupUpdateIsBalanceReload(request.getSourceAppId(), request.getRoGroupId(), requestbody);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 更新最小应用数量接口
      * 根据请求体中的最小应用数量更新只读组的最小应用数量
      * 
      * @param request 包含最小应用数量、源应用ID、只读组ID的请求体
      * @return 操作结果
      */

    @RequestMapping(value = "/updateLeastAppAmount", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdateLeastAppAmount(
            @IdPermission @Valid @RequestBody DashboardRoGroupUpdateRequest request) {
        RoGroupUpdateRequest requestbody = new RoGroupUpdateRequest();
        requestbody.setLeastAppAmount(request.getLeastAppAmount());
        roGroupService.roGroupUpdateLeastAppAmount(request.getSourceAppId(), request.getRoGroupId(), requestbody);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 更新延迟阈值接口
      * 根据请求更新指定只读组的延迟阈值
      *
      * @param request 包含延迟阈值、源应用ID和只读组ID的请求体
      * @return 返回操作结果
      */

    @RequestMapping(value = "/updateDelayThreshold", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdateDelayThreshold(
            @IdPermission @Valid @RequestBody DashboardRoGroupUpdateRequest request) {
        RoGroupUpdateRequest requestbody = new RoGroupUpdateRequest();
        requestbody.setDelayThreshold(request.getDelayThreshold());
        roGroupService.roGroupUpdateDelayThreshold(request.getSourceAppId(), request.getRoGroupId(), requestbody);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 更新重载RO组
      * 通过POST请求对RO组进行更新重载操作
      * 
      * @param request 包含源应用ID和RO组ID的请求体
      * @return EdpResultResponse<Boolean> 更新重载操作的结果
      */

    @RequestMapping(value = "/updateReload", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdateReload(
            @IdPermission @Valid @RequestBody DashboardRoGroupUpdateRequest request) {
        roGroupService.roGroupUpdateReload(request.getSourceAppId(), request.getRoGroupId());
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 更新权重
      * 根据请求参数，更新读复制组权重
      * 
      * @param request 更新权重请求参数，包含源应用ID、读复制组ID和读复制组列表
      * @return EdpResultResponse<Boolean> 返回操作结果，成功返回true
      */

    @RequestMapping(value = "/updateWeight", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdateWeight(
            @IdPermission @Valid @RequestBody DashboardRoGroupUpdateWeightRequest request) {
        RoGroupUpdateWeightRequest requestbody = new RoGroupUpdateWeightRequest();
        requestbody.setReadReplicaList(request.getReadReplicaList());
        roGroupService.roGroupUpdateWeight(request.getSourceAppId(), request.getRoGroupId(), requestbody);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 更新只读组权重接口
      * 根据请求参数更新指定只读组的权重信息
      * 
      * @param request 请求参数，包含待更新的只读组信息
      * @return EdpResultResponse<Boolean> 操作结果，包含操作是否成功
      */

    @RequestMapping(value = "/join", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupUpdateJoin(
            @IdPermission @Valid @RequestBody DashboardRoGroupUpdateWeightRequest request) {
        RoGroupUpdateWeightRequest requestbody = new RoGroupUpdateWeightRequest();
        requestbody.setReadReplicaList(request.getReadReplicaList());
        roGroupService.roGroupUpdateJoin(request.getSourceAppId(), request.getRoGroupId(), requestbody);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
    /**
      * 离开只读组接口
      * 该接口用于处理应用离开只读组的请求
      * 
      * @param request 请求体，包含离开只读组所需的信息
      * @return EdpResultResponse<Boolean> 返回处理结果，包含是否成功离开只读组
      */

    @RequestMapping(value = "/leave", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> roGroupLeave(
            @IdPermission @Valid @RequestBody DashboardRoGroupLeaveRequest request) {
        RoGroupLeaveRequest requestbody = new RoGroupLeaveRequest();
        requestbody.setReadReplicaList(request.getReadReplicaList());
        roGroupService.roGroupLeave(request.getSourceAppId(), request.getRoGroupId(), requestbody);
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(true);
        return response;
    }
}
