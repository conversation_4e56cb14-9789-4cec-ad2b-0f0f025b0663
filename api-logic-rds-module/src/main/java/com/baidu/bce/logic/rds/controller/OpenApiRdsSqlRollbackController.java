package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.rollback.CheckTimeResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.PreCheckTimeRequest;
import com.baidu.bce.internalsdk.rds.model.rollback.RollbackListResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlRollbackResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlRollbacksRequest;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlTaskDetailResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.TaskParamResponse;
import com.baidu.bce.logic.rds.service.RdsSqlRollbackService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@Api(value = "RDS sql回滚功能")
@RestController
@RequestMapping("/v1/instance/{instanceId}/flashback")
public class OpenApiRdsSqlRollbackController {

    @Autowired
    private RdsSqlRollbackService rollbackService;
    
    /**
      * 创建Sql闪回任务
      * 根据传入的instanceId和SqlRollbacksRequest对象创建Sql闪回任务
      * 
      * @param instanceId 实例ID，用于标识需要操作的数据库实例
      * @param request SqlRollbacksRequest对象，包含创建Sql闪回任务所需的具体信息
      * @return SqlRollbackResponse对象，包含创建Sql闪回任务的结果
      */
    @ApiOperation("创建Sql闪回任务")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlRollbackResponse rollbackTask(@IdPermission @IdMapper @PathVariable String instanceId,
                                                              @Valid @RequestBody SqlRollbacksRequest request) {

        return rollbackService.rollbackTask(instanceId, request);
    }
    /**
     * 闪回任务的前置时间检查
     * 用于检查闪回任务的前置时间是否符合要求
     * 
     * @param instanceId 实例ID
     * @param request 前置时间检查请求体
     * @return CheckTimeResponse 返回检查时间的结果
     */

    @ApiOperation("闪回任务的前置时间检查")
    @RequestMapping(value = "/checkTime", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public CheckTimeResponse checkTime(@IdPermission @IdMapper @PathVariable String instanceId,
                                                          @RequestBody PreCheckTimeRequest request) {


        return rollbackService.checkTime(instanceId, request);


    }
    
    /**
      * 查询 sql闪回任务列表
      * 查询指定RDS实例的sql闪回任务列表，支持分页查询。
      *
      * @param instanceId RDS实例ID
      * @param pageNo 页码
      * @param pageSize 每页数量
      * @return RollbackListResponse 查询结果
      */    
    @ApiOperation("查询 sql闪回任务列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public RollbackListResponse taskList(@IdPermission @IdMapper @PathVariable String instanceId,
                                                            @RequestParam(value = "pageNo") Integer pageNo,
                                                            @RequestParam(value = "pageSize") Integer pageSize) {
        String from = "api";
        return rollbackService.taskList(instanceId, pageNo, pageSize, from);

    }
    /**
      * 查询任务详情
      * 根据任务ID和实例ID获取任务详情
      * 
      * @param instanceId 实例ID
      * @param taskID 任务ID
      * @return SqlTaskDetailResponse 任务详情对象
      */

    @ApiOperation("查询任务详情")
    @RequestMapping(value = "/process", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlTaskDetailResponse detail(@IdPermission @IdMapper @PathVariable String instanceId,
                                                           @RequestParam(value = "taskID") String taskID) {

        return rollbackService.detail(instanceId, taskID);

    }
    /**
     * 查询任务参数
     * 根据实例ID和任务ID获取任务参数信息
     * 
     * @param instanceId 实例ID
     * @param taskID 任务ID
     * @return 任务参数响应对象
     */

    @ApiOperation("查询任务参数")
    @RequestMapping(value = "/parameter", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public TaskParamResponse getTaskParameters(@IdPermission @IdMapper @PathVariable String instanceId,
                                                                  @RequestParam(value = "taskID") String taskID) {


        return rollbackService.getTaskParameters(instanceId, taskID);

    }
}
