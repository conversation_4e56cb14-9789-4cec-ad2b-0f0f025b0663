package com.baidu.bce.logic.rds.controller;
import com.baidu.bce.internalsdk.rds.model.instance.RecoveryInstanceRequest;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.rds.service.RecyclerService;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.instance.OpenApiInstanceMarkerListResponse;
import com.baidubce.util.JsonUtils;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@RestController
@RequestMapping("/v1/instance/recycler")
public class OpenApiRecyclerController {

    @Autowired
    private RecyclerService recyclerService;

    @Autowired
    IdMapperService idMapperService;

    private static final Logger LOGGER = LoggerFactory.getLogger(RecyclerService.class);


    /**
     * 获取回收站实例列表
     * 该函数用于获取回收站中的实例列表，并通过一系列处理返回OpenApiInstanceMarkerListResponse对象
     * 
     * @param marker 分页标记，用于查询指定页的数据
     * @param maxKeys 每页显示的最大条目数
     * @return OpenApiInstanceMarkerListResponse对象，包含回收站实例列表及相关信息
     * @throws Exception 如果在获取或处理数据时发生异常，则抛出
     */
    @ApiOperation("获取回收站实例列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public OpenApiInstanceMarkerListResponse getRecyclerInstances(
            @RequestParam(required = false) String marker,
            @RequestParam(required = false) Integer maxKeys){

        LogicMarkerResultResponse<InstanceAbstract> pageResultResponse = null;
        OpenApiInstanceMarkerListResponse openApiInstanceMarkerListResponse = null;
        try {
            pageResultResponse = recyclerService.getRecyclerInstancesFromAPI(marker, maxKeys);
            LOGGER.info("LogicMarkerResultResponse<InstanceAbstract> data is {}",
                    JsonUtils.toJsonString(pageResultResponse));

            openApiInstanceMarkerListResponse = new OpenApiInstanceMarkerListResponse();
            openApiInstanceMarkerListResponse.setMarker(pageResultResponse.getMarker());
            openApiInstanceMarkerListResponse.setIsTruncated(pageResultResponse.getIsTruncated());
            openApiInstanceMarkerListResponse.setNextMarker(pageResultResponse.getNextMarker());
            openApiInstanceMarkerListResponse.setMaxKeys(pageResultResponse.getMaxKeys());
            openApiInstanceMarkerListResponse.setInstances(new ArrayList<>(pageResultResponse.getResult()));
            for (InstanceAbstract instanceAbstract : openApiInstanceMarkerListResponse.getInstances()) {
                if (instanceAbstract.getInstanceShortId() != null) {
                    instanceAbstract.setInstanceId(instanceAbstract.getInstanceShortId());
                }
                instanceAbstract.setMemoryCapacity(instanceAbstract.getAllocatedMemoryInGB());
                instanceAbstract.setVolumeCapacity(instanceAbstract.getAllocatedStorageInGB());
                instanceAbstract.setUsedStorage(instanceAbstract.getUsedStorageInGB());
                instanceAbstract.setPublicAccessStatus(instanceAbstract.getEipStatus());
                instanceAbstract.setPaymentTiming(instanceAbstract.getProductType());

            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return openApiInstanceMarkerListResponse;
    }

    /**
      * 从回收站恢复实例
      * 从回收站恢复指定的实例。通过传入实例ID列表，将对应的实例从回收站中恢复。
      * 
      * @param request 包含需要恢复的实例ID列表的请求体
      * @throws RDSExceptions.ParamValidationException 如果实例ID列表为空或null，抛出参数验证异常
      */
    @ApiOperation("从回收站恢复实例")
    @RequestMapping(value = "/recover", method = RequestMethod.PUT)
    public void recoverInstance(@RequestBody RecoveryInstanceRequest request) {
        List<String> instanceIds = request.getInstanceIds();
        List<String> longInstanceIds = new ArrayList<>();
        for (String instanceId : instanceIds) {
            String longId = idMapperService.getInstanceUuid(instanceId);
            longInstanceIds.add(longId);
        }
        if (instanceIds == null || instanceIds.size() == 0) {
            throw new RDSExceptions.ParamValidationException();
        }
        recyclerService.recoverInstance(longInstanceIds);
    }

    /**
     * 从回收站中释放单个实例
     * 该接口用于删除回收站中的单个实例
     * 
     * @param instanceId 实例ID
     * @throws RDSExceptions.ParamValidationException 当instanceId为空或长度为0时抛出
     */
    @ApiOperation("从回收站中释放单个实例")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.DELETE)
    public void deleteIsolatedInstance(@PathVariable @IdMapper String instanceId) {

        if (instanceId == null || instanceId.length() == 0) {
           throw new RDSExceptions.ParamValidationException();
        }
        recyclerService.deleteIsolatedInstance(instanceId);
    }

    /**
     * 从回收站中批量释放实例
     * 批量释放回收站中的实例，通过传入实例ID字符串（逗号分隔）进行操作
     * 
     * @param instanceIds 实例ID字符串，逗号分隔
     * @throws RDSExceptions.ParamValidationException 如果instanceIds为空或长度为0，抛出参数验证异常
     */
    @ApiOperation("从回收站中批量释放实例")
    @RequestMapping(value = "/batch", method = RequestMethod.DELETE)
    public void deleteBatchInstances(@RequestParam String instanceIds) {

        if (instanceIds == null || instanceIds.length() == 0) {
            throw new RDSExceptions.ParamValidationException();
        }

        String[] split = instanceIds.split(",");
        List<String> strings = Arrays.asList(split);
        List<String> longInstanceIds = new ArrayList<>();
        for (String instanceId : strings) {
            String longId = idMapperService.getInstanceUuid(instanceId);
            longInstanceIds.add(longId);
        }
        recyclerService.deleteBatchInstances(longInstanceIds);
    }

}