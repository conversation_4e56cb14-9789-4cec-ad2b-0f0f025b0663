package com.baidu.bce.logic.rds.controller.group;


import com.baidu.bce.internalsdk.rds.model.CheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.AccountGetResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePrivilegesRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupAccountCheckRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupAccountCreateRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupAccountDeleteRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupAccountDetailRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupAccountListRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupAccountUpdateDescRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupAccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupAccountUpdatePermissionRequest;
import com.baidu.bce.logic.rds.service.group.InstanceGroupService;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 实例组账号
 * Created by shiyuxin on 19/5/21.
 */
@RestController
@RequestMapping("/api/rds/group/account")
public class ConsoleApiGroupAccountController {

    @Autowired
    private InstanceGroupService instanceGroupService;

    /**
      * 列表查询接口
      * 根据请求中的groupId，查询对应的账户列表
      * 
      * @param request 包含groupId的请求体
      * @return 账户列表查询结果
      */
    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    public EdpPageResultResponse<Account> list(@Valid @RequestBody DashboardGroupAccountListRequest request) {
        EdpPageResultResponse<Account> response = new EdpPageResultResponse<>();
        response.getPage().setResult(instanceGroupService.accountList(request.getGroupId()).getAccounts());
        return response;
    }
    /**
     * 创建仪表盘群组账户
     * 根据提供的实例ID和账户信息创建新的实例组账户
     * 
     * @param request 包含仪表盘群组ID和账户信息的请求体
     * @return EdpResultResponse<Boolean> 创建操作的结果
     */

    @RequestMapping(value = "create", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> create(@Valid @RequestBody DashboardGroupAccountCreateRequest request) {
        instanceGroupService.accountCreate(request.getGroupId(), request.getAccount(), null);
        return new EdpResultResponse<>();
    }
    /**
     * 获取账户详情
     * 根据传入的groupId和accountName获取账户详情
     * 
     * @param request 包含groupId和accountName的请求体
     * @return 账户详情响应体
     */

    @RequestMapping(value = "detail", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Account> detail(@Valid @RequestBody DashboardGroupAccountDetailRequest request) {
        AccountGetResponse accountGetResponse = instanceGroupService.accountDetail(request.getGroupId(),
                request.getAccountName());
        EdpResultResponse<Account> accountDetailResponse = new EdpResultResponse<>();
        accountDetailResponse.withResult(accountGetResponse.getAccount());
        return accountDetailResponse;
    }
    /**
     * 更新描述接口
     * 该接口用于更新指定账户的描述信息
     * 
     * @param request 包含更新描述请求的参数，如groupId、accountName和remark
     * @return EdpResultResponse对象，包含操作结果
     */

    @RequestMapping(value = "update_desc", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> updateDesc(@Valid @RequestBody DashboardGroupAccountUpdateDescRequest request) {
        AccountUpdateRemarkRequest accountUpdateRemarkRequest = new AccountUpdateRemarkRequest();
        accountUpdateRemarkRequest.setRemark(request.getRemark());
        instanceGroupService.accountUpdateRemark(request.getGroupId(), request.getAccountName(),
                accountUpdateRemarkRequest);
        return new EdpResultResponse<>();
    }
    /**
      * 更新密码
      * 该函数用于更新用户密码，通过传入包含新密码和用户信息的请求体，完成密码更新操作。
      * 
      * @param request 包含新密码和用户信息的请求体
      * @return 返回操作结果
      */

    @RequestMapping(value = "update_password", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> updatePassword(
            @Valid @RequestBody DashboardGroupAccountUpdatePasswordRequest request) {
        AccountUpdatePasswordRequest accountUpdatePasswordRequest = new AccountUpdatePasswordRequest();
        accountUpdatePasswordRequest.setEncryptedPassword(request.getEncryptedPassword());
        instanceGroupService.accountUpdatePW(request.getGroupId(), request.getAccountName(),
                accountUpdatePasswordRequest);
        return new EdpResultResponse<>();
    }
    /**
     * 更新权限
     * 更新指定账户在特定实例组中的权限
     * 
     * @param request 包含数据库权限、组ID、账户名的请求体
     * @return 返回执行结果
     */

    @RequestMapping(value = "update_permission", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> updatePermission(
            @Valid @RequestBody DashboardGroupAccountUpdatePermissionRequest request) {
        AccountUpdatePrivilegesRequest accountUpdatePrivilegesRequest = new AccountUpdatePrivilegesRequest();
        accountUpdatePrivilegesRequest.setDatabasePrivileges(request.getDatabasePrivileges());
        instanceGroupService.accountUpdatePrivileges(request.getGroupId(), request.getAccountName(),
                accountUpdatePrivilegesRequest);
        return new EdpResultResponse<>();
    }
    /**
     * 删除仪表板组账户
     * 根据提供的实例组ID和账户名称，删除对应的实例组账户
     * 
     * @param request 包含实例组ID和账户名称的请求体
     * @return 操作结果
     */

    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> delete(@Valid @RequestBody DashboardGroupAccountDeleteRequest request) {
        instanceGroupService.accountDelete(request.getGroupId(), request.getAccountName());
        return new EdpResultResponse<>();
    }
    /**
      * 检查接口
      * 检查组账号名称是否存在
      *
      * @param request 请求体，包含groupId和accountName
      * @return 返回检查结果，存在返回true，不存在返回false
      */

    @RequestMapping(value = "check", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> check(@Valid @RequestBody DashboardGroupAccountCheckRequest request) {
        CheckExistResponse checkExistResponse = instanceGroupService.accountCheckName(request.getGroupId(),
                request.getAccountName());
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.withResult(checkExistResponse.getIsExist() != 0);
        return response;
    }

}
