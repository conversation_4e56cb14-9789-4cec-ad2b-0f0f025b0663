package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.rds.model.PreCheckVersionRequest;
import com.baidu.bce.internalsdk.rds.model.RdsMinorVersionList;
import com.baidu.bce.internalsdk.rds.model.UpdateVersionRequest;
import com.baidu.bce.logic.rds.model.ResultResponse;
import com.baidu.bce.logic.rds.service.InstanceVersionService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/rds")
public class OpenApiInstanceVersionController {

    @Autowired
    private InstanceVersionService versionService;
    /**
     * 查看实例允许升级的小版本列表
     * 根据实例ID获取允许升级的小版本列表
     * 
     * @param instanceId 实例ID
     * @return 允许升级的小版本列表
     * @throws BceInternalResponseException 如果调用版本服务失败会抛出此异常
     */

    @ApiOperation("查看实例允许升级的小版本列表")
    @RequestMapping(value = "/instance/{instanceId}/upgradeMinorVersionList", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public RdsMinorVersionList getVersionList(@IdPermission @PathVariable @IdMapper String instanceId) {

        RdsMinorVersionList resultResponse = null;

        try {
            resultResponse = versionService.getVersionList(instanceId);
        } catch (BceInternalResponseException e) {
            BasisUtils.logicRdsExceptions(e);
        }
        return resultResponse;
    }
    /**
     * 实例升级小版本
     * 用于处理实例的小版本升级请求
     * 
     * @param instanceId 实例ID
     * @param request 版本升级请求体
     */

    @ApiOperation("实例升级小版本")
    @RequestMapping(value = "/instance/{instanceId}", method = RequestMethod.PUT, params = "upgradeMinorVersion")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateVersion(
            @IdPermission @PathVariable @IdMapper String instanceId, @RequestBody UpdateVersionRequest request) {

        String from = "api";
        try {
            versionService.updateVersion(instanceId, request, from);
        } catch (BceInternalResponseException e) {
            BasisUtils.logicRdsExceptions(e);
        }
    }
    /**
     * 加入热活组前置检查小版本
     * 进行加入热活组之前的小版本检查
     * 
     * @param request 前置检查请求参数
     * @return 返回检查结果
     * @throws BceInternalResponseException 内部响应异常
     */

    @ApiOperation("加入热活组前置检查小版本")
    @RequestMapping(value = "/group/checkVersion", method = RequestMethod.POST)
    public ResultResponse<Boolean> preCheckOfGroupVersion(@RequestBody PreCheckVersionRequest request) {

        ResultResponse<Boolean> resultResponse = new ResultResponse<>();

        try {
            versionService.preCheckOfGroupVersion(request);
            resultResponse.setResult(true);
        } catch (BceInternalResponseException e) {
            BasisUtils.logicRdsExceptions(e);
        }

        return resultResponse;




    }
}
