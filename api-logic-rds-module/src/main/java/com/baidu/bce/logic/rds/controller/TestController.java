package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.model.ResultResponse;
import com.baidu.bce.logic.rds.service.AccountService;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * Created by luping03 on 17/12/13.
 */
@RestController
@RequestMapping("/v1/rds/test")
public class TestController {

    @Autowired
    private InstanceDao instanceDao;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private AccountService accountService;
    /**
      * 创建实例预付费接口
      * 通过GET请求，创建实例并设置为预付费模式
      * 
      * @param 无
      * @return 无
      * @throws Exception 异常信息
      */

    @RequestMapping(value = "/createInstancePrepay", method = RequestMethod.GET)
    public void createInstance() {
        try {
            BaseCreateOrderRequestVo<InstanceCreateModel> requestVo= new BaseCreateOrderRequestVo<>();
            InstanceCreateModel instanceCreateModel = new InstanceCreateModel();
            InstanceCreateModel.DashCreateInstance createInstance = new InstanceCreateModel.DashCreateInstance();
            createInstance.setEngine("mysql");
            createInstance.setEngineVersion("5.6");
            createInstance.setAllocatedMemoryInGB(256);
            createInstance.setAllocatedStorageInGB(5);
            createInstance.setAzone("zoneA");
            createInstance.setSubnetId("zoneA:e4c38117-049e-402c-98bc-10b23354d09e");
            createInstance.setVpcId("5a879c71-c0cb-4f90-94a5-20e73b623266");
            instanceCreateModel.setInstance(createInstance);
            instanceCreateModel.setProductType("prepay");
//            instanceCreateModel.setProductType("Postpaid");
            instanceCreateModel.setDuration(1);

            BaseCreateOrderRequestVo.Item<InstanceCreateModel> item
                    = new BaseCreateOrderRequestVo.Item<>();
            item.setConfig(instanceCreateModel);
            requestVo.setItems(Collections.singletonList(item));

            instanceService.createInstances(requestVo, "console");

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 创建实例接口
      * 通过GET请求创建数据库实例
      * 
      * @param 无参数
      * @return 无返回值
      * @throws 无异常抛出，内部异常被捕获处理
      */

    @RequestMapping(value = "/createInstancePostpay", method = RequestMethod.GET)
    public void createInstance1() {
        try {
            BaseCreateOrderRequestVo<InstanceCreateModel> requestVo= new BaseCreateOrderRequestVo<>();
            InstanceCreateModel instanceCreateModel = new InstanceCreateModel();
            InstanceCreateModel.DashCreateInstance createInstance = new InstanceCreateModel.DashCreateInstance();
            createInstance.setEngine("mysql");
            createInstance.setEngineVersion("5.6");
            createInstance.setAllocatedMemoryInGB(256);
            createInstance.setAllocatedStorageInGB(5);
            instanceCreateModel.setInstance(createInstance);
//            instanceCreateModel.setProductType("Prepaid");
            instanceCreateModel.setProductType("Postpaid");
            instanceCreateModel.setDuration(1);

            BaseCreateOrderRequestVo.Item<InstanceCreateModel> item
                    = new BaseCreateOrderRequestVo.Item<>();
            item.setConfig(instanceCreateModel);
            requestVo.setItems(Collections.singletonList(item));

            instanceService.createInstances(requestVo, "api");

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 获取白名单接口
      * 通过GET请求获取白名单信息，并进行批量插入操作
      * 
      * @param 无
      * @return 无
      * @throws 无
      */

    @RequestMapping(value = "/batchInsert", method = RequestMethod.GET)
    public void getWhiteList() {
        try {
            List<InstancePO> instancePOList = new ArrayList<>();
//            InstancePO instancePO = new InstancePO();
//            instancePO.setInstanceId("rds-1");
//            instancePO.setInstanceUuid("rds-1");
//            instancePO.setEngine("mysql");
//            instancePO.getBackupPolicy().withBackupDays("1,2,3").withBackupTime("05:00:00Z");
//            instancePOList.add(instancePO);
//
//            InstancePO instancePO1 = new InstancePO();
//            instancePO1.setInstanceId("rds-2");
//            instancePO1.setInstanceUuid("rdsmx12448e81w8");
//            instancePO1.setEngine("mysql");
//            instancePO1.getBackupPolicy().withBackupDays("1,2,3").withBackupTime("05:00:00Z");
//            instancePOList.add(instancePO1);
            InstancePO instancePO = new InstancePO();
            instancePO.setInstanceId("rds-3");
            instancePO.setInstanceUuid("rds-3");
//            instancePO.setInstanceExpireTime(new Date());
            instancePO.setEngine("mysql");
            instancePO.getBackupPolicy().withBackupDays("1,2,3").withBackupTime("05:00:00Z");
            instancePOList.add(instancePO);

//            InstancePO instancePO1 = new InstancePO();
//            instancePO1.setInstanceId("rds-4");
//            instancePO1.setInstanceUuid("rds-4");
//            instancePO1.setInstanceExpireTime(new Timestamp(new Date().getTime()));
//            instancePO1.setEngine("mysql");
//            instancePO1.getBackupPolicy().withBackupDays("1,2,3").withBackupTime("05:00:00Z");
//            instancePOList.add(instancePO1);
            instanceDao.batchInsertInstances(instancePOList);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 加密接口
      * 该接口用于加密传入的密码
      * 
      * @param pw 需要加密的密码
      * @param httpServletRequest HttpServletRequest对象，用于获取请求头中的AK
      * @return 加密后的密码
      */

    @ApiOperation(value = "加密")
    @RequestMapping(value = "/encryptPass", method = RequestMethod.GET)
    public ResultResponse<String> create(@RequestParam(required = false, defaultValue = "") String pw,
                                         HttpServletRequest httpServletRequest) {
        ResultResponse response = new ResultResponse();
        try {
            String ak = httpServletRequest.getHeader(RDSConstant.X_BCE_ACCESS_KEY);
            String resultPw = accountService.encrypt(pw, ak);
            response.setResult(resultPw);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

}
