package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.errorlog.ErrorlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.errorlog.ErrorlogListResponse;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogErrorDetails;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogSlowDetails;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogDetailRequest;
import com.baidu.bce.logic.rds.service.ErrorlogService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import javax.validation.Valid;


/**
 * Created by luping03 on 17/11/3.
 */
@RestController
@RequestMapping("/v1/rds/instances/{instanceId}/errorlogs")
public class ErrorlogController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ErrorlogController.class);

    @Autowired
    private ErrorlogService errorlogService;
    
    /**
      * 获取日志列表
      * 根据instanceId和datetime获取错误日志列表
      * 
      * @param instanceId 实例ID
      * @param datetime 日志日期时间
      * @param from 日志起始位置（可选）
      * @return 错误日志列表响应对象
      */
    @ApiOperation(value = "获取日志列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ErrorlogListResponse getList(@IdPermission @PathVariable String instanceId,
                                        @RequestParam String datetime,
                                        @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get errorlog list. instanceId: {}", instanceId);
        ErrorlogListResponse response = null;
        try {
            response = errorlogService.list(instanceId, datetime);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取日志详情
      * 根据实例ID和日志ID获取日志详情
      *
      * @param instanceId 实例ID
      * @param logId 日志ID
      * @param downloadValidTimeInSec 下载有效时间
      * @param from 来源
      * @return 日志详情响应
      */

    @ApiOperation(value = "获取日志详情")
    @RequestMapping(value = "/{logId:.+}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ErrorlogGetResponse detail(@IdPermission @PathVariable String instanceId,
                                      @PathVariable String logId,
                                      @RequestParam Integer downloadValidTimeInSec,
                                      @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get errorlog detail. instanceId: {}, logId: {}", instanceId, logId);
        ErrorlogGetResponse response = null;
        try {
            response = errorlogService.detail(instanceId, logId, downloadValidTimeInSec);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

//    @ApiOperation(value = "检查时间是否可以作为克隆实例的备份恢复时间点")
//    @RequestMapping(value = "/check", method = RequestMethod.POST)
//    public void check(@PathVariable String instanceId,
//                      @RequestBody @Valid BinlogDateTime dateTime,
//                      @RequestParam(required = false, defaultValue = "") String from) {
//        LOGGER.debug("get log detail. instanceId: {}", instanceId);
//        try {
//            errorlogService.check(instanceId, dateTime);
//        } catch (Exception e) {
//            LogicRdsExceptionHandler.handle(e);
//        }
//    }
    /**
      * 获取错误日志详情
      * 根据传入的日志请求信息，获取对应的错误日志详情
      * 
      * @param logDetailRequest 日志请求信息
      * @return 日志详情
      */

    @ApiOperation(value = "获取错误日志详情")
    @RequestMapping(value = "/getErrorLogDetails", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogErrorDetails getErrorLogDetails(@RequestBody @Valid LogDetailRequest logDetailRequest) {
        LOGGER.debug("log errorLogDetail.");
        LogErrorDetails response = new LogErrorDetails();
        try {
            response = errorlogService.getErrorLogDetails(logDetailRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    /**
      * 获取慢日志详情
      * 根据传入的慢日志详情请求，返回对应的慢日志详情信息
      * 
      * @param logDetailRequest 慢日志详情请求体
      * @return 慢日志详情信息
      */
    }
    @ApiOperation(value = "获取慢日志详情")
    @RequestMapping(value = "/getSlowLogDetails", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogSlowDetails getSlowLogDetails(@RequestBody @Valid LogDetailRequest logDetailRequest) {
        LOGGER.debug("log slowLogDetail.");
        LogSlowDetails response = new LogSlowDetails();
        try {
            response = errorlogService.getSlowLogDetails(logDetailRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
}
