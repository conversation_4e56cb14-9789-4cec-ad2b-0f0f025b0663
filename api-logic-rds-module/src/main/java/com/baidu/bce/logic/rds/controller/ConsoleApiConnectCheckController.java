package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.idempotent.annotation.Idempotent;
import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.instance.BccConnectList;
import com.baidu.bce.internalsdk.rds.model.instance.BccInnerConnectDetail;
import com.baidu.bce.internalsdk.rds.model.instance.BccInstanceListRequest;
import com.baidu.bce.internalsdk.rds.model.instance.PublicNetworkConnDetail;
import com.baidu.bce.internalsdk.rds.model.instance.PublicNetworkConnectMessage;
import com.baidu.bce.internalsdk.rds.model.instance.PublicNetworkRequest;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/api/rds", produces = {"application/json"})
@Api(value = "RDS 连接检查模块")
public class ConsoleApiConnectCheckController {

    @Autowired
    private InstanceService instanceService;
    /**
     * 添加BCC实例接口
     * 该接口用于添加BCC实例
     * 
     * @param request BCC实例添加请求体，包含需要添加的BCC实例信息
     * @return EdpResultResponse<Boolean> 返回添加结果，成功返回true，失败返回false
     */

    @ApiOperation(value = "添加BCC实例接口")
    @RequestMapping(value = "/bcc/inner/add_task", method = RequestMethod.POST)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public EdpResultResponse<Boolean> insertBccInstance(@IdPermission @RequestBody @Validated
                                                                         BccInstanceListRequest request) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.insertBccInstance(request);
        resultResponse.setResult(true);
        return resultResponse;
    }
    /**
      * 查询BCC内网连接列表
      * 根据instanceId查询BCC内网连接列表，支持分页查询
      * 
      * @param instanceId 实例ID
      * @param pageNo 页码，默认为1
      * @param pageSize 每页条数，默认为100
      * @return BCC内网连接列表的查询结果
      */

    @ApiOperation(value = "查询BCC内网连接列表")
    @RequestMapping(value = "/bcc/inner/list", method = RequestMethod.GET)
    public EdpResultResponse<BccConnectList> innerCheckList
            (@IdPermission @RequestParam String instanceId,
             @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
             @RequestParam(value = "pageSize", required = false, defaultValue = "100") Integer pageSize) {
        EdpResultResponse<BccConnectList> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(instanceService.innerCheckList(instanceId, pageNo, pageSize));
        return resultResponse;
    }
    /**
     * 查询BCC内网连接检测详情
     * 根据提供的connectId查询BCC内网连接检测详情
     * 
     * @param connectId BCC内网连接ID
     * @return 返回BCC内网连接检测详情的结果
     */

    @ApiOperation(value = "查询BCC内网连接检测详情")
    @RequestMapping(value = "/bcc/inner/{connectId}", method = RequestMethod.GET)
    public EdpResultResponse<BccInnerConnectDetail> innerCheckDetail(@Valid @PathVariable("connectId") String connectId) {
        EdpResultResponse<BccInnerConnectDetail> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(instanceService.innerCheckDetail(connectId));
        return resultResponse;
    }
    /**
     * 开启内网连接检查
     * 该接口用于开启内网连接检查，传入连接ID，返回检查结果
     * 
     * @param connectId 连接ID
     * @return EdpResultResponse<Boolean> 返回检查结果
     */

    @ApiOperation(value = "开启内网连接检查")
    @RequestMapping(value = "/bcc/inner/checkConnect/{connectId}", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> checkInnerConnect(@Valid @PathVariable("connectId") String connectId) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.checkInnerConnect(connectId);
        resultResponse.setResult(true);
        return resultResponse;
    }
    /**
     * 删除内网连接检查
     * 删除指定的内网连接
     * 
     * @param connectId 需要删除的内网连接的ID
     * @return EdpResultResponse<Boolean> 删除成功返回true，否则返回false
     */

    @ApiOperation(value = "删除内网连接检查")
    @RequestMapping(value = "/bcc/inner/task/{connectId}", method = RequestMethod.DELETE)
    public EdpResultResponse<Boolean> deleteInnerConnect(@Valid @PathVariable("connectId") String connectId) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.deleteInnerConnect(connectId);
        resultResponse.setResult(true);
        return resultResponse;
    }
    /**
     * 添加外网ip接口
     * 添加外网ip，通过POST请求到/inet/outer/add_task地址
     * 
     * @param request 外网ip请求体
     * @return EdpResultResponse<Boolean> 插入结果
     */

    @ApiOperation(value = "添加外网ip")
    @RequestMapping(value = "/inet/outer/add_task", method = RequestMethod.POST)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public EdpResultResponse<Boolean> insertOuterIp(@IdPermission @RequestBody @Validated
                                                                PublicNetworkRequest request) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.insertOuterIp(request);
        resultResponse.setResult(true);
        return resultResponse;
    }
    /**
     * 查询外网连接列表
     * 通过GET请求获取外网连接列表，可分页查询
     * 
     * @param instanceId 实例ID
     * @param pageNo 页码，默认为1
     * @param pageSize 每页数量，默认为100
     * @return 外网连接列表的响应结果
     */

    @ApiOperation(value = "查询外网连接列表")
    @RequestMapping(value = "/inet/outer/list", method = RequestMethod.GET)
    public EdpResultResponse<PublicNetworkConnectMessage> outerCheckList
            (@IdPermission @RequestParam String instanceId,
             @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
             @RequestParam(value = "pageSize", required = false, defaultValue = "100") Integer pageSize) {
        EdpResultResponse<PublicNetworkConnectMessage> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(instanceService.outerCheckList(instanceId, pageNo, pageSize));
        return resultResponse;
    }
    /**
     * 查询外网连接检测详情
     * 根据提供的connectId查询外网连接检测详情信息
     * 
     * @param connectId 外网连接检测标识
     * @return EdpResultResponse<PublicNetworkConnDetail> 查询结果
     */

    @ApiOperation(value = "查询外网连接检测详情")
    @RequestMapping(value = "/inet/outer/checkConnect/{connectId}", method = RequestMethod.GET)
    public EdpResultResponse<PublicNetworkConnDetail> outerCheckDetail
            (@Valid @PathVariable("connectId") String connectId) {
        EdpResultResponse<PublicNetworkConnDetail> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(instanceService.outerCheckDetail(connectId));
        return resultResponse;
    }
    /**
     * 开启外网连接检查
     * 开启外网连接检查接口，传入连接ID，检查对应的外网连接是否可用。
     *
     * @param connectId 连接ID，用于标识要检查的外网连接
     * @return EdpResultResponse<Boolean> 检查结果，成功时返回true，否则返回false
     */

    @ApiOperation(value = "开启外网连接检查")
    @RequestMapping(value = "/outer/checkConnect/{connectId}", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> checkOuterConnect(@Valid @PathVariable("connectId") String connectId) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.checkOuterConnect(connectId);
        resultResponse.setResult(true);
        return resultResponse;
    }
    /**
     * 删除外网连接检查
     * 删除外网连接，通过connectId来指定删除哪一个外网连接
     * 
     * @param connectId 外网连接的ID
     * @return EdpResultResponse<Boolean> 删除操作的结果
     */

    @ApiOperation(value = "删除外网连接检查")
    @RequestMapping(value = "/outer/task/{connectId}", method = RequestMethod.DELETE)
    public EdpResultResponse<Boolean> deleteOuterConnect(@Valid @PathVariable("connectId") String connectId) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.deleteOuterConnect(connectId);
        resultResponse.setResult(true);
        return resultResponse;
    }

}
