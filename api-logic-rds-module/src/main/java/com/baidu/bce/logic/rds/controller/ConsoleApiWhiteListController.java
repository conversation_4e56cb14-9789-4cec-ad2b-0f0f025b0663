package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.FailinjectResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.security.DashboardWhitelistModifyRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityIp;
import com.baidu.bce.internalsdk.rds.model.security.SecurityIpPutRequest;
import com.baidu.bce.internalsdk.rds.model.security.UpdateProxyIpMsg;
import com.baidu.bce.logic.rds.service.WhiteListService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Controller
@RequestMapping(value = "/api/rds/whitelist", produces = {"application/json"})
@Api(value = "RDS Dashboard白名单管理API")
public class ConsoleApiWhiteListController {

    @Autowired
    private WhiteListService whiteListService;
    /**
      * 获取安全IP白名单
      * 根据实例ID获取对应的安全IP白名单信息，并设置响应头ETag
      * 
      * @param requestBody 包含实例ID的请求体
      * @param servletResponse HttpServletResponse对象，用于设置响应头
      * @return 包含安全IP白名单信息的响应对象
      */

    @RequestMapping(value = "get", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SecurityIp> get(
            @IdPermission @Valid @RequestBody InstanceIdRequest requestBody, HttpServletResponse servletResponse) {
        SecurityIp securityIp = whiteListService.getWhiteList(requestBody.getInstanceId());
        servletResponse.setHeader("ETag", securityIp.getETag());
        EdpResultResponse<SecurityIp> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(securityIp);
        return edpResultResponse;
    }
    /**
      * 设置白名单接口
      * 根据请求体中的白名单IP和实例ID，设置白名单
      * 
      * @param requestBody 请求体，包含要设置的白名单IP、实例ID和ETag
      * @return EdpResultResponse<Boolean> 设置结果
      */

    @RequestMapping(value = "set", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> set(
            @IdPermission @Valid @RequestBody DashboardWhitelistModifyRequest requestBody) {
        SecurityIpPutRequest request = new SecurityIpPutRequest();
        request.setSecurityIps(requestBody.getSecurityIps());
        whiteListService.setWhiteList(requestBody.getInstanceId(), request, requestBody.getETag());
        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }

    @ApiOperation("更新proxy ip白名单")
    @RequestMapping(value = "/proxy/accountIp", method = RequestMethod.PUT)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<FailinjectResponse> updateProxyAccountIp(
            @IdPermission @Valid @RequestBody UpdateProxyIpMsg requestBody) {
        EdpResultResponse<FailinjectResponse> response = new EdpResultResponse<>();
        response.setResult(whiteListService.updateProxyAccountIp(requestBody));
        return response;
    }

    @ApiOperation("查询proxy ip白名单")
    @RequestMapping(value = "/proxy/accountIp", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<UpdateProxyIpMsg> getProxyAccountIp(
            @IdPermission @RequestParam String instanceId,
            @RequestParam String accountName) {
        EdpResultResponse<UpdateProxyIpMsg> response = new EdpResultResponse<>();
        response.setResult(whiteListService.getProxyAccountIp(instanceId, accountName));
        return response;
    }


}
