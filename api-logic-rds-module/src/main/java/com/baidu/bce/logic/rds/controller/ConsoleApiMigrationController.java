package com.baidu.bce.logic.rds.controller;


import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.core.BceFormat;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.migration.DashboardMigrationCommitRequest;
import com.baidu.bce.internalsdk.rds.model.migration.DashboardMigrationConnectRequest;
import com.baidu.bce.internalsdk.rds.model.migration.DashboardMigrationId;
import com.baidu.bce.internalsdk.rds.model.migration.DashboardMigrationListRequest;
import com.baidu.bce.internalsdk.rds.model.migration.DatabaseConfig;
import com.baidu.bce.internalsdk.rds.model.migration.Migration;
import com.baidu.bce.internalsdk.rds.model.migration.MigrationDbList;
import com.baidu.bce.internalsdk.rds.model.migration.MigrationId;
import com.baidu.bce.internalsdk.rds.model.migration.MigrationStatus;
import com.baidu.bce.internalsdk.rds.model.migration.PageResponse;
import com.baidu.bce.logic.rds.service.MigrationService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

/**
 * Created by hejianbin on 2014/6/20.
 */
@Controller
@RequestMapping(value = "/api/rds/migration", produces = {"application/json"})
@Api(value = "RDS Dashboard 数据库迁移API")
public class ConsoleApiMigrationController {

    @Autowired
    private MigrationService migrationService;

    /**
      * 获取迁移列表
      * 根据传入的请求参数，获取迁移列表，并返回分页响应结果
      * 
      * @param request 包含分页信息、实例ID、开始时间和结束时间的请求体
      * @return 分页响应结果，包含迁移列表和分页信息
      */
    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public PageResponse<Migration> list(@IdPermission @Valid @RequestBody DashboardMigrationListRequest request) {
        String startTime = null;
        String endTime = null;
        if (request.getStartTime() != null) {
            startTime =  BceFormat.getDateTimeFormat().format(request.getStartTime());
        }
        if (request.getEndTime() != null) {
            endTime =  BceFormat.getDateTimeFormat().format(request.getEndTime());
        }

        PageResponse<Migration> response = new PageResponse<>();
        response.getPage().setPageParams(request.getPageNo(), request.getPageSize(),
                migrationService.list(request.getInstanceId(), startTime, endTime).getMigrations());
        response.setSuccess(true);
        return response;
    }
    /**
      * 连接数据库
      * 根据传入的连接请求信息，尝试连接到指定的数据库实例，并获取数据库列表
      *
      * @param request 数据库连接请求信息，包含IP、端口、用户名和密码等
      * @return EdpResultResponse<MigrationDbList> 封装了数据库列表的响应对象
      */

    @RequestMapping(value = "connect", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<MigrationDbList> connect(
            @IdPermission @Valid @RequestBody DashboardMigrationConnectRequest request) {
        DatabaseConfig databaseConfig = new DatabaseConfig(request.getRemoteIp(), request.getRemotePort(),
                request.getRemoteUser(), request.getRemotePassword());
        MigrationDbList migrationDbList = migrationService.dblist(request.getInstanceId(), databaseConfig);

        EdpResultResponse<MigrationDbList> response = new EdpResultResponse<>();
        response.setResult(migrationDbList);
        return response;
    }
    /**
      * 提交数据迁移请求
      * 该函数用于接收数据迁移的提交请求，进行预处理并返回迁移ID
      * 
      * @param request 数据迁移提交请求体，包含实例ID和其他必要信息
      * @return 包含迁移ID的响应体
      */

    @RequestMapping(value = "commit", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<MigrationId> commit(
            @IdPermission @Valid @RequestBody DashboardMigrationCommitRequest request) {
        MigrationId migrationId = migrationService.preCheck(request.getInstanceId(), request);

        EdpResultResponse<MigrationId> response = new EdpResultResponse<>();
        response.setResult(new MigrationId().withMigrationId(migrationId.getMigrationId()));
        return response;
    }
    /**
      * 检查迁移状态接口
      * 根据实例ID和迁移ID获取迁移状态
      * 
      * @param request 包含实例ID和迁移ID的请求体
      * @return 迁移状态响应
      */

    @RequestMapping(value = "check", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<MigrationStatus> check(@IdPermission @Valid @RequestBody DashboardMigrationId request) {
        MigrationStatus migrationStatus = migrationService.detail(request.getInstanceId(), request.getMigrationId());
        EdpResultResponse<MigrationStatus> response = new EdpResultResponse<>();
        response.setResult(migrationStatus);
        return response;
    }
    /**
      * 处理任务请求
      * 该函数用于处理来自客户端的任务请求，验证权限并执行任务逻辑
      * 
      * @param request 包含实例ID的请求体
      * @return 任务的执行结果
      */

    @RequestMapping(value = "task", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Migration> task(@IdPermission @Valid @RequestBody InstanceIdRequest request) {
        EdpResultResponse<Migration> response = new EdpResultResponse<>();
        response.setResult(migrationService.task(request.getInstanceId()));
        return response;
    }
    /**
      * 启动迁移任务
      * 根据提供的实例ID和迁移ID，创建并启动一个新的迁移任务。
      * 
      * @param request 包含实例ID和迁移ID的请求体
      * @return EdpResultResponse<Boolean> 返回操作结果
      */

    @RequestMapping(value = "start", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> start(@IdPermission @Valid @RequestBody DashboardMigrationId request) {
        migrationService.create(request.getInstanceId(), request.getMigrationId());
        return new EdpResultResponse<>();
    }
    /**
     * 停止同步接口
     * 用于停止RDS同步任务，接收同步任务的实例ID和迁移ID
     * 
     * @param request 包含实例ID和迁移ID的请求体
     * @return EdpResultResponse<Boolean> 停止操作的结果
     */

    @RequestMapping(value = "stop_synchronous", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> stop(@IdPermission @Valid @RequestBody DashboardMigrationId request) {
        migrationService.stop(request.getInstanceId(), request.getMigrationId());
        return new EdpResultResponse<>();
    }
    /**
      * 取消数据迁移接口
      * 该接口用于取消正在进行的数据迁移任务
      * 
      * @param request 包含实例ID和迁移ID的请求体
      * @return EdpResultResponse<Boolean> 返回操作结果
      */

    @RequestMapping(value = "cancel", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> cancel(@IdPermission @Valid @RequestBody DashboardMigrationId request) {
        migrationService.cancel(request.getInstanceId(), request.getMigrationId());
        return new EdpResultResponse<>();
    }
}
