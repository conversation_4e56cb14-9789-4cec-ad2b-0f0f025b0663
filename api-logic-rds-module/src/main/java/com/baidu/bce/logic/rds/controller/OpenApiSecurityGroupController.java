package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.security.OpenApiSecurityGroupResponse;
import com.baidu.bce.internalsdk.rds.model.security.PageSecurityResponse;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupBindRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupPageRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupUpdate;
import com.baidu.bce.logic.rds.service.SecurityGroupService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "/v1/instance", produces = {"application/json"})
@Api(value = "RDS 安全组管理API")
public class OpenApiSecurityGroupController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConsoleApiSecurityGroupController.class);

    @Autowired
    private SecurityGroupService securityGroupService;
    @Autowired
    private IdMapperService idMapperService;

    @ApiOperation("查询实例安全组列表")
    @RequestMapping(value = "/{instanceId}/securityList", method = RequestMethod.GET)
    @PermissionVertify(service = {
            RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @ResponseBody
    public OpenApiSecurityGroupResponse listSecurityGroup(@PathVariable @IdPermission @IdMapper String instanceId) {
        return securityGroupService.listSecurityGroup(instanceId);
    }
    /**
     * 查询vpc下安全组列表
     * 通过vpc信息查询安全组列表，支持分页查询
     *
     * @param groupPageRequest 分页查询参数
     * @param from 来源，默认为空
     * @return 安全组列表的分页结果
     */
    @ApiOperation(value = "查询vpc下安全组列表")
    @RequestMapping(value = "/security/listByVpc", method = RequestMethod.POST)
    @ResponseBody
    public PageSecurityResponse listSecurityGroupByVpc(
            @RequestBody SecurityGroupPageRequest groupPageRequest,
            @RequestParam(defaultValue = "", required = false) String from) {
        return securityGroupService.listSecurityGroupByVpc(groupPageRequest);
    }


    @ApiOperation(value = "绑定安全组")
    @RequestMapping(value = "/security/bind", method = RequestMethod.POST)
    @PermissionVertify(service = {
            RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @ResponseBody
    public void bindSecurityGroup(
            @RequestBody @IdPermission @IdMapper SecurityGroupBindRequest securityGroupBindRequest,
            @RequestParam(defaultValue = "", required = false) String from) {
        LOGGER.debug("bind securityGroup.");
        securityGroupService.bindSecurityGroup(securityGroupBindRequest, from);
    }
    /**
     * 解绑安全组
     * 解绑指定安全组的功能实现
     *
     * @param securityGroupUpdate 安全组更新信息
     * @param from 来源标记，默认为空字符串
     */
    @ApiOperation(value = "解绑安全组")
    @RequestMapping(value = "/security/unbind", method = RequestMethod.POST)
    @PermissionVertify(service = {
            RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @ResponseBody
    public void unbindSecurityGroup(
            @RequestBody @IdPermission @IdMapper SecurityGroupUpdate securityGroupUpdate,
            @RequestParam(defaultValue = "", required = false) String from) {
        LOGGER.debug("unbind securityGroup.");
        from = RDSConstant.FROM_API;
        String instanceUuid = idMapperService.getInstanceUuid(securityGroupUpdate.getInstanceId());
        securityGroupUpdate.setInstanceId(instanceUuid);
        securityGroupService.unbindSecurityGroup(securityGroupUpdate, from);
    }

    @ApiOperation(value = "批量替换安全组")
    @RequestMapping(value = "/security/batchBind", method = RequestMethod.POST)
    @PermissionVertify(service = {
            RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @ResponseBody
    public void updateSecurityGroup(
            @RequestBody @IdPermission @IdMapper SecurityGroupUpdate securityGroupUpdate,
            @RequestParam(defaultValue = "", required = false) String from) {
        LOGGER.debug("update SecurityGroup.");
        from = RDSConstant.FROM_API;
        String instanceUuid = idMapperService.getInstanceUuid(securityGroupUpdate.getInstanceId());
        securityGroupUpdate.setInstanceId(instanceUuid);
        securityGroupService.updateSecurityGroup(securityGroupUpdate, from);
    }

}
