package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.console.settings.service.UserSettingsService;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.rds.model.MaintainDurationRequest;
import com.baidu.bce.internalsdk.rds.model.MaintaintimeTasks;
import com.baidu.bce.internalsdk.rds.model.StatusResponse;
import com.baidu.bce.internalsdk.rds.model.SwitchMasterBackupRequest;
import com.baidu.bce.internalsdk.rds.model.TaskRequest;
import com.baidu.bce.internalsdk.rds.model.UpdateLoaclPolicyRequest;
import com.baidu.bce.internalsdk.rds.model.UpdateTaskRequest;
import com.baidu.bce.internalsdk.rds.model.ViewPageResponse;
import com.baidu.bce.internalsdk.rds.model.instance.AzoneInfo;
import com.baidu.bce.internalsdk.rds.model.instance.BcmGroupResponses;
import com.baidu.bce.internalsdk.rds.model.instance.DashboardInstanceCheckQuotaRequest;
import com.baidu.bce.internalsdk.rds.model.instance.DashboardInstanceDomainRequest;
import com.baidu.bce.internalsdk.rds.model.instance.DashboardInstanceListRequest;
import com.baidu.bce.internalsdk.rds.model.instance.DashboardInstanceModifyNameRequest;
import com.baidu.bce.internalsdk.rds.model.instance.DashboardInstanceModifyPortRequest;
import com.baidu.bce.internalsdk.rds.model.instance.DashboardInstanceModifyPublicRequest;
import com.baidu.bce.internalsdk.rds.model.instance.DashboardInstanceModifyReplicationTypeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.DashboardInstanceQuotRepairTimeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.DashboardInstanceUpdateConfigModel;
import com.baidu.bce.internalsdk.rds.model.instance.FeProductType;
import com.baidu.bce.internalsdk.rds.model.instance.FeRegion;
import com.baidu.bce.internalsdk.rds.model.instance.GlobalInstanceResponses;
import com.baidu.bce.internalsdk.rds.model.instance.HotUpgradeResponse;
import com.baidu.bce.internalsdk.rds.model.instance.HotupgradeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceAzone;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCheckPortResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceInnodbStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceOpenTdeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceProcesslistResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePutFlowRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceQuotChangeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceQuotTimeDetailResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceReplicaDelayMaster;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceTdeStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateAddressRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateNameRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdatePortRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdatePublicAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateReplicaOnLineRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateReplicationTypeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.ListFilter;
import com.baidu.bce.internalsdk.rds.model.instance.PageResponse;
import com.baidu.bce.internalsdk.rds.model.instance.ProxyTopoInfo;
import com.baidu.bce.internalsdk.rds.model.instance.PutFlowRequest;
import com.baidu.bce.internalsdk.rds.model.instance.RecoveryToSourceInstanceRequest;
import com.baidu.bce.internalsdk.rds.model.instance.SubnetIdRequest;
import com.baidu.bce.internalsdk.rds.model.instance.UserKmsListResponse;
import com.baidu.bce.internalsdk.rds.model.instance.ViewPageBaseResponse;
import com.baidu.bce.internalsdk.rds.model.instance.ViewPageRequest;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.common.RdsConfiguration;
import com.baidu.bce.logic.rds.model.InstanceIdListRequest;
import com.baidu.bce.logic.rds.model.InstanceReleaseResponse;
import com.baidu.bce.logic.rds.model.config.PhpAdminUrlConfig;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.PerformanceService;
import com.baidu.bce.logic.rds.service.PricingService;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.RdsListRequest;
import com.baidu.bce.logic.rds.service.model.instance.BatchPriceRequest;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtensionList;
import com.baidu.bce.logic.rds.service.model.instance.RdsResponse;
import com.baidu.bce.logic.rds.service.model.instance.RenzeOut;
import com.baidu.bce.logic.rds.service.model.instance.RenzeRequest;
import com.baidu.bce.logic.rds.service.model.otherservice.ServiceParam;
import com.baidu.bce.logic.rds.service.model.otherservice.ZoneDetailList;
import com.baidu.bce.logic.rds.service.model.pricing.Price;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.resource.manager.sdk.model.BindGroupInfo;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceGroupsDetailFull;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

@RestController
@RequestMapping(value = "/api/rds/instance", produces = {"application/json"})
@Api(value = "RDS Dashboard实例管理API")
public class ConsoleApiInstanceController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConsoleApiInstanceController.class);

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private OthersService othersService;

    @Autowired
    private PricingService pricingService;

    @Autowired
    private RdsConfiguration rdsConfiguration;

    @Autowired
    private IdMapperService idMapperService;

    @Value("${login.dmsUrl}")
    String dmsUrl;

    @Autowired
    private PerformanceService performanceService;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    /**
     * 开通TDE接口
     * 用于开通TDE服务
     * 
     * @param requestBody 开通TDE请求体，包含instanceId等信息
     * @return EdpResultResponse<Boolean> 返回结果，包含操作是否成功
     */
    @ApiOperation(value = "开通tde")
    @RequestMapping(value = "/openTde", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> openTde(@Valid @RequestBody InstanceOpenTdeRequest requestBody) {
        instanceService.openTde(requestBody.getInstanceId(), requestBody);
        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }
    /**
     * 查看tde状态
     * 接口用于查看实例的tde状态。
     * 
     * @param requestBody 包含instanceId的请求体
     * @return 返回查看tde状态的结果
     */

    @ApiOperation(value = "查看tde状态")
    @RequestMapping(value = "/checkTde", method = RequestMethod.POST)
    public EdpResultResponse<InstanceTdeStatusResponse> checkTde(
            @Valid @RequestBody InstanceOpenTdeRequest requestBody) {
        EdpResultResponse<InstanceTdeStatusResponse> response = new EdpResultResponse<>();
        response.setResult(instanceService.checkTde(requestBody.getInstanceId()));
        return response;
    }
    
    /**
      * 获取实例详情
      * 根据实例ID和来源，获取实例的详细信息，包括PHP管理地址和DMS地址
      * 
      * @param requestBody 实例ID请求体，包含实例ID
      * @param from 来源信息，非必须
      * @return 包含实例详细信息的响应体
      * @throws RDSBusinessExceptions.RegionNotExistException 如果区域不存在时抛出异常
      */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<InstanceExtension> getInstance(
            @IdPermission @Valid @RequestBody InstanceIdRequest requestBody,
            @RequestParam(required = false, defaultValue = "") String from) {
        EdpResultResponse<InstanceExtension> instanceDetailResponse = new EdpResultResponse<InstanceExtension>();
        PhpAdminUrlConfig phpAdminUrlConfig = rdsConfiguration.getPhpAdminUrlConfig(
                UserSettingsService.getRegion());
        // 兼容长短id
        String instanceUuid = idMapperService.getInstanceUuid(requestBody.getInstanceId());
        requestBody.setInstanceId(instanceUuid);
        InstanceExtension response = instanceService.detail(requestBody.getInstanceId(), from);
        response.setPhpAdminUrl(phpAdminUrlConfig.getPhpadminUrl());
        response.setDmsUrl(dmsUrl);
        instanceDetailResponse.setResult(response);
        return instanceDetailResponse;
    }
    /**
      * 恢复到源实例的快照
      * 通过指定的快照信息，将目标实例恢复到源实例的状态。
      * 
      * @param request 恢复源实例请求体，包含目标实例ID和快照信息
      * @return 执行结果
      */

    @RequestMapping(value = "/recoveryToSourceInstanceBySnapshot", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> recoveryToSourceInstanceBySnapshot(
            @IdPermission @RequestBody RecoveryToSourceInstanceRequest request) {
        instanceService.recoveryToSourceInstanceBySnapshot(request.getTargetInstanceId(), request, null);
        return new EdpResultResponse<>();
    }
    /**
     * 恢复到源实例接口
     * 根据提供的目标实例ID和时间信息，将实例恢复到源状态
     *
     * @param request 包含目标实例ID和时间信息的请求体
     * @return 操作结果
     */

    @RequestMapping(value = "/recoveryToSourceInstanceByDatetime", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> recoveryToSourceInstanceByDatetime(
            @RequestBody RecoveryToSourceInstanceRequest request) {
        instanceService.recoveryToSourceInstanceByTime(request.getTargetInstanceId(), request, null);
        return new EdpResultResponse<>();
    }
    /**
      * 更新名称接口
      * 用于更新实例名称
      * 
      * @param request 包含实例名称和实例ID的请求体
      * @return 更新操作的结果
      */

    @RequestMapping(value = "update_name", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateName(
            @IdPermission @Valid @RequestBody DashboardInstanceModifyNameRequest request) {
        InstanceUpdateNameRequest instanceUpdateNameRequest = new InstanceUpdateNameRequest();
        instanceUpdateNameRequest.setInstanceName(request.getInstanceName());
        instanceService.updateInstanceName(request.getInstanceId(), instanceUpdateNameRequest);
        return new EdpResultResponse<Boolean>().withResult(true);
    }
    /**
      * 更新维护时间接口
      * 该接口用于更新实例的维护时间。
      * 
      * @param request 维护时间请求对象，包含实例ID和维护时间信息
      * @return EdpResultResponse<Boolean> 返回操作结果，成功则返回true
      * @throws RDSBusinessExceptions.InstanceNotFoundException 实例未找到异常
      * @throws RDSBusinessExceptions.MissingParameterException 缺少参数异常
      * @throws RDSBusinessExceptions.InstanceNotSatisfiableException 实例不满足条件异常
      * @throws RDSBusinessExceptions.InvalidParameterException 参数无效异常
      * @throws RDSBusinessExceptions.InstanceStatusErrorException 实例状态错误异常
      * @throws RDSBusinessExceptions.InternalDBErrorException 数据库内部错误异常
      * @throws RDSBusinessExceptions.InternalServerErrorException 服务器内部错误异常
      */

    @RequestMapping(value = "/maintaintime", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateMaintaintime(
            @IdPermission @Valid @RequestBody MaintainDurationRequest request) {
        try {
            instanceService.updateMaintaintime(request.getInstanceId(), request);
        } catch (BceInternalResponseException e) {
            if ("RDSInstanceNotFound".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceNotFoundException();
            }
            else if ("MissingParameter".equals(e.getCode())) {
                throw new RDSBusinessExceptions.MissingParameterException();
            }
            else if ("InstanceNotSatisfiable ".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceNotSatisfiableException();
            }
            else if ("InvalidParameterValue".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InvalidParameterException();
            }
            else if ("InstanceStatusError".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceStatusErrorException();
            }
            else if ("InternalDBError".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InternalDBErrorException();
            }
            else {
                throw new RDSBusinessExceptions.InternalServerErrorException();
            }
        }
        return new EdpResultResponse<Boolean>().withResult(true);
    }
    /**
      * 更新复制类型
      * 更新实例的复制类型
      * 
      * @param request 包含复制类型和实例ID的请求体
      * @return 返回操作结果
      */

    @RequestMapping(value = "update_replicationType", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateReplicationType(
            @IdPermission @Valid @RequestBody DashboardInstanceModifyReplicationTypeRequest request) {
        InstanceUpdateReplicationTypeRequest instanceUpdateReplicationTypeRequest =
                new InstanceUpdateReplicationTypeRequest();
        instanceUpdateReplicationTypeRequest.setReplicationType(request.getReplicationType());
        instanceService.updateReplicationType(request.getInstanceId(), instanceUpdateReplicationTypeRequest);
        return new EdpResultResponse<Boolean>().withResult(true);
    }
    /**
      * 更新域名接口
      * 根据实例ID和域名信息更新实例的域名
      * 
      * @param request 包含实例ID和域名的请求体
      * @return 操作结果
      */

    @RequestMapping(value = "update_domain", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateDomain(
            @IdPermission @Valid @RequestBody DashboardInstanceDomainRequest request) {
        InstanceUpdateAddressRequest instanceUpdateAddressRequest = new InstanceUpdateAddressRequest();
        instanceUpdateAddressRequest.setEndpoint(new Instance.Endpoint().address(request.getDomain()));
        instanceService.updateDomain(request.getInstanceId(), instanceUpdateAddressRequest, "");
        return new EdpResultResponse<Boolean>().withResult(true);
    }
    /**
      * 实例端口检查
      * 根据实例ID进行端口检查
      * 
      * @param request 实例ID请求体
      * @return 端口检查结果
      */

    @RequestMapping(value = "entry_check", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<InstanceCheckPortResponse> instanceCheckPort(
            @IdPermission @Valid @RequestBody InstanceIdRequest request) {
        InstanceCheckPortResponse instanceCheckPortResponse =
                instanceService.instanceEnrtyCheck(request.getInstanceId());
        return new EdpResultResponse<InstanceCheckPortResponse>().withResult(instanceCheckPortResponse);
    }
    /**
      * 更新端口接口
      * 更新实例的端口信息
      * 
      * @param request 端口更新请求体，包含实例ID和端口信息
      * @return EdpResultResponse<Boolean> 操作结果
      */

    @RequestMapping(value = "update_port", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updatePort(
            @IdPermission @Valid @RequestBody DashboardInstanceModifyPortRequest request) {
        InstanceUpdatePortRequest instanceUpdatePortRequest = new InstanceUpdatePortRequest();
        instanceUpdatePortRequest.setEntryPort(request.getEntryPort());
        instanceService.updateInstancePort(request.getInstanceId(), instanceUpdatePortRequest);
        return new EdpResultResponse<Boolean>().withResult(true);
    }
    /**
      * 更新实例公网访问权限
      * 通过调用此方法，可以更新RDS实例的公网访问权限。
      * 
      * @param request 包含实例ID和是否公开访问的请求体
      * @return EdpResultResponse<Boolean> 返回操作结果，成功为true
      */

    @RequestMapping(value = "update_internet_access", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateInternetAccess(
            @IdPermission @Valid @RequestBody DashboardInstanceModifyPublicRequest request) {
        InstanceUpdatePublicAccessibleRequest instanceUpdatePublicAccessibleRequest =
                new InstanceUpdatePublicAccessibleRequest();
        instanceUpdatePublicAccessibleRequest.setPubliclyAccessible(request.isPubliclyAccessible());
        instanceService.updatePubliclyAccessible(request.getInstanceId(), instanceUpdatePublicAccessibleRequest);
        return new EdpResultResponse<Boolean>().withResult(true);
    }
    /**
     * 获取VPC列表
     * 该函数用于处理获取VPC列表的请求
     * 
     * @return 返回包含VPC列表的结果对象
     */

    @RequestMapping(value = "vpc_list", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<List<VpcVo>> vpcList() {
        EdpResultResponse<List<VpcVo>> response = new EdpResultResponse<>();
        response.setResult(othersService.vpcList());
        return response;
    }
    /**
     * 获取区域列表
     * 根据请求获取区域列表的信息
     * 
     * @return 区域列表信息
     */

    @RequestMapping(value = "zone_list", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<ZoneDetailList> zoneList() {
        EdpResultResponse<ZoneDetailList> response = new EdpResultResponse<>();
        response.setResult(othersService.zoneList(""));
        return response;
    }
    /**
     * 检查主配额接口
     * 用于检查主配额是否满足条件，通过POST方法请求
     * 
     * @return EdpResultResponse<Boolean> 返回检查结果，包含是否满足配额的布尔值
     */

    @RequestMapping(value = "/checkMasterQuota", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> checkMasterQuota() {
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(instanceService.checkMasterQuota());
        return response;
    /**
      * V2询价接口
      * 该接口用于提供V2版本的询价功能
      * 
      * @param request 请求体，包含询价所需的各种参数
      * @return 返回询价结果
      */
    }
    @ApiOperation("V2询价接口")
    @RequestMapping(value = "/v2/get_price", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Price> priceV2(@Valid @RequestBody InstanceCreateModel request) {
        EdpResultResponse<Price> response = new EdpResultResponse<>();
        response.setResult(pricingService.getPriceV2(request, null));
        return response;
    }
    
    /**
      * 询价接口
      * 提供询价功能，接收询价请求并返回询价结果
      * 
      * @param request 询价请求参数
      * @return 询价结果
      */
    @ApiOperation("询价接口")
    @RequestMapping(value = "get_price", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Price> price(@Valid @RequestBody InstanceCreateModel request) {
        EdpResultResponse<Price> response = new EdpResultResponse<>();
        response.setResult(pricingService.getPrice(request, null));
        return response;
    }

    /**
     * 批量询价接口
     * 该接口用于批量获取商品价格信息
     * 
     * @param request 批量询价请求参数，包含商品信息列表
     * @return 返回批量询价的结果
     */
    @ApiOperation("批量询价接口")
    @RequestMapping(value = "/getBatchPrice", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<List<Price>> batchPrice(@Valid @RequestBody BatchPriceRequest request) {
        EdpResultResponse<List<Price>> response = new EdpResultResponse<>();
        response.setResult(pricingService.getBatchPrice(request, null));
        return response;
    }
    /**
     * 获取子网剩余ip数量
     * 接口功能为通过子网ID获取子网剩余ip数量
     * 
     * @param request 包含子网ID的请求体
     * @return EdpResultResponse<SubnetVo> 返回子网剩余ip数量的响应体
     */

    @ApiOperation(value = "获取子网剩余ip数量")
    @RequestMapping(value = "/subnet/detail", method = RequestMethod.POST)
    public EdpResultResponse<SubnetVo> subnetIpUsed(@Valid @RequestBody SubnetIdRequest request) {
        EdpResultResponse<SubnetVo> response = new EdpResultResponse<>();
        response.setResult(othersService.subnetIpUsed(request.getSubnetId()));
        return response;
    }

    @RequestMapping(value = "convert_flavor", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<RdsResponse> ssr(@RequestBody RenzeRequest request) {
        RenzeOut cpu = new RenzeOut();
        cpu.setName("cpu");
        cpu.setValue("1");
        cpu.setScale(request.getInstance().getCpuCount());
        RenzeOut memory = new RenzeOut();
        memory.setName("memory");
        memory.setValue("1g");
        memory.setScale(request.getInstance().getAllocatedMemoryInGB());
        RenzeOut subServiceType = new RenzeOut();
        subServiceType.setName("subServiceType");
        subServiceType.setValue(request.getInstance().getEngine());
        RenzeOut disk = new RenzeOut();
        disk.setName("disk");
        disk.setValue("1g");
        disk.setScale(request.getInstance().getAllocatedStorageInGB());

        RdsResponse rds = new RdsResponse();
        rds.getRds().add(cpu);
        rds.getRds().add(memory);
        rds.getRds().add(subServiceType);
        rds.getRds().add(disk);

        EdpResultResponse<RdsResponse> response = new EdpResultResponse<>();
        response.setResult(rds);
        return response;
    }


    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    public EdpPageResultResponse<InstanceAbstract> list(@RequestBody DashboardInstanceListRequest request) {
        int daysToExpiration = "all".equalsIgnoreCase(request.getShowMode()) ? -1 : 7;
        if (request.getOrder().isEmpty()) {
            request.setOrder("desc");
        }
        if (request.getOrderBy().isEmpty()) {
            request.setOrderBy("instanceCreateTime");
        }

        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, request.getOrder(), request.getOrderBy(),
                request.getPageNo(), request.getPageSize());
        Map<String, String> filterMap = filter2Map(request.getFilters());
        if (filterMap != null && filterMap.get("instanceId") != null) {
            filterMap.put("instanceShortId", filterMap.get("instanceId"));
            filterMap.remove("instanceId");
        }
        listRequest.setFilterMap(filterMap);
        LogicPageResultResponse<InstanceAbstract> logicPageResultResponse =
                instanceService.listInstanceWithPageByMultiKey(listRequest, request.getMachineType(), Boolean.FALSE);

        EdpPageResultResponse<InstanceAbstract> edpPageResultResponse = new EdpPageResultResponse<>();
        edpPageResultResponse.setPage(new EdpPageResultResponse.Page<InstanceAbstract>());
        edpPageResultResponse.getPage().setPageNo(logicPageResultResponse.getPageNo());
        edpPageResultResponse.getPage().setPageSize(logicPageResultResponse.getPageSize());
        edpPageResultResponse.getPage().setOrder(logicPageResultResponse.getOrder());
        edpPageResultResponse.getPage().setOrderBy(logicPageResultResponse.getOrderBy());
        edpPageResultResponse.getPage().setTotalCount(logicPageResultResponse.getTotalCount());
        edpPageResultResponse.getPage().setResult(logicPageResultResponse.getResult());
        return edpPageResultResponse;
    }
    /**
     * 概览页：实例列表
     * 概览页实例列表接口，用于获取实例列表信息
     * 
     * @return EdpResultResponse<GlobalInstanceResponses> 返回实例列表的响应对象
     */

    @ApiOperation("概览页：实例列表")
    @RequestMapping(value = "/overview/list", method = RequestMethod.GET)
    @ResponseBody
    public EdpResultResponse<GlobalInstanceResponses> overviewList() {
        EdpResultResponse<GlobalInstanceResponses> response = new EdpResultResponse<>();
        response.setResult(instanceService.overviewList());
        return response;
    }
    /**
      * 列出非RDS代理实例
      * 根据请求条件，列出非RDS代理实例的分页信息。
      * 
      * @param request 请求参数，包含分页、排序、过滤等信息
      * @return EdpPageResultResponse<InstanceAbstract> 分页结果，包含实例列表和分页信息
      */

    @RequestMapping(value = "listexceptproxy", method = RequestMethod.POST)
    @ResponseBody
    public EdpPageResultResponse<InstanceAbstract> listexceptproxy(@RequestBody DashboardInstanceListRequest request) {
        int daysToExpiration = "all".equalsIgnoreCase(request.getShowMode()) ? -1 : 7;

        if (request.getOrder().isEmpty()) {
            request.setOrder("desc");
        }
        if (request.getOrderBy().isEmpty()) {
            request.setOrderBy("instanceCreateTime");
        }

        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, request.getOrder(), request.getOrderBy(),
                request.getPageNo(), request.getPageSize());
        Map<String, String> filterMap = filter2Map(request.getFilters());
        if (filterMap != null && filterMap.get("instanceId") != null) {
            filterMap.put("instanceShortId", filterMap.get("instanceId"));
            filterMap.remove("instanceId");
        }
        listRequest.setFilterMap(filterMap);
        LogicPageResultResponse<InstanceAbstract> logicPageResultResponse =
                instanceService.listInstanceWithPageByMultiKey(listRequest, request.getMachineType(), Boolean.FALSE);


        List<InstanceAbstract> list = new ArrayList<>();
        for (InstanceAbstract instanceAbstract : logicPageResultResponse.getResult()) {
            if (!"rdsproxy".equals(instanceAbstract.getInstanceType())){
                list.add(instanceAbstract);
            }
        }

        EdpPageResultResponse<InstanceAbstract> edpPageResultResponse = new EdpPageResultResponse<>();
        edpPageResultResponse.setPage(new EdpPageResultResponse.Page<InstanceAbstract>());
        edpPageResultResponse.getPage().setPageNo(logicPageResultResponse.getPageNo());
        edpPageResultResponse.getPage().setPageSize(logicPageResultResponse.getPageSize());
        edpPageResultResponse.getPage().setOrder(logicPageResultResponse.getOrder());
        edpPageResultResponse.getPage().setOrderBy(logicPageResultResponse.getOrderBy());
        edpPageResultResponse.getPage().setTotalCount(logicPageResultResponse.getTotalCount());
        edpPageResultResponse.getPage().setResult(list);
        return edpPageResultResponse;
    }
    /**
     * 获取BCM实例列表
     * 根据请求参数获取BCM实例列表，并对请求进行一系列处理，最终返回处理后的分页结果
     * 
     * @param request 请求参数，包含分页、排序、过滤等信息
     * @param type 类型，可选参数，默认为空字符串
     * @return 分页结果，包含BCM实例列表及分页信息
     */

    @RequestMapping(value = "listforbcm", method = RequestMethod.POST)
    @ResponseBody
    public EdpPageResultResponse<InstanceAbstract> listforbcm(@RequestBody DashboardInstanceListRequest request,
                                                    @RequestParam(required = false, defaultValue = "") String type) {

        int daysToExpiration = "all".equalsIgnoreCase(request.getShowMode()) ? -1 : 7;
        if (request.getOrder().isEmpty()) {
            request.setOrder("desc");
        }
        if (request.getOrderBy().isEmpty()) {
            request.setOrderBy("instanceCreateTime");
        }
        ListFilter filter = new ListFilter();
        filter.setKeyword(request.getKeyword());
        filter.setKeywordType(request.getKeywordType());
        List<ListFilter> filters = new ArrayList<>();
        filters.add(0, filter);
        LOGGER.info("filters:" + filters.get(0).getKeyword());
        request.setFilters(filters);
        LOGGER.info("setFilters:" + request.getFilters());

        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, request.getOrder(), request.getOrderBy(),
                request.getPageNo(), request.getPageSize());
        Map<String, String> filterMap = filter2Map(request.getFilters());
        listRequest.setFilterMap(filterMap);

        if (filterMap != null && filterMap.get("instanceId") != null) {
            filterMap.put("instanceShortId", filterMap.get("instanceId"));
            filterMap.remove("instanceId");
        }
        LogicPageResultResponse<InstanceAbstract> logicPageResultResponse =
                instanceService.listforbcm(listRequest, request.getMachineType(), Boolean.FALSE, type);
        EdpPageResultResponse<InstanceAbstract> edpPageResultResponse = new EdpPageResultResponse<>();
        edpPageResultResponse.setPage(new EdpPageResultResponse.Page<InstanceAbstract>());
        edpPageResultResponse.getPage().setPageNo(logicPageResultResponse.getPageNo());
        edpPageResultResponse.getPage().setPageSize(logicPageResultResponse.getPageSize());
        edpPageResultResponse.getPage().setTotalCount(logicPageResultResponse.getTotalCount());
        edpPageResultResponse.getPage().setOrder(logicPageResultResponse.getOrder());
        edpPageResultResponse.getPage().setOrderBy(logicPageResultResponse.getOrderBy());
        edpPageResultResponse.getPage().setResult(logicPageResultResponse.getResult());
        return edpPageResultResponse;
    }

    /**
     * 获取代理管理下的实例列表
     * 接口用于获取代理管理下的实例列表，支持按实例ID过滤、排序，返回包含实例列表的分页响应结果
     *
     * @param request 包含实例ID的请求参数
     * @return 包含实例列表的分页响应结果
     * @throws Exception 抛出异常
     */
    @RequestMapping(value = "list", method = RequestMethod.POST, params = {"action=proxyManage"})
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpPageResultResponse<Instance> listForProxyManage(
            @IdPermission @Valid @RequestBody InstanceIdRequest request) {
        Collection<Instance> instances = instanceService.proxyManageList(request.getInstanceId());
        EdpPageResultResponse<Instance> edpPageResultResponse = new EdpPageResultResponse<>();
        edpPageResultResponse.setPage(new EdpPageResultResponse.Page<Instance>());
        edpPageResultResponse.getPage().setResult(instances);
        return edpPageResultResponse;
    }
    /**
      * 只读流量开关
      * 控制实例只读流量的开启或关闭
      * 
      * @param request 只读流量开关请求体，包含rdsProxyId和其他请求信息
      * @return 执行结果
      */

    @ApiOperation(value = "只读流量开关")
    @RequestMapping(value = "/onOrOfflineReplica", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> onOrOfflineReplica(
            @IdPermission @RequestBody @Valid InstanceUpdateReplicaOnLineRequest request) {
        instanceService.onOrOfflineReplica(request.getRdsproxyId(), request);
        return new EdpResultResponse<>();
    }
    /**
     * 设置流量配比
     * 设置RDS流量配比接口
     * 
     * @param request 请求参数，包含rdsproxyId、replicaId和weight
     * @return EdpResultResponse<Boolean> 返回执行结果
     */

    @ApiOperation(value = "设置流量配比")
    @RequestMapping(value = "/putFlow", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> putFlow(@IdPermission @RequestBody @Valid InstancePutFlowRequest request) {
        PutFlowRequest putFlowRequest = new PutFlowRequest();
        putFlowRequest.setRdsproxyId(request.getRdsproxyId());
        putFlowRequest.setReplicaId(request.getReplicaId());
        putFlowRequest.setWeight(request.getWeight());
        instanceService.putFlow(request.getRdsproxyId(), putFlowRequest);
        return new EdpResultResponse<>();
    }

    /**
      * 下载RDS实例列表
      * 提供下载RDS实例列表的功能，支持按条件过滤、排序，返回CSV格式的字节流
      * 
      * @param order 排序方式，可选值为"asc"或"desc"，默认为"desc"
      * @param orderBy 排序字段，默认为"instanceCreateTime"
      * @param showMode 显示模式，可选值为"all"或"expire"，默认为"all"
      * @param filterStr 过滤条件，JSON格式的字符串
      * @param machineType 机器类型，默认为空字符串
      * @param servletResponse HttpServletResponse对象，用于设置响应头信息
      * @return CSV格式的字节流
      * @throws RDSExceptions.DownloadException 文件名编码异常时抛出
      */
    @RequestMapping(value = "download" , method = RequestMethod.GET)
    public byte[] downloadInstance(
            @RequestParam(value = "order", required = false) String order,
            @RequestParam(value = "orderBy", required = false) String orderBy,
            @RequestParam(value = "showMode", required = false, defaultValue = "all") String showMode,
            @RequestParam(value = "filters", required = false) String filterStr,
            @RequestParam(required = false, defaultValue = "") String machineType,
            javax.servlet.http.HttpServletResponse servletResponse) {
        String csv = "RDS_ID,名称,状态,引擎类型,公网IP,内网IP,域名,端口,实例规格(CPU(核)/内存(GB)),空间(GB)"
                + ",已用空间(MB),支付方式,标签,资源组,创建时间,到期时间,地域" + "\r\n";
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String date = df.format(new Date());
        final String region = UserSettingsService.getRegion().intern();

        LOGGER.info("rds region : {}", region);
        // get rds list
        List<ListFilter> filters = new ArrayList<>();
        if (StringUtils.isNotEmpty(filterStr)) {
            List<Map> filterList = fromJSON(filterStr, List.class);
            if (filterList != null && filterList.size() > 0) {
                for (Map filter : filterList) {
                    ListFilter listFilter = new ListFilter();
                    listFilter.setKeywordType((String) filter.get("keywordType"));
                    listFilter.setKeyword((String) filter.get("keyword"));
                    listFilter.setSubKeywordType((String) filter.get("subKeywordType"));
                    filters.add(listFilter);
                }
            }
        }
        List<InstanceAbstract> instanceAbstracts = null;
        int daysToExpiration = "all".equalsIgnoreCase(showMode) ? -1 : 7;
        if (order.isEmpty()) {
            order = "desc";
        }
        if (orderBy.isEmpty()) {
            orderBy = "instanceCreateTime";
        }

        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, order, orderBy, 1, 1000);
        Map<String, String> filterMap = filter2Map(filters);
        if (filterMap != null && filterMap.get("instanceId") != null) {
            filterMap.put("instanceShortId", filterMap.get("instanceId"));
            filterMap.remove("instanceId");
        }
        listRequest.setFilterMap(filterMap);
        LogicPageResultResponse<InstanceAbstract> logicPageResultResponse =
                instanceService.listInstanceWithPageByMultiKey(listRequest, machineType, Boolean.FALSE);
        instanceAbstracts = (List<InstanceAbstract>) logicPageResultResponse.getResult();

        if (instanceAbstracts != null && instanceAbstracts.size() != 0) {
            for (InstanceAbstract instanceAbstract : instanceAbstracts) {
                ResourceGroupsDetailFull resourceGroup = instanceAbstract.getResourceGroup();
                String instanceIdShow = instanceAbstract.getInstanceId();
                if (StringUtils.isNotEmpty(instanceAbstract.getInstanceShortId())) {
                    instanceIdShow = instanceAbstract.getInstanceShortId();
                }                    csv = csv + instanceIdShow + ",";
                csv = csv + instanceAbstract.getInstanceName() + ",";
                csv = csv + instanceAbstract.getInstanceStatus() + ",";
                csv = csv + instanceAbstract.getEngine() + ",";
                if (!StringUtils.isEmpty(instanceAbstract.getEndpoint().getInetIp())) {
                    csv = csv + instanceAbstract.getEndpoint().getInetIp() + ",";
                } else {
                    csv = csv + "——,";
                }
                csv = csv + instanceAbstract.getEndpoint().getVnetIp() + ",";
                csv = csv + instanceAbstract.getEndpoint().getAddress() + ",";
                csv = csv + instanceAbstract.getEndpoint().getPort() + ",";
                csv = csv + (instanceAbstract.getCpuCount() + "核"
                        + instanceAbstract.getAllocatedMemoryInMB() / 1024 + "GB") + ",";
                csv = csv + instanceAbstract.getAllocatedStorageInGB() + ",";
                csv = csv + instanceAbstract.getUsedStorageInMB() + ",";
                csv = csv + FeProductType.statusOf(instanceAbstract.getProductType()).toText() + ",";
                if (instanceAbstract.getTags() == null || instanceAbstract.getTags().size() == 0){
                    csv = csv + "——,";
                } else {
                    int len = instanceAbstract.getTags().size();
                    for (int i = 0; i < len; i++) {
                        csv = csv + instanceAbstract.getTags().get(i).getTagKey() + ":"
                                + instanceAbstract.getTags().get(i).getTagValue() + ";";
                    }
                    csv = csv.substring(0, csv.length()-1) + ",";
                }
                csv = csv + (resourceGroup == null ? "——" : convertGroupsToString(resourceGroup)) + ",";
                csv = csv + formatDateBj(instanceAbstract.getInstanceCreateTime()) + ",";
                if (instanceAbstract.getInstanceExpireTime() == null) {
                    csv = csv + "——,";
                } else {
                    csv = csv + formatDateBj(instanceAbstract.getInstanceExpireTime()) + ",";
                }
                csv = csv + FeRegion.statusOf(region).toText() + "\r\n";
            }
        }

        String fileName = "";
        try {
            fileName = URLEncoder.encode("RDS_" + region + "_" + date + "_list.csv", "UTF-8");
        } catch (UnsupportedEncodingException e) {
            LOGGER.debug("fileName error : {}", e);
            throw new RDSExceptions.DownloadException();
        }
        servletResponse.setHeader("Content-Disposition", "attachment; filename*=utf-8''" + fileName);
        servletResponse.setHeader("Content-Type", "application/octet-stream; charset=utf-8");
        byte[] bom4utf8 = new byte[] { (byte) 0xEF, (byte) 0xBB, (byte) 0xBF };
        byte[] bytes = csv.getBytes();
        byte[] outputBytes = new byte[bom4utf8.length + bytes.length];
        System.arraycopy(bom4utf8, 0, outputBytes, 0, bom4utf8.length);
        System.arraycopy(bytes, 0, outputBytes, 3, bytes.length);
        return outputBytes;
    }

    /**
     * 将资源组转变为key:value字符串
     *
     * @param group 资源组
     * @return
     */
    public String convertGroupsToString(ResourceGroupsDetailFull group) {
        StringBuilder sb = new StringBuilder();
        if (group == null) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        if (group.getName() != null) {
            sb.append(group.getName());
        }
        if (!CollectionUtils.isEmpty(group.getGroups())) {
            for (BindGroupInfo childGroup : group.getGroups()) {
                sb.append(":");
                if (childGroup.getName() != null) {
                    sb.append(childGroup.getName());
                } else {
                    sb.append(" ");
                }
                // sb.append(",");
            }
        }
        return sb.toString();
    }


    /**
      * 重启实例接口
      * 重启指定的RDS实例
      * 
      * @param request 重启实例的请求体，包含实例ID等必要信息
      * @return 操作结果
      */
    @RequestMapping(value = "reboot", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> reboot(@IdPermission @Valid @RequestBody @IdMapper InstanceIdRequest request) {
        instanceService.reboot(request);
        return new EdpResultResponse<>();
    }
    /**
     * 价格差异计算接口
     * 根据请求参数计算价格差异
     * 
     * @param request 价格差异请求参数
     * @return 价格差异计算结果
     */

    @RequestMapping(value = "price_difference", method = RequestMethod.POST)
    public EdpResultResponse<Price> priceDifference(@Valid @RequestBody PriceDiffModel request) {
        EdpResultResponse<Price> response = new EdpResultResponse<>();
        response.setResult(pricingService.getPriceDiff(request));
        return response;
    }
    /**
      * 延迟副本同步时间接口
      * 根据实例ID延迟副本同步时间
      * 
      * @param request 包含实例ID的请求体
      * @return 操作结果，包含延迟后的副本同步时间信息
      */

    @RequestMapping(value = "replica_behind_master", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<InstanceReplicaDelayMaster> delay(
            @IdPermission @Valid @RequestBody InstanceIdRequest request) {
        EdpResultResponse<InstanceReplicaDelayMaster> response = new EdpResultResponse<>();
        response.setResult(instanceService.replicaDelay(request.getInstanceId()));
        return response;
    }
    /**
      * 检查副本配额
      * 根据请求中的实例ID，检查副本配额是否满足要求
      * 
      * @param request 包含实例ID的请求体
      * @return EdpResultResponse<Boolean> 检查结果
      */

    @RequestMapping(value = "/checkReplicaQuota", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> checkReplicaQuota(
            @Valid @RequestBody DashboardInstanceCheckQuotaRequest request) {
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        response.setResult(instanceService.checkReplicaQuota(request.getInstanceId()));
        return response;
    }

    private InstanceReleaseResponse deleteBatch(String instanceIds, String from) {
        LOGGER.debug("release instance batch. instanceIds: " + instanceIds);

        // 部分api请求暂时只支持root user
//        checkApiRootUserOper(from);

        InstanceReleaseResponse instanceReleaseResponse = new InstanceReleaseResponse();
        List<String> delFailed;
        try {
            if (StringUtils.isEmpty(instanceIds)) {
                throw new RDSExceptions.ParamValidationException("instanceIds can not be empty.");
            }
            String[] instanceId = instanceIds.split(",");
            for (String id : instanceId) {
                if (StringUtils.isEmpty(id)) {
                    throw new RDSExceptions.ParamValidationException();
                }
            }
            if (instanceId.length > 10) {
                throw new RDSExceptions.ParamValidationException("Release up to 10 at a time");
            }
            delFailed = instanceService.oldDelete(instanceId, from);
            if (delFailed.size() > 0) {
                instanceReleaseResponse.setSuccess(false);
            }
            instanceReleaseResponse.setReleaseFailedInstanceIds(delFailed);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return instanceReleaseResponse;
    }
    /**
     * 释放实例
     * 用于批量释放RDS实例，支持按实例ID进行删除，支持边缘区域和非边缘区域的删除逻辑
     * 
     * @param request 包含要释放的实例ID信息
     * @return 返回操作结果，包含是否成功释放实例
     * @throws RDSExceptions.ParamValidationException 当实例ID为空或格式不正确时抛出
     * @throws RDSExceptions.BatchDeleteParamValidationException 当批量删除的实例ID超过3个时抛出
     * @throws RDSExceptions.InstanceDeleteFailedException 当部分实例删除失败时抛出
     */

    @ApiOperation("释放实例")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> deleteInstanceBatch(
            @IdPermission @Valid @RequestBody InstanceIdRequest request) {
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        String instanceIds = request.getInstanceId();
        if (instanceIds == null || instanceIds.length() == 0) {
            throw new RDSExceptions.ParamValidationException();
        }

        InstanceReleaseResponse instanceReleaseResponse = new InstanceReleaseResponse();
        if (regionConfiguration.getCurrentRegion().equals(RDSConstant.REGION_EDGE)) {
            instanceReleaseResponse = deleteBatch(instanceIds, "");
        } else {
            List<String> delFailed;
            if (instanceIds.contains(",")) {
                String[] splits = instanceIds.split(",");
                if (splits.length > 3) {
                    throw new RDSExceptions.BatchDeleteParamValidationException();
                }
                delFailed = instanceService.delete(splits, "");
            } else {
                String[] splits = new String[1];
                splits[0] = instanceIds;
                delFailed = instanceService.delete(splits, "");
            }
            if (delFailed.size() > 0) {
                instanceReleaseResponse.setSuccess(false);
            }
            instanceReleaseResponse.setReleaseFailedInstanceIds(delFailed);
        }

        List<String> delFailedInstanceIds = instanceReleaseResponse.getReleaseFailedInstanceIds();
        if (delFailedInstanceIds.size() > 0) {
            StringBuilder ids = new StringBuilder();
            for(String instanceId : delFailedInstanceIds) {
                ids.append(instanceId).append(",");
            }
//            throw new RDSExceptions.InstanceDeleteFailedException(ids.substring(0, ids.length() - 1));
            throw new RDSBusinessExceptions.InstanceReleaseFailedException(ids.substring(0, ids.length() - 1));
        }
        return response.withResult(true);
    }



    private static String formatDateBj(Date date) {
        if (date == null) {
            return "null";
        }
        DateFormat formater = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        formater.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        return formater.format(date);
    }

    public static <T> T fromJSON(String json, Class<T> clazz) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(json, clazz);
        } catch (JsonParseException e) {
            throw new RuntimeException(e);
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }



    public static String filter2String(List<ListFilter> filterList) {
        StringWriter writer = new StringWriter();
        try {
            Map<String, String> filterMap = null;
            if (filterList != null && filterList.size() > 0) {
                filterMap = new HashMap<>();
                for (ListFilter filter : filterList) {
                    if ("tag".equalsIgnoreCase(filter.getKeywordType()) && StringUtils.isNotBlank(filter
                            .getSubKeywordType())) {
                        filterMap.put("tag", filter.getSubKeywordType() + "__" + filter.getKeyword());
                        continue;
                    }
                    if ("status".equalsIgnoreCase(filter.getKeywordType())) {
                        filter.setKeywordType("instanceStatus");
                    }
                    filterMap.put(filter.getKeywordType(), filter.getKeyword());
                }
            }
            if (filterMap != null) {
                new ObjectMapper().writeValue(writer, filterMap);
            }
        } catch (Exception ex) {
            System.out.print("filter2String error");
        }
        return writer.toString();
    }
    /**
     * 获取实例短ID
     * 根据传入的实例ID列表，获取对应的短ID映射
     * 
     * @param request 包含实例ID列表的请求体
     * @return 包含实例ID到短ID映射的结果
     */

    @RequestMapping(value = "get_instance_shortids", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Map<String, String>> getInstanceShortIds(
            @Valid @RequestBody InstanceIdListRequest request) {
        EdpResultResponse<Map<String, String>> response = new EdpResultResponse<>();
        response.setResult(othersService.getIdMapV2(request.getInstanceIds()));
        return response;
    }
    
    /**
     * 获取服务参数
     * 该函数用于获取服务参数，并返回结果
     * 
     * @return EdpResultResponse<ServiceParam> 返回服务参数的结果对象
     */
    @RequestMapping(value = "param", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<ServiceParam> getServiceParam() {
        EdpResultResponse<ServiceParam> response = new EdpResultResponse<>();
        response.setResult(othersService.getServiceParam());
        return response;
    }
    /**
     * 获取RDS默认服务参数
     * 通过POST请求获取RDS默认服务参数
     * 
     * @return 返回RDS默认服务参数的响应结果
     */

    @RequestMapping(value = "rdsdefault_param", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<ServiceParam> getRdsDefaultServiceParam() {
        EdpResultResponse<ServiceParam> response = new EdpResultResponse<>();
        response.setResult(othersService.getServiceParamForRdsBackend());
        return response;
    }
    
    /**
      * 转换规格接口
      * 根据请求体中的配置信息，生成对应的RDS规格响应体
      * 
      * @param request 请求体，包含CPU、内存、存储等配置信息
      * @return 转换后的RDS规格响应体
      */
    @RequestMapping(value = "convert_flavor", method = RequestMethod.POST, params = {"orderType=RESIZE"})
    @ResponseBody
    public EdpResultResponse<RdsResponse> sr(@RequestBody DashboardInstanceUpdateConfigModel request) {
        RenzeOut cpu = new RenzeOut();
        cpu.setName("cpu");
        cpu.setValue("1");
        cpu.setScale(request.getCpuCount());
        RenzeOut memory = new RenzeOut();
        memory.setName("memory");
        memory.setValue("1g");
        memory.setScale(request.getAllocatedMemoryInGB());
        RenzeOut subServiceType = new RenzeOut();
        subServiceType.setName("subServiceType");
        subServiceType.setValue("");
        RenzeOut disk = new RenzeOut();
        disk.setName("disk");
        disk.setValue("1g");
        disk.setScale(request.getAllocatedStorageInGB());

        RdsResponse rds = new RdsResponse();
        rds.getRds().add(cpu);
        rds.getRds().add(memory);
        rds.getRds().add(subServiceType);
        rds.getRds().add(disk);

        EdpResultResponse<RdsResponse> response = new EdpResultResponse<>();
        response.setResult(rds);
        return response;
    }
    
    /**
      * 获取报价实例列表
      * 根据请求参数获取报价实例列表，并支持分页查询
      * 
      * @param request 请求参数，包含分页信息、排序信息、过滤条件等
      * @return 分页响应对象，包含报价实例列表及分页信息
      */                                                    
    @RequestMapping(value = "list", method = RequestMethod.POST, params = {"action=quotManage"})
    @ResponseBody
    public PageResponse<InstanceAbstract> quotList(@RequestBody DashboardInstanceListRequest request) {
        int daysToExpiration = "all".equalsIgnoreCase(request.getShowMode()) ? -1 : 7;
        if (request.getOrder().isEmpty()) {
            request.setOrder("desc");
        }
        if (request.getOrderBy().isEmpty()) {
            request.setOrderBy("instanceCreateTime");
        }

        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, request.getOrder(), request.getOrderBy(),
                request.getPageNo(), request.getPageSize());
        Map<String, String> filterMap = filter2Map(request.getFilters());
        if (filterMap != null && filterMap.get("instanceId") != null) {
            filterMap.put("instanceShortId", filterMap.get("instanceId"));
            filterMap.remove("instanceId");
        }
        listRequest.setFilterMap(filterMap);

        LogicPageResultResponse<InstanceAbstract> pageResultResponse =
                instanceService.listQuotInstanceWithPageByMultiKey(listRequest);

        PageResponse<InstanceAbstract> response = new PageResponse<>();
        response.setPage(new PageResponse.Page<InstanceAbstract>());
        response.getPage().setTotalCount(pageResultResponse.getTotalCount());
        response.getPage().setPageNo(pageResultResponse.getPageNo());
        response.getPage().setPageSize(pageResultResponse.getPageSize());
        response.getPage().setOrder(pageResultResponse.getOrder());
        response.getPage().setOrderBy(pageResultResponse.getOrderBy());
        response.getPage().setResult(pageResultResponse.getResult());
        return response;
    }

    public static Map<String, String> filter2Map(List<ListFilter> filterList) {
        Map<String, String> filterMap = null;
        if (filterList != null && filterList.size() > 0) {
            filterMap = new HashMap<>();
            for (ListFilter filter : filterList) {
                if ("tag".equalsIgnoreCase(filter.getKeywordType()) && StringUtils.isNotBlank(filter
                        .getSubKeywordType())) {
                    filterMap.put("tag", filter.getSubKeywordType() + "__" + filter.getKeyword());
                    continue;
                }
                if ("status".equalsIgnoreCase(filter.getKeywordType())) {
                    filter.setKeywordType("instanceStatus");
                }
                filterMap.put(filter.getKeywordType(), filter.getKeyword());
            }
        }
        return filterMap;
    }

    /**
      * 查询修复时间接口
      * 该接口用于查询指定实例的修复时间信息
      * 
      * @param request 请求参数，包含实例ID和类型
      * @return 返回查询结果，包含修复时间详情
      */
    // 查询过保可操作时间详情
    @RequestMapping(value = "quotFindRepairTime", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<InstanceQuotTimeDetailResponse> quotFindRepairTime(
            @IdPermission @Valid @RequestBody DashboardInstanceQuotRepairTimeRequest request) {
        EdpResultResponse<InstanceQuotTimeDetailResponse> response = new EdpResultResponse<>();
        response.setResult(instanceService.quotFindRepairTime(request.getInstanceId(), request.getType(), ""));
        return response;
    }

    /**
     * 修改报价维修时间接口
     * 该接口用于修改报价的维修时间
     * 
     * @param request 修改报价维修时间的请求参数，包含报价ID和维修时间等信息
     * @return EdpResultResponse<Boolean> 返回执行结果，包含是否成功以及错误信息等
     */
    // 设置过保时间
    @RequestMapping(value = "quotChangeRepairTime", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> quotChangeRepairTime(
            @IdPermission @Valid @RequestBody InstanceQuotChangeRequest request) {
        instanceService.quotChangeRepairTime(request);
        return new EdpResultResponse<>();
    }
    /**
      * 发起主备切换
      * 用于处理主备切换的请求，通过传入SwitchMasterBackupRequest对象来执行切换操作
      * 
      * @param request SwitchMasterBackupRequest对象，包含执行主备切换所需的所有参数
      * @return EdpResultResponse<Boolean> 返回执行结果
      * @throws RDSBusinessExceptions.InstanceNotFoundException 如果实例不存在
      * @throws RDSBusinessExceptions.InvalidInstanceStatus 如果实例状态不允许切换
      * @throws RDSBusinessExceptions.InternalServerErrorException 其他内部服务器错误
      */

    @ApiOperation(value = "发起主备切换")
    @RequestMapping(value = "/switchMasterBackup", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> switchMasterBackup(
            @IdPermission @Valid @RequestBody SwitchMasterBackupRequest request) {
        try {
            instanceService.switchMasterBackup(request.getInstanceId(), request, "");
        } catch (BceInternalResponseException e) {
            if ("RDSInstanceNotFound".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceNotFoundException();
            }
            else if ("DbinstanceStateChange".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InvalidInstanceStatus();
            }
            else {
                throw new RDSBusinessExceptions.InternalServerErrorException();
            }
        }
        return new EdpResultResponse<>();
    }
    /**
     * 查看kms列表
     * 接口功能为查看kms列表信息
     * 
     * @param request 请求参数，包含查看kms列表所需信息
     * @return EdpResultResponse<UserKmsListResponse> 返回kms列表查询结果
     */

    @ApiOperation(value = "查看kms列表")
    @RequestMapping(value = "/kms_list", method = RequestMethod.POST)
    public EdpResultResponse<UserKmsListResponse> kmsList(@Valid @RequestBody InstanceOpenTdeRequest request) {
        EdpResultResponse<UserKmsListResponse> response = new EdpResultResponse<>();
        UserKmsListResponse res = instanceService.getKmsList();
        response.setResult(res);
        return response;
    }
    /**
     * 查看kms列表
     * 通过POST请求获取kms列表信息
     * 
     * @param request 请求体，包含请求参数
     * @return EdpResultResponse<UserKmsListResponse> 返回kms列表的响应结果
     */

    @ApiOperation(value = "查看kms列表")
    @RequestMapping(value = "/kms_list_v2", method = RequestMethod.POST)
    public EdpResultResponse<UserKmsListResponse> kmsListV2(@Valid @RequestBody InstanceOpenTdeRequest request) {
        EdpResultResponse<UserKmsListResponse> response = new EdpResultResponse<>();
        UserKmsListResponse res = instanceService.getKmsListV2();
        response.setResult(res);
        return response;
    }
    /**
     * 查看kms列表
     * 该函数用于处理查看kms列表的请求，通过调用服务层获取kms列表信息，并封装成响应对象返回。
     * 
     * @param request 请求参数，包含查看kms列表所需要的信息
     * @return 返回封装了kms列表信息的响应对象
     */

    @ApiOperation(value = "查看kms列表")
    @RequestMapping(value = "/kms_list_v3", method = RequestMethod.POST)
    public EdpResultResponse<UserKmsListResponse> kmsListV3(@Valid @RequestBody InstanceOpenTdeRequest request) {
        EdpResultResponse<UserKmsListResponse> response = new EdpResultResponse<>();
        UserKmsListResponse res = instanceService.getKmsListV3();
        response.setResult(res);
        return response;
    }

    /**
      * 检查是否满足变更条件接口
      * 根据实例ID检查是否满足变更条件，并返回检查结果
      * 
      * @param request 包含实例ID的请求体
      * @return EdpResultResponse对象，包含检查结果
      */
    @RequestMapping(value = "/check_condition", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<StatusResponse> isMeetChangeCondition(@RequestBody InstanceIdRequest request){
        StatusResponse statusResponse = instanceService.checkFdisk(request.getInstanceId());

        EdpResultResponse<StatusResponse> response = new EdpResultResponse<>();
        response.setResult(statusResponse);
        return response;
    }
    /**
      * 执行azone迁移操作
      * 该函数用于处理azone迁移请求，通过传入的azone迁移信息，执行相应的迁移逻辑。
      * 
      * @param request azone迁移请求体，包含迁移所需的参数和配置
      * @return EdpResultResponse<Boolean> 返回迁移操作的结果
      */

    @RequestMapping(value = "azoneMigration", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> azoneMigration(@IdPermission @RequestBody InstanceAzone request) {
        AzoneInfo azoneInfo = new AzoneInfo();
        azoneInfo.setInstanceParameters(request);
        azoneInfo.setEffectiveTime(request.getEffectiveTime());
        instanceService.azoneMigration(request.getInstanceId(), azoneInfo);
        return new EdpResultResponse<>();
    }
    /**
      * 展示概览页面
      * 根据请求中的地区参数，调用服务获取概览页面数据，并返回响应结果
      * 
      * @param viewPageRequest 请求体，包含地区参数，可以为空，为空时默认为"global"
      * @return ViewPageBaseResponse，包含概览页面数据的响应体
      */

    @RequestMapping(value = "/overviewPage", method = RequestMethod.POST)
    @ResponseBody
    public ViewPageBaseResponse showOverviewPage(@RequestBody(required = false) ViewPageRequest viewPageRequest) {
        ViewPageBaseResponse response = new ViewPageBaseResponse();
        String region = viewPageRequest != null ? viewPageRequest.getRegion() : "global";
        ViewPageResponse viewPageResponse = instanceService.showOverviewPageAll(region);
        response.setResult(viewPageResponse);
        return response;

    }
    /**
      * 显示概览页面1
      * 根据请求参数中的地区显示概览页面，如果请求参数中地区为空，则默认显示全球概览页面
      * 
      * @param viewPageRequest 请求参数，包含地区信息
      * @return ViewPageBaseResponse，包含概览页面的结果
      */

    @RequestMapping(value = "/overviewPage1", method = RequestMethod.POST)
    @ResponseBody
    public ViewPageBaseResponse showOverviewPage1(@RequestBody(required = false) ViewPageRequest viewPageRequest) {
        ViewPageBaseResponse response = new ViewPageBaseResponse();
        String region = viewPageRequest != null ? viewPageRequest.getRegion() : "global";
        ViewPageResponse viewPageResponse = instanceService.showOverviewPageAll(region);
        response.setResult(viewPageResponse);
        return response;

    }
    /**
      * 处理实例列表接口
      * 该接口用于接收一个实例ID请求，并返回该实例的处理列表信息。
      * 
      * @param request 包含实例ID的请求体
      * @return 返回处理列表的结果
      */

    @RequestMapping(value = "/processList", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<InstanceProcesslistResponse> processList(@Valid @RequestBody InstanceIdRequest request) {
        EdpResultResponse<InstanceProcesslistResponse> response = new EdpResultResponse<>();
        response.setResult(performanceService.getProcesslist(request.getInstanceId()));
        return response;
    }
    /**
     * 获取实例InnoDB状态
     * 根据实例ID获取实例的InnoDB状态信息
     * 
     * @param request 包含实例ID的请求体
     * @return 包含实例InnoDB状态信息的响应
     */

    @RequestMapping(value = "/instanceInnodb", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<InstanceInnodbStatusResponse> instanceInnodb(
            @Valid @RequestBody InstanceIdRequest request) {
        EdpResultResponse<InstanceInnodbStatusResponse> response = new EdpResultResponse<>();
        response.setResult(performanceService.getInnodbstatus(request.getInstanceId()));
        return response;
    }
    /**
      * 获取实例详情
      * 根据传入的实例ID列表，获取每个实例的详细信息，并返回
      * 
      * @param request 包含实例ID列表的请求体
      * @return 包含实例详细信息的响应体
      */

    @RequestMapping(value = "details", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<InstanceExtensionList> details(
            @IdPermission @Valid @RequestBody InstanceIdListRequest request) {
        InstanceExtensionList instanceExtensionList = new InstanceExtensionList();
        List<InstanceExtension> list = new ArrayList<>();
        for (int i = 0; i < request.getInstanceIds().size(); i++) {
            InstanceExtension instance = instanceService.detail(request.getInstanceIds().get(i), "");
            list.add(i, instance);
        }
        instanceExtensionList.setInstanceExtensionList(list);

        EdpResultResponse<InstanceExtensionList> response = new EdpResultResponse<>();
        response.setResult(instanceExtensionList);
        return response;
    }
    /**
      * 检查热升级接口
      * 用于检查实例的热升级状态
      * 
      * @param request 请求体，包含实例ID等信息
      * @return EdpResultResponse<HotUpgradeResponse> 返回热升级检查结果
      */

    @RequestMapping(value = "/checkHotUpgrade", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<HotUpgradeResponse> checkHotUpgrade(@Valid @RequestBody HotupgradeRequest request) {
        EdpResultResponse<HotUpgradeResponse> response = new EdpResultResponse<>();
        response.setResult(instanceService.checkHotUpgrade(request.getInstanceId(), request));
        return response;
    }
    /**
      * 获取维护时间任务信息
      * 根据请求体中的参数，获取维护时间任务信息，并封装成响应体返回
      * 
      * @param request 请求体，包含获取维护时间任务信息所需的参数
      * @return EdpResultResponse<MaintaintimeTasks> 封装了维护时间任务信息的响应体
      */

    @RequestMapping(value = "/maintaintime/task", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<MaintaintimeTasks> getMaintaintimeTasks(
            @Valid @RequestBody TaskRequest request) {
        MaintaintimeTasks maintaintimeTasks = instanceService.getMaintaintimeTasks(request);
        EdpResultResponse<MaintaintimeTasks> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(maintaintimeTasks);
        return edpResultResponse;
    /**
     * 取消维护时间任务接口
     * 取消维护时间任务的接口，通过传入任务ID进行任务取消操作
     * 
     * @param request 包含任务ID的请求体
     * @return EdpResultResponse<Boolean> 返回操作结果
     */
    }
    @RequestMapping(value = "/maintaintime/task/cancel", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> cancelMaintaintimeTask(@Valid @RequestBody TaskRequest request) {
        instanceService.cancelMaintaintimeTask(request.getTaskId());
        return new EdpResultResponse<>();
    }
    /**
      * 修改任务类型
      * 根据taskId和请求体中的参数修改任务类型
      * 
      * @param taskId 任务ID
      * @param request 修改任务类型的请求体
      * @return 执行结果
      */

    @ApiOperation("修改任务类型")
    @RequestMapping(value = "/maintaintime/task/{taskId}/update", method = RequestMethod.PUT)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateMaintaintimeTasks(
            @PathVariable String taskId,
            @RequestBody UpdateTaskRequest request) {
        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        instanceService.updateMaintaintimeTasks(taskId, request);
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }
    /**
     * 拉取bcm实例组列表
     * 通过serviceName,region,pageNo,pageSize拉取bcm实例组列表
     *
     * @param serviceName 服务名称
     * @param region 区域，可选参数
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @return 返回拉取结果
     * @Deprecated 该接口已标记为过时
     */

    @ApiOperation("拉取 bcm 实例组列表")
    @RequestMapping(value = "/bcmGroupInstanceList", method = RequestMethod.GET)
    @ResponseBody
    @Deprecated
    public EdpResultResponse<BcmGroupResponses> bcmGroupList(
                                                      @RequestParam(value = "serviceName") String serviceName,
                                                      @RequestParam(value = "region", required = false) String region,
                                                      @RequestParam(value = "pageNo") Integer pageNo,
                                                      @RequestParam(value = "pageSize") Integer pageSize) {
        EdpResultResponse<BcmGroupResponses> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(instanceService.bcmGroupList(serviceName, region, pageNo, pageSize));
        return resultResponse;
    }

    @ApiOperation("更新本地保留策略")
    @RequestMapping(value = "/{instanceId}/updateLocalBinlogPolicy", method = RequestMethod.PUT)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateLocalBinlogPolicy(
            @PathVariable @IdPermission String instanceId,
            @RequestBody UpdateLoaclPolicyRequest request) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.updateLocalBinlogPolicy(instanceId, request);
        resultResponse.setResult(true);
        return resultResponse;
    }

    @ApiOperation("拉取 proxy 拓扑列表")
    @RequestMapping(value = "/proxy/topoInfo", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<ProxyTopoInfo> proxyTopoInfo(
            @RequestParam(value = "instanceId") @IdPermission String instanceId) {
        EdpResultResponse<ProxyTopoInfo> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(instanceService.proxyTopoInfo(instanceId));
        return resultResponse;
    }

}
