package com.baidu.bce.logic.rds.model;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by luping03 on 18/1/25.
 */
public class HomeStatisticResponse {

    private List<HomeStatisticInfo> result = new ArrayList<HomeStatisticInfo>();

    public void addStatisticInfo(String key, String value, String name) {
        HomeStatisticInfo info = new HomeStatisticInfo();
        info.setKey(key);
        info.setValue(value);
        info.setName(name);
        result.add(info);
    }

    public List<HomeStatisticInfo> getResult() {
        return result;
    }

    public void setResult(List<HomeStatisticInfo> result) {
        this.result = result;
    }

    public static class HomeStatisticInfo {
        private String key;

        private String value;

        private String name;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }


}
