package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceInnodbStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceProcesslistResponse;
import com.baidu.bce.internalsdk.rds.model.performance.ConnectionListResponse;
import com.baidu.bce.internalsdk.rds.model.performance.KillProcessRequest;
import com.baidu.bce.internalsdk.rds.model.performance.TransactionListResponse;
import com.baidu.bce.logic.rds.service.PerformanceService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/v1/instance/{instanceId}/performance")
public class OpenApiPerformanceController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiPerformanceController.class);

    @Autowired
    private PerformanceService performanceService;
    /**
     * 查询processlist快照数据
     * 通过instanceId获取RDS的processlist快照数据
     * 
     * @param instanceId 实例ID
     * @return InstanceProcesslistResponse 实例processlist快照数据
     */

    @ApiOperation(value = "查询processlist快照数据")
    @RequestMapping(value = "/processlist", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public InstanceProcesslistResponse getProcesslist(@IdPermission @PathVariable String instanceId) {
        InstanceProcesslistResponse response = new InstanceProcesslistResponse();
        try {
            response = performanceService.getProcesslist(instanceId);
        } catch (Exception e) {
            LOGGER.error("get instance processlist error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查询innodbstatus快照数据
      * 通过instanceId查询innodbstatus快照数据
      *
      * @param instanceId 实例ID
      * @return 查询结果
      */

    @ApiOperation(value = "查询innodbstatus快照数据")
    @RequestMapping(value = "/innodbstatus", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public InstanceInnodbStatusResponse getInnodbstatus(@IdPermission @PathVariable String instanceId) {
        InstanceInnodbStatusResponse response = new InstanceInnodbStatusResponse();
        try {
            response = performanceService.getInnodbstatus(instanceId);
        } catch (Exception e) {
            LOGGER.error("get instance innodbstatus error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    /**
      * 终止会话
      * 根据实例ID和请求体，终止指定的会话过程
      * 
      * @param instanceId 实例ID
      * @param request 终止会话的请求体
      */
    @ApiOperation(value = "Kill会话")
    @RequestMapping(value = "/process/kill", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void killProcess(@IdPermission @PathVariable String instanceId,
                            @Valid @RequestBody KillProcessRequest request) {
        try {
            performanceService.killProcess(instanceId, request);
        } catch (Exception e) {
            LOGGER.error("kill instance process error", e);
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 事务列表
      * 获取指定实例的事务列表信息
      * 
      * @param instanceId 实例ID
      * @return 事务列表响应对象
      */

    @ApiOperation(value = "事务列表")
    @RequestMapping(value = "/transaction", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public TransactionListResponse transactionList(@IdPermission @PathVariable String instanceId) {
        TransactionListResponse response = new TransactionListResponse();
        try {
            response = performanceService.getTransactionList(instanceId);
        } catch (Exception e) {
            LOGGER.error("get instance performance transaction list error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 连接列表
      * 获取实例的连接列表信息
      * 
      * @param instanceId 实例ID
      * @return 连接列表响应对象
      */

    @ApiOperation(value = "连接列表")
    @RequestMapping(value = "/connection", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ConnectionListResponse connectionList(@IdPermission @PathVariable String instanceId) {
        ConnectionListResponse response = new ConnectionListResponse();
        try {
            response = performanceService.getConnectionList(instanceId);
        } catch (Exception e) {
            LOGGER.error("get instance performance connection list error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
}
