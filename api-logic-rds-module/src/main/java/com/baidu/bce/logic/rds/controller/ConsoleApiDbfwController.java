package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DashboardDbfwListRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DashboardDbfwState;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DashboardDbfwUpdateRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DashboardSqlInjectRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DashboardSqlWhiteAddRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DashboardSqlWhiteDeleteRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwModifyStateRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwState;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwStateGetResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.ListFilter;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInject;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectDetail;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectGetResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteDetail;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteListCreateRequest;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.model.group.InstanceIdRequest;
import com.baidu.bce.logic.rds.service.DbFireWallService;
import com.baidu.bce.logic.rds.service.model.LogicCommonListRequest;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * Created by liuruisen on 2017/11/8.
 */
@RestController
@RequestMapping(value = "/api/rds/dbfirewall")
@Api("RDS Dashboard 数据库防火墙管理API")
public class ConsoleApiDbfwController {

    @Autowired
    private DbFireWallService dbFireWallService;

    // 与FE商量防火墙状态dbfwState拆分为两个字段 dbfwStatus、dbfwRule
    private DashboardDbfwState convertToDashboardDbfwState(DbfwState state) {
        DashboardDbfwState dbfwState = new DashboardDbfwState();
        if (state != null) {
            dbfwState.setAppId(state.getAppId());
            dbfwState.setId(state.getId());
            dbfwState.setUpdateTime(state.getUpdateTime());
            dbfwState.setDbfwRule(state.getDbfwState());
            if (state.getDbfwState() <= 0) {
                dbfwState.setDbfwStatus(false);
            } else {
                dbfwState.setDbfwStatus(true);
            }
        }
        return dbfwState;
    }

    /**
     * 获取某代理实例的数据库防火墙状态
     * 获取某代理实例的数据库防火墙状态
     *
     * @param request 包含代理实例ID的请求参数
     * @return 包含数据库防火墙状态的响应结果
     */
    @ApiOperation("某代理实例的数据库防火墙状态")
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<DashboardDbfwState> dbfwDetail(
            @IdPermission @Valid @RequestBody InstanceIdRequest request) {
        EdpResultResponse<DashboardDbfwState> edpResultResponse = new EdpResultResponse<>();
        DbfwStateGetResponse dbfwStateGetResponse = dbFireWallService.dbfwState(request.getInstanceId());
        // 是否会有为空的情况：跟后端确认后告知不会有空的返回值
        if (dbfwStateGetResponse != null) {
            edpResultResponse.setResult(convertToDashboardDbfwState(dbfwStateGetResponse.getDbfwState()));
        }
        return edpResultResponse;
    }

    /**
     * 修改数据库防火墙状态
     * 修改数据库防火墙状态
     *
     * @param request 数据库防火墙更新请求参数
     * @return 返回修改是否成功的布尔值响应
     */
    @ApiOperation("修改数据库防火墙状态")
    @RequestMapping(value = "/update_state", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> dbfwStateModify(
            @IdPermission @Valid @RequestBody DashboardDbfwUpdateRequest request) {
        DbfwModifyStateRequest dbfwModifyStateRequest = new DbfwModifyStateRequest();
        dbfwModifyStateRequest.setDbfwState(request.getDbfwStatus() ? request.getDbfwRule() : 0);
        dbFireWallService.updateState(request.getInstanceId(), dbfwModifyStateRequest);
        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }

    private LogicCommonListRequest composeLogicListRequest(DashboardDbfwListRequest dashboardRequest) {
        LogicCommonListRequest request = new LogicCommonListRequest();
        if (dashboardRequest != null) {
            request.setOrder(dashboardRequest.getOrder());
            request.setOrderBy(dashboardRequest.getOrderBy());
            request.setPageNo(dashboardRequest.getPageNo());
            request.setPageSize(dashboardRequest.getPageSize());
            List<ListFilter> filters = dashboardRequest.getFilters();
            if (filters != null) {
                request.setFilters(convertToJson(filters));
            }
        }
        return request;
    }

    private String convertToJson(Object object) {
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonStr = null;
        try {
            jsonStr = objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return jsonStr;
    }

    /**
     * SQL注入列表
     * 获取SQL注入列表
     *
     * @param request 包含实例ID和分页请求参数的DashboardDbfwListRequest对象
     * @return 包含SQL注入列表的分页响应结果EdpPageResultResponse对象
     */
    @ApiOperation("SQL注入列表")
    @RequestMapping(value = "/sqlinject/list", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpPageResultResponse<SqlInject> sqlInjectList(
            @IdPermission @Valid @RequestBody DashboardDbfwListRequest request) {
        LogicCommonListRequest logicCommonListRequest = composeLogicListRequest(request);
        LogicPageResultResponse<SqlInject> logicPageResultResponse =
                dbFireWallService.sqlInjectList(request.getInstanceId(), logicCommonListRequest);
        EdpPageResultResponse<SqlInject> edpPageResultResponse = new EdpPageResultResponse<>();
        edpPageResultResponse.setPage(new EdpPageResultResponse.Page<SqlInject>());
        edpPageResultResponse.getPage().setOrder(logicPageResultResponse.getOrder());
        edpPageResultResponse.getPage().setOrderBy(logicPageResultResponse.getOrderBy());
        edpPageResultResponse.getPage().setPageNo(logicPageResultResponse.getPageNo());
        edpPageResultResponse.getPage().setPageSize(logicPageResultResponse.getPageSize());
        edpPageResultResponse.getPage().setResult(logicPageResultResponse.getResult());
        edpPageResultResponse.getPage().setTotalCount(logicPageResultResponse.getTotalCount());
        return edpPageResultResponse;
    }

    /**
     * 单条SQL注入详情
     * 获取单条SQL注入详情
     *
     * @param request SQL注入详情请求参数
     * @return 包含SQL注入详情的响应结果
     */
    @ApiOperation("单条sql注入详情")
    @RequestMapping(value = "/sqlinject/detail", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SqlInjectDetail> sqlInjectDetail(
            @IdPermission @Valid @RequestBody DashboardSqlInjectRequest request) {
        SqlInjectGetResponse sqlInjectGetResponse =
                dbFireWallService.sqlInjectDetail(request.getInstanceId(), request.getSqlId());
        SqlInjectDetail sqlInjectDetail = sqlInjectGetResponse.getSqlInjectDetail();
        EdpResultResponse<SqlInjectDetail> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(sqlInjectDetail);
        return edpResultResponse;
    }

    /**
     * sql白名单列表
     * 获取sql白名单列表
     *
     * @param request 包含实例ID和分页请求参数的DashboardDbfwListRequest对象
     * @return 包含sql白名单列表的分页响应结果EdpPageResultResponse对象
     */
    @ApiOperation("sql白名单列表")
    @RequestMapping(value = "/sqlwhite/list", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpPageResultResponse<SqlWhiteDetail> sqlWhiteList(
            @IdPermission @Valid @RequestBody DashboardDbfwListRequest request) {
        LogicCommonListRequest logicCommonListRequest = composeLogicListRequest(request);
        LogicPageResultResponse<SqlWhiteDetail> logicPageResultResponse =
                dbFireWallService.sqlWhiteList(request.getInstanceId(), logicCommonListRequest);
        EdpPageResultResponse<SqlWhiteDetail> edpPageResultResponse = new EdpPageResultResponse<>();
        edpPageResultResponse.setPage(new EdpPageResultResponse.Page<SqlWhiteDetail>());
        edpPageResultResponse.getPage().setOrder(logicPageResultResponse.getOrder());
        edpPageResultResponse.getPage().setOrderBy(logicPageResultResponse.getOrderBy());
        edpPageResultResponse.getPage().setPageNo(logicPageResultResponse.getPageNo());
        edpPageResultResponse.getPage().setPageSize(logicPageResultResponse.getPageSize());
        edpPageResultResponse.getPage().setTotalCount(logicPageResultResponse.getTotalCount());
        edpPageResultResponse.getPage().setResult(logicPageResultResponse.getResult());
        return edpPageResultResponse;
    }

    /**
     * 添加SQL白名单
     * 添加SQL白名单
     *
     * @param request SQL白名单添加请求参数
     * @return 添加结果，成功返回true，失败返回false
     */
    @ApiOperation("添加SQl白名单")
    @RequestMapping(value = "sqlwhite/create", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> sqlWhiteCreate(
            @IdPermission @Valid @RequestBody DashboardSqlWhiteAddRequest request) {
        SqlWhiteListCreateRequest sqlWhiteListCreateRequest = new SqlWhiteListCreateRequest();
        sqlWhiteListCreateRequest.setDbName(request.getDbName());
        sqlWhiteListCreateRequest.setSqlMd5(request.getSqlMd5());
        sqlWhiteListCreateRequest.setSqlFingerprint(request.getSqlFingerprint());
        dbFireWallService.sqlWhiteListAdd(request.getInstanceId(), sqlWhiteListCreateRequest);

        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }

    /**
     * 删除SQL白名单
     * 删除SQL白名单
     *
     * @param request 包含实例ID和SQL白名单MD5值的DashboardSqlWhiteDeleteRequest对象
     * @return 返回删除是否成功的布尔值响应结果EdpResultResponse对象
     */
    @ApiOperation("删除SQL白名单")
    @RequestMapping(value = "sqlwhite/delete", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> sqlWhiteDelete(
            @IdPermission @Valid @RequestBody DashboardSqlWhiteDeleteRequest request) {
        dbFireWallService.sqlWhiteListDelete(request.getInstanceId(), request.getSqlMd5());

        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }
}
