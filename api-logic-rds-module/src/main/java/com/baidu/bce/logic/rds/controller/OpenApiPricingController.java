package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.logic.rds.service.PricingService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.pricing.Price;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;


import javax.validation.Valid;

/**
 * Created by luping03 on 17/11/7.
 */
@RestController
@RequestMapping("/v1/instance/price")
public class OpenApiPricingController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiPricingController.class);

    @Autowired
    private PricingService pricingService;
    
    /**
     * 获取价格
     * 通过POST请求获取指定实例的价格
     * 
     * @param request 实例创建模型，包含获取价格所需的参数
     * @param from 请求来源，默认为空字符串，函数内部将其置为"api"
     * @return 价格对象
     */
    @ApiOperation(value = "获取价格")
    @RequestMapping(value = "", method = RequestMethod.POST)
    public Price getPrice(@RequestBody @Valid InstanceCreateModel request,
                          @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get price.");
        from = "api";
        Price price = null;
        try {
            price = pricingService.getPriceV2(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return price;
    }
    /**
     * 获取预付费的变配差价
     * 根据请求中的参数计算预付费的变配差价
     * 
     * @param request 变配差价请求参数
     * @param from 请求来源（可选参数）
     * @return 计算得到的预付费变配差价
     */

    @ApiOperation(value = "获取预付费的变配差价")
    @RequestMapping(value = "/diff", method = RequestMethod.POST)
    public Price getPriceDiff(@RequestBody @IdMapper @Valid PriceDiffModel request,
                              @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get price.");
        Price price = null;
        try {
            price = pricingService.getPriceDiff(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return price;
    }

}
