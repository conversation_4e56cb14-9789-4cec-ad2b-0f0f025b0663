package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.AccountListResponse;
import com.baidu.bce.internalsdk.rds.model.account.V2AccountListResponse;
import com.baidu.bce.internalsdk.rds.model.account.V2AccountUpdatePrivilegesRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.instance.AccountShowResponse;
import com.baidu.bce.logic.rds.service.AccountService;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.V2AccountService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/v2/rds/instances/{instanceId}/accounts")
public class V2AccountController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private AccountService accountService;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private V2AccountService v2AccountService;

    
    /**
     * 获取帐号列表
     * 根据实例ID和起始位置获取帐号列表信息
     * 
     * @param instanceId 实例ID
     * @param from 起始位置
     * @return 帐号列表响应对象
     */    
    @ApiOperation(value = "获取帐号列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public V2AccountListResponse getList(@IdPermission @IdMapper @PathVariable String instanceId,
                                         @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get account list. instanceId is {}", instanceId);
        V2AccountListResponse response = null;
        try {
            response = v2AccountService.list(instanceId, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 创建帐号
      * 创建帐号的接口，支持POST请求方法，通过instanceId、request、from、ak参数进行帐号创建
      * 
      * @param instanceId 实例ID
      * @param request 帐号请求体
      * @param from 来源，非必须
      * @param httpServletRequest HttpServletRequest对象，用于获取请求头中的ak信息
      * @throws 无
      */

    @ApiOperation(value = "创建帐号")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void create(@IdPermission @IdMapper @PathVariable String instanceId,
                       @Valid @RequestBody Account request,
                       @RequestParam(required = false, defaultValue = "") String from,
                       HttpServletRequest httpServletRequest) {
        LOGGER.debug("create account. instanceId is {}", instanceId);
        AccountListResponse response = null;
        try {
            String ak = httpServletRequest.getHeader(RDSConstant.X_BCE_ACCESS_KEY);
            v2AccountService.create(instanceId, request, from, ak);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 查看账户详情权限
      * 根据instanceId, accountName, from等参数获取账户详情
      * 
      * @param instanceId 实例ID
      * @param accountName 账户名称
      * @param from 来源
      * @return 账户详情
      */

    @ApiOperation(value = "查看账户详情权限")
    @RequestMapping(value = "/{accountName}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public AccountShowResponse detail(@IdPermission @IdMapper @PathVariable String instanceId,
                                      @PathVariable String accountName,
                                      @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create account. instanceId: {}, accountName: {}", instanceId, accountName);
        AccountShowResponse response = null;
        try {
            response = v2AccountService.detail(instanceId, accountName, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 修改权限
     * 根据请求更新特定实例的权限信息
     * 
     * @param instanceId 实例ID，用于标识需要修改权限的实例
     * @param request 包含权限更新信息的请求体
     * @param from 请求来源，默认为空字符串
     */

    @ApiOperation(value = "修改权限")
    @RequestMapping(value = "/{accountName}/privileges", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePrivileges(@IdPermission @IdMapper @PathVariable String instanceId,
                                 @RequestBody @Valid V2AccountUpdatePrivilegesRequest request,
                                 @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. instanceId: {}, accountName: {}", instanceId, request.getAccountName());
        try {
            v2AccountService.updatePrivileges(instanceId, request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 删除帐号
     * 根据实例ID和帐号名称删除指定的帐号
     * 
     * @param instanceId 实例ID
     * @param accountName 帐号名称
     * @param from 来源
     */

    @ApiOperation(value = "删除帐号")
    @RequestMapping(value = "/{accountName}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void delete(@IdPermission @PathVariable String instanceId,
                       @PathVariable String accountName,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. instanceId: {}, accountName: {}", instanceId, accountName);
        try {
            accountService.deleteAccount(instanceId, accountName, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 修改密码
      * 更新指定accountName用户的密码
      *
      * @param instanceId 实例ID
      * @param accountName 账户名称
      * @param pwRequest 密码更新请求体
      * @param from 请求来源
      */

    @ApiOperation(value = "修改密码")
    @RequestMapping(value = "/{accountName}/password", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePW(@IdPermission @PathVariable String instanceId,
                         @PathVariable String accountName,
                         @RequestBody @Valid AccountUpdatePasswordRequest pwRequest,
                         @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update password. instanceId: {}, accountName: {}", instanceId, accountName);
        try {
            v2AccountService.updatePW(instanceId, accountName, pwRequest, "", null);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
}
