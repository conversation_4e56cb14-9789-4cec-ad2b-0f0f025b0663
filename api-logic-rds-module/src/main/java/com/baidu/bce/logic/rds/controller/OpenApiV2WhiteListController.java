package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.idempotent.annotation.Idempotent;
import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import com.baidu.bce.internalsdk.rds.model.security.SecurityIpPutRequest;
import com.baidu.bce.internalsdk.rds.model.security.V2ListRequest;
import com.baidu.bce.internalsdk.rds.model.security.V2SecurityIpResponse;
import com.baidu.bce.logic.rds.service.V2WhiteListService;

import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestHeader;


import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * Created by luping03 on 17/11/3.
 */
@RestController
@RequestMapping("/v2/instance")
public class OpenApiV2WhiteListController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private V2WhiteListService v2whiteListService;
    /**
      * 查看白名单
      * 获取指定RDS实例的白名单信息
      * 
      * @param instanceId RDS实例ID
      * @param requestBody 请求体，包含分页和过滤条件
      * @param servletResponse HttpServletResponse对象，用于设置响应头
      * @return 返回白名单信息的响应对象
      */

    @ApiOperation(value = "查看白名单")
    @RequestMapping(value = "/{instanceId}/securityIp", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public V2SecurityIpResponse getWhiteList(@IdPermission @IdMapper @PathVariable String instanceId,
                                             @RequestBody @IdMapper @Valid V2ListRequest requestBody,
                                             HttpServletResponse servletResponse) {
        LOGGER.debug("get whitelist.");
        V2SecurityIpResponse response = null;
        try {
            response = v2whiteListService.getWhiteList(instanceId, requestBody);
            servletResponse.setHeader("ETag", response.getETag());

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 更新已有分组白名单
      * 该函数用于更新指定实例ID的分组白名单
      * 
      * @param instanceId 实例ID
      * @param request 白名单请求体
      * @param etag 请求头中的etag值
      * @param from 请求来源
      */

    @ApiOperation(value = "更新已有分组白名单")
    @RequestMapping(value = "/{instanceId}/securityIp", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void setWhiteList(@IdPermission @IdMapper @PathVariable String instanceId,
                             @RequestBody @Valid SecurityIpPutRequest request,
                             @RequestHeader ("x-bce-if-match") String etag,
                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("set whitelist.");
        try {
            request.setETag(etag);
            v2whiteListService.setWhiteList(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 新增分组白名单
      * 该函数用于新增一个分组白名单。
      * 
      * @param instanceId 实例ID
      * @param request 包含白名单信息的请求体
      * @param from 请求来源，默认为空字符串
      */

    @ApiOperation(value = "新增分组白名单")
    @RequestMapping(value = "/{instanceId}/securityIp/new", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void setWhiteListNew(@IdPermission @IdMapper @PathVariable String instanceId,
                             @RequestBody @Valid SecurityIpPutRequest request,
                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("set whitelist.");
        try {
            v2whiteListService.setWhiteListNew(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 删除/清空分组白名单
      * 删除或清空指定实例ID的分组白名单
      * 
      * @param instanceId 实例ID
      * @param request 分组白名单请求体
      * @param etag 资源匹配串，用于乐观锁控制
      * @param from 请求来源标识
      */

    @ApiOperation(value = "删除/清空分组白名单")
    @RequestMapping(value = "/{instanceId}/securityIp/delete", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void deleteWhiteList(@IdPermission @IdMapper @PathVariable String instanceId,
                                @RequestBody @Valid SecurityIpPutRequest request,
                                @RequestHeader ("x-bce-if-match") String etag,
                                @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("set whitelist.");
        try {
            request.setETag(etag);
            v2whiteListService.deleteWhiteList(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

}
