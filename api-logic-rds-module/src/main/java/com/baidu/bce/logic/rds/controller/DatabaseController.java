package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.database.Database;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCheckExistRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseChecksizeResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListV2Response;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.database.TableListResponse;
import com.baidu.bce.logic.rds.service.DatabaseService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.model.IsExistResponse;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Created by luping03 on 17/10/10.
 */
@RestController
@RequestMapping("/v1/rds/instances/{instanceId}/databases")
public class DatabaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(DatabaseController.class);

    @Autowired
    private DatabaseService databaseService;
    
    /**
     * 获取数据库列表
     * 获取指定instanceId的数据库列表，可以指定来源from
     * 
     * @param instanceId 实例ID
     * @param from 来源，非必填，默认值为空字符串
     * @return 数据库列表响应
     */            
    @ApiOperation(value = "获取数据库列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public DatabaseListResponse getList(@IdPermission @PathVariable String instanceId,
                                        @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get database list. instanceId: {}", instanceId);
        DatabaseListResponse response = null;
        try {
            response = databaseService.list(instanceId, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    
    /**
     * 创建数据库
     * 创建数据库的接口，支持从指定实例创建数据库
     * 
     * @param instanceId 实例ID
     * @param database 数据库对象
     * @param from 来源标识，非必须
     */    
    @ApiOperation(value = "创建数据库")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void create(@IdPermission @PathVariable String instanceId,
                       @Valid @RequestBody Database database,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create database. instanceId: {}", instanceId);
        try {
            databaseService.create(instanceId, database, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 修改描述信息
      * 根据实例ID和数据库名称，修改数据库的描述信息。如果请求来源不是API，则使用请求体中的数据库名称。
      *
      * @param instanceId 实例ID
      * @param dbName 数据库名称
      * @param remarkRequest 修改描述信息的请求体
      * @param from 请求来源，非API时则使用请求体中的数据库名称
      */

    @ApiOperation(value = "修改描述信息")
    @RequestMapping(value = "/{dbName}/remark", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateRemark(@IdPermission @PathVariable String instanceId,
                             @PathVariable String dbName,
                             @RequestBody @Valid DatabaseUpdateRemarkRequest remarkRequest,
                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update remark. instanceId: {},dbName: {}", instanceId, dbName);
        try {
            if (!RDSConstant.FROM_API.equals(from)) {
                dbName = remarkRequest.getDbName();
            }
            databaseService.updateRemark(instanceId, dbName, remarkRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 删除数据库操作
      * 根据instanceId和dbName删除指定的数据库，如果from不是来自API，则使用request中的dbName
      * 
      * @param instanceId 数据库实例ID
      * @param dbName 数据库名称
      * @param databaseUpdateRemarkRequest 包含数据库名称等信息的请求体
      * @param from 请求来源标识
      */

    @ApiOperation(value = "删除数据库")
    @RequestMapping(value = "/{dbName}", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void delete(@IdPermission @PathVariable String instanceId,
                       @PathVariable String dbName,
                       @RequestBody DatabaseUpdateRemarkRequest databaseUpdateRemarkRequest,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. instanceId: {},dbName: {}", instanceId, dbName);
        try {
            if (!RDSConstant.FROM_API.equals(from)) {
                dbName = databaseUpdateRemarkRequest.getDbName();
            }
            databaseService.delete(instanceId, dbName);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 查询数据库是否已存在接口
     * 通过传入的instanceId、dbName和可选的from参数，查询数据库是否已存在
     * 
     * @param instanceId 实例ID，用于标识具体的数据库实例
     * @param dbName 数据库名称，用于查询指定数据库是否存在
     * @param databaseUpdateRemarkRequest 数据库更新备注请求，包含数据库名称等信息
     * @param from 来源标识，用于判断调用来源，可选参数，默认为空字符串
     * @return 数据库是否存在的响应结果
     */

    @ApiOperation(value = "查询数据库是否已存在")
    @RequestMapping(value = "/{dbName}/isExist", method = RequestMethod.GET)
    public IsExistResponse check(@PathVariable String instanceId,
                                 @PathVariable String dbName,
                                 @RequestBody DatabaseUpdateRemarkRequest databaseUpdateRemarkRequest,
                                 @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. instanceId: {},dbName: {}", instanceId, dbName);
        IsExistResponse response = null;
        try {
            if (!RDSConstant.FROM_API.equals(from)) {
                dbName = databaseUpdateRemarkRequest.getDbName();
            }
            response = databaseService.databaseCheck(instanceId, dbName);

        } catch (Exception ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return response;
    }
    /**
     * 获取实例下的库/表及表大小
     * 根据实例ID和可选的起始库名，查询该实例下的库/表及表的大小信息
     * 
     * @param instanceId 实例ID
     * @param from 起始库名，可选参数，默认为空字符串
     * @return 查询结果，封装在DatabaseChecksizeResponse对象中
     */

    @ApiOperation(value = "获取实例下的库/表及表大小")
    @RequestMapping(value = "/checksize", method = RequestMethod.GET)
    public DatabaseChecksizeResponse checksize(@PathVariable String instanceId,
                                               @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("checksize. instanceId: {},dbName: {}", instanceId);
        DatabaseChecksizeResponse response = null;
        try {
            response = databaseService.checksize(instanceId);

        } catch (Exception ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return response;
    }
    /**
     * 获取实例数据库信息
     * 获取实例的数据占用的磁盘空间、剩余磁盘空间和数据库大小
     * 
     * @param instanceId 实例ID
     * @param from 起始位置，用于分页，可选参数，默认为空字符串
     * @return 数据库信息列表
     */

    @ApiOperation(value = "获取实例的数据占用的磁盘空间、剩余磁盘空间和数据库大小")
    @RequestMapping(value = "/listdatabases", method = RequestMethod.GET)
    public DatabaseListV2Response listdatabases(@PathVariable String instanceId,
                                               @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("checksize. instanceId: {},dbName: {}", instanceId);
        DatabaseListV2Response response = null;

        response = databaseService.listdatabases(instanceId);

        return response;
    }

    /**
     * 获取实例指定库下满足条件的表的大小
     * 根据实例ID、模式、数据库名来获取满足条件的表的大小信息
     * 
     * @param instanceId 实例ID
     * @param pattern 模式，可选参数，默认为空字符串
     * @param dbName 数据库名，可选参数，默认为空字符串
     * @param from 起始位置，可选参数，默认为空字符串，此处未使用
     * @return 返回满足条件的表的大小信息
     */
    @ApiOperation(value = "获取实例指定库下满足条件的表的大小")
    @RequestMapping(value = "/listtables", method = RequestMethod.GET)
    public TableListResponse listtables(@PathVariable String instanceId,
                                           @RequestParam(required = false, defaultValue = "") String pattern,
                                           @RequestParam(required = false, defaultValue = "") String dbName,
                                              @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("checksize. instanceId: {},dbName: {}", instanceId);
        TableListResponse response = null;

        response = databaseService.listtables(instanceId, pattern, dbName);

        return response;
    }
    /**
     * 获取实例指定库下满足条件的表的大小
     * 根据实例ID和请求参数，查询数据库是否存在满足条件的表，并返回表的大小信息
     * 
     * @param instanceId 实例ID
     * @param request 请求参数，包含数据库名和表大小条件
     * @return 数据库检查存在响应对象，包含表的大小信息
     */

    @ApiOperation(value = "获取实例指定库下满足条件的表的大小")
    @RequestMapping(value = "/checkdbexist", method = RequestMethod.POST)
    public DatabaseCheckExistResponse checkdbexist(@PathVariable String instanceId,
                                          @RequestBody @Valid DatabaseCheckExistRequest request) {
        LOGGER.debug("checkdbexist. instanceId: {},dbName: {}", instanceId);
        DatabaseCheckExistResponse response = null;

        response = databaseService.checkdbexist(instanceId, request);

        return response;
    }


}
