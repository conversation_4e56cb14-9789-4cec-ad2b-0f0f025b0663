package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.console.order.model.UuidRequest;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.iam.facade.model.bcepass.session.BceSessionContext;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.logic.rds.model.RdsDetailBuilder;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.RdsOrderService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.model.OrderId;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.instance.LogicInstanceCreateResponse;
import com.baidu.bce.logic.rds.service.model.order.CancelToPostpayOrderRequest;
import com.baidu.bce.logic.rds.service.model.order.RdsCreateOrderRequestVo;
import com.baidu.bce.logic.rds.service.model.order.RenewModel;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.PaymentModel;
import com.baidu.bce.plat.webframework.authentication.service.SessionService;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

@RestController
@RequestMapping("/api/rds/order/v2")
public class ConsoleApiOrderControllerV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConsoleApiOrderControllerV2.class);

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private LogicRdsClientFactory rdsClientFactory;

    @Autowired
    private RdsOrderService orderService;


    /**
     * 变配接口，支持批量操作
     * 变配接口，用于支持批量变配操作
     *
     * @param request 变配请求参数
     * @param from    来源标识
     * @return 返回变配操作后的订单ID
     */
    @ApiOperation(value = "变配：支持批量")
    @RequestMapping(value = "/confirm", method = RequestMethod.POST, params = {"orderType=RESIZE"})
    public EdpResultResponse<OrderId> confirmResizeOrder(
            @RequestBody RdsCreateOrderRequestVo<PriceDiffModel> request,
            @RequestParam(required = false, defaultValue = "") String from) {
        from = RDSConstant.FROM_CONSOLE;
        PriceDiffModel priceDiffModel = request.getItems().get(0).getConfig();
        // 此处需兼容主实例、只读实例同时变配的情况
        if (CollectionUtils.isEmpty(request.getItems().get(0).getConfig().getReadReplicas())) {
            // 此处为单个变配的逻辑
            OrderUuidResult result;
            result = instanceService.resizeInstance(request.getItems().get(0).getConfig().getInstanceId(), request, from);
            return new EdpResultResponse<OrderId>().withResult(new OrderId().withId(result.getOrderId()));
        } else {
            // 批量变配
            LOGGER.debug("batch resize, priceDiffModel.getReadReplicas() : {}", priceDiffModel.getReadReplicas().size());
            request.setPaymentMethod(new HashSet<PaymentModel>(0));
            // 此处需保证 Item 项的大小等于主+只读的列表大小
            int initCapacity = priceDiffModel.getReadReplicas().size() + 1;
            request.setItems(new ArrayList<RdsCreateOrderRequestVo.Item<PriceDiffModel>>(initCapacity));
            // 先放主动发起的变配实例
            request.getItems().add(new RdsCreateOrderRequestVo.Item<PriceDiffModel>());
            request.getItems().get(0).setPaymentMethod(new HashSet<PaymentModel>(0));
            request.getItems().get(0).setConfig(priceDiffModel);
            priceDiffModel.setMemoryCapacity(priceDiffModel.getAllocatedMemoryInGB());
            priceDiffModel.setVolumeCapacity(priceDiffModel.getAllocatedStorageInGB());
            // 这条不能缺
            priceDiffModel.setInstanceId( request.getItems().get(0).getConfig().getInstanceId());
            // 再放发起的从实例变配（目前只支持只读实例）
            for (int i = 1; i < priceDiffModel.getReadReplicas().size() + 1; i++) {
                LOGGER.debug("readReplica......... {} ", priceDiffModel.getReadReplicas().size());
                request.getItems().add(new RdsCreateOrderRequestVo.Item<PriceDiffModel>());
                request.getItems().get(i).setPaymentMethod(new HashSet<PaymentModel>(0));
                request.getItems().get(i).setConfig(priceDiffModel.getReadReplicas().get(i - 1));
                request.getItems().get(i).getConfig().setAllocatedMemoryInGB
                        (request.getItems().get(i).getConfig().getMemoryCapacity());
                request.getItems().get(i).getConfig().setAllocatedStorageInGB
                        (request.getItems().get(i).getConfig().getVolumeCapacity());
            }

            LOGGER.debug("resize instance from console.");
            List<Order> orders = new ArrayList<>();

            try {
                // PATH上为主实例ID
                orders = instanceService.resizeBatchInstance(priceDiffModel.getInstanceId(), request, from);
            } catch (Exception e) {
                LogicRdsExceptionHandler.handle(e);
            }
            List<String> orderIds = new ArrayList<>(orders.size());

            // 只给用户返回订单 id
            for (Order order : orders) {
                orderIds.add(order.getUuid());
            }

            StringBuilder stringBuilder = new StringBuilder();
            for (String id : orderIds) {
                stringBuilder.append(id).append(",");
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);

            return new EdpResultResponse<OrderId>().withResult(new OrderId().withId(stringBuilder.toString()));
        }

    }
    /**
      * 获取订单详情
      * 通过传入的UuidRequest请求体，调用order service获取订单详情，并兼容从iamService中获取stsCredential
      * 
      * @param request 包含uuid的请求体
      * @return 订单详情的响应结果
      */

    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public EdpResultResponse<Order> orderDetail(@RequestBody UuidRequest request) {
        // 兼容order service从iamService中获取stsCredential
        StsCredential stsCredential = rdsClientFactory.getUserStsAccessKey();
        com.baidu.bce.iam.facade.model.sts.StsCredential iamStsCredential =
                new com.baidu.bce.iam.facade.model.sts.StsCredential();
        iamStsCredential.setAccessKeyId(stsCredential.getAccessKeyId());
        iamStsCredential.setSecretAccessKey(stsCredential.getSecretAccessKey());
        iamStsCredential.setSessionToken(stsCredential.getSessionToken());
        iamStsCredential.setUserId(stsCredential.getUserId());
        iamStsCredential.setRoleId(stsCredential.getRoleId());
        iamStsCredential.setExpiration(stsCredential.getExpiration());
        iamStsCredential.setCreateTime(stsCredential.getCreateTime());
        BceSessionContext bceSessionContext = new BceSessionContext();
        bceSessionContext.setStsCredential(iamStsCredential);
        SessionService.setSessionContext(bceSessionContext);

        Order order = new RdsDetailBuilder(request.getUuid()).build();

        return new EdpResultResponse<Order>().withResult(order);
    }

    /**
     * 确认新订单
     * 确认新订单，用于创建新订单
     *
     * @param request 创建新订单请求
     * @return 返回新订单的OrderId对象
     * @throws Exception 抛出异常
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST, params = {"orderType=NEW"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL, RDSConstant.CREATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX, ids = "*")
    public EdpResultResponse<OrderId> confirmNewOrder(
            @RequestBody BaseCreateOrderRequestVo<InstanceCreateModel> request) {
        if (ObjectUtils.equals("cloud_nor", request.getItems().get(0).getConfig().getInstance().
                getDiskIoType())) {
            request.getItems().get(0).getConfig().getInstance().setDiskType("cds");
            request.getItems().get(0).getConfig().getInstance().setCdsType("premium_ssd");
            // 高性能云磁盘
        } else if (ObjectUtils.equals("cloud_high", request.getItems().get(0)
                .getConfig().getInstance().getDiskIoType())) {
            request.getItems().get(0).getConfig().getInstance().setDiskType("cds");
            request.getItems().get(0).getConfig().getInstance().setCdsType("ssd");
        }
        LogicInstanceCreateResponse response = null;
        try {
            if (!"DCC".equalsIgnoreCase(request.getItems().get(0).getConfig().getInstance().getMachineType())) {
                response = instanceService.createInstances(request, "");
            } else {
                response = instanceService.createInstancesOnDcc(request, "");
            }
            return new EdpResultResponse<OrderId>().withResult(new OrderId().withId(response.getOrderId()));
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }

    /**
     * 确认续费订单接口
     * 确认续费订单接口，用于创建续费订单
     *
     * @param request 包含续费订单信息的请求体
     * @return 返回续费订单ID的响应体
     * @throws Exception 抛出异常
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST, params = {"orderType=RENEW"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<OrderId> confirmRenewOrder(
            @IdPermission @RequestBody BaseCreateOrderRequestVo<RenewModel> request) {
        OrderUuidResult result = orderService.createRenewOrder(request, "");
        return new EdpResultResponse<OrderId>().withResult(new OrderId().withId(result.getOrderId()));
    }

    /**
     * 确认订单为预付费类型接口
     * 确认订单为预付费类型接口，用于创建预付费订单
     *
     * @param request 包含预付费订单信息的请求体
     * @return 返回预付费订单ID的响应体
     * @throws Exception 抛出异常
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST, params = {"orderType=TO_PREPAY"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<OrderId> confirmToPrepayOrder(
            @IdPermission @RequestBody RdsCreateOrderRequestVo<RenewModel> request) {
        OrderUuidResult result = orderService.createToPrepayOrder(request);
        return new EdpResultResponse<OrderId>().withResult(new OrderId().withId(result.getOrderId()));
    }

    /**
     * 确认转为后付费订单
     * 确认转为后付费订单，用于创建转为后付费订单
     *
     * @param request 包含转为后付费订单信息的请求体
     * @return 返回转为后付费订单ID的响应体
     * @throws Exception 抛出异常
     */
    @RequestMapping(value = "/confirm", method = RequestMethod.POST, params = {"orderType=TO_POSTPAY"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<OrderId> confirmToPostpayOrder(
            @IdPermission @RequestBody RdsCreateOrderRequestVo<RenewModel> request) {
        OrderUuidResult orderUuidResult = orderService.createToPostpayOrder(request);
        OrderId result = new OrderId().withId(orderUuidResult.getOrderId());
        return new EdpResultResponse<OrderId>().withResult(result);
    }
    
    /**
     * 取消预付费转后付费计费变更
     * 该函数用于处理取消预付费转后付费的计费变更请求
     * 
     * @param request 包含取消预付费转后付费计费变更请求的数据
     * @return 处理结果，包含操作是否成功等信息
     */                                                                                                                                        
    @RequestMapping(value = "/confirm", method = RequestMethod.POST, params = {"orderType=CANCEL_TO_POSTPAY"})
    @ApiOperation(value = "取消 预付费转后付费 计费变更")
    public EdpResultResponse<Object> cancleToPostpay(@RequestBody CancelToPostpayOrderRequest request) {
        EdpResultResponse<Object> result = new EdpResultResponse<>();
        orderService.cancelToPostpayOrder(request);
        return result;
    }

}
