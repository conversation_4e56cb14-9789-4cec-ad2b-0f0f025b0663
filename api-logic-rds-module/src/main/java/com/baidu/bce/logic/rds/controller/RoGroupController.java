package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.rogroup.CreateRoGroupRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.CreateRoGroupResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateWeightRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupDetailResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupListResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupLeaveRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdatePubliclyAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateEnableDelayOffRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateIsBalanceReloadRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.UpdateRoGroupPropertyRequest;
import com.baidu.bce.logic.rds.service.RoGroupService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.Valid;

@RestController
@RequestMapping("/v1/rds/rogroups")
public class RoGroupController {
    private static final Logger LOGGER = LoggerFactory.getLogger(RoGroupController.class);

    @Autowired
    private RoGroupService roGroupService;
    /**
     * 创建只读组
     * 根据请求参数创建只读组
     *
     * @param request 创建只读组的请求参数，包含创建只读组所需的信息
     * @return 返回创建只读组的结果
     */
    @ApiOperation(value = "创建只读组")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public CreateRoGroupResponse roGroupCreate(@IdPermission @RequestBody @Valid CreateRoGroupRequest request) {
        CreateRoGroupResponse response = null;
        LOGGER.debug("create new roGroup.");
        try {
            response = roGroupService.roGroupCreate(request);

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 查询只读组详情
     * 根据sourceAppId和roGroupId获取只读组详情
     *
     * @param sourceAppId 应用ID
     * @param roGroupId 只读组ID
     * @return 只读组详情
     */
    @ApiOperation(value = "查询只读组详情")
    @RequestMapping(value = "/detail/{sourceAppId}/{roGroupId}", method = RequestMethod.GET)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.READ},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public RoGroupDetailResponse roGroupDetail(
        @IdPermission @PathVariable String sourceAppId, @PathVariable String roGroupId) {
        RoGroupDetailResponse response = null;
        LOGGER.debug("roGroup detail.");
        try {
            response = roGroupService.roGroupDetail(sourceAppId, roGroupId);

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 主实例关联的只读组列表
     * 获取指定主实例关联的只读组列表
     *
     * @param instanceId 主实例ID
     * @return 只读组列表响应
     */
    @ApiOperation(value = "主实例关联的只读组列表")
    @RequestMapping(value = "/list/{instanceId}", method = RequestMethod.GET)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.READ},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public RoGroupListResponse roGroupList(@IdPermission @PathVariable String instanceId) {
        RoGroupListResponse response = null;
        LOGGER.debug("list roGroup.");
        try {
            response = roGroupService.roGroupList(instanceId);

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 删除只读组
     * 删除指定的只读组信息
     *
     * @param instanceId 实例ID
     * @param roGroupId 只读组ID
     */
    @ApiOperation(" 删除只读组")
    @RequestMapping(value = "/{instanceId}/{roGroupId}", method = RequestMethod.DELETE)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void deleteRoGroup(
        @IdPermission @PathVariable("instanceId") String instanceId, @PathVariable("roGroupId") String roGroupId) {
        LOGGER.debug("delete : {}", instanceId, roGroupId);

        try {
            roGroupService.deleteRoGroup(instanceId, roGroupId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "修改只读组名称")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=updateName"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdateName(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid RoGroupUpdateRequest request) {
        LOGGER.debug("roGroupUpdateName.");
        try {
            roGroupService.roGroupUpdateName(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "修改连接信息")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=updateEndpoint"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdateEndpoint(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid RoGroupUpdateRequest request) {
        LOGGER.debug("roGroupUpdateEndpoint.");
        try {
            roGroupService.roGroupUpdateEndpoint(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "开启/关闭公网")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=updatePubliclyAccessible"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdatePubliclyAccessible(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid RoGroupUpdatePubliclyAccessibleRequest request) {
        LOGGER.debug("roGroupUpdatePubliclyAccessible.");
        try {
            roGroupService.roGroupUpdatePubliclyAccessible(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "批量修改只读组名称、延迟自动剔除开关、延迟阈值、重新负载均衡开关、组内只读实例权重")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=updateRoGroupProperty"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdateRoGroupProperty(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid UpdateRoGroupPropertyRequest request) {
        LOGGER.debug("roGroupUpdateRoGroupProperty.");
        try {
            roGroupService.roGroupUpdateRoGroupProperty(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "修改延迟自动剔除开关")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=updateEnableDelayOff"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdateEnableDelayOff(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid RoGroupUpdateEnableDelayOffRequest request) {
        LOGGER.debug("roGroupUpdateEnableDelayOff.");
        try {
            roGroupService.roGroupUpdateEnableDelayOff(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "修改重新负载均衡开关")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=updateIsBalanceReload"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdateIsBalanceReload(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid RoGroupUpdateIsBalanceReloadRequest request) {
        LOGGER.debug("roGroupUpdateIsBalanceReload.");
        try {
            roGroupService.roGroupUpdateIsBalanceReload(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "修改组内最少保留数目")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=updateLeastAppAmount"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdateLeastAppAmount(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid RoGroupUpdateRequest request) {
        LOGGER.debug("roGroupUpdateLeastAppAmount.");
        try {
            roGroupService.roGroupUpdateLeastAppAmount(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "修改延迟剔除阈值")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=updateDelayThreshold"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdateDelayThreshold(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid RoGroupUpdateRequest request) {
        LOGGER.debug("roGroupUpdateDelayThreshold.");
        try {
            roGroupService.roGroupUpdateDelayThreshold(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "只读组重新负载均衡")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=reload"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdateReload(@IdPermission @PathVariable String sourceAppId, @PathVariable String roGroupId) {
        LOGGER.debug("roGroupUpdateReload.");
        try {
            roGroupService.roGroupUpdateReload(sourceAppId, roGroupId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "修改只读组中只读实例的权重")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=updateWeight"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdateWeight(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid RoGroupUpdateWeightRequest request) {
        LOGGER.debug("roGroupUpdateWeight.");
        try {
            roGroupService.roGroupUpdateWeight(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "只读实例加入只读组")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=join"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupUpdateJoin(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid RoGroupUpdateWeightRequest request) {
        LOGGER.debug("roGroupUpdateJoin.");
        try {
            roGroupService.roGroupUpdateJoin(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 只读实例离开只读组
     * 只读实例离开只读组的接口方法，用于将只读实例从只读组中移除
     *
     * @param sourceAppId 应用ID
     * @param roGroupId 只读组ID
     * @param request 请求体，包含离开只读组所需的参数
     * @throws 无
     */
    @ApiOperation(value = "只读实例离开只读组")
    @RequestMapping(
        value = "/{sourceAppId}/{roGroupId}",
        method = RequestMethod.PUT,
        params = {"action=leave"}
    )
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
        idConverter = RdsToMasterShortIdConverter.class,
        type = RDSConstant.ID_PREFIX
    )
    public void roGroupLeave(
        @IdPermission @PathVariable String sourceAppId,
        @PathVariable String roGroupId,
        @RequestBody @Valid RoGroupLeaveRequest request) {
        LOGGER.debug("roGroupLeave.");
        try {
            roGroupService.roGroupLeave(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
}
