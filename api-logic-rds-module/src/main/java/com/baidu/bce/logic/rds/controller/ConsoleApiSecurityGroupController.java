package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.security.OpenApiSecurityGroupResponse;
import com.baidu.bce.internalsdk.rds.model.security.PageSecurityResponse;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupBindRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupPageRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupUpdate;
import com.baidu.bce.logic.rds.service.SecurityGroupService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "/api/rds", produces = {"application/json"})
@Api(value = "RDS 安全组管理API")
public class ConsoleApiSecurityGroupController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConsoleApiSecurityGroupController.class);

    @Autowired
    private SecurityGroupService securityGroupService;

    @ApiOperation("查询实例安全组列表")
    @RequestMapping(value = "/security/{instanceId}", method = RequestMethod.GET)
    @PermissionVertify(service = {
        RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
    idConverter = RdsToMasterShortIdConverter .class, type = RDSConstant.ID_PREFIX)
    @ResponseBody
    public EdpResultResponse<OpenApiSecurityGroupResponse> listSecurityGroup(@PathVariable @IdPermission String instanceId) {
        EdpResultResponse<OpenApiSecurityGroupResponse> response = new EdpResultResponse<>();
        response.setResult(securityGroupService.listSecurityGroup(instanceId));
        return response;
    }
    /**
     * 查询vpc下安全组列表
     * 通过vpc信息查询安全组列表，支持分页查询
     *
     * @param groupPageRequest 分页查询参数
     * @param from 来源，默认为空
     * @return 安全组列表的分页结果
     */
    @ApiOperation(value = "查询vpc下安全组列表")
    @RequestMapping(value = "/security/listByVpc", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<PageSecurityResponse> listSecurityGroupByVpc(
            @RequestBody SecurityGroupPageRequest groupPageRequest,
            @RequestParam(defaultValue = "", required = false) String from) {
        EdpResultResponse<PageSecurityResponse> response = new EdpResultResponse<>();
        response.setResult(securityGroupService.listSecurityGroupByVpc(groupPageRequest));
        return response;
    }


    @ApiOperation(value = "绑定安全组")
    @RequestMapping(value = "/security/bind", method = RequestMethod.POST)
    @PermissionVertify(service = {
            RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @ResponseBody
    public EdpResultResponse<Boolean> bindSecurityGroup(
            @RequestBody @IdPermission SecurityGroupBindRequest securityGroupBindRequest,
            @RequestParam(defaultValue = "", required = false) String from) {
        LOGGER.debug("bind securityGroup.");
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        securityGroupService.bindSecurityGroup(securityGroupBindRequest, from);
        response.setResult(true);
        return response;
    }
    /**
     * 解绑安全组
     * 解绑指定安全组的功能实现
     *
     * @param securityGroupUpdate 安全组更新信息
     * @param from 来源标记，默认为空字符串
     */
    @ApiOperation(value = "解绑安全组")
    @RequestMapping(value = "/security/unbind", method = RequestMethod.POST)
    @PermissionVertify(service = {
            RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @ResponseBody
    public EdpResultResponse<Boolean> unbindSecurityGroup(
            @RequestBody @IdPermission SecurityGroupUpdate securityGroupUpdate,
            @RequestParam(defaultValue = "", required = false) String from) {
        LOGGER.debug("unbind securityGroup.");
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        securityGroupService.unbindSecurityGroup(securityGroupUpdate, from);
        response.setResult(true);
        return response;
    }

    @ApiOperation(value = "批量替换安全组")
    @RequestMapping(value = "/updateSecurityGroup", method = RequestMethod.POST)
    @PermissionVertify(service = {
            RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @ResponseBody
    public EdpResultResponse<Boolean> updateSecurityGroup(
            @RequestBody @IdPermission SecurityGroupUpdate securityGroupUpdate,
            @RequestParam(defaultValue = "", required = false) String from) {
        LOGGER.debug("update SecurityGroup.");
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        securityGroupService.updateSecurityGroup(securityGroupUpdate, from);
        response.setResult(true);
        return response;
    }


}
