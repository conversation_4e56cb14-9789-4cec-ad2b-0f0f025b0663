package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.FailinjectResponse;
import com.baidu.bce.internalsdk.rds.model.FailinjectRequest;
import com.baidu.bce.logic.rds.service.FailinjectService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;

@RestController
@RequestMapping("/v1/failinject")
public class OpenApiFailinjectController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiAccountController.class);

    @Autowired
    private FailinjectService failinjectService;
    /**
      * 查看用户的故障注入白名单
      * 访问/whitelist接口，获取用户的故障注入白名单信息
      * 
      * @return FailinjectRequest 返回用户的故障注入白名单信息
      */

    @ApiOperation(value = "查看用户的故障注入白名单")
    @RequestMapping(value = "/whitelist", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public FailinjectRequest getFailinjectWhitelist() {
        FailinjectRequest response  = failinjectService.getFailinjectWhitelist();
        return response;
    }
    /**
     * 将实例加入故障注入白名单
     * 将指定的实例加入故障注入白名单，允许对其进行故障注入操作
     * 
     * @param request 包含要加入白名单的实例信息的请求体
     */

    @ApiOperation(value = "将实例加入故障注入白名单")
    @RequestMapping(value = "/whitelist", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void putFailinjectWhitelist(@RequestBody @IdPermission FailinjectRequest request) {
        failinjectService.putFailinjectWhitelist(request);
    }
    /**
     * 将实例从故障注入功能白名单移除
     * 将指定的实例从故障注入功能的白名单中移除
     * 
     * @param request 包含需要移除白名单的实例信息
     */

    @ApiOperation(value = "将实例从故障注入功能白名单移除")
    @RequestMapping(value = "/whitelist/remove", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void removeFailinjectWhitelist(@RequestBody @IdPermission FailinjectRequest request) {
        failinjectService.removeFailinjectWhitelist(request);
    }
    /**
     * 发起故障注入操作
     * 该函数用于发起故障注入操作，传入appId参数，调用服务处理并返回处理结果
     * 
     * @param appId 应用ID，用于标识要发起故障注入操作的应用
     * @return FailinjectResponse 返回故障注入操作的结果
     */

    @ApiOperation(value = "发起故障注入操作")
    @RequestMapping(value = "/{appId}", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public FailinjectResponse postFailinject(@IdPermission @IdMapper @PathVariable String appId) {

        FailinjectResponse response = failinjectService.postFailinject(appId);
        return response;
    }
}
