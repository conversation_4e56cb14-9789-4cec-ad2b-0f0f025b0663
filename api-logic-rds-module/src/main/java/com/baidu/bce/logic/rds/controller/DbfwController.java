package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwModifyStateRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwState;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwStateGetResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInject;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectGetResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteDetail;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteListCreateRequest;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.service.model.LogicCommonListRequest;
import com.baidu.bce.logic.rds.service.DbFireWallService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Created by liuruisen on 2017/11/6.
 */
@RestController
@RequestMapping("/v1/rds/dbfirewall")
public class DbfwController {
    private static final Logger LOGGER = LoggerFactory.getLogger(DbfwController.class);

    @Autowired
    private DbFireWallService dbFireWallService;
    
    /**
     * 获取当前用户所有数据库防火墙状态列表
     * 根据请求获取当前用户所有数据库防火墙状态列表信息
     * 
     * @param request 请求参数，包含分页、排序等信息
     * @return 数据库防火墙状态列表的响应结果
     */            
    @ApiOperation("获取当前用户所有数据库防火墙状态列表--未使用")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public LogicPageResultResponse<DbfwState> list(@Valid LogicCommonListRequest request) {
        LOGGER.debug("get dbfirewall list");

        LogicPageResultResponse<DbfwState> response = null;
        try {
            response = dbFireWallService.dbfwList(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查看代理实例的数据库防火墙状态
      * 通过instanceId获取数据库防火墙的状态
      * 
      * @param instanceId 实例ID
      * @param from 请求来源
      * @return 数据库防火墙状态
      */

    @ApiOperation("查看代理实例的数据库防火墙状态")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public DbfwStateGetResponse instantDbfirewall(@IdPermission @PathVariable String instanceId,
                                                  @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get insatnce dbfirewall state");

        DbfwStateGetResponse response = null;
        try {
            response = dbFireWallService.dbfwState(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 修改防火墙状态
      * 修改数据库防火墙实例的状态
      *
      * @param instanceId 实例ID
      * @param stateRequest 状态请求体
      * @param from 来源标识
      */

    @ApiOperation("修改防火墙状态")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateState(@IdPermission @PathVariable String instanceId,
                            @RequestBody @Valid DbfwModifyStateRequest stateRequest,
                            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("modify dbfirewall state");
        try {
            dbFireWallService.updateState(instanceId, stateRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 查看SQL注入列表
      * 获取指定实例的SQL注入列表
      * 
      * @param instanceId 实例ID
      * @param request 分页查询请求参数
      * @param from 查询来源，可选参数，默认为空
      * @return SQL注入列表的分页结果
      */

    @ApiOperation("查看SQL注入列表")
    @RequestMapping(value = "/{instanceId}/sqlinject", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogicPageResultResponse<SqlInject> sqlInjectList(
            @IdPermission @PathVariable String instanceId,
            @Valid LogicCommonListRequest request,
            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get sqlinject List");
        LogicPageResultResponse<SqlInject> response = null;

        try {
            response = dbFireWallService.sqlInjectList(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }

        return response;

    }
    /**
      * 单条SQL注入详情
      * 根据实例ID和SQL注入ID获取SQL注入详情
      * 
      * @param instanceId 实例ID
      * @param sqlId SQL注入ID
      * @param from 来源，默认为空字符串
      * @return SQL注入详情响应对象
      */

    @ApiOperation("单条SQL注入详情")
    @RequestMapping(value = "/{instanceId}/sqlinject/{sqlId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlInjectGetResponse sqlInjectDescribe(@IdPermission @PathVariable String instanceId,
                                                  @PathVariable String sqlId,
                                                  @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("sqlinject Detail");

        SqlInjectGetResponse response = null;
        try {
            response = dbFireWallService.sqlInjectDetail(instanceId, sqlId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查看SQL白名单列表
      * 获取指定RDS实例的SQL白名单信息列表
      * 
      * @param instanceId RDS实例ID
      * @param request 分页查询参数
      * @param from 来源标志，用于区分调用方
      * @return SQL白名单信息列表
      */

    @ApiOperation("查看SQL白名单列表")
    @RequestMapping(value = "/{instanceId}/sqlwhite", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogicPageResultResponse<SqlWhiteDetail> sqlWhiteList(@IdPermission @PathVariable String instanceId,
                @Valid LogicCommonListRequest request,
                @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get sqlwhite list");

        LogicPageResultResponse<SqlWhiteDetail> response = null;

        try {
            response = dbFireWallService.sqlWhiteList(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }

        return response;
    }
    /**
      * 添加SQL白名单
      * 添加SQL白名单接口，用于向数据库防火墙添加SQL白名单规则
      * 
      * @param instanceId 实例ID，用于标识不同的数据库实例
      * @param request SQL白名单创建请求体，包含创建白名单所需的信息
      * @param from 请求来源，可选参数，默认为空字符串
      */

    @ApiOperation("添加SQL白名单")
    @RequestMapping(value = "/{instanceId}/sqlwhite", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void addSqlWhiteList(@IdPermission @PathVariable String instanceId,
                                @RequestBody @Valid SqlWhiteListCreateRequest request,
                                @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("Add SQLWhiteList");
        try {
            dbFireWallService.sqlWhiteListAdd(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 删除SQL白名单
      * 根据instanceId和sqlMd5删除SQL白名单
      *
      * @param instanceId 实例ID
      * @param sqlMd5 SQL的MD5值
      * @param from 来源
      */

    @ApiOperation("删除SQL白名单")
    @RequestMapping(value = "/{instanceId}/sqlwhite/{sqlMd5}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void deleteSqlWhiteList(@IdPermission @PathVariable String instanceId,
                                   @PathVariable String sqlMd5,
                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("delete sqlwhitelist");
        try {
            dbFireWallService.sqlWhiteListDelete(instanceId, sqlMd5);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }



}
