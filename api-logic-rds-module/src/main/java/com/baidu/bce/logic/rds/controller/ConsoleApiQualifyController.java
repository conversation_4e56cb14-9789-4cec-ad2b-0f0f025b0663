package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.internalsdk.qualify.model.RealNameInfoResponse;
import com.baidu.bce.internalsdk.rds.model.QualifyResponse;
import com.baidu.bce.logic.rds.service.QualifyService;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "/api/rds/qualify", produces = {"application/json"})
@Api(value = "RDS 实名认证API")
public class ConsoleApiQualifyController {

    @Autowired
    private QualifyService qualifyService;
    /**
      * 获取实名认证信息接口
      * 该接口通过POST方法请求"/realname"路径，获取实名认证信息
      * 
      * @return EdpResultResponse<RealNameInfoResponse> 返回实名认证信息响应体
      */

    @RequestMapping(value = "/realname", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<RealNameInfoResponse> getClusters() {
        RealNameInfoResponse result = qualifyService.getQualifyRealname();
        EdpResultResponse<RealNameInfoResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(result);
        return edpResultResponse;
    }
}
