package com.baidu.bce.logic.rds.controller.group;


import com.baidu.bce.internalsdk.rds.model.CheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.database.Database;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseGetResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.group.CommonGroupListRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupDataBaseCheckRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupDatabaseCreateRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupDatabaseDeleteRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupDatabaseDetailRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupDatabaseModifyDescRequest;
import com.baidu.bce.logic.rds.service.group.InstanceGroupService;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Created by shiyuxin on 19/5/20.
 */
@RestController
@RequestMapping(value = "/api/rds/group/database", produces = {"application/json"})
public class ConsoleApiGroupDatabaseController {

    @Autowired
    private InstanceGroupService instanceGroupService;
    /**
      * 列表查询接口
      * 根据请求参数获取数据库列表信息
      * 
      * @param request 请求参数，包含排序字段、排序方式、分组ID等信息
      * @return EdpPageResultResponse对象，包含数据库列表信息
      */

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    public EdpPageResultResponse<Database> list(@Valid @RequestBody CommonGroupListRequest request) {
        EdpPageResultResponse<Database> edpPageResultResponse = new EdpPageResultResponse<>();
        edpPageResultResponse.getPage().setOrderBy(request.getOrderBy());
        edpPageResultResponse.getPage().setOrder(request.getOrder());
        edpPageResultResponse.getPage().setResult(
                instanceGroupService.databaseList(request.getGroupId()).getDatabases());
        return edpPageResultResponse;
    }
    /**
      * 获取数据库详情
      * 根据传入的groupId和dbName获取数据库的详细信息
      * 
      * @param request 包含groupId和dbName的请求体
      * @return 数据库详情封装后的响应体
      */

    @RequestMapping(value = "detail", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Database> detail(@Valid @RequestBody DashboardGroupDatabaseDetailRequest request) {
        DatabaseGetResponse response = instanceGroupService.databaseDetail(request.getGroupId(), request.getDbName());
        EdpResultResponse<Database> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(response.getDatabase());
        return edpResultResponse;
    }
    /**
      * 更新描述接口
      * 该接口用于更新实例组数据库的描述信息
      * 
      * @param request 包含更新描述请求的信息，包括remark（描述），groupId（组ID），dbName（数据库名称）
      * @return EdpResultResponse<Boolean> 更新操作的结果
      */

    @RequestMapping(value = "update_desc", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> updateDesc(@Valid @RequestBody DashboardGroupDatabaseModifyDescRequest request) {
        DatabaseUpdateRemarkRequest databaseUpdateRemarkRequest = new DatabaseUpdateRemarkRequest();
        databaseUpdateRemarkRequest.setRemark(request.getRemark());
        instanceGroupService.databaseUpdateRemark(request.getGroupId(), request.getDbName(),
                databaseUpdateRemarkRequest);
        return new EdpResultResponse<>();
    }
    /**
      * 创建仪表盘组数据库
      * 接收创建实例组数据库请求，处理并返回结果
      * 
      * @param request 创建实例组数据库请求体
      * @return 处理结果，成功则返回EdpResultResponse对象，包含操作成功标志
      */

    @RequestMapping(value = "create", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> create(@Valid @RequestBody DashboardGroupDatabaseCreateRequest request) {
        Database database = request.getDatabase();
        instanceGroupService.databaseCreate(request.getGroupId(), database);
        return new EdpResultResponse<>();
    }
    /**
     * 删除实例组数据库
     * 删除指定实例组的数据库
     * 
     * @param request 包含groupId和dbName的请求体
     * @return EdpResultResponse<Boolean> 返回操作结果
     */

    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> delete(@Valid @RequestBody DashboardGroupDatabaseDeleteRequest request) {
        instanceGroupService.databaseDelete(request.getGroupId(), request.getDbName());
        return new EdpResultResponse<>();
    }
    /**
      * 检查接口
      * 对指定数据库进行存在性检查
      * 
      * @param request 包含groupId和dbName的请求体
      * @return 返回检查结果
      */

    @RequestMapping(value = "check", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> check(@Valid @RequestBody DashboardGroupDataBaseCheckRequest request) {
        EdpResultResponse<Boolean> response = new EdpResultResponse<Boolean>();
        CheckExistResponse existResponse = instanceGroupService.databaseCheck(request.getGroupId(),
                request.getDbName());
        response.withResult(existResponse.getIsExist() != 0);
        return response;
    }



}
