package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceRestoreRequest;
import com.baidu.bce.internalsdk.rds.model.snapshot.BackupCrossRegionListResponses;
import com.baidu.bce.internalsdk.rds.model.snapshot.BackupTargetRegionResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.CommonListRequest;
import com.baidu.bce.internalsdk.rds.model.snapshot.CopyRegionSnapshotDetail;
import com.baidu.bce.internalsdk.rds.model.snapshot.DashboardBackupCommonRequest;
import com.baidu.bce.internalsdk.rds.model.snapshot.DashboardBackupListResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.DashboardbackupListRequest;
import com.baidu.bce.internalsdk.rds.model.snapshot.ListAllCrossRegions;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotGetResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotListWithTimeResponse;
import com.baidu.bce.logic.rds.model.DownloadUrlRequest;
import com.baidu.bce.logic.rds.model.Url;
import com.baidu.bce.logic.rds.service.BackupService;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/api/rds/backup")
public class ConsoleApiSnapshotController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private BackupService backupService;

    @Autowired
    private InstanceService instanceService;
    /**
      * 列出快照备份
      * 根据请求参数列出快照备份信息，并返回响应结果
      * 
      * @param request 快照备份列表请求参数
      * @return DashboardBackupListResponse 快照备份列表响应结果
      */

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public DashboardBackupListResponse list(@IdPermission @Valid @RequestBody DashboardbackupListRequest request) {
        DashboardBackupListResponse backupListResponse = new DashboardBackupListResponse();
        SnapshotListWithTimeResponse snapshotListResponse;
        snapshotListResponse = backupService.list(request);
//        if (StringUtils.isNotEmpty(request.getStartDatetime())
//                && StringUtils.isNotEmpty(request.getStopDatetime())) {
//            snapshotListResponse = backupService.list(request.getInstanceId(), request.getStartDatetime(),
//                    request.getStopDatetime());
//        } else {
//            snapshotListResponse = backupService.list(request.getInstanceId());
//        }

        backupListResponse.getResult().getPage().withResult(snapshotListResponse
                .getSnapshots()).withOrder("").withOrderBy("").withPageNo(10);
        backupListResponse.success(true);
        backupListResponse.getResult().setFreeSpaceInMB(snapshotListResponse.getFreeSpaceInMB());
        backupListResponse.getResult().setUsedSpaceInMB(snapshotListResponse.getUsedSpaceInMB());
        backupListResponse.getResult().setPeriod(snapshotListResponse.getPeriod());
        backupListResponse.getResult().getPage().setTotalCount(snapshotListResponse.getTotalCount());
//        backupListResponse.getResult().getPage().setPageNo(Integer.parseInt(snapshotListResponse.getPageNo()));
        backupListResponse.getResult().getPage().setPageSize(snapshotListResponse.getPageSize());
        return backupListResponse;
    }
    /**
      * 获取克隆列表
      * 用于获取指定实例的克隆列表信息，返回克隆列表及对应的时间信息
      * 
      * @param request 请求参数，包含实例ID等
      * @return EdpResultResponse<SnapshotListWithTimeResponse> 返回克隆列表及对应的时间信息
      */

    @RequestMapping(value = "list_for_clone", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SnapshotListWithTimeResponse> listForClone(
            @IdPermission @Valid @RequestBody CommonListRequest request) {
        EdpResultResponse<SnapshotListWithTimeResponse> resultResponse = new EdpResultResponse<>();
        SnapshotListWithTimeResponse snapshotListResponse;
        snapshotListResponse = backupService.list(request.getInstanceId());
        resultResponse.withResult(snapshotListResponse);
        return resultResponse;
    }
    /**
     * 备份接口
     * 根据请求中的实例ID进行备份操作
     * 
     * @param request 备份请求，包含实例ID
     * @return 备份操作的结果
     */

    @RequestMapping(value = "do", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Url> backup(@IdPermission @Valid @RequestBody @IdMapper InstanceIdRequest request) {
        if (request == null) {
            request = new InstanceIdRequest();
        }
        EdpResultResponse<Url> response = new EdpResultResponse<>();
        backupService.create(request);
        return response;
    }
    /**
      * 恢复数据接口
      * 根据提供的请求参数恢复数据
      * 
      * @param request 恢复请求参数，包含快照ID和实例ID
      * @return 恢复操作的结果
      */

    @RequestMapping(value = "rebase", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> restore(@IdPermission @Valid @RequestBody DashboardBackupCommonRequest request) {
        InstanceRestoreRequest instanceRestoreRequest = new InstanceRestoreRequest();
        instanceRestoreRequest.setSnapshotId(request.getSnapshotId());
        instanceService.restore(request.getInstanceId(), instanceRestoreRequest);
        return new EdpResultResponse<>();
    }
    /**
      * 删除快照备份
      * 该函数用于删除指定的快照备份，通过传入的实例ID和快照ID进行删除操作
      * 
      * @param request 包含实例ID和快照ID的请求体
      * @return EdpResultResponse<Boolean> 返回操作结果，成功时包含true，失败时包含false
      */

    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> delete(@IdPermission @Valid @RequestBody DashboardBackupCommonRequest request) {
        backupService.snapshotDelete(request.getInstanceId(), request.getSnapshotId());
        return new EdpResultResponse<>();
    }
    /**
      * 获取备份详情
      * 根据提供的实例ID、快照ID和下载有效时间，获取备份文件的下载URL、过期时间和数据备份类型。
      * 
      * @param request 包含实例ID、快照ID和下载有效时间的请求体
      * @return 包含下载URL、过期时间和数据备份类型的响应体
      */

    @RequestMapping(value = "/download_url", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Url> getDetail(@IdPermission @Valid @RequestBody DownloadUrlRequest request) {
        LOGGER.debug("get backup detail.");
        EdpResultResponse<Url> edpResultResponse = new EdpResultResponse<Url>();
        SnapshotGetResponse snapshotGetResponse = backupService.detail(request.getInstanceId(),
                request.getSnapshotId(), Integer.valueOf(request.getDownloadValidTimeInSec()));
        edpResultResponse.withResult(new Url().url(snapshotGetResponse.getSnapshot().getDownloadUrl())
                .expire(snapshotGetResponse.getSnapshot().getDownloadExpires())
                .dataBackupType(snapshotGetResponse.getSnapshot().getDataBackupType())
                .multiDownloadUrl(snapshotGetResponse.getSnapshot().getMultiDownloadUrl()));
        return edpResultResponse;
    }
    /**
      * 跨地域备份列表
      * 获取跨地域备份列表的接口
      * 
      * @param instanceId 实例ID
      * @param startDataTime 开始时间（可选）
      * @param endDataTime 结束时间（可选）
      * @param marker 分页标记（可选）
      * @param maxKeys 分页大小（可选）
      * @return EdpResultResponse<BackupCrossRegionListResponses> 返回跨地域备份列表的结果
      */

    @ApiOperation("跨地域备份列表（跨地域备份列表）")
    @RequestMapping(value = "/{instanceId}/crossRegion/list", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<BackupCrossRegionListResponses> getCrossRegionList
            (@IdPermission @PathVariable String instanceId,
             @RequestParam(required = false, value = "startDataTime") String startDataTime,
             @RequestParam(required = false, value = "endDataTime") String endDataTime,
             @RequestParam(required = false, value = "marker") String marker,
             @RequestParam(required = false, value = "maxKeys") String maxKeys,
             @RequestParam(required = false, value = "storageRegion") String storageRegion) {
        EdpResultResponse<BackupCrossRegionListResponses> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(backupService.getCrossRegionList(instanceId, startDataTime,
                endDataTime, marker, maxKeys, storageRegion));
        return resultResponse;

    }
    /**
     * 数据备份副本详情（跨可用区备份详情）
     * 获取跨可用区的备份副本详情
     * 
     * @param instanceId 实例ID
     * @param snapshotId 快照ID
     * @return 跨可用区备份副本详情结果
     */

    @ApiOperation("数据备份副本详情（跨可用区备份详情）")
    @RequestMapping(value = "/{instanceId}/crossRegion/{snapshotId}", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<CopyRegionSnapshotDetail> getCrossRegionDetail
            (@IdPermission @PathVariable String instanceId,
             @PathVariable String snapshotId) {
        EdpResultResponse<CopyRegionSnapshotDetail> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(backupService.getCrossRegionDetail(instanceId, snapshotId));
        return resultResponse;

    }
    /**
      * 数据备份副本目标地域映射（目标地域列表）
      * 通过instanceId获取数据备份副本的目标地域列表
      * 
      * @param instanceId 实例ID
      * @return EdpResultResponse<BackupTargetRegionResponse> 返回结果
      */

    @ApiOperation("数据备份副本目标地域映射（目标地域列表）")
    @RequestMapping(value = "/{instanceId}/target/region", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<BackupTargetRegionResponse> getTargetRegionList
            (@IdPermission @PathVariable String instanceId) {
        EdpResultResponse<BackupTargetRegionResponse> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(backupService.getTargetRegionList(instanceId));
        return resultResponse;

    }

    @ApiOperation("数据备份副本区域列表")
    @RequestMapping(value = "/{instanceId}/listCopyRegions", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<ListAllCrossRegions> getListCopyRegions
            (@IdPermission @PathVariable String instanceId,
             @RequestParam(required = false, value = "startDateTime") String startDataTime,
             @RequestParam(required = false, value = "endDateTime") String endDataTime,
             @RequestParam(required = false, value = "dataBackupType") String dataBackupType) {
        EdpResultResponse<ListAllCrossRegions> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(backupService.getListCopyRegions(instanceId, startDataTime,
                endDataTime, dataBackupType));
        return resultResponse;

    }
}
