package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ApplyDetail;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ApplyInstance;
import com.baidu.bce.internalsdk.rds.model.paratemplate.DuplicateTempRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateApplyHistory;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateCompare;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateCreateRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateModel;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplatePage;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateResponse;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateUpdateRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateCompareRequest;
import com.baidu.bce.logic.rds.service.ParaTemplateService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Api("参数模板api")
@RestController
@RequestMapping(
    value = "/v1/instance/paraTemplate",
    produces = {"application/json"}
)
public class OpenApiParaTemplateController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiParaTemplateController.class);

    @Autowired
    ParaTemplateService paraTemplateService;

    /**
     * 获取参数模板列表
     * 通过分页信息及参数类型、数据库类型、数据库版本获取参数模板列表
     *
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @param type 参数类型
     * @param dbType 数据库类型
     * @param dbVersion 数据库版本
     * @return 参数模板分页信息
     */
    @ApiOperation("参数模板列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public ParaTemplatePage<ParaTemplateModel> listParaTemplate(
        @RequestParam(value = "pageNo", required = false) Integer pageNo,
        @RequestParam(value = "pageSize", required = false) Integer pageSize,
        @RequestParam(value = "type", required = false) String type,
        @RequestParam(value = "dbType", required = false) String dbType,
        @RequestParam(value = "dbVersion", required = false) String dbVersion,
        @RequestParam(value = "tplName", required = false) String tplName,
        @RequestParam(value = "tplId", required = false) String showId) {
        ParaTemplatePage<ParaTemplateModel> paraTemplatePage =
            paraTemplateService.listParaTemplate(pageNo, pageSize, dbType, dbVersion, type, tplName, showId);
        return paraTemplatePage;
    }
    /**
     * 模板应用历史
     * 查询模板应用历史列表
     *
     * @param templateId 模板ID
     * @param pageNo 页码，非必填
     * @param pageSize 每页大小，非必填
     * @return 模板应用历史列表的响应对象
     */
    @ApiOperation("模板应用历史")
    @RequestMapping(value = "/apply/{templateId}", method = RequestMethod.GET)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.READ},
        type = RDSConstant.ID_PREFIX
    )
    public ParaTemplateResponse listParaTemplateApplyHistory(
        @PathVariable("templateId") String templateId,
        @RequestParam(value = "pageNo", required = false) Integer pageNo,
        @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        LOGGER.debug("get paraTemplateApplyHistory list. templateId: {}", templateId);
        ParaTemplateResponse response = new ParaTemplateResponse();
        try {
            ParaTemplatePage<ParaTemplateApplyHistory> paraTemplatePage =
                paraTemplateService.listParaTemplateApplyHistory(templateId, pageNo, pageSize);
            response.setResult(paraTemplatePage);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取模板应用详情
     * 根据提供的applyId获取模板应用详情
     *
     * @param applyId 模板应用ID
     * @return 模板应用详情响应对象
     */
    @ApiOperation("模板应用详情")
    @RequestMapping(value = "/apply/detail/{applyId}", method = RequestMethod.GET)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.READ},
        type = RDSConstant.ID_PREFIX
    )
    public ParaTemplateResponse getParaTemplateApplyDetail(@PathVariable("applyId") String applyId) {
        LOGGER.debug("get paraTemplateApply detail. applyId: {}", applyId);
        ParaTemplateResponse response = new ParaTemplateResponse();
        try {
            List<ApplyDetail> applyDetailList = paraTemplateService.getParaTemplateApplyDetail(applyId);
            response.setResult(applyDetailList);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 创建参数模板
     * 创建参数模板接口，用于添加新的参数模板
     *
     * @param paraTemplateCreateRequest 参数模板创建请求体
     */
    @ApiOperation("创建参数模板")
    @RequestMapping(value = "/add/template", method = RequestMethod.POST)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.FULL_CONTROL},
        type = RDSConstant.ID_PREFIX
    )
    public void addParaTemplate(@Valid @RequestBody ParaTemplateCreateRequest paraTemplateCreateRequest) {
        LOGGER.debug("create ParaTemplate");
        try {
            paraTemplateService.addParaTemplate(paraTemplateCreateRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 模板复制
     * 根据请求体中的信息复制模板
     *
     * @param request 包含模板复制所需信息的请求体
     * @return 操作成功返回true
     */
    @ApiOperation("复制模板")
    @RequestMapping(value = "/duplicate/template", method = RequestMethod.POST)
    public Boolean duplicateTemplate(@Valid @RequestBody DuplicateTempRequest request) {
        paraTemplateService.duplicateTemplate(request);
        return true;
    }
    /**
     * 获取模板详情
     * 根据传入的模板ID获取模板详情信息，并进行处理返回
     *
     * @param templateId 模板ID
     * @return 模板详情信息
     */
    @ApiOperation("模板详情")
    @RequestMapping(value = "/template/detail/{templateId}", method = RequestMethod.GET)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.READ},
        type = RDSConstant.ID_PREFIX
    )
    public ParaTemplateResponse getTemplateDetail(@PathVariable("templateId") String templateId) {
        LOGGER.debug("get paraTemplate detail. templateId: {}", templateId);
        ParaTemplateResponse response = new ParaTemplateResponse();
        try {
            ParaTemplateModel paraTemplateModel = paraTemplateService.getTemplateDetail(templateId);
            if (paraTemplateModel != null
                && paraTemplateModel.getDbType() != null
                && "mysql".equals(paraTemplateModel.getDbType())) {
                paraTemplateModel.setDbType("MySQL");
            }
            response.setResult(paraTemplateModel);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 模板删除
     * 根据提供的模板ID，删除对应的模板信息
     *
     * @param templateId 模板ID
     */
    @ApiOperation("模板删除")
    @RequestMapping(value = "/delete/template/{templateId}", method = RequestMethod.DELETE)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.OPERATE},
        type = RDSConstant.ID_PREFIX
    )
    public void deleteTemplate(@PathVariable("templateId") String templateId) {
        LOGGER.debug("delete paraTemplate. templateId: {}", templateId);

        try {
            paraTemplateService.deleteTemplate(templateId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 模板修改
     * 根据提供的模板ID修改模板信息
     *
     * @param templateId 模板ID
     * @param name 模板名称，可选参数
     * @param updateRequestList 模板更新请求体
     */
    @ApiOperation("模板修改")
    @RequestMapping(value = "/update/template/{templateId}", method = RequestMethod.PUT)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.OPERATE},
        type = RDSConstant.ID_PREFIX
    )
    public void updateTemplate(
        @PathVariable("templateId") String templateId,
        @RequestParam(value = "name", required = false) String name,
        @RequestBody List<ParaTemplateUpdateRequest> updateRequestList) {
        LOGGER.debug("update paraTemplate. templateId: {}", templateId);
        try {
            paraTemplateService.updateTemplate(templateId, name, updateRequestList);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 应用模板
     * 根据传入的模板ID和请求参数，应用模板并返回结果
     *
     * @param templateId 模板ID
     * @param request 应用模板的请求参数
     * @return 应用模板后的结果
     * @throws Throwable 如果应用模板过程中发生异常，则抛出异常
     */
    @ApiOperation("模板应用")
    @RequestMapping(value = "/template/apply/{templateId}", method = RequestMethod.POST)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.OPERATE},
        type = RDSConstant.ID_PREFIX
    )
    public ParaTemplateResponse applyTemplate(
        @PathVariable("templateId") String templateId, @RequestBody ParaTemplateCompareRequest request)
        throws Throwable {
        LOGGER.debug("apply paraTemplate. templateId: {}", templateId);
        ParaTemplateResponse response = new ParaTemplateResponse();
        try {
            Map<String, List<ParaTemplateCompare>> paraTemplateCompareList =
                paraTemplateService.applyTemplate(templateId, request);
            response.setResult(paraTemplateCompareList);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 可应用实例列表
     * 获取参数模板的可应用实例列表
     *
     * @param engine 引擎名称
     * @param engineVersion 引擎版本
     * @return 参数模板的可应用实例列表
     */
    @ApiOperation("可应用实例列表")
    @RequestMapping(value = "/apply/instanceList", method = RequestMethod.GET)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.READ},
        type = RDSConstant.ID_PREFIX
    )
    public ParaTemplateResponse listApplyInstance(
        @RequestParam("engine") String engine,
        @RequestParam("engineVersion") String engineVersion,
        @RequestParam(value = "instanceType", required = false) String instanceType) {
        LOGGER.debug("get paraTemplate list");
        ParaTemplateResponse response = new ParaTemplateResponse();
        try {
            List<ApplyInstance> applyInstanceList =
                    paraTemplateService.listApplyInstance(engine, engineVersion, instanceType);
            response.setResult(applyInstanceList);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 模板参数比较
     * 用于比较模板参数，根据传入的templateId和instanceId进行比较
     *
     * @param templateId 模板ID
     * @param instanceId 实例ID
     * @return 比较结果
     */
    @ApiOperation("模板参数比较")
    @RequestMapping(value = "/compare/{templateId}/{instanceId}", method = RequestMethod.POST)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.READ},
        type = RDSConstant.ID_PREFIX
    )
    public ParaTemplateResponse compareParaTemplate(
        @PathVariable("templateId") String templateId, @PathVariable("instanceId") String instanceId) {
        LOGGER.debug("paraTemplate compare");
        ParaTemplateResponse response = new ParaTemplateResponse();
        try {
            List<ParaTemplateCompare> paraTemplateCompareList =
                paraTemplateService.compareParaTemplate(templateId, instanceId);
            response.setResult(paraTemplateCompareList);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 查看数据库参数
     * 根据数据库类型和版本以及可选的模板ID，查询数据库参数列表
     *
     * @param dbType 数据库类型
     * @param dbVersion 数据库版本
     * @param templateId 模板ID，非必传
     * @return 数据库参数列表的响应对象
     */
    @ApiOperation("查看数据库参数")
    @RequestMapping(value = "/list/datebaseParameters", method = RequestMethod.GET)
    @PermissionVertify(
        service = {RDSConstant.SERVICE_RDS},
        permission = {RDSConstant.READ},
        type = RDSConstant.ID_PREFIX
    )
    public ParaTemplateResponse listDatebaseParameters(
        @RequestParam("dbType") String dbType,
        @RequestParam("dbVersion") String dbVersion,
        @RequestParam(value = "templateId", required = false) String templateId) {
        LOGGER.debug("listDatebaseParameters");
        ParaTemplateResponse response = new ParaTemplateResponse();
        try {
            List<LinkedHashMap> paraTemplateCompareList =
                paraTemplateService.listDatebaseParameters(dbType, dbVersion, templateId);
            response.setResult(paraTemplateCompareList);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
}
