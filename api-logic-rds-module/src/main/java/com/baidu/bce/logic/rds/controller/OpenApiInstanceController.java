package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.idempotent.annotation.Idempotent;
import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.iam.facade.model.bcepass.session.BceSessionContext;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.rds.model.EffectiveTimeRequest;
import com.baidu.bce.internalsdk.rds.model.MaintainDurationRequest;
import com.baidu.bce.internalsdk.rds.model.SwitchMasterBackupRequest;
import com.baidu.bce.internalsdk.rds.model.MaintaintimeTasksResponse;
import com.baidu.bce.internalsdk.rds.model.TaskRequest;
import com.baidu.bce.internalsdk.rds.model.instance.ClusterStatusResponce;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceChangeTagRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateAddressRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateNameRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdatePortRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdatePublicAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateReplicationTypeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.ProbeInstanceResponse;
import com.baidu.bce.internalsdk.rds.model.instance.RecoveryToSourceInstanceRequest;
import com.baidu.bce.internalsdk.rds.model.instance.HotupgradeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.HotUpgradeResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceChangeDiskTypeResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePrecheckParameterRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePrecheckParameterResponse;
import com.baidu.bce.internalsdk.rds.model.instance.OrderStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.PrecheckResourceCreateResponse;
import com.baidu.bce.internalsdk.rds.model.instance.PrecheckResourceCreateRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceAzone;
import com.baidu.bce.internalsdk.rds.model.instance.AzoneInfo;
import com.baidu.bce.internalsdk.rds.model.instance.SlaInstanceResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotGetResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.rds.model.InstanceReleaseResponse;
import com.baidu.bce.logic.rds.model.RdsDetailBuilder;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.RdsOrderService;
import com.baidu.bce.logic.rds.service.constant.TagsChangeType;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.BackendExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import com.baidu.bce.logic.rds.service.model.instance.LogicInstanceCreateResponse;
import com.baidu.bce.logic.rds.service.model.instance.OpenApiCreateInstanceRequest;
import com.baidu.bce.logic.rds.service.model.instance.OpenApiInstanceMarkerListResponse;
import com.baidu.bce.logic.rds.service.model.order.ApiRenewModel;
import com.baidu.bce.logic.rds.service.model.order.RdsCreateOrderRequestVo;
import com.baidu.bce.logic.rds.service.model.pricing.AutoRenew;
import com.baidu.bce.logic.rds.service.model.pricing.OrderIdsRequest;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.PaymentModel;
import com.baidu.bce.plat.webframework.authentication.service.SessionService;
import com.baidubce.util.JsonUtils;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by luping03 on 17/11/2.
 */
@RestController
@RequestMapping("/v1/instance")
public class OpenApiInstanceController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiInstanceController.class);

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private RdsOrderService orderService;

    @Autowired
    private IdMapperService idMapperService;

    @Autowired
    private OthersService othersService;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    private void removeDuplicateZoneNameAndSubnetId(OpenApiCreateInstanceRequest request) {
        Set<String> zoneNameSet = new HashSet<>();
        if (request.getZoneNames() != null) {
            for (String zoneName : request.getZoneNames()) {
                if (zoneName == null) {
                    continue;
                }
                zoneNameSet.add(zoneName);
            }
            request.setZoneNames(new ArrayList<>(zoneNameSet));
        }
        Map<String, String> zoneNameSubnetIdMap = new HashMap<>();
        if (request.getSubnets() != null) {
            for (OpenApiCreateInstanceRequest.SubnetMap subnetMap : request.getSubnets()) {
                if (subnetMap == null || subnetMap.getZoneName() == null || subnetMap.getSubnetId() == null) {
                    continue;
                }
                zoneNameSubnetIdMap.put(subnetMap.getZoneName(), subnetMap.getSubnetId());
            }
            request.setSubnets(new ArrayList<OpenApiCreateInstanceRequest.SubnetMap>());
            for (Map.Entry<String, String> entry : zoneNameSubnetIdMap.entrySet()) {
                OpenApiCreateInstanceRequest.SubnetMap subnetMap = new OpenApiCreateInstanceRequest.SubnetMap();
                subnetMap.setZoneName(entry.getKey());
                subnetMap.setSubnetId(entry.getValue());
                request.getSubnets().add(subnetMap);
            }
        }
    }

    /**
     * 创建实例
     * 该接口用于创建实例，包括主实例、只读实例和代理实例。
     *
     * @param readReplica 只读实例标识，默认为空
     * @param rdsproxy RDS代理标识，默认为空
     * @param openApiCreateInstanceRequest 创建实例的请求参数
     * @return 创建实例的响应结果
     * @throws Exception 创建实例时可能发生的异常
     */
    @ApiOperation(value = "创建实例")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL, RDSConstant.CREATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX, ids = "*")
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public LogicInstanceCreateResponse createInstances(
            @RequestParam(required = false) String readReplica,
            @RequestParam(required = false) String rdsproxy,
            @RequestBody @Valid OpenApiCreateInstanceRequest openApiCreateInstanceRequest) {
        LOGGER.debug(String.format("readReplica = %s, rdsproxy = %s, openApiCreateInstanceRequest = %s",
                readReplica, rdsproxy, JsonUtils.toJsonString(openApiCreateInstanceRequest)));

        removeDuplicateZoneNameAndSubnetId(openApiCreateInstanceRequest);

        // 转换请求参数，即老网关 rds/api.yaml 中的逻辑
        String from = "api";
        BaseCreateOrderRequestVo<InstanceCreateModel> request;
        if (readReplica == null && rdsproxy == null) {
            // 创建主实例
            request = convertForCreateMasterInstance(openApiCreateInstanceRequest);
        } else if (readReplica != null) {
            // 只读实例
            request = convertForCreateReadReplicaInstance(openApiCreateInstanceRequest);
        } else {
            // 这里不再判断 rdsproxy != null
            // 代理实例
            request = convertForCreateProxyInstance(openApiCreateInstanceRequest);
        }
        request.getItems().get(0).getConfig().getInstance().setEdgeRegion(openApiCreateInstanceRequest.getEdgeRegion());
        request.getItems().get(0).getConfig().getInstance().setEdgeVpcId(openApiCreateInstanceRequest.getEdgeVpcId());
        request.getItems().get(0).getConfig().getInstance().setEdgeSubnetId(
                openApiCreateInstanceRequest.getEdgeSubnetId());
        LogicInstanceCreateResponse response = null;
        try {
            if (!"DCC".equalsIgnoreCase(request.getItems().get(0).getConfig().getInstance().getMachineType())) {
                response = instanceService.createInstances(request, from);
            } else {
                response = instanceService.createInstancesOnDcc(request, from);
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    /**
     * 通过marker方式分页查询实例列表
     * 通过marker方式分页查询实例列表 该接口支持通过marker方式分页查询实例列表，每次请求返回的marker作为下一次请求的marker
     *
     * @param marker 分页查询的起始位置标识，默认为"-1"
     * @param maxKeys 每页返回的最大数量，默认为1000
     * @return OpenApiInstanceMarkerListResponse 类型的实例分页列表响应体
     * @throws Exception 查询实例列表时可能发生的异常
     */
    @RequestMapping(method = RequestMethod.GET)
    @ApiOperation(value = "实例列表: 通过marker方式分页")
    public OpenApiInstanceMarkerListResponse listInstanceByMarker(
            @RequestParam(required = false, defaultValue = "-1") String marker,
            @RequestParam(required = false, defaultValue = "1000") Integer maxKeys,
            @RequestParam(required = false, defaultValue = "") String vnetIp) {
        LOGGER.debug("list instance[marker], marker:{}, maxKeys:{}.", marker, maxKeys);
        LogicMarkerResultResponse<InstanceAbstract> pageResultResponse = null;
        OpenApiInstanceMarkerListResponse openApiInstanceMarkerListResponse = null;
        try {
            pageResultResponse = instanceService.listInstanceWithMarkerByMultiKey(marker, maxKeys, vnetIp);

            openApiInstanceMarkerListResponse = new OpenApiInstanceMarkerListResponse();
            openApiInstanceMarkerListResponse.setMarker(pageResultResponse.getMarker());
            openApiInstanceMarkerListResponse.setIsTruncated(pageResultResponse.getIsTruncated());
            openApiInstanceMarkerListResponse.setNextMarker(pageResultResponse.getNextMarker());
            openApiInstanceMarkerListResponse.setMaxKeys(pageResultResponse.getMaxKeys());
            openApiInstanceMarkerListResponse.setInstances(new ArrayList<>(pageResultResponse.getResult()));
            for (InstanceAbstract instanceAbstract : openApiInstanceMarkerListResponse.getInstances()) {
                if (instanceAbstract.getInstanceShortId() != null) {
                    instanceAbstract.setInstanceId(instanceAbstract.getInstanceShortId());
                }
                instanceAbstract.setMemoryCapacity(instanceAbstract.getAllocatedMemoryInGB());
                instanceAbstract.setVolumeCapacity(instanceAbstract.getAllocatedStorageInGB());
                instanceAbstract.setUsedStorage(instanceAbstract.getUsedStorageInGB());
                instanceAbstract.setPublicAccessStatus(instanceAbstract.getEipStatus());
                instanceAbstract.setPaymentTiming(instanceAbstract.getProductType());
                if (instanceAbstract.getResourceGroup() != null
                        && instanceAbstract.getResourceGroup().getGroups() != null
                        && !instanceAbstract.getResourceGroup().getGroups().isEmpty()) {
                    instanceAbstract.setResourceGroupId(
                            instanceAbstract.getResourceGroup().getGroups().get(0).getGroupId());
                    instanceAbstract.setResourceGroupName(
                            instanceAbstract.getResourceGroup().getGroups().get(0).getName());
                }
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return openApiInstanceMarkerListResponse;
    }

    /**
     * 获取实例详情
     * 获取实例详情该接口用于获取实例详情，包括实例的配置信息、状态信息等
     *
     * @param instanceId 实例ID
     * @return InstanceExtension 实例详情
     * @throws Exception 查询实例详情时可能发生的异常
     */
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.GET)
    @ApiOperation(value = "实例详情")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public InstanceExtension detail(@IdPermission @PathVariable String instanceId) {
        String from = "api";
        InstanceExtension instanceExtension = null;
        try {
            instanceExtension = instanceService.detail(instanceId, from);
            if (instanceExtension.getInstanceShortId() != null) {
                instanceExtension.setInstanceId(instanceExtension.getInstanceShortId());
            }
            instanceExtension.setMemoryCapacity(instanceExtension.getAllocatedMemoryInGB());
            instanceExtension.setVolumeCapacity(instanceExtension.getAllocatedStorageInGB());
            instanceExtension.setUsedStorage(instanceExtension.getUsedStorageInGB());
            instanceExtension.setPublicAccessStatus(instanceExtension.getEipStatus());
            instanceExtension.setSyncMode(instanceExtension.getReplicationType());
            if (instanceExtension.getBackupPolicy() != null) {
                instanceExtension.getBackupPolicy().setFreeSpace(
                        instanceExtension.getBackupPolicy().getFreeSpaceInGB());
            }
            if (instanceExtension.getVpcShortId() != null) {
                instanceExtension.setVpcId(instanceExtension.getVpcShortId());
            }
            if (instanceExtension.getSubnets() != null) {
                for (InstanceExtension.Subnet subnet : instanceExtension.getSubnets()) {
                    if (subnet.getShortId() != null) {
                        subnet.setSubnetId(subnet.getShortId());
                    }
                    if (subnet.getAz() != null) {
                        subnet.setZoneName(subnet.getAz());
                    }
                }
            }
            instanceExtension.setPaymentTiming(instanceExtension.getProductType());
            if (instanceExtension.getResourceGroup() != null
                    && instanceExtension.getResourceGroup().getGroups() != null
                    && !instanceExtension.getResourceGroup().getGroups().isEmpty()) {
                instanceExtension.setResourceGroupId(
                        instanceExtension.getResourceGroup().getGroups().get(0).getGroupId());
                instanceExtension.setResourceGroupName(
                        instanceExtension.getResourceGroup().getGroups().get(0).getName());
            }
        } catch (Exception e) {
            LOGGER.error("get instance detail error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return instanceExtension;
    }

    /**
     * 释放实例
     * 释放实例该接口用于释放实例，释放后实例将不再产生费用。请谨慎操作
     *
     * @param instanceIds 需要释放的实例ID列表，使用逗号分隔
     * @return 返回释放实例的结果，包括释放成功或失败的实例ID列表
     * @throws RDSExceptions.ParamValidationException 当实例ID为空时抛出
     * @throws RDSExceptions.BatchDeleteParamValidationException 当实例ID列表长度超过3个时抛出
     */
    @ApiOperation(value = "释放实例")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public InstanceReleaseResponse deleteBatch(@IdPermission @RequestParam String instanceIds) {

        String from = "api";

        LOGGER.debug("release instance batch. instanceIds: " + instanceIds);

        InstanceReleaseResponse instanceReleaseResponse = new InstanceReleaseResponse();
        List<String> delFailed;

        if (instanceIds == null || instanceIds.length() == 0) {
            throw new RDSExceptions.ParamValidationException();
        }

        if (instanceIds.contains(",")) {

            String[] splits = instanceIds.split(",");
            if (splits.length > 3) {
                throw new RDSExceptions.BatchDeleteParamValidationException();
            }
            delFailed = instanceService.delete(splits, from);
        } else {
            String[] splits = new String[1];
            splits[0] = instanceIds;
            delFailed = instanceService.delete(splits, from);
        }

        if (delFailed.size() > 0) {
            instanceReleaseResponse.setSuccess(false);
        }
        instanceReleaseResponse.setReleaseFailedInstanceIds(delFailed);

        return instanceReleaseResponse;


//        if (instanceIds == null || instanceIds.length() == 0) {
//            throw new RDSExceptions.ParamValidationException();
//        }
//
//        if (instanceIds.contains(",")) {
//
//            String[] splits = instanceIds.split(",");
//            instanceService.delete(splits, from);
//        } else {
//            String[] splits = new String[1];
//            splits[0] = instanceIds;
//            instanceService.delete(splits, from);
//        }

    }

    /**
     * 变更实例配置，不支持批量操作
     * 该接口用于变更实例配置，不支持批量操作
     *
     * @param instanceId 实例ID
     * @param priceDiffModel 价格差异模型
     * @return 返回订单ID
     * @throws Exception 变更实例配置时发生的异常
     */
    @ApiOperation(value = "变配：不支持批量")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.PUT, params = {"resize"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public OrderUuidResult createResizeOrder(@IdPermission @PathVariable @IdMapper String instanceId,
                                             @RequestBody PriceDiffModel priceDiffModel) {
        String from = "api";
        RdsCreateOrderRequestVo<PriceDiffModel> request = new RdsCreateOrderRequestVo<>();
        request.setPaymentMethod(new HashSet<PaymentModel>(0));
        request.setItems(new ArrayList<RdsCreateOrderRequestVo.Item<PriceDiffModel>>(1));
        request.getItems().add(new RdsCreateOrderRequestVo.Item<PriceDiffModel>());
        request.getItems().get(0).setPaymentMethod(new HashSet<PaymentModel>(0));
        request.getItems().get(0).setConfig(priceDiffModel);
        priceDiffModel.setAllocatedMemoryInGB(priceDiffModel.getMemoryCapacity());
        priceDiffModel.setAllocatedStorageInGB(priceDiffModel.getVolumeCapacity());
        priceDiffModel.setInstanceId(instanceId);
        LOGGER.debug("resize instance.");
        OrderUuidResult orderId = null;

        try {
            orderId = instanceService.resizeInstance(instanceId, request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return orderId;
    }

    /**
     * 修改实例名称
     * 该接口用于修改实例名称，支持主实例、只读实例和代理实例
     *
     * @param instanceId 实例ID
     * @param request    修改实例名称请求
     * @throws Exception 修改实例名称时发生的异常
     */
    @ApiOperation(value = "修改实例名称")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.PUT, params = {"rename"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void updateInstanceName(@IdPermission @IdMapper @PathVariable String instanceId,
                                   @RequestBody @Valid InstanceUpdateNameRequest request) {
        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.updateInstanceName(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 开通/关闭公网访问
     * 该接口用于开通/关闭实例的公网访问，支持主实例、只读实例和代理实例
     *
     * @param instanceId 实例ID
     * @param request    修改公网访问请求
     * @param from       来源标识，默认为空字符串
     * @throws Exception 修改公网访问时发生的异常
     */
    @ApiOperation(value = "开通／关闭公网访问")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.PUT, params = {"modifyPublicAccess"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void updatePubliclyAccessible(@IdPermission @IdMapper @PathVariable String instanceId,
                                         @RequestBody @Valid InstanceUpdatePublicAccessibleRequest request,
                                         @RequestParam(required = false, defaultValue = "") String from) {
        if (request.getPublicAccess() != null) {
            request.setPubliclyAccessible(request.getPublicAccess());
        }
        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.updatePubliclyAccessible(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 修改域名
     * 该接口用于修改实例的域名，支持主实例、只读实例和代理实例
     *
     * @param instanceId 实例ID
     * @param request    修改域名请求
     * @throws Exception 修改域名时发生的异常
     */
    @ApiOperation(value = "修改域名")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.PUT, params = {"modifyEndpoint"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void updateDomain(@IdPermission @IdMapper @PathVariable String instanceId,
                             @RequestBody @Valid InstanceUpdateAddressRequest request) {
        String from = "api";
        request.setEndpoint(new Instance.Endpoint());
        request.getEndpoint().setAddress(request.getAddress());

        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.updateDomain(instanceId, request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 修改备份策略
     * 该接口用于修改实例的备份策略
     *
     * @param instanceId 实例ID
     * @param policy     备份策略对象
     * @throws BackendExceptions.RDSInstanceNotFound 实例不存在异常
     * @throws BackendExceptions.AccessDenied 权限不足异常
     * @throws BackendExceptions.InvalidRequest 请求参数异常
     * @throws BackendExceptions.InternalFailure 内部错误异常
     */
    @ApiOperation(value = "修改备份策略")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.PUT, params = {"modifyBackupPolicy"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void updateBackupPolicy(@IdPermission @IdMapper @PathVariable String instanceId,
                                   @RequestBody @Valid SnapshotPolicy policy) {
        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.updateBackupPolicy(instanceId, policy);
        } catch (BceInternalResponseException e) {
            if ("InstanceNotFound".equalsIgnoreCase(e.getCode())) {
                throw new BackendExceptions.RDSInstanceNotFound(e);
            }
            else if ("AccessDenied".equalsIgnoreCase(e.getCode())) {
                throw new BackendExceptions.AccessDenied(e);
            }
            else if ("InvalidRequest".equalsIgnoreCase(e.getCode())) {
                throw new BackendExceptions.InvalidRequest(e);
            }
            else if ("InternalFailure".equalsIgnoreCase(e.getCode())) {
                throw new BackendExceptions.InternalFailure(e);
            }

        }
    }

    /**
     * 修改同步方式
     * 该接口用于修改实例的同步方式，支持主实例
     *
     * @param instanceId 实例ID
     * @param request    修改同步方式请求
     * @throws Exception 修改同步方式时发生的异常
     */
    @ApiOperation(value = "修改同步方式")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.PUT, params = {"modifySyncMode"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void updateReplicationType(@IdPermission @IdMapper @PathVariable String instanceId,
                                      @RequestBody @Valid InstanceUpdateReplicationTypeRequest request) {
        request.setReplicationType(request.getSyncMode());
        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.updateReplicationType(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 重启实例
     * 该接口用于重启实例
     * @param instanceId 实例ID
     * @param request    重启请求，包含生效时间，可选参数
     * @throws Exception 重启实例时发生的异常
     */
    @ApiOperation(value = "重启实例")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.PUT, params = {"reboot"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void reboot(@PathVariable @IdPermission @IdMapper String instanceId,
                       @RequestBody(required = false) EffectiveTimeRequest request) {
        LOGGER.debug("reboot instance. instanceId is {}", instanceId);
        if (request == null) {
            request = new EffectiveTimeRequest();
            request.setEffectiveTime("immediate");
        }

        try {
            instanceService.reboot(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 绑定标签
     * 该接口用于绑定标签，将标签与实例进行绑定
     *
     * @param instanceId 实例ID
     * @param request    实例修改标签请求
     * @throws Exception 绑定标签时发生的异常
     */
    @ApiOperation(value = "绑定标签")
    @RequestMapping(value = "/{instanceId}/tag", method = RequestMethod.PUT, params = {"bind"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void bindTag(@IdPermission @IdMapper @PathVariable String instanceId,
                        @RequestBody @Valid InstanceChangeTagRequest request) {
        LOGGER.debug("bind tag. instanceId is {}", instanceId);
        try {
            instanceService.changeTagsToInstance(instanceId, request.getChangeTags(),
                    TagsChangeType.BIND, Boolean.FALSE);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 解绑标签
     * 该接口用于解绑标签，将标签与实例进行解绑
     *
     * @param instanceId 实例ID
     * @param request    实例修改标签请求
     * @throws Exception 解绑标签时发生的异常
     */
    @ApiOperation(value = "解绑标签")
    @RequestMapping(value = "/{instanceId}/tag", method = RequestMethod.PUT, params = {"unbind"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void unbindTag(@IdPermission @IdMapper @PathVariable String instanceId,
                          @RequestBody @Valid InstanceChangeTagRequest request) {
        LOGGER.debug("unbind tags. instanceId is {}", instanceId);
        try {
            instanceService.changeTagsToInstance(instanceId, request.getChangeTags(),
                    TagsChangeType.UNBIND, Boolean.FALSE);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }


    /**
     * api续费:批量
     * 该接口用于批量续费实例，支持批量操作
     *
     * @param request 续费请求模型
     * @return 续费订单ID
     * @throws Exception 续费时发生的异常
     */
    @ApiOperation(value = "api续费:批量")
    @RequestMapping(value = "/renew", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OrderUuidResult createRenewOrder(
            @IdPermission @RequestBody @Valid  ApiRenewModel request) {
        LOGGER.debug("create renew order.");

        String from = "api";

        OrderUuidResult orderId = null;
        List<String> longIds = new ArrayList<>();
        List<String> instanceIds = request.getInstanceIds();
        for (int i = 0 ;i < instanceIds.size(); i++) {
            String longId = idMapperService.getInstanceUuid(instanceIds.get(i));
            longIds.add(longId);
        }

        request.setInstanceIds(longIds);

        try {
            orderId = orderService.createRenewOrderFromApi(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return orderId;
    }


    /**
     * 已创建实例续费
     * 该接口用于续费已创建的实例，支持主实例、只读实例和代理实例
     *
     * @param autoRenew 包含续费信息的AutoRenew对象
     * @throws BceInternalResponseException 续费时发生的异常
     */
    @ApiOperation(value = "已创建实例续费")
    @RequestMapping(value = "", method = RequestMethod.PUT, params = {"autoRenew"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void createdOrderAutoRenew (@RequestBody @Valid AutoRenew autoRenew) {
        String from = "api";

        List<Order> orderList = new ArrayList<>();
        List<String> instanceIds = autoRenew.getInstanceIds();
        try {
            OrderIdsRequest orderIdsRequest = instanceService.getOrderId(instanceIds, from);
            if (orderIdsRequest.getOrderIds() != null && orderIdsRequest.getOrderIds().size() > 0) {
                for (String orderId : orderIdsRequest.getOrderIds()) {
                    LOGGER.debug("orderId is {}.", orderId);
                    // 兼容order service从iamService中获取stsCredential
                    StsCredential stsCredential = clientFactory.getUserStsAccessKey();
                    com.baidu.bce.iam.facade.model.sts.StsCredential iamStsCredential =
                            new com.baidu.bce.iam.facade.model.sts.StsCredential();
                    iamStsCredential.setAccessKeyId(stsCredential.getAccessKeyId());
                    iamStsCredential.setSecretAccessKey(stsCredential.getSecretAccessKey());
                    iamStsCredential.setSessionToken(stsCredential.getSessionToken());
                    iamStsCredential.setUserId(stsCredential.getUserId());
                    iamStsCredential.setRoleId(stsCredential.getRoleId());
                    iamStsCredential.setExpiration(stsCredential.getExpiration());
                    iamStsCredential.setCreateTime(stsCredential.getCreateTime());
                    BceSessionContext bceSessionContext = new BceSessionContext();
                    bceSessionContext.setStsCredential(iamStsCredential);
                    SessionService.setSessionContext(bceSessionContext);
                    Order order = new RdsDetailBuilder(orderId).build();
                    orderList.add(order);
                }
            }
            instanceService.setAutoRenewRules(orderList, orderIdsRequest.getInstanceIds(), autoRenew);
        } catch (BceInternalResponseException e) {
            LogicRdsExceptionHandler.handle(e);
        }

    }

    private  BaseCreateOrderRequestVo<InstanceCreateModel> convertForCreateMasterInstance(
            OpenApiCreateInstanceRequest request) {
        if (request.getBilling() == null) {
            throw new RDSExceptions.ParamValidationException("billing can't be null.");
        }
        if (request.getBilling().getPaymentTiming() == null) {
            throw new RDSExceptions.ParamValidationException("paymentTiming can't be null.");
        }
        if (request.getEngine() == null) {
            throw new RDSExceptions.ParamValidationException("engine can't be null.");
        }
        if (request.getEngineVersion() == null) {
            throw new RDSExceptions.ParamValidationException("engineVersion can't be null.");
        }
        if (request.getCpuCount() == null) {
            throw new RDSExceptions.ParamValidationException("cpuCount can't be null.");
        }
        if (request.getMemoryCapacity() == null) {
            throw new RDSExceptions.ParamValidationException("memoryCapacity can't be null.");
        }
        if (request.getMemoryCapacity() < 1) {
            throw new RDSExceptions.ParamValidationException("memoryCapacity can't less than one.");
        }
        if (request.getVolumeCapacity() == null) {
            throw new RDSExceptions.ParamValidationException("volumeCapacity can't be null.");
        }
        if (request.getVolumeCapacity() < 5) {
            throw new RDSExceptions.ParamValidationException("volumeCapacity can't less than five.");
        }

        BaseCreateOrderRequestVo<InstanceCreateModel> baseCreateOrderRequestVo = new BaseCreateOrderRequestVo<>();
        baseCreateOrderRequestVo.setPaymentMethod(new HashSet<PaymentModel>(0));
        baseCreateOrderRequestVo.setItems(new ArrayList<BaseCreateOrderRequestVo.Item<InstanceCreateModel>>(1));
        baseCreateOrderRequestVo.getItems().add(new BaseCreateOrderRequestVo.Item<InstanceCreateModel>());
        baseCreateOrderRequestVo.getItems().get(0).setPaymentMethod(new HashSet<PaymentModel>(0));
        baseCreateOrderRequestVo.getItems().get(0).setConfig(new InstanceCreateModel());
        InstanceCreateModel instanceCreateModel = baseCreateOrderRequestVo.getItems().get(0).getConfig();
        if (request.getPurchaseCount() == null) {
            instanceCreateModel.setNumber(1);
        } else {
            instanceCreateModel.setNumber(request.getPurchaseCount());
        }
        instanceCreateModel.setProductType(request.getBilling().getPaymentTiming());
        if (request.getBilling().getReservation() != null
                && request.getBilling().getReservation().getReservationLength() != null) {
            instanceCreateModel.setDuration(request.getBilling().getReservation().getReservationLength());
        }
        if (request.getAutoRenewTimeUnit() != null) {
            instanceCreateModel.setAutoRenewTimeUnit(request.getAutoRenewTimeUnit());
        }
        if (request.getAutoRenewTime() != null) {
            instanceCreateModel.setAutoRenewTime(request.getAutoRenewTime());
        }
        if (request.getIsDirectPay() != null) {
            instanceCreateModel.setIsDirectPay(request.getIsDirectPay());
        }
        // 支持批量创建主实例
        if (request.getPurchaseCount() != null) {
            instanceCreateModel.setNumber(request.getPurchaseCount());
        }
        instanceCreateModel.setInstance(new InstanceCreateModel.DashCreateInstance());

        InstanceCreateModel.DashCreateInstance instance = instanceCreateModel.getInstance();
        instance.setEngine(request.getEngine());
        instance.setEngineVersion(request.getEngineVersion());
        instance.setCharacterSetName(request.getCharacterSetName());
        instance.setInstanceName(request.getInstanceName());
        if (request.getCategory() != null) {
            instance.setCategory(request.getCategory());
        } else {
            instance.setCategory("Standard");
        }
        instance.setCpuCount(request.getCpuCount());
        instance.setAllocatedMemoryInGB(request.getMemoryCapacity());
        instance.setAllocatedStorageInGB(request.getVolumeCapacity());
        if (StringUtils.isNotBlank(request.getReplicationType())) {
            instance.setReplicationType(request.getReplicationType());
        } else {
            instance.setReplicationType("async");
        }
        if (request.getZoneNames() != null) {
            instance.setZoneNames(request.getZoneNames());
        } else {
            instance.setZoneNames(new ArrayList<String>(0));
        }
        instance.setVpcId(request.getVpcId());
        if (request.getSubnets() != null) {
            instance.setSubnets(new ArrayList<InstanceCreateModel.SubnetMap>(request.getSubnets().size()));
            for (OpenApiCreateInstanceRequest.SubnetMap subnetMap : request.getSubnets()) {
                InstanceCreateModel.SubnetMap theSubnetMap = new InstanceCreateModel.SubnetMap();
                theSubnetMap.setSubnetId(subnetMap.getSubnetId());
                theSubnetMap.setZoneName(subnetMap.getZoneName());

                instance.getSubnets().add(theSubnetMap);
            }
        } else {
            instance.setSubnets(new ArrayList<InstanceCreateModel.SubnetMap>(0));
        }
        instance.setRelationTag(request.getRelationTag());
        if (request.getTags() != null) {
            instance.setTags(request.getTags());
        } else {
            instance.setTags(new ArrayList<Tag>(0));
        }
        // 设置专属集群BLB ID
        if (StringUtils.isNotBlank(request.getBgwGroupId())) {
            instance.setBgwGroupId(request.getBgwGroupId());
            instance.setBgwGroupExclusive(true);
        }
        if (request.getIsDataBackupCopy()) {
            instance.setIsDataBackupCopy(request.getIsDataBackupCopy());
        }
        if (StringUtils.isNotEmpty(request.getResourcePlatform())) {
            instance.setResourcePlatform(request.getResourcePlatform());
        }
        if (StringUtils.isNotEmpty(request.getSupportStorageEngine())) {
            instance.setSupportStorageEngine(request.getSupportStorageEngine());
        }
        if (request.getInitialDataReference() != null) {
            // 此处需限制下逻辑备份的情况,若克隆实例时使用了逻辑备份，需拦截
            if (StringUtils.isNotEmpty(request.getInitialDataReference().getInstanceId()) &&
                      StringUtils.isNotEmpty(request.getInitialDataReference().getSnapshotId())) {
                // 长短ID兼容
                request.getInitialDataReference().setInstanceId(idMapperService.getInstanceUuid
                        (request.getInitialDataReference().getInstanceId()));
                SnapshotGetResponse snapshotGetResponse = clientFactory.createRdsClient2().
                        snapshotGet(request.getInitialDataReference().getInstanceId(),
                                request.getInitialDataReference().getSnapshotId(), 43200);
                if (StringUtils.isNotEmpty(snapshotGetResponse.getSnapshot().getDataBackupType()) &&
                        RDSConstant.BACK_UP_TYPE_LOGICAL.equalsIgnoreCase
                                (snapshotGetResponse.getSnapshot().getDataBackupType())) {
                    throw new RDSBusinessExceptions.BackupParameterException();
                }
            }

            instance.setInitialDataReference(request.getInitialDataReference());
        }

        if (request.getData() != null) {
            instance.setData(request.getData());
        }

        if (StringUtils.isNotBlank(request.getDiskType())){
            instance.setDiskType(request.getDiskType());
        }

        if (StringUtils.isNotBlank(request.getCdsType())){
            instance.setCdsType(request.getCdsType());
        }

        if (StringUtils.isNotBlank(request.getDiskIoType())){
            instance.setDiskIoType(request.getDiskIoType());
        }

        if (StringUtils.isNotBlank(request.getParameterTemplateId())){
            instance.setParameterTemplateId(request.getParameterTemplateId());
        }

        if (request.getBcmGroupName() != null) {
            instance.setBcmGroupName(request.getBcmGroupName());
        }

        if (request.getLeaderInstanceId() != null) {
            instance.setLeaderAppId(request.getLeaderInstanceId());
            instance.setLeaderInstanceId(request.getLeaderInstanceId());
        }
        if (request.getResourceType() != null) {
            instance.setResourceType(request.getResourceType());
        }
        instance.setOvip(request.getOvip());
        instance.setEntryPort(request.getEntryPort());
        instance.setResourceGroupId(request.getResourceGroupId());
        instance.setLowerCaseTableNames(request.getLowerCaseTableNames());

        return baseCreateOrderRequestVo;
    }

    private BaseCreateOrderRequestVo<InstanceCreateModel> convertForCreateReadReplicaInstance(
            OpenApiCreateInstanceRequest request) {
        if (request.getBilling() == null) {
            throw new RDSExceptions.ParamValidationException("billing can't be null.");
        }
        if (request.getBilling().getPaymentTiming() == null) {
            throw new RDSExceptions.ParamValidationException("paymentTiming can't be null.");
        }
        if (request.getSourceInstanceId() == null) {
            throw new RDSExceptions.ParamValidationException("sourceInstanceId can't be null.");
        }
        if (request.getCpuCount() == null) {
            throw new RDSExceptions.ParamValidationException("cpuCount can't be null.");
        }
        if (request.getMemoryCapacity() == null) {
            throw new RDSExceptions.ParamValidationException("memoryCapacity can't be null.");
        }
        if (request.getMemoryCapacity() < 1) {
            throw new RDSExceptions.ParamValidationException("memoryCapacity can't less than one.");
        }
        if (request.getVolumeCapacity() == null) {
            throw new RDSExceptions.ParamValidationException("volumeCapacity can't be null.");
        }
        if (request.getVolumeCapacity() < 5) {
            throw new RDSExceptions.ParamValidationException("volumeCapacity can't less than five.");
        }

        BaseCreateOrderRequestVo<InstanceCreateModel> baseCreateOrderRequestVo = new BaseCreateOrderRequestVo<>();
        baseCreateOrderRequestVo.setPaymentMethod(new HashSet<PaymentModel>(0));
        baseCreateOrderRequestVo.setItems(new ArrayList<BaseCreateOrderRequestVo.Item<InstanceCreateModel>>(1));
        baseCreateOrderRequestVo.getItems().add(new BaseCreateOrderRequestVo.Item<InstanceCreateModel>());
        baseCreateOrderRequestVo.getItems().get(0).setPaymentMethod(new HashSet<PaymentModel>(0));
        baseCreateOrderRequestVo.getItems().get(0).setConfig(new InstanceCreateModel());
        InstanceCreateModel instanceCreateModel = baseCreateOrderRequestVo.getItems().get(0).getConfig();

        instanceCreateModel.setNumber(1);
        instanceCreateModel.setProductType(request.getBilling().getPaymentTiming());
        if (request.getBilling().getReservation() != null
                && request.getBilling().getReservation().getReservationLength() != null) {
            instanceCreateModel.setDuration(request.getBilling().getReservation().getReservationLength());
        }
        if (request.getAutoRenewTimeUnit() != null) {
            instanceCreateModel.setAutoRenewTimeUnit(request.getAutoRenewTimeUnit());
        }
        if (request.getAutoRenewTime() != null) {
            instanceCreateModel.setAutoRenewTime(request.getAutoRenewTime());
        }
        if (request.getIsDirectPay() != null) {
            instanceCreateModel.setIsDirectPay(request.getIsDirectPay());
        }
        // 支持批量创建
        if (request.getPurchaseCount() != null) {
            instanceCreateModel.setNumber(request.getPurchaseCount());
        }
        instanceCreateModel.setInstance(new InstanceCreateModel.DashCreateInstance());

        InstanceCreateModel.DashCreateInstance instance = instanceCreateModel.getInstance();
        instance.setEngine("readReplica");
        instance.setSourceInstanceId(request.getSourceInstanceId());
        instance.setInstanceName(request.getInstanceName());
        instance.setCpuCount(request.getCpuCount());
        instance.setAllocatedMemoryInGB(request.getMemoryCapacity());
        instance.setAllocatedStorageInGB(request.getVolumeCapacity());
        if (request.getIsInheritMasterAuthip() != null) {
            instance.setIsInheritMasterAuthip(request.getIsInheritMasterAuthip());
        }
        if (StringUtils.isNotEmpty(request.getReplicaType())) {
            instance.setReplicaType(request.getReplicaType());
        }
        if (request.getZoneNames() != null) {
            instance.setZoneNames(request.getZoneNames());
        } else {
            instance.setZoneNames(new ArrayList<String>(0));
        }

        if (request.getBcmGroupName() != null) {
            instance.setBcmGroupName(request.getBcmGroupName());
        }

        instance.setVpcId(request.getVpcId());
        if (request.getSubnets() != null) {
            instance.setSubnets(new ArrayList<InstanceCreateModel.SubnetMap>(request.getSubnets().size()));
            for (OpenApiCreateInstanceRequest.SubnetMap subnetMap : request.getSubnets()) {
                InstanceCreateModel.SubnetMap theSubnetMap = new InstanceCreateModel.SubnetMap();
                theSubnetMap.setSubnetId(subnetMap.getSubnetId());
                theSubnetMap.setZoneName(subnetMap.getZoneName());

                instance.getSubnets().add(theSubnetMap);
            }
        } else {
            instance.setSubnets(new ArrayList<InstanceCreateModel.SubnetMap>(0));
        }
        instance.setRelationTag(request.getRelationTag());
        if (request.getTags() != null) {
            instance.setTags(request.getTags());
        } else {
            instance.setTags(new ArrayList<Tag>(0));
        }
        if (request.getInitialDataReference() != null) {
            instance.setInitialDataReference(request.getInitialDataReference());
        }

        if (request.getData() != null) {
            instance.setData(request.getData());
        }

        if (StringUtils.isNotBlank(request.getDiskType())){
            instance.setDiskType(request.getDiskType());
        }

        if (StringUtils.isNotBlank(request.getCdsType())){
            instance.setCdsType(request.getCdsType());
        }

        if (StringUtils.isNotBlank(request.getDiskIoType())){
            instance.setDiskIoType(request.getDiskIoType());
        }

        if (StringUtils.isNotBlank(request.getParameterTemplateId())){
            instance.setParameterTemplateId(request.getParameterTemplateId());
        }
        instance.setOvip(request.getOvip());
        // api 创建只读实例时，若未填写端口，默认和主一致
        if (request.getEntryPort() == null && StringUtils.isNotEmpty(request.getSourceInstanceId())) {
            String instanceUuid = idMapperService.getInstanceUuid(request.getSourceInstanceId());
            InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(instanceUuid)
                    .instanceDescribe(instanceUuid);
            if (instanceGetResponse.getInstance().getEndpoint().getPort() != null) {
                instance.setEntryPort(instanceGetResponse.getInstance().getEndpoint().getPort());
            }
        } else {
            instance.setEntryPort(request.getEntryPort());
        }
        instance.setResourceGroupId(request.getResourceGroupId());
        instance.setLowerCaseTableNames(request.getLowerCaseTableNames());

        return baseCreateOrderRequestVo;
    }

    private static BaseCreateOrderRequestVo<InstanceCreateModel> convertForCreateProxyInstance(
            OpenApiCreateInstanceRequest request) {
        if (request.getBilling() == null) {
            throw new RDSExceptions.ParamValidationException("billing can't be null.");
        }
        if (request.getBilling().getPaymentTiming() == null) {
            throw new RDSExceptions.ParamValidationException("paymentTiming can't be null.");
        }
        if (request.getSourceInstanceId() == null) {
            throw new RDSExceptions.ParamValidationException("sourceInstanceId can't be null.");
        }
        if (request.getNodeAmount() == null) {
            throw new RDSExceptions.ParamValidationException("nodeAmount can't be null.");
        }

        BaseCreateOrderRequestVo<InstanceCreateModel> baseCreateOrderRequestVo = new BaseCreateOrderRequestVo<>();
        baseCreateOrderRequestVo.setPaymentMethod(new HashSet<PaymentModel>(0));
        baseCreateOrderRequestVo.setItems(new ArrayList<BaseCreateOrderRequestVo.Item<InstanceCreateModel>>(1));
        baseCreateOrderRequestVo.getItems().add(new BaseCreateOrderRequestVo.Item<InstanceCreateModel>());
        baseCreateOrderRequestVo.getItems().get(0).setPaymentMethod(new HashSet<PaymentModel>(0));
        baseCreateOrderRequestVo.getItems().get(0).setConfig(new InstanceCreateModel());
        InstanceCreateModel instanceCreateModel = baseCreateOrderRequestVo.getItems().get(0).getConfig();
        instanceCreateModel.setNumber(1);
        instanceCreateModel.setProductType(request.getBilling().getPaymentTiming());
        if (request.getBilling().getReservation() != null
                && request.getBilling().getReservation().getReservationLength() != null) {
            instanceCreateModel.setDuration(request.getBilling().getReservation().getReservationLength());
        }
        if (request.getAutoRenewTimeUnit() != null) {
            instanceCreateModel.setAutoRenewTimeUnit(request.getAutoRenewTimeUnit());
        }
        if (request.getAutoRenewTime() != null) {
            instanceCreateModel.setAutoRenewTime(request.getAutoRenewTime());
        }
        if (request.getIsDirectPay() != null) {
            instanceCreateModel.setIsDirectPay(request.getIsDirectPay());
        }
        instanceCreateModel.setInstance(new InstanceCreateModel.DashCreateInstance());

        InstanceCreateModel.DashCreateInstance instance = instanceCreateModel.getInstance();
        instance.setEngine("rdsproxy");
        instance.setSourceInstanceId(request.getSourceInstanceId());
        instance.setInstanceName(request.getInstanceName());
        instance.setNodeAmount(request.getNodeAmount());
        if (request.getZoneNames() != null) {
            instance.setZoneNames(request.getZoneNames());
        } else {
            instance.setZoneNames(new ArrayList<String>(0));
        }
        instance.setVpcId(request.getVpcId());
        if (request.getSubnets() != null) {
            instance.setSubnets(new ArrayList<InstanceCreateModel.SubnetMap>(request.getSubnets().size()));
            for (OpenApiCreateInstanceRequest.SubnetMap subnetMap : request.getSubnets()) {
                InstanceCreateModel.SubnetMap theSubnetMap = new InstanceCreateModel.SubnetMap();
                theSubnetMap.setSubnetId(subnetMap.getSubnetId());
                theSubnetMap.setZoneName(subnetMap.getZoneName());

                instance.getSubnets().add(theSubnetMap);
            }
        } else {
            instance.setSubnets(new ArrayList<InstanceCreateModel.SubnetMap>(0));
        }
        instance.setRelationTag(request.getRelationTag());
        if (request.getTags() != null) {
            instance.setTags(request.getTags());
        } else {
            instance.setTags(new ArrayList<Tag>(0));
        }
        if (request.getInitialDataReference() != null) {
            instance.setInitialDataReference(request.getInitialDataReference());
        }

        if (request.getData() != null) {
            instance.setData(request.getData());
        }

        if (StringUtils.isNotBlank(request.getDiskType())){
            instance.setDiskType(request.getDiskType());
        }

        if (StringUtils.isNotBlank(request.getCdsType())){
            instance.setCdsType(request.getCdsType());
        }

        if (StringUtils.isNotBlank(request.getDiskIoType())){
            instance.setDiskIoType(request.getDiskIoType());
        }

        if (StringUtils.isNotBlank(request.getParameterTemplateId())){
            instance.setParameterTemplateId(request.getParameterTemplateId());
        }
        instance.setOvip(request.getOvip());
        instance.setEntryPort(request.getEntryPort());
        instance.setResourceGroupId(request.getResourceGroupId());
        instance.setLowerCaseTableNames(request.getLowerCaseTableNames());

        return baseCreateOrderRequestVo;
    }

    /**
     * 按备份集点恢复指定库/表到原实例或指定实例
     * 该接口用于按备份集点恢复指定库/表到原实例或指定实例，支持MySQL主实例
     *
     * @param instanceId 实例ID
     * @param request    恢复请求对象
     * @param from       请求来源，默认为空字符串
     * @throws Exception 恢复失败时抛出异常
     */
    @ApiOperation(value = "按备份集点恢复指定库/表到原实例或指定实例")
    @RequestMapping(value = "/{instanceId}/recoveryToSourceInstanceBySnapshot", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void recoveryToSourceInstanceBySnapshot(@IdPermission @IdMapper @PathVariable String instanceId,
                                                   @RequestBody @Valid RecoveryToSourceInstanceRequest request,
                                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("recoveryToSourceInstanceBySnapshot. instanceId is {}", instanceId);
        // 此处逻辑层兼容请求参数 sourceInstanceId
        request.setSourceInstanceId(instanceId);
        if (StringUtils.isNotEmpty(request.getTargetInstanceId())
                && !request.getTargetInstanceId().equalsIgnoreCase(instanceId)) {
            // 该操作为恢复至指定实例
            instanceService.recoveryToSourceInstanceBySnapshot(request.getTargetInstanceId(), request, from);
        } else {
            // 此为恢复至原实例
            instanceService.recoveryToSourceInstanceBySnapshot(instanceId, request, from);
        }
    }

    /**
     * 按时间点恢复指定库/表到原实例或指定实例
     * 该接口用于按时间点恢复指定库/表到原实例或指定实例，支持MySQL主实例
     *
     * @param instanceId 实例ID
     * @param request    恢复请求对象，包含恢复信息
     * @param from       请求来源，默认为空字符串
     * @throws Exception 恢复失败时抛出异常
     */
    @ApiOperation(value = "按时间点恢复指定库/表到原实例或指定实例")
    @RequestMapping(value = "/{instanceId}/recoveryToSourceInstanceByDatetime", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void recoveryToSourceInstanceByDatatime(@IdPermission @IdMapper @PathVariable String instanceId,
                                                   @RequestBody @Valid RecoveryToSourceInstanceRequest request,
                                                   @RequestParam(required = false, defaultValue = "") String from) {
        // 由于模块位置原因，此处手动进行实体类内长短 ID 的转换
        request.setSourceInstanceId(instanceId);
        request.setTargetInstanceId(idMapperService.getInstanceUuid(request.getTargetInstanceId()));
        LOGGER.debug("recoveryToSourceInstanceByDatatime. instanceId is {}", instanceId);
        if (StringUtils.isNotEmpty(request.getTargetInstanceId())
                && !request.getTargetInstanceId().equalsIgnoreCase(instanceId)) {
            // 该操作为恢复至指定实例
            instanceService.recoveryToSourceInstanceByTime(request.getTargetInstanceId(), request, from);
        } else {
            // 该操作为恢复至原实例
            instanceService.recoveryToSourceInstanceByTime(instanceId, request, from);
        }
    }

    /**
     * 检查实例是否支持热变配
     * 检查实例是否支持热变配该接口用于检查实例是否支持热变配
     *
     * @param instanceId 实例ID
     * @param request    热变配请求对象
     * @return HotUpgradeResponse 热变配响应对象
     */
    @ApiOperation(value = "检查实例是否支持热变配")
    @RequestMapping(value = "/{instanceId}/checkHotUpgrade", method = RequestMethod.POST)
    public HotUpgradeResponse checkHotUpgrade(@PathVariable String instanceId,
                                              @RequestBody HotupgradeRequest request) {
        LOGGER.debug("get pnetIp. instanceId is {}", instanceId);
        HotUpgradeResponse response = new HotUpgradeResponse();
        try {
            response = instanceService.checkHotUpgrade(instanceId, request);

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    /**
     * 暂停实例
     * 该接口用于暂停实例，暂停后实例将不再产生费用。请谨慎操作
     *
     * @param instanceId 实例ID
     * @throws Exception 暂停实例时发生的异常
     */
    @RequestMapping(value = "/{instanceId}/suspend", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX, ids = "*")
    public void suspend(
            @IdPermission @IdMapper @PathVariable String instanceId) {
        try {
            instanceService.suspend(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 启动实例
     * 该接口用于启动实例，启动后实例将开始产生费用。请谨慎操作
     *
     * @param instanceId 实例ID
     * @throws Exception 启动实例时发生的异常
     */
    @RequestMapping(value = "/{instanceId}/start", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX, ids = "*")
    public void start(
            @IdPermission @IdMapper @PathVariable String instanceId) {
        try {
            instanceService.start(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 变更实例磁盘类型预检查
     * 该接口用于预检查变更实例磁盘类型，返回是否支持变更磁盘类型
     *
     * @param instanceId 实例ID
     * @param targetDiskType 目标磁盘类型
     * @return InstanceChangeDiskTypeResponse 变更实例磁盘类型预检查结果
     */
    @RequestMapping(value = "/{instanceId}/precheck/changeDiskType/{targetDiskType}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public InstanceChangeDiskTypeResponse changeDiskType(@IdPermission @IdMapper @PathVariable String instanceId,
                                                         @PathVariable String targetDiskType) {
        try {
            InstanceChangeDiskTypeResponse res = instanceService.
                    changeDiskType(instanceId, targetDiskType);
            return res;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }

    /**
     * 对实例参数进行预检查
     * 该接口用于对实例参数进行预检查，返回是否支持修改
     *
     * @param instanceId 实例ID
     * @param request    实例参数预检查请求
     * @return InstancePrecheckParameterResponse 实例参数预检查结果
     */
    @RequestMapping(value = "/{instanceId}/precheck/parameter", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public InstancePrecheckParameterResponse precheckParameter(@IdPermission @IdMapper @PathVariable String instanceId,
                                                            @RequestBody InstancePrecheckParameterRequest request) {
        try {
            InstancePrecheckParameterResponse res = instanceService.
                    precheckParameter(instanceId, request);
            return res;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }

    /**
     * 修改接口
     * 修改实例端口该接口用于修改实例的访问端口
     *
     * @param instanceId 实例ID
     * @param request    修改实例端口的请求参数
     * @throws Exception 修改实例端口时发生的异常
     */
    @ApiOperation(value = "修改接口")
    @RequestMapping(value = "/{instanceId}/port", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateInstancePort(@IdPermission @IdMapper @PathVariable String instanceId,
                                   @RequestBody @Valid InstanceUpdatePortRequest request) {


        instanceService.updateInstancePort(instanceId, request);

    }

    /**
     * 获取订单状态
     * 该接口用于获取订单状态，包括已创建的实例、续费和变更配置等
     *
     * @param orderId 订单ID
     * @return OrderStatusResponse 订单状态响应
     * @throws Exception 查询订单状态时发生异常
     */
    @RequestMapping(value = "/order/{orderId}", method = RequestMethod.GET)
    public OrderStatusResponse getOrderStatus(
            @PathVariable String orderId) {
        try {
            OrderStatusResponse response = instanceService.getOrderStatus(orderId);
            return response;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }

    /**
     * 校验资源创建请求
     * 该接口用于校验资源创建请求
     *
     * @param request 包含校验所需参数的PrecheckResourceCreateRequest对象
     * @return 包含校验结果的PrecheckResourceCreateResponse对象
     */
    @RequestMapping(value = "/precheck/resourceCreate", method = RequestMethod.POST)
    public PrecheckResourceCreateResponse precheckResourceCreate(
            @RequestBody PrecheckResourceCreateRequest request) {

        PrecheckResourceCreateResponse res = instanceService.
                precheckResource(request);
        return res;
    }



    /**
     * MySQL+PG支持可用区迁移
     * 该接口用于迁移实例的可用区，支持MySQL和PG
     *
     * @param instanceId 实例ID
     * @param request 包含迁移信息的InstanceAzone对象
     * @throws Exception 迁移失败时抛出异常
     */
    @ApiOperation(value = "MySQL+PG支持可用区迁移")
    @RequestMapping(value = "/{instanceId}/azoneMigration", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void azoneMigration(@IdPermission @PathVariable @IdMapper String instanceId,
                               @RequestBody @Valid InstanceAzone request) {
        LOGGER.debug("azone migration. instanceId is {}", instanceId);
        AzoneInfo azoneInfo = new AzoneInfo();
        request.setAzone(instanceService.getZoneUuid(request.getZoneNames()));
        request.setSubnetId(instanceService.getSubnetUuid(request.getSubnets()));
        request.setLogicalZoneMaster(othersService.apiZoneTologicalZone(request.getLogicalZoneMaster()));
        request.setLogicalZoneBackup(othersService.apiZoneTologicalZone(request.getLogicalZoneBackup()));
        azoneInfo.setInstanceParameters(request);
        azoneInfo.setEffectiveTime(request.getEffectiveTime());
        instanceService.azoneMigration(instanceId, azoneInfo);
    }

    /**
     * 修改实例的维护时间窗口
     * 该接口用于修改实例的维护时间窗口
     *
     * @param instanceId 实例ID
     * @param request 包含维护时间窗口信息的MaintainDurationRequest对象
     * @throws RDSBusinessExceptions.InstanceNotFoundException 如果实例不存在
     * @throws RDSBusinessExceptions.MissingParameterException 如果请求中缺少必要参数
     * @throws RDSBusinessExceptions.InstanceNotSatisfiableException 如果实例不满足操作要求
     * @throws RDSBusinessExceptions.InvalidParameterException 如果请求中的参数无效
     * @throws RDSBusinessExceptions.InstanceStatusErrorException 如果实例状态错误
     * @throws RDSBusinessExceptions.InternalDBErrorException 如果内部数据库错误
     * @throws RDSBusinessExceptions.InternalServerErrorException 如果服务器内部错误
     */
    @ApiOperation(value = "修改实例的维护时间窗口")
    @RequestMapping(value = "/{instanceId}/maintaintime", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            type = RDSConstant.ID_PREFIX)
    public void updateMaintaintime(@IdPermission @PathVariable @IdMapper String instanceId,
                                   @RequestBody MaintainDurationRequest request) {
        try {
            instanceService.updateMaintaintime(instanceId, request);
        } catch (BceInternalResponseException e) {
            if ("RDSInstanceNotFound".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceNotFoundException();
            }
            else if ("MissingParameter".equals(e.getCode())) {
                throw new RDSBusinessExceptions.MissingParameterException();
            }
            else if ("InstanceNotSatisfiable ".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceNotSatisfiableException();
            }
            else if ("InvalidParameterValue".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InvalidParameterException();
            }
            else if ("InstanceStatusError".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceStatusErrorException();
            }
            else if ("InternalDBError".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InternalDBErrorException();
            }
            else {
                throw new RDSBusinessExceptions.InternalServerErrorException();
            }
        }
    }
    /**
     * 获取用户当前地域下的时间窗口内执行的任务列表
     * 该接口用于获取用户当前地域下的时间窗口内执行的任务列表
     *
     * @param request 包含任务查询信息的TaskRequest对象，可以为空
     * @return 返回MaintaintimeTasksResponse对象，包含任务列表信息
     * @throws RDSBusinessExceptions.InstanceNotFoundException 如果实例不存在
     * @throws RDSBusinessExceptions.InternalServerErrorException 如果服务器内部错误
     */
    @ApiOperation(value = "获取用户当前地域下的时间窗口内执行的任务列表")
    @RequestMapping(value = "/maintaintime/task", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public MaintaintimeTasksResponse getMaintaintimeTasks(
            @IdPermission @RequestBody(required = false) TaskRequest request) {

        MaintaintimeTasksResponse tasks = new MaintaintimeTasksResponse();
        if (request == null) {
            request = new TaskRequest();
        }
        tasks = instanceService.getMaintaintimeTasksResponse(request);
        return tasks;
    }

    /**
     * 发起主备切换，功能迭代，向前兼容接口
     * 该接口用于发起主备切换，功能迭代，向前兼容接口
     *
     * @param instanceId 实例ID
     * @param request 包含主备切换请求信息的SwitchMasterBackupRequest对象
     * @throws RDSBusinessExceptions.InstanceNotFoundException 如果实例不存在
     * @throws RDSBusinessExceptions.InternalServerErrorException 如果服务器内部错误
     */
    @RequestMapping(value = "/{instanceId}/masterBackupInfo", method = RequestMethod.PUT)
    @ApiOperation(value = "发起主备切换，功能迭代，向前兼容接口")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void switchMasterBackup(@IdPermission @PathVariable @IdMapper String instanceId,
                                   @RequestBody SwitchMasterBackupRequest request) {
        String from = "api";
        instanceService.switchMasterBackup(instanceId, request, from);

    }

    /**
     * 发起主备切换, 推荐使用
     * 该接口用于发起主备切换, 推荐使用
     *
     * @param instanceId 实例ID
     * @param request 包含主备切换请求信息的SwitchMasterBackupRequest对象
     * @param from 请求来源，默认为空字符串
     * @throws RDSBusinessExceptions.InstanceNotFoundException 如果实例不存在
     * @throws RDSBusinessExceptions.InternalServerErrorException 如果服务器内部错误
     */
    @RequestMapping(value = "/{instanceId}/switchMasterBackup", method = RequestMethod.PUT)
    @ApiOperation(value = "发起主备切换, 推荐使用")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void switchMasterBackup(@IdPermission @PathVariable @IdMapper String instanceId,
                                   @RequestBody SwitchMasterBackupRequest request,
                                   @RequestParam(required = false, defaultValue = "") String from) {
        from = "api";
        instanceService.switchMasterBackup(instanceId, request, from);

    }

    /**
     * 查询集群状态
     * 该接口用于查询集群状态，包括实例是否正常、检查列表和集群状态
     *
     * @param instanceId 实例ID
     * @return 集群状态响应
     * @throws RDSBusinessExceptions.InstanceNotFoundException 如果实例不存在
     * @throws RDSBusinessExceptions.InternalServerErrorException 如果服务器内部错误
     */
    @RequestMapping(value = "/{instanceId}/status", method = RequestMethod.GET)
    @ApiOperation(value = "查询集群状态")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ClusterStatusResponce clusterStatus(
            @PathVariable @IdMapper @IdPermission String instanceId) {
        return instanceService.clusterStatus(instanceId);
    }

    /**
     * 拨测接口
     * 该接口用于拨测实例是否正常，包括实例是否存活和错误信息
     *
     * @param instanceId 实例ID
     * @return 返回拨测结果
     * @throws RDSBusinessExceptions.InstanceNotFoundException 如果实例不存在
     * @throws RDSBusinessExceptions.InternalServerErrorException 如果服务器内部错误
     */
    @ApiOperation(value = "拨测接口")
    @RequestMapping(value = "/{instanceId}/probe", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ProbeInstanceResponse probeInstance(@IdPermission @IdMapper @PathVariable String instanceId) {
        return instanceService.probeInstance(instanceId);
    }

    /**
     * SLA 接口
     * 该接口用于获取实例的SLA信息，包括周内平均值和比率
     *
     * @param instanceId 实例ID
     * @return SlaInstanceResponse SLA接口响应结果
     * @throws RDSBusinessExceptions.InstanceNotFoundException 如果实例不存在
     * @throws RDSBusinessExceptions.InternalServerErrorException 如果服务器内部错误
     */
    @ApiOperation(value = "SLA 接口")
    @RequestMapping(value = "/{instanceId}/availability", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlaInstanceResponse availableInstance(@IdPermission @IdMapper @PathVariable String instanceId) {
        return instanceService.availableInstance(instanceId);
    }

}
