package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryBaseResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryChartRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryChartResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDetailRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDetailResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDownloadRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDownloadResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryGetSqlRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryGetSqlResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryInstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryOpenStatusResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlExplainResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerFeedbackRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryStartOrStopResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySummaryRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySummaryResponse;
import com.baidu.bce.logic.rds.service.SlowqueryService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import java.util.List;

/**
 * Created by chensilan on 15/11/27.
 */
@Controller
@RequestMapping(value = "/api/rds/slowquery", produces = {"application/json"})
@Api(value = "RDS Dashboard Slowlog管理API")
public class ConsoleApiSlowqueryController {

    @Autowired
    private SlowqueryService slowqueryService;
    /**
      * 获取慢查询图表信息
      * 通过POST请求调用接口，获取慢查询图表数据
      * 
      * @param requestBody 慢查询图表请求参数，包含必要的信息以生成图表
      * @return 慢查询图表响应数据
      */

    @RequestMapping(value = "chart", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryChartResponse chart(@IdPermission @Valid @RequestBody SlowqueryChartRequest requestBody) {
        return slowqueryService.chart(requestBody, null);
    }
    /**
      * 获取慢查询详情
      * 该函数用于处理慢查询详情的请求，验证权限后，调用服务层方法获取慢查询详情
      * 
      * @param requestBody 慢查询详情请求体，包含查询所需的参数
      * @return 慢查询详情响应体
      */

    @RequestMapping(value = "detail", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryDetailResponse detail(@IdPermission @Valid @RequestBody SlowqueryDetailRequest requestBody) {
        return slowqueryService.detail(requestBody, null);
    }
    /**
      * 获取慢查询摘要信息
      * 根据传入的请求体，调用服务获取慢查询摘要信息
      * 
      * @param requestBody 慢查询摘要请求体
      * @return 慢查询摘要响应体
      */

    @RequestMapping(value = "summary", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowquerySummaryResponse summary(@IdPermission @Valid @RequestBody SlowquerySummaryRequest requestBody) {
        return slowqueryService.summary(requestBody, null);
    }
    /**
      * 下载文件接口
      * 用于处理下载文件的请求，验证用户权限并调用服务层方法执行下载操作
      * 
      * @param requestBody 下载文件请求体，包含下载所需的参数信息
      * @return 下载文件的响应信息
      */

    @RequestMapping(value = "downloadfile", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryDownloadResponse downloadfile(
            @IdPermission @Valid @RequestBody SlowqueryDownloadRequest requestBody) {
        return slowqueryService.downloadfile(requestBody);
    }
    /**
      * 获取SQL接口
      * 根据请求参数获取对应的SQL语句
      *
      * @param requestBody 请求体，包含获取SQL所需的各种参数
      * @return SlowqueryGetSqlResponse 获取SQL的结果
      */

    @RequestMapping(value = "getsql", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryGetSqlResponse getSql(@IdPermission @Valid @RequestBody SlowqueryGetSqlRequest requestBody) {
        return slowqueryService.getSql(requestBody);
    }
    /**
      * 查询慢查询的开启状态
      * 根据提供的实例ID，查询对应慢查询的开启状态
      *
      * @param requestBody 包含实例ID的请求体
      * @return 慢查询开启状态响应
      */

    @RequestMapping(value = "openstatus", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryOpenStatusResponse openStatus(
            @IdPermission @Valid @RequestBody SlowqueryInstanceIdRequest requestBody) {
        return slowqueryService.getOpenStatus(requestBody.getInstanceId(), null);
    }
    /**
      * 启动慢查询实例
      * 该函数用于处理启动慢查询实例的请求。它接收一个包含慢查询实例ID的请求体，并调用服务层的方法来处理该请求。
      * 
      * @param requestBody 包含慢查询实例ID的请求体
      * @return 慢查询实例启动或停止的响应
      */

    @RequestMapping(value = "start", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryStartOrStopResponse start(
            @IdPermission @Valid @RequestBody SlowqueryInstanceIdRequest requestBody) {
        return slowqueryService.start(requestBody);
    }
    /**
      * 停止慢查询实例
      * 用于停止指定的慢查询实例
      * 
      * @param requestBody 停止慢查询实例的请求体，包含实例ID等信息
      * @return 停止慢查询实例的响应
      */

    @RequestMapping(value = "stop", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryStartOrStopResponse stop(
            @IdPermission @Valid @RequestBody SlowqueryInstanceIdRequest requestBody) {
        return slowqueryService.stop(requestBody);
    }
    /**
      * SQL优化接口
      * 用于接收SQL优化请求，处理请求参数，并调用服务层进行SQL优化
      *
      * @param request SQL优化请求体，包含实例ID、数据库、执行时间、SQL MD5和SQL类型等信息
      * @return SQL优化结果
      */

    @RequestMapping(value = "sqlOptimizer", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowquerySqlOptimizerResponse sqlOptimizer(
            @IdPermission @Valid @RequestBody SlowquerySqlOptimizerRequest request) {
        SlowquerySqlOptimizerRequest slowquerySqlOptimizerRequest = new SlowquerySqlOptimizerRequest();
        slowquerySqlOptimizerRequest.setInstanceId(request.getInstanceId());
        slowquerySqlOptimizerRequest.setDatabase(request.getDatabase() == null ? "" : request.getDatabase());
        slowquerySqlOptimizerRequest.setExecuteTime(request.getExecuteTime() == null ? "" : request.getExecuteTime());
        slowquerySqlOptimizerRequest.setSqlMd5(request.getSqlMd5() == null ? "" : request.getSqlMd5());
        slowquerySqlOptimizerRequest.setSqlType(request.getSqlType() == null ? "" : request.getSqlType());
        return slowqueryService.sqlOptimizer(slowquerySqlOptimizerRequest);
    }
    /**
      * SQL解释接口
      * 该接口用于对SQL语句进行解释，获取SQL语句的执行计划和详细信息
      * 
      * @param requestBody 请求体，包含SQL语句的相关信息，如实例ID、数据库、执行时间、SQL的MD5值、SQL类型等
      * @return SlowquerySqlExplainResponse 返回对象，包含SQL解释的结果，如执行计划、SQL语句的详细信息等
      */

    @RequestMapping(value = "sqlExplain", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowquerySqlExplainResponse sqlExplain(
            @IdPermission @Valid @RequestBody SlowquerySqlOptimizerRequest requestBody) {
        SlowquerySqlOptimizerRequest request = new SlowquerySqlOptimizerRequest();
        request.setInstanceId(requestBody.getInstanceId());
        request.setDatabase(request.getDatabase() == null ? "" : request.getDatabase());
        request.setExecuteTime(request.getExecuteTime() == null ? "" : request.getExecuteTime());
        request.setSqlMd5(request.getSqlMd5() == null ? "" : request.getSqlMd5());
        request.setSqlType(request.getSqlType() == null ? "" : request.getSqlType());

        SlowquerySqlExplainResponse response = slowqueryService.sqlExplain(request);
        List<SlowquerySqlExplainResponse.ExplainSql> list = response.getResult().getExplainSql();
        int len = list.size();
        for (int i = 0;i < len;i++) {
            list.get(i).setExtra(list.get(i).getExtraInfo());
        }
        response.getResult().setExplainSql(list);
        return response;
    }
    /**
      * sql优化器反馈接口
      * 用于接收用户对sql优化器的反馈
      * 
      * @param requestBody 用户对sql优化器的反馈请求体
      * @return 慢查询基础响应对象
      */

    @RequestMapping(value = "sqlOptimizerFeedback", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryBaseResponse sqlOptimizerFeedback(@IdPermission @Valid @RequestBody
                                                                  SlowquerySqlOptimizerFeedbackRequest requestBody) {
        SlowquerySqlOptimizerFeedbackRequest request = new SlowquerySqlOptimizerFeedbackRequest();
        request.setInstanceId(requestBody.getInstanceId());
        request.setOptimizerId(requestBody.getOptimizerId() == null ? "" : requestBody.getOptimizerId());
        request.setScore(requestBody.getScore() == null ? "" : requestBody.getScore());
        request.setUserAdvice(requestBody.getUserAdvice() == null ? "" : requestBody.getUserAdvice());

        return slowqueryService.sqlOptimizerFeedback(request);
    }
}
