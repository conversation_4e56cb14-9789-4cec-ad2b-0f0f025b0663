package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.idempotent.annotation.Idempotent;
import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.security.OpenApiSecurityIp;
import com.baidu.bce.internalsdk.rds.model.security.SecurityIp;
import com.baidu.bce.internalsdk.rds.model.security.SecurityIpPutRequest;
import com.baidu.bce.internalsdk.rds.model.security.SslAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.security.SslGetCaResponse;
import com.baidu.bce.internalsdk.rds.model.security.SslInfoResponse;
import com.baidu.bce.logic.rds.service.WhiteListService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;

/**
 * Created by luping03 on 17/11/3.
 */
@RestController
@RequestMapping("/v1/instance")
public class OpenApiWhiteListController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private WhiteListService whiteListService;
    /**
      * 获取白名单列表
      * 根据instanceId获取对应白名单信息，并处理成OpenApiSecurityIp格式返回
      *
      * @param instanceId 实例ID
      * @param servletResponse HttpServletResponse对象，用于设置返回头信息
      * @return OpenApiSecurityIp对象，包含白名单信息
      */

    @ApiOperation(value = "获取白名单列表")
    @RequestMapping(value = "/{instanceId}/securityIp", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenApiSecurityIp getWhiteList(@IdPermission @IdMapper @PathVariable String instanceId,
                                          HttpServletResponse servletResponse) {
        LOGGER.debug("get whitelist.");
        SecurityIp response = null;
        OpenApiSecurityIp openApiSecurityIp = null;
        try {
            response = whiteListService.getWhiteList(instanceId);
            servletResponse.setHeader("ETag", response.getETag());

            openApiSecurityIp = new OpenApiSecurityIp();
            openApiSecurityIp.setEtag(response.getETag());
            if (response.getIp() != null) {
                openApiSecurityIp.setSecurityIps(new ArrayList<String>(response.getIp().size()));
                for (String ip : response.getIp()) {
                    openApiSecurityIp.getSecurityIps().add(ip);
                }
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return openApiSecurityIp;
    }
    /**
     * 设置白名单列表
     * 根据提供的instanceId和请求体，设置白名单。需要x-bce-if-match头信息，from为可选参数。
     *
     * @param instanceId 实例ID
     * @param request 白名单设置请求体
     * @param etag 匹配头信息
     * @param from 来源标记，可选参数
     */

    @ApiOperation(value = "设置白名单列表")
    @RequestMapping(value = "/{instanceId}/securityIp", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void setWhiteList(@IdPermission @IdMapper @PathVariable String instanceId,
                             @RequestBody @Valid SecurityIpPutRequest request,
                             @RequestHeader ("x-bce-if-match") String etag,
                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("set whitelist.");
        try {
            whiteListService.setWhiteList(instanceId, request, etag);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 获取SSL加密信息
     * 根据实例ID获取SSL加密信息
     * 
     * @param instanceId 实例ID
     * @param from 请求来源
     * @return SSL加密信息
     */

    @ApiOperation(value = "获取SSL加密信息")
    @RequestMapping(value = "/ssl/{instanceId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SslInfoResponse getSslState(@IdPermission @PathVariable String instanceId,
                                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get SslState.");
        SslInfoResponse response = null;
        try {
            response = whiteListService.getSslState(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 设置SSL
      * 该函数用于更新实例的SSL访问状态
      * 
      * @param instanceId 实例ID
      * @param request SSL访问请求体
      * @param from 请求来源
      * @throws Exception 异常信息
      */

    @ApiOperation(value = "设置SSL")
    @RequestMapping(value = "/ssl/{instanceId}", params = {"sslAccessible"}, method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void setSslState(@IdPermission @PathVariable String instanceId,
                            @RequestBody SslAccessibleRequest request,
                            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("set SslState.");
//        SslInfoResponse response = null;
        try {
            whiteListService.updateSslAccess(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
//        return response;
    }
    /**
      * 获取SSL加密证书
      * 通过GET请求获取SSL加密证书信息
      * 
      * @param from 来源（可选参数，默认值为空字符串）
      * @return SslGetCaResponse 返回SSL加密证书信息
      */

    @ApiOperation(value = "获取SSL加密证书")
    @RequestMapping(value = "/ssl/static/ca", method = RequestMethod.GET)
//    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
//            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX, ids = "*")
    public SslGetCaResponse getCa(@RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get ca.");
        SslGetCaResponse response = null;
        try {
            response = whiteListService.queryCa();
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
}
