package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.instance.ExtensionHistoryResp;
import com.baidu.bce.internalsdk.rds.model.instance.ExtensionListRequest;
import com.baidu.bce.internalsdk.rds.model.instance.ExtenstionListResponse;
import com.baidu.bce.internalsdk.rds.model.instance.OperateExtensionRequest;
import com.baidu.bce.logic.rds.service.ExtensionService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

@Controller
@RequestMapping(value = "/api/rds", produces = {"application/json"})
@Api(value = "RDS Pg 插件管理 API")
public class ConsoleApiPgExtensionController {

    @Autowired
    ExtensionService extensionService;

    @ApiOperation("查询插件列表")
    @RequestMapping(value = "/extension/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<ExtenstionListResponse> list(
            @IdPermission @Valid @RequestBody ExtensionListRequest request) {
        EdpResultResponse<ExtenstionListResponse> response = new EdpResultResponse<>();
        response.setResult(extensionService.list(request));
        return response;
    }

    @ApiOperation("安装、卸载插件")
    @RequestMapping(value = "/extension", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.OPERATE, RDSConstant.WRITE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> operateExtension(
            @IdPermission @Valid @RequestBody OperateExtensionRequest request) {
        EdpResultResponse<Boolean> response = new EdpResultResponse<>();
        extensionService.operateExtension(request);
        response.setResult(true);
        return response;
    }


    @ApiOperation("查看插件历史记录")
    @RequestMapping(value = "/extension/{instanceId}/history", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<ExtensionHistoryResp> historyList(
            @IdPermission @PathVariable("instanceId") String instanceId) {
        EdpResultResponse<ExtensionHistoryResp> response = new EdpResultResponse<>();
        response.setResult(extensionService.historyList(instanceId));
        return response;
    }

}
