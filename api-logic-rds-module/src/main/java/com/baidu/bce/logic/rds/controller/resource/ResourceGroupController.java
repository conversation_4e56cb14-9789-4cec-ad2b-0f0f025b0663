package com.baidu.bce.logic.rds.controller.resource;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.internalsdk.rds.model.resource.BindResourceRequest;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.resource.ResourceGroupService;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupTreeResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.HistoryGroupResRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.HistoryGroupResResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupDetailRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupDetailResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupListResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceGroupsPageInfo;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Created by luping03 on 17/10/10.
 */
@RestController
@RequestMapping("/v1/rds/resource")
public class ResourceGroupController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResourceGroupController.class);

    @Autowired
    private ResourceGroupService resourceGroupService;
    /**
     * 获取资源组
     * 根据传入的资源组名称和来源，获取资源组信息
     * 
     * @param name 资源组名称，可以为null
     * @param from 资源组来源，默认为空字符串
     * @return 资源组信息
     */

    @ApiOperation(value = "获取资源组")
    @RequestMapping(value = "/getGroup", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GroupTreeResponse getGroup(@RequestParam(required = false) String name,
                                              @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get resource group. name is {}", name);
        GroupTreeResponse response = null;
        try {
            response = resourceGroupService.getGroup(name, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 创建资源组
      * 创建资源组的接口，用于添加新的资源组
      *
      * @param request 资源组创建请求体，包含创建资源组所需的信息
      * @param from 请求来源，非必须字段，默认为空字符串
      * @return 创建资源组的结果
      */

    @ApiOperation(value = "创建资源组")
    @RequestMapping(value = "/createGroup", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public String createGroup(@Valid @RequestBody GroupRequest request,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create resource group.");
        String response = null;
        try {
            response = resourceGroupService.createGroup(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    /**
     * 修改资源组
     * 修改资源组信息
     *
     * @param request 资源组请求信息
     * @param from 请求来源
     * @param force 是否强制修改
     * @return 修改后的资源组信息
     */
    @ApiOperation(value = "修改资源组")
    @RequestMapping(value = "/updateGroup", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public String createGroup(@Valid @RequestBody GroupRequest request,
                              @RequestParam(required = false, defaultValue = "") String from,
                        @RequestParam(value = "force", defaultValue = "fasle", required = false) boolean force) {
        LOGGER.debug("update resource group.");
        String response = null;
        try {
            response = resourceGroupService.updateGroup(request, force, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 删除资源组
     * 删除指定的资源组信息
     * 
     * @param from 来源，非必需参数，默认值为空字符串
     * @param groupId 资源组ID
     */

    @ApiOperation(value = "删除资源组")
    @RequestMapping(value = "/deleteGroup", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void deleteGroup(@RequestParam(required = false, defaultValue = "") String from,
                              @RequestParam(value = "groupId") String groupId) {
        LOGGER.debug("delete resource group.");
        try {
            resourceGroupService.deleteGroup(groupId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 绑定资源
     * 绑定资源的接口，支持PUT请求，用于绑定资源
     * 
     * @param request 绑定资源请求体，包含绑定所需的信息
     * @param from 请求来源，默认为空字符串
     */

    @ApiOperation(value = "绑定资源")
    @RequestMapping(value = "/bindResource", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void bindResource(@Valid @RequestBody BindResourceRequest request,
                           @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("bind resource. ");
        try {
            resourceGroupService.changeBindResource(request, request.getForce(), RDSConstant.RESOURCE_BIND, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 解绑资源
     * 解绑资源接口，用于解绑用户或服务与资源之间的关系
     * 
     * @param request 解绑资源请求体，包含解绑所需的所有信息
     * @param from 解绑操作来源标识，非必需参数，默认为空字符串
     */

    @ApiOperation(value = "解绑资源")
    @RequestMapping(value = "/unbindResource", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void unbindResource(@Valid @RequestBody BindResourceRequest request,
                                        @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("unbind resource. ");
        try {
            resourceGroupService.changeBindResource(request, request.getForce(), RDSConstant.RESOURCE_UNBIND, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 分组关联的资源查询
     * 根据groupId和其他可选参数查询分组关联的资源信息
     * 
     * @param groupId 分组ID
     * @param id 资源ID
     * @param types 资源类型
     * @param regions 资源所在区域
     * @param name 资源名称
     * @param isCurrent 是否只查询当前分组下的资源
     * @param tags 资源标签
     * @param pageNo 分页页码
     * @param pageSize 分页大小
     * @param from 请求来源
     * @return 分组关联的资源信息
     */

    @ApiOperation(value = "分组关联的资源查询")
    @RequestMapping(value = "/getGroupResWithTag", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ResourceGroupsPageInfo getGroupResWithTag(@RequestParam String groupId,
                                   @RequestParam(value = "id",required = false) String id,
                                   @RequestParam(value = "types", required = false) String types,
                                   @RequestParam(value = "regions", required = false) String regions,
                                   @RequestParam(value = "name", required = false) String name,
                                   @RequestParam(value = "isCurrent", required = false) boolean isCurrent,
                                   @RequestParam(value = "tags", required = false) String tags,
                                   @RequestParam(value = "pageNo", required = false) int pageNo,
                                   @RequestParam(value = "pageSize", required = false) int pageSize,
                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get group resource with tag. ");
        ResourceGroupsPageInfo groupResWithTag = null;
        try {
            groupResWithTag = resourceGroupService.getGroupResWithTag(groupId, id, types,
                    regions, name, isCurrent, tags, pageNo, pageSize, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return groupResWithTag;
    }
    /**
      * 获取资源列表
      * 调用此接口可以获取带有标签的资源分页列表。
      * 
      * @param id 资源ID，可选参数
      * @param types 资源类型，可选参数
      * @param regions 资源区域，可选参数
      * @param name 资源名称，可选参数
      * @param tags 资源标签，可选参数
      * @param pageNo 分页页码，可选参数
      * @param pageSize 分页大小，可选参数
      * @param from 调用来源，可选参数
      * @return 资源分页信息
      */

    @ApiOperation(value = "资源列表")
    @RequestMapping(value = "/getResourcePageWithTag", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ResourceGroupsPageInfo getResourcePageWithTag(@RequestParam(value = "id",required = false) String id,
                                                     @RequestParam(value = "types", required = false) String types,
                                                     @RequestParam(value = "regions", required = false) String regions,
                                                     @RequestParam(value = "name", required = false) String name,
                                                     @RequestParam(value = "tags", required = false) String tags,
                                                     @RequestParam(value = "pageNo", required = false) int pageNo,
                                                     @RequestParam(value = "pageSize", required = false) int pageSize,
                                                     @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get resource Page with tag. ");
        ResourceGroupsPageInfo groupResWithTag = null;
        try {
            groupResWithTag = resourceGroupService.getResourcePageWithTag(id, types,
                    regions, name, tags, pageNo, pageSize, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return groupResWithTag;
    }

//    @ApiOperation(value = "资源列表不分页")
//    @RequestMapping(value = "/getResNoPageWithTag", method = RequestMethod.GET)
//    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
//            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    public ResourceGroupsPageInfo getResNoPageWithTag(@RequestParam(value = "id",required = false) String id,
//                                                     @RequestParam(value = "types", required = false) String types,
//                                                     @RequestParam(value = "regions", required = false) String regions,
//                                                     @RequestParam(value = "name", required = false) String name,
//                                                     @RequestParam(value = "tags", required = false) String tags,
//                                                     @RequestParam(required = false, defaultValue = "") String from) {
//        LOGGER.debug("get resource Page with tag. ");
//        ResourceGroupsPageInfo groupResWithTag = null;
//        try {
//            groupResWithTag = resourceGroupService.getResNoPageWithTag(id, types,
//                    regions, name, tags, from);
//        } catch (Exception e) {
//            LogicRdsExceptionHandler.handle(e);
//        }
//        return groupResWithTag;
//    }
//
//    @ApiOperation(value = "资源列表（不分页，不与tag联动）")
//    @RequestMapping(value = "/getResNoPageWithoutTag", method = RequestMethod.GET)
//    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
//            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    public ResourceGroupsPageInfo getResNoPageWithTag(@RequestParam(value = "id",required = false) String id,
//                                                      @RequestParam(value = "types", required = false) String types,
//                                                      @RequestParam(value = "regions", required = false) String regions,
//                                                      @RequestParam(value = "name", required = false) String name,
//                                                      @RequestParam(required = false, defaultValue = "") String from) {
//        LOGGER.debug("get resource Page with tag. ");
//        ResourceGroupsPageInfo groupResWithTag = null;
//        try {
//            groupResWithTag = resourceGroupService.getResNoPageWithoutTag(id, types,
//                    regions, name, from);
//        } catch (Exception e) {
//            LogicRdsExceptionHandler.handle(e);
//        }
//        return groupResWithTag;
//    }
//
//    @ApiOperation(value = "资源列表（分页，不与tag联动）")
//    @RequestMapping(value = "/getResNoPageWithoutTag", method = RequestMethod.GET)
//    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
//            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    public ResourceGroupsPageInfo getResNoPageWithTag(@RequestParam(value = "id",required = false) String id,
//                                                      @RequestParam(value = "types", required = false) String types,
//                                                      @RequestParam(value = "regions", required = false) String regions,
//                                                      @RequestParam(value = "name", required = false) String name,
//                                                      @RequestParam(value = "pageNo", required = false) int pageNo,
//                                                      @RequestParam(value = "pageSize", required = false) int pageSize,
//                                                      @RequestParam(required = false, defaultValue = "") String from) {
//        LOGGER.debug("get resource no page without tag. ");
//        ResourceGroupsPageInfo groupResWithTag = null;
//        try {
//            groupResWithTag = resourceGroupService.getResourcePageWithoutTag(id, types,
//                    regions, name, pageNo, pageSize, from);
//        } catch (Exception e) {
//            LogicRdsExceptionHandler.handle(e);
//        }
//        return groupResWithTag;
//    }

    /**
      * 批量查询资源所属分组
      * 根据请求参数批量查询资源所属分组的信息
      *
      * @param request 请求参数，包含需要查询的资源信息
      * @param from 请求来源标识，用于区分不同的调用方，可选参数，默认为空字符串
      * @return 返回资源所属分组详细信息
      */
    @ApiOperation(value = "批量查询资源所属分组")
    @RequestMapping(value = "/getResGroupBatch", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ResGroupDetailResponse getResGroupBatch(@Valid @RequestBody ResGroupDetailRequest request,
                                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get resource group batch. ");
        ResGroupDetailResponse response = null;
        try {
            response = resourceGroupService.getResGroupBatch(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 批量查询资源所属的历史分组
     * 根据资源信息批量查询资源所属的历史分组
     * 
     * @param request 资源详细信息请求对象
     * @param from 查询来源，非必须参数，默认值为空字符串
     * @return 历史分组查询响应对象
     */

    @ApiOperation(value = "批量查询资源所属的历史分组")
    @RequestMapping(value = "/getHistoryResGroupBatchByRes", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public HistoryGroupResResponse getHistoryResGroupBatchByRes(@Valid @RequestBody ResGroupDetailRequest request,
                                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get history resource group batch by resource. ");
        HistoryGroupResResponse response = null;
        try {
            response = resourceGroupService.getHistoryResGroupBatchByRes(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 分组历史关联资源查询
      * 根据分组信息批量查询历史关联资源
      *
      * @param request 分组信息查询请求体
      * @param from    查询来源，默认为空字符串
      * @return 历史关联资源查询响应
      */

    @ApiOperation(value = "分组历史关联资源查询")
    @RequestMapping(value = "/getHistoryResGroupBatchByGroup", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public HistoryGroupResResponse getHistoryResGroupBatchByRes(@Valid @RequestBody HistoryGroupResRequest request,
                                                    @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get history resource group batch by group. ");
        HistoryGroupResResponse response = null;
        try {
            response = resourceGroupService.getHistoryResGroupBatchByGroup(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 批量查询资源所属分组链路
     * 批量查询资源所属分组链路接口
     * 
     * @param request 资源分组详情请求体
     * @param from 来源标识，默认为空字符串
     * @return 分组链路响应
     */

    @ApiOperation(value = "批量查询资源所属分组链路")
    @RequestMapping(value = "/getGroupResList", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ResGroupListResponse getGroupResList(@Valid @RequestBody ResGroupDetailRequest request,
                                                @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get group resource list. ");
        ResGroupListResponse response = null;
        try {
            response = resourceGroupService.getGroupResList(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
}
