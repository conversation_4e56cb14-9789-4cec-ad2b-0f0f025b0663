package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.DeploymentPointRequest;
import com.baidu.bce.logic.rds.service.DeploymentPointService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api("服务发布点")
@RestController
@RequestMapping("/api/rds/instance")
public class ConsoleApiDeploymentPointController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConsoleApiDeploymentPointController.class);

    @Autowired
    private DeploymentPointService service;

    /**
      * 创建服务发布点
      * 根据提供的instanceId和DeploymentPointRequest请求体创建服务发布点
      * 
      * @param instanceId 服务实例ID
      * @param request 服务发布点请求体
      * @param from 来源标记，默认为空字符串
      * @return EdpResultResponse<Boolean> 创建结果
      */
    @ApiOperation(value = "创建服务发布点")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.POST, params = "blbService")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> create(@IdPermission @PathVariable @IdMapper String instanceId,
                                    @Valid @RequestBody DeploymentPointRequest request,
                                    @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create deployment point. instanceId: {}", instanceId);

        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        service.createDeploymentPoint(instanceId, request, from);
        resultResponse.setResult(true);

        return resultResponse;

    }
    /**
     * 删除服务发布点
     * 根据实例ID删除服务发布点
     * 
     * @param instanceId 实例ID
     * @param from 来源标识，默认为空字符串
     * @return EdpResultResponse<Boolean> 删除结果
     */

    @ApiOperation(value = "删除服务发布点")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.DELETE, params = "blbService")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> delete(@IdPermission @PathVariable @IdMapper String instanceId,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("delete deployment point. instanceId: {}", instanceId);

        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        DeploymentPointRequest request = new DeploymentPointRequest();
        request.setAction("delete");
        service.deleteDeploymentPoint(instanceId, request, from);
        resultResponse.setResult(true);

        return resultResponse;

    }
}
