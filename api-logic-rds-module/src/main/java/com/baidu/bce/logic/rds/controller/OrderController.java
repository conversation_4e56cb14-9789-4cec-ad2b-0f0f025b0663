package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.common.network.common.service.UserTokenService;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.iam.facade.model.bcepass.session.BceSessionContext;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.logic.rds.model.RdsDetailBuilder;
import com.baidu.bce.logic.rds.service.RdsOrderService;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.order.CancelToPostpayOrderRequest;
import com.baidu.bce.logic.rds.service.model.order.RdsCreateOrderRequestVo;
import com.baidu.bce.logic.rds.service.model.order.RenewModel;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.webframework.authentication.service.SessionService;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Created by luping03 on 17/11/8.
 */
@RestController
@RequestMapping("/v1/rds/order")
public class OrderController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PricingController.class);

    @Autowired
    private RdsOrderService orderService;

    @Autowired
    private LogicRdsClientFactory rdsClientFactory;

    @ApiOperation("获取订单详情")
    @RequestMapping(method = RequestMethod.GET, value = "/{orderUuid}")
    public Order orderDetail(@PathVariable String orderUuid) {
        Order order = null;
        try {
            // 兼容order service从iamService中获取stsCredential
            StsCredential stsCredential = rdsClientFactory.getUserStsAccessKey();
            com.baidu.bce.iam.facade.model.sts.StsCredential iamStsCredential =
                    new com.baidu.bce.iam.facade.model.sts.StsCredential();
            iamStsCredential.setAccessKeyId(stsCredential.getAccessKeyId());
            iamStsCredential.setSecretAccessKey(stsCredential.getSecretAccessKey());
            iamStsCredential.setSessionToken(stsCredential.getSessionToken());
            iamStsCredential.setUserId(stsCredential.getUserId());
            iamStsCredential.setRoleId(stsCredential.getRoleId());
            iamStsCredential.setExpiration(stsCredential.getExpiration());
            iamStsCredential.setCreateTime(stsCredential.getCreateTime());
            BceSessionContext bceSessionContext = new BceSessionContext();
            bceSessionContext.setStsCredential(iamStsCredential);
            SessionService.setSessionContext(bceSessionContext);

            order = new RdsDetailBuilder(orderUuid).build();
        } catch (BceInternalResponseException e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return order;
    }

    
    @Deprecated
    @ApiOperation(value = "创建订单、实例")
    @RequestMapping(value = "", method = RequestMethod.POST, params = {"type=NEW"})
    @PermissionVertify(isForbidden = true)
    public OrderUuidResult createNewOrder(@RequestBody @Valid BaseCreateOrderRequestVo<InstanceCreateModel> request,
                                          @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create new order.");

        // 部分api请求暂时只支持root user
//        checkApiRootUserOper(from);

        OrderUuidResult orderId = null;
        try {
            orderId = orderService.createNewOrder(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return orderId;
    }

    @ApiOperation(value = "续费:批量")
    @RequestMapping(value = "", method = RequestMethod.POST, params = {"type=RENEW"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    @PermissionVertify(isForbidden = true)
    public OrderUuidResult createRenewOrder(
            @IdPermission @RequestBody @Valid BaseCreateOrderRequestVo<RenewModel> request,
            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create renew order.");

        // 部分api请求暂时只支持root user
//        checkApiRootUserOper(from);
        from = "console";
        OrderUuidResult orderId = null;
        try {
            orderId = orderService.createRenewOrder(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return orderId;
    }

    @ApiOperation(value = "后付费转预付费：批量")
    @RequestMapping(value = "", method = RequestMethod.POST, params = {"type=TO_PREPAY"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    @PermissionVertify(isForbidden = true)
    public OrderUuidResult createToPrepayOrder(
            @IdPermission @RequestBody @Valid RdsCreateOrderRequestVo<RenewModel> request,
            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create renew order.");

        OrderUuidResult orderId = null;
        try {
            orderId = orderService.createToPrepayOrder(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return orderId;
    }

    @ApiOperation(value = "开通：预付费转后付费：批量")
    @RequestMapping(value = "", method = RequestMethod.POST, params = {"type=TO_POSTPAY"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    @PermissionVertify(isForbidden = true)
    public OrderUuidResult createToPostpayOrder(
            @IdPermission @RequestBody @Valid RdsCreateOrderRequestVo<RenewModel> request,
            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create renew order.");

        OrderUuidResult orderId = null;
        try {
            orderId = orderService.createToPostpayOrder(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return orderId;
    }

    @ApiOperation(value = "取消：预付费转后付费：批量")
    @RequestMapping(value = "", method = RequestMethod.POST, params = {"type=CANCEL_TO_POSTPAY"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    @PermissionVertify(isForbidden = true)
    public void cancelToPostpayOrder(
            @IdPermission @RequestBody @Valid CancelToPostpayOrderRequest request,
            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("cancel to_prepay order.");

        try {
            orderService.cancelToPostpayOrder(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @ApiOperation(value = "变配：不支持批量")
    @RequestMapping(value = "", method = RequestMethod.POST, params = {"type=RESIZE"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    @PermissionVertify(isForbidden = true)
    public OrderUuidResult createResizeOrder(
            @IdPermission @RequestBody @Valid RdsCreateOrderRequestVo<PriceDiffModel> request,
            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create resize order.");

        OrderUuidResult orderId = null;
        try {
            orderId = orderService.createResizeOrder(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return orderId;
    }
    
    /**
     * 创建变配订单
     * 变配接口，用于创建不支持批量的变配订单
     * 
     * @param request 变配订单请求参数，包含价格差异模型
     * @param from 来源，非必需参数，默认值为空字符串
     * @return OrderUuidResult 订单UUID结果
     */                                                                                                                                        
    private void checkApiRootUserOper(String from) {

        // from 不为空并且为api
        if (StringUtils.isNotBlank(from) && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            boolean isRoot = UserTokenService.getSubjectToken().isRoot();
            if (!isRoot) {
                LOGGER.warn("[checkApiRootUserOper] Not support sub user.");
                throw new RDSExceptions.NotSupportSubUserOperation("Not Support sub user");
            }
        }
    }
}
