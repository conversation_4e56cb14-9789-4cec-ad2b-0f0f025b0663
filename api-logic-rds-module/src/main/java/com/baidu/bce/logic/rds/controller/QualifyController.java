package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.internalsdk.qualify.model.RealNameInfoResponse;
import com.baidu.bce.internalsdk.rds.model.QualifyResponse;
import com.baidu.bce.logic.rds.service.QualifyService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/rds/qualify")
public class QualifyController {
    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceController.class);

    @Autowired
    private QualifyService qualifyService;
    /**
      * 获取实名认证接口
      * 该接口用于获取实名认证信息
      * 
      * @return QualifyResponse 返回实名认证信息的响应对象
      */

    @ApiOperation(value = "获取实名认证")
    @RequestMapping(value = "/realname", method = RequestMethod.GET)
    public QualifyResponse createInstances() {
        QualifyResponse response = new QualifyResponse();
        try {
            RealNameInfoResponse result = qualifyService.getQualifyRealname();
            response.setResult(result);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
}
