package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.common.network.common.service.UserTokenService;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.iam.facade.model.bcepass.session.BceSessionContext;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.rds.model.ViewPageResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceAzone;
import com.baidu.bce.internalsdk.rds.model.instance.AzoneInfo;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceChangeTagRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceMoreDetailRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceOpenTdeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePageForApiRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePnetIpResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceQuotChangeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceQuotTimeDetailResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceReplicaDelayMaster;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceRestoreRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceTdeStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateAddressRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateNameRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdatePublicAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateReplicaOnLineRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateReplicationTypeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.PutFlowRequest;
import com.baidu.bce.internalsdk.rds.model.instance.RecoveryToSourceInstanceRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdatePortRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCheckPortResponse;
import com.baidu.bce.internalsdk.rds.model.instance.HotupgradeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.HotUpgradeResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.baidu.bce.internalsdk.rds.model.MaintainDurationRequest;
import com.baidu.bce.internalsdk.rds.model.SwitchMasterBackupRequest;
import com.baidu.bce.internalsdk.rds.model.MaintaintimeTasks;
import com.baidu.bce.internalsdk.rds.model.TaskRequest;
import com.baidu.bce.internalsdk.rds.model.MaintaintimeResponse;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.core.utils.JsonConvertUtil;
import com.baidu.bce.logic.rds.model.InstanceDetailResponse;
import com.baidu.bce.logic.rds.model.InstanceMoreDetailResponse;
import com.baidu.bce.logic.rds.model.InstanceReleaseResponse;
import com.baidu.bce.logic.rds.model.RdsDetailBuilder;
import com.baidu.bce.logic.rds.model.ResultResponse;
import com.baidu.bce.internalsdk.rds.model.StatusResponse;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.PerformanceService;
import com.baidu.bce.logic.rds.service.RdsOrderService;
import com.baidu.bce.logic.rds.service.aspect.AuthValidator;
import com.baidu.bce.logic.rds.service.constant.TagsChangeType;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.model.RdsListRequest;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.instance.LogicInstanceCreateResponse;
import com.baidu.bce.logic.rds.service.model.order.RdsCreateOrderRequestVo;
import com.baidu.bce.logic.rds.service.model.pricing.AutoRenew;
import com.baidu.bce.logic.rds.service.model.pricing.OrderIdsRequest;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.webframework.authentication.service.SessionService;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.jsoup.helper.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by luping03 on 17/11/2.
 */
@RestController
@RequestMapping("/v1/rds/instances")
public class InstanceController {
    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceController.class);

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private RdsOrderService rdsOrderService;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private PerformanceService performanceService;

    public static class FilterMap extends HashMap<String, String> {
    }
    
    /**
      * 创建实例
      * 根据请求中的信息创建实例，支持普通创建和在DCC上创建
      * 
      * @param request 包含创建实例所需信息的请求体
      * @param from 请求来源，默认为空字符串
      * @return 创建实例的响应结果
      */                
    @ApiOperation(value = "创建实例")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL, RDSConstant.CREATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX, ids = "*")
    @AuthValidator
    public LogicInstanceCreateResponse createInstances(
            @RequestBody @Valid BaseCreateOrderRequestVo<InstanceCreateModel> request,
            @RequestParam(required = false, defaultValue = "") String from) {
        LogicInstanceCreateResponse response = null;
        try {
            if (!"DCC".equalsIgnoreCase(request.getItems().get(0).getConfig().getInstance().getMachineType())) {
                response = instanceService.createInstances(request, from);
            } else {
                response = instanceService.createInstancesOnDcc(request, from);
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    // TODO 需要重构
    @RequestMapping(method = RequestMethod.GET, params = {"manner=page"})
    @ApiOperation(value = "实例列表: 通过pageNo方式分页")
    public LogicPageResultResponse<InstanceAbstract> list(
            @RequestParam(required = false, defaultValue = "-1") Integer daysToExpiration,
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String filterMapStr,
            @RequestParam(required = false, defaultValue = "") String machineType) {
        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, order, orderBy, pageNo, pageSize);

        LogicPageResultResponse<InstanceAbstract> pageResultResponse = null;

        // 如果accountId为空，说明身份拦截器没有注入主账号id，认为是当前用户没有权限，直接返回空列表
        if (LogicUserService.getSubjectToken() == null
                || LogicUserService.getSubjectToken().getUser() == null) {
            LOGGER.warn("The user no rds permission in possible.");
            pageResultResponse = new  LogicPageResultResponse<InstanceAbstract>();
            pageResultResponse.setOrder(order);
            pageResultResponse.setOrderBy(orderBy);
            pageResultResponse.setPageNo(pageNo);
            pageResultResponse.setPageSize(pageSize);
            pageResultResponse.setTotalCount(0);
            pageResultResponse.setResult(new ArrayList());
            return pageResultResponse;
        }

        try {
            if (StringUtils.isNotEmpty(filterMapStr)) {
                filterMapStr = StringEscapeUtils.unescapeHtml(filterMapStr).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list instance[page], after unescapeHtml filterMaperStr is : {}", filterMapStr);
                FilterMap filterMap = JsonConvertUtil.fromJSON(filterMapStr, FilterMap.class);
                listRequest.setFilterMap(filterMap);

                if (filterMap != null && filterMap.get("instanceId") != null) {
                    filterMap.put("instanceShortId", filterMap.get("instanceId"));
                    filterMap.remove("instanceId");
                }

            }
            pageResultResponse
                    = instanceService.listInstanceWithPageByMultiKey(listRequest, machineType, Boolean.FALSE);
        } catch (Exception e) {
            LOGGER.error("List instance error", e);
            LogicRdsExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }

    @RequestMapping(method = RequestMethod.GET, params = {"manner=pagebcm"})
    @ApiOperation(value = "实例列表: 通过pageNo方式分页")
    public LogicPageResultResponse<InstanceAbstract> listforbcm(
            @RequestParam(required = false, defaultValue = "-1") Integer daysToExpiration,
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String filterMapStr,
            @RequestParam(required = false, defaultValue = "") String machineType,
            @RequestParam(required = false, defaultValue = "") String type) {
        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, order, orderBy, pageNo, pageSize);

        LogicPageResultResponse<InstanceAbstract> pageResultResponse = null;

        // 如果accountId为空，说明身份拦截器没有注入主账号id，认为是当前用户没有权限，直接返回空列表
        if (LogicUserService.getSubjectToken() == null
                || LogicUserService.getSubjectToken().getUser() == null) {
            LOGGER.warn("The user no rds permission in possible.");
            pageResultResponse = new  LogicPageResultResponse<InstanceAbstract>();
            pageResultResponse.setOrder(order);
            pageResultResponse.setOrderBy(orderBy);
            pageResultResponse.setPageNo(pageNo);
            pageResultResponse.setPageSize(pageSize);
            pageResultResponse.setTotalCount(0);
            pageResultResponse.setResult(new ArrayList());
            return pageResultResponse;
        }

        try {
            if (StringUtils.isNotEmpty(filterMapStr)) {
                filterMapStr = StringEscapeUtils.unescapeHtml(filterMapStr).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list instance[page], after unescapeHtml filterMaperStr is : {}", filterMapStr);
                FilterMap filterMap = JsonConvertUtil.fromJSON(filterMapStr, FilterMap.class);
                listRequest.setFilterMap(filterMap);

                if (filterMap != null && filterMap.get("instanceId") != null) {
                    filterMap.put("instanceShortId", filterMap.get("instanceId"));
                    filterMap.remove("instanceId");
                }

            }
            pageResultResponse
                    = instanceService.listforbcm(listRequest, machineType, Boolean.FALSE, type);
        } catch (Exception e) {
            LOGGER.error("List instance error", e);
            LogicRdsExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }
    /**
     * 实例列表: 通过pageNo方式分页
     * 通过pageNo方式分页获取实例列表，支持按关键字搜索
     * 
     * @param instancePageForApiRequest 分页请求参数，包括页码、页面大小、搜索关键字类型、搜索关键字
     * @return LogicPageResultResponse<InstanceAbstract> 分页响应，包含实例列表、页码、页面大小、总数量等信息
     */

    @RequestMapping(value = "/pageApi", method = RequestMethod.POST, params = {"manner=pageApi"})
    @ApiOperation(value = "实例列表: 通过pageNo方式分页")
    public LogicPageResultResponse<InstanceAbstract> listFromApi(
            @RequestBody InstancePageForApiRequest instancePageForApiRequest) {
        if (instancePageForApiRequest == null) {
            instancePageForApiRequest = new InstancePageForApiRequest();
        }
        RdsListRequest listRequest
                = new RdsListRequest(-1, "desc", "instanceCreateTime",
                instancePageForApiRequest.getPageNo(), instancePageForApiRequest.getPageSize());

        LogicPageResultResponse<InstanceAbstract> pageResultResponse = null;

        // 如果accountId为空，说明身份拦截器没有注入主账号id，认为是当前用户没有权限，直接返回空列表
        if (LogicUserService.getSubjectToken() == null
                || LogicUserService.getSubjectToken().getUser() == null) {
            LOGGER.warn("The user no rds permission in possible.");
            pageResultResponse = new  LogicPageResultResponse<InstanceAbstract>();
            pageResultResponse.setOrder("desc");
            pageResultResponse.setOrderBy("instanceCreateTime");
            pageResultResponse.setPageNo(instancePageForApiRequest.getPageNo());
            pageResultResponse.setPageSize(instancePageForApiRequest.getPageSize());
            pageResultResponse.setTotalCount(0);
            pageResultResponse.setResult(new ArrayList());
            return pageResultResponse;
        }
        try {
            if (StringUtils.isNotEmpty(instancePageForApiRequest.getKeyword())) {
                FilterMap filterMap = new FilterMap();
                filterMap.put(instancePageForApiRequest.getKeywordType(), instancePageForApiRequest.getKeyword());
                listRequest.setFilterMap(filterMap);
            }

            pageResultResponse = instanceService.listInstanceWithPageByApi(listRequest);
        } catch (Exception e) {
            LOGGER.error("List instance error", e);
            LogicRdsExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }


    @RequestMapping(method = RequestMethod.GET, params = {"action=proxyManage"})
    @ApiOperation(value = "集群管理列表：不分页")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogicPageResultResponse<Instance> proxyManageList(@IdPermission @RequestParam String proxyInstanceUuid) {
        LogicPageResultResponse<Instance> pageResultResponse = new LogicPageResultResponse<>();
        try {
            pageResultResponse.setResult(instanceService.proxyManageList(proxyInstanceUuid));
        } catch (Exception e) {
            LOGGER.error("List instance error", e);
            LogicRdsExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }

    /**
     * 通过marker方式分页查询实例列表
     * 实例列表：通过marker方式分页查询实例列表
     *
     * @param marker 分页查询的起始位置标识
     * @param maxKeys 每页返回的最大数量
     * @return 分页查询结果的响应体
     */
    @RequestMapping(method = RequestMethod.GET, params = {"manner=marker"})
    @ApiOperation(value = "实例列表: 通过marker方式分页")
    public LogicMarkerResultResponse listInstanceByMarker(
            @RequestParam(required = false, defaultValue = "-1") String marker,
            @RequestParam(required = false, defaultValue = "1000") Integer maxKeys) {
        LOGGER.debug("list instance[marker], marker:{}, maxKeys:{}.", marker, maxKeys);
        LogicMarkerResultResponse pageResultResponse = null;
        try {
            pageResultResponse = instanceService.listInstanceWithMarkerByMultiKey(marker, maxKeys, "");
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return pageResultResponse;
    }
    /**
      * 实例列表查询（自定义策略页面用）
      * 通过pageNo方式分页查询实例列表，支持过滤条件、排序等，用于自定义策略页面。
      * 
      * @param daysToExpiration 距离到期天数，用于过滤实例
      * @param order 排序字段
      * @param orderBy 排序方式（asc/desc）
      * @param pageNo 页码
      * @param pageSize 每页数量
      * @param tagKey 标签键，用于过滤实例
      * @param tagValue 标签值，用于过滤实例
      * @param filterMapStr 过滤条件字符串，json格式
      * @param machineType 机器类型，用于过滤实例
      * @return 实例列表查询结果
      */

    @RequestMapping(value = "/forPolicy", method = RequestMethod.GET)
    @ApiOperation(value = "实例列表: 通过pageNo方式分页－自定义策略页面用")
    public LogicPageResultResponse<InstanceAbstract> listForPolicy(
            @RequestParam(required = false, defaultValue = "-1") Integer daysToExpiration,
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String tagKey,
            @RequestParam(required = false, defaultValue = "") String tagValue,
            @RequestParam(required = false, defaultValue = "") String filterMapStr,
            @RequestParam(required = false, defaultValue = "") String machineType) {
        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, order, orderBy, pageNo, pageSize);

        LogicPageResultResponse<InstanceAbstract> pageResultResponse = null;
        try {
            if (StringUtils.isNotEmpty(filterMapStr)) {
                filterMapStr = StringEscapeUtils.unescapeHtml(filterMapStr).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list instance[page], after unescapeHtml filterMaperStr is : {}", filterMapStr);
                FilterMap filterMap = JsonConvertUtil.fromJSON(filterMapStr, FilterMap.class);
                listRequest.setFilterMap(filterMap);

                if (filterMap != null && filterMap.get("instanceId") != null) {
                    filterMap.put("instanceShortId", filterMap.get("instanceId"));
                    filterMap.remove("instanceId");
                }
            }

            // 如果有tagKey 或 tagValue 则转成filterMap参数
            if (StringUtils.isNotBlank(tagKey)) {
                listRequest.setFilterMap(coverTagFilter(listRequest.getFilterMap(), tagKey, tagValue));
            }

            pageResultResponse = instanceService.listInstanceWithPageForPolicy(listRequest);

            // 填充name字段，代表实例名称（兼容iam的接口规范）
            for (InstanceAbstract eachInstance : pageResultResponse.getResult()) {
                eachInstance.setName(eachInstance.getInstanceName());
            }

        } catch (Exception e) {
            LOGGER.error("List instance error", e);
            LogicRdsExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }

    private Map<String, String> coverTagFilter(Map<String, String> filterMap, String tagKey, String tagValue) {

        // 不存在tag，返回
        if (StringUtils.isBlank(tagKey)) {
            return filterMap;
        }

        // map为空，创建
        if (filterMap == null ) {
            filterMap = new HashMap<String, String>();
        }

        // 单独存在tagValue视为错误
        if (StringUtils.isNotBlank(tagValue) && StringUtil.isBlank(tagKey)) {
            throw new RDSExceptions.ParamValidationException();
        }

        // {tagKey}__{tagValue} 标签查询
        // {tagKey}_@@@ 标签key查询
        String tagFilterStr = tagKey + "__";
        if (StringUtils.isNotBlank(tagValue)) {
            tagFilterStr += tagValue;
        } else {
            // @@@表示所有value
            tagFilterStr += "@@@";
        }

        filterMap.put("tag", tagFilterStr);

        return filterMap;
    }
    /**
      * 实例详情
      * 根据instanceId获取实例详情
      * 
      * @param instanceId 实例ID
      * @param from 来源
      * @return 实例详情
      */

    @RequestMapping(value = "/{instanceId}", method = RequestMethod.GET)
    @ApiOperation(value = "实例详情")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public InstanceDetailResponse detail(@IdPermission @PathVariable String instanceId,
                                         @RequestParam(required = false, defaultValue = "") String from) {
        InstanceDetailResponse response = new InstanceDetailResponse();
        try {
            response.setInstance(instanceService.detail(instanceId, from));
        } catch (Exception e) {
            LOGGER.error("get instance detail error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    /**
      * 实例详情
      * 获取实例的更多详细信息
      *
      * @param moreDetailRequest 实例详情请求体
      * @param from 来源
      * @return 实例详情响应
      */
    @RequestMapping(value = "/moreDetail", method = RequestMethod.POST)
    @ApiOperation(value = "实例详情")
    public InstanceMoreDetailResponse moreDetail(
                                         @RequestBody(required = false)InstanceMoreDetailRequest moreDetailRequest,
                                         @RequestParam(required = false, defaultValue = "") String from) {
        InstanceMoreDetailResponse response = new InstanceMoreDetailResponse();
        try {
            response.setInstanceList(instanceService.moreDetail(moreDetailRequest, from));
        } catch (Exception e) {
            LOGGER.error("get instance moreDetail error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    /**
      * 开通／关闭公网访问
      * 根据实例ID开通或关闭公网访问
      * 
      * @param instanceId 实例ID
      * @param request 开通或关闭公网访问的请求体
      * @param from 请求来源（可选参数，默认值为空字符串）
      */
    @ApiOperation(value = "开通／关闭公网访问")
    @RequestMapping(value = "/{instanceId}/publiclyAccessible", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePubliclyAccessible(@IdPermission @IdMapper @PathVariable String instanceId,
                                         @RequestBody @Valid InstanceUpdatePublicAccessibleRequest request,
                                         @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.updatePubliclyAccessible(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 修改实例名称
      * 根据实例ID和请求体内容，修改实例的名称
      * 
      * @param instanceId 实例ID
      * @param request 修改实例名称的请求体
      * @param from 请求来源（可选）
      */

    @ApiOperation(value = "修改实例名称")
    @RequestMapping(value = "/{instanceId}/instanceName", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateInstanceName(@IdPermission @IdMapper @PathVariable String instanceId,
                                   @RequestBody @Valid InstanceUpdateNameRequest request,
                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.updateInstanceName(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 修改域名
     * 根据instanceId修改域名信息
     *
     * @param instanceId 实例ID
     * @param request 修改域名的请求信息
     * @param from 来源标识，默认为空字符串
     */

    @ApiOperation(value = "修改域名")
    @RequestMapping(value = "/{instanceId}/domain", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateDomain(@IdPermission @IdMapper @PathVariable String instanceId,
                             @RequestBody @Valid InstanceUpdateAddressRequest request,
                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.updateDomain(instanceId, request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }


    /**
     * 获取只读实例与主实例的同步延迟
     * 通过实例ID获取只读实例与主实例的同步延迟信息
     * 
     * @param instanceId 实例ID
     * @param from 未使用的保留字段
     * @return InstanceReplicaDelayMaster 实例同步延迟信息
     */
    @ApiOperation(value = "只读实例与主实例的同步延迟")
    @RequestMapping(value = "/{instanceId}/replicaDelay", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public InstanceReplicaDelayMaster replicaDelay(@IdPermission @IdMapper @PathVariable String instanceId,
                                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get secondsBehindMaster. instanceId is {}", instanceId);
        InstanceReplicaDelayMaster response = null;
        try {
            response = instanceService.replicaDelay(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 修改同步方式
      * 修改数据库的同步方式
      * 
      * @param instanceId 实例ID
      * @param request 修改同步方式的请求体
      * @param from 请求来源（可选）
      */

    @ApiOperation(value = "修改同步方式")
    @RequestMapping(value = "/{instanceId}/replicationType", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateReplicationType(@IdPermission @IdMapper @PathVariable String instanceId,
                                      @RequestBody @Valid InstanceUpdateReplicationTypeRequest request,
                                      @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.updateReplicationType(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 修改备份策略
      * 根据实例ID和备份策略信息，更新实例的备份策略
      * 
      * @param instanceId 实例ID
      * @param policy 备份策略信息
      * @param from 来源标记，用于记录调用来源
      */

    @ApiOperation(value = "修改备份策略")
    @RequestMapping(value = "/{instanceId}/backupPolicy", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateBackupPolicy(@IdPermission @IdMapper @PathVariable String instanceId,
                                   @RequestBody @Valid SnapshotPolicy policy,
                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.updateBackupPolicy(instanceId, policy);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 变配接口
     * 创建变配订单，不支持批量操作
     * 
     * @param instanceId 实例ID
     * @param request 变配请求参数
     * @param from 来源参数，非必须
     * @return 变配订单ID
     */

    @ApiOperation(value = "变配：不支持批量")
    @RequestMapping(value = "/{instanceId}/resize", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OrderUuidResult createResizeOrder(@IdPermission @PathVariable @IdMapper String instanceId,
                                             @RequestBody @Valid RdsCreateOrderRequestVo<PriceDiffModel> request,
                                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("resize instance.");
        OrderUuidResult orderId = null;

        try {
            orderId = instanceService.resizeInstance(instanceId, request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return orderId;
    }
    /**
      * 恢复实例到指定快照
      * 该函数用于将RDS实例恢复到指定的快照状态
      * 
      * @param instanceId 实例ID，用于指定需要恢复的实例
      * @param request 恢复请求体，包含快照ID等信息
      * @param from 保留字段，目前未使用
      */

    @ApiOperation(value = "恢复实例到指定快照")
    @RequestMapping(value = "/{instanceId}/restore", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void restore(@IdPermission @PathVariable String instanceId,
                        @RequestBody @Valid InstanceRestoreRequest request,
                        @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update backupPolicy. instanceId is {}", instanceId);
        try {
            instanceService.restore(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 是否还有主实例配额
      * 检查是否还有主实例配额
      * 
      * @param from 来源，可选参数，默认为空字符串
      * @return 返回检查结果
      */

    @ApiOperation(value = "是否还有主实例配额")
    @RequestMapping(value = "/masterQuota", method = RequestMethod.GET)
    public ResultResponse<Boolean> masterQuota(@RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("check masterQuota.");
        ResultResponse<Boolean> response = new ResultResponse<>();
        try {
            response.setResult(instanceService.checkMasterQuota());
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 是否还有只读实例配额
      * 根据instanceId查询是否还有只读实例配额
      *
      * @param instanceId 实例ID
      * @param from 来源
      * @return 查询结果
      */

    @ApiOperation(value = "是否还有只读实例配额")
    @RequestMapping(value = "/{instanceId}/replicaQuota", method = RequestMethod.GET)
    public ResultResponse<Boolean> replicaQuota(@PathVariable String instanceId,
                                                @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("check replicaQuota. instanceId is {}", instanceId);
        ResultResponse<Boolean> response = new ResultResponse<>();
        try {
            response.setResult(instanceService.checkReplicaQuota(instanceId));
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 释放实例
      * 删除指定的实例，执行释放操作
      * 
      * @param instanceId 实例ID
      * @param from 请求来源，默认为空字符串
      * @Deprecated 不再使用，改用下面的批量释放接口
      */

    @ApiOperation(value = "释放实例")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    @PermissionVertify(isForbidden = true)
    @Deprecated
    // 不再使用，改用下面的批量释放接口
    public void delete(@PathVariable String instanceId,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("release instance. instanceId:" + instanceId);

        // 部分api请求暂时只支持root user
//        checkApiRootUserOper(from);

        try {
            instanceService.release(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

//    /**
//     * 释放单个实例进入回收站
//     * @param instanceId
//     * @param from
//     */
//
//    @ApiOperation(value = "释放单个实例")
//    @RequestMapping(value = "/{instanceId}", method = RequestMethod.DELETE)
//    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
//            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
////    @PermissionVertify(isForbidden = true)
//    public void deleteInstance(@IdPermission @PathVariable String instanceId,
//                                               @RequestParam(required = false, defaultValue = "") String from) {
//        LOGGER.debug("release instance batch. instanceIds: " + instanceId);
//
//        // 部分api请求暂时只支持root user
////        checkApiRootUserOper(from);
//        if (instanceId == null || instanceId.length() == 0){
//            throw new RDSExceptions.ParamValidationException();
//        }
//        instanceService.delete(instanceId, from);
//
//    }

    private InstanceReleaseResponse deleteBatch(String instanceIds, String from) {
        LOGGER.debug("release instance batch. instanceIds: " + instanceIds);

        // 部分api请求暂时只支持root user
//        checkApiRootUserOper(from);

        InstanceReleaseResponse instanceReleaseResponse = new InstanceReleaseResponse();
        List<String> delFailed;
        try {
            if (StringUtils.isEmpty(instanceIds)) {
                throw new RDSExceptions.ParamValidationException("instanceIds can not be empty.");
            }
            String[] instanceId = instanceIds.split(",");
            for (String id : instanceId) {
                if (StringUtils.isEmpty(id)) {
                    throw new RDSExceptions.ParamValidationException();
                }
            }
            if (instanceId.length > 10) {
                throw new RDSExceptions.ParamValidationException("Release up to 10 at a time");
            }
            delFailed = instanceService.oldDelete(instanceId, from);
            if (delFailed.size() > 0) {
                instanceReleaseResponse.setSuccess(false);
            }
            instanceReleaseResponse.setReleaseFailedInstanceIds(delFailed);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return instanceReleaseResponse;
    }

    /**
      * 释放实例进回收站
      * 支持单个或批量释放实例到回收站，仅root用户可调用，且部分api请求暂时只支持root用户
      * 如果当前区域配置为边缘区域，则调用deleteBatch方法处理。
      * 
      * @param instanceIds 实例ID，支持单个和批量，批量时以逗号分隔
      * @param from 调用来源
      * @return InstanceReleaseResponse 实例释放响应，包含释放失败的实例ID列表
      * @throws RDSExceptions.ParamValidationException 如果instanceIds为空或长度为0时抛出
      * @throws RDSExceptions.BatchDeleteParamValidationException 如果批量删除时实例ID数量超过3个时抛出
      */        
    @ApiOperation(value = "释放实例")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
//    @PermissionVertify(isForbidden = true)
    public InstanceReleaseResponse deleteInstances(@IdPermission @RequestParam String instanceIds,
                               @RequestParam(required = false, defaultValue = "") String from) {
        if (regionConfiguration.getCurrentRegion().equals(RDSConstant.REGION_EDGE)) {
            return deleteBatch(instanceIds, from);
        }
        // 部分api请求暂时只支持root user
//        checkApiRootUserOper(from);

        // DeleteInstanceRequest request
        // List<String> requestInstanceIds = request.getInstanceIds();

        InstanceReleaseResponse instanceReleaseResponse = new InstanceReleaseResponse();
        List<String> delFailed;

        if (instanceIds == null || instanceIds.length() == 0) {
            throw new RDSExceptions.ParamValidationException();
        }

        if (instanceIds.contains(",")) {

            String[] splits = instanceIds.split(",");
            if (splits.length > 3) {
                throw new RDSExceptions.BatchDeleteParamValidationException();
            }
            delFailed = instanceService.delete(splits, from);
        } else {
            String[] splits = new String[1];
            splits[0] = instanceIds;
            delFailed = instanceService.delete(splits, from);
        }

        if (delFailed.size() > 0) {
            instanceReleaseResponse.setSuccess(false);
        }
        instanceReleaseResponse.setReleaseFailedInstanceIds(delFailed);

        return instanceReleaseResponse;

//        if (instanceIds.contains(",")){
//            String[] split = instanceIds.split(",");
//            List<String> stringList = Arrays.asList(split);
//            // 批量进回收站
//            instanceService.delete(stringList, from);
//        }else {
//            // 单个进回收站
//            instanceService.delete(instanceIds, from);
//        }



    }
    /**
      * 修改备份实例的上下线状态
      * 通过PUT请求修改指定RDS只读实例的上下线状态
      * 
      * @param proxyInstanceId 实例ID
      * @param request 修改上下线状态的请求体
      * @param from 请求来源，默认为空字符串
      */

    @ApiOperation(value = "只读流量开关")
    @RequestMapping(value = "/{proxyInstanceId}/onOrOfflineReplica", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void onOrOfflineReplica(@IdPermission @PathVariable String proxyInstanceId,
                                   @RequestBody @Valid InstanceUpdateReplicaOnLineRequest request,
                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update backupPolicy. instanceId is {}", proxyInstanceId);
        try {
            instanceService.onOrOfflineReplica(proxyInstanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 设置流量配比
      * 该函数用于设置流量配比，通过传入代理实例ID、流量配比请求对象和来源进行流量配比的设置。
      * 
      * @param proxyInstanceId 代理实例ID
      * @param request 流量配比请求对象
      * @param from 来源，默认为空字符串
      */

    @ApiOperation(value = "设置流量配比")
    @RequestMapping(value = "/{proxyInstanceId}/putFlow", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void putFlow(@IdPermission @PathVariable String proxyInstanceId,
                        @RequestBody @Valid PutFlowRequest request,
                        @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("putFlow. instanceId is {}", proxyInstanceId);
        try {
            instanceService.putFlow(proxyInstanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    @RequestMapping(method = RequestMethod.GET, params = {"action=quotManage"})
    @ApiOperation(value = "实例过保: 查询过保实例列表信息")
    public LogicPageResultResponse<InstanceAbstract> quotList(
            @RequestParam(required = false, defaultValue = "-1") Integer daysToExpiration,
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String filterMapStr,
            @RequestParam(required = false, defaultValue = "") String machineType) {
        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, order, orderBy, pageNo, pageSize);

        LogicPageResultResponse<InstanceAbstract> pageResultResponse = null;

        // 如果accountId为空，说明身份拦截器没有注入主账号id，认为是当前用户没有权限，直接返回空列表
        if (LogicUserService.getSubjectToken() == null
                || LogicUserService.getSubjectToken().getUser() == null) {
            LOGGER.warn("The user no rds permission in possible.");
            pageResultResponse = new  LogicPageResultResponse<InstanceAbstract>();
            pageResultResponse.setOrder(order);
            pageResultResponse.setOrderBy(orderBy);
            pageResultResponse.setPageNo(pageNo);
            pageResultResponse.setPageSize(pageSize);
            pageResultResponse.setTotalCount(0);
            pageResultResponse.setResult(new ArrayList());
            return pageResultResponse;
        }

        try {
            if (StringUtils.isNotEmpty(filterMapStr)) {
                filterMapStr = StringEscapeUtils.unescapeHtml(filterMapStr).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list instance[page], after unescapeHtml filterMaperStr is : {}", filterMapStr);
                FilterMap filterMap = JsonConvertUtil.fromJSON(filterMapStr, FilterMap.class);
                listRequest.setFilterMap(filterMap);

                if (filterMap != null && filterMap.get("instanceId") != null) {
                    filterMap.put("instanceShortId", filterMap.get("instanceId"));
                    filterMap.remove("instanceId");
                }
            }
            pageResultResponse = instanceService.listQuotInstanceWithPageByMultiKey(listRequest);
        } catch (Exception e) {
            LOGGER.error("List instance error", e);
            LogicRdsExceptionHandler.handle(e);
        }

        return pageResultResponse;
    }
    /**
      * 实例过保：过保实例可执行切换操作时间范围查询
      * 
      * @param instanceId 实例ID
      * @param type 类型
      * @param from 起始时间
      * @return InstanceQuotTimeDetailResponse 实例过保时间范围详情
      */

    @RequestMapping(value = "/{instanceId}/quot", method = RequestMethod.GET)
    @ApiOperation(value = "实例过保：过保实例可执行切换操作时间范围查询")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public InstanceQuotTimeDetailResponse quotFindRepairTime(@IdPermission @PathVariable String instanceId,
                                                          @RequestParam(required = true) String type,
                                                      @RequestParam(required = false, defaultValue = "") String from) {
        InstanceQuotTimeDetailResponse response = new InstanceQuotTimeDetailResponse();
        try {
            response = instanceService.quotFindRepairTime(instanceId, type, from);
        } catch (Exception e) {
            LOGGER.error("get instance detail error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 实例过保：过保实例修改过保替换时间
      * 修改过保实例的替换时间
      * 
      * @param instanceId 实例ID
      * @param request 实例过保替换时间请求体
      * @param from 来源，非必需参数，默认为空字符串
      */

    @RequestMapping(value = "/{instanceId}/quot", method = RequestMethod.PUT)
    @ApiOperation(value = "实例过保：过保实例修改过保替换时间")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void quotChangeRepairTime(@IdPermission @PathVariable String instanceId,
                                     @RequestBody @Valid InstanceQuotChangeRequest request,
                                     @RequestParam(required = false, defaultValue = "") String from) {
        request.setInstanceId(instanceId);

        try {
            instanceService.quotChangeRepairTime(request);
        } catch (Exception e) {
            LOGGER.error("get instance detail error", e);

        }
    }
    /**
      * 发起主备切换
      * 切换RDS实例的主备状态
      * 
      * @param instanceId RDS实例ID
      * @param request 切换主备请求体
      * @param from 发起切换的来源，默认为空字符串
      * @throws RDSBusinessExceptions.InstanceNotFoundException 实例未找到异常
      * @throws RDSBusinessExceptions.InvalidInstanceStatus 实例状态无效异常
      * @throws RDSBusinessExceptions.InternalServerErrorException 服务器内部错误异常
      */

    @RequestMapping(value = "/{instanceId}/masterBackupInfo", method = RequestMethod.PUT)
    @ApiOperation(value = "发起主备切换")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void switchMasterBackup(@IdPermission @PathVariable String instanceId,
                                   @RequestBody SwitchMasterBackupRequest request,
                                   @RequestParam(required = false, defaultValue = "") String from) {
        try {
            instanceService.switchMasterBackup(instanceId, request, from);
        } catch (BceInternalResponseException e) {
            if ("RDSInstanceNotFound".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceNotFoundException();
            }
            else if ("DbinstanceStateChange".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InvalidInstanceStatus();
            }
            else {
                throw new RDSBusinessExceptions.InternalServerErrorException();
            }
        }
    }

    /**
      * 绑定标签
      * 绑定标签到实例上
      * 
      * @param instanceId 实例ID
      * @param request 标签变更请求体
      * @param from 请求来源，默认为空字符串
      */
    @ApiOperation(value = "绑定标签")
    @RequestMapping(value = "/{instanceId}/tag/bind", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void bindTag(@IdPermission @IdMapper @PathVariable String instanceId,
                        @RequestBody @Valid InstanceChangeTagRequest request,
                        @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("bind tag. instanceId is {}", instanceId);
        try {
            instanceService.changeTagsToInstance(instanceId, request.getChangeTags(),
                    TagsChangeType.BIND, Boolean.FALSE);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 解绑标签
     * 解绑实例的标签
     * 
     * @param instanceId 实例ID
     * @param request 解绑标签的请求体
     * @param from 请求来源，默认为空字符串
     */

    @ApiOperation(value = "解绑标签")
    @RequestMapping(value = "/{instanceId}/tag/unbind", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void unbindTag(@IdPermission @IdMapper @PathVariable String instanceId,
                          @RequestBody @Valid InstanceChangeTagRequest request,
                          @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("unbind tags. instanceId is {}", instanceId);
        try {
            instanceService.changeTagsToInstance(instanceId, request.getChangeTags(),
                    TagsChangeType.UNBIND, Boolean.FALSE);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    private void checkApiRootUserOper(String from) {

        // from 不为空并且为api
        if (StringUtils.isNotBlank(from) && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            boolean isRoot = UserTokenService.getSubjectToken().isRoot();
            if (!isRoot) {
                LOGGER.warn("[checkApiRootUserOper] Not support sub user.");
                throw new RDSExceptions.NotSupportSubUserOperation("Not Support sub user");
            }
        }
    }
    /**
      * 查询实例pnetIp
      * 根据实例ID查询实例的pnetIp地址
      *
      * @param instanceId 实例ID
      * @return 返回查询结果
      */

    @ApiOperation(value = "查询实例pnetIp")
    @RequestMapping(value = "/{instanceId}/pnetIp", method = RequestMethod.GET)
    public ResultResponse<String> pnetIp(@PathVariable String instanceId) {
        LOGGER.debug("get pnetIp. instanceId is {}", instanceId);
        ResultResponse<String> response = new ResultResponse<>();
        try {
            InstancePnetIpResponse res = instanceService.getPnetIp(instanceId);

            response.setResult(res == null ? "" : res.getPnetIp());
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 开通TDE接口
     * 根据实例ID开通TDE服务
     * 
     * @param instanceId 实例ID
     * @param request 开通TDE的请求体
     * @return 操作结果
     */

    @ApiOperation(value = "开通tde")
    @RequestMapping(value = "/{instanceId}/tde", method = RequestMethod.POST)
    public ResultResponse<String> openTde(@PathVariable String instanceId,
                                          @RequestBody InstanceOpenTdeRequest request) {
        LOGGER.debug("get pnetIp. instanceId is {}", instanceId);
        ResultResponse<String> response = new ResultResponse<>();
        try {
            instanceService.openTde(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查询TDE状态
      * 根据实例ID查询TDE状态
      * 
      * @param instanceId 实例ID
      * @return 实例TDE状态响应
      */

    @ApiOperation(value = "查询tde状态")
    @RequestMapping(value = "/{instanceId}/tde", method = RequestMethod.GET)
    public InstanceTdeStatusResponse checkTde(@PathVariable String instanceId) {
        LOGGER.debug("get checkTde. instanceId is {}", instanceId);
        InstanceTdeStatusResponse response = new InstanceTdeStatusResponse();
        try {
            response = instanceService.checkTde(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 判断是否满足条件变配条件
     * 该函数用于判断实例是否满足条件变配的条件
     * 
     * @param instanceId 实例ID
     * @return 返回判断结果，封装在StatusResponse对象中
     */

    @ApiOperation(value = "判断是否满足条件变配条件")
    @RequestMapping(value = "/{instanceId}/checkFdisk", method = RequestMethod.GET)
    public StatusResponse checkFdisk(@PathVariable String instanceId) {
        LOGGER.debug("get checkFdisk. instanceId is {}", instanceId);
        StatusResponse statusResponse = new StatusResponse();
        try {
            statusResponse = instanceService.checkFdisk(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return statusResponse;
    }
    /**
      * 可用区迁移接口
      * 支持MySQL和PG数据库的实例进行可用区迁移操作
      * 
      * @param instanceId 实例ID
      * @param request 可用区迁移请求参数，包括目标可用区等信息
      * @param from 迁移来源，默认为空字符串
      */

    @ApiOperation(value = "MySQL+PG支持可用区迁移")
    @RequestMapping(value = "/{instanceId}/azoneMigration", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void azoneMigration(@IdPermission @PathVariable String instanceId,
                               @RequestBody @Valid InstanceAzone request,
                               @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("azone migration. instanceId is {}", instanceId);
        AzoneInfo azoneInfo = new AzoneInfo();
        azoneInfo.setInstanceParameters(request);
        azoneInfo.setEffectiveTime(request.getEffectiveTime());
        try {
            instanceService.azoneMigration(instanceId, azoneInfo);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 按备份集点恢复指定库/表到原实例
     * 该接口用于将指定备份集的库或表恢复到原实例。
     * 
     * @param instanceId 实例ID，用于标识需要操作的实例
     * @param request 恢复请求体，包含恢复所需的详细信息
     * @param from 恢复的起始位置，可选参数，默认为空
     * @deprecated 该接口已被标记为过时，建议使用新的接口进行恢复操作
     */

    @ApiOperation(value = "按备份集点恢复指定库/表到原实例")
    @RequestMapping(value = "/{instanceId}/recoveryToSourceInstanceBySnapshot", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Deprecated
    public void recoveryToSourceInstanceBySnapshot(@IdPermission @IdMapper @PathVariable String instanceId,
                                                   @RequestBody @Valid RecoveryToSourceInstanceRequest request,
                                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("recoveryToSourceInstanceBySnapshot. instanceId is {}", instanceId);
        try {
            instanceService.recoveryToSourceInstanceBySnapshot(instanceId, request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 按时间点恢复指定库/表到原实例
     * 按时间点恢复实例，可以指定库/表。使用PUT方法，路径参数包括实例ID，请求体包含恢复的具体信息，可选的请求参数from指定恢复来源。
     * 
     * @param instanceId 实例ID
     * @param request 恢复请求体，包含恢复的具体信息
     * @param from 可选参数，指定恢复来源
     */

    @ApiOperation(value = "按时间点恢复指定库/表到原实例")
    @RequestMapping(value = "/{instanceId}/recoveryToSourceInstanceByDatetime", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Deprecated
    public void recoveryToSourceInstanceByDatatime(@IdPermission @IdMapper @PathVariable String instanceId,
                                                   @RequestBody @Valid RecoveryToSourceInstanceRequest request,
                                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("recoveryToSourceInstanceByDatatime. instanceId is {}", instanceId);
        try {
            instanceService.recoveryToSourceInstanceByTime(instanceId, request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 概览页接口
     * 该接口用于获取概览页的信息
     * 
     * @param region 地区参数，默认为"global"
     * @return 返回概览页信息的ViewPageResponse对象
     */

    @ApiOperation(value = "概览页")
    @RequestMapping(value = "/showOverviewPage", method = RequestMethod.GET)
    public ViewPageResponse showOverviewPage1(@RequestParam(value = "region",
            required = false, defaultValue = "global") String region) {
        ViewPageResponse viewPageResponse = new ViewPageResponse();
        try {
//            regionConfiguration.setCurrentRegion(region);
//            LogicUserService.getSubjectToken().getUser().setId(userId);
            viewPageResponse = instanceService.showOverviewPageAll(region);

        } catch (Exception e) {
            LOGGER.error("show overviewPage info error, use default info", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return viewPageResponse;
    }

    /**
      * 概览页
      * 展示概览页面信息
      * 
      * @param region 地区标识，默认为global
      * @return 返回概览页面的响应数据
      */
    @ApiOperation(value = "概览页")
    @RequestMapping(value = "/showOverviewPageEachRegion", method = RequestMethod.GET)
    public ViewPageResponse showOverviewPage(@RequestParam(value = "region",
            required = false, defaultValue = "global") String region) {
        ViewPageResponse viewPageResponse = new ViewPageResponse();
        try {
//            regionConfiguration.setCurrentRegion(region);
//            LogicUserService.getSubjectToken().getUser().setId(userId);
            viewPageResponse = instanceService.showOverviewPage(region);

        } catch (Exception e) {
            LOGGER.error("show overviewPage info error, use default info", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return viewPageResponse;
    }

    /**
     * 开启rds接口
     * 该接口用于开启rds服务
     *
     * @throws Exception 抛出异常信息
     */
    @ApiOperation(value = "开启rds")
    @RequestMapping(value = "/activateService", method = RequestMethod.PUT)
    public void activateService() {
        try {
            rdsOrderService.activateService();
        } catch (Exception e) {
            LOGGER.error("activateService", e);
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 已创建实例续费
     * 用于对已创建的实例进行续费操作
     * 
     * @param autoRenew 自动续费参数
     * @param from 请求来源
     * @throws BceInternalResponseException 内部响应异常
     */

    @ApiOperation(value = "已创建实例续费")
    @RequestMapping(value = "/createdOrderAutoRenew", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void createdOrderAutoRenew (@RequestBody @Valid AutoRenew autoRenew,
                                       @RequestParam(required = false, defaultValue = "") String from) {
        List<Order> orderList = new ArrayList<>();
        List<String> instanceIds = autoRenew.getInstanceIds();
        try {
            OrderIdsRequest orderIdsRequest = instanceService.getOrderId(instanceIds, from);
            if (orderIdsRequest.getOrderIds() != null && orderIdsRequest.getOrderIds().size() > 0) {
                for (String orderId : orderIdsRequest.getOrderIds()) {
                    LOGGER.debug("orderId is {}.", orderId);
                    // 兼容order service从iamService中获取stsCredential
                    StsCredential stsCredential = clientFactory.getUserStsAccessKey();
                    com.baidu.bce.iam.facade.model.sts.StsCredential iamStsCredential =
                            new com.baidu.bce.iam.facade.model.sts.StsCredential();
                    iamStsCredential.setAccessKeyId(stsCredential.getAccessKeyId());
                    iamStsCredential.setSecretAccessKey(stsCredential.getSecretAccessKey());
                    iamStsCredential.setSessionToken(stsCredential.getSessionToken());
                    iamStsCredential.setUserId(stsCredential.getUserId());
                    iamStsCredential.setRoleId(stsCredential.getRoleId());
                    iamStsCredential.setExpiration(stsCredential.getExpiration());
                    iamStsCredential.setCreateTime(stsCredential.getCreateTime());
                    BceSessionContext bceSessionContext = new BceSessionContext();
                    bceSessionContext.setStsCredential(iamStsCredential);
                    SessionService.setSessionContext(bceSessionContext);
                    Order order = new RdsDetailBuilder(orderId).build();
                    orderList.add(order);
                }
            }
            instanceService.setAutoRenewRules(orderList, orderIdsRequest.getInstanceIds(), autoRenew);
        } catch (BceInternalResponseException e) {
            LogicRdsExceptionHandler.handle(e);
        }

    }
    /**
      * 修改接口
      * 用于修改实例端口信息
      * 
      * @param instanceId 实例ID
      * @param request 包含端口信息的请求体
      * @param from 来源参数，可选，默认值为空字符串
      */

    @ApiOperation(value = "修改接口")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.PUT, params = {"port"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateInstancePort(@IdPermission @IdMapper @PathVariable String instanceId,
                                   @RequestBody @Valid InstanceUpdatePortRequest request,
                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update port. instanceId is {}", instanceId);
        try {
            instanceService.updateInstancePort(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 检查实例是否支持修改端口
      * 根据实例ID检查实例是否支持修改端口，如果不支持则返回错误信息
      * 
      * @param instanceId 实例ID，用于标识需要检查的实例
      * @param from 请求来源，默认为空字符串
      * @return InstanceCheckPortResponse 检查端口的结果
      */

    @RequestMapping(value = "/{instanceId}/entry/check", method = RequestMethod.GET)
    @ApiOperation(value = "检查实例是否支持修改端口")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public InstanceCheckPortResponse instanceEnrtyCheck(@IdPermission @PathVariable @IdMapper String instanceId,
                                                       @RequestParam(required = false, defaultValue = "")
                                                               String from) {
        InstanceCheckPortResponse response = new InstanceCheckPortResponse();
        try {
            response = instanceService.instanceEnrtyCheck(instanceId);
        } catch (Exception e) {
            LOGGER.error("get instance enrty check error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 检查实例是否支持热变配
     * 此函数用于检查指定实例是否支持热变配操作
     * 
     * @param instanceId 实例ID，用于指定要检查的实例
     * @param request 包含热变配请求信息的对象
     * @return 返回热变配检查的结果
     */

    @ApiOperation(value = "检查实例是否支持热变配")
    @RequestMapping(value = "/{instanceId}/checkHotUpgrade", method = RequestMethod.POST)
    public HotUpgradeResponse checkHotUpgrade(@PathVariable String instanceId,
                                          @RequestBody HotupgradeRequest request) {
        LOGGER.debug("get pnetIp. instanceId is {}", instanceId);
        HotUpgradeResponse response = new HotUpgradeResponse();
        try {
            response = instanceService.checkHotUpgrade(instanceId, request);

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 修改实例的维护时间窗口
     * 用于修改RDS实例的维护时间窗口
     * 
     * @param instanceId 实例ID
     * @param request 维护时间窗口请求参数
     * @throws RDSBusinessExceptions.InstanceNotFoundException 实例未找到异常
     * @throws RDSBusinessExceptions.MissingParameterException 缺少参数异常
     * @throws RDSBusinessExceptions.InstanceNotSatisfiableException 实例不满足条件异常
     * @throws RDSBusinessExceptions.InvalidParameterException 参数无效异常
     * @throws RDSBusinessExceptions.InstanceStatusErrorException 实例状态错误异常
     * @throws RDSBusinessExceptions.InternalDBErrorException 内部数据库错误异常
     * @throws RDSBusinessExceptions.InternalServerErrorException 服务器内部错误异常
     */

    @ApiOperation(value = "修改实例的维护时间窗口")
    @RequestMapping(value = "/{instanceId}/maintaintime", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateMaintaintime(@IdPermission @PathVariable String instanceId,
                                              @RequestBody MaintainDurationRequest request) {
        try {
            instanceService.updateMaintaintime(instanceId, request);
        } catch (BceInternalResponseException e) {
            if ("RDSInstanceNotFound".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceNotFoundException();
            }
            else if ("MissingParameter".equals(e.getCode())) {
                throw new RDSBusinessExceptions.MissingParameterException();
            }
            else if ("InstanceNotSatisfiable ".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceNotSatisfiableException();
            }
            else if ("InvalidParameterValue".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InvalidParameterException();
            }
            else if ("InstanceStatusError".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InstanceStatusErrorException();
            }
            else if ("InternalDBError".equals(e.getCode())) {
                throw new RDSBusinessExceptions.InternalDBErrorException();
            }
            else {
                throw new RDSBusinessExceptions.InternalServerErrorException();
            }
        }
    }
    /**
     * 获取用户当前地域下的时间窗口内执行的任务列表
     * 根据用户请求获取其当前地域下指定时间窗口内执行的任务列表
     * 
     * @param request 用户请求，包含地域、时间窗口等信息
     * @return MaintaintimeResponse 响应对象，包含任务列表
     */

    @ApiOperation(value = "获取用户当前地域下的时间窗口内执行的任务列表")
    @RequestMapping(value = "/maintaintime/task", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public MaintaintimeResponse getMaintaintimeTasks(@IdPermission @RequestBody TaskRequest request) {
        MaintaintimeResponse response = new MaintaintimeResponse();
        try {
            MaintaintimeTasks tasks = instanceService.getMaintaintimeTasks(request);
            response.setResult(tasks);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 取消指定时间窗口执行的主备切换任务
      * 在任务处于created（已创建）状态时支持取消
      * 
      * @param taskId 任务ID
      */

    @ApiOperation(value = "指定时间窗口执行的主备切换任务，在任务处于created（已创建）状态时支持取消")
    @RequestMapping(value = "/maintaintime/task/{taskId}/cancel", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void cancelMaintaintimeTask(@PathVariable String taskId) {
        try {
            instanceService.cancelMaintaintimeTask(taskId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
}
