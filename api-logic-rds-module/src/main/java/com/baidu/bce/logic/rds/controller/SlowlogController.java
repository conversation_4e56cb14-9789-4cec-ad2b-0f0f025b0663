package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogListResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogSummaryRequest;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogSummaryResponse;
import com.baidu.bce.logic.rds.service.SlowlogService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;

/**
 * Created by luping03 on 17/11/3.
 */
@RestController
@RequestMapping("/v1/rds/instances/{instanceId}/slowlogs")
public class SlowlogController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private SlowlogService slowlogService;
    
    /**
      * 获取日志列表
      * 根据实例ID和日期时间获取慢日志列表
      * 
      * @param instanceId 实例ID
      * @param datetime 日期时间
      * @param from 起始位置（可选）
      * @return 慢日志列表响应
      */
    @ApiOperation(value = "获取日志列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowlogListResponse getList(@IdPermission @PathVariable String instanceId,
                                       @RequestParam String datetime,
                                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log list.");
        SlowlogListResponse response = null;
        try {
            response = slowlogService.list(instanceId, datetime);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取日志详情
      * 根据日志ID获取日志详情信息
      * 
      * @param instanceId 实例ID
      * @param logId 日志ID
      * @param downloadValidTimeInSec 下载有效时间
      * @param from 来源
      * @return 日志详情
      */

    @ApiOperation(value = "获取日志详情")
    @RequestMapping(value = "/{logId:.+}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowlogGetResponse detail(@IdPermission @PathVariable String instanceId,
                                     @PathVariable String logId,
                                     @RequestParam Integer downloadValidTimeInSec,
                                     @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log detail.");
        SlowlogGetResponse response = null;
        try {
            response = slowlogService.detail(instanceId, logId, downloadValidTimeInSec);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 慢日志统计报表
     * 该函数用于处理慢日志的统计报表请求
     * 
     * @param request 慢日志统计的请求体，包含统计所需的各种参数
     * @return 慢日志统计的响应体
     */

    @ApiOperation(value = "慢日志统计报表")
    @RequestMapping(value = "/summary", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowlogSummaryResponse slowlogSummary(@Valid @RequestBody SlowlogSummaryRequest request) {
        SlowlogSummaryResponse response = null;
        try {
            response = slowlogService.slowlogSummary(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

}
