package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.idempotent.annotation.Idempotent;
import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.config.ConfigItem;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyHistoryResponse;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyItem;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyRequest;
import com.baidu.bce.internalsdk.rds.model.config.OpenApiConfigItem;
import com.baidu.bce.logic.rds.service.ArgumentService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.model.argument.ConfigList;
import com.baidu.bce.logic.rds.service.model.argument.OpenApiConfigList;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;

/**
 * Created by luping03 on 17/10/12.
 */
@RestController
@RequestMapping("/v1/instance/{instanceId}/parameter")
public class OpenApiArgumentController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiArgumentController.class);

    @Autowired
    private ArgumentService argumentService;
    
    /**
     * 获取配置信息列表
     * 该接口用于获取配置信息列表，支持通过instanceId和keyword进行过滤查询。
     * 
     * @param instanceId 实例ID，用于指定要查询的配置信息所属的实例
     * @param keyword 关键字，用于过滤查询结果
     * @param servletResponse HttpServletResponse对象，用于设置响应头ETag
     * @return 返回配置信息列表的封装对象OpenApiConfigList
     */
    @ApiOperation(value = "获取配置信息列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenApiConfigList getList(@IdPermission @IdMapper @PathVariable String instanceId,
                                     @RequestParam(required = false) String keyword,
                                     HttpServletResponse servletResponse) {
        String from = "api";

        LOGGER.debug("get arguments list. instanceId is {}", instanceId);
        ConfigList response = null;
        OpenApiConfigList openApiConfigList = null;
        try {
            response = argumentService.list(instanceId, keyword, from);
            if (response.getItems() != null && !response.getItems().isEmpty()) {
                servletResponse.setHeader("ETag", response.getItems().get(0).getEtag());
                response.setETag(response.getItems().get(0).getEtag());
            }

            openApiConfigList = new OpenApiConfigList();
            openApiConfigList.setEtag(response.getETag());
            if (response.getItems() != null) {
                openApiConfigList.setParameters(new ArrayList<OpenApiConfigItem>(response.getItems().size()));
                for (ConfigItem configItem : response.getItems()) {
                    OpenApiConfigItem openApiConfigItem = new OpenApiConfigItem();
                    BeanUtils.copyProperties(configItem, openApiConfigItem);
                    openApiConfigItem.setDesc(configItem.getDescription());
                    openApiConfigItem.setDynamic(String.valueOf(configItem.getDynamic()));
                    openApiConfigItem.setModifiable(String.valueOf(configItem.getModifiable()));

                    openApiConfigList.getParameters().add(openApiConfigItem);
                }
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return openApiConfigList;
    }
    /**
      * 修改配置信息
      * 用于修改实例的配置信息，支持从API调用时设置默认的ApplyMethod为immediate
      * 
      * @param instanceId 实例ID
      * @param request 修改配置信息的请求体
      * @param etag 请求头中的etag，用于乐观锁控制
      * @param from 请求来源标识，用于判断是否为API调用
      * @throws Exception 如果修改配置过程中发生异常，则抛出
      */

    @ApiOperation(value = "修改配置信息")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public void modify(@IdPermission @IdMapper @PathVariable String instanceId,
                       @Valid @RequestBody ConfigModifyRequest request,
                       @RequestHeader ("x-bce-if-match") String etag,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("modify argument. instanceId is {}", instanceId);
        try {

            // api给applyMethod参数设置默认值
            if (StringUtils.isNotBlank(from) && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
                if (request != null && request.getParameters() != null) {
                    for (ConfigModifyItem each : request.getParameters()) {

                        if (StringUtils.isBlank(each.getApplyMethod())) {
                            each.setApplyMethod("immediate");
                            each.setEtag(etag);
                            LOGGER.debug("modify params : Append 'ApplyMethod' params default value 'immediate'.");
                        }
                    }
                }
            }

            argumentService.modify(instanceId, request, etag);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 修改历史
      * 获取指定实例ID的参数修改历史
      * 
      * @param instanceId 实例ID
      * @param from 分页查询起始位置，默认为空字符串
      * @return 参数修改历史响应对象
      */

    @ApiOperation(value = "修改历史")
    @RequestMapping(value = "/history", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ConfigModifyHistoryResponse history(@IdPermission @PathVariable String instanceId,
                                               @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get arguments modified history. instanceId is {}", instanceId);
        ConfigModifyHistoryResponse response = null;
        try {
            response = argumentService.history(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
}
