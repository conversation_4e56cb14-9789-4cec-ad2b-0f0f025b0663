package com.baidu.bce.logic.rds.controller.group;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.rds.model.group.BatchJoinRequest;
import com.baidu.bce.internalsdk.rds.model.group.CreateGroupRequest;
import com.baidu.bce.internalsdk.rds.model.group.FollowerIdRequest;
import com.baidu.bce.internalsdk.rds.model.group.GroupForceChangeLeaderRequest;
import com.baidu.bce.internalsdk.rds.model.group.GroupForceChangeLeaderResponse;
import com.baidu.bce.internalsdk.rds.model.group.GroupInfo;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupDetailResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.utils.JsonConvertUtil;
import com.baidu.bce.logic.rds.controller.InstanceController;
import com.baidu.bce.logic.rds.model.ResultResponse;
import com.baidu.bce.logic.rds.model.group.CheckPingRequest;
import com.baidu.bce.logic.rds.model.group.InstanceIdRequest;
import com.baidu.bce.logic.rds.model.group.CreateGroupRequestVo;
import com.baidu.bce.logic.rds.model.group.FollowerIdRequestVo;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.group.InstanceGroupService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.group.GroupListRequest;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import javax.validation.Valid;
import java.util.Map;

/**
 * Created by shiyuxin on 19/5/20.
 */
@RestController
@RequestMapping("/v1/instance/group")
public class OpenApiGroupController {

    private static final Logger LOGGER = LoggerFactory.getLogger(GroupAccountController.class);

    @Autowired
    private InstanceGroupService instanceGroupService;

    @Autowired
    IdMapperService idMapperService;

    /**
     * 实例组：通过pageNo方式分页
     * 实例组：列表页通过pageNo方式分页
     *
     * @param daysToExpiration 距离实例组过期剩余天数，默认为-1，表示不限制
     * @param order 排序方式，默认为空
     * @param orderBy 排序字段，默认为空
     * @param pageNo 页码，默认为1
     * @param pageSize 每页显示数量，默认为1000
     * @param filterMapStr 过滤条件，默认为空
     * @param machineType 机器类型，默认为空
     * @return 分页后的实例组信息
     */
    @RequestMapping(method = RequestMethod.GET, params = {"manner=page"})
    @ApiOperation(value = "实例组: 通过pageNo方式分页")
    public LogicPageResultResponse<GroupInfo> list(
            @RequestParam(required = false, defaultValue = "-1") Integer daysToExpiration,
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String filterMapStr,
            @RequestParam(required = false, defaultValue = "") String machineType) {

        GroupListRequest listRequest = new GroupListRequest(daysToExpiration, order, orderBy, pageNo, pageSize);

        if (StringUtils.isNotEmpty(filterMapStr)) {
            filterMapStr = StringEscapeUtils.unescapeHtml(filterMapStr).replaceAll("\\\\", "\\\\\\\\");
            LOGGER.info("list instance[page], after unescapeHtml filterMaperStr is : {}", filterMapStr);
            InstanceController.FilterMap filterMap =
                    JsonConvertUtil.fromJSON(filterMapStr, InstanceController.FilterMap.class);
            listRequest.setFilterMap(filterMap);

        }

        LogicPageResultResponse<GroupInfo> result  = instanceGroupService.list(listRequest);

        return result;
    }

    /**
      * 实例组详情
      * 根据实例组ID获取实例组详情
      * 
      * @param groupId 实例组ID
      * @return 实例组详情
      */
    @RequestMapping(value = "/{groupId}", method = RequestMethod.GET)
    @ApiOperation(value = "实例组: 实例组详情")
    public InstanceGroupDetailResponse detail(@PathVariable String groupId) {
        return instanceGroupService.detail(groupId);
    }

    /**
     * 实例组前置检查（GTID检查）
     * 该函数用于进行实例组的前置检查，主要检查GTID。如果检查通过，则返回true，否则返回false。
     * 
     * @param request 包含了需要检查的实例ID的请求体
     * @return 返回检查结果，true表示检查通过，false表示检查不通过
     */
    @RequestMapping(value = "/checkGtid", method = RequestMethod.POST)
    @ApiOperation(value = "实例组:实例组前置检查（GTID检查）")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ResultResponse<Boolean> checkGtid(@RequestBody @IdPermission @Valid InstanceIdRequest request) {

        ResultResponse<Boolean> result = new ResultResponse<Boolean>();

        try {
            instanceGroupService.checkGtid(request.getInstanceId());
            result.setResult(true);
        } catch (Exception e) {
            LOGGER.error("get checkGtid error", e);
            result.setResult(false);
        }
        return result;
    }

    /**
      * 实例组前置检查
      * 实例组前置检查（实例连通性检查）
      * 
      * @param request 包含sourceId和targetId的请求体
      * @return ResultResponse<Boolean> 检查结果
      */
    @RequestMapping(value = "/checkPing", method = RequestMethod.POST)
    @ApiOperation(value = "实例组:: 实例组前置检查（实例连通性检查）")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ResultResponse<Boolean> checkPing(@RequestBody @Valid @IdPermission CheckPingRequest request) {

        ResultResponse<Boolean> result = new ResultResponse<Boolean>();
        try {
            instanceGroupService.checkPing(request.getSourceId(), request.getTargetId());
            result.setResult(true);
        } catch (Exception e) {
            LOGGER.error("get checkPing error", e);
            result.setResult(false);
        }
        return result;
    }

    /**
      * 实例组前置检查（数据检查）
      * 进行实例组的前置数据检查，确保数据符合要求
      * 
      * @param request 实例组ID请求体，包含需要进行数据检查的实例组ID
      * @return 检查结果，true表示数据检查通过，false表示数据检查未通过
      */
    @RequestMapping(value = "/checkData", method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 实例组前置检查（数据检查）")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ResultResponse<Boolean> checkData(@RequestBody @Valid @IdPermission InstanceIdRequest request) {
        ResultResponse<Boolean> result = new ResultResponse<Boolean>();
        try {
            instanceGroupService.checkData(request.getInstanceId());
            result.setResult(true);
        } catch (Exception e) {
            LOGGER.error("get checkData error", e);
            result.setResult(false);
        }
        return result;
    }


    /**
     * 实例组: 创建实例组
     * 实例组: 创建实例组
     *
     * @param request 创建实例组的请求参数
     * @return 返回创建实例组的结果，包括状态码等
     */
    @RequestMapping(method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 创建实例组")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ResultResponse<Integer> create(@RequestBody @IdMapper @Valid CreateGroupRequestVo request) {

        ResultResponse<Integer> result = new ResultResponse<Integer>();
        CreateGroupRequest req = new CreateGroupRequest();
        BeanUtils.copyProperties(request, req);
        instanceGroupService.createGroup(req);
        result.setResult(200);

        return result;
    }
    /**
      * 加入某个实例组
      * 将指定的实例加入到某个实例组中
      * 
      * @param groupId 实例组ID
      * @param request 包含实例ID的请求体
      * @return 操作结果
      */

    @RequestMapping(value = "/{groupId}/instance", method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 加入某个实例组")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ResultResponse<Integer> follower(@PathVariable String groupId,
                                            @RequestBody @IdPermission FollowerIdRequestVo request) {

        ResultResponse<Integer> result = new ResultResponse<Integer>();
        FollowerIdRequest req = new FollowerIdRequest();
        BeanUtils.copyProperties(request, req);
        req.setGroupId(groupId);
        instanceGroupService.joinGroup(req);
        result.setResult(200);

        return result;
    }
    /**
      * 批量加入实例组
      * 该函数用于批量将实例加入到指定的实例组中
      * 
      * @param request 批量加入实例组请求体，包含leaderId和followerIds等信息
      * @throws BceInternalResponseException 如果内部处理请求时发生异常
      */

    @RequestMapping(value = "/batchjoin", method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 批量加入实例组")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void batchjoin(@RequestBody BatchJoinRequest request) {
//        String leaderId = request.getLeaderId();
//        List<String> followerIds = request.getFollowerIds();
//        List<String> longIds = new ArrayList<>();
//        for (int i = 0;i < followerIds.size();i++) {
//            String longId = idMapperService.getInstanceUuid(followerIds.get(i));
//            longIds.add(longId);
//        }
//
//        String longLeaderId = idMapperService.getInstanceUuid(leaderId);
//
//        request.setLeaderId(longLeaderId);
//        request.setFollowerIds(longIds);

        try {
            instanceGroupService.batchjoin(request);
        } catch (BceInternalResponseException e) {
            BasisUtils.logicRdsExceptions(e);
        }
    }
    /**
      * 修改热活实例组的名称
      * 通过POST请求修改指定实例组的名称
      * 
      * @param groupId 实例组ID
      * @param params 包含新名称的参数集合
      */

    @RequestMapping(value = "/{groupId}/name", method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 修改热活实例组的名称")
    public void changeName(@PathVariable String groupId,
                           @RequestBody Map<String, String> params) {
        instanceGroupService.changeName(groupId,  params.get("name"));
    }
    /**
     * 实例组删除接口
     * 用于删除指定的实例组
     * 
     * @param groupId 实例组的ID
     */

    @RequestMapping(value = "/{groupId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "实例组: 热活实例组的删除")
    public void delete(@PathVariable String groupId) {
        instanceGroupService.delete(groupId);
    }
    /**
      * 实例组: 主角色变更
      * 根据groupId和新的主角色ID，变更实例组的主角色
      * 
      * @param groupId 实例组的ID
      * @param request 包含新主角色ID的请求体
      */

    @RequestMapping(value = "/{groupId}/instance", method = RequestMethod.PUT)
    @ApiOperation(value = "实例组: 主角色变更")
    public void changeLeader(@PathVariable String groupId,
                             @RequestBody @Valid CreateGroupRequestVo request) {
        instanceGroupService.changeLeader(groupId, request.getLeaderId());
    }

    /**
      * 退出热活实例组
      * 调用实例组服务，实现退出热活实例组功能
      *
      * @param groupId 实例组ID
      * @param instanceId 实例ID
      * @throws RDSExceptions.ClusterNotFoundWithCurrentRegion 当前地域不存在该实例时抛出异常
      */
    @RequestMapping(value = "/{groupId}/instance/{instanceId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "实例组: 退出热活实例组")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void signOut(@PathVariable String groupId,
                        @PathVariable @IdPermission @IdMapper String instanceId) {
        if (StringUtils.isEmpty(instanceId)) {
            // 当前地域不存在该实例，请确实该实例所属地域
            LOGGER.error("The instance does not exist in the current region.");
            throw new RDSExceptions.ClusterNotFoundWithCurrentRegion();
        }
        instanceGroupService.signOut(groupId, instanceId);
    }
    /**
      * 强切实例组
      * 强制更换实例组的leader节点
      * 
      * @param groupId 实例组ID
      * @param changeLeaderRequest 更换leader请求参数
      * @return 更换leader后的响应结果
      */

    @RequestMapping(value = "/{groupId}/forceChange", method = RequestMethod.PUT)
    @ApiOperation(value = "实例组: 强切")
    public GroupForceChangeLeaderResponse forceChangeLeader(
            @PathVariable String groupId,
            @RequestBody GroupForceChangeLeaderRequest changeLeaderRequest) {
        GroupForceChangeLeaderResponse response = null;
        try {
            response = instanceGroupService.forceChangeLeader(groupId, changeLeaderRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }


}
