package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.idempotent.annotation.Idempotent;
import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import com.baidu.bce.logic.rds.service.model.instance.OpenApiInstanceMarkerListResponse;
import com.baidu.bce.logic.rds.service.model.order.RdsCreateOrderRequestVo;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.servicecatalog.model.order.PaymentModel;
import com.wordnik.swagger.annotations.ApiOperation;
import io.swagger.annotations.Api;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

@Api("实例管理V2接口")
@RestController
@RequestMapping("/v2/instance")
public class OpenApiV2InstanceController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiV2InstanceController.class);

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private LogicRdsClientFactory clientFactory;
    /**
      * 批量变配
      * 根据实例ID和变配参数，进行批量变配操作，包括主实例和只读实例的变配。
      * 支持一键批量变配和按照原批量变配逻辑进行处理。
      * 
      * @param instanceId 实例ID
      * @param priceDiffModel 变配参数，包括内存、存储等
      * @return 返回订单ID列表
      */

    @ApiOperation(value = "批量变配")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.PUT, params = {"resize"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public List<String> createBatchResizeOrder(@IdPermission @PathVariable @IdMapper String instanceId,
                                             @RequestBody PriceDiffModel priceDiffModel) {
        String from = RDSConstant.FROM_API;
        RdsCreateOrderRequestVo<PriceDiffModel> request = new RdsCreateOrderRequestVo<>();
        // 此处需兼容主实例、只读实例同时变配的情况
//        if (CollectionUtils.isEmpty(priceDiffModel.getReadReplicas())) {
//            request.setPaymentMethod(new HashSet<PaymentModel>(0));
//            request.setItems(new ArrayList<RdsCreateOrderRequestVo.Item<PriceDiffModel>>(1));
//            request.getItems().add(new RdsCreateOrderRequestVo.Item<PriceDiffModel>());
//            request.getItems().get(0).setPaymentMethod(new HashSet<PaymentModel>(0));
//            request.getItems().get(0).setConfig(priceDiffModel);
//            priceDiffModel.setAllocatedMemoryInGB(priceDiffModel.getMemoryCapacity());
//            priceDiffModel.setAllocatedStorageInGB(priceDiffModel.getVolumeCapacity());
//            priceDiffModel.setInstanceId(instanceId);
//        } else {
        // 此处开启一键批量变配
        if (priceDiffModel.getIsBatchResize()) {
            LOGGER.debug("one shot batch resize...");
            // String masterId = priceDiffModel.getInstanceId();
            RDSClient2 rdsClient2 = clientFactory.createRdsClient2ByInstanceId(instanceId);
            Instance instance1 = rdsClient2.instanceDescribe(instanceId).getInstance();
            InstanceExtension instance = new InstanceExtension(instance1);
            // 拿出主实例详情拓扑中只读的规格配置，判断哪些需要同时变配
            List<Instance.ReadReplicaIdMapping> readReplicaIdMapping = instance.getTopology().getReadReplicaIdMapping();
            List<Instance.ReadReplicaIdMapping> needResizeMappings = new ArrayList<>();
            // 取出主实例目标存储容量
            Integer masterVolumeCapacity = priceDiffModel.getVolumeCapacity();
            for (Instance.ReadReplicaIdMapping replicaIdMapping : readReplicaIdMapping) {
                if (replicaIdMapping.getAllocatedStorageInGB() < masterVolumeCapacity) {
                    // 将存储大小不满足的只读实例加入变配队列
                    replicaIdMapping.setAllocatedStorageInGB(masterVolumeCapacity);
                    needResizeMappings.add(replicaIdMapping);
                }
            }
            LOGGER.debug("needResizeMappings.size() : {}", needResizeMappings.size());
            request.setPaymentMethod(new HashSet<PaymentModel>(0));
            // 此处需保证 Item 项的大小等于主+只读的列表大小
            int initCapacity = needResizeMappings.size() + 1;
            request.setItems(new ArrayList<RdsCreateOrderRequestVo.Item<PriceDiffModel>>(initCapacity));
            // 先放主动发起的变配实例
            request.getItems().add(new RdsCreateOrderRequestVo.Item<PriceDiffModel>());
            request.getItems().get(0).setPaymentMethod(new HashSet<PaymentModel>(0));
            request.getItems().get(0).setConfig(priceDiffModel);
            priceDiffModel.setAllocatedMemoryInGB(priceDiffModel.getMemoryCapacity());
            priceDiffModel.setAllocatedStorageInGB(priceDiffModel.getVolumeCapacity());
            // 这条不能缺
            priceDiffModel.setInstanceId(instanceId);
            // 再放发起的从实例变配（目前只支持只读实例）
            for (int i = 1; i < needResizeMappings.size() + 1; i++) {
                request.getItems().add(new RdsCreateOrderRequestVo.Item<PriceDiffModel>());
                request.getItems().get(i).setPaymentMethod(new HashSet<PaymentModel>(0));
                request.getItems().get(i).setConfig(new PriceDiffModel());
                request.getItems().get(i).getConfig().setAllocatedMemoryInGB
                        (needResizeMappings.get(i - 1).getAllocatedMemoryInMB() / 1024);
                request.getItems().get(i).getConfig().setAllocatedStorageInGB
                        (needResizeMappings.get(i - 1).getAllocatedStorageInGB());
                request.getItems().get(i).getConfig().setCpuCount
                        (needResizeMappings.get(i - 1).getCpuCount());
                request.getItems().get(i).getConfig().setMemoryCapacity
                        (needResizeMappings.get(i - 1).getAllocatedMemoryInMB() / 1024);
                request.getItems().get(i).getConfig().setVolumeCapacity
                        (needResizeMappings.get(i - 1).getAllocatedStorageInGB());
                request.getItems().get(i).getConfig().setInstanceId(needResizeMappings.get(i - 1).getAppId());
            }
            LOGGER.debug(" one shot batch resize, priceDiffModel.getReadReplicas() : {}",
                    request.getItems().size());
        }
        // 此时需按照原批量变配逻辑进行处理
        else {
            LOGGER.debug("batch resize, priceDiffModel.getReadReplicas() : {}",
                    priceDiffModel.getReadReplicas().size());
            request.setPaymentMethod(new HashSet<PaymentModel>(0));
            // 此处需保证 Item 项的大小等于主+只读的列表大小
            int initCapacity = priceDiffModel.getReadReplicas().size() + 1;
            request.setItems(new ArrayList<RdsCreateOrderRequestVo.Item<PriceDiffModel>>(initCapacity));
            // 先放主动发起的变配实例
            request.getItems().add(new RdsCreateOrderRequestVo.Item<PriceDiffModel>());
            request.getItems().get(0).setPaymentMethod(new HashSet<PaymentModel>(0));
            request.getItems().get(0).setConfig(priceDiffModel);
            priceDiffModel.setAllocatedMemoryInGB(priceDiffModel.getMemoryCapacity());
            priceDiffModel.setAllocatedStorageInGB(priceDiffModel.getVolumeCapacity());
            // 这条不能缺
            priceDiffModel.setInstanceId(instanceId);
            // 再放发起的从实例变配（目前只支持只读实例）
            for (int i = 1; i < priceDiffModel.getReadReplicas().size() + 1; i++) {
                request.getItems().add(new RdsCreateOrderRequestVo.Item<PriceDiffModel>());
                request.getItems().get(i).setPaymentMethod(new HashSet<PaymentModel>(0));
                request.getItems().get(i).setConfig(priceDiffModel.getReadReplicas().get(i - 1));
                request.getItems().get(i).getConfig().setAllocatedMemoryInGB
                        (request.getItems().get(i).getConfig().getMemoryCapacity());
                request.getItems().get(i).getConfig().setAllocatedStorageInGB
                        (request.getItems().get(i).getConfig().getVolumeCapacity());
            }
        }
//        }
        List<Order> orders = new ArrayList<>();

        try {
             orders = instanceService.resizeBatchInstance(instanceId, request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        List<String> orderIds = new ArrayList<>(orders.size());

        // 只给用户返回订单 id
        for (Order order : orders) {
            orderIds.add(order.getUuid());
        }

        return orderIds;
    }


    /**
     * 通过marker方式分页查询实例列表
     * 通过marker方式分页查询实例列表 该接口支持通过marker方式分页查询实例列表，每次请求返回的marker作为下一次请求的marker
     *
     * @param marker 分页查询的起始位置标识，默认为"-1"
     * @param maxKeys 每页返回的最大数量，默认为1000
     * @return OpenApiInstanceMarkerListResponse 类型的实例分页列表响应体
     * @throws Exception 查询实例列表时可能发生的异常
     */
    @RequestMapping(method = RequestMethod.GET)
    @ApiOperation(value = "V2实例列表: 通过marker方式分页")
    public OpenApiInstanceMarkerListResponse listInstanceByMarker(
            @RequestParam(required = false, defaultValue = "-1") String marker,
            @RequestParam(required = false, defaultValue = "1000") Integer maxKeys,
            @RequestParam(required = false, defaultValue = "") String vnetIp) {
        LOGGER.debug("list instance[marker], marker:{}, maxKeys:{}.", marker, maxKeys);
        LogicMarkerResultResponse<InstanceAbstract> pageResultResponse = null;
        OpenApiInstanceMarkerListResponse openApiInstanceMarkerListResponse = null;
        try {
            pageResultResponse = instanceService.listV2InstanceWithMarkerByMultiKey(marker, maxKeys, vnetIp);

            openApiInstanceMarkerListResponse = new OpenApiInstanceMarkerListResponse();
            openApiInstanceMarkerListResponse.setMarker(pageResultResponse.getMarker());
            openApiInstanceMarkerListResponse.setIsTruncated(pageResultResponse.getIsTruncated());
            openApiInstanceMarkerListResponse.setNextMarker(pageResultResponse.getNextMarker());
            openApiInstanceMarkerListResponse.setMaxKeys(pageResultResponse.getMaxKeys());
            openApiInstanceMarkerListResponse.setInstances(new ArrayList<>(pageResultResponse.getResult()));
            for (InstanceAbstract instanceAbstract : openApiInstanceMarkerListResponse.getInstances()) {
                if (instanceAbstract.getInstanceShortId() != null) {
                    instanceAbstract.setInstanceId(instanceAbstract.getInstanceShortId());
                }
                instanceAbstract.setMemoryCapacity(instanceAbstract.getAllocatedMemoryInGB());
                instanceAbstract.setVolumeCapacity(instanceAbstract.getAllocatedStorageInGB());
                instanceAbstract.setUsedStorage(instanceAbstract.getUsedStorageInGB());
                instanceAbstract.setPublicAccessStatus(instanceAbstract.getEipStatus());
                instanceAbstract.setPaymentTiming(instanceAbstract.getProductType());
                if (instanceAbstract.getResourceGroup() != null
                        && instanceAbstract.getResourceGroup().getGroups() != null
                        && !instanceAbstract.getResourceGroup().getGroups().isEmpty()) {
                    instanceAbstract.setResourceGroupId(
                            instanceAbstract.getResourceGroup().getGroups().get(0).getGroupId());
                    instanceAbstract.setResourceGroupName(
                            instanceAbstract.getResourceGroup().getGroups().get(0).getName());
                }
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return openApiInstanceMarkerListResponse;
    }
}
