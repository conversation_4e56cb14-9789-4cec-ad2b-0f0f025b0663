package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.security.SslAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.security.SslInfoResponse;
import com.baidu.bce.logic.rds.service.WhiteListService;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@RestController
@RequestMapping(value = "/api/rds/ssl/", produces = {"application/json"})
@Api(value = "RDS Dashboard白名单管理API")
public class ConsoleApiSslController {

    @Autowired
    private WhiteListService whiteListService;
    /**
      * 获取SSL状态接口
      * 根据实例ID获取SSL状态信息
      * 
      * @param requestBody 实例ID请求体
      * @return SSL状态响应结果
      */

    @RequestMapping(value = "get", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<SslInfoResponse> get(@Valid @RequestBody InstanceIdRequest requestBody) {
        EdpResultResponse<SslInfoResponse> response = new EdpResultResponse<>();
        response.setResult(whiteListService.getSslState(requestBody.getInstanceId()));
        return response;
    }
    /**
      * 设置函数
      * 用于处理设置请求，更新SSL访问权限，并返回操作结果
      * 
      * @param requestBody 请求体，包含需要设置的实例ID和其他必要信息
      * @return EdpResultResponse<Boolean> 操作结果，包含是否成功
      */

    @RequestMapping(value = "set", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> set(@Valid @RequestBody SslAccessibleRequest requestBody) {
        whiteListService.updateSslAccess(requestBody.getInstanceId(), requestBody);
        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(true);
        return edpResultResponse;
    }
    /**
      * 获取CA证书
      * 根据请求获取CA证书内容，并设置响应头，返回CA证书的字节数组
      * 
      * @param servletResponse HttpServletResponse对象，用于设置响应头和返回数据
      * @return CA证书的字节数组
      */

    @RequestMapping(value = "get/ca", method = RequestMethod.GET)
    public byte[] getCa(javax.servlet.http.HttpServletResponse servletResponse) {
        String content = whiteListService.queryCa().getCaContent();
        String fileName = "";
        try {
            fileName = URLEncoder.encode("ca.pem", "UTF-8");
        } catch (UnsupportedEncodingException ignored) {
        }
        servletResponse.setHeader("Content-Disposition", "attachment; filename*=utf-8''" + fileName);
        servletResponse.setHeader("Content-Type", "application/octet-stream; charset=utf-8");
        byte[] bytes = content.getBytes();
        return bytes;
    }
}
