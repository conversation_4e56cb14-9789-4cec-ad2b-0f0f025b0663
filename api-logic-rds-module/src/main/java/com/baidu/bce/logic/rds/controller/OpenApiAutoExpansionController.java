package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.AutoResizeConfigResponse;
import com.baidu.bce.internalsdk.rds.model.SupportEnabledDiskAutoResizeResponse;
import com.baidu.bce.internalsdk.rds.model.UpdateAutoExpansionConfigRequest;
import com.baidu.bce.logic.rds.service.AutoExpansionService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;

@RestController
@RequestMapping("/v1/instance")
public class OpenApiAutoExpansionController {
    @Autowired
    public AutoExpansionService autoExpansionService;
    /**
      * 实例是否支持自动扩容
      * 根据实例ID查询实例是否支持自动扩容
      * 
      * @param instanceId 实例ID
      * @return SupportEnabledDiskAutoResizeResponse 返回结果
      */

    @ApiOperation(value = "实例是否支持自动扩容")
    @RequestMapping(value = "/{instanceId}/autoExpansion", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SupportEnabledDiskAutoResizeResponse supportAutoExpansion(
            @IdPermission @PathVariable @IdMapper String instanceId) {
        SupportEnabledDiskAutoResizeResponse resultResponse = autoExpansionService.supportAutoExpansion(instanceId);
        return resultResponse;
    }
    /**
      * 获取自动扩容配置信息
      * 根据实例ID获取自动扩容的配置信息
      * 
      * @param instanceId 实例ID
      * @return 自动扩容配置信息
      */

    @ApiOperation(value = "自动扩容配置信息")
    @RequestMapping(value = "/{instanceId}/autoResizeConfig", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public AutoResizeConfigResponse getAutoExpansionConfig(
            @IdPermission @PathVariable @IdMapper String instanceId) {

        AutoResizeConfigResponse resultResponse = autoExpansionService.getAutoExpansionConfig(instanceId);
        return resultResponse;
    }
    /**
     * 修改存储自动扩容配置
     * 开启/关闭/修改存储自动扩容配置
     * 
     * @param instanceId 实例ID
     * @param action 操作类型
     * @param request 请求参数
     */

    @ApiOperation(value = "开启/关闭/修改存储自动扩容配置")
    @RequestMapping(value = "/{instanceId}/diskAutoResize/config/{action}", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateAutoExpansionConfig(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @PathVariable String action,
            @RequestBody UpdateAutoExpansionConfigRequest request){
        autoExpansionService.updateAutoExpansionConfig(instanceId, action, request);

    }

}
