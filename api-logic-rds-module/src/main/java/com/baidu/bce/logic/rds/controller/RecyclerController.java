package com.baidu.bce.logic.rds.controller;


import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.instance.RecoveryInstanceRequest;
import com.baidu.bce.logic.rds.service.RecyclerService;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.model.ListRequest;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.Produces;
import java.util.Arrays;
import java.util.List;

@Api(value = "RDS 回收站")
@Produces("application/json")
@RestController
@RequestMapping("/api/rds/recycler")
public class RecyclerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecyclerController.class);

    @Autowired
    private RecyclerService recyclerService;
    /**
     * 获取回收站实例列表
     * 该函数用于获取回收站中的实例列表。
     * 
     * @param listRequest 请求参数，包括分页信息和查询条件
     * @return EdpPageResultResponse<InstanceAbstract> 返回回收站实例列表的分页结果
     */

    @ApiOperation("获取回收站实例列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public EdpPageResultResponse<InstanceAbstract> getRecyclerInstances(
            @RequestBody ListRequest listRequest) {
        EdpPageResultResponse<InstanceAbstract> resultResponse = new EdpPageResultResponse<>();
        resultResponse = recyclerService.getRecyclerInstances(listRequest);

        return resultResponse;
    }

    /**
     * 从回收站恢复实例
     * 从回收站恢复实例的接口，支持批量恢复，但一次最多恢复3个实例
     * 
     * @param request 恢复实例的请求体，包含要恢复的实例ID列表
     * @return EdpResultResponse<Boolean> 恢复操作的结果，成功为true，失败为false
     * @throws RDSExceptions.ParamValidationException 当实例ID列表为空或null时抛出
     * @throws RDSExceptions.BatchDeleteParamValidationException 当实例ID列表超过3个时抛出
     */
    @ApiOperation("从回收站恢复实例")
    @RequestMapping(value = "/recover", method = RequestMethod.PUT)
    @ResponseBody
    public EdpResultResponse<Boolean> recoverInstance(@RequestBody RecoveryInstanceRequest request) {
        List<String> instanceIds = request.getInstanceIds();
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        if (instanceIds == null || instanceIds.size() == 0) {
           throw new RDSExceptions.ParamValidationException();
        }

        if (instanceIds.size() > 3) {
            throw new RDSExceptions.BatchDeleteParamValidationException();
        }
        recyclerService.recoverInstance(instanceIds);
        resultResponse.setResult(true);

        return resultResponse;
    }

    /**
      * 从回收站中释放单个实例
      * 根据提供的实例ID，从回收站中删除指定的实例
      * 
      * @param instanceId 实例ID，用于标识需要释放的实例
      * @return EdpResultResponse<Boolean> 返回操作结果，成功为true，失败为false
      * @throws RDSExceptions.ParamValidationException 如果instanceId为空或长度为0，抛出参数验证异常
      */
    @ApiOperation("从回收站中释放单个实例")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.DELETE)
    @ResponseBody
    public EdpResultResponse<Boolean> deleteIsolatedInstance(@IdPermission @PathVariable String instanceId) {

        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        if (instanceId == null || instanceId.length() == 0) {
            throw new RDSExceptions.ParamValidationException();
        }
        recyclerService.deleteIsolatedInstance(instanceId);

        resultResponse.setResult(true);
        return resultResponse;
    }

    /**
     * 从回收站中批量释放实例
     * 该接口用于从回收站中批量释放实例，通过传入的实例ID列表进行删除操作。
     * 
     * @param instanceIds 要释放的实例ID列表，多个ID之间用逗号分隔
     * @return EdpResultResponse<Boolean> 返回操作结果，包含操作是否成功
     * @throws RDSExceptions.ParamValidationException 如果传入的instanceIds为空或长度为0，抛出参数验证异常
     * @throws RDSExceptions.BatchDeleteParamValidationException 如果传入的instanceIds数量超过3个，抛出批量删除参数验证异常
     */
    @ApiOperation("从回收站中批量释放实例")
    @RequestMapping(value = "/batch", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @ResponseBody
    public EdpResultResponse<Boolean> deleteBatchInstances(
            @IdPermission @RequestParam(value = "instanceIds") String instanceIds) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        // List<String> instanceIds = request.getInstanceIds();
        if (instanceIds == null || instanceIds.length() == 0) {
            throw new RDSExceptions.ParamValidationException();
        }

        String[] split = instanceIds.split(",");
        List<String> strings = Arrays.asList(split);

        if (split.length > 3) {
            throw new RDSExceptions.BatchDeleteParamValidationException();
        }

        recyclerService.deleteBatchInstances(strings);
        resultResponse.setResult(true);

        return resultResponse;
    }

}
