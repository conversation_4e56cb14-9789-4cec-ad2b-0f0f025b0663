package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.service.QuotaValidatorFactory;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.model.QuotaResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class QuotaServiceController {
    private static final Logger LOGGER = LoggerFactory.getLogger(QuotaServiceController.class);

    @Autowired
    QuotaValidatorFactory quotaValidatorFactory;

    @Autowired
    private RegionConfiguration regionConfiguration;
    /**
     * 配额用量查询
     * 该函数用于查询指定accountId, region, serviceType和quotaName的配额用量。
     *
     * @param accountId 账户ID
     * @param region 区域
     * @param serviceType 服务类型
     * @param quotaName 配额名称
     * @return 返回配额用量信息
     */

    @ApiOperation(value = "配额用量查询")
    @RequestMapping(value = "/{accountId}/{region}/{serviceType}/{quotaName}", method = RequestMethod.GET)
    public QuotaResponse quotaUseCount(@PathVariable String accountId, @PathVariable String region,
                                        @PathVariable String serviceType, @PathVariable String quotaName) {
        LOGGER.debug("accountId:{}, region:{}, serviceType:{} ,quotaName:{}",
                accountId, region, serviceType, quotaName);
        QuotaResponse quotaResponse = new QuotaResponse();
        Integer count = 0;
        try {
            quotaResponse.setQuotaName(quotaName);
            if ("master".equalsIgnoreCase(quotaName)) {
                count = quotaValidatorFactory.getQuotaValidator(ServiceType.RDS)
                        .getActiveInstanceCount(null);
            } else {
                count = quotaValidatorFactory.getQuotaValidator(ServiceType.RDS_REPLICA)
                        .getActiveInstanceCount(null);
            }

            quotaResponse.setUsed(count.toString());
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return quotaResponse;
    }
}
