package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.config.CommonListRequest;
import com.baidu.bce.internalsdk.rds.model.config.ConfigItemModifyHistory;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyHistoryResponse;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyRequest;
import com.baidu.bce.internalsdk.rds.model.config.DashboardConfigListRequest;
import com.baidu.bce.internalsdk.rds.model.config.DashboardConfigModifyHistoryResponse;
import com.baidu.bce.internalsdk.rds.model.config.DashboardConfigModifyRequest;
import com.baidu.bce.logic.rds.service.ArgumentService;
import com.baidu.bce.logic.rds.service.model.argument.ConfigList;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RestController
@RequestMapping(value = "/api/rds/argument")
public class ConsoleApiArgumentController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConsoleApiArgumentController.class);

    @Autowired
    private ArgumentService argumentService;
    /**
      * 列表查询接口
      * 根据请求条件查询配置列表，并设置响应头ETag
      * 
      * @param request 请求参数，包含instanceId, keyword等查询条件
      * @param servletResponse HttpServletResponse对象，用于设置响应头
      * @return EdpResultResponse对象，包含查询结果
      */

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<ConfigList> list(@IdPermission @Valid @RequestBody DashboardConfigListRequest request,
                                            HttpServletResponse servletResponse) {
        ConfigList configList = argumentService.list(request.getInstanceId(), request.getKeyword(), "");
        if (configList.getItems() != null && !configList.getItems().isEmpty()) {
            servletResponse.setHeader("ETag", configList.getItems().get(0).getEtag());
            configList.setETag(configList.getItems().get(0).getEtag());
        }
        EdpResultResponse<ConfigList> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(configList);
        return edpResultResponse;
    }

    /**
      * 修改配置历史记录接口
      * 该接口用于获取指定实例的配置修改历史记录，并以分页形式返回
      *
      * @param request 请求体，包含实例ID、页码、每页数量等信息
      * @return DashboardConfigModifyHistoryResponse 修改历史记录响应体，包含是否成功、分页信息等
      */
    @RequestMapping(value = "history", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public DashboardConfigModifyHistoryResponse modify(@IdPermission @RequestBody CommonListRequest request) {
        ConfigModifyHistoryResponse configModifyHistoryResponse = argumentService.history(request.getInstanceId());
        ConfigItemModifyHistory.ConfigItemModifyHistoryList modifyList =
                configModifyHistoryResponse.getParameters();
        DashboardConfigModifyHistoryResponse response = new DashboardConfigModifyHistoryResponse();
        response.setSuccess(true);
        response.getPage().setPageParams(request.getPageNo(), request.getPageSize(), modifyList);
        return response;
    }
    /**
      * 修改仪表板配置
      * 根据请求中的参数修改仪表板配置
      * 
      * @param request 修改仪表板配置请求体，包含要修改的配置项、生效时间、是否切换等信息
      * @return EdpResultResponse 修改结果
      */

    @RequestMapping(value = "/modify", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse modify(@IdPermission @Valid @RequestBody DashboardConfigModifyRequest request) {
        EdpResultResponse response = new EdpResultResponse();
        ConfigModifyRequest req = new ConfigModifyRequest();
        if (!request.getModified().isEmpty()) {
            req.setParameters(request.getModified());
            req.setEffectiveTime(request.getEffectiveTime());
            req.setSwitchover(request.getSwitchover());
            argumentService.modify(request.getInstanceId(), req,
                    req.getParameters().get(0).getEtag());

        }
        return response;

    }

}
