package com.baidu.bce.logic.rds.controller;


import com.baidu.bce.internalsdk.order.model.ProductPayType;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.rds.model.autorenew.ListAutoRenew;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.model.RdsListRequest;
import com.baidu.bce.logic.rds.service.model.otherservice.AutoRenewDetail;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.wordnik.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by luping03 on 17/6/8.
 */
@RestController
@RequestMapping(value = "/api/rds/autorenew", produces = {"application/json"})
@Api(value = "RDS 自动续费管理API")
public class ConsoleApiAutoRenewController {

    @Autowired
    private OthersService othersService;
    /**
      * 获取自动续费详情列表
      * 根据请求条件，查询符合条件的自动续费详情列表
      * 
      * @param request 请求参数，包含查询条件、分页信息等
      * @return 自动续费详情列表
      */

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    public EdpPageResultResponse<AutoRenewDetail> list(@RequestBody ListAutoRenew request) {
        if ("expireTime".equals(request.getOrderBy())) {
            request.setOrderBy("instanceExpireTime");
        }
        Map<String, String> filterMap = new HashMap<>();
        filterMap.put("productType", ProductPayType.PRE_PAY.toString());
        if (request.getResourceId() != null && !request.getResourceId().equalsIgnoreCase("")) {
            filterMap.put("instanceId", request.getResourceId());
        }
        if (request.getResourceName() != null && !request.getResourceName().equalsIgnoreCase("")) {
            filterMap.put("instanceName", request.getResourceName());
        }
        if (filterMap.get("instanceId") != null) {
            filterMap.put("instanceShortId", filterMap.get("instanceId"));
            filterMap.remove("instanceId");
        }

        RdsListRequest listRequest = new RdsListRequest(request.getDaysToExpiration(), request.getOrder(),
                request.getOrderBy(), request.getPageNo(), request.getPageSize());
        listRequest.setFilterMap(filterMap);
        listRequest.setServiceType(ServiceType.RDS.name());

        LogicPageResultResponse<AutoRenewDetail> logicPageResultResponse =
                othersService.listInstanceForAutoRenew(listRequest);
        if ("instanceExpireTime".equals(request.getOrderBy())) {
            request.setOrderBy("expireTime");
            logicPageResultResponse.setOrderBy("expireTime");
        }

        EdpPageResultResponse.Page<AutoRenewDetail> page = new EdpPageResultResponse.Page<>();
        page.setOrder(logicPageResultResponse.getOrder());
        page.setOrderBy(logicPageResultResponse.getOrderBy());
        page.setPageNo(logicPageResultResponse.getPageNo());
        page.setPageSize(logicPageResultResponse.getPageSize());
        page.setTotalCount(logicPageResultResponse.getTotalCount());
        page.setResult(logicPageResultResponse.getResult());
        EdpPageResultResponse<AutoRenewDetail> edpPageResultResponse = new EdpPageResultResponse<>();
        edpPageResultResponse.setPage(page);
        return edpPageResultResponse;
    }



}
