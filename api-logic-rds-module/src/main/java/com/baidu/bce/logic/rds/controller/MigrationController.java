package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.migration.*;
import com.baidu.bce.logic.rds.service.MigrationService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Created by luping03 on 17/10/14.
 */
@RestController
@RequestMapping("/v1/rds/instances/{instanceId}/migrations")
public class MigrationController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private MigrationService migrationService;
    
    /**
      * 获取迁移记录列表
      * 该接口用于获取迁移记录列表
      *
      * @param instanceId 实例ID
      * @param startTime 开始时间
      * @param endTime 结束时间
      * @param from 来源，默认为空字符串
      * @return 迁移记录列表的响应
      */        
    @ApiOperation(value = "获取迁移记录列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public MigrationsResponse getList(@IdPermission @PathVariable String instanceId,
                                      @RequestParam String startTime,
                                      @RequestParam String endTime,
                                      @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get migration list.");
        MigrationsResponse response = null;
        try {
            response = migrationService.list(instanceId, startTime, endTime);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取迁移地址上的数据库列表
      * 该接口用于获取指定迁移地址上的数据库列表信息。
      * 
      * @param instanceId 实例ID
      * @param remoteIp 远程IP地址
      * @param remotePort 远程端口号
      * @param remoteUser 远程用户名
      * @param remotePassword 远程密码
      * @param from 来源标记（可选参数，默认为空）
      * @return 迁移地址上的数据库列表
      */

    @ApiOperation(value = "获取迁移地址上的数据库列表")
    @RequestMapping(value = "/database", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public MigrationDbList getList(@IdPermission @PathVariable String instanceId,
                                   @RequestParam String remoteIp,
                                   @RequestParam Integer remotePort,
                                   @RequestParam String remoteUser,
                                   @RequestParam String remotePassword,
                                   @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get migration db list.");
        MigrationDbList response = null;
        try {
            DatabaseConfig databaseConfig = new DatabaseConfig(remoteIp, remotePort, remoteUser, remotePassword);
            response = migrationService.dblist(instanceId, databaseConfig);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 预检查接口
     * 用于在迁移前进行预检查操作
     * 
     * @param instanceId 实例ID，用于标识需要操作的RDS实例
     * @param migrationConfig 迁移配置，包含迁移所需的各种参数
     * @param from 来源标识，可选参数，默认为空字符串
     * @return 返回预检查的结果，包含是否可以进行迁移等信息
     */

    @ApiOperation(value = "预检查")
    @RequestMapping(value = "/preCheck", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public MigrationId preCheck(@IdPermission @PathVariable String instanceId,
                                @RequestBody @Valid MigrationConfig migrationConfig,
                                @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("check before migration.");
        MigrationId response = null;
        try {
            response = migrationService.preCheck(instanceId, migrationConfig);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取迁移任务信息
     * 根据实例ID和迁移任务ID获取迁移任务详细信息
     * 
     * @param instanceId 实例ID
     * @param migrationId 迁移任务ID
     * @param from 请求参数，默认为空字符串
     * @return 迁移任务状态信息
     */

    @ApiOperation(value = "迁移任务信息")
    @RequestMapping(value = "/{migrationId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public MigrationStatus detail(@IdPermission @PathVariable String instanceId,
                                  @PathVariable String migrationId,
                                  @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get one migration info.");
        MigrationStatus response = null;
        try {
            response = migrationService.detail(instanceId, migrationId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取正在执行中的任务
      * 通过instanceId获取正在执行中的任务信息，可选参数from用于过滤
      * 
      * @param instanceId 实例ID，用于定位具体任务
      * @param from 可选参数，用于过滤任务
      * @return Migration 返回任务信息
      */

    @ApiOperation(value = "获取正在执行中的任务")
    @RequestMapping(value = "/task", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public Migration task(@IdPermission @PathVariable String instanceId,
                          @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get .");
        Migration response = null;
        try {
            response = migrationService.task(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 创建迁移任务
      * 根据提供的instanceId和migrationId创建迁移任务，可选参数from用于指定来源
      * 
      * @param instanceId 实例ID
      * @param migrationId 迁移任务ID
      * @param from 来源（可选，默认为空字符串）
      */

    @ApiOperation(value = "创建迁移任务")
    @RequestMapping(value = "/{migrationId}", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void createTask(@IdPermission @PathVariable String instanceId,
                           @PathVariable String migrationId,
                           @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get one migration info.");
        try {
            migrationService.create(instanceId, migrationId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
     * 暂停迁移任务
     * 暂停迁移任务，暂停后任务将停止执行
     *
     * @param instanceId 实例ID
     * @param migrationId 迁移任务ID
     * @param from 来源标识，默认为空字符串
     * @throws Exception 抛出异常
     */
    @ApiOperation(value = "暂停迁移任务")
    @RequestMapping(value = "/{migrationId}", method = RequestMethod.PUT, params = {"action=stopReplicate"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void stopTask(@IdPermission @PathVariable String instanceId,
                         @PathVariable String migrationId,
                         @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get one migration info.");
        try {
            migrationService.stop(instanceId, migrationId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    
    /**
      * 取消迁移任务
      * 取消指定迁移任务的执行
      * 
      * @param instanceId 实例ID
      * @param migrationId 迁移任务ID
      * @param from 请求来源，非必需
      */        
    @ApiOperation(value = "取消迁移任务")
    @RequestMapping(value = "/{migrationId}", method = RequestMethod.PUT, params = {"action=cancel"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void cancelTask(@IdPermission @PathVariable String instanceId,
                           @PathVariable String migrationId,
                           @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get one migration info.");
        try {
            migrationService.cancel(instanceId, migrationId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

}
