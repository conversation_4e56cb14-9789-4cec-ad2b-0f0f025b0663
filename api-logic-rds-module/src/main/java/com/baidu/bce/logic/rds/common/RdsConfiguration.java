package com.baidu.bce.logic.rds.common;

import com.baidu.bce.logic.rds.model.config.PhpAdminUrlConfig;
import com.baidu.bce.logic.rds.model.config.PhpAdminUrlConfigs;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

import java.util.List;

@Configuration
public class RdsConfiguration implements InitializingBean {

    @Value("${login.cookie.md5.key}")
    private String authCookieSecret;

    @Value("${phpadmin.configuration.path:classpath:phpadmin.conf}")
    private String phpadminConfigPath;

    private List<PhpAdminUrlConfig> phpadminConfigs;

    @Autowired
    ApplicationContext applicationContext;

    public String getAuthCookieSecret() {
        return authCookieSecret;
    }

    public PhpAdminUrlConfig getPhpAdminUrlConfig(String region) {
        for (PhpAdminUrlConfig config : phpadminConfigs) {
            if (config.getRegion().equalsIgnoreCase(region)) {
                return config;
            }
        }
        throw new RDSBusinessExceptions.RegionNotExistException();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Resource resource = applicationContext.getResource(phpadminConfigPath);
        phpadminConfigs = new ObjectMapper().readValue(
                resource.getInputStream(), PhpAdminUrlConfigs.class).getPhpadminConfigs();
    }
}

