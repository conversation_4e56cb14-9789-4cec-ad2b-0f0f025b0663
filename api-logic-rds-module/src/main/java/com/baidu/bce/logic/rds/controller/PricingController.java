package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.logic.rds.service.PricingService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.pricing.Price;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Created by luping03 on 17/11/7.
 */
@RestController
@RequestMapping("/v1/rds/price")
public class PricingController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PricingController.class);

    @Autowired
    private PricingService pricingService;
    
    /**
     * 获取价格
     * 根据提供的实例创建模型获取价格信息
     * 
     * @param request 实例创建模型，包含获取价格所需的参数
     * @param from 请求来源，默认为空字符串
     * @return 价格信息
     */
    @ApiOperation(value = "获取价格")
    @RequestMapping(value = "", method = RequestMethod.POST)
    public Price getPrice(@RequestBody @Valid InstanceCreateModel request,
                          @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get price.");
        Price price = null;
        try {
            price = pricingService.getPrice(request, null);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return price;
    }
    /**
      * 获取预付费的变配差价
      * 根据传入的变配请求计算预付费的变配差价
      *
      * @param request 变配请求信息
      * @param from 请求来源（可选）
      * @return 计算出的变配差价
      */

    @ApiOperation(value = "获取预付费的变配差价")
    @RequestMapping(value = "/diff", method = RequestMethod.POST)
    public Price getPriceDiff(@RequestBody @Valid PriceDiffModel request,
                              @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get price.");
        Price price = null;
        try {
            price = pricingService.getPriceDiff(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return price;
    }

}
