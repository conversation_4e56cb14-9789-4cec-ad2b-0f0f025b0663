package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.console.settings.service.UserSettingsService;
import com.baidu.bce.internalsdk.rds.model.resource.BindResourceRequest;
import com.baidu.bce.internalsdk.rds.model.resource.GroupResWithTagRequest;
import com.baidu.bce.internalsdk.rds.model.resource.ResourceGroupRequest;
import com.baidu.bce.logic.rds.service.resource.ResourceGroupService;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.resource.manager.sdk.model.BindResResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupTreeResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.HistoryGroupResRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.HistoryGroupResResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupDetailRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupDetailResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupListResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceGroupsPageInfo;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Created by luping03 on 17/10/10.
 */
@RestController
@RequestMapping("/api/rds/resource/group")
public class ConsoleApiResourceGroupController {

    @Autowired
    private ResourceGroupService resourceGroupService;
    /**
     * 获取资源组
     * 通过POST请求，根据传入参数获取资源组信息
     * 
     * @param request 资源组请求参数，包含资源组名称等
     * @return EdpResultResponse<GroupTreeResponse> 返回资源组信息
     */

    @ApiOperation(value = "获取资源组")
    @RequestMapping(value = "/getGroup", method = RequestMethod.POST)
    public EdpResultResponse<GroupTreeResponse> getGroup(@RequestBody(required = false) ResourceGroupRequest request) {
        EdpResultResponse<GroupTreeResponse> response = new EdpResultResponse<>();
        String name = null;
        if (request != null) {
            name = request.getName();
        }
        response.setResult(resourceGroupService.getGroup(name, ""));
        return response;
    }
    /**
     * 创建资源组
     * 该函数用于创建资源组，接收包含资源组信息的请求体，并返回创建结果。
     * 
     * @param request 包含创建资源组所需信息的请求体
     * @return 创建资源组的结果
     */

    @ApiOperation(value = "创建资源组")
    @RequestMapping(value = "/createGroup", method = RequestMethod.POST)
    public EdpResultResponse<String> createGroup(@Valid @RequestBody GroupRequest request) {
        EdpResultResponse<String> response = new EdpResultResponse<>();
        response.setResult(resourceGroupService.createGroup(request, ""));
        return response;
    }

    /**
      * 修改资源组
      * 修改指定资源组的信息
      *
      * @param request 资源组请求信息，包括资源组ID、名称、父ID等
      * @return 操作结果
      */
    @ApiOperation(value = "修改资源组")
    @RequestMapping(value = "/updateGroup", method = RequestMethod.POST)
    public EdpResultResponse<String> createGroup(@Valid @RequestBody ResourceGroupRequest request) {
        EdpResultResponse<String> response = new EdpResultResponse<>();
        GroupRequest groupRequest = new GroupRequest();
        groupRequest.setGroupId(request.getGroupId());
        groupRequest.setExtra(request.getExtra());
        groupRequest.setName(request.getName());
        groupRequest.setParentId(request.getParentId());
        response.setResult(resourceGroupService.updateGroup(groupRequest,  request.getForce(), ""));
        return response;
    }
    /**
     * 删除资源组
     * 根据请求中的资源组ID删除指定的资源组
     * 
     * @param request 包含资源组ID的请求体
     * @return EdpResultResponse<Boolean> 删除操作的结果
     */

    @ApiOperation(value = "删除资源组")
    @RequestMapping(value = "/deleteGroup", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> deleteGroup(@Valid @RequestBody ResourceGroupRequest request) {
        resourceGroupService.deleteGroup(request.getGroupId());
        return new EdpResultResponse<>();
    }
    /**
     * 绑定资源
     * 用于绑定资源的接口，接收绑定资源请求，并调用service层处理
     * 
     * @param request 绑定资源请求参数
     * @return 处理结果，包含绑定响应
     */

    @ApiOperation(value = "绑定资源")
    @RequestMapping(value = "/bindResource", method = RequestMethod.POST)
    public EdpResultResponse<BindResResponse> bindResource(@Valid @RequestBody BindResourceRequest request) {
        EdpResultResponse<BindResResponse> response = new EdpResultResponse<>();
        resourceGroupService.changeBindResource(request, request.getForce(), RDSConstant.RESOURCE_BIND, "");
        response.setResult(new BindResResponse());
        return response;
    }
    /**
     * 解绑资源
     * 解绑资源接口，通过POST请求进行资源解绑操作
     * 
     * @param request 解绑资源请求参数，包含解绑所需信息
     * @return EdpResultResponse<Boolean> 返回解绑结果
     */

    @ApiOperation(value = "解绑资源")
    @RequestMapping(value = "/unbindResource", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> unbindResource(@Valid @RequestBody BindResourceRequest request) {
        resourceGroupService.changeBindResource(request, request.getForce(), RDSConstant.RESOURCE_UNBIND, "");
        return new EdpResultResponse<>();
    }
    /**
      * 分组关联的资源查询
      * 根据分组ID和请求参数，查询关联的资源信息
      * 
      * @param request 分组关联的资源查询请求参数
      * @return 分组关联的资源查询结果
      */

    @ApiOperation(value = "分组关联的资源查询")
    @RequestMapping(value = "/getGroupResWithTag", method = RequestMethod.POST)
    public EdpResultResponse<ResourceGroupsPageInfo> getGroupResWithTag(
            @Valid @RequestBody GroupResWithTagRequest request) {
        EdpResultResponse<ResourceGroupsPageInfo> response = new EdpResultResponse<>();
        response.setResult(resourceGroupService.getGroupResWithTag(request.getGroupId(), request.getId(),
                request.getTypes(), UserSettingsService.getRegion(),
                request.getName(), request.getIsCurrent(), request.getTags(), request.getPageNo(),
                request.getPageSize(), ""));
        return response;
    }
    /**
     * 获取资源列表
     * 通过指定的条件获取资源列表信息
     * 
     * @param request 包含获取资源列表所需的条件信息，如ID、类型、名称、标签、页码、页面大小等
     * @return EdpResultResponse<ResourceGroupsPageInfo> 返回资源列表的响应对象，包含资源列表信息和请求结果状态
     */

    @ApiOperation(value = "资源列表")
    @RequestMapping(value = "/getResourcePageWithTag", method = RequestMethod.POST)
    public EdpResultResponse<ResourceGroupsPageInfo> getResourcePageWithTag(
            @Valid @RequestBody GroupResWithTagRequest request) {
        EdpResultResponse<ResourceGroupsPageInfo> response = new EdpResultResponse<>();
        response.setResult(resourceGroupService.getResourcePageWithTag(request.getId(), request.getTypes(),
                UserSettingsService.getRegion(), request.getName(),
                request.getTags(), request.getPageNo(), request.getPageSize(), ""));
        return response;
    }
    /**
     * 资源列表不分页接口
     * 用于获取资源列表（不分页），可通过条件进行筛选
     * 
     * @param request 包含请求参数的对象，包括ID、类型、区域、名称和标签
     * @return EdpResultResponse<ResourceGroupsPageInfo> 返回资源列表的结果对象
     */

    @ApiOperation(value = "资源列表不分页")
    @RequestMapping(value = "/getResNoPageWithTag", method = RequestMethod.POST)
    public EdpResultResponse<ResourceGroupsPageInfo> getResNoPageWithTag(@RequestBody GroupResWithTagRequest request) {
        EdpResultResponse<ResourceGroupsPageInfo> response = new EdpResultResponse<>();
        if (request == null) {
            request = new GroupResWithTagRequest();
        }
        response.setResult(resourceGroupService.getResNoPageWithTag(request.getId(), request.getTypes(),
                UserSettingsService.getRegion(), request.getName(),
                request.getTags(), ""));
        return response;
    }
    /**
     * 资源列表（不分页，不与tag联动）
     * 该接口用于获取资源列表，返回的资源列表不分页，也不与tag联动。
     * 
     * @param request 包含请求参数的对象，包括资源ID、类型、区域、名称等
     * @return EdpResultResponse<ResourceGroupsPageInfo> 返回资源列表的响应对象
     */

    @ApiOperation(value = "资源列表（不分页，不与tag联动）")
    @RequestMapping(value = "/getResNoPageWithoutTag", method = RequestMethod.POST)
    public EdpResultResponse<ResourceGroupsPageInfo> getResNoPageWithoutTag(
            @RequestBody GroupResWithTagRequest request) {
        EdpResultResponse<ResourceGroupsPageInfo> response = new EdpResultResponse<>();
        if (request == null) {
            request = new GroupResWithTagRequest();
        }
        response.setResult(resourceGroupService.getResNoPageWithoutTag(request.getId(), request.getTypes(),
                UserSettingsService.getRegion(), request.getName(), ""));
        return response;
    }
    /**
     * 资源列表（分页，不与tag联动）
     * 分页获取资源列表，不与tag联动
     * 
     * @param request 分页获取资源列表的请求体，包含id、类型、区域、名称、页码、每页数量等
     * @return 分页获取资源列表的响应体
     */

    @ApiOperation(value = "资源列表（分页，不与tag联动）")
    @RequestMapping(value = "/getResPageWithoutTag", method = RequestMethod.POST)
    public EdpResultResponse<ResourceGroupsPageInfo> getResourcePageWithoutTag(
            @RequestBody GroupResWithTagRequest request) {
        EdpResultResponse<ResourceGroupsPageInfo> response = new EdpResultResponse<>();
        if (request == null) {
            request = new GroupResWithTagRequest();
        }
        response.setResult(resourceGroupService.getResourcePageWithoutTag(request.getId(), request.getTypes(),
                UserSettingsService.getRegion(), request.getName(), request.getPageNo(),
                request.getPageSize(), ""));
        return response;
    }

    /**
     * 批量查询资源所属分组
     * 用于批量查询资源所属的分组信息
     * 
     * @param request 请求参数，包含需要查询的资源信息
     * @return 返回查询结果，包含资源所属分组信息
     */
    @ApiOperation(value = "批量查询资源所属分组")
    @RequestMapping(value = "/getResGroupBatch", method = RequestMethod.POST)
    public EdpResultResponse<ResGroupDetailResponse> getResGroupBatch(
            @Valid @RequestBody ResGroupDetailRequest request) {
        EdpResultResponse<ResGroupDetailResponse> response = new EdpResultResponse<>();
        response.setResult(resourceGroupService.getResGroupBatch(request, ""));
        return response;
    }
    /**
      * 批量查询资源所属的历史分组
      * 该函数用于批量查询资源所属的历史分组信息
      * 
      * @param request 资源分组详情请求对象
      * @return 返回查询结果，包含历史分组信息
      */

    @ApiOperation(value = "批量查询资源所属的历史分组")
    @RequestMapping(value = "/getHistoryResGroupBatchByRes", method = RequestMethod.POST)
    public EdpResultResponse<HistoryGroupResResponse> getHistoryResGroupBatchByRes(
            @Valid @RequestBody ResGroupDetailRequest request) {
        EdpResultResponse<HistoryGroupResResponse> response = new EdpResultResponse<>();
        response.setResult(resourceGroupService.getHistoryResGroupBatchByRes(request, ""));
        return response;
    }
    /**
      * 分组历史关联资源查询
      * 根据请求参数查询分组历史关联资源
      * 
      * @param request 分组历史关联资源查询请求参数
      * @return 分组历史关联资源查询结果
      */

    @ApiOperation(value = "分组历史关联资源查询")
    @RequestMapping(value = "/getHistoryResGroupBatchByGroup", method = RequestMethod.POST)
    public EdpResultResponse<HistoryGroupResResponse> getHistoryResGroupBatchByRes(
            @Valid @RequestBody HistoryGroupResRequest request) {
        EdpResultResponse<HistoryGroupResResponse> response = new EdpResultResponse<>();
        response.setResult(resourceGroupService.getHistoryResGroupBatchByGroup(request, ""));
        return response;
    }
    /**
     * 批量查询资源所属分组链路
     * 该函数用于批量查询资源所属的分组链路
     * 
     * @param request 资源分组详情请求参数
     * @return EdpResultResponse<ResGroupListResponse> 查询结果
     */

    @ApiOperation(value = "批量查询资源所属分组链路")
    @RequestMapping(value = "/getGroupResList", method = RequestMethod.POST)
    public EdpResultResponse<ResGroupListResponse> getGroupResList(@Valid @RequestBody ResGroupDetailRequest request) {
        EdpResultResponse<ResGroupListResponse> response = new EdpResultResponse<>();
        response.setResult(resourceGroupService.getGroupResList(request, ""));
        return response;
    }
}
