package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.snapshot.BackupCrossRegionListResponses;
import com.baidu.bce.internalsdk.rds.model.snapshot.BackupTargetRegionResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.CopyRegionSnapshotDetail;
import com.baidu.bce.internalsdk.rds.model.snapshot.OpenApiBackup;
import com.baidu.bce.internalsdk.rds.model.snapshot.OpenApiSnapshotsResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.Snapshot;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotGetResponse;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.rds.service.BackupService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;

/**
 * Created by luping03 on 17/10/14.
 */
@RestController
@RequestMapping("/v1/instance/{instanceId}/backup")
public class OpenApiSnapshotController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private BackupService backupService;

    /**
     * 获取备份列表: 通过marker方式分页
     * 获取备份列表: 通过marker方式分页获取备份列表
     *
     * @param instanceId 实例ID
     * @param marker 分页标识，如果不传或者传入-1表示第一页
     * @param maxKeys 每页的最大记录数
     * @return OpenApiSnapshotsResponse 备份列表分页响应结果
     */
    @RequestMapping(method = RequestMethod.GET)
    @ApiOperation(value = "获取备份列表: 通过marker方式分页")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenApiSnapshotsResponse listInstanceByMarker(
            @IdPermission @IdMapper @PathVariable String instanceId,
            @RequestParam(required = false, defaultValue = "-1") String marker,
            @RequestParam(required = false, defaultValue = "1000") Integer maxKeys) {
        LOGGER.debug("list instance[marker], marker:{}, maxKeys:{}.", marker, maxKeys);
        LogicMarkerResultResponse<Snapshot> pageResultResponse = null;
        OpenApiSnapshotsResponse openApiSnapshotsResponse = null;
        try {
            pageResultResponse = backupService.listWithMarker(instanceId, marker, maxKeys);

            // API 会多返回一个转换为MB的备份大小字段
            for (Snapshot eachSnapshots : pageResultResponse.getResult()) {
                // 转换大小从Byte为MB
                String snapshotSizeInBytesStr = eachSnapshots.getSnapshotSizeInBytes();

                eachSnapshots.setSnapshotSizeInMBytes(
                        String.valueOf(BasisUtils.transforByte2MByte(snapshotSizeInBytesStr)));
            }

            openApiSnapshotsResponse = new OpenApiSnapshotsResponse();
            openApiSnapshotsResponse.setMarker(pageResultResponse.getMarker());
            openApiSnapshotsResponse.setIsTruncated(pageResultResponse.getIsTruncated());
            openApiSnapshotsResponse.setNextMarker(pageResultResponse.getNextMarker());
            openApiSnapshotsResponse.setMaxKeys(pageResultResponse.getMaxKeys());
            if (pageResultResponse.getResult() != null) {
                openApiSnapshotsResponse.setBackups(
                        new ArrayList<OpenApiBackup>(pageResultResponse.getResult().size()));
                for (Snapshot snapshot : pageResultResponse.getResult()) {
                    OpenApiBackup openApiBackup = new OpenApiBackup();
                    openApiBackup.setBackupId(snapshot.getSnapshotId());
                    openApiBackup.setBackupSize(snapshot.getSnapshotSizeInMBytes() == null ? null :
                            Long.parseLong(snapshot.getSnapshotSizeInMBytes()));
                    openApiBackup.setBackupType(snapshot.getSnapshotType());
                    openApiBackup.setDataBackupType(snapshot.getDataBackupType());
                    openApiBackup.setBackupStatus(snapshot.getSnapshotStatus());
                    openApiBackup.setBackupStartTime(snapshot.getSnapshotStartTime());
                    openApiBackup.setBackupEndTime(snapshot.getSnapshotEndTime());

                    openApiSnapshotsResponse.getBackups().add(openApiBackup);
                }
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
//        LOGGER.debug("list instance[marker], response data is {}", pageResultResponse);
        return openApiSnapshotsResponse;
    }
    
    /**
      * 获取备份详情
      * 根据实例ID和备份ID获取备份的详细信息
      * 
      * @param instanceId 实例ID
      * @param snapshotId 备份ID
      * @param downloadValidTimeInSec 下载有效期时间，单位为秒，默认值为43200秒
      * @return 返回OpenApiBackup对象，包含备份的详细信息
      * @throws Exception 如果在获取备份详情的过程中发生异常，抛出Exception
      */
    @ApiOperation(value = "获取备份详情")
    @RequestMapping(value = "/{snapshotId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenApiBackup getDetail(@IdPermission @IdMapper @PathVariable String instanceId,
                                         @PathVariable String snapshotId,
                                         @RequestParam(required = false, defaultValue = "43200")
                                                 Integer downloadValidTimeInSec) {
        String from = "api";
        LOGGER.debug("get backup detail.");
        OpenApiBackup openApiBackup = null;
        try {
            SnapshotGetResponse response = backupService.detail(instanceId, snapshotId,
                    Integer.valueOf(downloadValidTimeInSec));

            // API 会多返回一个转换为MB的备份大小字段
            if (response != null && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
                // 转换大小从Byte为MB
                String snapshotSizeInBytesStr = response.getSnapshot().getSnapshotSizeInBytes();

                response.getSnapshot().setSnapshotSizeInMBytes(
                        String.valueOf(BasisUtils.transforByte2MByte(snapshotSizeInBytesStr)));
            }

            if (response != null && response.getSnapshot() != null) {
                openApiBackup = new OpenApiBackup();

                Snapshot snapshot = response.getSnapshot();
                openApiBackup.setBackupId(snapshot.getSnapshotId());
                openApiBackup.setBackupSize(snapshot.getSnapshotSizeInMBytes() == null ? null :
                        Long.parseLong(snapshot.getSnapshotSizeInMBytes()));
                openApiBackup.setBackupType(snapshot.getSnapshotType());
                openApiBackup.setBackupStatus(snapshot.getSnapshotStatus());
                openApiBackup.setBackupStartTime(snapshot.getSnapshotStartTime());
                openApiBackup.setBackupEndTime(snapshot.getSnapshotEndTime());
                openApiBackup.setDownloadUrl(snapshot.getDownloadUrl());
                openApiBackup.setDownloadExpires(snapshot.getDownloadExpires());
                openApiBackup.setMultiDownloadUrl(snapshot.getMultiDownloadUrl());
            }
            return openApiBackup;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return openApiBackup;
    }
    /**
     * 删除指定的备份集
     * 删除指定的备份集信息
     * 
     * @param instanceId 实例ID
     * @param snapshotId 备份集ID
     */

    @ApiOperation(value = "删除指定的备份集")
    @RequestMapping(value = "/{snapshotId}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void snapshotDelete(@IdPermission @PathVariable String instanceId,
                               @PathVariable String snapshotId) {
        LOGGER.debug("instanceId: {}, snapshotId: {}", instanceId, snapshotId);
        try {
            backupService.snapshotDelete(instanceId, snapshotId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 创建备份
     * 创建备份的功能实现，接收备份请求参数，处理备份请求。
     *
     * @param request 备份请求参数，包含instanceId等信息
     * @param instanceId 实例ID，用于标识需要备份的实例
     * @param from 备份来源，默认为空字符串
     */

    @ApiOperation(value = "创建备份")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void snapshotCreate(@RequestBody(required = false) InstanceIdRequest request,
                               @IdPermission @IdMapper @PathVariable String instanceId,
                               @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get snapshot create.");
        if (request == null) {
            request = new InstanceIdRequest();
        }
        // 此处，为了保持使用方式不变以及兼容 service 层使用方式
        request.setInstanceId(instanceId);
        try {
            backupService.create(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 跨可用区备份列表
      * 获取跨可用区备份列表
      * 
      * @param instanceId 实例ID
      * @return 跨可用区备份列表响应对象
      */

    @io.swagger.annotations.ApiOperation("跨可用区备份列表（跨可用区备份列表）")
    @RequestMapping(value = "/crossRegion/list", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public BackupCrossRegionListResponses getCrossRegionList
            (@IdPermission @PathVariable @IdMapper String instanceId) {
        BackupCrossRegionListResponses resultResponse = new BackupCrossRegionListResponses();
        return backupService.getCrossRegionList(instanceId, null, null,
                null, null, null);

    }
    /**
     * 数据备份副本详情（跨可用区备份详情）
     * 根据实例ID和快照ID获取跨可用区备份详情
     * 
     * @param instanceId 实例ID
     * @param snapshotId 快照ID
     * @return 跨可用区备份详情
     */

    @io.swagger.annotations.ApiOperation("数据备份副本详情（跨可用区备份详情）")
    @RequestMapping(value = "/crossRegion/{snapshotId}", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public CopyRegionSnapshotDetail getCrossRegionDetail
            (@IdPermission @PathVariable @IdMapper String instanceId,
             @PathVariable String snapshotId) {
        CopyRegionSnapshotDetail resultResponse = new CopyRegionSnapshotDetail();
        return backupService.getCrossRegionDetail(instanceId, snapshotId);

    }
    /**
      * 数据备份副本目标地域映射（目标地域列表）
      * 获取RDS数据备份副本目标地域列表
      * 
      * @param instanceId 实例ID
      * @return 备份目标地域列表
      */

    @io.swagger.annotations.ApiOperation("数据备份副本目标地域映射（目标地域列表）")
    @RequestMapping(value = "/target/region", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public BackupTargetRegionResponse getTargetRegionList
            (@IdPermission @PathVariable @IdMapper String instanceId) {
        BackupTargetRegionResponse resultResponse = new BackupTargetRegionResponse();
        return backupService.getTargetRegionList(instanceId);

    }

}
