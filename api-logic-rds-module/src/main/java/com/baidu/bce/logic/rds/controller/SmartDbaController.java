package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdMapper;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.smartdba.DiskInfoResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSlowSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlExplainListResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlStatsDurationList;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTableList;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTemplateResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTrend;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaInstanceResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaPageRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaPageResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaResultResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaTopoResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SqlIdRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SqlIdResponse;
import com.baidu.bce.logic.rds.service.SmartDbaService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


@RestController
@RequestMapping(value = "/v1/rds/smartDba", produces = {"application/json"})
public class SmartDbaController {

    @Autowired
    private SmartDbaService smartDbaService;
    /**
      * 获取实例磁盘信息
      * 根据实例ID获取实例磁盘信息
      *
      * @param instanceId 实例ID
      * @return 实例磁盘信息
      */

    @RequestMapping(value = "/disk/list/{instanceId}", method = RequestMethod.POST)
    public DiskInfoResponse getInstanceDisk(@PathVariable("instanceId") String instanceId) {
        DiskInfoResponse diskInfoResponse = null;
        try {
            diskInfoResponse = smartDbaService.instanceDisk(instanceId);

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return diskInfoResponse;
    }

    /**
      * 获取实例接口
      * 根据instanceId获取对应的实例信息
      * 
      * @param instanceId 实例ID
      * @return SmartDbaInstanceResponse 实例信息
      */
    @RequestMapping(value = "/instance/{instanceId}", method = RequestMethod.POST)
    public SmartDbaInstanceResponse getInstance(@PathVariable("instanceId") String instanceId) {
        SmartDbaInstanceResponse smartDbaInstanceResponse = null;
        try {
            smartDbaInstanceResponse = smartDbaService.instance(instanceId);

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }

        return smartDbaInstanceResponse;
    }

    /**
      * 获取数据库列表
      * 根据实例ID和列表编号获取数据库列表信息
      *
      * @param instanceId 实例ID
      * @param listNo 列表编号，可以为空
      * @return 数据库列表信息
      * @throws 无
      */
    @RequestMapping(value = "/db/list/{instanceId}", method = RequestMethod.POST)
    public SmartDbaResultResponse getDbList(@PathVariable("instanceId") String instanceId,
                                            @RequestParam(value = "listNo", required = false) Integer listNo) {
        SmartDbaResultResponse smartDbaDbResponse = null;
        try {
            smartDbaDbResponse = smartDbaService.getDbList(instanceId, listNo);

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }

        return smartDbaDbResponse;
    }
    /**
      * 获取SmartDba分页数据
      * 根据instanceId和smartDbaPageRequest请求参数获取分页数据
      *
      * @param instanceId 实例ID
      * @param smartDbaPageRequest 分页请求参数
      * @return SmartDbaPageResponse 分页响应数据
      * @throws 无
      */

    @RequestMapping(value = "/tb/list/{instanceId}", method = RequestMethod.POST)
    public SmartDbaPageResponse getSmartDbaPage(@PathVariable("instanceId") String instanceId,
                                                @RequestBody SmartDbaPageRequest smartDbaPageRequest) {
        SmartDbaPageResponse smartDbaPageResponse = null;
        try {
            if (smartDbaPageRequest == null) {
                smartDbaPageRequest = new SmartDbaPageRequest();
            }
            smartDbaPageResponse = smartDbaService
                    .getSmartDbaPage(instanceId, smartDbaPageRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }

        return smartDbaPageResponse;
    }
    /**
      * 获取连接列表
      * 根据实例ID获取数据库连接列表
      * 
      * @param instanceId 实例ID
      * @return 数据库连接列表
      */

    @RequestMapping(value = "/connList/{instanceId}", method = RequestMethod.POST)
    public SmartDbaInstanceResponse getConnList(@PathVariable("instanceId") String instanceId) {
        SmartDbaInstanceResponse smartDbaDbResponse = null;
        try {
            smartDbaDbResponse = smartDbaService.getConnList(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }

        return smartDbaDbResponse;
    }
    /**
     * 获取拓扑列表
     * 根据实例ID获取数据库拓扑列表
     * 
     * @param instanceId 实例ID
     * @return 拓扑列表响应对象
     */

    @RequestMapping(value = "/topo/list/{instanceId}", method = RequestMethod.POST)
    public SmartDbaTopoResponse getTopoList(@PathVariable("instanceId") String instanceId) {
        SmartDbaTopoResponse smartDbaTopoResponse = null;
        try {
            smartDbaTopoResponse = smartDbaService.getTopoList(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return smartDbaTopoResponse;

    }
    /**
      * 获取数据库连接状态
      * 根据实例ID获取数据库连接状态
      * 
      * @param instanceId 实例ID
      * @return 数据库连接状态响应对象
      */

    @RequestMapping(value = "/conn/stati/{instanceId}", method = RequestMethod.POST)
    public SmartDbaInstanceResponse getConnStati(@PathVariable("instanceId") String instanceId) {
        SmartDbaInstanceResponse smartDbaDbResponse = null;
        try {
            smartDbaDbResponse = smartDbaService.getConnStati(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }

        return smartDbaDbResponse;
    }
    /**
      * 查询慢查询诊断开通状态
      * 根据instanceId查询慢查询诊断的开通状态
      * 
      * @param instanceId 实例ID
      * @return 慢查询诊断开通状态结果
      */

    @ApiOperation(value = "查询慢查询诊断开通状态")
    @RequestMapping(value = "/slowsqlflow/{instanceId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSlowSqlflowResponse getSqlflow(@IdPermission @PathVariable @IdMapper String instanceId) {
        GetSlowSqlflowResponse getSlowSqlflowResponse = null;
        try {
            getSlowSqlflowResponse = smartDbaService.getSlowSqlflow(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return getSlowSqlflowResponse;
    }
    /**
      * 开通慢查询诊断
      * 该函数用于开通RDS实例的慢查询诊断功能
      * 
      * @param instanceId RDS实例ID
      * @return GetSlowSqlflowResponse 返回开通慢查询诊断的结果
      */

    @ApiOperation(value = "开通慢查询诊断")
    @RequestMapping(value = "/slowsqlflow/{instanceId}", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSlowSqlflowResponse postSqlflow(@IdPermission @PathVariable @IdMapper String instanceId) {
        GetSlowSqlflowResponse getSlowSqlflowResponse = null;
        try {
            getSlowSqlflowResponse = smartDbaService.putSlowSqlflow(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return getSlowSqlflowResponse;
    }
    /**
      * 关闭慢查询诊断
      * 根据instanceId删除慢查询诊断
      *
      * @param instanceId 实例ID
      * @return 删除操作的结果
      * @throws 无
      */

    @ApiOperation(value = "关闭慢查询诊断")
    @RequestMapping(value = "/slowsqlflow/{instanceId}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSlowSqlflowResponse deleteSqlflow(@IdPermission @PathVariable @IdMapper String instanceId) {
        GetSlowSqlflowResponse getSlowSqlflowResponse = null;
        try {
            getSlowSqlflowResponse = smartDbaService.deleteSlowSqlflow(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return getSlowSqlflowResponse;
    }
    /**
      * 根据sqlId获取慢SQL
      * 通过POST请求和指定的instanceId以及SqlIdRequest对象来获取慢SQL的详细信息
      *
      * @param instanceId 实例ID，用于标识具体的数据库实例
      * @param request SqlIdRequest对象，包含请求慢SQL所需的参数
      * @return SqlIdResponse对象，包含慢SQL的详细信息
      */

    @ApiOperation(value = "根据sqlId获取慢SQL")
    @RequestMapping(value = "{instanceId}/slowsqlid", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlIdResponse getSlowSqlBySqlId(@PathVariable("instanceId") String instanceId,
                                           @Valid @RequestBody SqlIdRequest request) {
        SqlIdResponse sqlIdResponse = null;
        try {
            sqlIdResponse = smartDbaService.getSlowSqlBySqlId(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return sqlIdResponse;
    }
    /**
      * 获取慢SQL列表
      * 该函数用于获取指定实例的慢SQL列表。
      * 
      * @param instanceId 实例ID，用于指定需要查询的数据库实例
      * @param request 查询请求体，包含查询慢SQL所需的参数
      * @return 返回一个包含慢SQL列表的响应对象
      * @throws 无
      */

    @ApiOperation(value = "获取慢SQL列表")
    @RequestMapping(value = "/{instanceId}/slowsql/list", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlListResponse getSlowSqlList(@PathVariable("instanceId") String instanceId,
                                           @Valid @RequestBody SlowsqlListRequest request) {
        SlowsqlListResponse response = null;
        try {
            response = smartDbaService.getSlowSqlList(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取慢SQL说明
     * 根据instanceId和请求参数，获取慢SQL的详细说明
     * 
     * @param instanceId 实例ID
     * @param request 请求参数，包含慢SQL查询的相关信息
     * @return 慢SQL说明的响应结果
     */

    @ApiOperation(value = "获取慢SQL说明")
    @RequestMapping(value = "/{instanceId}/slowsql/explain", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlExplainListResponse getSlowSqlExplain(@PathVariable("instanceId") String instanceId,
                                              @Valid @RequestBody SlowsqlListRequest request) {
        SlowsqlExplainListResponse response = null;
        try {
            response = smartDbaService.getSlowSqlExplain(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取慢SQL模板的统计信息
     * 获取指定实例的慢SQL模板的统计信息
     *
     * @param instanceId 实例ID
     * @param request 慢SQL列表请求参数
     * @return 慢SQL模板的响应信息
     */

    @ApiOperation(value = "Get the statistics information of SQL template dimension.")
    @RequestMapping(value = "/{instanceId}/slowsql/stats/digest", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTemplateResponse getSlowSqlTemplate(@PathVariable("instanceId") String instanceId,
                                                    @Valid @RequestBody SlowsqlListRequest request) {
        SlowsqlTemplateResponse response = null;
        try {
            response = smartDbaService.getSlowSqlTemplate(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取慢SQL运行时长分布
      * 根据实例ID和请求参数，获取慢SQL运行时长分布的数据
      * 
      * @param instanceId 实例ID
      * @param request 请求参数，包含获取慢SQL运行时长分布所需的信息
      * @return 慢SQL运行时长分布的数据
      */

    @ApiOperation(value = "Get slow sql run time duration distribution.")
    @RequestMapping(value = "/{instanceId}/slowsql/stats/duration", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlStatsDurationList getSlowSqlStatsDuration(@PathVariable("instanceId") String instanceId,
                                                      @Valid @RequestBody SlowsqlListRequest request) {
        SlowsqlStatsDurationList response = null;
        try {
            response = smartDbaService.getSlowSqlStatsDuration(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取慢SQL源客户端分布
     * 根据实例ID和请求参数获取慢SQL源客户端的分布情况
     * 
     * @param instanceId 实例ID
     * @param request 请求参数，包含查询条件
     * @return 慢SQL源客户端分布列表
     */

    @ApiOperation(value = "Get slow sql source client distribution.")
    @RequestMapping(value = "/{instanceId}/slowsql/stats/source", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlStatsDurationList getSlowSqlStatsSource(@PathVariable("instanceId") String instanceId,
                                                        @Valid @RequestBody SlowsqlListRequest request) {
        SlowsqlStatsDurationList response = null;
        try {
            response = smartDbaService.getSlowSqlStatsSource(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取慢查询SQL的表
     * 获取指定实例下的慢查询SQL的表信息
     * 
     * @param instanceId 实例ID
     * @param request 慢查询SQL列表请求参数
     * @return 慢查询SQL的表信息列表
     */

    @ApiOperation(value = "Get tables in slow sql.")
    @RequestMapping(value = "/{instanceId}/slowsql/table", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTableList getSlowSqlTable(@PathVariable("instanceId") String instanceId,
                                                      @Valid @RequestBody SlowsqlListRequest request) {
        SlowsqlTableList response = null;
        try {
            response = smartDbaService.getSlowSqlTable(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取慢SQL表列
     * Get table columns in slow sql.
     * 
     * @param instanceId 实例ID
     * @param request 慢SQL列表请求参数
     * @return 慢SQL表列
     */

    @ApiOperation(value = "Get table columns in slow sql.")
    @RequestMapping(value = "/{instanceId}/slowsql/table/column", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTableList getSlowSqlTableColumn(@PathVariable("instanceId") String instanceId,
                                        @Valid @RequestBody SlowsqlListRequest request) {
        SlowsqlTableList response = null;
        try {
            response = smartDbaService.getSlowSqlTableColumn(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取慢SQL的表索引
      * 根据instanceId和请求参数获取慢SQL的表索引信息
      * 
      * @param instanceId 实例ID
      * @param request 慢SQL列表请求参数
      * @return 慢SQL表索引列表
      */

    @ApiOperation(value = "Get table index in slow sql.")
    @RequestMapping(value = "/{instanceId}/slowsql/table/index", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTableList getSlowSqlTableIndex(@PathVariable("instanceId") String instanceId,
                                              @Valid @RequestBody SlowsqlListRequest request) {
        SlowsqlTableList response = null;
        try {
            response = smartDbaService.getSlowSqlTableIndex(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取慢SQL趋势请求
      * 根据实例ID和请求参数获取慢SQL趋势信息
      * 
      * @param instanceId 实例ID
      * @param request 慢SQL列表请求参数
      * @return 慢SQL趋势信息
      * @throws 无
      */

    @ApiOperation(value = "Get slow sql trend request.")
    @RequestMapping(value = "/{instanceId}/slowsql/trend", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTrend getSlowSqlTrend(@PathVariable("instanceId") String instanceId,
                                             @Valid @RequestBody SlowsqlListRequest request) {
        SlowsqlTrend response = null;
        try {
            response = smartDbaService.getSlowSqlTrend(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取慢SQL调优建议
      * 根据实例ID和慢SQL列表请求，获取慢SQL调优建议。
      *
      * @param instanceId 实例ID
      * @param request 慢SQL列表请求体
      * @return 慢SQL调优建议结果
      */

    @ApiOperation(value = "Get slow sql tuning advices.")
    @RequestMapping(value = "/{instanceId}/slowsql/tuning", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTrend getSlowSqlTuning(@PathVariable("instanceId") String instanceId,
                                        @Valid @RequestBody SlowsqlListRequest request) {
        SlowsqlTrend response = null;
        try {
            response = smartDbaService.getSlowSqlTuning(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

}
