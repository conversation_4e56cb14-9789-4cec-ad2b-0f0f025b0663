package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.idempotent.annotation.Idempotent;
import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.slowquery.OpenApiSlowqueryOpenStatusResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.OpenApiSlowqueryStartOrStopResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryChartRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryChartResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryCreateTaskRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryCreateTaskResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDetailRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDetailResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryGetSqlRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryGetSqlResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryInstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryOpenStatusResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryStartOrStopResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySummaryRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySummaryResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskDetailResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskListRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskListResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskModel;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.rds.service.SlowqueryService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Created by luping03 on 17/11/3.
 */
@RestController
@RequestMapping("/v1/instance/slowquery")
public class OpenApiSlowqueryController extends BaseController{
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private SlowqueryService slowqueryService;
    /**
      * 创建慢查询任务
      * 根据请求参数创建慢查询任务，并将任务信息返回
      * 
      * @param instanceId 实例ID，用于标识操作的目标实例
      * @param request 创建任务的请求参数，包含慢查询任务的具体信息
      * @return 创建任务的响应结果，包含任务的状态和ID等信息
      */

    @ApiOperation(value = "创建慢查询任务")
    @RequestMapping(value = "/task/{instanceId}", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public SlowqueryCreateTaskResponse createTask(@IdPermission @PathVariable @IdMapper String instanceId,
                                                  @RequestBody @Valid SlowqueryCreateTaskRequest request) {
        LOGGER.debug("slowquer createTask().");

        request.setInstanceId(instanceId);

        SlowqueryCreateTaskResponse response = null;
        try {
            response = slowqueryService.createTask(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查看慢查询任务详情
      * 根据instanceId和taskId获取慢查询任务详情
      *
      * @param instanceId 实例ID
      * @param taskId 任务ID
      * @return 慢查询任务详情
      */

    @ApiOperation(value = "查看慢查询任务详情")
    @RequestMapping(value = "/task/{instanceId}/{taskId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryTaskDetailResponse taskDetail(@IdPermission @PathVariable @IdMapper String instanceId,
                                                  @PathVariable String taskId) {
        LOGGER.debug("slowquer taskList().");
        SlowqueryTaskDetailResponse response = null;
        try {
            response = slowqueryService.taskDetail(instanceId, taskId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查看慢查询任务列表
      * 通过instanceId查询慢查询任务列表，支持分页和筛选时间
      * 
      * @param instanceId 实例ID
      * @param marker 分页标记
      * @param maxKeys 每页最大条目数
      * @param filterStartTime 筛选开始时间
      * @param filterEndTime 筛选结束时间
      * @return 慢查询任务列表
      */

    @ApiOperation(value = "查看慢查询任务列表")
    @RequestMapping(value = "/task/{instanceId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogicMarkerResultResponse<SlowqueryTaskModel> taskList(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @RequestParam(required = false, defaultValue = "-1") String marker,
            @RequestParam(required = false, defaultValue = "100") Integer maxKeys,
            @RequestParam(required = false) String filterStartTime,
            @RequestParam(required = false) String filterEndTime

    ) {
        LOGGER.debug("slowquer taskList().");

        SlowqueryTaskListRequest request = new SlowqueryTaskListRequest();

        request.setInstanceId(instanceId);
        request.setPageSize("100");
        request.setPageNo("1");
        request.setFilterStartTime(filterStartTime);
        request.setFilterEndTime(filterEndTime);

        LogicMarkerResultResponse<SlowqueryTaskModel> response = new LogicMarkerResultResponse<SlowqueryTaskModel>();

        try {
            SlowqueryTaskListResponse responseTemp = slowqueryService.taskList(request);

            // 总是大于100，按100算
            if (responseTemp.getPage().getTotalCount() > 100) {
                responseTemp.getPage().setTotalCount(100);
            }

            response =  slowqueryService.listForPageByMarker(responseTemp, marker, maxKeys);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 查看是否开启慢查询接口
     * 根据实例ID查询慢查询是否开启
     * 
     * @param instanceId 实例ID
     * @return OpenApiSlowqueryOpenStatusResponse对象，包含慢查询是否开启的状态
     */

    @ApiOperation(value = "查看是否开启慢查询")
    @RequestMapping(value = "/task/{instanceId}/isOpen", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenApiSlowqueryOpenStatusResponse getOpenStatus(@IdPermission @PathVariable @IdMapper String instanceId) {
        String from = "api";

        LOGGER.debug("get getOpenStatus list.");
        SlowqueryOpenStatusResponse response = null;
        OpenApiSlowqueryOpenStatusResponse openApiSlowqueryOpenStatusResponse = null;
        try {
            response = slowqueryService.getOpenStatus(instanceId, from);
            openApiSlowqueryOpenStatusResponse = new OpenApiSlowqueryOpenStatusResponse();
            if (response.getResult() != null && response.getResult().getCheckSlowqueryStatus() != null) {
                if ("yes".equals(response.getResult().getCheckSlowqueryStatus())) {
                    openApiSlowqueryOpenStatusResponse.setIsOpen("true");
                } else {
                    openApiSlowqueryOpenStatusResponse.setIsOpen("false");
                }
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return openApiSlowqueryOpenStatusResponse;
    }
    /**
      * 开启慢查询接口
      * 开启实例的慢查询功能
      * 
      * @param instanceId 实例ID
      * @param request 开启慢查询请求参数
      * @return 开启慢查询响应结果
      * @throws Exception 开启慢查询过程中出现异常
      */

    @ApiOperation(value = "开启慢查询")
    @RequestMapping(value = "/{instanceId}/start", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @Idempotent(serviceName = RDSConstant.SERVICE_NAME)
    public OpenApiSlowqueryStartOrStopResponse start(@IdPermission @PathVariable @IdMapper String instanceId,
                                                     @Valid SlowqueryInstanceIdRequest request) {
        LOGGER.debug("slowquer start().");
        if (StringUtils.isNotBlank(instanceId)) {
            request.setInstanceId(instanceId);
        }
        SlowqueryStartOrStopResponse response = null;
        OpenApiSlowqueryStartOrStopResponse openApiSlowqueryStartOrStopResponse = null;
        try {
            response = slowqueryService.start(request);

            openApiSlowqueryStartOrStopResponse = new OpenApiSlowqueryStartOrStopResponse();
            if (response.getResult() != null && response.getResult().getStartSlowQuery() != null) {
                if ("success".equals(response.getResult().getStartSlowQuery())) {
                    openApiSlowqueryStartOrStopResponse.setStartSlowQuery("true");
                } else {
                    openApiSlowqueryStartOrStopResponse.setStartSlowQuery("false");
                }
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return openApiSlowqueryStartOrStopResponse;
    }
    /**
      * 获取日志列表
      * 提供日志列表的查询接口，根据请求参数返回对应的日志数据
      * 
      * @param instanceId 实例ID，用于标识查询的数据库实例
      * @param request 查询请求参数，包含查询日志所需的各种条件
      * @return 日志列表查询结果
      */

    @ApiOperation(value = "获取日志列表")
    @RequestMapping(value = "/{instanceId}/chart", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryChartResponse chart(@IdPermission @PathVariable @IdMapper String instanceId,
                                        @RequestBody @Valid SlowqueryChartRequest request) {
        String from = "api";

        LOGGER.debug("get log list.");
        SlowqueryChartResponse response = null;

        if (StringUtils.isNotBlank(instanceId)) {
            request.setInstanceId(instanceId);
        }

        try {
            response = slowqueryService.chart(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取慢日志详情
      * 通过instanceId和请求参数获取慢日志的详细信息
      * 
      * @param instanceId 实例ID
      * @param request 慢日志请求参数
      * @return 慢日志详细信息
      */

    @ApiOperation(value = "获取慢日志详情")
    @RequestMapping(value = "/{instanceId}/detail", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryDetailResponse detail(@IdPermission @PathVariable @IdMapper String instanceId,
                                          @RequestBody @Valid SlowqueryDetailRequest request) {
        String from = "api";

        LOGGER.debug("get log detail.");
        SlowqueryDetailResponse response = null;

        if (StringUtils.isNotBlank(instanceId)) {
            request.setInstanceId(instanceId);
        }

        try {
            response = slowqueryService.detail(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取慢日志统计
      * 该函数用于获取指定实例的慢日志统计信息。
      * 
      * @param instanceId 实例ID，用于指定要获取哪个实例的慢日志统计信息
      * @param request 慢日志统计请求参数，包含查询条件等
      * @return 返回慢日志统计结果
      */

    @ApiOperation(value = "获取慢日志统计")
    @RequestMapping(value = "/{instanceId}/summary", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowquerySummaryResponse summary(@IdPermission @PathVariable @IdMapper String instanceId,
                                            @RequestBody @Valid SlowquerySummaryRequest request) {
        String from = "api";

        LOGGER.debug("get summary list.");
        SlowquerySummaryResponse response = null;

        if (StringUtils.isNotBlank(instanceId)) {
            request.setInstanceId(instanceId);
        }

        try {
            response = slowqueryService.summary(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取慢日志sql详情
     * 根据实例ID和请求体中的条件，获取慢日志sql详情
     * 
     * @param instanceId 实例ID
     * @param request 请求体，包含获取慢日志sql的条件
     * @return 获取到的慢日志sql详情
     */

    @ApiOperation(value = "获取慢日志sql详情")
    @RequestMapping(value = "/{instanceId}/getsql", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryGetSqlResponse getSql(@IdPermission @PathVariable @IdMapper String instanceId,
                                            @RequestBody @Valid SlowqueryGetSqlRequest request) {
        LOGGER.debug("get getSql list.");
        SlowqueryGetSqlResponse response = null;
        try {

            if (StringUtils.isNotBlank(instanceId)) {
                request.setInstanceId(instanceId);
            }

            response = slowqueryService.getSql(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

}
