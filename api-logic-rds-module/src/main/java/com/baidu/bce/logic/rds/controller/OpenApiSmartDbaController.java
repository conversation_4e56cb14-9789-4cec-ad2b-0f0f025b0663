package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSlowSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.OpenAPIGetSlowSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.PostSqlflowRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SqlIdResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SqlIdRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlExplainListResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTemplateResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlStatsDurationList;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTableList;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTrend;
import com.baidu.bce.internalsdk.rds.model.smartdba.DiskInfoResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaResultResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaPageResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaPageRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaTopoResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSessionKillTypesResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionSummaryRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionDetailResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionDetailRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionKillAuthorityRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionKillHistoryResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionStatisticsResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.ErrorLogDetailRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.ErrorLogDetailResponse;
import com.baidu.bce.logic.rds.service.SmartDbaService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;


/**
 * SmartDBA 相关 API
 *
 * <AUTHOR>
 * @since 3/11/22
 */
@RestController
@RequestMapping("/v1/instance/{instanceId}/smartdba")
public class OpenApiSmartDbaController {

    @Autowired
    private SmartDbaService smartDbaService;
    /**
      * 获取实例磁盘信息
      * 通过实例ID获取磁盘信息
      *
      * @param instanceId 实例ID
      * @return DiskInfoResponse 磁盘信息
      */

    @RequestMapping(value = "/disk/list", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public DiskInfoResponse getInstanceDisk(@IdPermission @PathVariable @IdMapper String instanceId) {
        DiskInfoResponse diskInfoResponse  = smartDbaService.instanceDisk(instanceId);
        return diskInfoResponse;
    }
    /**
      * 获取数据库列表
      * 根据instanceId和listNo参数获取数据库列表信息
      * 
      * @param instanceId 实例ID
      * @param listNo 分页编号
      * @return 数据库列表信息
      */

    @RequestMapping(value = "/db/list", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SmartDbaResultResponse getDbList(@IdPermission @PathVariable @IdMapper String instanceId,
                                            @RequestParam(value = "listNo", required = false) Integer listNo) {
        SmartDbaResultResponse smartDbaDbResponse = smartDbaService.getDbList(instanceId, listNo);
        return smartDbaDbResponse;
    }
    /**
      * 获取SmartDba分页数据接口
      * 根据提供的instanceId和smartDbaPageRequest参数获取对应的SmartDba分页数据
      * 
      * @param instanceId 实例ID，用于权限验证和数据获取
      * @param smartDbaPageRequest 分页请求参数，包含分页信息和查询条件
      * @return SmartDba分页响应数据
      */

    @RequestMapping(value = "/tb/list", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SmartDbaPageResponse getSmartDbaPage(@IdPermission @PathVariable @IdMapper String instanceId,
                                                @RequestBody SmartDbaPageRequest smartDbaPageRequest) {
        if (smartDbaPageRequest == null) {
            smartDbaPageRequest = new SmartDbaPageRequest();
        }
        SmartDbaPageResponse smartDbaPageResponse = smartDbaService
                .getSmartDbaPage(instanceId, smartDbaPageRequest);
        return smartDbaPageResponse;
    }
    /**
      * 获取拓扑列表
      * 通过instanceId获取数据库拓扑列表
      * 
      * @param instanceId 实例ID
      * @return 拓扑列表信息
      */

    @RequestMapping(value = "/topo/list", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SmartDbaTopoResponse getTopoList(@IdPermission @PathVariable @IdMapper String instanceId) {
        SmartDbaTopoResponse smartDbaTopoResponse = smartDbaService.getTopoList(instanceId);
        return smartDbaTopoResponse;

    }
    /**
      * 查询SQL洞察开通状态
      * 根据实例ID获取SQL洞察开通状态
      * 
      * @param instanceId 实例ID
      * @return SQL洞察开通状态结果
      */

    @ApiOperation(value = "查询 SQL 洞察开通状态")
    @RequestMapping(value = "/sqlflow", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSqlflowResponse getSqlflow(@IdPermission @PathVariable @IdMapper String instanceId) {
        GetSqlflowResponse getSqlflowResponse = null;
        try {
            getSqlflowResponse = smartDbaService.getSqlflow(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return getSqlflowResponse;
    }
    /**
     * 开通SQL洞察
     * 开通SQL洞察的接口，允许用户通过POST请求开通SQL洞察功能
     * 
     * @param instanceId 实例ID，用于指定要开通SQL洞察的数据库实例
     * @return GetSqlflowResponse 开通SQL洞察后的响应信息
     */

    @ApiOperation(value = "开通 SQL 洞察")
    @RequestMapping(value = "/sqlflow", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSqlflowResponse postSqlflow(@IdPermission @PathVariable @IdMapper String instanceId) {
        GetSqlflowResponse getSqlflowResponse = null;
        try {
            PostSqlflowRequest postSqlflowRequest = new PostSqlflowRequest();
            postSqlflowRequest.setInstanceId(instanceId);
            getSqlflowResponse = smartDbaService.postSqlflow(postSqlflowRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return getSqlflowResponse;
    }
    /**
     * 关闭SQL洞察
     * 删除指定实例的SQL洞察功能
     * 
     * @param instanceId 实例ID
     * @return GetSqlflowResponse 返回结果
     */

    @ApiOperation(value = "关闭 SQL 洞察")
    @RequestMapping(value = "/sqlflow", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSqlflowResponse deleteSqlflow(@IdPermission @PathVariable @IdMapper String instanceId) {
        GetSqlflowResponse getSqlflowResponse = null;
        try {
            getSqlflowResponse = smartDbaService.deleteSqlflow(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return getSqlflowResponse;
    }
    /**
      * 查询 SQL 列表
      * 提供一个接口，用于查询 SQL 列表信息。支持通过请求体传递查询参数。
      * 
      * @param instanceId 实例ID，用于标识需要查询的数据库实例
      * @param getSqlRequest 查询请求体，包含查询SQL列表所需的参数，可以为null
      * @return GetSqlResponse 返回查询结果
      */

    @ApiOperation(value = "查询 SQL 列表")
    @RequestMapping(value = "/full-sql", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSqlResponse getSql(@IdPermission @PathVariable @IdMapper String instanceId,
                                 @RequestBody(required = false) GetSqlRequest getSqlRequest) {
        GetSqlResponse getSqlResponse = null;
        try {
            getSqlRequest.setInstanceId(instanceId);
            getSqlResponse = smartDbaService.getSql(getSqlRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return getSqlResponse;
    }
    /**
     * 开通慢查询诊断
     * 开通指定RDS实例的慢查询诊断功能
     * 
     * @param instanceId RDS实例ID
     * @return 开通慢查询诊断的结果
     */

    @ApiOperation(value = "开通慢查询诊断")
    @RequestMapping(value = "/slowsqlflow", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenAPIGetSlowSqlflowResponse postSqlflowflow(@IdPermission @PathVariable @IdMapper String instanceId) {
        GetSlowSqlflowResponse getSlowSqlflowResponse = null;
        try {
            getSlowSqlflowResponse = smartDbaService.putSlowSqlflow(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }

        OpenAPIGetSlowSqlflowResponse resultresponse = new OpenAPIGetSlowSqlflowResponse();

        if (getSlowSqlflowResponse != null) {
            resultresponse.setSuccess(getSlowSqlflowResponse.getSuccess());
        }
        return resultresponse;
    }
    /**
      * 查询慢查询诊断开通状态
      * 根据实例ID查询慢查询诊断的开通状态
      * 
      * @param instanceId 实例ID
      * @return 慢查询诊断开通状态的结果
      */

    @ApiOperation(value = "查询慢查询诊断开通状态")
    @RequestMapping(value = "/slowsqlflow", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSlowSqlflowResponse getSlowSqlflow(@IdPermission @PathVariable @IdMapper String instanceId) {
        GetSlowSqlflowResponse getSlowSqlflowResponse = smartDbaService.getSlowSqlflow(instanceId);
        return getSlowSqlflowResponse;
    }
    /**
      * 关闭慢查询诊断
      * 该函数用于关闭数据库的慢查询诊断功能
      * 
      * @param instanceId 实例ID，用于标识需要关闭慢查询诊断的数据库实例
      * @return 关闭慢查询诊断的结果
      */

    @ApiOperation(value = "关闭慢查询诊断")
    @RequestMapping(value = "/slowsqlflow", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSlowSqlflowResponse deleteSlowSqlflow(@IdPermission @PathVariable @IdMapper String instanceId) {
        GetSlowSqlflowResponse getSlowSqlflowResponse = smartDbaService.deleteSlowSqlflow(instanceId);
        return getSlowSqlflowResponse;
    }
    /**
      * 获取慢SQL列表
      * 提供获取慢SQL列表的接口，支持通过请求体中的参数进行筛选
      * 
      * @param instanceId 实例ID，用于指定要查询的数据库实例
      * @param request 请求体，包含筛选慢SQL列表的参数，可以为null，此时使用默认参数
      * @return 慢SQL列表的响应体
      */

    @ApiOperation(value = "获取慢SQL列表")
    @RequestMapping(value = "/slowsql/list", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlListResponse getSlowSqlList(@IdPermission @PathVariable @IdMapper String instanceId,
                                              @RequestBody(required = false) SlowsqlListRequest request) {
        if (request == null) {
            request = new SlowsqlListRequest();
        }
        SlowsqlListResponse response  = smartDbaService.getSlowSqlList(instanceId, request);
        return response;
    }
    /**
      * 根据sqlId获取慢SQL
      * 根据提供的sqlId和实例id，从后端服务获取慢SQL的详细信息
      * 
      * @param instanceId 实例id，用于标识不同的数据库实例
      * @param sqlId SQL的唯一标识，用于查询具体的慢SQL记录
      * @return 返回慢SQL的详细信息
      */

    @ApiOperation(value = "根据sqlId获取慢SQL")
    @RequestMapping(value = "/{sqlId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlIdResponse getSlowSqlBySqlId(@IdPermission @PathVariable @IdMapper String instanceId,
                                           @PathVariable String sqlId) {
        SqlIdRequest request = new SqlIdRequest();
        request.setSqlId(sqlId);
        SqlIdResponse sqlIdResponse = smartDbaService.getSlowSqlBySqlId(instanceId, request);
        return sqlIdResponse;
    }
    /**
      * 获取慢SQL说明
      * 根据给定的SQL ID和schema，获取慢SQL的详细解释信息。
      * 
      * @param instanceId 实例ID
      * @param schema 数据库schema
      * @param sqlId SQL ID
      * @return 慢SQL解释信息
      */

    @ApiOperation(value = "获取慢SQL说明")
    @RequestMapping(value = "/slowsql/explain/{sqlId}/{schema}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlExplainListResponse getSlowSqlExplain(@IdPermission @PathVariable @IdMapper String instanceId,
                                                        @PathVariable String schema, @PathVariable String sqlId) {
        SlowsqlListRequest request = new SlowsqlListRequest();
        request.setSchema(schema);
        request.setSqlId(sqlId);
        SlowsqlExplainListResponse response = smartDbaService.getSlowSqlExplain(instanceId, request);
        return response;
    }
    /**
      * 获取SQL模板维度的统计信息
      * 通过POST请求获取指定RDS实例的慢SQL模板统计信息
      *
      * @param instanceId RDS实例ID
      * @param request 慢SQL列表请求参数，可以为null，为null时默认创建一个新的请求参数对象
      * @return 慢SQL模板统计信息响应对象
      */

    @ApiOperation(value = "Get the statistics information of SQL template dimension.")
    @RequestMapping(value = "/slowsql/stats/digest", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTemplateResponse getSlowSqlTemplate(@IdPermission @PathVariable @IdMapper String instanceId,
                                                      @RequestBody(required = false)
                                                              SlowsqlListRequest request) {
        if (request == null) {
            request = new SlowsqlListRequest();
        }
        SlowsqlTemplateResponse response = smartDbaService.getSlowSqlTemplate(instanceId, request);
        return response;
    }
    /**
      * 获取慢SQL运行时长分布
      * 通过POST请求，获取指定RDS实例的慢SQL运行时长分布信息
      *
      * @param instanceId RDS实例ID
      * @param request 慢SQL列表请求参数，可以为null，如果为null则使用默认请求参数
      * @return 慢SQL运行时长分布列表
      */

    @ApiOperation(value = "Get slow sql run time duration distribution.")
    @RequestMapping(value = "/slowsql/stats/duration", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlStatsDurationList getSlowSqlStatsDuration(@IdPermission @PathVariable @IdMapper String instanceId,
                                                            @RequestBody(required = false)
                                                                    SlowsqlListRequest request) {
        if (request == null) {
            request = new SlowsqlListRequest();
        }
        SlowsqlStatsDurationList response = smartDbaService.getSlowSqlStatsDuration(instanceId, request);
        return response;
    }
    /**
      * 获取慢SQL源客户端分布
      * 接口用于获取RDS慢SQL源客户端分布
      * 
      * @param instanceId 实例ID
      * @param request 慢SQL列表请求参数
      * @return 慢SQL统计时长列表
      */

    @ApiOperation(value = "Get slow sql source client distribution.")
    @RequestMapping(value = "/slowsql/stats/source", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlStatsDurationList getSlowSqlStatsSource(@IdPermission @PathVariable @IdMapper String instanceId,
                                                          @RequestBody(required = false)
                                                                  SlowsqlListRequest request) {
        if (request == null) {
            request = new SlowsqlListRequest();
        }
        SlowsqlStatsDurationList response = smartDbaService.getSlowSqlStatsSource(instanceId, request);
        return response;
    }
    /**
     * 获取慢查询SQL表信息
     * 根据提供的sqlId和schema获取慢查询SQL表信息
     * 
     * @param instanceId 实例ID
     * @param sqlId SQL ID
     * @param schema 数据库schema
     * @return 慢查询SQL表信息
     */

    @ApiOperation(value = "Get tables in slow sql.")
    @RequestMapping(value = "/slowsql/{sqlId}/{schema}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTableList getSlowSqlTable(@IdPermission @PathVariable @IdMapper String instanceId,
                                            @PathVariable String sqlId, @PathVariable String schema) {
        SlowsqlListRequest request = new SlowsqlListRequest();
        request.setSqlId(sqlId);
        request.setSchema(schema);
        SlowsqlTableList response = smartDbaService.getSlowSqlTable(instanceId, request);
        return response;
    }
    /**
      * 获取慢SQL表列信息
      * 根据传入的实例ID、SQLID、数据库schema和表名，获取对应的慢SQL表列信息
      * 
      * @param instanceId 实例ID
      * @param sqlId SQLID
      * @param schema 数据库schema
      * @param table 表名
      * @return 慢SQL表列信息
      */

    @ApiOperation(value = "Get table columns in slow sql.")
    @RequestMapping(value = "/slowsql/{sqlId}/{schema}/{table}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTableList getSlowSqlTableColumn(@IdPermission @PathVariable @IdMapper String instanceId,
                                                  @PathVariable String sqlId, @PathVariable String schema,
                                                  @PathVariable String table) {
        SlowsqlListRequest request = new SlowsqlListRequest();
        request.setSqlId(sqlId);
        request.setSchema(schema);
        request.setTable(table);
        SlowsqlTableList response = smartDbaService.getSlowSqlTableColumn(instanceId, request);
        return response;
    }
    /**
     * 获取慢查询表的索引
     * 根据实例ID和请求参数获取慢查询表的索引信息
     * 
     * @param instanceId 实例ID
     * @param request 请求参数，可以为null，null时创建默认请求
     * @return 慢查询表的索引信息
     */

    @ApiOperation(value = "Get table index in slow sql.")
    @RequestMapping(value = "/slowsql/table/index", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTableList getSlowSqlTableIndex(@IdPermission @PathVariable @IdMapper String instanceId,
                                                 @RequestBody(required = false) SlowsqlListRequest request) {
        if (request == null) {
            request = new SlowsqlListRequest();
        }
        SlowsqlTableList response = smartDbaService.getSlowSqlTableIndex(instanceId, request);
        return response;
    }
    /**
      * 获取慢SQL趋势请求
      * 该接口用于获取指定实例的慢SQL趋势信息
      * 
      * @param instanceId 实例ID
      * @param request 慢SQL列表请求体，可以为null，为null时将使用默认请求体
      * @return 慢SQL趋势信息
      */

    @ApiOperation(value = "Get slow sql trend request.")
    @RequestMapping(value = "/slowsql/trend", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTrend getSlowSqlTrend(@IdPermission @PathVariable @IdMapper String instanceId,
                                        @RequestBody(required = false) SlowsqlListRequest request) {
        if (request == null) {
            request = new SlowsqlListRequest();
        }
        SlowsqlTrend response  = smartDbaService.getSlowSqlTrend(instanceId, request);
        return response;
    }
    /**
     * 获取慢SQL调优建议
     * 根据提供的sqlId和schema获取慢SQL调优建议
     * 
     * @param instanceId 实例ID
     * @param sqlId SQL ID
     * @param schema schema名称
     * @return 慢SQL调优建议
     */

    @ApiOperation(value = "Get slow sql tuning advices.")
    @RequestMapping(value = "/slowsql/tuning/{sqlId}/{schema}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowsqlTrend getSlowSqlTuning(@IdPermission @PathVariable @IdMapper String instanceId,
                                         @PathVariable String sqlId, @PathVariable String schema) {
        SlowsqlListRequest request = new SlowsqlListRequest();
        request.setSqlId(sqlId);
        request.setSchema(schema);
        SlowsqlTrend response = smartDbaService.getSlowSqlTuning(instanceId, request);
        return response;
    }
    /**
     * 获取会话kill类型参数
     * 获取指定会话kill类型的相关参数.
     *
     * @param instanceId 实例ID
     * @param type 类型，默认为"command"
     * @return 会话kill类型参数结果
     */

    @ApiOperation(value = "获取指定会话kill类型的相关参数.")
    @RequestMapping(value = "/session/kill/types", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSessionKillTypesResponse getSessionKillTypesRequest(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @RequestParam(required = false, defaultValue = "command") String type) {
        GetSessionKillTypesResponse response = smartDbaService.getSessionKillTypesRequest(instanceId, type);
        return response;
    }
    /**
      * 获取实例会话概览
      * 通过实例ID获取会话概览信息
      *
      * @param instanceId 实例ID
      * @return 会话概览信息
      */

    @ApiOperation(value = "获取指定实例的会话概览.")
    @RequestMapping(value = "/session/summary", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SessionSummaryRequest getSessionSummaryRequest(
            @IdPermission @PathVariable @IdMapper String instanceId) {
        SessionSummaryRequest response = smartDbaService.getSessionSummaryRequest(instanceId);
        return response;
    }
    /**
     * 获取指定实例的实时会话
     * 根据指定的实例ID和会话详情请求，获取实时会话详情。
     * 
     * @param instanceId 实例ID
     * @param request 会话详情请求体
     * @return 会话详情响应
     */

    @ApiOperation(value = "获取指定实例的实时会话.")
    @RequestMapping(value = "/session/detail", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SessionDetailResponse getSessionDetailRequest(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @RequestBody(required = false) SessionDetailRequest request) {
        if (request == null) {
            request = new SessionDetailRequest();
        }
        SessionDetailResponse response = smartDbaService.getSessionDetailRequest(instanceId, request);
        return response;
    }
    /**
      * 校验执行kill操作的数据库用户及密码是否正确
      * 校验执行kill操作的数据库用户及密码是否正确，传入用户名和密码时直接校验，不传入用户名和密码时校验数据库是否存在有效记录。
      * 
      * @param instanceId 数据库实例ID
      * @param request 请求参数，包含用户名和密码，可以为null
      * @return 校验结果
      */

    @ApiOperation(value = "校验执行kill操作的数据库用户及密码是否正确传入用户名和密码时直接校验，" +
            "不传入用户名和密码时校验数据库是否存在有效记录.")
    @RequestMapping(value = "/session/kill/authority", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenAPIGetSlowSqlflowResponse postSessionKillAuthorityRequest(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @RequestBody(required = false) SessionKillAuthorityRequest request) {
        if (request == null) {
            request = new SessionKillAuthorityRequest();
        }
        OpenAPIGetSlowSqlflowResponse response = smartDbaService.postSessionKillAuthorityRequest(instanceId, request);
        return response;
    }
    /**
      * 获取实例会话kill记录
      * 根据实例ID和会话详情请求获取会话kill记录
      *
      * @param instanceId 实例ID
      * @param request 会话详情请求
      * @return 会话kill记录响应
      */

    @ApiOperation(value = "获取指定实例的会话kill记录.")
    @RequestMapping(value = "/session/kill/history", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SessionKillHistoryResponse getSessionKillHistoryRequest(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @RequestBody(required = false) SessionDetailRequest request) {
        if (request == null) {
            request = new SessionDetailRequest();
        }
        SessionKillHistoryResponse response = smartDbaService.getSessionKillHistoryRequest(instanceId, request);
        return response;
    }
    /**
     * 根据传入的kill类型及类型所对应的值执行kill会话的操作
     * 该函数用于处理POST请求，根据传入的kill类型及类型所对应的值执行kill会话的操作
     * 
     * @param instanceId 实例ID，用于指定要操作的实例
     * @param request kill会话的请求体，包含kill类型及类型所对应的值
     * @return OpenAPIGetSlowSqlflowResponse 响应体，包含操作结果
     */

    @ApiOperation(value = "根据传入的kill类型及类型所对应的值执行kill会话的操作")
    @RequestMapping(value = "/session/kill", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenAPIGetSlowSqlflowResponse postSessionKillRequest(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @RequestBody(required = false) SessionKillAuthorityRequest request) {
        if (request == null) {
            request = new SessionKillAuthorityRequest();
        }
        OpenAPIGetSlowSqlflowResponse response = smartDbaService.postSessionKillRequest(instanceId, request);
        return response;
    }
    /**
      * 获取实例会话统计
      * 获取指定实例的会话统计信息。
      * 
      * @param instanceId 实例ID
      * @return 会话统计信息
      */

    @ApiOperation(value = "获取指定实例的会话统计.")
    @RequestMapping(value = "/session/statistics", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SessionStatisticsResponse getSessionStatisticsRequest(
            @IdPermission @PathVariable @IdMapper String instanceId) {
        SessionStatisticsResponse response = smartDbaService.getSessionStatisticsRequest(instanceId);
        return response;
    }
    /**
     * 查询错误日志服务是否开启
     * 此函数用于查询错误日志服务是否开启，通过调用smartDbaService.getErrorLogFlowRequest方法实现。
     * 
     * @param instanceId 实例ID，用于指定查询哪个实例的错误日志服务状态
     * @return GetSlowSqlflowResponse 返回查询结果，包含错误日志服务是否开启的信息
     */

    @ApiOperation(value = "查询错误日志服务是否开启.")
    @RequestMapping(value = "/errorlogflow", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSlowSqlflowResponse getErrorLogFlowRequest(
            @IdPermission @PathVariable @IdMapper String instanceId) {
        GetSlowSqlflowResponse response = smartDbaService.getErrorLogFlowRequest(instanceId);
        return response;
    }
    /**
      * 开启错误日志服务
      * PUT请求到/errorlogflow地址，用于开启错误日志服务
      *
      * @param instanceId 实例ID，用于标识需要开启错误日志服务的实例
      * @return GetSlowSqlflowResponse 响应结果
      */

    @ApiOperation(value = "开启错误日志服务.")
    @RequestMapping(value = "/errorlogflow", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSlowSqlflowResponse putErrorLogFlowRequest(
            @IdPermission @PathVariable @IdMapper String instanceId) {
        GetSlowSqlflowResponse response = smartDbaService.putErrorLogFlowRequest(instanceId);
        return response;
    }
    /**
      * 关闭错误日志服务
      * 根据实例ID删除错误日志服务
      * 
      * @param instanceId 实例ID
      * @return 删除错误日志服务的结果
      */

    @ApiOperation(value = "关闭错误日志服务.")
    @RequestMapping(value = "/errorlogflow", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public GetSlowSqlflowResponse deleteErrorLogFlowRequest(
            @IdPermission @PathVariable @IdMapper String instanceId) {
        GetSlowSqlflowResponse response = smartDbaService.deleteErrorLogFlowRequest(instanceId);
        return response;
    }
    /**
      * 获取错误日志详情
      * 根据实例ID和请求参数获取错误日志的详细信息。
      * 
      * @param instanceId 实例ID
      * @param request 请求参数，可以为null，如果为null则创建新的ErrorLogDetailRequest对象
      * @return 错误日志详情
      */

    @ApiOperation(value = "获取错误日志列表.")
    @RequestMapping(value = "/errorlog/detail", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ErrorLogDetailResponse getErrorLogDetailRequest(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @RequestBody(required = false) ErrorLogDetailRequest request) {
        if (request == null) {
            request = new ErrorLogDetailRequest();
        }
        ErrorLogDetailResponse response = smartDbaService.getErrorLogDetailRequest(instanceId, request);
        return response;
    }
}
