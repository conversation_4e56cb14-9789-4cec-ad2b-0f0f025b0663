package com.baidu.bce.logic.rds.controller;


import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.rds.model.AutoResizeConfigResponse;
import com.baidu.bce.internalsdk.rds.model.CpuAutoResizeConfigResp;
import com.baidu.bce.internalsdk.rds.model.CpuAutoResizeEnableResp;
import com.baidu.bce.internalsdk.rds.model.ModifyCpuAutoResizeReq;
import com.baidu.bce.internalsdk.rds.model.SupportEnabledDiskAutoResizeResponse;
import com.baidu.bce.internalsdk.rds.model.SupportEnablesDiskAutoResizeRequest;
import com.baidu.bce.internalsdk.rds.model.UpdateAutoExpansionConfigRequest;
import com.baidu.bce.logic.rds.service.AutoExpansionService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/rds/instance")
public class ConsoleApiAutoExpansionController {

    @Autowired
    public AutoExpansionService autoExpansionService;

    @Autowired
    private IdMapperService idMapperService;
    /**
     * 实例是否支持自动扩容
     * 判断实例是否支持自动扩容的功能
     * 
     * @param request 支持自动扩容的请求参数，包含实例ID
     * @return EdpResultResponse<SupportEnabledDiskAutoResizeResponse> 返回实例是否支持自动扩容的结果
     * @throws BceInternalResponseException 内部响应异常
     */

    @ApiOperation(value = "实例是否支持自动扩容")
    @RequestMapping(value = "/autoExpansion", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SupportEnabledDiskAutoResizeResponse> supportAutoExpansion(
            @IdPermission @RequestBody SupportEnablesDiskAutoResizeRequest request) {
        EdpResultResponse<SupportEnabledDiskAutoResizeResponse> resultResponse =
                new EdpResultResponse<>();

        // 控制台兼容长短id
        String instanceUuid = idMapperService.getInstanceUuid(request.getInstanceId());
        request.setInstanceId(instanceUuid);

        try {
            resultResponse.setResult(autoExpansionService.supportAutoExpansion(request.getInstanceId()));
        } catch (BceInternalResponseException e) {
            BasisUtils.logicRdsExceptions(e);
        }


        return resultResponse;
    }

    /**
      * 获取自动扩容配置信息
      * 根据instanceId获取自动扩容的配置信息
      * 
      * @param instanceId 实例ID
      * @return 自动扩容配置信息
      * @throws BceInternalResponseException 内部响应异常
      */
    @ApiOperation(value = "自动扩容配置信息")
    @RequestMapping(value = "/{instanceId}/autoResizeConfig", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<AutoResizeConfigResponse> getAutoExpansionConfig(
            @IdPermission @PathVariable String instanceId) {

        EdpResultResponse<AutoResizeConfigResponse> resultResponse =
                new EdpResultResponse<>();
        try {
            resultResponse.setResult(autoExpansionService.getAutoExpansionConfig(instanceId));
        } catch (BceInternalResponseException e) {
            BasisUtils.logicRdsExceptions(e);
        }

        return resultResponse;
    }
    /**
      * 开启/关闭/修改存储自动扩容配置
      * 根据传入的instanceId、action和request更新自动扩容配置
      * 
      * @param instanceId 实例ID
      * @param action 操作类型，如开启、关闭或修改
      * @param request 更新自动扩容配置请求体
      * @return EdpResultResponse<Boolean> 操作结果
      * @throws BceInternalResponseException 内部响应异常
      */

    @ApiOperation(value = "开启/关闭/修改存储自动扩容配置")
    @RequestMapping(value = "/{instanceId}/diskAutoResize/config", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateAutoExpansionConfig(
            @IdPermission @PathVariable String instanceId,
            @RequestParam(value = "action", required = false) String action,
            @RequestBody UpdateAutoExpansionConfigRequest request){

        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();

        try {
            autoExpansionService.updateAutoExpansionConfig(instanceId, action, request);
        } catch (BceInternalResponseException e) {
            BasisUtils.logicRdsExceptions(e);
        }

        resultResponse.setResult(true);
        return resultResponse;
    }

    @ApiOperation(value = "实例是否支持启用cpu自动变配")
    @RequestMapping(value = "/{instanceId}/enableCpuAutoResize", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<CpuAutoResizeEnableResp> enableCpuAutoResize(
            @IdPermission @PathVariable String instanceId) {
        EdpResultResponse<CpuAutoResizeEnableResp> resultResponse =
                new EdpResultResponse<>();

        resultResponse.setResult(autoExpansionService.enableCpuAutoResize(instanceId));
        return resultResponse;
    }

    @ApiOperation(value = "获取指定实例的cpu自动变配配置信息")
    @RequestMapping(value = "/{instanceId}/cpu/autoresize/config", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<CpuAutoResizeConfigResp> getCpuAutoResize(
            @IdPermission @PathVariable String instanceId) {
        EdpResultResponse<CpuAutoResizeConfigResp> resultResponse =
                new EdpResultResponse<>();
        resultResponse.setResult(autoExpansionService.getCpuAutoResize(instanceId));
        return resultResponse;
    }


    @ApiOperation(value = "实例开启/关闭/修改cpu自动变配配置")
    @RequestMapping(value = "/{instanceId}/cpu/autoresize/config", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> modifyCpuAutoResize(
            @IdPermission @PathVariable String instanceId,
            @RequestParam(value = "action") String action,
            @RequestBody ModifyCpuAutoResizeReq request){

        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        autoExpansionService.modifyCpuAutoResize(instanceId, action, request);
        resultResponse.setResult(true);
        return resultResponse;
    }
}
