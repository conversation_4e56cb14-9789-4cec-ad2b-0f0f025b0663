package com.baidu.bce.logic.rds.controller;


import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountCheckRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountCreateRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountDeleteRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountDetailRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountListRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountUpdateDescRequest;
import com.baidu.bce.internalsdk.rds.model.account.DashboardAccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelAccountDetailRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelDeleteAccountRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelDetailRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelListResponse;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelPrivilegeScopeResponse;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelUpdateDescRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelUpdatePrivilegeRequest;
import com.baidu.bce.internalsdk.rds.model.account.V2Account;
import com.baidu.bce.internalsdk.rds.model.account.V2AccountUpdatePrivilegesRequest;
import com.baidu.bce.internalsdk.rds.model.instance.AccountShowResponse;
import com.baidu.bce.logic.rds.service.AccountService;
import com.baidu.bce.logic.rds.service.V2AccountService;
import com.baidu.bce.logic.rds.service.model.IsExistResponse;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

/**
 * Created by hejianbin on 2014/6/20.
 */

@Controller
@RequestMapping(value = "/api/rds/account2", produces = {"application/json"})
@Api(value = "RDS Dashboard Account管理API")
public class ConsoleApiV2AccountController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private V2AccountService v2AccountService;

    /**
      * 列表查询接口
      * 用于获取账户列表的信息
      * 
      * @param requestBody 请求体，包含查询条件和分页信息
      * @return EdpPageResultResponse<V2Account> 分页响应结果，包含账户列表信息
      */    
    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpPageResultResponse<V2Account> list(
            @IdPermission @Valid @RequestBody DashboardAccountListRequest requestBody) {
        EdpPageResultResponse<V2Account> response = new EdpPageResultResponse<>();
        response.getPage().setResult(v2AccountService.list(requestBody.getInstanceId(), "").getAccounts());
        return response;
    }
    
    /**
      * 创建仪表盘账户接口
      * 通过POST请求创建仪表盘账户，需要校验权限，支持RDS服务
      * 
      * @param request 创建仪表盘账户的请求体，包含instanceId，account等信息
      * @return EdpResultResponse，创建成功返回true，否则返回false
      */
    @RequestMapping(value = "create", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> create(@IdPermission @Valid @RequestBody DashboardAccountCreateRequest request) {
        v2AccountService.create(request.getInstanceId(), request.getAccount(), "", null);
        return new EdpResultResponse<>();
    }
    
    /**
      * 获取账户详情
      * 根据实例ID和账户名称获取账户的详细信息
      * 
      * @param request 包含实例ID和账户名称的请求体
      * @return 账户详细信息的结果封装
      */
    @RequestMapping(value = "detail", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<AccountShowResponse> detail(
            @IdPermission @Valid @RequestBody DashboardAccountDetailRequest request) {
        EdpResultResponse<AccountShowResponse> response = new EdpResultResponse<>();
        response.setResult(v2AccountService.detail(request.getInstanceId(), request.getAccountName(), ""));
        return response;
    }
    
    /**
     * 更新描述接口
     * 根据请求体中的instanceId、accountName、remark更新账户备注信息
     * 
     * @param request 请求体，包含instanceId、accountName、remark等字段
     * @return EdpResultResponse对象，表示操作结果
     */
    @RequestMapping(value = "update_desc", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateDesc(
            @IdPermission @Valid @RequestBody DashboardAccountUpdateDescRequest request) {
        AccountUpdateRemarkRequest accountUpdateRemarkRequest = new AccountUpdateRemarkRequest();
        accountUpdateRemarkRequest.setRemark(request.getRemark());
        accountService.updateRemark(request.getInstanceId(), request.getAccountName(), accountUpdateRemarkRequest);
        return new EdpResultResponse<>();
    }
    
    /**
      * 更新密码
      * 更新用户密码的接口函数，使用POST方法，并需要进行权限验证
      * 
      * @param request 包含更新密码所需信息的请求体
      * @return EdpResultResponse<Boolean> 更新操作的结果
      */
    @RequestMapping(value = "update_password", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updatePassword(
            @IdPermission @Valid @RequestBody DashboardAccountUpdatePasswordRequest request) {
        AccountUpdatePasswordRequest accountUpdatePasswordRequest = new AccountUpdatePasswordRequest();
        accountUpdatePasswordRequest.setEncryptedPassword(request.getEncryptedPassword());
        v2AccountService.updatePW(request.getInstanceId(), request.getAccountName(), accountUpdatePasswordRequest,
                "", null);
        return new EdpResultResponse<>();
    }
    
    /**
      * 更新权限
      * 该函数用于更新RDS的权限，通过传入请求体中包含的实例ID和权限信息来更新指定实例的权限。
      * 
      * @param request 包含实例ID和权限信息的请求体
      * @return EdpResultResponse<Boolean> 操作结果，成功返回true，失败返回false
      */
    @RequestMapping(value = "update_permission", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updatePermission(
            @IdPermission @Valid @RequestBody V2AccountUpdatePrivilegesRequest request) {
        v2AccountService.updatePrivileges(request.getInstanceId(), request, "");
        return new EdpResultResponse<>();
    }
    
    /**
      * 删除账户接口
      * 删除指定实例下的账户
      * 
      * @param request 账户删除请求，包含实例ID和账户名称
      * @return EdpResultResponse对象，包含操作结果
      */
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> delete(@IdPermission @Valid @RequestBody DashboardAccountDeleteRequest request) {
        accountService.deleteAccount(request.getInstanceId(), request.getAccountName(), "");
        return new EdpResultResponse<>();
    }

    /**
      * 获取表级别列表
      * 根据请求体中的instanceId获取对应的表级别列表信息
      *
      * @param requestBody 请求体，包含instanceId等信息
      * @return EdpResultResponse对象，包含表级别列表信息
      */
    @RequestMapping(value = "/tableLevel/list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<TableLevelListResponse> listTableLevel(
            @IdPermission @Valid @RequestBody DashboardAccountListRequest requestBody) {
        EdpResultResponse<TableLevelListResponse> response = new EdpResultResponse<>();
        response.setResult(v2AccountService.listTableLevel(requestBody.getInstanceId(), ""));
        return response;
    }
    /**
      * 创建表级别
      * 创建表级别的接口函数，通过传入表级别详细信息请求，实现表级别的创建
      * 
      * @param request 表级别详细信息请求对象，包含instanceId, account等信息
      * @return EdpResultResponse<Boolean> 返回创建表级别的结果
      */

    @RequestMapping(value = "/tableLevel/create", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> createTableLevel(@IdPermission @Valid @RequestBody TableLevelDetailRequest request) {
        v2AccountService.createTableLevel(request.getInstanceId(), request.getAccount(), "", null);
        return new EdpResultResponse<>();
    }
    /**
      * 获取表级别账户详情
      * 根据请求参数获取表级别账户的详细信息
      * 
      * @param request 表级别账户请求参数
      * @return 表级别账户详情响应结果
      */

    @RequestMapping(value = "/tableLevel/detail", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<TableLevelAccountDetailRequest> detailTableLevel(
            @IdPermission @Valid @RequestBody TableLevelDeleteAccountRequest request) {
        EdpResultResponse<TableLevelAccountDetailRequest> response = new EdpResultResponse<>();
        response.setResult(v2AccountService.detailTableLevel(request, ""));
        return response;
    }
    /**
      * 更新表级别描述
      * 更新数据库表级别的描述信息
      *
      * @param request 更新表级别描述请求参数，包含表级别描述信息
      * @return EdpResultResponse<Boolean> 更新操作结果
      */

    @RequestMapping(value = "/tableLevel/update_desc", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateDescTableLevel(
            @IdPermission @Valid @RequestBody TableLevelUpdateDescRequest request) {
        v2AccountService.updateRemarkTableLevel(request);
        return new EdpResultResponse<>();
    }
    /**
      * 更新表级别密码
      * 更新指定表级别的密码，需要验证RDS服务的写权限和操作权限
      * 
      * @param request 更新密码请求体，包含新密码等信息
      * @return 操作结果
      */

    @RequestMapping(value = "/tableLevel/update_password", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updatePasswordTableLevel(
            @IdPermission @Valid @RequestBody TableLevelUpdatePasswordRequest request) {

        v2AccountService.updatePWTableLevel(request, "", null);
        return new EdpResultResponse<>();
    }
    /**
      * 更新表级别权限
      * 根据请求参数更新表级别的权限
      * 
      * @param request 更新表级别权限的请求参数
      * @return 操作结果
      */

    @RequestMapping(value = "/tableLevel/update_permission", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updatePermissionTableLevel(
            @IdPermission @Valid @RequestBody TableLevelUpdatePrivilegeRequest request) {
        v2AccountService.updatePrivilegesTableLevel(request, "");
        return new EdpResultResponse<>();
    }
    /**
      * 删除表级别接口
      * 根据传入的请求参数删除对应的表级别信息
      * 
      * @param request 包含删除表级别信息的请求参数
      * @return EdpResultResponse<Boolean> 删除操作的结果
      */

    @RequestMapping(value = "/tableLevel/delete", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> deleteTableLevel(@IdPermission @Valid @RequestBody TableLevelDeleteAccountRequest request) {
        v2AccountService.deleteAccountTableLevel(request, "");
        return new EdpResultResponse<>();
    }
    /**
      * 检查账户接口
      * 检查指定实例下的账户是否存在
      * 
      * @param request 请求参数，包含实例ID和账户名称
      * @return EdpResultResponse<Boolean> 返回结果，包含检查结果
      */

    @RequestMapping(value = "check", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> check(@Valid @RequestBody DashboardAccountCheckRequest request) {
        EdpResultResponse<Boolean> response = new EdpResultResponse<Boolean>();
        IsExistResponse isExistResponse =
                accountService.accountCheck(request.getInstanceId(), request.getAccountName());
        response.withResult(isExistResponse.isExist());
        return response;
    }
    /**
      * 获取权限范围
      * 根据instanceId获取对应的数据库权限范围
      * 
      * @param instanceId 实例ID
      * @return EdpResultResponse<TableLevelPrivilegeScopeResponse> 权限范围结果
      */

    @RequestMapping(value = "scope", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<TableLevelPrivilegeScopeResponse> permissionScope(@IdPermission @RequestParam String instanceId) {
        EdpResultResponse<TableLevelPrivilegeScopeResponse> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(v2AccountService.permissionScope(instanceId, ""));
        return resultResponse;
    }
}
