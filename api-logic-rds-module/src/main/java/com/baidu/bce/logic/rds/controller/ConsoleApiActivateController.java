package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.logic.rds.service.model.otherservice.ServiceParam;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/rds/getRoleName")
public class ConsoleApiActivateController {

    @Value("${rds.service.policyId:}")
    String rdsServicePolicyId;

    @Value("${rds.service.accountId:}")
    String rdsServiceAccountId;

    @Value("${console.rds.service.accountId:}")
    String consoleRdsServiceAccountId;

    @Value("${console.rds.service.policyId:}")
    String consoleRdsServicePolicyId;

    @Value("${bec.iam.serviceId:}")
    String becIamServiceId;

    @Value("${bec.iam.policyId:}")
    String becIamPolicyId;

    @Value("${bec.iam.roleName:}")
    String becIamRoleName;
    /**
     * 获取角色名称列表
     * 通过POST请求，该方法用于获取一组服务角色的名称及相关信息，并将这些信息封装在EdpResultResponse对象中返回
     * 
     * @return EdpResultResponse对象，其中包含了List<ServiceParam>类型的角色名称列表
     */

    @RequestMapping(value = "", method = RequestMethod.POST)
    public EdpResultResponse<List<ServiceParam>> getRoleName () {
        EdpResultResponse<List<ServiceParam>> edpResultResponse = new EdpResultResponse<>();

        List<ServiceParam> serviceParamList = new ArrayList<>();

        ServiceParam consoleRdsService = new ServiceParam();
        consoleRdsService.setPolicyId(consoleRdsServicePolicyId);
        consoleRdsService.setRoleName("BceServiceRole_console_rds");
        consoleRdsService.setServiceId(consoleRdsServiceAccountId);
        serviceParamList.add(consoleRdsService);

        ServiceParam rdsService = new ServiceParam();
        rdsService.setPolicyId(rdsServicePolicyId);
        rdsService.setRoleName("RdsDefaultRole");
        rdsService.setServiceId(rdsServiceAccountId);
        serviceParamList.add(rdsService);

        if (StringUtils.isNotEmpty(becIamPolicyId)) {
            // 只有边缘计算区域需要激活该服务角色
            ServiceParam becService = new ServiceParam();
            becService.setPolicyId(becIamPolicyId);
            becService.setRoleName(becIamRoleName);
            becService.setServiceId(becIamServiceId);
            serviceParamList.add(becService);
        }

        edpResultResponse.setResult(serviceParamList);
        return edpResultResponse;
    }
}
