package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.internalsdk.rds.model.edge.EdgeRegion;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeVpc;
import com.baidu.bce.logic.rds.service.EdgeService;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/api/rds/edge")
public class ConsoleApiEdgeController {

    @Autowired
    private EdgeService edgeService;
    /**
      * 查询可用节点列表
      * 通过调用该接口可以获取到可用的节点列表信息。
      * 注意：当前端查询参数types传入时，会被忽略，完全由Console层自身控制查询条件。
      *
      * @param types 查询条件，当前实现中被忽略，由Console层控制
      * @return 返回包含可用节点列表的EdpResultResponse对象
      */

    @ApiOperation(value = "查询可用节点列表")
    @RequestMapping(value = "/region", method = RequestMethod.GET)
    public EdpResultResponse<List<EdgeRegion>> getEdgeRegions(@RequestParam String types) {
        EdpResultResponse<List<EdgeRegion>> edpResultResponse = new EdpResultResponse<>();
        // 这里，前端和 Console 层使用的查询条件所有场景都是一致的，边缘计算提供给 RDS 产品侧的查询参数现在有变动；
        // 所以，暂时忽略前端查询参数 types，完全由 Console 层自身控制！
        List<EdgeRegion> edgeRegions = edgeService.getRegions(Arrays.asList(EdgeService.RDS_EDGE_REGION_TYPES));
        edpResultResponse.setResult(edgeRegions);
        return edpResultResponse;
    }
    /**
      * 查询VPC列表
      * 根据regionId查询VPC列表
      * 
      * @param regionId 区域ID
      * @return 返回查询结果
      */

    @ApiOperation(value = "查询 VPC 列表")
    @RequestMapping(value = "/vpc", method = RequestMethod.GET)
    public EdpResultResponse<List<EdgeVpc>> getVpcsByRegionId(@RequestParam String regionId) {
        EdpResultResponse<List<EdgeVpc>> edpResultResponse = new EdpResultResponse<>();
        List<EdgeVpc> edgeVpcs = edgeService.getVpcsByRegionId(regionId);
        edpResultResponse.setResult(edgeVpcs);
        return edpResultResponse;
    }
    /**
     * 查询VPC详情
     * 根据提供的vpc短ID查询VPC的详细信息
     * 
     * @param vpcId vpc短ID，不是vpcUuid。提供给用户侧的参数统一使用短ID
     * @return 查询VPC的结果
     */

    @ApiOperation(value = "查询 VPC 详情")
    @RequestMapping(value = "/vpc/{vpcId}", method = RequestMethod.GET)
    public EdpResultResponse<EdgeVpc> getVpc(@PathVariable String vpcId) {
        // vpcId 是 vpc 短 ID，不是 vpcUuid。提供给用户侧的参数统一使用短 ID
        EdpResultResponse<EdgeVpc> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(edgeService.getVpc(vpcId));
        return edpResultResponse;
    }
}
