package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.console.order.model.UuidRequest;
import com.baidu.bce.iam.facade.model.bcepass.session.BceSessionContext;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.logic.rds.model.RdsDetailBuilder;
import com.baidu.bce.logic.rds.service.RdsOrderService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import com.baidu.bce.plat.webframework.authentication.service.SessionService;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/rds/order")
public class ConsoleApiOrderController {

    @Autowired
    private RdsOrderService rdsOrderService;

    @Autowired
    private LogicRdsClientFactory rdsClientFactory;
    /**
      * 查询创建订单参数接口
      * 用于接入购物车，查询创建订单所需参数
      * 
      * @param request 创建订单请求参数，包含实例创建模型
      * @return EdpResultResponse，包含创建订单请求对象
      * @throws 异常信息由 LogicRdsExceptionHandler 处理
      */

    @ApiOperation(value = "查询创建订单参数，用于接入购物车")
    @RequestMapping(value = "/getCreateOrderParam", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL, RDSConstant.CREATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX, ids = "*")
    public EdpResultResponse<CreateOrderRequest<CreateNewTypeOrderItem>> getCreateOrderParam(
            @RequestBody @Valid BaseCreateOrderRequestVo<InstanceCreateModel> request) {
        try {
            EdpResultResponse<CreateOrderRequest<CreateNewTypeOrderItem>> edpResultResponse = new EdpResultResponse<>();
            edpResultResponse.setResult(rdsOrderService.getCreateOrderParam(request));
            return edpResultResponse;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }
    /**
      * 获取订单详情接口
      * 该接口通过接收一个包含uuid的请求体，调用order service获取订单详情，并返回结果。
      * 同时，该接口还兼容从iamService中获取stsCredential，并设置到会话上下文中。
      * 
      * @param request 包含uuid的请求体
      * @return 订单详情结果
      */

    @RequestMapping(value = "/detail", method = RequestMethod.POST, produces = {"application/json"})
    @ResponseBody
    public EdpResultResponse<Order> detail(@RequestBody UuidRequest request) {
        // 兼容order service从iamService中获取stsCredential
        StsCredential stsCredential = rdsClientFactory.getUserStsAccessKey();
        com.baidu.bce.iam.facade.model.sts.StsCredential iamStsCredential =
                new com.baidu.bce.iam.facade.model.sts.StsCredential();
        iamStsCredential.setAccessKeyId(stsCredential.getAccessKeyId());
        iamStsCredential.setSecretAccessKey(stsCredential.getSecretAccessKey());
        iamStsCredential.setSessionToken(stsCredential.getSessionToken());
        iamStsCredential.setUserId(stsCredential.getUserId());
        iamStsCredential.setRoleId(stsCredential.getRoleId());
        iamStsCredential.setExpiration(stsCredential.getExpiration());
        iamStsCredential.setCreateTime(stsCredential.getCreateTime());
        BceSessionContext bceSessionContext = new BceSessionContext();
        bceSessionContext.setStsCredential(iamStsCredential);
        SessionService.setSessionContext(bceSessionContext);

        Order order = new RdsDetailBuilder(request.getUuid()).build();

        return new EdpResultResponse<Order>().withResult(order);
    }

}
