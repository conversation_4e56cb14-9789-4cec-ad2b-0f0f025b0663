package com.baidu.bce.logic.rds.model;

import com.baidu.bce.internalsdk.rds.model.snapshot.Snapshot;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Url {
    private String url;
    private Date downloadExpires;
    private String dataBackupType;
    private List<Snapshot.MultiDownloads> multiDownloadUrl;


    @Override
    public String toString() {
        return "Url{"
                + "url='" + url + '\''
                + ", downloadExpires="
                + downloadExpires + '}';
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getDownloadExpires() {
        return downloadExpires;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setDownloadExpires(Date downloadExpires) {
        this.downloadExpires = downloadExpires;
    }

    public String getDataBackupType() {
        return dataBackupType;
    }

    public void setDataBackupType(String dataBackupType) {
        this.dataBackupType = dataBackupType;
    }

    public List<Snapshot.MultiDownloads> getMultiDownloadUrl() {
        return multiDownloadUrl;
    }

    public void setMultiDownloadUrl(List<Snapshot.MultiDownloads> multiDownloadUrl) {
        this.multiDownloadUrl = multiDownloadUrl;
    }

    public Url url(final String url) {
        this.url = url;
        return this;
    }

    public Url expire(final Date expire) {
        this.downloadExpires = expire;
        return this;
    }

    public Url dataBackupType(final String dataBackupType) {
        this.dataBackupType = dataBackupType;
        return this;
    }

    public Url multiDownloadUrl(final List<Snapshot.MultiDownloads> multiDownloadUrl) {
        this.multiDownloadUrl = multiDownloadUrl;
        return this;
    }

}
