package com.baidu.bce.logic.rds.controller.group;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.rds.model.group.CreateGroupRequest;
import com.baidu.bce.internalsdk.rds.model.group.DashboardGroupListRequest;
import com.baidu.bce.internalsdk.rds.model.group.FollowerIdRequest;
import com.baidu.bce.internalsdk.rds.model.group.GroupInfo;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupDetailResponse;
import com.baidu.bce.internalsdk.rds.model.group.ListFilter;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.group.InstanceGroupService;
import com.baidu.bce.logic.rds.service.model.group.GroupListRequest;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/rds/group")
public class ConsoleApiGroupController {

    private static final Logger LOGGER = LoggerFactory.getLogger(GroupAccountController.class);

    @Autowired
    private InstanceGroupService instanceGroupService;
    /**
     * 获取可用实例组列表
     * 实例组: 创建热活组实例组时的可用列表
     * 
     * @param azone 可用区，用于筛选实例组
     * @return EdpResultResponse<List<Instance>> 返回可用实例组列表
     */

    @ApiOperation(value = "实例组: 创建热活组实例组时的可用列表")
    @RequestMapping(value = "/available/list", method = RequestMethod.GET)
    @ResponseBody
    public EdpResultResponse<List<Instance>> availableList(
            @RequestParam(required = false, defaultValue = "") String azone,
            @RequestParam(required = false, defaultValue = "MySQL") String engine) {
        EdpResultResponse<List<Instance>> response = new EdpResultResponse<>();
        response.setResult(instanceGroupService.availableList(azone, engine));
        return response;
    }
    /**
     * 实例组详情
     * 获取实例组的详细信息
     * 
     * @param params 请求参数，包含groupId
     * @return EdpResultResponse<GroupInfo> 返回实例组的详细信息
     */

    @ApiOperation(value = "实例组: 实例组详情")
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<GroupInfo> detail(@RequestBody Map<String, String> params) {
        EdpResultResponse<GroupInfo> groupDetail = new EdpResultResponse<GroupInfo>();
        InstanceGroupDetailResponse res = instanceGroupService.detail(params.get("groupId"));
        if (res != null) {
            groupDetail.setResult(res.getGroup());
        }
        return groupDetail;
    }
    /**
      * 变更主角色
      * 实例组: 变更主角色，通过传入groupId和leaderId来变更实例组的主角色
      * 
      * @param params 包含"groupId"和"leaderId"的Map对象
      * @return EdpResultResponse<Boolean> 变更结果
      */

    @RequestMapping(value = "/change_leader", method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 变更主角色")
    public EdpResultResponse<Boolean> changeLeader(@RequestBody Map<String, String> params) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();

        instanceGroupService.changeLeader(params.get("groupId"), params.get("leaderId"));

        resultResponse.setResult(true);
        return resultResponse;
    }

    public static Map<String, String> filter2String(List<ListFilter> filterList) {
        Map<String, String> filterMap = null;
        if (filterList != null && filterList.size() > 0) {
            filterMap = new HashMap<>();
            for (ListFilter filter : filterList) {
                if ("tag".equalsIgnoreCase(filter.getKeywordType()) && StringUtils.isNotBlank(filter
                        .getSubKeywordType())) {
                    filterMap.put("tag", filter.getSubKeywordType() + "__" + filter.getKeyword());
                    continue;
                }
                if ("status".equalsIgnoreCase(filter.getKeywordType())) {
                    filter.setKeywordType("instanceStatus");
                }
                filterMap.put(filter.getKeywordType(), filter.getKeyword());
            }
        }
        return filterMap;
    }
    /**
      * 列表查询接口
      * 根据请求参数查询实例组信息列表
      * 
      * @param request 请求参数，包含分页、排序、过滤条件等信息
      * @return EdpPageResultResponse<GroupInfo> 返回查询结果，包含分页信息和实例组信息列表
      */

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    public EdpPageResultResponse<GroupInfo> list(@RequestBody DashboardGroupListRequest request) {
        int daysToExpiration = "all".equalsIgnoreCase(request.getShowMode()) ? -1 : 7;
        if (request.getOrder().isEmpty()) {
            request.setOrder("desc");
        }
        if (request.getOrderBy().isEmpty()) {
            request.setOrderBy("instanceCreateTime");
        }
        Map<String, String> filterMap = filter2String(request.getFilters());

        GroupListRequest listRequest = new GroupListRequest(daysToExpiration, request.getOrder(), request.getOrderBy(),
                request.getPageNo(), request.getPageSize());
        listRequest.setFilterMap(filterMap);
        LogicPageResultResponse<GroupInfo> result  = instanceGroupService.list(listRequest);

        EdpPageResultResponse<GroupInfo> edpPageResultResponse = new EdpPageResultResponse<>();
        edpPageResultResponse.setPage(new EdpPageResultResponse.Page<GroupInfo>());
        edpPageResultResponse.getPage().setOrder(result.getOrder());
        edpPageResultResponse.getPage().setOrderBy(result.getOrderBy());
        edpPageResultResponse.getPage().setPageNo(result.getPageNo());
        edpPageResultResponse.getPage().setPageSize(result.getPageSize());
        edpPageResultResponse.getPage().setResult(result.getResult());
        edpPageResultResponse.getPage().setTotalCount(result.getTotalCount());

        return edpPageResultResponse;
    }

    /**
     * 检查GTID接口
     * 该接口用于检查GTID是否存在或有效
     * 
     * @param request 请求体，包含instanceId信息
     * @return 返回检查结果，返回值为true表示GTID存在或有效
     */
    // 实例组前置检查（GTID检查）
    @RequestMapping(value = "/check_gtid", method = RequestMethod.POST)
    public EdpResultResponse<String> checkGtid(@RequestBody @Valid InstanceIdRequest request) {
        instanceGroupService.checkGtid(request.getInstanceId());
        return new EdpResultResponse<String>().withResult(Boolean.TRUE.toString());
    }

    /**
     * 检查Ping接口
     * 用于检查两个实例之间的连通性
     * 
     * @param params 包含"sourceId"和"targetId"的请求参数
     * @return 返回检查结果，成功返回true字符串
     */
    // 实例组前置检查（实例连通性检查）
    @RequestMapping(value = "/check_ping", method = RequestMethod.POST)
    public EdpResultResponse<String> checkPing(@RequestBody Map<String, String> params) {
        instanceGroupService.checkPing(params.get("sourceId"), params.get("targetId"));
        return new EdpResultResponse<String>().withResult(Boolean.TRUE.toString());
    }

    /**
     * 检查数据接口
     * 接收一个包含实例ID的请求体，校验数据是否存在
     * 
     * @param request 包含实例ID的请求体
     * @return 校验结果，存在返回true字符串形式，否则返回false字符串形式
     */
    // 实例组前置检查（数据检查）
    @RequestMapping(value = "/check_data", method = RequestMethod.POST)
    public EdpResultResponse<String> checkData(@RequestBody @Valid InstanceIdRequest request) {
        instanceGroupService.checkData(request.getInstanceId());
        return new EdpResultResponse<String>().withResult(Boolean.TRUE.toString());
    }
    /**
      * 创建实例组接口
      * 通过POST请求创建新的实例组
      * 
      * @param request 创建实例组时需要的参数和信息
      * @return 操作结果
      */

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> create(@RequestBody CreateGroupRequest request) {
        instanceGroupService.createGroup(request);
        return new EdpResultResponse<>();
    }
    /**
     * 加入实例组接口
     * 处理用户加入实例组的请求
     * 
     * @param request 用户加入群组请求参数，包含必要的用户信息和实例组信息
     * @return EdpResultResponse<Boolean> 操作结果，包含是否成功加入实例组的信息
     */

    @RequestMapping(value = "/follower", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> follower(@RequestBody @Valid FollowerIdRequest request) {
        instanceGroupService.joinGroup(request);
        return new EdpResultResponse<>();
    }
    /**
     * 更改名称
     * 根据传入的groupId和name，调用服务更改实例组的名称
     * 
     * @param params 包含groupId和name的键值对
     * @return 操作结果
     */

    @RequestMapping(value = "/change_name", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> changeName(@RequestBody Map<String, String> params) {
        instanceGroupService.changeName(params.get("groupId"),  params.get("name"));
        return new EdpResultResponse<>();
    }
    /**
      * 删除实例组接口
      * 根据传入的groupId删除对应的实例组
      * 
      * @param params 包含"groupId"键值对的参数集
      * @return EdpResultResponse对象，删除成功时返回true，否则返回false
      */

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> delete(@RequestBody Map<String, String> params) {
        instanceGroupService.delete(params.get("groupId"));
        return new EdpResultResponse<>();
    }
    /**
     * 注销接口
     * 用于处理注销请求，根据传入的groupId和instanceId进行注销操作
     * 
     * @param params 包含注销所需参数，其中groupId为实例组ID，instanceId为实例ID
     * @return EdpResultResponse<Boolean> 返回操作结果
     */

    @RequestMapping(value = "/sign_out", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> signOut(@RequestBody Map<String, String> params) {
        instanceGroupService.signOut(params.get("groupId"), params.get("instanceId"));
        return new EdpResultResponse<>();
    }

}
