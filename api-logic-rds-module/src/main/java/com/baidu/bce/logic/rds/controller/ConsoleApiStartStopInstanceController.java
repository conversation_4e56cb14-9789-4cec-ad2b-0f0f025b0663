package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/rds/instance")
public class ConsoleApiStartStopInstanceController {

    @Autowired
    private InstanceService instanceService;
    /**
     * 关闭实例
     * 该函数用于关闭数据库实例
     * 
     * @param request 包含实例ID的请求体
     * @return EdpResultResponse 关闭实例的结果
     */

    @ApiOperation(value = "关闭实例")
    @RequestMapping(value = "/suspend", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse suspend(
            @IdPermission @RequestBody @Valid InstanceIdRequest request) {
        try {
            EdpResultResponse response = new EdpResultResponse();
            instanceService.suspend(request.getInstanceId());
            return response;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }
    /**
      * 启动实例
      * 通过POST请求启动指定的RDS实例
      * 
      * @param request 包含实例ID的请求体
      * @return EdpResultResponse 启动结果
      * @throws Exception 如果启动过程中出现异常
      */

    @ApiOperation(value = "启动实例")
    @RequestMapping(value = "/start", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse start(
            @IdPermission @RequestBody @Valid InstanceIdRequest request) {
        try {
            EdpResultResponse response = new EdpResultResponse();
            instanceService.start(request.getInstanceId());
            return response;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }
}
