package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.strategy.ExchangeStrategyResponse;
import com.baidu.bce.internalsdk.rds.model.strategy.TaskStatusResponses;
import com.baidu.bce.internalsdk.rds.model.strategy.UpdateExchangeStrategyRequest;
import com.baidu.bce.logic.rds.service.SwitchStrategyService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/rds/instance")
public class OpenApiSwitchStrategyController {

    @Autowired
    private SwitchStrategyService strategyService;

    /**
     * 查询切换策略接口
     * 根据instanceId查询切换策略详情
     * 
     * @param instanceId 实例ID
     * @return 切换策略详情
     */
    @ApiOperation("查询切换策略")
    @RequestMapping(value = "/{instanceId}/exchangeStrategy", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS},
            permission = {RDSConstant.READ}, type = RDSConstant.ID_PREFIX)
    public ExchangeStrategyResponse detail(@IdPermission @PathVariable @IdMapper String instanceId) {

        return strategyService.detail(instanceId);
    }
    /**
     * 更新切换策略
     * 更新指定实例的切换策略
     * 
     * @param instanceId 实例ID
     * @param request 更新切换策略请求体
     */

    @ApiOperation("更新切换策略")
    @RequestMapping(value = "/{instanceId}/exchangeStrategy", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS},
            permission = {RDSConstant.OPERATE}, type = RDSConstant.ID_PREFIX)
    public void updateExchangeStrategy(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @RequestBody UpdateExchangeStrategyRequest request) {

        strategyService.updateExchangeStrategy(instanceId, request);
    }

    /**
     * 切换前置检查
     * 进行切换前置检查的接口
     * 
     * @param instanceId 实例ID，用于指定要检查的实例
     */
    @ApiOperation("切换前置检查")
    @RequestMapping(value = "/{instanceId}", method = RequestMethod.POST, params = "switchoverPrecheck")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS},
            permission = {RDSConstant.OPERATE}, type = RDSConstant.ID_PREFIX)
    public void preCheck(@IdPermission @PathVariable @IdMapper String instanceId) {

        strategyService.strategyService(instanceId);

    }
    /**
     * 查询任务状态
     * 根据任务ID查询任务状态信息
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     * @Deprecated 该接口已不推荐使用
     */

    @ApiOperation("查询任务状态")
    @RequestMapping(value = "/precheck/{taskId}", method = RequestMethod.GET)
    @Deprecated
    public TaskStatusResponses taskStatus(@PathVariable Integer taskId) {

        TaskStatusResponses responses = new TaskStatusResponses();

        responses = strategyService.taskStatus(taskId);

        return responses;
    }
}