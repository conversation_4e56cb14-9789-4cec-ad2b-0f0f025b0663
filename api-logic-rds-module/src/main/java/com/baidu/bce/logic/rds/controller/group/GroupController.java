package com.baidu.bce.logic.rds.controller.group;

import com.baidu.bce.internalsdk.rds.model.group.CreateGroupRequest;
import com.baidu.bce.internalsdk.rds.model.group.FollowerIdRequest;
import com.baidu.bce.internalsdk.rds.model.group.GroupForceChangeLeaderRequest;
import com.baidu.bce.internalsdk.rds.model.group.GroupForceChangeLeaderResponse;
import com.baidu.bce.internalsdk.rds.model.group.GroupInfo;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupDetailResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.utils.JsonConvertUtil;
import com.baidu.bce.logic.rds.controller.InstanceController;
import com.baidu.bce.logic.rds.model.ResultResponse;
import com.baidu.bce.logic.rds.model.group.CheckPingRequest;
import com.baidu.bce.logic.rds.model.group.InstanceIdRequest;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.group.InstanceGroupService;
import com.baidu.bce.logic.rds.service.model.group.GroupListRequest;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Map;

/**
 * Created by shiyuxin on 19/5/20.
 */
@RestController
@RequestMapping("/v1/rds/group")
public class GroupController {

    private static final Logger LOGGER = LoggerFactory.getLogger(GroupAccountController.class);

    @Autowired
    private InstanceGroupService instanceGroupService;

    /**
     * 实例组：通过pageNo方式分页
     * 实例组：通过pageNo方式实现分页
     *
     * @param daysToExpiration 距离实例组过期剩余天数，默认为-1，表示不限制
     * @param order 排序方式，默认为空
     * @param orderBy 排序字段，默认为空
     * @param pageNo 页码，默认为1
     * @param pageSize 每页显示数量，默认为1000
     * @param filterMapStr 过滤条件，默认为空
     * @param machineType 机器类型，默认为空
     * @return 分页后的实例组信息
     */
    @RequestMapping(method = RequestMethod.GET, params = {"manner=page"})
    @ApiOperation(value = "实例组: 通过pageNo方式分页")
    public LogicPageResultResponse<GroupInfo> list(
            @RequestParam(required = false, defaultValue = "-1") Integer daysToExpiration,
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String filterMapStr,
            @RequestParam(required = false, defaultValue = "") String machineType) {

        GroupListRequest listRequest = new GroupListRequest(daysToExpiration, order, orderBy, pageNo, pageSize);

        if (StringUtils.isNotEmpty(filterMapStr)) {
            filterMapStr = StringEscapeUtils.unescapeHtml(filterMapStr).replaceAll("\\\\", "\\\\\\\\");
            LOGGER.info("list instance[page], after unescapeHtml filterMaperStr is : {}", filterMapStr);
            InstanceController.FilterMap filterMap =
                    JsonConvertUtil.fromJSON(filterMapStr, InstanceController.FilterMap.class);
            listRequest.setFilterMap(filterMap);

        }

        LogicPageResultResponse<GroupInfo> result  = instanceGroupService.list(listRequest);

        return result;
    }

    /**
      * 实例组详情
      * 根据实例组ID获取实例组详情
      * 
      * @param groupId 实例组ID
      * @return 实例组详情
      */
    @RequestMapping(value = "/{groupId}", method = RequestMethod.GET)
    @ApiOperation(value = "实例组: 实例组详情")
    public InstanceGroupDetailResponse detail(@PathVariable String groupId) {
        return instanceGroupService.detail(groupId);
    }

    /**
     * 实例组前置检查（GTID检查）
     * 对实例组进行前置检查，主要检查GTID是否有效
     * 
     * @param request 包含实例ID的请求体
     * @return 返回检查结果
     */
    @RequestMapping(value = "/checkGtid", method = RequestMethod.POST)
    @ApiOperation(value = "实例组:: 实例组前置检查（GTID检查）")
    public ResultResponse<Integer> checkGtid(@RequestBody @Valid InstanceIdRequest request) {

        ResultResponse<Integer> result = new ResultResponse<Integer>();

        try {
            instanceGroupService.checkGtid(request.getInstanceId());
        } catch (Exception e) {
            LOGGER.error("get instance detail error", e);
            LogicRdsExceptionHandler.handle(e);
        }

        result.setResult(200);

        return result;
    }

    /**
     * 实例组前置检查
     * 实例组前置检查（实例连通性检查）
     * 
     * @param request 包含sourceId和targetId的请求体
     * @return 返回检查结果，200表示成功
     */
    @RequestMapping(value = "/checkPing", method = RequestMethod.POST)
    @ApiOperation(value = "实例组:: 实例组前置检查（实例连通性检查）")
    public ResultResponse<Integer> checkPing(@RequestBody @Valid CheckPingRequest request) {

        ResultResponse<Integer> result = new ResultResponse<Integer>();

        instanceGroupService.checkPing(request.getSourceId(), request.getTargetId());
        result.setResult(200);

        return result;
    }

    /**
      * 实例组前置检查（数据检查）
      * 该函数用于进行实例组的前置数据检查，检查实例组数据是否满足要求
      * 
      * @param request 包含实例组ID的请求体
      * @return 检查结果
      */
    @RequestMapping(value = "/checkData", method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 实例组前置检查（数据检查）")
    public ResultResponse<Integer> checkData(@RequestBody @Valid InstanceIdRequest request) {
        ResultResponse<Integer> result = new ResultResponse<Integer>();

        instanceGroupService.checkData(request.getInstanceId());
        result.setResult(200);

        return result;
    }


    /**
     * 实例组：创建实例组
     * 实例组：创建实例组
     *
     * @param request 创建实例组的请求参数
     * @return 返回创建实例组的结果，包括状态码等
     */
    @RequestMapping(method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 创建实例组")
    public ResultResponse<Integer> create(@RequestBody @Valid CreateGroupRequest request) {

        ResultResponse<Integer> result = new ResultResponse<Integer>();

        instanceGroupService.createGroup(request);
        result.setResult(200);

        return result;
    }
    /**
     * 加入实例组
     * 该函数用于处理HTTP POST请求，允许用户加入指定的实例组
     * 
     * @param groupId 路径变量，表示要加入的实例组的ID
     * @param request 请求体，包含要加入实例组的用户信息
     * @return 返回操作结果，包括状态码等
     */

    @RequestMapping(value = "/{groupId}/instance", method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 加入某个实例组")
    public ResultResponse<Integer> follower(@PathVariable String groupId,
                                                @RequestBody FollowerIdRequest request) {

        ResultResponse<Integer> result = new ResultResponse<Integer>();

        instanceGroupService.joinGroup(request);
        result.setResult(200);

        return result;
    }
    /**
      * 修改热活实例组的名称
      * 修改指定热活实例组的名称。
      *
      * @param groupId 实例组ID
      * @param params 包含新名称的参数集合，其中key为"name"
      */

    @RequestMapping(value = "/{groupId}/name", method = RequestMethod.POST)
    @ApiOperation(value = "实例组: 修改热活实例组的名称")
    public void changeName(@PathVariable String groupId,
                                                @RequestBody Map<String, String> params) {
        instanceGroupService.changeName(groupId,  params.get("name"));
    }
    /**
      * 实例组: 热活实例组的删除
      * 删除指定groupId的实例组
      * 
      * @param groupId 实例组ID
      */

    @RequestMapping(value = "/{groupId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "实例组: 热活实例组的删除")
    public void delete(@PathVariable String groupId) {
        instanceGroupService.delete(groupId);
    }
    /**
      * 实例组: 主角色变更
      * 变更实例组的主角色
      * 
      * @param groupId 实例组的ID
      * @param params 包含变更信息的参数，其中必须包含新主角色的ID，key为"leaderId"
      */

    @RequestMapping(value = "/{groupId}/instance", method = RequestMethod.PUT)
    @ApiOperation(value = "实例组: 主角色变更")
    public void changeLeader(@PathVariable String groupId,
                             @RequestBody Map<String, String> params) {
        instanceGroupService.changeLeader(groupId, params.get("leaderId"));
    }

    /**
     * 退出热活实例组
     * 该函数用于处理退出热活实例组的请求
     * 
     * @param groupId 实例组的ID
     * @param instanceId 实例的ID
     */
    @RequestMapping(value = "/{groupId}/instance/{instanceId}", method = RequestMethod.DELETE)
    @ApiOperation(value = "实例组: 退出热活实例组")
    public void signOut(@PathVariable String groupId,
                                                    @PathVariable String instanceId) {
        instanceGroupService.signOut(groupId, instanceId);
    }
    /**
      * 强切实例组领导节点
      * 通过PUT请求，根据groupId和changeLeaderRequest参数，强切实例组领导节点
      * 
      * @param groupId 实例组ID
      * @param changeLeaderRequest 强切请求参数
      * @return 强切结果
      */

    @RequestMapping(value = "/{groupId}/forceChange", method = RequestMethod.PUT)
    @ApiOperation(value = "实例组: 强切")
    public GroupForceChangeLeaderResponse forceChangeLeader(@PathVariable String groupId,
                                               @RequestBody GroupForceChangeLeaderRequest changeLeaderRequest) {
        GroupForceChangeLeaderResponse response = null;
        try {
            response = instanceGroupService.forceChangeLeader(groupId, changeLeaderRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

}
