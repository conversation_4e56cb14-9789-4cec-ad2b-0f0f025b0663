package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.SqlFilterAllowedResponse;
import com.baidu.bce.internalsdk.rds.model.SqlFilterList;
import com.baidu.bce.internalsdk.rds.model.SqlFilterRequest;
import com.baidu.bce.internalsdk.rds.model.SqlFilterResponse;
import com.baidu.bce.logic.rds.service.SqlFilterService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * SQL限流 相关 API
 *
 * <AUTHOR>
 * @since 5/5/23
 */
@RestController
@RequestMapping("/v1/instance/{instanceId}/sqlfilter")
public class OpenApiSqlFilterController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiSqlFilterController.class);

    @Autowired
    private SqlFilterService sqlFilterService;
    
    /**
     * 获取实例限流规则列表
     * 根据实例ID获取对应的SQL过滤规则列表
     * 
     * @param instanceId 实例ID
     * @return SQL过滤规则列表
     */    
    @ApiOperation(value = "获取实例限流规则列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlFilterList sqlFilterList(@IdPermission @PathVariable @IdMapper String instanceId) {
        LOGGER.debug("get sqlfilter list.");
        SqlFilterList response = sqlFilterService.sqlFilterList(instanceId);
        return response;
    }
    /**
      * 获取限流规则详情
      * 通过指定的实例ID和限流规则ID，获取对应的限流规则详情
      * 
      * @param instanceId 实例ID
      * @param sqlFilterId 限流规则ID
      * @return 限流规则详情
      */

    @ApiOperation(value = "获取某个限流规则详情")
    @RequestMapping(value = "/{sqlFilterId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlFilterResponse sqlFilterDetail(@IdPermission @PathVariable @IdMapper String instanceId,
                                             @PathVariable String sqlFilterId) {
        LOGGER.debug("get sqlfilter detail.");
        SqlFilterResponse response = sqlFilterService.sqlFilterDetail(instanceId, sqlFilterId);
        return response;
    }
    
    /**
      * 添加一条限流规则
      * 添加一条限流规则到系统中，用于控制数据库的访问频率
      * 
      * @param instanceId 实例ID，用于标识要添加限流规则的数据库实例
      * @param request 限流规则请求体，包含限流规则的具体信息
      */
    @ApiOperation(value = "添加一条限流规则")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void addSqlFilter(@IdPermission @PathVariable @IdMapper String instanceId,
                             @Valid @RequestBody SqlFilterRequest request) {
        LOGGER.debug("get sqlfilter add.");
        sqlFilterService.addSqlFilter(instanceId, request);
    }
    /**
     * 更新限流规则
     * 根据提供的instanceId, sqlFilterId和请求体SqlFilterRequest，更新一条限流规则
     * 
     * @param instanceId 实例ID
     * @param sqlFilterId 限流规则ID
     * @param request 更新限流规则的请求体
     */

    @ApiOperation(value = "更新一条限流规则")
    @RequestMapping(value = "/{sqlFilterId}", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateSqlFilter(@IdPermission @PathVariable @IdMapper String instanceId,
                                @PathVariable String sqlFilterId, @Valid @RequestBody SqlFilterRequest request) {
        LOGGER.debug("get sqlfilter update.");
        sqlFilterService.updateSqlFilter(instanceId, sqlFilterId , request);
    }
    /**
      * 开启或关闭某个限流规则
      * 根据实例ID和限流规则ID开启或关闭某个限流规则
      * 
      * @param instanceId 实例ID
      * @param sqlFilterId 限流规则ID
      * @param request 限流规则请求体
      */

    @ApiOperation(value = "开启|关闭某个限流规则")
    @RequestMapping(value = "/{sqlFilterId}", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void actionSqlFilter(@IdPermission @PathVariable @IdMapper String instanceId,
                                @PathVariable String sqlFilterId, @Valid @RequestBody SqlFilterRequest request) {
        LOGGER.debug("get sqlfilter action.");
        sqlFilterService.actionSqlFilter(instanceId, sqlFilterId , request);
    }
    /**
      * 删除限流规则
      * 删除某个限流规则
      *
      * @param instanceId 实例ID
      * @param sqlFilterId 限流规则ID
      */

    @ApiOperation(value = "删除某个限流规则")
    @RequestMapping(value = "/{sqlFilterId}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void deleteSqlFilter(@IdPermission @PathVariable @IdMapper String instanceId,
                                @PathVariable String sqlFilterId) {
        LOGGER.debug("get sqlfilter delete.");
        sqlFilterService.deleteSqlFilter(instanceId, sqlFilterId);
    }
    /**
     * 实例是否支持限流
     * 查询实例是否支持SQL过滤功能
     * 
     * @param instanceId 实例ID
     * @return SqlFilterAllowedResponse 实例是否支持SQL过滤的响应
     */

    @ApiOperation(value = "实例是否支持限流")
    @RequestMapping(value = "/allowed", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SqlFilterAllowedResponse allowedSqlFilter(@IdPermission @PathVariable @IdMapper String instanceId) {
        SqlFilterAllowedResponse response = sqlFilterService.allowedSqlFilter(instanceId);
        return response;
    }

}
