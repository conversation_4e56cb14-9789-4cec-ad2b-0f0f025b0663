package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ApplyDetail;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ApplyInstance;
import com.baidu.bce.internalsdk.rds.model.paratemplate.CreateParaTemplateResponse;
import com.baidu.bce.internalsdk.rds.model.paratemplate.DatebaseParametersRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.DuplicateTempRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateApplyHistory;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateApplyInstanceRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateApplyRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateCompare;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateCompareRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateCreateRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateIdRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateModel;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplatePage;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateResponse;
import com.baidu.bce.logic.rds.service.ParaTemplateService;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/rds/paraTemplate")
public class ConsoleApiParaTemplateController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConsoleApiParaTemplateController.class);

    @Autowired
    ParaTemplateService paraTemplateService;

    /**
      * 模板列表
      * 查询参数模板的列表信息
      * 
      * @param paraTemplateRequest 参数模板请求对象，包含分页信息和查询条件
      * @return 返回参数模板列表的响应对象
      */                                                
    @RequestMapping(value = "", method = RequestMethod.POST)
    public EdpResultResponse<ParaTemplatePage<ParaTemplateModel>> listParaTemplate(@RequestBody(required = false)
                                                 ParaTemplateRequest paraTemplateRequest) {
        ParaTemplatePage<ParaTemplateModel> paraTemplatePage =
                paraTemplateService.listParaTemplate(paraTemplateRequest.getPageNo(),
                        paraTemplateRequest.getPageSize(),
                        paraTemplateRequest.getDbType(), paraTemplateRequest.getDbVersion(),
                        paraTemplateRequest.getType(), paraTemplateRequest.getName(),
                        paraTemplateRequest.getId());
        EdpResultResponse<ParaTemplatePage<ParaTemplateModel>> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(paraTemplatePage);
        return edpResultResponse;
    }

    /**
      * 创建参数模板
      * POST方法用于添加参数模板
      * 
      * @param paraTemplateCreateRequest 参数模板创建请求体
      * @return EdpResultResponse<Boolean> 添加操作的结果
      */    
    @RequestMapping(value = "/add/template", method = RequestMethod.POST)
    public EdpResultResponse<CreateParaTemplateResponse> addParaTemplate(
            @RequestBody ParaTemplateCreateRequest paraTemplateCreateRequest) {
        EdpResultResponse<CreateParaTemplateResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(paraTemplateService.addParaTemplate(paraTemplateCreateRequest));
        return edpResultResponse;
    }
    /**
      * 比较参数模板
      * 根据提供的模板ID和实例ID，比较参数模板，返回比较结果
      * 
      * @param request 包含模板ID和实例ID的请求体
      * @return 包含比较结果的响应体
      */

    @RequestMapping(value = "/compare", method = RequestMethod.POST)
    public EdpResultResponse<List<ParaTemplateCompare>> compareParaTemplate(@Valid @RequestBody
                                                    ParaTemplateCompareRequest request) {
        List<ParaTemplateCompare> paraTemplateCompareList =
                paraTemplateService.compareParaTemplate(request.getTemplateId(), request.getInstanceId());
        EdpResultResponse<List<ParaTemplateCompare>> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(paraTemplateCompareList);
        return edpResultResponse;
    }
    /**
     * 应用模板接口
     * 根据请求中的模板ID和参数，调用服务层处理并返回处理结果
     * 
     * @param paraTemplateCompareRequest 应用模板请求参数，包含模板ID和其他必要参数
     * @return EdpResultResponse<Map<String, List<ParaTemplateCompare>>> 处理结果，包含状态码、消息和结果数据
     * @throws Throwable 抛出异常，包含可能的运行时异常
     */

    @RequestMapping(value = "/template/apply", method = RequestMethod.POST)
    public EdpResultResponse<Map<String, List<ParaTemplateCompare>>> applyTemplate
            (@Valid @RequestBody ParaTemplateCompareRequest paraTemplateCompareRequest) throws Throwable {
        EdpResultResponse<Map<String, List<ParaTemplateCompare>>> edpResultResponse = new EdpResultResponse<>();
        Map<String, List<ParaTemplateCompare>> paraTemplateCompareList
                = paraTemplateService.applyTemplate(paraTemplateCompareRequest.getTemplateId(),
                paraTemplateCompareRequest);
        edpResultResponse.setResult(paraTemplateCompareList);
        return edpResultResponse;
    }

    /**
      * 模板应用历史列表
      * 根据模板ID、页码、每页数量查询模板应用历史列表，并返回查询结果
      * 
      * @param request 包含模板ID、页码、每页数量的请求参数
      * @return 查询到的模板应用历史列表的响应结果
      */    
    @RequestMapping(value = "/apply/history", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS},
            permission = {RDSConstant.READ}, type = RDSConstant.ID_PREFIX)
    public ParaTemplateResponse listParaTemplateApplyHistory(@Valid @RequestBody ParaTemplateRequest request) {
        ParaTemplatePage<ParaTemplateApplyHistory> paraTemplatePage =
                paraTemplateService.listParaTemplateApplyHistory(request.getTemplateId(), request.getPageNo(),
                        request.getPageSize());
        ParaTemplateResponse response = new ParaTemplateResponse();
        response.setResult(paraTemplatePage);
        return response;
    }

    /**
      * 模板应用详情
      * 通过传入模板应用ID，获取模板应用详情信息
      * 
      * @param request 模板应用请求信息，包含应用ID
      * @return 模板应用详情响应
      */    
    @RequestMapping(value = "/apply/detail", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS},
            permission = {RDSConstant.READ}, type = RDSConstant.ID_PREFIX)
    public ParaTemplateResponse getParaTemplateApplyDetail(@Valid @RequestBody ParaTemplateApplyRequest request) {
        ParaTemplateResponse response = new ParaTemplateResponse();
        List<ApplyDetail> applyDetailList = paraTemplateService.getParaTemplateApplyDetail(request.getApplyId());
        response.setResult(applyDetailList);
        return response;
    }

    /**
      * 模板详情
      * 根据提供的模板ID获取模板详情，并返回模板信息。如果模板的数据库类型为mysql，则将其转换为大写"MySQL"。
      * 
      * @param request 包含模板ID的请求体
      * @return 包含模板详情的响应体
      */    
    @RequestMapping(value = "/template/detail", method = RequestMethod.POST)
    public ParaTemplateResponse getTemplateDetail(@Valid @RequestBody ParaTemplateIdRequest request) {
        ParaTemplateResponse response = new ParaTemplateResponse();
        ParaTemplateModel paraTemplateModel = paraTemplateService.getTemplateDetail(request.getTemplateId());
        if (paraTemplateModel != null && paraTemplateModel.getDbType() != null
                && "mysql".equals(paraTemplateModel.getDbType())) {
            paraTemplateModel.setDbType("MySQL");
        }
        response.setResult(paraTemplateModel);
        return response;
    }

    /**
      * 模板删除
      * 删除指定模板
      *
      * @param paraTemplateIdRequest 包含模板ID的请求体
      * @return 删除操作的响应体
      */    
    @RequestMapping(value = "/delete/template", method = RequestMethod.POST)
    public ParaTemplateResponse deleteTemplate(@Valid @RequestBody ParaTemplateIdRequest paraTemplateIdRequest) {
        paraTemplateService.deleteTemplate(paraTemplateIdRequest.getTemplateId());
        ParaTemplateResponse response = new ParaTemplateResponse();
        return response;
    }

    /**
      * 模板复制
      * 复制指定的模板。
      * 
      * @param request 模板复制请求参数，包含需要复制的模板信息
      * @return 返回复制结果，EdpResultResponse封装了复制操作是否成功的信息
      */    
    @RequestMapping(value = "/duplicate/template", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> duplicateTemplate(@Valid @RequestBody DuplicateTempRequest request) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        paraTemplateService.duplicateTemplate(request);
        resultResponse.setResult(true);
        return resultResponse;
    }

    /**
      * 模板修改
      * 根据提供的模板ID，名称和更新请求列表来更新模板
      * 
      * @param request 包含模板ID，名称和更新请求列表的请求体
      * @return 更新模板后的响应
      */    
    @RequestMapping(value = "/update/template", method = RequestMethod.POST)
    public ParaTemplateResponse updateTemplate(@Valid @RequestBody ParaTemplateIdRequest request) {
        paraTemplateService.updateTemplate(request.getTemplateId(), request.getName(), request.getUpdateRequestList());
        ParaTemplateResponse response = new ParaTemplateResponse();
        return response;
    }
    /**
     * 获取申请实例列表
     * 根据请求参数获取申请实例列表，并返回结果
     * 
     * @param request 请求参数，包含引擎和引擎版本
     * @return 返回申请实例列表的响应对象
     */

    @RequestMapping(value = "/apply/instanceList", method = RequestMethod.POST)
    public ParaTemplateResponse listApplyInstance(@Valid @RequestBody ParaTemplateApplyInstanceRequest request) {
        ParaTemplateResponse response = new ParaTemplateResponse();
        List<ApplyInstance> applyInstanceList = paraTemplateService.listApplyInstance(request.getEngine(),
                request.getEngineVersion(), request.getInstanceType());
        response.setResult(applyInstanceList);
        return response;
    }
    /**
      * 获取数据库参数列表
      * 根据请求中的数据库类型、数据库版本和模板ID，查询并返回对应的数据库参数列表
      * 
      * @param request 包含数据库类型、数据库版本和模板ID的请求体
      * @return 数据库参数列表的响应
      */

    @RequestMapping(value = "/list/datebaseParameters", method = RequestMethod.POST)
    public ParaTemplateResponse listDatebaseParameters(@Valid @RequestBody DatebaseParametersRequest request) {
        ParaTemplateResponse response = new ParaTemplateResponse();
        List<LinkedHashMap> paraTemplateCompareList =
                paraTemplateService.listDatebaseParameters(request.getDbType(), request.getDbVersion(),
                        request.getTemplateId());
        response.setResult(paraTemplateCompareList);
        return response;
    }

}
