package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.account.*;
import com.baidu.bce.logic.rds.service.AccountService;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.model.IsExistResponse;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * Created by luping03 on 17/10/10.
 */
@RestController
@RequestMapping("/v1/rds/instances/{instanceId}/accounts")
public class AccountController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private AccountService accountService;

    @Autowired
    private InstanceService instanceService;
    
    /**
     * 获取帐号列表
     * 根据instanceId获取帐号列表，支持通过from参数筛选
     * 
     * @param instanceId 实例ID
     * @param from 筛选条件
     * @return 帐号列表响应
     */
    @ApiOperation(value = "获取帐号列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public AccountListResponse getList(@IdPermission @IdMapper @PathVariable String instanceId,
                                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get account list. instanceId is {}", instanceId);
        AccountListResponse response = null;
        try {
            response = accountService.list(instanceId, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    
    /**
     * 创建帐号
     * 用于创建新的帐号
     * 
     * @param instanceId 实例ID
     * @param request 帐号信息
     * @param from 来源
     * @param httpServletRequest HttpServlet请求
     */
    @ApiOperation(value = "创建帐号")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void create(@IdPermission @IdMapper @PathVariable String instanceId,
                       @Valid @RequestBody Account request,
                       @RequestParam(required = false, defaultValue = "") String from,
                       HttpServletRequest httpServletRequest) {
        LOGGER.debug("create account. instanceId is {}", instanceId);
        AccountListResponse response = null;
        try {
            String ak = httpServletRequest.getHeader(RDSConstant.X_BCE_ACCESS_KEY);
            accountService.create(instanceId, request, from, ak);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 获取帐号详情
      * 根据实例ID和帐号名称获取帐号详情
      *
      * @param instanceId 实例ID
      * @param accountName 帐号名称
      * @param from 来源
      * @return 帐号详情响应对象
      */

    @ApiOperation(value = "帐号详情")
    @RequestMapping(value = "/{accountName}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public AccountGetResponse detail(@IdPermission @IdMapper @PathVariable String instanceId,
                                     @PathVariable String accountName,
                                     @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("create account. instanceId: {}, accountName: {}", instanceId, accountName);
        AccountGetResponse response = null;
        try {
            response = accountService.detail(instanceId, accountName, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 修改描述信息
      * 根据提供的accountName和instanceId更新账户的描述信息
      * 
      * @param instanceId 实例ID，用于标识需要操作的账户
      * @param accountName 账户名称，用于标识需要操作的账户
      * @param remarkRequest 账户描述信息更新请求体，包含更新的描述信息
      * @param from 请求来源，默认为空字符串
      */

    @ApiOperation(value = "修改描述信息")
    @RequestMapping(value = "/{accountName}/remark", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updateRemark(@IdPermission @PathVariable String instanceId,
                             @PathVariable String accountName,
                             @RequestBody @Valid AccountUpdateRemarkRequest remarkRequest,
                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update remark. instanceId: {}, accountName: {}", instanceId, accountName);
        try {
            accountService.updateRemark(instanceId, accountName, remarkRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
      * 修改密码
      * 修改用户密码接口
      * 
      * @param instanceId 实例ID
      * @param accountName 用户名
      * @param pwRequest 密码修改请求体
      * @param from 请求来源
      */
    @ApiOperation(value = "修改密码")
    @RequestMapping(value = "/{accountName}/password", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePW(@IdPermission @PathVariable String instanceId,
                         @PathVariable String accountName,
                         @RequestBody @Valid AccountUpdatePasswordRequest pwRequest,
                         @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update password. instanceId: {}, accountName: {}", instanceId, accountName);
        try {
            accountService.updatePW(instanceId, accountName, pwRequest, "", null);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 修改权限
      * 根据实例ID和账户名修改账户的权限
      * 
      * @param instanceId 实例ID
      * @param accountName 账户名
      * @param request 权限更新请求体
      * @param ETag ETag请求头，用于乐观锁控制
      * @param from 请求来源，可选参数
      * @throws Exception 抛出异常，由LogicRdsExceptionHandler统一处理
      */

    @ApiOperation(value = "修改权限")
    @RequestMapping(value = "/{accountName}/privileges", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void updatePrivileges(@IdPermission @IdMapper @PathVariable String instanceId,
                                 @PathVariable String accountName,
                                 @RequestBody @Valid AccountUpdatePrivilegesRequest request,
                                 @RequestHeader ("x-bce-if-match") String ETag,
                                 @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. instanceId: {}, accountName: {}", instanceId, accountName);
        try {
            accountService.updatePrivileges(instanceId, accountName, request, ETag, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 删除帐号
      * 删除指定instanceId和accountName的帐号
      * 
      * @param instanceId 实例ID
      * @param accountName 帐号名称
      * @param from 来源
      */

    @ApiOperation(value = "删除帐号")
    @RequestMapping(value = "/{accountName}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void delete(@IdPermission @PathVariable String instanceId,
                       @PathVariable String accountName,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. instanceId: {}, accountName: {}", instanceId, accountName);
        try {
            accountService.deleteAccount(instanceId, accountName, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 查询帐号是否已存在
      * 根据实例ID和帐号名称查询帐号是否已存在
      * 
      * @param instanceId 实例ID
      * @param accountName 帐号名称
      * @param from 来源，可选参数，默认为空字符串
      * @return 查询结果，IsExistResponse对象
      */

    @ApiOperation(value = "查询帐号是否已存在")
    @RequestMapping(value = "/{accountName}/isExist", method = RequestMethod.POST)
    public IsExistResponse check(@PathVariable String instanceId,
                                 @PathVariable String accountName,
                                 @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. instanceId: {}, accountName: {}", instanceId, accountName);
        IsExistResponse response = null;
        try {
            response = accountService
                    .accountCheck(instanceId, accountName);

        } catch (Exception ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return response;
    }
}
