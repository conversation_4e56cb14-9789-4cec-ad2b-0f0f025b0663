package com.baidu.bce.logic.rds.controller;


import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.rds.model.MajorVersionPrecheckResponse;
import com.baidu.bce.internalsdk.rds.model.PreCheckVersionRequest;
import com.baidu.bce.internalsdk.rds.model.RdsMinorVersionList;
import com.baidu.bce.internalsdk.rds.model.SourceCheckResponse;
import com.baidu.bce.internalsdk.rds.model.UpdateVersionRequest;
import com.baidu.bce.internalsdk.rds.model.UpgradeMajorVersion;
import com.baidu.bce.logic.rds.service.InstanceVersionService;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessException;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api("实例版本管理")
@RestController
@RequestMapping("/api/rds")
public class ConsoleApiInstanceVersionController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConsoleApiInstanceVersionController.class);

    @Autowired
    private InstanceVersionService versionService;
    /**
     * 查看实例允许升级的小版本列表
     * 根据实例ID获取允许升级的小版本列表
     * 
     * @param instanceId 实例ID
     * @return EdpResultResponse<RdsMinorVersionList> 返回允许升级的小版本列表的结果
     * @throws BceInternalResponseException 内部响应异常
     */

    @ApiOperation("查看实例允许升级的小版本列表")
    @RequestMapping(value = "/instance/{instanceId}/upgradeMinorVersionList", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<RdsMinorVersionList> getVersionList(
            @IdPermission @PathVariable @IdMapper String instanceId) {

        EdpResultResponse<RdsMinorVersionList> resultResponse = new EdpResultResponse<>();

        try {
            resultResponse.setResult(versionService.getVersionList(instanceId));
        } catch (BceInternalResponseException e) {
            BasisUtils.logicRdsExceptions(e);
        }

        return resultResponse;
    }
    /**
      * 实例升级小版本
      * 更新实例的小版本
      * 
      * @param instanceId 实例ID
      * @param request 更新版本请求体
      * @return 操作结果
      * @throws BceInternalResponseException 内部响应异常
      */

    @ApiOperation("实例升级小版本")
    @RequestMapping(value = "/instance/{instanceId}", method = RequestMethod.PUT, params = "upgradeMinorVersion")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateVersion(
            @IdPermission @PathVariable @IdMapper String instanceId, @RequestBody UpdateVersionRequest request) {

        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();

        try {
            versionService.updateVersion(instanceId, request, null);
        } catch (BceInternalResponseException e) {
            BasisUtils.logicRdsExceptions(e);
        }
        resultResponse.setResult(true);

        return resultResponse;
    }
    /**
      * 加入热活组前置检查小版本
      * 对传入的小版本进行前置检查，以确定是否可以加入热活组
      * 
      * @param request 前置检查请求参数
      * @return 检查结果
      * @throws BceInternalResponseException 检查过程中出现的内部响应异常
      */

    @ApiOperation("加入热活组前置检查小版本")
    @RequestMapping(value = "/group/checkVersion", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> preCheckOfGroupVersion(@RequestBody PreCheckVersionRequest request) {

        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();

        try {
            versionService.preCheckOfGroupVersion(request);
        } catch (BceInternalResponseException e) {
            throw new RDSBusinessExceptions.
                    PrecheckGroupVersionException(request.getLeaderId(), request.getFollowerId());
        }

        resultResponse.setResult(true);

        return resultResponse;
    }

    @ApiOperation("发起大版本升级")
    @RequestMapping(value = "/instance/{instanceId}", method = RequestMethod.PUT, params = "upgradeMajorVersion")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> upgradeMajorVerison(@IdPermission @PathVariable @IdMapper String instanceId
            , @RequestBody UpgradeMajorVersion request) {

        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        versionService.upgradeMajorVerison(instanceId, request);
        resultResponse.setResult(true);

        return resultResponse;
    }

    @ApiOperation("大版本升级前置检查")
    @RequestMapping(value = "/instance/{instanceId}/majorVersion/preCheck", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<MajorVersionPrecheckResponse> precheckOfMajorVersion(
            @IdPermission @PathVariable @IdMapper String instanceId,
             @RequestParam(required = true, defaultValue = "1000") String checkType) {
        EdpResultResponse<MajorVersionPrecheckResponse> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(versionService.precheckOfMajorVersion(instanceId, checkType));

        return resultResponse;
    }

    @ApiOperation("迁移评估")
    @RequestMapping(value = "/instance/{instanceId}/dtsSourceCheck", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SourceCheckResponse> dtsSourceCheck(
            @IdPermission @PathVariable @IdMapper String instanceId) {
        EdpResultResponse<SourceCheckResponse> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(versionService.dtsSourceCheck(instanceId));

        return resultResponse;
    }

}
