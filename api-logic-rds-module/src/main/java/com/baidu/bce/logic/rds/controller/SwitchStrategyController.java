package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.strategy.ExchangeStrategyResponse;
import com.baidu.bce.internalsdk.rds.model.strategy.SwitchPrecheckResponse;
import com.baidu.bce.internalsdk.rds.model.strategy.TaskStatusResponses;
import com.baidu.bce.internalsdk.rds.model.strategy.UpdateExchangeStrategyRequest;
import com.baidu.bce.logic.rds.service.SwitchStrategyService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/rds/instance")
public class SwitchStrategyController {

    @Autowired
    private SwitchStrategyService strategyService;

    /**
     * 查询切换策略
     * 根据实例ID查询对应的切换策略详情
     * 
     * @param instanceId 实例ID
     * @return 切换策略详情
     */
    @ApiOperation("查询切换策略")
    @RequestMapping(value = "/{instanceId}/exchangeStrategy", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS},
            permission = {RDSConstant.READ}, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<ExchangeStrategyResponse> detail(@IdPermission @PathVariable @IdMapper String instanceId) {
        EdpResultResponse<ExchangeStrategyResponse> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(strategyService.detail(instanceId));
        return resultResponse;
    }
    /**
      * 更新切换策略
      * 根据提供的实例ID和请求体，更新对应的切换策略
      * 
      * @param instanceId 实例ID
      * @param request 更新切换策略的请求体
      * @return EdpResultResponse<Boolean> 返回操作结果
      */

    @ApiOperation("更新切换策略")
    @RequestMapping(value = "/{instanceId}/exchangeStrategy", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS},
            permission = {RDSConstant.OPERATE}, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateExchangeStrategy(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @RequestBody UpdateExchangeStrategyRequest request) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();

        strategyService.updateExchangeStrategy(instanceId, request);
        resultResponse.setResult(true);
        return resultResponse;
    }
    /**
     * 切换前置检查
     * 对实例进行切换前的检查操作
     * 
     * @param instanceId 实例ID
     * @return EdpResultResponse<SwitchPrecheckResponse> 检查结果
     */

    @ApiOperation("切换前置检查")
    @RequestMapping(value = "/switch/{instanceId}", method = RequestMethod.POST, params = "switchoverPrecheck")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS},
            permission = {RDSConstant.OPERATE}, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SwitchPrecheckResponse> preCheck(@IdPermission @PathVariable @IdMapper String instanceId) {
        EdpResultResponse<SwitchPrecheckResponse> resultResponse = new EdpResultResponse<>();

        resultResponse.setResult(strategyService.strategyService(instanceId));

        return resultResponse;
    }
    /**
      * 查询任务状态
      * 根据任务ID查询任务的状态信息
      * 
      * @param taskId 任务ID
      * @return EdpResultResponse<TaskStatusResponses> 查询结果
      */

    @ApiOperation("查询任务状态")
    @RequestMapping(value = "/precheck/{taskId}", method = RequestMethod.GET)
    public EdpResultResponse<TaskStatusResponses> taskStatus(@PathVariable Integer taskId) {
        EdpResultResponse<TaskStatusResponses> resultResponse = new EdpResultResponse<>();

        resultResponse.setResult(strategyService.taskStatus(taskId));

        return resultResponse;

    }
}
