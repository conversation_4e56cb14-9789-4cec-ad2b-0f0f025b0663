package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.security.*;
import com.baidu.bce.logic.rds.service.WhiteListService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * Created by luping03 on 17/11/3.
 */
@RestController
@RequestMapping("/v1/rds/instances")
public class WhiteListController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private WhiteListService whiteListService;
    /**
     * 获取白名单列表
     * 根据实例ID获取白名单信息，并设置ETag头信息
     * 
     * @param instanceId 实例ID
     * @param from 起始位置，非必需，默认值为空字符串
     * @param servletResponse HTTP响应对象，用于设置ETag头信息
     * @return SecurityIp对象，包含白名单信息
     * @throws Exception 异常信息，调用过程中可能出现的异常
     */

    @ApiOperation(value = "获取白名单列表")
    @RequestMapping(value = "/{instanceId}/whitelist", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SecurityIp getWhiteList(@IdPermission @IdMapper @PathVariable String instanceId,
                                   @RequestParam(required = false, defaultValue = "") String from,
                                   HttpServletResponse servletResponse) {
        LOGGER.debug("get whitelist.");
        SecurityIp response = null;
        try {
            response = whiteListService.getWhiteList(instanceId);
            servletResponse.setHeader("ETag", response.getETag());
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 设置白名单列表
      * 设置白名单接口，用于更新或添加RDS实例的白名单规则
      * 
      * @param instanceId 实例ID，用于标识需要操作的RDS实例
      * @param request 包含白名单信息的请求体
      * @param ETag 请求头，用于乐观锁控制
      * @param from 请求参数，标识请求来源，默认为空字符串
      */

    @ApiOperation(value = "设置白名单列表")
    @RequestMapping(value = "/{instanceId}/whitelist", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void setWhiteList(@IdPermission @IdMapper @PathVariable String instanceId,
                             @RequestBody @Valid SecurityIpPutRequest request,
                             @RequestHeader ("x-bce-if-match") String ETag,
                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("set whitelist.");
        try {
            whiteListService.setWhiteList(instanceId, request, ETag);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 获取SSL加密信息
     * 该接口用于获取指定RDS实例的SSL加密信息。
     * 
     * @param instanceId RDS实例ID
     * @param from 请求来源，可选参数，默认为空字符串
     * @return SslInfoResponse SSL加密信息
     */

    @ApiOperation(value = "获取SSL加密信息")
    @RequestMapping(value = "/ssl/{instanceId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SslInfoResponse getSslState(@IdPermission @PathVariable String instanceId,
                                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get SslState.");
        SslInfoResponse response = null;
        try {
            response = whiteListService.getSslState(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 设置SSL状态
      * 用于更新指定实例的SSL访问状态
      * 
      * @param instanceId 实例ID
      * @param request SSL访问请求体
      * @param from 请求来源（可选参数，默认值为空字符串）
      * @throws Exception 如果在更新SSL访问状态时发生异常，则抛出
      */

    @ApiOperation(value = "设置SSL")
    @RequestMapping(value = "/ssl/{instanceId}", params = {"sslAccessible"}, method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void setSslState(@IdPermission @PathVariable String instanceId,
                            @RequestBody SslAccessibleRequest request,
                            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("set SslState.");
//        SslInfoResponse response = null;
        try {
            whiteListService.updateSslAccess(instanceId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
//        return response;
    }
    /**
      * 获取SSL加密证书接口
      * 通过GET请求获取SSL加密证书信息
      * 
      * @param from 来源标识，默认为空字符串
      * @return SslGetCaResponse SSL加密证书响应对象
      */

    @ApiOperation(value = "获取SSL加密证书")
    @RequestMapping(value = "/ssl/static/ca", method = RequestMethod.GET)
//    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
//            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX, ids = "*")
    public SslGetCaResponse getCa(@RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get ca.");
        SslGetCaResponse response = null;
        try {
            response = whiteListService.queryCa();
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
}
