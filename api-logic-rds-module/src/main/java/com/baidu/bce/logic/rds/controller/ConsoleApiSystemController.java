package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.model.GroupWhiteAccountResult;
import com.baidu.bce.logic.rds.service.model.WhiteAccountResult;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by gengguangming on 2016/1/18.
 */
@RestController
@RequestMapping("/api/rds/system")
public class ConsoleApiSystemController {


    @Autowired
    private OthersService othersService;
    /**
     * 判断是否为白名单账户接口
     * 根据传入的feature参数，判断账户是否为白名单账户
     * 
     * @param feature 需要查询的feature值
     * @return EdpResultResponse<WhiteAccountResult> 返回查询结果
     */

    @RequestMapping(value = "/is_white_account", method = RequestMethod.POST)
    public EdpResultResponse<WhiteAccountResult> isWhiteAccount(@RequestParam(required = false) String feature) {
        return new EdpResultResponse<WhiteAccountResult>().withResult(othersService.isWhiteAccount(feature));
    }
    /**
      * 判断是否为群组白名单账户
      * 该函数用于判断当前账户是否为某个群组的白名单账户
      * 
      * @return EdpResultResponse<GroupWhiteAccountResult> 返回判断结果，包含是否属于白名单的标识
      */

    @RequestMapping(value = "/is_group_white_account", method = RequestMethod.POST)
    public EdpResultResponse<GroupWhiteAccountResult> isGroupWhiteAccount() {
        return new EdpResultResponse<GroupWhiteAccountResult>().withResult(othersService.isGroupWhiteAccount());
    }
}
