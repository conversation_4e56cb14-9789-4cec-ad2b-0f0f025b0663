package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryBaseResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryChartRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryChartResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryCreateTaskRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryCreateTaskResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDetailRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDetailResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDownloadRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDownloadResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryGetSqlRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryGetSqlResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryInstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryOpenStatusResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlExplainResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerFeedbackRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryStartOrStopResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySummaryRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySummaryResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskDetailResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskListRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskListResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskModel;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.service.SlowqueryService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * Created by luping03 on 17/11/3.
 */
@RestController
@RequestMapping("/v1/rds/instances/{instanceId}/slowquery")
public class SlowqueryController extends BaseController{
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private SlowqueryService slowqueryService;

    @Autowired
    private InstanceDao instanceDao;
    /**
      * 获取日志列表
      * 
      * @param instanceId 实例ID
      * @param request 日志列表请求体
      * @param from 日志来源，可选参数，默认为空字符串
      * @return 日志列表响应
      */

    @ApiOperation(value = "获取日志列表")
    @RequestMapping(value = "chart", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryChartResponse chart(@IdPermission @PathVariable String instanceId,
                                        @RequestBody @Valid SlowqueryChartRequest request,
                                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log list.");
        SlowqueryChartResponse response = null;

        if (StringUtils.isNotBlank(instanceId)) {
            request.setInstanceId(instanceId);
        }

        try {
            response = slowqueryService.chart(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取慢日志详情
      * 根据请求中的instanceId和请求参数，获取慢日志详情。
      * 
      * @param instanceId 实例ID，用于权限验证和设置请求参数
      * @param request 慢日志详情请求参数
      * @param from 请求来源，可选参数，默认值为空字符串
      * @return 慢日志详情响应
      */

    @ApiOperation(value = "获取慢日志详情")
    @RequestMapping(value = "detail", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryDetailResponse detail(@IdPermission @PathVariable String instanceId,
                                          @RequestBody @Valid SlowqueryDetailRequest request,
                                        @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log detail.");
        SlowqueryDetailResponse response = null;

        if (StringUtils.isNotBlank(instanceId)) {
            request.setInstanceId(instanceId);
        }

        try {
            response = slowqueryService.detail(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取慢日志统计
      * 该函数用于获取RDS慢日志的统计信息。它接收实例ID、慢日志统计请求对象和起始时间作为参数，并返回慢日志统计响应对象。
      *
      * @param instanceId 实例ID，用于指定要查询的RDS实例
      * @param request 慢日志统计请求对象，包含查询条件等信息
      * @param from 起始时间，用于指定查询的时间范围，可以为空
      * @return 慢日志统计响应对象，包含统计结果等信息
      */

    @ApiOperation(value = "获取慢日志统计")
    @RequestMapping(value = "summary", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowquerySummaryResponse summary(@IdPermission @PathVariable String instanceId,
                                            @RequestBody @Valid SlowquerySummaryRequest request,
                                          @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get summary list.");
        SlowquerySummaryResponse response = null;

        if (StringUtils.isNotBlank(instanceId)) {
            request.setInstanceId(instanceId);
        }

        try {
            response = slowqueryService.summary(request, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取慢日志下载地址（按天）
     * 根据请求获取特定RDS实例的慢日志下载地址
     * 
     * @param instanceId RDS实例ID
     * @param request 慢日志下载请求参数
     * @param from 起始时间（可选）
     * @return 慢日志下载地址响应
     */

    @ApiOperation(value = "获取慢日志下载地址（按天）")
    @RequestMapping(value = "downloadfile", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryDownloadResponse downloadfile(@IdPermission @PathVariable String instanceId,
                                            @RequestBody @Valid SlowqueryDownloadRequest request,
                                            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get downloadfile.");
        SlowqueryDownloadResponse response = null;
        try {
            response = slowqueryService.downloadfile(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取慢日志sql详情
     * 获取指定RDS实例的慢日志sql详情
     * 
     * @param instanceId RDS实例ID
     * @param request 慢日志sql请求参数
     * @param from 来源
     * @return 慢日志sql详情响应
     */

    @ApiOperation(value = "获取慢日志sql详情")
    @RequestMapping(value = "getsql", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryGetSqlResponse getSql(@IdPermission @PathVariable String instanceId,
                                            @RequestBody @Valid SlowqueryGetSqlRequest request,
                                            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get getSql list.");
        SlowqueryGetSqlResponse response = null;
        try {

            if (StringUtils.isNotBlank(instanceId)) {
                request.setInstanceId(instanceId);
            }

            response = slowqueryService.getSql(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 查看是否开启慢查询
     * 根据实例ID和来源获取慢查询的开启状态
     * 
     * @param instanceId 实例ID
     * @param from 来源
     * @return 慢查询开启状态
     */

    @ApiOperation(value = "查看是否开启慢查询")
    @RequestMapping(value = "openstatus", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryOpenStatusResponse getOpenStatus(@IdPermission @PathVariable String instanceId,
                                                     @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get getOpenStatus list.");
        SlowqueryOpenStatusResponse response = null;
        try {
            response = slowqueryService.getOpenStatus(instanceId, from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 开启慢查询
      * 该函数用于开启数据库的慢查询功能
      *
      * @param instanceId 实例ID，用于指定要操作的数据库实例
      * @param request 包含慢查询开启请求信息的对象
      * @param from 请求来源，非必需参数，默认为空字符串
      * @return SlowqueryStartOrStopResponse 对象，包含操作结果信息
      */

    @ApiOperation(value = "开启慢查询")
    @RequestMapping(value = "start", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryStartOrStopResponse start(@IdPermission @PathVariable String instanceId,
                                              @RequestBody @Valid SlowqueryInstanceIdRequest request,
                                              @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("slowquer start().");
        if (StringUtils.isNotBlank(instanceId)) {
            request.setInstanceId(instanceId);
        }
        SlowqueryStartOrStopResponse response = null;
        try {
            response = slowqueryService.start(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 关闭慢查询
      * 关闭慢查询接口，根据instanceId和请求参数关闭对应的慢查询
      *
      * @param instanceId 实例ID
      * @param request 慢查询实例ID请求体
      * @param from 请求来源，默认为空字符串
      * @return 关闭慢查询后的响应结果
      */

    @ApiOperation(value = "关闭慢查询")
    @RequestMapping(value = "stop", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryStartOrStopResponse stop(@IdPermission @PathVariable String instanceId,
                                             @RequestBody @Valid SlowqueryInstanceIdRequest request,
                                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("slowquer stop().");
        SlowqueryStartOrStopResponse response = null;
        try {
            response = slowqueryService.stop(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 创建慢查询任务
      * 根据传入的instanceId和SlowqueryCreateTaskRequest对象创建慢查询任务
      * 
      * @param instanceId 实例ID
      * @param request 慢查询任务请求对象
      * @param from 请求来源（可选）
      * @return 创建任务后的响应对象
      */

    @ApiOperation(value = "创建慢查询任务")
    @RequestMapping(value = "createTask", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryCreateTaskResponse createTask(@IdPermission @PathVariable String instanceId,
                                             @RequestBody @Valid SlowqueryCreateTaskRequest request,
                                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("slowquer createTask().");

        request.setInstanceId(instanceId);

        SlowqueryCreateTaskResponse response = null;
        try {
            response = slowqueryService.createTask(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 查看慢查询任务列表
     * 根据提供的参数，查询并返回慢查询任务列表
     * 
     * @param instanceId 实例ID
     * @param marker 分页查询标记
     * @param maxKeys 最大返回条数
     * @param filterStartTime 过滤开始时间
     * @param filterEndTime 过滤结束时间
     * @return 慢查询任务列表的响应
     */

    @ApiOperation(value = "查看慢查询任务列表")
    @RequestMapping(value = "taskList", method = RequestMethod.GET, params = {"manner=marker"})
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogicMarkerResultResponse<SlowqueryTaskModel> taskList(@IdPermission @PathVariable String instanceId,
                                                                  @RequestParam(required = false, defaultValue = "-1") String marker,
                                                                  @RequestParam(required = false, defaultValue = "100") Integer maxKeys,
                                                                  @RequestParam(required = false) String filterStartTime,
                                                                  @RequestParam(required = false) String filterEndTime

    ) {
        LOGGER.debug("slowquer taskList().");

        SlowqueryTaskListRequest request = new SlowqueryTaskListRequest();

        request.setInstanceId(instanceId);
        request.setPageSize("100");
        request.setPageNo("1");
        request.setFilterStartTime(filterStartTime);
        request.setFilterEndTime(filterEndTime);

        LogicMarkerResultResponse<SlowqueryTaskModel> response = new LogicMarkerResultResponse<SlowqueryTaskModel>();

        try {
            SlowqueryTaskListResponse responseTemp = slowqueryService.taskList(request);

            // 总是大于100，按100算
            if (responseTemp.getPage().getTotalCount() > 100) {
                responseTemp.getPage().setTotalCount(100);
            }

            response =  slowqueryService.listForPageByMarker(responseTemp, marker, maxKeys);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查看慢查询任务详情
      * 根据实例ID和任务ID获取慢查询任务的详细信息
      * 
      * @param instanceId 实例ID
      * @param taskId 任务ID
      * @param from 请求来源（可选）
      * @return 慢查询任务详情
      */

    @ApiOperation(value = "查看慢查询任务详情")
    @RequestMapping(value = "taskDetail/{taskId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryTaskDetailResponse taskDetail(@IdPermission @PathVariable String instanceId,
                                                  @PathVariable String taskId,
                                              @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("slowquer taskList().");
        SlowqueryTaskDetailResponse response = null;
        try {
            response = slowqueryService.taskDetail(instanceId, taskId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查看sql优化建议
      * 
      * 通过GET请求访问"sql/optimizer"接口，获取sql优化建议
      * 
      * @param instanceId 实例ID
      * @param database 数据库名，非必须参数，默认为空
      * @param executeTime 执行时间，非必须参数，默认为空
      * @param sqlMd5 sql的md5值，非必须参数，默认为空
      * @param sqlType sql类型，非必须参数，默认为空
      * @return 返回sql优化建议的响应体
      */

    @ApiOperation(value = "查看sql优化建议")
    @RequestMapping(value = "sql/optimizer", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowquerySqlOptimizerResponse sqlOptimizer(@IdPermission @PathVariable String instanceId,
                                                      @RequestParam(required = false,
                                                              defaultValue = "") String database,
                                                      @RequestParam(required = false,
                                                              defaultValue = "") String executeTime,
                                                      @RequestParam(required = false, defaultValue = "") String sqlMd5,
                                                      @RequestParam(required = false, defaultValue = "") String sqlType

    ) {

        SlowquerySqlOptimizerRequest request = new SlowquerySqlOptimizerRequest();
        request.setInstanceId(instanceId);
        request.setDatabase(database);
        request.setExecuteTime(executeTime);
        request.setSqlMd5(sqlMd5);
        request.setSqlType(sqlType);

        LOGGER.debug("slowquer sqlOptimizer().");
        SlowquerySqlOptimizerResponse response = null;
        try {
            response = slowqueryService.sqlOptimizer(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查看优化建议解释
      * 根据提供的数据库、执行时间、SQL的MD5和SQL类型，查询慢查询SQL的解释信息
      * 
      * @param instanceId 实例ID
      * @param database 数据库名
      * @param executeTime 执行时间
      * @param sqlMd5 SQL的MD5值
      * @param sqlType SQL类型
      * @return 返回慢查询SQL的解释信息
      */

    @ApiOperation(value = "查看优化建议解释")
    @RequestMapping(value = "sql/explain", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowquerySqlExplainResponse sqlExplain(@IdPermission @PathVariable String instanceId,
                                                  @RequestParam(required = false, defaultValue = "") String database,
                                                  @RequestParam(required = false,
                                                          defaultValue = "") String executeTime,
                                                  @RequestParam(required = false, defaultValue = "") String sqlMd5,
                                                  @RequestParam(required = false, defaultValue = "") String sqlType) {
        LOGGER.debug("slowquer sqlExplain().");

        SlowquerySqlOptimizerRequest request = new SlowquerySqlOptimizerRequest();
        request.setInstanceId(instanceId);
        request.setDatabase(database);
        request.setExecuteTime(executeTime);
        request.setSqlMd5(sqlMd5);
        request.setSqlType(sqlType);

        SlowquerySqlExplainResponse response = null;
        try {
            response = slowqueryService.sqlExplain(request);
            List<SlowquerySqlExplainResponse.ExplainSql> list = response.getResult().getExplainSql();
            int len = list.size();
            for (int i = 0;i < len;i++) {
                list.get(i).setExtra(list.get(i).getExtraInfo());
            }
            response.getResult().setExplainSql(list);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查看优化建议反馈
      * 根据实例ID、优化ID、评分和用户建议查看SQL优化建议反馈
      * 
      * @param instanceId 实例ID
      * @param optimizerId 优化ID
      * @param score 评分
      * @param userAdvice 用户建议
      * @return 优化建议反馈结果
      */

    @ApiOperation(value = "查看优化建议反馈")
    @RequestMapping(value = "sql/optimizer/feedback", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowqueryBaseResponse sqlOptimizerFeedback(@IdPermission @PathVariable String instanceId,
                                                      @RequestParam(required = false,
                                                              defaultValue = "") String optimizerId,
                                                      @RequestParam(required = false,
                                                              defaultValue = "") String score,
                                                      @RequestParam(required = false,
                                                              defaultValue = "") String userAdvice) {
        LOGGER.debug("slowquer sqlOptimizerFeedback().");
        SlowquerySqlOptimizerFeedbackRequest request = new SlowquerySqlOptimizerFeedbackRequest();
        request.setInstanceId(instanceId);
        request.setOptimizerId(optimizerId);
        request.setScore(score);
        request.setUserAdvice(userAdvice);
        SlowqueryBaseResponse response = null;
        try {
            response = slowqueryService.sqlOptimizerFeedback(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

}
