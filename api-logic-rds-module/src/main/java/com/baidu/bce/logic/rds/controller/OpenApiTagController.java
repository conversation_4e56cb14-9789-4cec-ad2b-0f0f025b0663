package com.baidu.bce.logic.rds.controller;


import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.tag.AssignTagRequest;
import com.baidu.bce.logic.rds.service.model.tag.LogicalAssignResource;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * Created by lilinlin02
 */
@RestController
@RequestMapping("/v1/instance")
public class OpenApiTagController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiTagController.class);

    @Autowired
    private OthersService othersService;

    @Autowired
    private IdMapperService idMapperService;
    /**
     * 更新标签关联关系
     * 更新资源标签关联关系
     * 
     * @param request 标签关联关系请求体
     */

    @RequestMapping(value = "/tags", method = RequestMethod.POST)
    @ApiOperation(value = "更新标签关联关系")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void instanceAssignTags(@IdPermission @RequestBody AssignTagRequest request) {
        LOGGER.debug("tag api...");
        // 长短 ID 转换 此处 对应标签实体类所用长短 ID 转换注解不起作用
        List<LogicalAssignResource> resources = request.getResources();
        for (int i = 0; i < resources.size(); i++) {
            String instanceUuid = idMapperService.getInstanceUuid(request.getResources().get(i).getInstanceId());
            request.getResources().get(i).setInstanceId(instanceUuid);
        }
        othersService.assignTags(request);
    }
}
