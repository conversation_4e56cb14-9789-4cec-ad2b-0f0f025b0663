package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceChangeDiskTypeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceChangeDiskTypeResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePrecheckParameterRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePrecheckParameterResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.logic.rds.service.BinlogService;
import com.baidu.bce.internalsdk.rds.model.instance.PrecheckResourceCreateResponse;
import com.baidu.bce.internalsdk.rds.model.instance.PrecheckResourceCreateRequest;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.model.otherservice.DiskIoTypeResponse;
import com.baidu.bce.logic.rds.service.model.otherservice.ZoneRequest;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/rds/instance")
public class ConsoleApiController {

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private BinlogService binlogService;

    @Autowired
    private OthersService othersService;
    /**
      * 查看实例是否支持切换磁盘类型
      * 特指本地盘转云盘，支持对实例的磁盘类型切换进行预检查
      * 
      * @param request 包含实例ID和目标磁盘类型的请求体
      * @return EdpResultResponse 返回结果，包含操作是否成功等信息
      * @throws Exception 抛出异常，如果出现操作失败或其他错误情况
      */

    @ApiOperation(value = "查看实例是否支持切换磁盘类型：特指本地盘转云盘")
    @RequestMapping(value = "/precheck/changeDiskType", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse changeDiskType(
            @IdPermission @RequestBody @Valid @IdMapper InstanceChangeDiskTypeRequest request) {
        try {
            EdpResultResponse response = new EdpResultResponse();
            InstanceChangeDiskTypeResponse res = instanceService.
                    changeDiskType(request.getInstanceId(), request.getTargetDiskType());
            response.setResult(res);
            return response;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }
    /**
      * 查询MySQL实例变配参数
      * 查询MySQL实例如果发生变配，哪些参数发生变化，只关注跟规格相关的参数
      * 
      * @param request 包含实例ID和请求参数的对象
      * @return EdpResultResponse 返回查询结果
      * @throws Exception 可能会抛出异常，由LogicRdsExceptionHandler处理
      */

    @ApiOperation(value = "查询MySQL实例如果发生变配，哪些参数发生变化，只关注跟规格相关的参数")
    @RequestMapping(value = "/precheck/parameter", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse precheckParameter(
            @IdPermission @RequestBody @Valid @IdMapper InstancePrecheckParameterRequest request) {
        try {
            EdpResultResponse response = new EdpResultResponse();
            InstancePrecheckParameterResponse res = instanceService.
                    precheckParameter(request.getInstanceId(), request);
            response.setResult(res);
            return response;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    }
    /**
     * 刷新Binlog接口
     * 用于刷新数据库的Binlog，以便于同步等操作
     * 
     * @param request 包含实例ID的请求体
     * @return EdpResultResponse 响应结果
     */

    @ApiOperation(value = "Flush Binlog")
    @RequestMapping(value = "/binlog/flush", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse binlogFlush(
            @IdPermission @RequestBody @Valid @IdMapper InstanceIdRequest request) {
        try {
            EdpResultResponse response = new EdpResultResponse();
            binlogService.binlogFlush(request.getInstanceId());
            return response;
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return null;
    /**
     * 子网IP剩余量前置检查接口
     * 用于前置检查资源创建请求是否符合条件
     * 
     * @param request 前置检查资源创建请求参数
     * @return EdpResultResponse 返回结果对象
     */
    }
    @ApiOperation(value = "子网IP剩余量前置检查接口")
    @RequestMapping(value = "/precheck/resourceCreate", method = RequestMethod.POST)
    public EdpResultResponse precheckResourceCreate(
            @RequestBody @Valid @IdMapper PrecheckResourceCreateRequest request) {

        EdpResultResponse response = new EdpResultResponse();
        PrecheckResourceCreateResponse res = instanceService.
                precheckResource(request);
        response.setResult(res);
        return response;
    }
    /**
     * 通过az获取支持的磁盘类型
     * 通过指定的可用区az，获取该可用区支持的磁盘类型
     *
     * @param request 请求体，包含指定的可用区az
     * @return EdpResultResponse<DiskIoTypeResponse> 返回结果，包含支持的磁盘类型信息
     */

    @ApiOperation(value = "通过az获取支持的磁盘类型")
    @RequestMapping(value = "/getDiskIoTypeByZone", method = RequestMethod.POST)
    public EdpResultResponse<DiskIoTypeResponse> getDiskIoTypeByZone(@RequestBody ZoneRequest request) {
        EdpResultResponse<DiskIoTypeResponse> edpResultResponse = new EdpResultResponse<>();
        DiskIoTypeResponse response = othersService.getDiskIoTypeByZone(request.getZone());
        edpResultResponse.setResult(response);
        return edpResultResponse;
    }

}
