package com.baidu.bce.logic.rds.controller.exception.handling.service;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.rds.service.I18nService;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessException;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.baidu.bce.plat.webframework.exception.BceValidationException;
import com.baidu.bce.plat.webframework.exception.handling.ExceptionUtil;
import com.baidu.bce.plat.webframework.exception.handling.service.ServiceExceptionResponse;
import com.baidu.bce.plat.webframework.model.edpv2.EdpNormalErrorResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.Map;

/**
 * bce-plat-webframework 框架中的 ServiceExceptionHandler 不支持国际化配置，所以这里我们 copy 一份修改之。
 * @see com.baidu.bce.plat.webframework.exception.handling.service.ServiceExceptionHandler;
 */
@ControllerAdvice
public class ServiceExceptionHandler {

    Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private I18nService i18nService;

    private void handleForI18n(RDSBusinessException ex) {
        String i18nKey = ex.getI18nKey();
        if (i18nKey == null) {
            return;
        }
        String message = i18nService.getMessage(i18nKey, ex.getI18nArgs());
        if (message == null) {
            return;
        }
        Field detailMessageField = BasisUtils.getDeclaredFieldByRecursively(ex.getClass(), "detailMessage");
        detailMessageField.setAccessible(true);
        try {
            detailMessageField.set(ex, message);
        } catch (IllegalAccessException e) {
            log.error("Occur exception: ", e);
        }
    }

    private void updateDetailMessage(Throwable throwable, String detailMessage) {
        Field detailMessageField =
                BasisUtils.getDeclaredFieldByRecursively(throwable.getClass(), "detailMessage");
        detailMessageField.setAccessible(true);
        try {
            detailMessageField.set(throwable, detailMessage);
        } catch (IllegalAccessException e) {
            log.error("Occur exception: ", e);
        }
    }

    private void handleForI18n(BceInternalResponseException ex) {
        String i18nKey = RDSBusinessException.EXCEPTION_OTHER_SERVICE_I18N_KEY_PREFIX + ex.getCode();
        String message = i18nService.getMessage(i18nKey);
        if (message == null) {
            return;
        }
        updateDetailMessage(ex, message);
    }

    private void handleForI18n(BceException ex) {
        String i18nKey = RDSBusinessException.EXCEPTION_OTHER_SERVICE_I18N_KEY_PREFIX + ex.getCode();
        String message = i18nService.getMessage(i18nKey);
        if (message == null) {
            return;
        }
        updateDetailMessage(ex, message);
    }

    private void handleForI18n(Exception ex) {
        // 目前该类错误指定为内部服务错误
        String i18nKey = RDSBusinessException.EXCEPTION_I18N_KEY_PREFIX + "InternalServerError";
        String message = i18nService.getMessage(i18nKey);
        if (message == null) {
            return;
        }
        updateDetailMessage(ex, message);
    }

    @ExceptionHandler(RDSBusinessException.class)
    public Object handle(HttpServletRequest request, HttpServletResponse response,
                                                           RDSBusinessException ex) {
        log.error("Handle RDSBusinessException: ", ex);
        handleForI18n(ex);
        if (request.getRequestURI().startsWith(RDSConstant.CONSOLE_API_PREFIX)) {
            // Console API，这里直接返回 Edp 格式响应
            return createEdpNormalErrorResponseEntity(request, response, ex, false);
        }
        ServiceExceptionResponse body = new ServiceExceptionResponse();
        String requestId = ExceptionUtil.getRequestId(request, response, ex.getRequestId());
        body.setRequestId(requestId);
        body.setCode(ex.getCode());
        body.setMessage(ex.getMessage());
        log.info("[exception] [status:{},code:{}] {} {}", ex.getHttpStatus(), body.getCode(), request.getMethod()
                , request.getRequestURI());
        return new ResponseEntity<>(body, HttpStatus.valueOf(ex.getHttpStatus()));
    }

    /**
     * api-logic-rds 模块调用 RDS 后端或其他服务（比如 Billing 相关服务）的 API 时，
     * 收到错误响应会产生异常 BceInternalResponseException，这里拦截进行处理。
     */
    @ExceptionHandler(BceInternalResponseException.class)
    public Object handle(HttpServletRequest request, HttpServletResponse response
            , BceInternalResponseException ex) {
        log.error("Handle BceInternalResponseException: ", ex);
        log.debug("ex.getCode() = {}", ex.getCode());
        if (StringUtils.isEmpty(ex.getCode())) {
            ex.setCode("InternalServerErrorException");
        }
        log.debug("ex.getMessage() = {}", ex.getMessage());
        if (StringUtils.isEmpty(ex.getMessage())) {
            updateDetailMessage(ex, "Internal service occurs error.");
        }
        // 1. MessageBodyReader not found for media type=text/html,
        //      type=class com.baidu.bce.internalsdk.core.BceInternalError,
        //      genericType=class com.baidu.bce.internalsdk.core.BceInternalError.
        if (ex.getMessage().startsWith("MessageBodyReader not found for media type")) {
            updateDetailMessage(ex, "Internal service occurs error.");
        }
        // 与管控同学确认，此限制需放行4XX类报错
        if (!String.valueOf(ex.getHttpStatus()).startsWith("4") && ex.getMessage().length() > 200) {
            ex.setCode("InternalServerErrorException");
            updateDetailMessage(ex, "Internal service occurs error.");
        }
        handleForI18n(ex);
        if (request.getRequestURI().startsWith(RDSConstant.CONSOLE_API_PREFIX)) {
            // Console API，这里直接返回 Edp 格式响应
            return createEdpNormalErrorResponseEntity(request, response, ex, false);
        }
        ServiceExceptionResponse body = new ServiceExceptionResponse();
        String requestId = ExceptionUtil.getRequestId(request, response, ex.getRequestId());
        body.setRequestId(requestId);
        body.setCode(ex.getCode());
        body.setMessage(ex.getMessage());
        log.info("[exception] [status:{},code:{}] {} {}", ex.getHttpStatus(), body.getCode(), request.getMethod()
                , request.getRequestURI());
        return new ResponseEntity<>(body, HttpStatus.valueOf(ex.getHttpStatus()));
    }

    @ExceptionHandler(BceException.class)
    public Object handle(HttpServletRequest request, HttpServletResponse response
            , BceException ex) {
        log.error("[exception:BceException] handled: {}", ex);

        log.debug("ex.getCode() = {}", ex.getCode());
        log.debug("ex.getMessage() = {}", ex.getMessage());
        // 登录过期的情况特殊处理，其余暂时与之前保持一致
        if (StringUtils.isNotEmpty(ex.getMessage()) &&
                RDSConstant.ILLEGAL_REQUEST_MESSAGE.equalsIgnoreCase(ex.getMessage())) {
            ex.setCode("SessionTokenExpired");
        }
        handleForI18n(ex);
        if (request.getRequestURI().startsWith(RDSConstant.CONSOLE_API_PREFIX)) {
            // Console API，这里直接返回 Edp 格式响应
            return createEdpNormalErrorResponseEntity(request, response, ex, false);
        }
        ServiceExceptionResponse body = new ServiceExceptionResponse();
        String requestId = ExceptionUtil.getRequestId(request, response, ex.getRequestId());
        body.setRequestId(requestId);
        body.setCode(ex.getCode());
        body.setMessage(ex.getMessage());
        log.info("[exception] [status:{},code:{}] {} {}", ex.getHttpStatus(), body.getCode(), request.getMethod()
                , request.getRequestURI());
        return new ResponseEntity<>(body, HttpStatus.valueOf(ex.getHttpStatus()));
    }

    @ExceptionHandler(Exception.class)
    public Object handle(HttpServletRequest request, HttpServletResponse response
            , Exception ex) {
        log.error("[exception:UnknownException] " + request.getMethod() + " " + request.getRequestURI(), ex);
        handleForI18n(ex);
        if (request.getRequestURI().startsWith(RDSConstant.CONSOLE_API_PREFIX)) {
            // Console API，这里直接返回 Edp 格式响应
//            BceInternalResponseException bceInternalResponseException =
//                    new BceInternalResponseException("内部服务错误");
//            bceInternalResponseException.setRequestId(
//                    ExceptionUtil.getRequestId(request, response, null));
//            bceInternalResponseException.setCode("Exception");
//            return createEdpNormalErrorResponseEntity(
//                    request, response, bceInternalResponseException, true);
            return createEdpNormalErrorResponseEntity(request, response, ex, false);
        }
        ServiceExceptionResponse body = new ServiceExceptionResponse();
        String requestId = ExceptionUtil.getRequestId(request, response, null);
        body.setRequestId(requestId);
        body.setCode("Exception");
        body.setMessage(ex.getMessage());
        log.info("[exception:{}] {} {}", body.getCode(), request.getMethod(), request.getRequestURI());
        return new ResponseEntity<>(body, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(BceValidationException.class)
    public Object handle(HttpServletRequest request, HttpServletResponse response
            , BceValidationException ex) {
        log.debug("[exception:BceValidationException] handled: {}", ex);
        if (request.getRequestURI().startsWith(RDSConstant.CONSOLE_API_PREFIX)) {
            // Console API，这里直接返回 Edp 格式响应
            BceInternalResponseException bceInternalResponseException =
                    new BceInternalResponseException(convertFiledMessageMapToString(ex));
            bceInternalResponseException.setRequestId(
                    ExceptionUtil.getRequestId(request, response, ex.getRequestId()));
            bceInternalResponseException.setCode(ex.getCode());
            return createEdpNormalErrorResponseEntity(
                    request, response, bceInternalResponseException, true);
        }
        ServiceExceptionResponse body = new ServiceExceptionResponse();
        String requestId = ExceptionUtil.getRequestId(request, response, ex.getRequestId());
        body.setRequestId(requestId);
        body.setCode(ex.getCode());
        body.setMessage(convertFiledMessageMapToString(ex));
        log.info("[exception:{}] {} {}", body.getCode(), request.getMethod(), request.getRequestURI());
        return new ResponseEntity<>(body, HttpStatus.valueOf(ex.getHttpStatus()));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handle(HttpServletRequest request, HttpServletResponse response
            , MethodArgumentNotValidException ex) {
        return handle(request, response, new BceValidationException(ex.getBindingResult()));
    }

    /**
     * HttpMessageNotReadableException Handler
     * Handle Exceptions like Json deserialization exception `com.fasterxml.jackson.databind.exc.InvalidFormatException`
     * and some other bad request Exception
     * Error message only return some general message, like `Could not read document`, `Could not read JSON`,
     * in case of some sensitive information disclosure
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Object handle(HttpServletRequest request, HttpServletResponse response,
                                                           HttpMessageNotReadableException ex) {
        log.warn("[exception:HttpMessageNotReadableException] " + request.getMethod() + " "
                + request.getRequestURI(), ex);
        BceInternalResponseException bceInternalResponseException =
                new BceInternalResponseException(
                        "Http Message Not Readable: " + ex.getMessage().split(":", 2)[0]);
        bceInternalResponseException.setRequestId(
                ExceptionUtil.getRequestId(request, response, null));
        bceInternalResponseException.setCode("HttpMessageNotReadable");
        handleForI18n(bceInternalResponseException);
        if (request.getRequestURI().startsWith(RDSConstant.CONSOLE_API_PREFIX)) {
            // Console API，这里直接返回 Edp 格式响应
            return createEdpNormalErrorResponseEntity(
                    request, response, bceInternalResponseException, true);
        }
        ServiceExceptionResponse body = new ServiceExceptionResponse();
        String requestId = ExceptionUtil.getRequestId(request, response, null);
        body.setRequestId(requestId);
        body.setCode("HttpMessageNotReadable");
        // Error message only return some general message
        // like `Could not read document` from `Could not read document: xxxx: xxxx`
        body.setMessage("Http Message Not Readable: " + ex.getMessage().split(":", 2)[0]);
        log.info("[exception:{}] {} {}", body.getCode(), request.getMethod(), request.getRequestURI());
        return new ResponseEntity<>(body, HttpStatus.BAD_REQUEST);
    }

    private String convertFiledMessageMapToString(BceValidationException ex) {
        StringBuilder message = new StringBuilder();
        for (Map.Entry<String, String> fieldMessage : ex.getFieldMessageMap().entrySet()) {
            message.append(fieldMessage.getKey()).append(":").append(fieldMessage).append('\n');
        }
        return message.toString();
    }

    private ResponseEntity<EdpNormalErrorResponse> createEdpNormalErrorResponseEntity(
            HttpServletRequest request, HttpServletResponse response, BceException ex, boolean isInternalError) {
        // status
        int status = 400;
        if (isInternalError) {
            status = 500;
        }

        // error
        String requestId = ExceptionUtil.getRequestId(request, response, ex.getRequestId());
        EdpNormalErrorResponse.NormalMessage normalMessage = new EdpNormalErrorResponse
                .NormalMessage(ex.getMessage(), ex.getDetail());

        // create response
        EdpNormalErrorResponse edpNormalErrorResponse = new EdpNormalErrorResponse(status, null);

        // 兼容已有字段
        edpNormalErrorResponse.setRequestId(requestId);
        edpNormalErrorResponse.setCode(ex.getCode());
        edpNormalErrorResponse.setMessage(normalMessage);

        log.info("[exception:{}] {} {}", ex.getCode(), request.getMethod(), request.getRequestURI());
        return new ResponseEntity<>(edpNormalErrorResponse, HttpStatus.OK);
    }

    private ResponseEntity<EdpNormalErrorResponse> createEdpNormalErrorResponseEntity(
            HttpServletRequest request, HttpServletResponse response, BceInternalResponseException ex,
            boolean isInternalError) {
        // status
        int status = 400;
        if (isInternalError) {
            status = 500;
        }

        // error
        String requestId = ExceptionUtil.getRequestId(request, response, ex.getRequestId());
        EdpNormalErrorResponse.NormalMessage normalMessage = new EdpNormalErrorResponse
                .NormalMessage(ex.getMessage(), null);

        // create response
        EdpNormalErrorResponse edpNormalErrorResponse = new EdpNormalErrorResponse(status, null);

        // 兼容已有字段
        edpNormalErrorResponse.setRequestId(requestId);
        edpNormalErrorResponse.setCode(ex.getCode());
        edpNormalErrorResponse.setMessage(normalMessage);

        log.info("[exception:{}] {} {}", ex.getCode(), request.getMethod(), request.getRequestURI());
        return new ResponseEntity<>(edpNormalErrorResponse, HttpStatus.OK);
    }

    private ResponseEntity<EdpNormalErrorResponse> createEdpNormalErrorResponseEntity(
            HttpServletRequest request, HttpServletResponse response, Exception ex,
            boolean isInternalError) {
        // status
        int status = 400;
        if (isInternalError) {
            status = 500;
        }

        // error
        String requestId = ExceptionUtil.getRequestId(request, response, null);
        EdpNormalErrorResponse.NormalMessage normalMessage = new EdpNormalErrorResponse
                .NormalMessage(ex.getMessage(), null);

        // create response
        EdpNormalErrorResponse edpNormalErrorResponse = new EdpNormalErrorResponse(status, null);

        // 兼容已有字段
        edpNormalErrorResponse.setRequestId(requestId);
        edpNormalErrorResponse.setCode("Exception");
        edpNormalErrorResponse.setMessage(normalMessage);

        log.info("[exception:{}] {} {}", "Exception", request.getMethod(), request.getRequestURI());
        return new ResponseEntity<>(edpNormalErrorResponse, HttpStatus.OK);
    }

}
