package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceInnodbStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceProcesslistResponse;
import com.baidu.bce.internalsdk.rds.model.performance.ConnectionListResponse;
import com.baidu.bce.internalsdk.rds.model.performance.KillProcessRequest;
import com.baidu.bce.internalsdk.rds.model.performance.TransactionListResponse;
import com.baidu.bce.logic.rds.service.PerformanceService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/v1/rds/instance/{instanceId}/performance")
public class PerformanceController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PerformanceController.class);

    @Autowired
    private PerformanceService performanceService;
    /**
     * 获取Processlist快照数据
     * 根据实例ID获取Processlist快照数据
     * 
     * @param instanceId 实例ID
     * @return InstanceProcesslistResponse 实例Processlist快照数据响应
     */

    @ApiOperation(value = "Processlist快照数据")
    @RequestMapping(value = "/processlist", method = RequestMethod.POST)
    public InstanceProcesslistResponse getProcesslist(@IdPermission @PathVariable String instanceId) {
        InstanceProcesslistResponse response = new InstanceProcesslistResponse();
        try {
            response = performanceService.getProcesslist(instanceId);
        } catch (Exception e) {
            LOGGER.error("get instance performance processlist error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 终止会话
     * 用于终止指定实例的会话
     * 
     * @param instanceId 实例ID
     * @param request 终止会话请求体
     */

    @ApiOperation(value = "Kill会话")
    @RequestMapping(value = "/process/kill", method = RequestMethod.POST)
    public void killProcess(@IdPermission @PathVariable String instanceId,
                            @Valid @RequestBody KillProcessRequest request) {
        try {
            performanceService.killProcess(instanceId, request);
        } catch (Exception e) {
            LOGGER.error("kill instance process error", e);
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 获取innodbstatus快照数据
      * 根据实例ID获取innodbstatus快照数据
      * 
      * @param instanceId 实例ID
      * @return 返回innodbstatus快照数据
      */

    @ApiOperation(value = "innodbstatus快照数据")
    @RequestMapping(value = "/innodbstatus", method = RequestMethod.POST)
    public InstanceInnodbStatusResponse getInnodbstatus(@IdPermission @PathVariable String instanceId) {
        InstanceInnodbStatusResponse response = new InstanceInnodbStatusResponse();
        try {
            response = performanceService.getInnodbstatus(instanceId);
        } catch (Exception e) {
            LOGGER.error("get instance performance innodbstatus error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 事务列表
      * 获取事务列表接口
      *
      * @param instanceId 实例ID
      * @return 事务列表响应对象
      */

    @ApiOperation(value = "事务列表")
    @RequestMapping(value = "/transaction", method = RequestMethod.GET)
    public TransactionListResponse transactionList(@IdPermission @PathVariable String instanceId) {
        TransactionListResponse response = new TransactionListResponse();
        try {
            response = performanceService.getTransactionList(instanceId);
        } catch (Exception e) {
            LOGGER.error("get instance performance transaction list error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 连接列表
      * 获取指定实例的连接列表
      * 
      * @param instanceId 实例ID
      * @return 连接列表响应对象
      */

    @ApiOperation(value = "连接列表")
    @RequestMapping(value = "/connection", method = RequestMethod.GET)
    public ConnectionListResponse connectionList(@IdPermission @PathVariable String instanceId) {
        ConnectionListResponse response = new ConnectionListResponse();
        try {
            response = performanceService.getConnectionList(instanceId);
        } catch (Exception e) {
            LOGGER.error("get instance performance connection list error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
}
