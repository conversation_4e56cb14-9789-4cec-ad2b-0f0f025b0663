package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogErrorDetails;
import com.baidu.bce.internalsdk.rds.model.errorlog.Errorlog;
import com.baidu.bce.internalsdk.rds.model.errorlog.OpenapiErrorlogListResponse;
import com.baidu.bce.logic.rds.model.DownloadUrlResponse;
import com.baidu.bce.logic.rds.model.Url;
import com.baidu.bce.logic.rds.service.ErrorlogService;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogDetailRequest;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

@RestController
@RequestMapping("/v1/instance/{instanceId}/errorlogs")
public class OpenApiErrorlogController extends BaseController{
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiErrorlogController.class);

    @Autowired
    private ErrorlogService errorlogService;
    /**
      * 获取错误日志详情
      * 该接口用于获取指定实例的错误日志详情信息。
      * 
      * @param instanceId 实例ID，用于标识需要查询错误日志的实例
      * @param logDetailRequest 请求体，包含查询错误日志所需的详细信息
      * @return 返回错误日志详情
      */

    @ApiOperation(value = "获取错误日志详情")
    @RequestMapping(value = "/details", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogErrorDetails getErrorLogDetails(@IdPermission @IdMapper @PathVariable String instanceId
            , @RequestBody @Valid LogDetailRequest logDetailRequest) {
        LOGGER.debug("log errorLogDetail.");
        LogErrorDetails response = new LogErrorDetails();
        try {
            logDetailRequest.setInstanceId(instanceId);
            response = errorlogService.getErrorLogDetails(logDetailRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取错误日志列表
     * 获取指定实例和时间的错误日志列表
     * 
     * @param instanceId 实例ID
     * @param datetime 日志时间
     * @param from 请求参数from（可选）
     * @return 错误日志列表响应
     */

    @ApiOperation(value = "获取错误日志列表")
    @RequestMapping(value = "/logList/{datetime}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public OpenapiErrorlogListResponse getList(@IdPermission @PathVariable @IdMapper String instanceId,
                                        @PathVariable String datetime,
                                        @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get errorlog list. instanceId: {}", instanceId);
        OpenapiErrorlogListResponse response = null;
        try {
            response = errorlogService.list2(instanceId, datetime);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取错误日志下载详情
      * 根据提供的instanceId、logId和downloadValidTimeInSec获取错误日志的下载详情
      * 
      * @param instanceId 实例ID
      * @param logId 日志ID
      * @param downloadValidTimeInSec 下载有效期（秒）
      * @param from 请求来源
      * @return 返回包含下载链接和过期时间的Url对象
      * @throws RDSExceptions.ParamValidationException 当downloadValidTimeInSec不是正整数时抛出
      */

    @ApiOperation(value = "获取错误日志下载详情")
    @RequestMapping(value = "/download_url/{logId:.+}/{downloadValidTimeInSec}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public Url detail(@IdPermission @PathVariable @IdMapper String instanceId,
                                      @PathVariable String logId,
                                      @PathVariable Integer downloadValidTimeInSec,
                                      @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get errorlog detail. instanceId: {}, logId: {}", instanceId, logId);
        DownloadUrlResponse response = new DownloadUrlResponse();
        Url url = null;
        if (downloadValidTimeInSec < 0){
            throw new RDSExceptions.ParamValidationException("downloadValidTimeInSec not positive integer");
        }
        try {
            Errorlog errorlog = errorlogService.detail(
                    instanceId, logId, downloadValidTimeInSec).getErrorlog();
            url = new Url().url(errorlog.getDownloadUrl()).expire(errorlog.getDownloadExpires());
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return url;
    }
}
