package com.baidu.bce.logic.rds.controller;


import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.rollback.CheckTimeResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.PreCheckTimeRequest;
import com.baidu.bce.internalsdk.rds.model.rollback.RollbackListResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlRollbackResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlRollbacksRequest;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlTaskDetailResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.TaskParamResponse;
import com.baidu.bce.logic.rds.service.RdsSqlRollbackService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(value = "RDS sql回滚功能")
@RestController
@RequestMapping("/api/rds/flashback")
public class ConsoleApiSqlRollbackController {

    @Autowired
    private RdsSqlRollbackService rollbackService;
    /**
      * 创建Sql闪回任务
      * 创建Sql闪回任务的接口，通过POST请求，传入instanceId和任务请求信息，返回任务创建结果。
      * 
      * @param instanceId 实例ID，用于标识要操作的数据库实例
      * @param request Sql闪回任务的请求信息，包括任务名称、任务描述等
      * @return 返回任务创建结果，包括任务ID、任务状态等信息
      */

    @ApiOperation("创建Sql闪回任务")
    @RequestMapping(value = "/{instanceId}/create", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SqlRollbackResponse> rollbackTask(@IdPermission @PathVariable @IdMapper String instanceId,
                                                               @Valid @RequestBody SqlRollbacksRequest request) {
        EdpResultResponse<SqlRollbackResponse> resultResponse = new EdpResultResponse<>();


        resultResponse.setResult(rollbackService.rollbackTask(instanceId, request));


        return resultResponse;
    }
    /**
     * 闪回任务的前置时间检查
     * 用于检查闪回任务的前置时间是否符合要求
     * 
     * @param instanceId 实例ID
     * @param request 前置时间检查请求参数
     * @return 检查时间的结果
     */

    @ApiOperation("闪回任务的前置时间检查")
    @RequestMapping(value = "/{instanceId}/checkTime", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<CheckTimeResponse> checkTime(@IdPermission @PathVariable @IdMapper String instanceId,
                                                          @RequestBody PreCheckTimeRequest request) {

        EdpResultResponse<CheckTimeResponse> resultResponse = new EdpResultResponse<>();


        resultResponse.setResult(rollbackService.checkTime(instanceId, request));


        return resultResponse;

    }
    /**
      * 查询sql闪回任务列表
      * 通过GET请求查询指定实例的sql闪回任务列表
      * 
      * @param instanceId 实例ID
      * @param marker 分页查询的起始位置标记
      * @param maxKeys 分页查询的最大条目数
      * @return EdpResultResponse<RollbackListResponse> 查询结果
      */

    @ApiOperation("查询 sql闪回任务列表")
    @RequestMapping(value = "/{instanceId}/list", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<RollbackListResponse> taskList(@IdPermission @PathVariable @IdMapper String instanceId,
                                                            @RequestParam(value = "marker") Integer marker,
                                                            @RequestParam(value = "maxKeys") Integer maxKeys) {
        EdpResultResponse<RollbackListResponse> resultResponse = new EdpResultResponse<>();
        String from = "";
        resultResponse.setResult(rollbackService.taskList(instanceId, marker, maxKeys, from));



        return resultResponse;
    }
    /**
      * 查询任务详情
      * 通过任务ID和实例ID查询任务详情
      * 
      * @param instanceId 实例ID
      * @param taskID 任务ID
      * @return EdpResultResponse<SqlTaskDetailResponse> 返回任务详情
      */

    @ApiOperation("查询任务详情")
    @RequestMapping(value = "/{instanceId}/process", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<SqlTaskDetailResponse> detail(@IdPermission @PathVariable @IdMapper String instanceId,
                                                           @RequestParam(value = "taskID") String taskID) {
        EdpResultResponse<SqlTaskDetailResponse> resultResponse = new EdpResultResponse<>();


        resultResponse.setResult(rollbackService.detail(instanceId, taskID));



        return resultResponse;
    }
    /**
     * 查询任务参数
     * 根据实例ID和任务ID查询任务参数
     * 
     * @param instanceId 实例ID
     * @param taskID 任务ID
     * @return EdpResultResponse<TaskParamResponse> 返回查询结果
     */

    @ApiOperation("查询任务参数")
    @RequestMapping(value = "/{instanceId}/parameter", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<TaskParamResponse> getTaskParameters(
            @IdPermission @PathVariable @IdMapper String instanceId,
            @RequestParam(value = "taskID") String taskID) {

        EdpResultResponse<TaskParamResponse> resultResponse = new EdpResultResponse<>();

        resultResponse.setResult(rollbackService.getTaskParameters(instanceId, taskID));


        return resultResponse;

    }

}
