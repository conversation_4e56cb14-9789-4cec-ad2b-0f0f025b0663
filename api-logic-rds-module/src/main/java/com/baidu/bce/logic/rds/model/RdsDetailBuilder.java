/*
 * Copyright (C) 2015 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.model;

import com.baidu.bce.console.finance.service.FinanceService;
import com.baidu.bce.console.order.service.AbstractDetailBuilder;
import com.baidu.bce.console.order.service.OrderService;
import com.baidu.bce.console.order.service.PriceService;
import com.baidu.bce.console.order.util.OrderConstant;
import com.baidu.bce.internalsdk.order.model.Flavor;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.PriceType;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by machuanxia on 2014/12/31.
 */
public class RdsDetailBuilder extends AbstractDetailBuilder {

    private static final Logger LOGGER = LoggerFactory.getLogger(RdsDetailBuilder.class);

    private boolean isPostPay = OrderConstant.PAY_TYPE_POSTPAY.equalsIgnoreCase(order.getProductType());

    public RdsDetailBuilder(String uuid) {
        super(uuid);
    }

    public RdsDetailBuilder(OrderService orderService, PriceService priceService,
                            FinanceService financeService, Order order) {
        super(orderService, priceService, financeService, order);
    }

    @Override
    protected String serviceType() {
        if (ServiceType.RDS_REPLICA.name().equalsIgnoreCase(order.getServiceType())) {
            return ServiceType.RDS_REPLICA.name();
        } else if (ServiceType.RDS_PROXY.name().equalsIgnoreCase(order.getServiceType())) {
            return ServiceType.RDS_PROXY.name();
        } else {
            return ServiceType.RDS.name();
        }
    }

    /**
     * 显示可用区域 (从Item的Flavor里取出azone)
     */
    @Override
    protected String logicalZone(Order.Item item) {
        String logicalZone = "";
        final Flavor flavor = item.getFlavor();
        for (Flavor.FlavorItem flavorItem : flavor) {
            if ("azone".equalsIgnoreCase(flavorItem.getName())) {
                logicalZone = flavorItem.getValue();
                break;
            }
        }
        return logicalZone;
    }

    @Override
    protected String unitPriceShow(Order.Item item) {
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setGroupingUsed(false);
        numberFormat.setMaximumFractionDigits(10);
        if (isPostPay) {
            // 此段代码是临时代码，若有问题使用原有逻辑返回价格
            try {

                // fwh 的价格直接用item的
                if (item.getRegion().equals("fwh")) {
                    return item.getPricingDetail().getCpt1Price().toString() + "元/分钟";
                }

            } catch (Exception e) {
                LOGGER.error("unitPriceShow error", e);
            }
            BigDecimal price = priceService.getCpt1Price(item.getRegion(),
                    ServiceType.valueOf(serviceType()),
                    "minute",
                    NumberUtils.INTEGER_ONE,
                    new Date(),
                    item.getFlavor());
            return numberFormat.format(price) + "元/分钟";
        }

        return item.getUnitPrice() + "元/台";
    }

    @Override
    protected List<String> chargeType(Order.Item item) {
        return isPostPay ? Arrays.asList(PriceType.CPT1.name()) :
                Arrays.asList(PriceType.CPT2.name(), PriceType.CPC.name());
    }

    @Override
    protected List<String> configuration(Order.Item item) {
        List<String> configuration = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(item.getFlavor())) {
            for (Flavor.FlavorItem flavorItem : item.getFlavor()) {
                if (ServiceType.RDS_PROXY.name().equals(serviceType())) {
                    if (flavorItem.getName().equals("nodeAmount")) {
                        configuration.add("资源规格: " + flavorItem.getValue() + "节点");
                        break;
                    }
                    continue;
                }
                if (flavorItem.getName().equals("cpu")) {
                    configuration.add("CPU: " + flavorItem.getValue() + "核");
                } else if (flavorItem.getName().equals("memory")) {
                    if (flavorItem.getScale().intValue() != 1) {
                        configuration.add("内存: " + flavorItem.getScale() + "GB");
                    } else {
                        if (flavorItem.getValue()
                                .substring(flavorItem.getValue().length() - 1).equalsIgnoreCase("g")) {
                            configuration.add("内存: " + flavorItem.getValue()
                                    .substring(0, flavorItem.getValue().length() - 1) + "GB");
                        } else {
                            configuration.add("内存: " + flavorItem.getValue()
                                    .substring(0, flavorItem.getValue().length() - 1) + "GB");
                        }
                    }

                } else if (flavorItem.getName().equals("disk")) {
                    configuration.add("磁盘: " + flavorItem.getScale() + "GB");
                } else if (flavorItem.getName().equals("sourceInstanceId")) {
                    if (StringUtils.isNotEmpty(flavorItem.getValue())) {
                        configuration.add("原实例: " + flavorItem.getValue());
                    }
                }
            }
        }
        return configuration;
    }

}
