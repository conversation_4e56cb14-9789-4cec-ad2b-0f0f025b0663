package com.baidu.bce.logic.rds.model;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BackupPolicyRequest {

    @NotNull
    @IdPermission
    private String instanceId;

    @Valid
    private SnapshotPolicy backupPolicy;

    @Override
    public String toString() {
        return "DashboardBackupPolicyRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", backupPolicy=" + backupPolicy +
                '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public SnapshotPolicy getBackupPolicy() {
        return backupPolicy;
    }

    public void setBackupPolicy(SnapshotPolicy backupPolicy) {
        this.backupPolicy = backupPolicy;
    }
}
