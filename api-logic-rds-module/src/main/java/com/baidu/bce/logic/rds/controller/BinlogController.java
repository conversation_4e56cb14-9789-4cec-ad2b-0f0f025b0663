package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogDateTime;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogListResponse;
import com.baidu.bce.logic.rds.service.BinlogService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;

/**
 * Created by luping03 on 17/11/3.
 */
@RestController
@RequestMapping("/v1/rds/instances/{instanceId}/binlogs")
public class BinlogController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private BinlogService binlogService;
    
    /**
     * 获取日志列表
     * 根据instanceId和datetime获取日志列表，支持分页查询
     * 
     * @param instanceId 实例ID，用于标识要查询的日志实例
     * @param datetime 日志时间，用于筛选日志的时间范围
     * @param from 分页查询起始位置，默认为空字符串
     * @return 日志列表响应对象
     */
    @ApiOperation(value = "获取日志列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public BinlogListResponse getList(@IdPermission @PathVariable String instanceId,
                                      @RequestParam String datetime,
                                      @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log list. instanceId: {}", instanceId);
        BinlogListResponse response = null;
        try {
            response = binlogService.list(instanceId, datetime);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取日志详情
     * 根据日志ID获取日志详情信息
     * 
     * @param instanceId 实例ID
     * @param logId 日志ID
     * @param downloadValidTimeInSec 下载有效时间
     * @param from 请求来源
     * @return 日志详情响应
     */

    @ApiOperation(value = "获取日志详情")
    @RequestMapping(value = "/{logId:.+}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public BinlogGetResponse detail(@IdPermission @PathVariable String instanceId,
                                    @PathVariable String logId,
                                    @RequestParam Integer downloadValidTimeInSec,
                                    @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log detail. instanceId: {}, logId: {}", instanceId, logId);
        BinlogGetResponse response = null;
        try {
            response = binlogService.detail(instanceId, logId, downloadValidTimeInSec);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 检查时间是否可以作为克隆实例的备份恢复时间点
     * 该函数用于检查给定的时间是否可以作为克隆实例的备份恢复时间点
     * 
     * @param instanceId 实例ID
     * @param dateTime 日志日期时间
     * @param from 来源标识（可选）
     */

    @ApiOperation(value = "检查时间是否可以作为克隆实例的备份恢复时间点")
    @RequestMapping(value = "/check", method = RequestMethod.POST)
    public void check(@PathVariable String instanceId,
                      @RequestBody @Valid BinlogDateTime dateTime,
                      @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get log detail. instanceId: {}", instanceId);
        try {
            binlogService.check(instanceId, dateTime);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
}
