package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.core.BceFormat;
import com.baidu.bce.internalsdk.rds.model.binlog.Binlog;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogDateTime;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogListResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.DashboardBinlogCheckRequest;
import com.baidu.bce.internalsdk.rds.model.binlog.DashboardBinlogListRequest;
import com.baidu.bce.internalsdk.rds.model.binlog.DashboardBinlogUrlRequest;
import com.baidu.bce.internalsdk.rds.model.binlog.Url;
import com.baidu.bce.logic.rds.service.BinlogService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

/**
 * Created by hejianbin on 2014/6/20.
 */
@Controller
@RequestMapping(value = "/api/rds/binlog", produces = {"application/json"})
@Api(value = "RDS Dashboard Binlog管理API")
public class ConsoleApiBinlogController {

    @Autowired
    private BinlogService binlogService;
    /**
      * 获取binlog列表
      * 根据请求参数获取binlog分页列表
      *
      * @param request 请求参数，包含分页信息和时间信息
      * @return EdpPageResultResponse<Binlog> 返回binlog分页列表的结果
      */

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpPageResultResponse<Binlog> list(@IdPermission @Valid @RequestBody DashboardBinlogListRequest request) {
        EdpPageResultResponse<Binlog> listResponse = new EdpPageResultResponse<>();
        request.setStartDateTime(BceFormat.getDateTimeFormat().format(request.getDate()));
        BinlogListResponse binlogListResponse = binlogService.list(request);
        listResponse.getPage().setResult(binlogListResponse.getBinlogs());
        listResponse.getPage().setTotalCount(binlogListResponse.getTotalCount());
        listResponse.getPage().setPageNo(Integer.parseInt(binlogListResponse.getPageNo()));
        listResponse.getPage().setPageSize(binlogListResponse.getPageSize());
        return listResponse;
    }
    /**
      * 下载URL接口
      * 通过请求参数获取对应binlog的下载链接
      * 
      * @param request 包含instanceId, binlogId, downloadValidTime的请求体
      * @return 包含下载链接和过期时间的响应体
      */

    @RequestMapping(value = "download_url", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Url> downloadUrl(@IdPermission @Valid @RequestBody DashboardBinlogUrlRequest request) {
        EdpResultResponse<Url> response = new EdpResultResponse<>();
        Binlog binlog = binlogService.detail(request.getInstanceId(), request.getBinlogId(),
                request.getDownloadValidTime() * 60).getBinlog();
        Url url = new Url().url(binlog.getDownloadUrl()).expire(binlog.getDownloadExpires());
        response.withResult(url);
        return response;
    }
    /**
     * 检查接口
     * 该接口用于检查binlog信息
     * 
     * @param request 检查请求参数，包含日期和实例ID
     * @return 返回检查结果，封装在EdpResultResponse对象中
     */

    @RequestMapping(value = "check", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> check(@Valid @RequestBody DashboardBinlogCheckRequest request) {
        EdpResultResponse<Boolean> response = new EdpResultResponse<Boolean>();
        BinlogDateTime binlogDateTime = new BinlogDateTime();
        binlogDateTime.setDatetime(request.getDate());
        binlogService.check(request.getInstanceId(), binlogDateTime);
        return response.withResult(Boolean.TRUE);
    }

}
