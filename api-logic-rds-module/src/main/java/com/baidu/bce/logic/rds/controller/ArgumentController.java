package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyHistoryResponse;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyItem;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyRequest;
import com.baidu.bce.logic.rds.service.ArgumentService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.model.argument.ConfigList;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * Created by luping03 on 17/10/12.
 */
@RestController
@RequestMapping("/v1/rds/instances/{instanceId}/arguments")
public class ArgumentController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ArgumentController.class);

    @Autowired
    private ArgumentService argumentService;
    
    /**
     * 获取配置信息列表
     * 根据instanceId、keyword、from等参数获取配置信息列表，并处理响应头ETag
     * 
     * @param instanceId 实例ID
     * @param keyword 关键字（可选）
     * @param from 来源（可选，默认值为""）
     * @param servletResponse HttpServletResponse对象，用于设置响应头ETag
     * @return ConfigList 返回配置信息列表
     */
    @ApiOperation(value = "获取配置信息列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ConfigList getList(@IdPermission @IdMapper @PathVariable String instanceId,
                              @RequestParam(required = false) String keyword,
                              @RequestParam(required = false, defaultValue = "") String from,
                              HttpServletResponse servletResponse) {
        LOGGER.debug("get arguments list. instanceId is {}", instanceId);
        ConfigList response = null;
        try {

            // api给applyMethod参数设置默认值
//            if (org.apache.commons.lang.StringUtils.isNotBlank(from) && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
//                if (request != null && request.getParameters() != null) {
//                    for (ConfigModifyItem each : request.getParameters()) {
//                        if (StringUtils.isBlank(each.getApplyMethod())) {
//                            each.setApplyMethod("immediate");
//                            LOGGER.debug("modify params : Append 'ApplyMethod' params default value 'immediate'.");
//                        }
//                    }
//                }
//            }

            response = argumentService.list(instanceId, keyword, from);
            if (response.getItems() != null && !response.getItems().isEmpty()) {
                servletResponse.setHeader("ETag", response.getItems().get(0).getEtag());
                response.setETag(response.getItems().get(0).getEtag());
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 修改配置信息
      * 修改指定实例的配置信息。支持对配置参数进行增加、修改和删除操作。
      * 
      * @param instanceId 实例ID
      * @param request 修改配置请求体，包含要修改的配置项
      * @param ETag 请求头中的ETag，用于乐观锁控制
      * @param from 请求来源标识，可选参数，默认值为空字符串
      * @throws Exception 如果在修改配置过程中发生异常，将抛出异常
      */

    @ApiOperation(value = "修改配置信息")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void modify(@IdPermission @IdMapper @PathVariable String instanceId,
                       @Valid @RequestBody ConfigModifyRequest request,
                       @RequestHeader ("x-bce-if-match") String ETag,
                       @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("modify argument. instanceId is {}", instanceId);
        try {

            // api给applyMethod参数设置默认值
            if (org.apache.commons.lang.StringUtils.isNotBlank(from) && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
                if (request != null && request.getParameters() != null) {
                    for (ConfigModifyItem each : request.getParameters()) {

                        if (StringUtils.isBlank(each.getApplyMethod())) {
                            each.setApplyMethod("immediate");
                            each.setEtag(ETag);
                            LOGGER.debug("modify params : Append 'ApplyMethod' params default value 'immediate'.");
                        }
                    }
                }
            }

            argumentService.modify(instanceId, request, ETag);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 修改历史
      * 根据实例ID获取参数修改历史
      * 
      * @param instanceId 实例ID
      * @param from 分页起始位置，默认为空字符串
      * @return 参数修改历史响应对象
      */

    @ApiOperation(value = "修改历史")
    @RequestMapping(value = "/history", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public ConfigModifyHistoryResponse history(@IdPermission @PathVariable String instanceId,
                                               @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("get arguments modified history. instanceId is {}", instanceId);
        ConfigModifyHistoryResponse response = null;
        try {
            response = argumentService.history(instanceId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
}
