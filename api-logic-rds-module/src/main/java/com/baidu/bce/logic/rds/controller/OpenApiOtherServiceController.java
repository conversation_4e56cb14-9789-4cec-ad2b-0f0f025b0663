package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.core.utils.JsonConvertUtil;
import com.baidu.bce.logic.rds.model.HomeStatisticResponse;
import com.baidu.bce.logic.rds.model.IdMapperResponse;
import com.baidu.bce.logic.rds.model.InstanceIdListRequest;
import com.baidu.bce.logic.rds.model.OpenApiSubnetListResponse;
import com.baidu.bce.logic.rds.model.OpenApiSubnetVo;
import com.baidu.bce.logic.rds.model.ResultResponse;
import com.baidu.bce.logic.rds.model.VpcListResponse;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.model.GroupWhiteAccountResult;
import com.baidu.bce.logic.rds.service.model.RdsListRequest;
import com.baidu.bce.logic.rds.service.model.WhiteAccountResult;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtensionList;
import com.baidu.bce.logic.rds.service.model.otherservice.AutoRenewDetail;
import com.baidu.bce.logic.rds.service.model.otherservice.ServiceParam;
import com.baidu.bce.logic.rds.service.model.otherservice.ZoneDetailList;
import com.baidu.bce.logic.rds.service.model.otherservice.DiskIoTypeResponse;
import com.baidu.bce.logic.rds.service.model.otherservice.ZoneRequest;
import com.baidu.bce.logic.rds.service.model.tag.AssignTagRequest;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by luping03 on 17/10/14.
 */
@RestController
@RequestMapping("/v1")
public class OpenApiOtherServiceController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private OthersService othersService;
    /**
     * 获取用户zone列表
     * 获取用户zone列表，包括多可用区及机房可用情况
     * 
     * @return ZoneDetailList 用户zone列表
     */

    @ApiOperation(value = "获取用户zone列表，包括多可用区及机房可用情况")
    @RequestMapping(value = "/zone", method = RequestMethod.GET)
    public ZoneDetailList zoneList() {
        String from = "api";
        LOGGER.debug("zone list.");
        ZoneDetailList zones = null;
        try {
            zones = othersService.zoneList(from);
            if (zones.getZones() != null) {
                for (ZoneDetailList.ZoneDetail zoneDetail : zones.getZones()) {
                    if (zoneDetail.getApiZoneNames() != null && !zoneDetail.getApiZoneNames().isEmpty()) {
                        zoneDetail.setZoneNames(zoneDetail.getApiZoneNames());
                    }
                }
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return zones;
    }
    /**
     * 获取用户子网列表
     * 获取用户子网列表接口，支持通过zoneName和vpcId进行筛选
     * 
     * @param zoneName 区域名称，非必传参数
     * @param vpcId VPC的ID，非必传参数
     * @return 返回用户子网列表的响应对象
     */

    @ApiOperation(value = "获取用户子网列表")
    @RequestMapping(value = "/subnet", method = RequestMethod.GET)
    public OpenApiSubnetListResponse subnetList(@RequestParam(required = false) String zoneName,
                                                @RequestParam(required = false) String vpcId) {
        String from = "api";
        LOGGER.debug("subnet list.zoneName:{}, vpcId:{}", zoneName, vpcId);
        OpenApiSubnetListResponse subnets = new OpenApiSubnetListResponse();
        try {
            List<SubnetVo> subnetVos = othersService.subnetList(zoneName, vpcId, from);
            if (subnetVos != null) {
                subnets.setSubnets(new ArrayList<OpenApiSubnetVo>(subnetVos.size()));
                for (SubnetVo subnetVo : subnetVos) {
                    OpenApiSubnetVo openApiSubnetVo = new OpenApiSubnetVo();
                    openApiSubnetVo.setName(subnetVo.getName());
                    openApiSubnetVo.setSubnetId(subnetVo.getShortId());
                    openApiSubnetVo.setCidr(subnetVo.getCidr());
                    openApiSubnetVo.setZoneName(subnetVo.getAz());
                    openApiSubnetVo.setVpcId(subnetVo.getVpcShortId());

                    subnets.getSubnets().add(openApiSubnetVo);
                }
            }
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return subnets;
    }
    /**
     * 设置标签
     * 为指定资源设置标签
     * 
     * @param request 标签设置请求体
     * @param from 请求来源
     */

    @ApiOperation(value = "设置标签")
    @RequestMapping(value = "/tags", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void assignTags(@IdPermission @RequestBody @Valid AssignTagRequest request,
                           @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("assign tags.");
        try {
            othersService.assignTags(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 获取用户的操作白名单
      * 根据传入的参数，调用服务获取用户的操作白名单
      * 
      * @param from 来源，默认为空字符串
      * @param feature 功能标识
      * @return 返回用户的操作白名单结果
      */

    @ApiOperation(value = "获取用户的操作白名单")
    @RequestMapping(value = "/whiteAccount", method = RequestMethod.GET)
    public WhiteAccountResult isWhiteAccount(@RequestParam(required = false, defaultValue = "") String from,
                                             @RequestParam(required = false) String feature) {
        LOGGER.debug("is white account.");
        WhiteAccountResult whiteAccountResult = null;
        try {
            whiteAccountResult = othersService.isWhiteAccount(feature);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return whiteAccountResult;
    }
    /**
     * 获取用户实例组的操作白名单
     * 该函数用于获取用户实例组的操作白名单信息
     * 
     * @param from 请求来源，默认为空字符串
     * @return 返回GroupWhiteAccountResult对象，包含操作白名单信息
     */

    @ApiOperation(value = "获取用户实例组的操作白名单")
    @RequestMapping(value = "/groupWhiteAccount", method = RequestMethod.GET)
    public GroupWhiteAccountResult isGroupWhiteAccount(@RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("is group white account.");
        GroupWhiteAccountResult whiteAccountResult = null;
        try {
            whiteAccountResult = othersService.isGroupWhiteAccount();
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return whiteAccountResult;
    }
    /**
     * 获取用户磁盘最大值白名单接口
     * 该接口用于检查用户磁盘最大值是否超过白名单限制
     * 
     * @return 返回检查结果，ResultResponse封装了Boolean类型的结果
     * @throws BceInternalResponseException 如果检查过程中发生内部异常
     */

    @ApiOperation(value = "获取用户磁盘最大值白名单")
    @RequestMapping(value = "/checkDiskMaxLimit", method = RequestMethod.POST)
    public ResultResponse<Boolean> checkDiskLimit() {
        ResultResponse response = new ResultResponse();
        try {
            response.setResult(othersService.checkDiskLimit());
        } catch (BceInternalResponseException e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取用户vpc列表
     * 此函数用于获取用户的vpc列表信息
     * 
     * @param from 请求参数，用于指定查询的来源，默认为空字符串
     * @return 返回VpcListResponse对象，包含vpc列表信息
     */

    @ApiOperation(value = "获取用户vpc列表")
    @RequestMapping(value = "/vpc", method = RequestMethod.GET)
    public VpcListResponse vpcList(@RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("vpc list.");
        VpcListResponse vpcs = new VpcListResponse();
        try {
            vpcs.setVpc(othersService.vpcList());
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return vpcs;
    }

    @io.swagger.annotations.ApiOperation("查看子网的剩余IP个数")
    @RequestMapping(method = RequestMethod.GET, value = "/subnet/detail")
    public SubnetVo countAvailableIps(@RequestParam(required = true, defaultValue = "") String subnetId) {
        SubnetVo result = null;
        try {
            LOGGER.debug("countAvailableIps");
            result = othersService.subnetIpUsed(subnetId);
        } catch (BceInternalResponseException e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return result;
    }
    /**
      * 查询带有自动续费信息的实例列表（分页）
      * 通过pageNo方式进行分页查询，获取带有自动续费信息的实例列表
      * 
      * @param daysToExpiration 到期天数，用于筛选即将到期的实例
      * @param order 排序字段
      * @param orderBy 排序方式，asc或desc
      * @param pageNo 当前页码
      * @param pageSize 每页记录数
      * @param filterMapStr 过滤条件字符串，JSON格式
      * @return 实例列表的分页结果
      */

    @RequestMapping(value = "/autoRenew", method = RequestMethod.GET, params = {"manner=page"})
    @ApiOperation(value = "实例列表: 通过pageNo方式分页")
    public LogicPageResultResponse<AutoRenewDetail> listInstanceWithAutoRenewInfo(
            @RequestParam(required = false, defaultValue = "-1") Integer daysToExpiration,
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String filterMapStr) {
        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, order, orderBy, pageNo, pageSize);

        LogicPageResultResponse<AutoRenewDetail> pageResultResponse = null;
        try {
            if (StringUtils.isNotEmpty(filterMapStr)) {
                filterMapStr = StringEscapeUtils.unescapeHtml(filterMapStr).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list instance[page], after unescapeHtml filterMaperStr is : {}", filterMapStr);
                InstanceController.FilterMap filterMap =
                        JsonConvertUtil.fromJSON(filterMapStr, InstanceController.FilterMap.class);
                listRequest.setFilterMap(filterMap);

                if (filterMap != null && filterMap.get("instanceId") != null) {
                    filterMap.put("instanceShortId", filterMap.get("instanceId"));
                    filterMap.remove("instanceId");
                }
            }
            listRequest.setServiceType(ServiceType.RDS.name());
            pageResultResponse = othersService.listInstanceForAutoRenew(listRequest);
        } catch (Exception e) {
            LOGGER.error("List instance error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return pageResultResponse;
    }
    /**
     * Id映射接口
     * 该接口用于将输入的实例ID列表映射为对应的ID列表
     * 
     * @param request 实例ID列表请求对象
     * @param from 请求来源，可选参数，默认为空字符串
     * @return IdMapperResponse 对象，包含映射后的ID列表
     */

    @ApiOperation(value = "Id映射")
    @RequestMapping(value = "/idMapper", method = RequestMethod.POST)
    public IdMapperResponse idMapper(@RequestBody @Valid InstanceIdListRequest request,
                                     @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("idMapper. instanceIds:" + request);
        IdMapperResponse response = new IdMapperResponse();
        try {
            response.setIdMap(othersService.getIdMap(request.getInstanceIds()));
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * Id映射接口
      * 用于将实例ID映射为其他格式，仅限于查看自己的实例
      * 
      * @param request 包含实例ID列表的请求体
      * @param from 请求来源，默认为空字符串
      * @return IdMapperResponse对象，包含映射后的ID信息
      */

    @ApiOperation(value = "Id映射，只能查看自己的实例")
    @RequestMapping(value = "/idMapperV2", method = RequestMethod.POST)
    public IdMapperResponse idMapperV2(@RequestBody @Valid InstanceIdListRequest request,
                                     @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("idMapper. instanceIds:" + request);
        IdMapperResponse response = new IdMapperResponse();
        try {

            response.setIdMap(othersService.getIdMapV2(request.getInstanceIds()));

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取console服务账号信息
      * 通过GET请求获取console服务的账号信息
      * 
      * @param from 来源，默认为空字符串
      * @return ServiceParam 返回服务参数对象
      */

    @ApiOperation(value = "console服务账号信息")
    @RequestMapping(value = "/console_service_info", method = RequestMethod.GET)
    public ServiceParam consoleServiceInfo(@RequestParam(required = false, defaultValue = "") String from) {
        ServiceParam response = null;
        try {
            response = othersService.getServiceParam();
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取rds服务账号信息
      * 根据请求获取rds服务的账号信息
      * 
      * @param from 请求来源，默认为空字符串
      * @return rds服务账号信息
      */

    @ApiOperation(value = "rds服务账号信息")
    @RequestMapping(value = "/rds_default_service_info", method = RequestMethod.GET)
    public ServiceParam rdsServiceInfo(@RequestParam(required = false, defaultValue = "") String from) {
        ServiceParam response = null;
        try {
            response = othersService.getServiceParamForRdsBackend();
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    /**
     * 获取角色名称
     * 根据请求获取角色名称列表
     * 
     * @param from 来源参数，默认为空字符串
     * @return 返回角色名称列表的响应对象
     */
    @RequestMapping(value = "/getRoleName", method = RequestMethod.GET)
    public ResultResponse getRoleName(@RequestParam(required = false, defaultValue = "") String from) {
        ResultResponse response = new ResultResponse();
        try {
            List<ServiceParam> serviceParamList = othersService.getRoleName();
            response.setResult(serviceParamList);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }



    /**
     * Id映射on dcc接口
     * Id映射on dcc，提供将hostId映射到对应id的功能
     * 
     * @param from 来源参数，非必需，默认值为空字符串
     * @param hostId 主机Id，必需参数
     * @return IdMapperResponse对象，包含映射后的id信息
     */
    @ApiOperation(value = "Id映射on dcc")
    @RequestMapping(value = "/idMapper/dcc", method = RequestMethod.GET)
    public IdMapperResponse idMapperOnDcc(@RequestParam(required = false, defaultValue = "") String from,
                                          @RequestParam String hostId) {
        IdMapperResponse response = new IdMapperResponse();
        try {
            response.setIdMap(othersService.idMapperOnDcc(hostId));
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 总览页接口
      * 获取RDS实例总览信息，包括实例总数、运行中实例数、磁盘超限实例数、即将过期实例数、已过期实例数、MySQL版实例数、SQL Server版实例数、PostgreSQL版实例数
      * 
      * @param userId 用户ID，非必填
      * @param region 区域，非必填
      * @return HomeStatisticResponse 实例总览信息
      */

    @ApiOperation(value = "总览页接口")
    @RequestMapping(value = "/statistic", method = RequestMethod.GET)
    public HomeStatisticResponse statistic(@RequestParam(required = false, defaultValue = "") String userId,
                                           @RequestParam(required = false, defaultValue = "") String region) {
        HomeStatisticResponse response = new HomeStatisticResponse();

        try {

            // 设置用户id
            LogicUserService.getSubjectToken().getUser().setId(userId);

            // 设置region
            regionConfiguration.setCurrentRegion(region);

            LogicMarkerResultResponse<InstanceAbstract> pageResultResponse =
                    instanceService.listInstanceWithMarkerByMultiKey("-1", 1000, "");

            List<InstanceAbstract> instances = (List<InstanceAbstract>) pageResultResponse.getResult();

            int instanceTotalCount = instances.size();
            int instanceRunningCount = 0;
            int instanceDiskLockCount = 0;
            int instanceWillExpireCount = 0;
            int instanceExpiredCount = 0;
            int instanceMysqlCount = 0;
            int instanceSqlServerCount = 0;
            int instancePGCount = 0;

            for (InstanceAbstract each : instances) {
                if (ObjectUtils.equals("available", each.getInstanceStatus().toLowerCase())) {
                    instanceRunningCount++;
                } else if (ObjectUtils.equals("lockdiskquota", each.getInstanceStatus().toLowerCase())) {
                    instanceDiskLockCount++;

                } else {
                    LOGGER.info("[statistic] instanceStatus is especial {}", each.getInstanceStatus());
                }

                // 只有预付费才有到期概念
                if (ObjectUtils.equals("prepay", each.getProductType().toLowerCase())) {
                    // 即将到期，为0表示今天到期
                    if (each.getExpireDate() < 0) {
                        instanceExpiredCount++;
                    } else if (0 <= each.getExpireDate() && each.getExpireDate() < 7) {
                        instanceWillExpireCount++;
                    }
                }

                // 引擎类型
                String engineLowerCase = each.getEngine().toLowerCase();
                if (ObjectUtils.equals("mysql", engineLowerCase)) {
                    instanceMysqlCount++;
                } else if (ObjectUtils.equals("sqlserver", engineLowerCase)) {
                    instanceSqlServerCount++;
                } else if (ObjectUtils.equals("postgresql", engineLowerCase)) {
                    instancePGCount++;
                } else {
                    LOGGER.info("[statistic] engine is especial {}", each.getEngine());
                }
            }

            response.addStatisticInfo("InstanceTotalCount", String.valueOf(instanceTotalCount), "RDS实例总数");
            response.addStatisticInfo("InstanceRunningCount", String.valueOf(instanceRunningCount), "运行中");
            response.addStatisticInfo("InstanceDiskLockCount", String.valueOf(instanceDiskLockCount), "磁盘超限");
            response.addStatisticInfo("InstanceWillExpireCount", String.valueOf(instanceWillExpireCount), "即将过期");
            response.addStatisticInfo("InstanceExpiredCount", String.valueOf(instanceExpiredCount), "已过期");
            response.addStatisticInfo("InstanceMysqlCount", String.valueOf(instanceMysqlCount), "MySQL版");
            response.addStatisticInfo("InstanceSqlServerCount", String.valueOf(instanceSqlServerCount), "SQL Server版");
            response.addStatisticInfo("InstancePGCount", String.valueOf(instancePGCount), "Postgre SQL版");
        } catch (Exception e) {

            LOGGER.error("get statistic info error, use default info", e);

            // 默认
            response.addStatisticInfo("InstanceTotalCount", "未知", "RDS实例总数");
            response.addStatisticInfo("InstanceRunningCount", "未知", "运行中");
            response.addStatisticInfo("InstanceDiskLockCount", "未知", "磁盘超限");
            response.addStatisticInfo("InstanceWillExpireCount", "未知", "即将过期");
            response.addStatisticInfo("InstanceExpiredCount", "未知", "已过期");
            response.addStatisticInfo("InstanceMysqlCount", "未知", "MySQL版");
            response.addStatisticInfo("InstanceSqlServerCount", "未知", "SQL Service版");
            response.addStatisticInfo("InstancePGCount", "未知", "Postgre SQL版");
        }

        return response;
    }
    /**
     * 通过az获取支持的磁盘类型
     * 接口主函数，通过传入ZoneRequest请求体，获取指定AZ支持的磁盘类型
     *
     * @param request ZoneRequest请求体，包含需要查询的AZ信息
     * @return DiskIoTypeResponse 返回查询结果，包含支持的磁盘类型
     */

    @ApiOperation(value = "通过az获取支持的磁盘类型")
    @RequestMapping(value = "/getDiskIoTypeByZone", method = RequestMethod.POST)
    public DiskIoTypeResponse getDiskIoTypeByZone(@RequestBody ZoneRequest request) {
        for (String zone : request.getZoneNames()) {
            String logicalZone = othersService.apiZoneTologicalZone(zone);
            request.setZone(request.getZone() + logicalZone + ",");
        }
        request.setZone(request.getZone().substring(0, request.getZone().length() - 1));
        DiskIoTypeResponse response = othersService.getDiskIoTypeByZone(request.getZone());
        return response;
    }
    /**
      * 获取RDS实例详情
      * 根据传入的实例ID列表，获取对应的RDS实例详情信息
      * 
      * @param request 包含实例ID列表的请求体
      * @return 包含RDS实例详情列表的响应体
      * @throws BceInternalResponseException 如果实例状态不满足条件，抛出异常
      */

    @RequestMapping(value = "/rds/instance/details", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<InstanceExtensionList> details(
            @IdPermission @Valid @RequestBody InstanceIdListRequest request) {
        InstanceExtensionList instanceExtensionList = new InstanceExtensionList();
        List<InstanceExtension> list = new ArrayList<>();
        for (int i = 0; i < request.getInstanceIds().size(); i++) {
            InstanceExtension instance = new InstanceExtension();
            try {
                instance = instanceService.getInstanceExtension(request.getInstanceIds().get(i));
            } catch (BceInternalResponseException e) {
                // 若此时报错码为 InstanceAlreadyDeleted，需特殊处理 返回404
                if (StringUtils.isNotEmpty(e.getCode()) && "InstanceAlreadyDeleted".equalsIgnoreCase(e.getCode())) {
                    LOGGER.info("bcm get instance detail, instance status is deleted, code is {}", e.getCode());
                    String code = "InstanceStatusUnsatisfied";
                    String message = "Instance status is not satisfied";
                    throw new BceInternalResponseException(code, HttpStatus.NOT_FOUND.value(), message);
                }
            }
            if (RDSConstant.RDS_TO_BCM_UNAVIALABLE_STATUS.contains(instance.getInstanceStatus())) {
                String code = "InstanceStatusUnsatisfied";
                String message = "Instance status is not satisfied";
                throw new BceInternalResponseException(code, HttpStatus.NOT_FOUND.value(), message);
            } else {
                list.add(i, instance);
            }
        }
        instanceExtensionList.setInstanceExtensionList(list);

        EdpResultResponse<InstanceExtensionList> response = new EdpResultResponse<>();
        response.setResult(instanceExtensionList);
        return response;
    }
}
