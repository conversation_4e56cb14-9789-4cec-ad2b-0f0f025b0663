package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCreateResponse;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.rds.model.blb.GetLbdcClusterResponse;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.core.utils.JsonConvertUtil;
import com.baidu.bce.logic.rds.model.HomeStatisticResponse;
import com.baidu.bce.logic.rds.model.IdMapperResponse;
import com.baidu.bce.logic.rds.model.InstanceIdListRequest;
import com.baidu.bce.logic.rds.model.ResultResponse;
import com.baidu.bce.logic.rds.model.SubnetListResponse;
import com.baidu.bce.logic.rds.model.VpcListResponse;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.GroupWhiteAccountResult;
import com.baidu.bce.logic.rds.service.model.RdsListRequest;
import com.baidu.bce.logic.rds.service.model.WhiteAccountResult;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.otherservice.AutoRenewDetail;
import com.baidu.bce.logic.rds.service.model.otherservice.ServiceParam;
import com.baidu.bce.logic.rds.service.model.otherservice.ZoneDetailList;
import com.baidu.bce.logic.rds.service.model.tag.AssignTagRequest;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * Created by luping03 on 17/10/14.
 */
@RestController
@RequestMapping("/v1/rds")
public class OtherServiceController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private OthersService othersService;

    @Autowired
    private IdMapperService idMapperService;
    /**
     * 设置标签
     * 设置标签的接口方法
     * 
     * @param request 标签设置请求体
     * @param from 来源标识（可选，默认为空字符串）
     */

    @ApiOperation(value = "设置标签")
    @RequestMapping(value = "/tags", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void assignTags(@IdPermission @RequestBody @Valid AssignTagRequest request,
                           @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("assign tags.");
        try {
            othersService.assignTags(request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 获取用户的操作白名单
      * 根据传入的特征参数判断用户是否在白名单中
      * 
      * @param from 来源，默认为空字符串
      * @param feature 特征参数
      * @return WhiteAccountResult 对象，包含用户是否在白名单中的信息
      */

    @ApiOperation(value = "获取用户的操作白名单")
    @RequestMapping(value = "/whiteAccount", method = RequestMethod.GET)
    public WhiteAccountResult isWhiteAccount(@RequestParam(required = false, defaultValue = "") String from,
                                             @RequestParam(required = false) String feature) {
        LOGGER.debug("is white account.");
        WhiteAccountResult whiteAccountResult = null;
        try {
            whiteAccountResult = othersService.isWhiteAccount(feature);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return whiteAccountResult;
    }
    /**
     * 获取用户实例组的操作白名单
     * 用于获取用户实例组的操作白名单信息
     * 
     * @param from 请求来源，非必需参数，默认为空字符串
     * @return GroupWhiteAccountResult 返回用户实例组的操作白名单信息
     */

    @ApiOperation(value = "获取用户实例组的操作白名单")
    @RequestMapping(value = "/groupWhiteAccount", method = RequestMethod.GET)
    public GroupWhiteAccountResult isGroupWhiteAccount(@RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("is group white account.");
        GroupWhiteAccountResult whiteAccountResult = null;
        try {
            whiteAccountResult = othersService.isGroupWhiteAccount();
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return whiteAccountResult;
    }
    /**
      * 获取用户磁盘最大值白名单
      * 验证用户是否在磁盘最大值白名单中
      *
      * @return 返回验证结果
      */

    @ApiOperation(value = "获取用户磁盘最大值白名单")
    @RequestMapping(value = "/checkDiskMaxLimit", method = RequestMethod.POST)
    public ResultResponse<Boolean> checkDiskLimit() {
        ResultResponse response = new ResultResponse();
        try {
            response.setResult(othersService.checkDiskLimit());
        } catch (BceInternalResponseException e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取用户vpc列表
      * 根据请求参数获取用户vpc列表信息
      * 
      * @param from 请求参数，默认为空字符串
      * @return 返回VpcListResponse对象，包含vpc列表信息
      */

    @ApiOperation(value = "获取用户vpc列表")
    @RequestMapping(value = "/vpc", method = RequestMethod.GET)
    public VpcListResponse vpcList(@RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("vpc list.");
        VpcListResponse vpcs = new VpcListResponse();
        try {
            vpcs.setVpc(othersService.vpcList());
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return vpcs;
    }
    /**
     * 获取用户zone列表
     * 获取用户zone列表，包括多可用区及机房可用情况
     * 
     * @param from 请求来源
     * @return ZoneDetailList 用户zone列表信息
     */

    @ApiOperation(value = "获取用户zone列表，包括多可用区及机房可用情况")
    @RequestMapping(value = "/zone", method = RequestMethod.GET)
    public ZoneDetailList zoneList(@RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("zone list.");
        ZoneDetailList zones = null;
        try {
            zones = othersService.zoneList(from);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return zones;
    }
    /**
     * 获取用户子网列表
     * 获取用户子网列表，支持按zoneName和vpcId进行过滤查询
     * 
     * @param zoneName 区域名称，非必填
     * @param vpcId 虚拟私有云ID，非必填
     * @param from 查询起始位置，默认为空字符串
     * @return SubnetListResponse对象，包含用户子网列表
     */

    @ApiOperation(value = "获取用户子网列表")
    @RequestMapping(value = "/subnet", method = RequestMethod.GET)
    public SubnetListResponse subnetList(@RequestParam(required = false) String zoneName,
                                         @RequestParam(required = false) String vpcId,
                                         @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("subnet list.zoneName:{}, vpcId:{}", zoneName, vpcId);
        SubnetListResponse subnets = new SubnetListResponse();
        try {
            subnets.setSubnets(othersService.subnetList(zoneName, vpcId, from));
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return subnets;
    }

    @io.swagger.annotations.ApiOperation("查看子网的剩余IP个数")
    @RequestMapping(method = RequestMethod.GET, value = "/subnet/detail")
    public SubnetVo countAvailableIps(@RequestParam(required = true, defaultValue = "") String subnetId) {
        SubnetVo result = null;
        try {
            LOGGER.debug("countAvailableIps");
            result = othersService.subnetIpUsed(subnetId);
        } catch (BceInternalResponseException e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return result;
    }
    /**
      * 实例列表查询（通过pageNo方式分页）
      * 通过给定的查询参数，分页获取具有自动续费信息的实例列表。
      * 
      * @param daysToExpiration 到期天数，用于筛选即将到期的实例
      * @param order 排序字段
      * @param orderBy 排序方式
      * @param pageNo 当前页码
      * @param pageSize 每页大小
      * @param filterMapStr 过滤条件字符串，可转换为FilterMap对象进行高级筛选
      * @return 分页查询结果，包含自动续费信息的实例列表
      */

    @RequestMapping(value = "/autoRenew", method = RequestMethod.GET, params = {"manner=page"})
    @ApiOperation(value = "实例列表: 通过pageNo方式分页")
    public LogicPageResultResponse<AutoRenewDetail> listInstanceWithAutoRenewInfo(
            @RequestParam(required = false, defaultValue = "-1") Integer daysToExpiration,
            @RequestParam(required = false) String order,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000") Integer pageSize,
            @RequestParam(required = false, defaultValue = "") String filterMapStr) {
        RdsListRequest listRequest = new RdsListRequest(daysToExpiration, order, orderBy, pageNo, pageSize);

        LogicPageResultResponse<AutoRenewDetail> pageResultResponse = null;
        try {
            if (StringUtils.isNotEmpty(filterMapStr)) {
                filterMapStr = StringEscapeUtils.unescapeHtml(filterMapStr).replaceAll("\\\\", "\\\\\\\\");
                LOGGER.info("list instance[page], after unescapeHtml filterMaperStr is : {}", filterMapStr);
                InstanceController.FilterMap filterMap =
                        JsonConvertUtil.fromJSON(filterMapStr, InstanceController.FilterMap.class);
                listRequest.setFilterMap(filterMap);

                if (filterMap != null && filterMap.get("instanceId") != null) {
                    filterMap.put("instanceShortId", filterMap.get("instanceId"));
                    filterMap.remove("instanceId");
                }
            }
            listRequest.setServiceType(ServiceType.RDS.name());
            pageResultResponse = othersService.listInstanceForAutoRenew(listRequest);
        } catch (Exception e) {
            LOGGER.error("List instance error", e);
            LogicRdsExceptionHandler.handle(e);
        }
        return pageResultResponse;
    }
    /**
      * Id映射
      * 将实例ID列表映射为另一套系统中的ID列表
      * 
      * @param request 实例ID列表请求体
      * @param from 来源系统标识（可选）
      * @return Id映射结果
      */

    @ApiOperation(value = "Id映射")
    @RequestMapping(value = "/idMapper", method = RequestMethod.POST)
    public IdMapperResponse idMapper(@RequestBody @Valid InstanceIdListRequest request,
                                     @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("idMapper. instanceIds:" + request);
        IdMapperResponse response = new IdMapperResponse();
        try {
            response.setIdMap(othersService.getIdMap(request.getInstanceIds()));
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * Id映射接口
     * Id映射，只能查看自己的实例
     * 
     * @param request 实例ID列表请求对象
     * @param from 来源，非必需参数，默认为空字符串
     * @return IdMapperResponse 对象，包含映射后的ID信息
     */

    @ApiOperation(value = "Id映射，只能查看自己的实例")
    @RequestMapping(value = "/idMapperV2", method = RequestMethod.POST)
    public IdMapperResponse idMapperV2(@RequestBody @Valid InstanceIdListRequest request,
                                     @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("idMapper. instanceIds:" + request);
        IdMapperResponse response = new IdMapperResponse();
        try {

            response.setIdMap(othersService.getIdMapV2(request.getInstanceIds()));

        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 通过短id获得长id
     * 该接口通过接收短id请求，并返回对应的长id信息
     * 
     * @param request InstanceIdRequest对象，包含需要转换的短id
     * @param from 请求来源，非必须参数，默认为空字符串
     * @return InstanceCreateResponse对象，包含转换后的长id
     */

    @ApiOperation(value = "通过短id获得长id")
    @RequestMapping(value = "/getInstanceUuid", method = RequestMethod.POST)
    public InstanceCreateResponse getInstanceUuid(@RequestBody @Valid InstanceIdRequest request,
                                @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("instanceId:" + request);
        InstanceCreateResponse response = new InstanceCreateResponse();
        try {
            response.setInstanceId(
                    idMapperService.getInstanceUuid(request.getInstanceId())
            );
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取console服务账号信息
      * 该接口用于获取console服务的账号信息
      *
      * @param from 请求来源参数，默认为空字符串
      * @return 返回ServiceParam对象，包含服务账号信息
      */

    @ApiOperation(value = "console服务账号信息")
    @RequestMapping(value = "/console_service_info", method = RequestMethod.GET)
    public ServiceParam consoleServiceInfo(@RequestParam(required = false, defaultValue = "") String from) {
        ServiceParam response = null;
        try {
            response = othersService.getServiceParam();
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 获取rds服务账号信息
      * 该函数用于获取rds服务的账号信息，通过调用后端服务获取相关信息
      * 
      * @param from 请求来源，默认为空字符串
      * @return 返回ServiceParam对象，包含rds服务账号信息
      */

    @ApiOperation(value = "rds服务账号信息")
    @RequestMapping(value = "/rds_default_service_info", method = RequestMethod.GET)
    public ServiceParam rdsServiceInfo(@RequestParam(required = false, defaultValue = "") String from) {
        ServiceParam response = null;
        try {
            response = othersService.getServiceParamForRdsBackend();
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    /**
      * 获取角色名称
      * 根据请求获取角色名称的列表
      * 
      * @param from 请求来源参数，默认为空字符串
      * @return 返回角色名称列表的结果
      */
    @RequestMapping(value = "/getRoleName", method = RequestMethod.GET)
    public ResultResponse getRoleName(@RequestParam(required = false, defaultValue = "") String from) {
        ResultResponse response = new ResultResponse();
        try {
            List<ServiceParam> serviceParamList = othersService.getRoleName();
            response.setResult(serviceParamList);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 获取blb专属集群列表
     * 根据输入的集群名称获取blb专属集群列表信息
     * 
     * @param name 集群名称，可以为空
     * @return 返回获取到的blb专属集群列表信息
     */

    @ApiOperation(value = "获取blb专属集群列表")
    @RequestMapping(value = "/blb/lbdc", method = RequestMethod.GET)
    public GetLbdcClusterResponse getBlbLdbc(@RequestParam(required = false, defaultValue = "") String name) {
        return othersService.getBlbLbdc(name);
    }

    /**
     * Id映射on dcc接口
     * 根据提供的hostId，获取对应的Id映射信息
     * 
     * @param from 可选参数，默认值为空字符串，表示来源
     * @param hostId 必须参数，表示主机Id
     * @return IdMapperResponse对象，包含Id映射信息
     */
    @ApiOperation(value = "Id映射on dcc")
    @RequestMapping(value = "/idMapper/dcc", method = RequestMethod.GET)
    public IdMapperResponse idMapperOnDcc(@RequestParam(required = false, defaultValue = "") String from,
                                          @RequestParam String hostId) {
        IdMapperResponse response = new IdMapperResponse();
        try {
            response.setIdMap(othersService.idMapperOnDcc(hostId));
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 总览页接口
      * 获取RDS实例的概览信息，包括实例总数、运行状态、磁盘超限、即将过期、已过期以及不同数据库引擎的数量
      * 
      * @param userId 用户ID，用于设置当前用户
      * @param region 地区信息，用于设置当前地区
      * @return HomeStatisticResponse 实例统计信息
      */

    @ApiOperation(value = "总览页接口")
    @RequestMapping(value = "/statistic", method = RequestMethod.GET)
    public HomeStatisticResponse statistic(@RequestParam(required = false, defaultValue = "") String userId,
                                           @RequestParam(required = false, defaultValue = "") String region) {
        HomeStatisticResponse response = new HomeStatisticResponse();

        try {

            // 设置用户id
            LogicUserService.getSubjectToken().getUser().setId(userId);

            // 设置region
            regionConfiguration.setCurrentRegion(region);

            LogicMarkerResultResponse<InstanceAbstract> pageResultResponse =
                    instanceService.listInstanceWithMarkerByMultiKey("-1", 1000, "");

            List<InstanceAbstract> instances = (List<InstanceAbstract>) pageResultResponse.getResult();

            int instanceTotalCount = instances.size();
            int instanceRunningCount = 0;
            int instanceDiskLockCount = 0;
            int instanceWillExpireCount = 0;
            int instanceExpiredCount = 0;
            int instanceMysqlCount = 0;
            int instanceSqlServerCount = 0;
            int instancePGCount = 0;

            for (InstanceAbstract each : instances) {
                if (ObjectUtils.equals("available", each.getInstanceStatus().toLowerCase())) {
                    instanceRunningCount++;
                } else if (ObjectUtils.equals("lockdiskquota", each.getInstanceStatus().toLowerCase())) {
                    instanceDiskLockCount++;

                } else {
                    LOGGER.info("[statistic] instanceStatus is especial {}", each.getInstanceStatus());
                }

                // 只有预付费才有到期概念
                if (ObjectUtils.equals("prepay", each.getProductType().toLowerCase())) {
                    // 即将到期，为0表示今天到期
                    if (each.getExpireDate() < 0) {
                        instanceExpiredCount++;
                    } else if (0 <= each.getExpireDate() && each.getExpireDate() < 7) {
                        instanceWillExpireCount++;
                    }
                }

                // 引擎类型
                String engineLowerCase = each.getEngine().toLowerCase();
                if (ObjectUtils.equals("mysql", engineLowerCase)) {
                    instanceMysqlCount++;
                } else if (ObjectUtils.equals("sqlserver", engineLowerCase)) {
                    instanceSqlServerCount++;
                } else if (ObjectUtils.equals("postgresql", engineLowerCase)) {
                    instancePGCount++;
                } else {
                    LOGGER.info("[statistic] engine is especial {}", each.getEngine());
                }
            }

            response.addStatisticInfo("InstanceTotalCount", String.valueOf(instanceTotalCount), "RDS实例总数");
            response.addStatisticInfo("InstanceRunningCount", String.valueOf(instanceRunningCount), "运行中");
            response.addStatisticInfo("InstanceDiskLockCount", String.valueOf(instanceDiskLockCount), "磁盘超限");
            response.addStatisticInfo("InstanceWillExpireCount", String.valueOf(instanceWillExpireCount), "即将过期");
            response.addStatisticInfo("InstanceExpiredCount", String.valueOf(instanceExpiredCount), "已过期");
            response.addStatisticInfo("InstanceMysqlCount", String.valueOf(instanceMysqlCount), "MySQL版");
            response.addStatisticInfo("InstanceSqlServerCount", String.valueOf(instanceSqlServerCount), "SQL Server版");
            response.addStatisticInfo("InstancePGCount", String.valueOf(instancePGCount), "Postgre SQL版");
        } catch (Exception e) {

            LOGGER.error("get statistic info error, use default info", e);

            // 默认
            response.addStatisticInfo("InstanceTotalCount", "未知", "RDS实例总数");
            response.addStatisticInfo("InstanceRunningCount", "未知", "运行中");
            response.addStatisticInfo("InstanceDiskLockCount", "未知", "磁盘超限");
            response.addStatisticInfo("InstanceWillExpireCount", "未知", "即将过期");
            response.addStatisticInfo("InstanceExpiredCount", "未知", "已过期");
            response.addStatisticInfo("InstanceMysqlCount", "未知", "MySQL版");
            response.addStatisticInfo("InstanceSqlServerCount", "未知", "SQL Service版");
            response.addStatisticInfo("InstancePGCount", "未知", "Postgre SQL版");
        }

        return response;
    }
}
