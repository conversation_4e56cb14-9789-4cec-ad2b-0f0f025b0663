package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.rogroup.CreateRoGroupResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.CreateRoGroupRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupDetailResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupListResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdatePubliclyAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.UpdateRoGroupPropertyRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateWeightRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupLeaveRequest;
import com.baidu.bce.logic.rds.service.RoGroupService;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;


import javax.validation.Valid;

@RestController
@RequestMapping("/v1/rds/{sourceAppId}/rogroup")
public class OpenApiRoGroupController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiRoGroupController.class);

    @Autowired
    private RoGroupService roGroupService;
    /**
     * 创建只读组
     * 创建只读组的接口
     * 
     * @param sourceAppId 应用ID
     * @param request 创建只读组的请求信息
     * @return 创建只读组的响应信息
     */

    @ApiOperation(value = "创建只读组")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public CreateRoGroupResponse roGroupCreate(@IdPermission @PathVariable String sourceAppId,
            @RequestBody @Valid CreateRoGroupRequest request) {
        CreateRoGroupResponse response = null;
        LOGGER.debug("create new roGroup.");
        try {
            request.setSourceAppId(sourceAppId);
            response = roGroupService.roGroupCreate(request);

        } catch (Exception e){
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 查询只读组详情
      * 根据传入的sourceAppId和roGroupId查询只读组详情
      *
      * @param sourceAppId 应用ID
      * @param roGroupId 只读组ID
      * @return 只读组详情
      */

    @ApiOperation(value = "查询只读组详情")
    @RequestMapping(value = "/detail/{roGroupId}", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public RoGroupDetailResponse roGroupDetail(
            @IdPermission @PathVariable String sourceAppId, @PathVariable String roGroupId) {
        RoGroupDetailResponse response = null;
        LOGGER.debug("roGroup detail.");
        try {
            response = roGroupService.roGroupDetail(sourceAppId, roGroupId);

        } catch (Exception e){
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
      * 主实例关联的只读组列表
      * 获取主实例关联的只读组列表
      * 
      * @param sourceAppId 主实例ID
      * @return 只读组列表响应
      */

    @ApiOperation(value = "主实例关联的只读组列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public RoGroupListResponse roGroupList(
            @IdPermission @PathVariable String sourceAppId) {
        RoGroupListResponse response = null;
        LOGGER.debug("list roGroup.");
        try {
            response = roGroupService.roGroupList(sourceAppId);

        } catch (Exception e){
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 删除只读组
     * 删除指定的只读组
     * 
     * @param sourceAppId 应用ID
     * @param roGroupId 只读组ID
     */

    @ApiOperation(" 删除只读组")
    @RequestMapping(value = "/{roGroupId}", method = RequestMethod.DELETE)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void deleteRoGroup(@IdPermission @PathVariable("sourceAppId") String sourceAppId
            , @PathVariable("roGroupId") String roGroupId) {
        LOGGER.debug("delete : {}", sourceAppId, roGroupId);

        try {
            roGroupService.deleteRoGroup(sourceAppId, roGroupId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 修改连接信息
      * 根据传入的sourceAppId, roGroupId和RoGroupUpdateRequest对象，更新连接信息。
      * 先校验请求中的endpoint地址是否符合规范，然后调用roGroupService的roGroupUpdateEndpoint方法更新连接信息。
      * 
      * @param sourceAppId 应用ID
      * @param roGroupId 只读组ID
      * @param request 更新连接信息的请求体
      * @throws RDSExceptions.ParamValidationException 如果endpoint地址不符合规范，抛出参数校验异常
      */

    @ApiOperation(value = "修改连接信息")
    @RequestMapping(value = "/{roGroupId}/updateEndpoint", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void roGroupUpdateEndpoint(@IdPermission @PathVariable String sourceAppId,
                                  @PathVariable String roGroupId,
                                  @RequestBody @Valid RoGroupUpdateRequest request) {
        LOGGER.debug("roGroupUpdateEndpoint.");
        try {
             String address = "[a-z][a-z0-9\\-_]{2,29}";
             if (request.getEndpoint() != null && !request.getEndpoint().getAddress().isEmpty()){
                 if (!request.getEndpoint().getAddress().matches(address)){
                     throw new RDSExceptions.ParamValidationException();
                 }
             }
            roGroupService.roGroupUpdateEndpoint(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 开启/关闭公网
     * 根据传入的参数开启或关闭指定资源组的公网访问
     * 
     * @param sourceAppId 应用ID，用于权限验证
     * @param roGroupId 资源组ID，指定要操作的资源组
     * @param request 请求体，包含开启/关闭公网的具体信息
     */

    @ApiOperation(value = "开启/关闭公网")
    @RequestMapping(value = "/{roGroupId}/updatePubliclyAccessible", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void roGroupUpdatePubliclyAccessible(@IdPermission @PathVariable String sourceAppId,
                                      @PathVariable String roGroupId,
                                      @RequestBody @Valid RoGroupUpdatePubliclyAccessibleRequest request) {
        LOGGER.debug("roGroupUpdatePubliclyAccessible.");
        try {
            roGroupService.roGroupUpdatePubliclyAccessible(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
      * 批量修改只读组属性
      * 批量修改只读组名称、延迟自动剔除开关、延迟阈值、重新负载均衡开关、组内只读实例权重
      * 
      * @param sourceAppId 应用ID
      * @param roGroupId 只读组ID
      * @param request 修改只读组属性请求体
      * @throws RDSExceptions.ParamValidationException 如果请求体中的延迟阈值不在有效范围内
      */

    @ApiOperation(value = "批量修改只读组名称、延迟自动剔除开关、延迟阈值、重新负载均衡开关、组内只读实例权重")
    @RequestMapping(value = "/{roGroupId}/updateRoGroupProperty", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void roGroupUpdateRoGroupProperty(@IdPermission @PathVariable String sourceAppId,
                                                @PathVariable String roGroupId,
                                                @RequestBody @Valid UpdateRoGroupPropertyRequest request) {
        LOGGER.debug("roGroupUpdateRoGroupProperty.");
        if (request != null && request.getDelayThreshold() != null){
            if (request.getDelayThreshold() < 1 || request.getDelayThreshold() > 2147483646){
                throw new RDSExceptions.ParamValidationException();
            }
        }
        roGroupService.roGroupUpdateRoGroupProperty(sourceAppId, roGroupId, request);

    }
    /**
     * 只读组重新负载均衡
     * 对指定的只读组执行重新负载均衡操作
     * 
     * @param sourceAppId 应用ID
     * @param roGroupId 只读组ID
     */

    @ApiOperation(value = "只读组重新负载均衡")
    @RequestMapping(value = "/{roGroupId}/reload", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void roGroupUpdateReload(@IdPermission @PathVariable String sourceAppId,
                                            @PathVariable String roGroupId) {
        LOGGER.debug("roGroupUpdateReload.");
        try {
            roGroupService.roGroupUpdateReload(sourceAppId, roGroupId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 只读实例加入只读组
     * 只读实例加入只读组的接口，用于将一个只读实例加入到指定的只读组中。
     * 
     * @param sourceAppId 应用ID
     * @param roGroupId 只读组ID
     * @param request 只读组更新权重请求体
     * @throws RDSExceptions.RoInstancesMAXException 当只读组中的应用数量达到上限时抛出
     */

    @ApiOperation(value = "只读实例加入只读组")
    @RequestMapping(value = "/{roGroupId}/join", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void roGroupUpdateJoin(@IdPermission @PathVariable String sourceAppId,
                                    @PathVariable String roGroupId,
                                    @RequestBody @Valid RoGroupUpdateWeightRequest request) {
        LOGGER.debug("roGroupUpdateJoin.");
        try {
            RoGroupDetailResponse res = roGroupService.roGroupDetail(sourceAppId, roGroupId);
            if (res.getAppList().size() >= 5){
                throw new RDSExceptions.RoInstancesMAXException();
            }
            roGroupService.roGroupUpdateJoin(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 只读实例离开只读组
     * 将只读实例从只读组中移除
     * 
     * @param sourceAppId 应用ID
     * @param roGroupId 只读组ID
     * @param request 只读组离开请求参数
     */

    @ApiOperation(value = "只读实例离开只读组")
    @RequestMapping(value = "/{roGroupId}/leave", method = RequestMethod.PUT)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public void roGroupLeave(@IdPermission @PathVariable String sourceAppId,
                                  @PathVariable String roGroupId,
                                  @RequestBody @Valid RoGroupLeaveRequest request) {
        LOGGER.debug("roGroupLeave.");
        try {
            roGroupService.roGroupLeave(sourceAppId, roGroupId, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

}
