package com.baidu.bce.logic.rds.model;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

public class DownloadUrlRequest {
    @IdPermission
    private String instanceId;
    @NotNull
    private String snapshotId;
    private Integer downloadValidTime = 300;

    private Integer downloadValidTimeInSec = 43200;

    @Override
    public String toString() {
        return "DownloadUrlRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", snapshotId='" + snapshotId + '\'' +
                ", downloadValidTime=" + downloadValidTime +
                ", downloadValidTimeInSec=" + downloadValidTimeInSec +
                '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getDownloadValidTime() {
        return downloadValidTime;
    }

    public void setDownloadValidTime(Integer downloadValidTime) {
        this.downloadValidTime = downloadValidTime;
    }

    public Integer getDownloadValidTimeInSec() {
        return downloadValidTimeInSec;
    }

    public void setDownloadValidTimeInSec(Integer downloadValidTimeInSec) {
        this.downloadValidTimeInSec = downloadValidTimeInSec;
    }

    public String getSnapshotId() {
        return snapshotId;
    }

    public void setSnapshotId(String snapshotId) {
        this.snapshotId = snapshotId;
    }
}
