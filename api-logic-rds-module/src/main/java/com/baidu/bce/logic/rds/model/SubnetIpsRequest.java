package com.baidu.bce.logic.rds.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by lipeishuai on 2018/3/26.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SubnetIpsRequest {
    private List<String> subnetIds;

    public List<String> getSubnetIds() {
        return subnetIds;
    }

    public void setSubnetIds(List<String> subnetIds) {
        this.subnetIds = subnetIds;
    }

    @Override
    public String toString() {
        return "SubnetIpsRequest{"
                + "subnetIds=" + subnetIds
                + " }";
    }
}