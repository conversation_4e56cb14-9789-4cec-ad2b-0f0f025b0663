package com.baidu.bce.logic.rds.controller;


import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.rds.model.RetainCategoryRequest;
import com.baidu.bce.internalsdk.rds.model.SplitCopyRegionPolicyRequest;
import com.baidu.bce.internalsdk.rds.model.UpdateEncryptPolicyReq;
import com.baidu.bce.logic.rds.model.BackupPolicyRequest;
import com.baidu.bce.internalsdk.rds.model.snapshot.BackupUsageResponse;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.exception.BackendExceptions;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baidu.bce.internalsdk.rds.model.BasicCategoryRequest;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/api/rds/backup", produces = {"application/json"})
@Api(value = "RDS 备份管理API")
public class ConsoleApiRdsBackupController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConsoleApiRdsBackupController.class);


    @Autowired
    private InstanceService instanceService;

    /**
      * 修改备份策略
      * 此函数用于修改备份策略，接收一个包含备份策略信息的请求体，并根据请求体中的信息更新备份策略。
      * 
      * @param request 包含备份策略信息的请求体
      * @param from 请求来源，默认为空字符串
      * @return 执行结果，包含操作是否成功
      * @throws BackendExceptions.RDSInstanceNotFound 当实例不存在时抛出
      * @throws BackendExceptions.AccessDenied 当没有权限执行操作时抛出
      * @throws BackendExceptions.InvalidRequest 当请求无效时抛出
      * @throws BackendExceptions.InternalFailure 当内部处理请求时出现错误时抛出
      */
    @ApiOperation(value = "修改备份策略")
    @RequestMapping(value = "/setting", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateBackupPolicy(@IdPermission  @RequestBody @Valid BackupPolicyRequest request,
                                                @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update backupPolicy. instanceId is {}", request.getInstanceId());
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        try {

            instanceService.updateBackupPolicy(request.getInstanceId(), request.getBackupPolicy());

        } catch (BceInternalResponseException e) {
            if ("InstanceNotFound".equalsIgnoreCase(e.getCode())) {
                throw new BackendExceptions.RDSInstanceNotFound(e);
            }
            else if ("AccessDenied".equalsIgnoreCase(e.getCode())) {
                throw new BackendExceptions.AccessDenied(e);
            }
            else if ("InvalidRequest".equalsIgnoreCase(e.getCode())) {
                throw new BackendExceptions.InvalidRequest(e);
            }
            else {
                throw new BackendExceptions.InternalFailure(e);
            }

        }

        resultResponse.setResult(true);
        return resultResponse;
    }

    /**
      * 设置基础策略（拆分后的备份策略）
      * 更新拆分后的基础备份策略
      * 
      * @param request 基础策略请求对象
      * @param from 来源，默认为空字符串
      * @return 执行结果
      */
    @ApiOperation(value = "设置基础策略（拆分后的备份策略）")
    @RequestMapping(value = "/split/basicPolicy", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateBasicBackupPolicy(
                                            @IdPermission @RequestBody @Valid BasicCategoryRequest request,
                                            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("basic update backupPolicy. instanceId is {}", request.getInstanceId());
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.updateBasicBackupPolicy(request);
        resultResponse.setResult(true);
        return resultResponse;
    }
    /**
      * 设置保留策略（拆分后的备份策略）
      * 用于更新拆分后的备份策略
      * 
      * @param request 保留策略请求体，包含实例ID等必要信息
      * @param from 请求来源参数，非必须
      * @return 操作结果，包含是否成功标志
      */

    @ApiOperation(value = "设置保留策略（拆分后的备份策略）")
    @RequestMapping(value = "/split/retainPolicy", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateRetainBackupPolicy(
                                            @IdPermission @RequestBody @Valid RetainCategoryRequest request,
                                            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("reation update backupPolicy. instanceId is {}", request.getInstanceId());
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.updateRetainBackupPolicy(request);
        resultResponse.setResult(true);
        return resultResponse;
    }

    /**
      * 设置跨地域策略（拆分后的备份策略）
      * 该函数用于设置跨地域策略，即拆分后的备份策略。
      * 
      * @param request 包含跨地域策略设置的请求参数
      * @param from 来源标识，默认为空字符串
      * @return EdpResultResponse<Boolean> 包含操作结果的响应
      */
    @ApiOperation(value = "设置跨地域策略（拆分后的备份策略）")
    @RequestMapping(value = "/split/updateCopyPolicy", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateCopyRegionBackupPolicy(
            @IdPermission @RequestBody @Valid SplitCopyRegionPolicyRequest request,
            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("copy region update backupPolicy. instanceId is {}", request.getInstanceId());
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.updateCopyRegionBackupPolicy(request);
        resultResponse.setResult(true);
        return resultResponse;
    }
    /**
      * 设置加密策略
      * 更新拆分后的备份策略
      * 
      * @param request 更新加密策略请求体，包含实例ID等信息
      * @param from 来源标记，默认为空字符串
      * @return 操作结果
      */

    @ApiOperation(value = "设置加密策略（拆分后的备份策略）")
    @RequestMapping(value = "/split/updateEncryptPolicy", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Boolean> updateEncryptPolicy(
            @IdPermission @RequestBody @Valid UpdateEncryptPolicyReq request,
            @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update encrypt backupPolicy. instanceId is {}", request.getInstanceId());
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        instanceService.updateEncryptPolicy(request);
        resultResponse.setResult(true);
        return resultResponse;
    }

    /**
      * 查询备份用量详情
      * 该接口用于根据实例ID获取备份用量详情
      * 
      * @param instanceId 实例ID
      * @return EdpResultResponse<BackupUsageResponse> 返回查询结果
      */
    @ApiOperation(value = "查询备份用量详情")
    @RequestMapping(value = "/usage", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<BackupUsageResponse> getBackupUsage(@IdPermission @RequestParam String instanceId) {
        EdpResultResponse<BackupUsageResponse> resultResponse = new EdpResultResponse<>();

        resultResponse.setResult(instanceService.getBackupUsage(instanceId));
        return resultResponse;
    }
}
