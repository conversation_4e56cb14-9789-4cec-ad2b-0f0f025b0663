package com.baidu.bce.logic.rds.model.config;

import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;

import java.net.URL;

public class PhpAdminUrlConfig {

    private String region;

    private String phpadminUrl;

    private Integer validTimeInSecond;

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getPhpadminUrl() {
        return phpadminUrl;
    }

    public void setPhpadminUrl(String phpadminUrl) {
        this.phpadminUrl = phpadminUrl;
    }

    public Integer getValidTimeInSecond() {
        return validTimeInSecond;
    }

    public void setValidTimeInSecond(Integer validTimeInSecond) {
        this.validTimeInSecond = validTimeInSecond;
    }

    public String getPhpadminUrlHost() {
        // http://nmg02-bce-test90.nmg02.baidu.com/phpmyadmin/index.php?instanceId=
        String host = "";
        try {
            URL url = new URL(phpadminUrl);
            host = url.getHost();
            if (url.getPort() != -1) {
                host += ":" + url.getPort();
            }
        } catch (Exception ex) {
            throw new RDSBusinessExceptions.WrongFormatUrlExcption();
        }
        return host;
    }
}

