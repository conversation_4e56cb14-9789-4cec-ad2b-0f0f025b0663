package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;

import com.baidu.bce.internalsdk.rds.model.security.SecurityIpPutRequest;
import com.baidu.bce.internalsdk.rds.model.security.V2ListRequest;
import com.baidu.bce.internalsdk.rds.model.security.V2SecurityIpResponse;
import com.baidu.bce.logic.rds.service.V2WhiteListService;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * Created by luping03 on 17/11/3.
 */
@RestController
@RequestMapping("/api/rds/whitelist2")
public class ConsoleApiV2WhiteListController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private V2WhiteListService whiteListService;
    /**
     * 查看白名单
     * 用于查看白名单的接口，接收白名单查询请求，并返回查询结果
     *
     * @param requestBody 白名单查询请求体
     * @param servletResponse HttpServletResponse对象，用于设置响应头
     * @return EdpResultResponse对象，包含查询结果
     */

    @ApiOperation(value = "查看白名单")
    @RequestMapping(value = "get", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse getWhiteList(@Valid @RequestBody V2ListRequest requestBody,
                                             HttpServletResponse servletResponse) {
        LOGGER.debug("get whitelist.");
        EdpResultResponse res = new EdpResultResponse();
        V2SecurityIpResponse response = null;

        response = whiteListService.getWhiteList(requestBody.getInstanceId(), requestBody);
        servletResponse.setHeader("ETag", response.getETag());
        res.setResult(response);

        return res;
    }
    /**
     * 更新已有分组白名单
     * 更新指定实例的分组白名单信息
     * 
     * @param request 分组白名单请求体
     * @param from 来源标识
     * @return 操作结果
     */

    @ApiOperation(value = "更新已有分组白名单")
    @RequestMapping(value = "set", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse setWhiteList(@RequestBody @Valid SecurityIpPutRequest request,
                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("set whitelist.");

        EdpResultResponse response = new EdpResultResponse();
        whiteListService.setWhiteList(request.getInstanceId(), request);
        return response;

    }
    /**
     * 新增分组白名单
     * 新增分组白名单接口，用于添加新的分组白名单信息
     *
     * @param request 分组白名单请求体
     * @param from 请求来源
     * @return EdpResultResponse 返回结果
     */

    @ApiOperation(value = "新增分组白名单")
    @RequestMapping(value = "create", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse setWhiteListNew(@RequestBody @Valid SecurityIpPutRequest request,
                             @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("set whitelist.");
        EdpResultResponse response = new EdpResultResponse();
        whiteListService.setWhiteListNew(request.getInstanceId(), request);
        return response;
    }
    /**
     * 删除/清空分组白名单
     * 删除指定实例的分组白名单信息
     * 
     * @param request 分组白名单信息请求体
     * @param from 来源标识，默认为空字符串
     * @return 执行结果
     */

    @ApiOperation(value = "删除/清空分组白名单")
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse deleteWhiteList(@RequestBody @Valid SecurityIpPutRequest request,
                                @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("set whitelist.");

        EdpResultResponse response = new EdpResultResponse();
        whiteListService.deleteWhiteList(request.getInstanceId(), request);
        return response;

    }



}
