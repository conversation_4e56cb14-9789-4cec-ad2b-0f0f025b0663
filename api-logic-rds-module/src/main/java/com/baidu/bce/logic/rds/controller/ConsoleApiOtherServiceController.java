package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.internalsdk.rds.model.InsertCustomMonitorReq;
import com.baidu.bce.internalsdk.rds.model.UserType;
import com.baidu.bce.internalsdk.rds.model.UserTypeInfoResponse;
import com.baidu.bce.internalsdk.rds.model.ZoneListDetails;
import com.baidu.bce.internalsdk.rds.model.blb.GetLbdcClusterResponse;
import com.baidu.bce.logic.rds.dao.model.CustomMonitorPO;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/rds")
public class ConsoleApiOtherServiceController {

    @Autowired
    private OthersService othersService;
    /**
      * 发送群组消息
      * 该函数用于激活用户，通过发送群组消息的方式。
      * 
      * @param userName 用户名，指定发送给哪个用户，可以为空
      * @return EdpResultResponse<Boolean> 返回操作结果，包含是否成功
      */

    @RequestMapping(value = "/activate", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> sendGroupMessage(@RequestParam(required = false) String userName) {
        EdpResultResponse<Boolean> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(true);
        othersService.sendGroupMessage(userName);
        return edpResultResponse;
    }
    /**
     * 检查用户类型
     * 接口用于获取用户类型信息，默认返回外部公有云普通用户，如果是内部用户则返回内部用户类型
     * 
     * @return EdpResultResponse<UserTypeInfoResponse> 返回用户类型信息的响应结果
     */

    @ApiOperation(value = "检查用户类型")
    @RequestMapping(value = "/userinfo", method = RequestMethod.GET)
    public EdpResultResponse<UserTypeInfoResponse> userInfo() {
        UserTypeInfoResponse userTypeInfoResponse = new UserTypeInfoResponse();
        // 返回用户类型 默认为外部公有云普通用户
        String userType = UserType.EXTERNAL_NORMAL.getValue();
        boolean isInternalUser = othersService.checkInternalUser();
        if (isInternalUser) {
            userType = UserType.INTERNAL_NORMAL.getValue();
        }
        userTypeInfoResponse.setType(userType);
        EdpResultResponse<UserTypeInfoResponse> edpResultResponse = new EdpResultResponse<>();
        edpResultResponse.setResult(userTypeInfoResponse);
        return edpResultResponse;
    }
    /**
     * 获取负载均衡集群信息
     * 通过指定名称获取负载均衡集群的详细信息
     * 
     * @param name 负载均衡集群名称，非必传参数，默认为空字符串
     * @return 返回负载均衡集群信息
     */

    @RequestMapping(value = "/blb/lbdc", method = RequestMethod.GET)
    public GetLbdcClusterResponse getBlbLbdc(@RequestParam(required = false, defaultValue = "") String name) {
        return othersService.getBlbLbdc(name);
    }
    /**
     * 检验FullControl权限
     * 用于检验用户是否具有FullControl权限，通过特定的service和permission进行验证
     * 
     * @return 返回验证结果，EdpResultResponse封装了验证是否通过的信息
     */

    @ApiOperation("用于检验 FullControl 权限")
    @RequestMapping(value = "/checkPermissionOfFullControl", method = RequestMethod.GET)
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL, RDSConstant.CREATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX, ids = "*")
    public EdpResultResponse<Boolean> checkPermissionOfCreate() {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(true);
        return resultResponse;
    }
    /**
     * 获取可用区映射关系以及接口白名单
     * 该函数用于获取可用区映射关系以及接口白名单信息，并通过GET请求方式实现。
     * 
     * @return 返回可用区映射关系以及接口白名单的响应信息
     */

    @ApiOperation("用于获取可用区映射关系以及接口白名单，待扩充")
    @RequestMapping(value = "/zone/prohibit/configuration", method = RequestMethod.GET)
    public EdpResultResponse<ZoneListDetails> getZoneConfiguration() {
        EdpResultResponse<ZoneListDetails> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(othersService.getZoneConfiguration());
        return resultResponse;
    }
    /**
      * 新增指定账户、引擎类型的自定义监控视图
      * 新增自定义监控视图，接收请求体中包含的账户和引擎类型等信息，进行插入操作
      * 
      * @param customMonitorReq 请求体，包含账户和引擎类型等信息
      * @return EdpResultResponse<Boolean> 操作结果
      */

    @ApiOperation("新增指定账户、引擎类型的自定义监控视图")
    @RequestMapping(value = "/custom/monitor/view", method = RequestMethod.POST)
    public EdpResultResponse<Boolean> insertCustomMonitor(@RequestBody @Valid InsertCustomMonitorReq customMonitorReq) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        othersService.insertCustomMonitor(customMonitorReq);
        resultResponse.setResult(true);
        return resultResponse;
    }
    /**
     * 查询指定账户、引擎类型的自定义监控视图
     * 根据账户ID和引擎类型查询对应的自定义监控视图
     * 
     * @param accountId 账户ID
     * @param engine 引擎类型
     * @return 查询结果
     */

    @ApiOperation("查询指定账户、引擎类型的自定义监控视图")
    @RequestMapping(value = "/custom/monitor/view/{accountId}/{engine}", method = RequestMethod.GET)
    public EdpResultResponse<CustomMonitorPO> getCustomMonitor(@PathVariable("accountId") String accountId,
                                                               @PathVariable("engine") String engine) {
        EdpResultResponse<CustomMonitorPO> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(othersService.getCustomMonitor(accountId, engine));
        return resultResponse;
    }
    /**
     * 更新自定义监控视图
     * 更新指定账户、引擎类型的自定义监控视图
     *
     * @param customMonitorReq 自定义监控视图请求参数
     * @return 操作结果
     */

    @ApiOperation("更新指定账户、引擎类型的自定义监控视图")
    @RequestMapping(value = "/custom/monitor/view", method = RequestMethod.PUT)
    public EdpResultResponse<Boolean> updateCustomMonitor(@RequestBody @Valid InsertCustomMonitorReq customMonitorReq) {
        EdpResultResponse<Boolean> resultResponse = new EdpResultResponse<>();
        othersService.updateCustomMonitor(customMonitorReq);
        resultResponse.setResult(true);
        return resultResponse;
    }
}
