package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.core.BceFormat;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogDateTime;
import com.baidu.bce.internalsdk.rds.model.binlog.DashboardBinlogCheckRequest;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogDetailRequest;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogSlowDetails;
import com.baidu.bce.internalsdk.rds.model.slowlog.DashboardSlowlogListRequest;
import com.baidu.bce.internalsdk.rds.model.slowlog.DashboardSlowlogUrlRequest;
import com.baidu.bce.internalsdk.rds.model.slowlog.PgLogListResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.PglogDownloadResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.Slowlog;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogSummaryRequest;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogSummaryResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.Url;
import com.baidu.bce.logic.rds.service.ErrorlogService;
import com.baidu.bce.logic.rds.service.SlowlogService;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

/**
 * Created by chensilan on 15/11/27.
 */
@Controller
@RequestMapping(value = "/api/rds/slowlog", produces = {"application/json"})
@Api(value = "RDS Dashboard Slowlog管理API")
public class ConsoleApiSlowlogController {

    @Autowired
    private SlowlogService slowlogService;

    @Autowired
    private ErrorlogService errorlogService;
    /**
      * 列表查询接口
      * 根据条件查询慢日志列表
      * 
      * @param request 查询请求，包含实例ID和日期
      * @return 查询结果，封装在EdpPageResultResponse对象中
      */

    @RequestMapping(value = "list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpPageResultResponse<Slowlog> list(@IdPermission @Valid @RequestBody DashboardSlowlogListRequest request) {
        EdpPageResultResponse<Slowlog> listResponse = new EdpPageResultResponse<>();
        listResponse.getPage().setResult(slowlogService.list(request.getInstanceId(),
                BceFormat.getDateTimeFormat().format(request.getDate())).getSlowlogs());
        return listResponse;
    }
    /**
      * 下载慢日志URL接口
      * 根据请求参数获取慢日志的下载URL
      * 
      * @param request 请求参数，包含实例ID、慢日志ID和下载有效时间
      * @return 返回包含下载URL和过期时间的响应对象
      */

    @RequestMapping(value = "download_url", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Url> downloadUrl(@IdPermission @Valid @RequestBody DashboardSlowlogUrlRequest request) {
        Slowlog slowlog = slowlogService.detail(request.getInstanceId(), request.getSlowlogId(),
                request.getDownloadValidTime() * 60).getSlowlog();
        Url url = new Url().url(slowlog.getDownloadUrl()).expire(slowlog.getDownloadExpires());

        EdpResultResponse<Url> response = new EdpResultResponse<>();
        response.withResult(url);
        return response;
    }
    /**
      * 获取慢日志详情
      * 根据传入的日志详情请求参数，调用服务获取慢日志的详细信息
      * 
      * @param logDetailRequest 日志详情请求参数，包含需要查询慢日志的详细信息
      * @return 慢日志详情
      */

    @RequestMapping(value = "/getSlowLogDetails", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public LogSlowDetails getSlowLogDetails(@RequestBody @Valid LogDetailRequest logDetailRequest) {
        return errorlogService.getSlowLogDetails(logDetailRequest);
    }
    /**
      * 获取慢日志摘要信息
      * 根据请求中的参数，调用服务获取慢日志摘要信息，并设置分页信息后返回
      * 
      * @param request 慢日志摘要请求信息，包含查询条件及分页信息
      * @return 慢日志摘要响应信息，包含慢日志摘要列表及分页信息
      */

    @RequestMapping(value = "/summary", method = RequestMethod.POST)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.FULL_CONTROL},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public SlowlogSummaryResponse slowlogSummary(@RequestBody @Valid SlowlogSummaryRequest request) {
        SlowlogSummaryResponse response = slowlogService.slowlogSummary(request);
        response.setPageNo(request.getPageNo());
        response.setPageSize(request.getPageSize());
        return response;
    }

    /**
      * 获取pg日志列表
      * 根据instanceId和date获取pg日志列表
      * 
      * @param instanceId 实例ID
      * @param date 日期
      * @return pg日志列表的响应结果
      */
    @RequestMapping(value = "/pg/list", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @ApiOperation("pg 日志列表")
    public EdpResultResponse<PgLogListResponse> pgList(
            @IdPermission @RequestParam(required = true, value = "instanceId") String instanceId,
            @RequestParam(required = true, value = "date") String date) {
        EdpResultResponse<PgLogListResponse> resultResponse = new EdpResultResponse<>();
        resultResponse.setResult(slowlogService.pgList(instanceId, date));
        return resultResponse;
    }
    /**
      * 下载pg日志
      * 通过instanceId和pglogId下载pg日志，支持设置下载链接的有效时间
      *
      * @param instanceId 实例ID，用于标识要下载日志的数据库实例
      * @param pglogId pg日志ID，用于标识要下载的日志
      * @param downloadValidTimeInSec 下载链接有效时间，单位为秒，用于设置下载链接的有效期
      * @return EdpResultResponse<PglogDownloadResponse> 返回下载结果，包含下载链接等信息
      */

    @RequestMapping(value = "/pg/download", method = RequestMethod.GET)
    @ResponseBody
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.READ},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    @ApiOperation("pg 日志下载")
    public EdpResultResponse<PglogDownloadResponse> downloadPglog(
            @IdPermission @RequestParam(required = true, value = "instanceId") String instanceId,
            @RequestParam(required = true, value = "pglogId") String pglogId,
            @RequestParam(value = "downloadValidTimeInSec") Integer downloadValidTimeInSec) {
        EdpResultResponse<PglogDownloadResponse> resultResponse = new EdpResultResponse<>();

        resultResponse.setResult(slowlogService.downloadPglog(instanceId, pglogId, downloadValidTimeInSec));

        return resultResponse;

    }
    /**
      * pg实例按时间点恢复的时间点检查
      * 该函数用于检查pg实例按时间点恢复的时间点是否有效
      * 
      * @param request 请求体，包含实例ID和日期
      * @return EdpResultResponse<Boolean> 检查结果，成功返回true，失败返回false
      */

    @ApiOperation("pg实例按时间点恢复的时间点检查")
    @RequestMapping(value = "pg/check", method = RequestMethod.POST)
    @ResponseBody
    public EdpResultResponse<Boolean> checkPgWal(@Valid @RequestBody DashboardBinlogCheckRequest request) {
        EdpResultResponse<Boolean> response = new EdpResultResponse<Boolean>();
        BinlogDateTime binlogDateTime = new BinlogDateTime();
        binlogDateTime.setDatetime(request.getDate());
        slowlogService.checkPgWal(request.getInstanceId(), binlogDateTime);
        return response.withResult(Boolean.TRUE);
    }

}
