package com.baidu.bce.logic.rds.controller;

import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.model.tag.AssignTagRequest;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by luping03 on 17/7/7.
 */

@RestController
@RequestMapping(value = "/api/rds/tag", produces = {"application/json"})
@Api(value = "RDS Dashboard Tag管理API")
public class ConsoleApiTagController {

    @Autowired
    private OthersService othersService;
    /**
      * 更新标签关联关系
      * 此函数用于处理更新标签关联关系的请求。
      * 
      * @param request 包含更新标签关联关系所需的数据
      * @return 处理结果
      */

    @RequestMapping(value = "/assign", method = RequestMethod.POST)
    @ApiOperation(value = "更新标签关联关系")
    @PermissionVertify(service = {RDSConstant.SERVICE_RDS}, permission = {RDSConstant.WRITE, RDSConstant.OPERATE},
            idConverter = RdsToMasterShortIdConverter.class, type = RDSConstant.ID_PREFIX)
    public EdpResultResponse<Object> instanceAssignTags(@IdPermission @RequestBody AssignTagRequest request) {
        othersService.assignTags(request);
        return new EdpResultResponse<>();
    }
}
