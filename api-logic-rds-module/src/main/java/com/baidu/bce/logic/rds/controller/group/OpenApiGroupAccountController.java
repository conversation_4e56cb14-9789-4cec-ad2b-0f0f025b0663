package com.baidu.bce.logic.rds.controller.group;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.CheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.AccountGetResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountListResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePrivilegesRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateRemarkRequest;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.group.InstanceGroupService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.wordnik.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 实例组账号
 * Created by shiyuxin on 19/5/21.
 */
@RestController
@RequestMapping("/v1/instance/group/{groupId}/account")
public class OpenApiGroupAccountController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpenApiGroupAccountController.class);

    @Autowired
    private InstanceGroupService instanceGroupService;

    /**
     * 实例组：帐号列表
     * 实例组：获取帐号列表
     *
     * @param groupId 实例组ID
     * @return 帐号列表响应
     */
    @ApiOperation(value = "实例组: 帐号列表")
    @RequestMapping(method = RequestMethod.GET)
    public AccountListResponse accountList(@PathVariable String groupId) {

        LOGGER.debug("get account list. groupId is {}", groupId);
        AccountListResponse response = null;
        try {
            response = instanceGroupService.accountList(groupId);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }
    /**
     * 帐号详情
     * 根据groupId和accountName获取帐号详情
     * 
     * @param groupId 实例组ID
     * @param accountName 帐号名称
     * @return 帐号详情
     */

    @ApiOperation(value = "实例组: 帐号详情")
    @RequestMapping(value = "/{accountName}", method = RequestMethod.GET)
    public AccountGetResponse accountDetail(@PathVariable String groupId,
                                            @PathVariable String accountName) {


        LOGGER.debug("get account detail. groupId is {}", groupId);
        AccountGetResponse response = null;
        try {
            response = instanceGroupService.accountDetail(groupId, accountName);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;

    }
    /**
     * 检查帐号名称是否存在
     * 实例组接口，用于检查指定帐号名称是否已存在
     * 
     * @param groupId 实例组ID
     * @param accountName 帐号名称
     * @return 返回检查结果
     */

    @ApiOperation(value = "实例组: 检查帐号名称是否存在")
    @RequestMapping(value = "/{accountName}/isExist", method = RequestMethod.POST)
    public CheckExistResponse checkName(@PathVariable String groupId,
                                        @PathVariable String accountName) {
        LOGGER.debug("get account checkName. groupId is {}, accountName {}", groupId, accountName);
        CheckExistResponse response = null;
        try {
            response = instanceGroupService.accountCheckName(groupId, accountName);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
        return response;
    }

    /**
     * 实例组：创建账号
     * 实例组：创建账号
     *
     * @param groupId 实例组ID
     * @param request 创建账号的请求参数
     * @param httpServletRequest 封装了客户端请求信息的HttpServletRequest对象
     * @return 无返回值
     */
    @ApiOperation(value = "实例组: 创建账号")
    @RequestMapping(method = RequestMethod.POST)
    public void createAccount(@IdPermission @PathVariable String groupId,
                                                                @Valid @RequestBody Account request,
                                                                HttpServletRequest httpServletRequest) {
        LOGGER.debug("create account. groupId is {}", groupId);
        AccountListResponse response = null;
        try {
            String ak = httpServletRequest.getHeader(RDSConstant.X_BCE_ACCESS_KEY);
            instanceGroupService.accountCreate(groupId, request, ak);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 修改账号密码
     * 修改指定实例组下的账号密码
     * 
     * @param groupId 实例组ID
     * @param accountName 账号名称
     * @param pwRequest 账号密码修改请求体
     */

    @ApiOperation(value = "实例组: 修改账号密码")
    @RequestMapping(value = "/{accountName}/password", method = RequestMethod.PUT)
    public void updatePW(@IdPermission @PathVariable String groupId,
                                                           @PathVariable String accountName,
                                                           @RequestBody @Valid AccountUpdatePasswordRequest pwRequest) {
        LOGGER.debug("update password. groupId: {}, accountName: {}", groupId, accountName);
        try {
            instanceGroupService.accountUpdatePW(groupId, accountName, pwRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
    /**
     * 修改账号备注
     * 修改指定账号的备注信息
     * 
     * @param groupId 实例组ID
     * @param accountName 账号名称
     * @param remarkRequest 账号备注更新请求体
     * @param from 请求来源
     */

    @ApiOperation(value = "实例组: 修改账号备注")
    @RequestMapping(value = "/{accountName}/remark", method = RequestMethod.PUT)
    public void updateRemark(@PathVariable String groupId,
                             @PathVariable String accountName,
                             @RequestBody @Valid AccountUpdateRemarkRequest remarkRequest,
                             @RequestParam(required = false, defaultValue = "") String from) {

        LOGGER.debug("update remark. groupId: {}, accountName: {}", groupId, accountName);
        try {
            instanceGroupService.accountUpdateRemark(groupId, accountName, remarkRequest);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }


    }
    /**
     * 修改账号权限
     * 用于修改指定账号的权限
     * 
     * @param groupId 实例组ID
     * @param accountName 账号名称
     * @param request 权限更新请求对象
     * @param from 来源标识，可选参数，默认值为空字符串
     */

    @ApiOperation(value = "实例组: 修改账号权限")
    @RequestMapping(value = "/{accountName}/privileges", method = RequestMethod.PUT)
    public void updatePrivileges(@IdMapper @PathVariable String groupId,
                                 @PathVariable String accountName,
                                 @RequestBody @Valid AccountUpdatePrivilegesRequest request,
                                 @RequestParam(required = false, defaultValue = "") String from) {
        LOGGER.debug("update privilege. groupId: {}, accountName: {}", groupId, accountName);
        try {
            instanceGroupService.accountUpdatePrivileges(groupId, accountName, request);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }

    /**
      * 删除帐号
      * 根据实例组ID和帐号名称删除指定的帐号
      * 
      * @param groupId 实例组ID
      * @param accountName 帐号名称
      */
    @ApiOperation(value = "实例组：删除帐号")
    @RequestMapping(value = "/{accountName}", method = RequestMethod.DELETE)
    public void delete(@IdPermission @PathVariable String groupId,
                       @PathVariable String accountName) {
        LOGGER.debug("update privilege. instanceId: {}, accountName: {}", groupId, accountName);
        try {
            instanceGroupService.accountDelete(groupId, accountName);
        } catch (Exception e) {
            LogicRdsExceptionHandler.handle(e);
        }
    }
}
