<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>api-logic-rds-root</artifactId>
        <groupId>com.baidu.bce</groupId>
        <version>${api-logic-rds-version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>api-logic-rds-internalsdk</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-iam</artifactId>
            <version>1.3.52.2_jdk7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-vpc-external-sdk</artifactId>
            <version>1.0.1026.3</version>
            <exclusions>
                <exclusion>
                    <groupId>Baidu_Local.cglib</groupId>
                    <artifactId>cglib-nodep</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-qualify</artifactId>
            <version>1.0.103.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.fbi</groupId>
            <artifactId>fp-cashier-client</artifactId>
            <version>2.0.210.123887</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>