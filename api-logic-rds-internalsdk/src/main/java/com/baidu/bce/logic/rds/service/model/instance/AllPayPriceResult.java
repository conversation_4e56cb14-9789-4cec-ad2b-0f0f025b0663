package com.baidu.bce.logic.rds.service.model.instance;

import com.baidu.bce.pricing.service.model.query.PricingQueryResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.math.BigDecimal;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AllPayPriceResult {

    private String uuid;

    private BigDecimal price;

    private Long priceId;

    private BigDecimal catalogPrice;

    private String priceType;

    private String priceName;

    private Integer productDiscountRate;

    private Long dayCount;

    private int errorCode;

    private String errorMessage;

    @Min(0)
    @Max(100)
    private BigDecimal discountRate = new BigDecimal(100);

    /**
     * 旧配置的价格信息（预留实例券）
     */
    private PricingQueryResponse pastOriginPrice;

    PostPayPriceDetail postPayPriceDetail;

}
