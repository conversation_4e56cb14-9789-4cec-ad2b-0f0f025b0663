package com.baidu.bce.logic.rds.service.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PriceQueryResponse {

    /**
     * 实际价格
     */
    private BigDecimal price;

    /**
     * 目录价格
     */
    private BigDecimal catalogPrice;

    /**
     * 多年原价
     */
    private BigDecimal realCatalogPrice;

    /**
     * 实际价格对应的ID
     */
    private Long priceId;

    /**
     * 实际价格对应的type
     */
    private String priceType;

    /**
     * 实际价格对应的name
     */
    private String priceName;
}