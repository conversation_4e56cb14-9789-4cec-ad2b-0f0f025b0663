package com.baidu.bce.logic.rds.service.model.instance;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AuditStopRequest {
    private String region;
    private String  status;
    private String productType;
    private String reason;
    private List<String> chargeItemList;

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<String> getChargeItemList() {
        return chargeItemList;
    }

    public void setChargeItemList(List<String> chargeItemList) {
        this.chargeItemList = chargeItemList;
    }
}
