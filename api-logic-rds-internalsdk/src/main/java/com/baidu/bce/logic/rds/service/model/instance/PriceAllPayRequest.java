package com.baidu.bce.logic.rds.service.model.instance;

import com.baidu.bce.internalsdk.order.model.TimeUnit;
import com.baidu.bce.pricing.service.model.common.Flavor;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.joda.time.DateTime;

import java.util.Date;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PriceAllPayRequest {

    private String uuid;

    /**
     * 账户id
     */
    private String accountId;

    /**
     * 产品名称
     */
    private String serviceType;

    /**
     * 计费项名称
     */
    private String chargeItemName;

    /**
     * 区域
     */
    private String region;

    /**
     * 时长
     */
    private int duration;

    /**
     * 时间单位
     */
    private String timeUnit =  TimeUnit.MONTH.name().toLowerCase();

    /**
     * 询价价格的开始时间（仅对续费订单生效）
     */
    private Date startTime;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 询价资源个数
     */
    private int count = 1;

    /**
     * 资源配置
     */
    private Flavor flavor;

    /**
     * 后付费对应的用量
     */
    private String amount;

    /**
     * 资源instanceId, 可为空
     */
    private String instanceId;

    /**
     * 付费类型
     */
    private String productType;

    /**
     * 定价策略
     */
    private String policy;

    /**
     * 计费子类型
     */
    private String subProductType;

    /**
     * 预留实例券的付费方式
     */
    private String purchaseMode;

    /**
     * 查询时间
     */
    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'"
    )
    private DateTime queryTime = DateTime.now();

    /**
     * 价格类型
     */
    private String pricingType;

    /**
     * 价格名称
     */
    private String pricingName;


}