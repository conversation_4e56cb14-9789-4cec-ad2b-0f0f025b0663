package com.baidu.bce.internalsdk.rds.model.rogroup;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

public class CreateRoGroupRequest {
    @IdPermission
    private String sourceAppId;
    private String vpcId;
    private String subnetId;
    private String roGroupName;
    private boolean enableDelayOff;
    private Integer delayThreshold;
    private Integer leastAppAmount;
    private boolean balanceReload;
    private boolean bgwGroupExclusive;
    private String bgwGroupId;
    private Integer entryPort;
    private String vnetIp;

    public String getSourceAppId() {
        return sourceAppId;
    }

    public void setSourceAppId(String sourceAppId) {
        this.sourceAppId = sourceAppId;
    }

    public String getVpcId() {
        return vpcId;
    }

    public void setVpcId(String vpcId) {
        this.vpcId = vpcId;
    }

    public String getSubnetId() {
        return subnetId;
    }

    public void setSubnetId(String subnetId) {
        this.subnetId = subnetId;
    }

    public String getRoGroupName() {
        return roGroupName;
    }

    public void setRoGroupName(String roGroupName) {
        this.roGroupName = roGroupName;
    }

    public boolean isEnableDelayOff() {
        return enableDelayOff;
    }

    public void setEnableDelayOff(boolean enableDelayOff) {
        this.enableDelayOff = enableDelayOff;
    }

    public Integer getDelayThreshold() {
        return delayThreshold;
    }

    public void setDelayThreshold(Integer delayThreshold) {
        this.delayThreshold = delayThreshold;
    }

    public Integer getLeastAppAmount() {
        return leastAppAmount;
    }

    public void setLeastAppAmount(Integer leastAppAmount) {
        this.leastAppAmount = leastAppAmount;
    }

    public boolean isBalanceReload() {
        return balanceReload;
    }

    public void setBalanceReload(boolean balanceReload) {
        this.balanceReload = balanceReload;
    }

    public boolean isBgwGroupExclusive() {
        return bgwGroupExclusive;
    }

    public void setBgwGroupExclusive(boolean bgwGroupExclusive) {
        this.bgwGroupExclusive = bgwGroupExclusive;
    }

    public String getBgwGroupId() {
        return bgwGroupId;
    }

    public void setBgwGroupId(String bgwGroupId) {
        this.bgwGroupId = bgwGroupId;
    }

    public Integer getEntryPort() {
        return entryPort;
    }

    public void setEntryPort(Integer entryPort) {
        this.entryPort = entryPort;
    }

    public String getVnetIp() {
        return vnetIp;
    }

    public void setVnetIp(String vnetIp) {
        this.vnetIp = vnetIp;
    }
}
