package com.baidu.bce.internalsdk.rds.model.database;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/17 10:13
 */
public class ListTableResponse {
    private List<String> tables;
    private Integer pageSize;
    private Integer pageNo;
    private Integer count;

    @Override
    public String toString() {
        return "ListTableResponse{" +
                "tables=" + tables +
                ", pageSize=" + pageSize +
                ", pageNo=" + pageNo +
                ", count=" + count +
                '}';
    }

    public List<String> getTables() {
        return tables;
    }

    public void setTables(List<String> tables) {
        this.tables = tables;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
