package com.baidu.bce.internalsdk.rds.model.performance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by yangxin on 2022/1/14
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TransactionListResponse {

    private String datetime;
    private List<InnodbTransaction> innodbTrxList;

    public String getDatetime() {
        return datetime;
    }

    public void setDatetime(String datetime) {
        this.datetime = datetime;
    }

    public List<InnodbTransaction> getInnodbTrxList() {
        return innodbTrxList;
    }

    public void setInnodbTrxList(List<InnodbTransaction> innodbTrxList) {
        this.innodbTrxList = innodbTrxList;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InnodbTransaction {
        private Integer trxTablesLocked;
        private Integer trxTablesInUse;
        private String trxQuery;
        private String trxStarted;
        private String trxRequestedLockId;
        private String trxId;
        private Integer trxRowsLocked;
        private String trxWaitStarted;
        private String trxState;
        private Long trxMysqlThreadId;

        public Integer getTrxTablesLocked() {
            return trxTablesLocked;
        }

        public void setTrxTablesLocked(Integer trxTablesLocked) {
            this.trxTablesLocked = trxTablesLocked;
        }

        public Integer getTrxTablesInUse() {
            return trxTablesInUse;
        }

        public void setTrxTablesInUse(Integer trxTablesInUse) {
            this.trxTablesInUse = trxTablesInUse;
        }

        public String getTrxQuery() {
            return trxQuery;
        }

        public void setTrxQuery(String trxQuery) {
            this.trxQuery = trxQuery;
        }

        public String getTrxStarted() {
            return trxStarted;
        }

        public void setTrxStarted(String trxStarted) {
            this.trxStarted = trxStarted;
        }

        public String getTrxRequestedLockId() {
            return trxRequestedLockId;
        }

        public void setTrxRequestedLockId(String trxRequestedLockId) {
            this.trxRequestedLockId = trxRequestedLockId;
        }

        public String getTrxId() {
            return trxId;
        }

        public void setTrxId(String trxId) {
            this.trxId = trxId;
        }

        public Integer getTrxRowsLocked() {
            return trxRowsLocked;
        }

        public void setTrxRowsLocked(Integer trxRowsLocked) {
            this.trxRowsLocked = trxRowsLocked;
        }

        public String getTrxWaitStarted() {
            return trxWaitStarted;
        }

        public void setTrxWaitStarted(String trxWaitStarted) {
            this.trxWaitStarted = trxWaitStarted;
        }

        public String getTrxState() {
            return trxState;
        }

        public void setTrxState(String trxState) {
            this.trxState = trxState;
        }

        public Long getTrxMysqlThreadId() {
            return trxMysqlThreadId;
        }

        public void setTrxMysqlThreadId(Long trxMysqlThreadId) {
            this.trxMysqlThreadId = trxMysqlThreadId;
        }
    }
}
