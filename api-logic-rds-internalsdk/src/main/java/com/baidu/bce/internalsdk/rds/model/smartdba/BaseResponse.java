package com.baidu.bce.internalsdk.rds.model.smartdba;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseResponse {

    private DiskInfo diskInfo;

    public BaseResponse() {
    }

    public BaseResponse(DiskInfo diskInfo) {
        this.diskInfo = diskInfo;
    }

    public DiskInfo getDiskInfo() {
        return diskInfo;
    }

    public void setDiskInfo(DiskInfo diskInfo) {
        this.diskInfo = diskInfo;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Message {
        private String global;

        public String getGlobal() {
            return global;
        }

        public void setGlobal(String global) {
            this.global = global;
        }
    }
}
