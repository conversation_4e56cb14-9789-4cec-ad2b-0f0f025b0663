package com.baidu.bce.internalsdk.rds.model.security;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by luping03 on 18/4/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SslAccessibleRequest {

    private String instanceId;

    private boolean sslAccessible;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public boolean isSslAccessible() {
        return sslAccessible;
    }

    public void setSslAccessible(boolean sslAccessible) {
        this.sslAccessible = sslAccessible;
    }
}
