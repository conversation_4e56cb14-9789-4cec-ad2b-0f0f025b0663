package com.baidu.bce.internalsdk.rds.model.rogroup;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RoGroupListResponse {
    private List<RoGroupDetailResponse> roGroupList;

    public List<RoGroupDetailResponse> getRoGroupList() {
        return roGroupList;
    }

    public void setRoGroupList(List<RoGroupDetailResponse> roGroupList) {
        this.roGroupList = roGroupList;
    }
}
