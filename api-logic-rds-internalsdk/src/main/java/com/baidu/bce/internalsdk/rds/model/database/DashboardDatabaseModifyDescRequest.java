package com.baidu.bce.internalsdk.rds.model.database;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class DashboardDatabaseModifyDescRequest {
    @NotNull
    @IdPermission
    private String instanceId;
    @NotNull
    private String dbName;
    @NotNull
    private String remark;

    @Override
    public String toString() {
        return "DashboardDatabaseModifyDescRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", dbName='" + dbName + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
