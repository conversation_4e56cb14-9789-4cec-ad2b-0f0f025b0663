package com.baidu.bce.internalsdk.rds.model.smartdba;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GetSqlRequest {

    private Map<String, Long>  affectedRows;

    private String clientIP;

    private Integer clientPort;

    private Map<String, Double> duration;

    @IdPermission
    private String instanceId;

    private List<String> keys;

    private String keysRelation;

    private Long limit;

    private List<String> method;

    private Map<String, Long> numRows;

    private Long offset;

    private String sort;

    private List<String> status;

    private Map<String, String> timeRange;

    private String user;

    public Map<String, Long> getAffectedRows() {
        return affectedRows;
    }

    public void setAffectedRows(Map<String, Long> affectedRows) {
        this.affectedRows = affectedRows;
    }

    public String getClientIP() {
        return clientIP;
    }

    public void setClientIP(String clientIP) {
        this.clientIP = clientIP;
    }

    public Integer getClientPort() {
        return clientPort;
    }

    public void setClientPort(Integer clientPort) {
        this.clientPort = clientPort;
    }

    public Map<String, Double> getDuration() {
        return duration;
    }

    public void setDuration(Map<String, Double> duration) {
        this.duration = duration;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public List<String> getKeys() {
        return keys;
    }

    public void setKeys(List<String> keys) {
        this.keys = keys;
    }

    public String getKeysRelation() {
        return keysRelation;
    }

    public void setKeysRelation(String keysRelation) {
        this.keysRelation = keysRelation;
    }

    public Long getLimit() {
        return limit;
    }

    public void setLimit(Long limit) {
        this.limit = limit;
    }

    public List<String> getMethod() {
        return method;
    }

    public void setMethod(List<String> method) {
        this.method = method;
    }

    public Map<String, Long> getNumRows() {
        return numRows;
    }

    public void setNumRows(Map<String, Long> numRows) {
        this.numRows = numRows;
    }

    public Long getOffset() {
        return offset;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public List<String> getStatus() {
        return status;
    }

    public void setStatus(List<String> status) {
        this.status = status;
    }

    public Map<String, String> getTimeRange() {
        return timeRange;
    }

    public void setTimeRange(Map<String, String> timeRange) {
        this.timeRange = timeRange;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }
}
