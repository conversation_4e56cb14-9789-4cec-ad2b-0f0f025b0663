package com.baidu.bce.internalsdk.rds.model.database;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
public class DatabaseCheckExistResponse {

    private DuplicateListModel duplicateList;

    private Boolean isExist;

    public DuplicateListModel getDuplicateList() {
        return duplicateList;
    }

    public void setDuplicateList(DuplicateListModel duplicateList) {
        this.duplicateList = duplicateList;
    }

    public Boolean getIsExist() {
        return isExist;
    }

    public void setIsExist(Boolean isExist) {
        this.isExist = isExist;
    }

    public static class DuplicateListModel {
       private List<String> duplicateTables;

       private List<String> duplicateDatabases;

        public List<String> getDuplicateTables() {
            return duplicateTables;
        }

        public void setDuplicateTables(List<String> duplicateTables) {
            this.duplicateTables = duplicateTables;
        }

        public List<String> getDuplicateDatabases() {
            return duplicateDatabases;
        }

        public void setDuplicateDatabases(List<String> duplicateDatabases) {
            this.duplicateDatabases = duplicateDatabases;
        }
    }

}
