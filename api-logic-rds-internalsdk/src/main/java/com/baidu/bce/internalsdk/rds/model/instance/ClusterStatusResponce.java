package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClusterStatusResponce {

    private String clusterStatus;

    private CheckStatusResponse checkList;

    public String getClusterStatus() {
        return clusterStatus;
    }

    public void setClusterStatus(String clusterStatus) {
        this.clusterStatus = clusterStatus;
    }

    public CheckStatusResponse getCheckList() {
        return checkList;
    }

    public void setCheckList(CheckStatusResponse checkList) {
        this.checkList = checkList;
    }
}
