package com.baidu.bce.internalsdk.rds.model.account;

import java.util.Collection;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
public class AccountUpdatePrivilegesRequest {
    private Collection<Account.DatabasePrivilege> databasePrivileges;

    @Override
    public String toString() {
        return "AccountModifyPrivilegesResponse{" +
                "databasePrivileges=" + databasePrivileges +
                '}';
    }

    public Collection<Account.DatabasePrivilege> getDatabasePrivileges() {
        return databasePrivileges;
    }

    public void setDatabasePrivileges(Collection<Account.DatabasePrivilege> databasePrivileges) {
        this.databasePrivileges = databasePrivileges;
    }
}
