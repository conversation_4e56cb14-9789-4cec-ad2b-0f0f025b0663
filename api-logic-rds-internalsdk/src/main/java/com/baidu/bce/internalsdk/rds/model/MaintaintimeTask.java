package com.baidu.bce.internalsdk.rds.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MaintaintimeTask {
    private Integer taskId;
    private String taskType;
    private String taskName;
    @JSONField(serialize = false)
    private String appId;
    @JSONField(name = "instanceName")
    private String appName;
    private String userId;
    private String region;
    private String taskStatus;
    private String createTime;
    private String startTime;
    private String updateTime;
    private String finishTime;
    private Integer cancelFlag;
    @J<PERSON>NField(name = "instanceId")
    private String appShortId;
    private List<MaintaintimeTaskProgress> progress;
    private String effectiveTime;
    private int supportUpdate;
    private int percentage;
    private String maintainStartTime;
    private int maintainDuration;
    private String maintainPeriod;


    public String getMaintainStartTime() {
        return maintainStartTime;
    }

    public void setMaintainStartTime(String maintainStartTime) {
        this.maintainStartTime = maintainStartTime;
    }

    public int getMaintainDuration() {
        return maintainDuration;
    }

    public void setMaintainDuration(int maintainDuration) {
        this.maintainDuration = maintainDuration;
    }

    public String getMaintainPeriod() {
        return maintainPeriod;
    }

    public void setMaintainPeriod(String maintainPeriod) {
        this.maintainPeriod = maintainPeriod;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public int getSupportUpdate() {
        return supportUpdate;
    }

    public void setSupportUpdate(int supportUpdate) {
        this.supportUpdate = supportUpdate;
    }

    public int getPercentage() {
        return percentage;
    }

    public void setPercentage(int percentage) {
        this.percentage = percentage;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }

    public Integer getCancelFlag() {
        return cancelFlag;
    }

    public void setCancelFlag(Integer cancelFlag) {
        this.cancelFlag = cancelFlag;
    }

    public String getAppShortId() {
        return appShortId;
    }

    public void setAppShortId(String appShortId) {
        this.appShortId = appShortId;
    }

    public List<MaintaintimeTaskProgress> getProgress() {
        return progress;
    }

    public void setProgress(List<MaintaintimeTaskProgress> progress) {
        this.progress = progress;
    }
}
