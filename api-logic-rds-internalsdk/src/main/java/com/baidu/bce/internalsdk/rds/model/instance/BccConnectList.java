package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BccConnectList {

    private List<BccConnectMessage> bccConnectList;

    public List<BccConnectMessage> getBccConnectList() {
        return bccConnectList;
    }

    public void setBccConnectList(List<BccConnectMessage> bccConnectList) {
        this.bccConnectList = bccConnectList;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BccConnectMessage {
        private String bccInstanceId;
        private String bccInstanceName;
        private String connectStatus;
        private String bccVpcName;
        private String bccVpcCidr;
        private String bccVnetIp;
        private String updateTime;
        private String connectId;
        private String bccInstanceUuid;

        public String getBccInstanceId() {
            return bccInstanceId;
        }

        public void setBccInstanceId(String bccInstanceId) {
            this.bccInstanceId = bccInstanceId;
        }

        public String getBccInstanceName() {
            return bccInstanceName;
        }

        public void setBccInstanceName(String bccInstanceName) {
            this.bccInstanceName = bccInstanceName;
        }

        public String getConnectStatus() {
            return connectStatus;
        }

        public void setConnectStatus(String connectStatus) {
            this.connectStatus = connectStatus;
        }

        public String getBccVpcName() {
            return bccVpcName;
        }

        public void setBccVpcName(String bccVpcName) {
            this.bccVpcName = bccVpcName;
        }

        public String getBccVpcCidr() {
            return bccVpcCidr;
        }

        public void setBccVpcCidr(String bccVpcCidr) {
            this.bccVpcCidr = bccVpcCidr;
        }

        public String getBccVnetIp() {
            return bccVnetIp;
        }

        public void setBccVnetIp(String bccVnetIp) {
            this.bccVnetIp = bccVnetIp;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getConnectId() {
            return connectId;
        }

        public void setConnectId(String connectId) {
            this.connectId = connectId;
        }

        public String getBccInstanceUuid() {
            return bccInstanceUuid;
        }

        public void setBccInstanceUuid(String bccInstanceUuid) {
            this.bccInstanceUuid = bccInstanceUuid;
        }
    }
}
