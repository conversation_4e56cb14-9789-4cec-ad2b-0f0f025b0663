package com.baidu.bce.internalsdk.rds.model.smartdba;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SlowsqlTrend {
    private Integer interval;
    private List<Items> items;
    private List<Advice> indexAdvice;
    private List<Advice> statementAdvice;

    public Integer getInterval() {
        return interval;
    }

    public void setInterval(Integer interval) {
        this.interval = interval;
    }

    public List<Items> getItems() {
        return items;
    }

    public void setItems(List<Items> items) {
        this.items = items;
    }

    public List<Advice> getIndexAdvice() {
        return indexAdvice;
    }

    public void setIndexAdvice(List<Advice> indexAdvice) {
        this.indexAdvice = indexAdvice;
    }

    public List<Advice> getStatementAdvice() {
        return statementAdvice;
    }

    public void setStatementAdvice(List<Advice> statementAdvice) {
        this.statementAdvice = statementAdvice;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Items {
        private String datetime;
        private Integer times;

        public String getDatetime() {
            return datetime;
        }

        public void setDatetime(String datetime) {
            this.datetime = datetime;
        }

        public Integer getTimes() {
            return times;
        }

        public void setTimes(Integer times) {
            this.times = times;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Advice {
        private String advice;
        private Integer level;

        public String getAdvice() {
            return advice;
        }

        public void setAdvice(String advice) {
            this.advice = advice;
        }

        public Integer getLevel() {
            return level;
        }

        public void setLevel(Integer level) {
            this.level = level;
        }
    }

}
