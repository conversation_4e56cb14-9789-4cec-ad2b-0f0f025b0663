package com.baidu.bce.internalsdk.rds.model.group;


import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardGroupAccountCreateRequest {
    @NotNull
    private String groupId;

    private Account account;

    @Override
    public String toString() {
        return "DashboardAccountCreateRequest{" +
                "groupId='" + groupId + '\'' +
                ", account=" + getAccount() +
                '}';
    }

    public DashboardGroupAccountCreateRequest withGroupId(final String groupId) {
        this.setGroupId(groupId);
        return this;
    }

    public DashboardGroupAccountCreateRequest withAccount(final Account account) {
        this.setAccount(account);
        return this;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Account getAccount() {
        return account;
    }

    public void setAccount(Account account) {
        this.account = account;
    }
}
