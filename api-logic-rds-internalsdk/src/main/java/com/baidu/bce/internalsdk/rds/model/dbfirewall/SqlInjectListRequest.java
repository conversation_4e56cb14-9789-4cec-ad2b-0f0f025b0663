package com.baidu.bce.internalsdk.rds.model.dbfirewall;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/6.
 */
public class SqlInjectListRequest {

    private String startTime;

    private String endTime;

    private String dbName;

    private String accountName;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    @Override
    public String toString() {
        return "SqlInjectListRequest{"
                + "startTime=" + startTime
                + ", endTime=" + endTime
                + ", dbName='" + dbName + '\''
                + ", accountName='" + accountName + '\''
                + '}';
    }
}
