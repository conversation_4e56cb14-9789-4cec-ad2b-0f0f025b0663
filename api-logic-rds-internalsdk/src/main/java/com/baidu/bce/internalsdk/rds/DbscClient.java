package com.baidu.bce.internalsdk.rds;


import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceClientConfig;
import com.baidu.bce.internalsdk.core.BceInternalClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogDetailRequest;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogErrorDetails;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogSlowDetails;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTemplateResponse;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * DBSC 后端服务 Client
 */
public class DbscClient extends BceClient {

    private static final String SERVICE_NAME = "DBSC";

    private String accountId;

    private String userId;

    public DbscClient(String accountId, String userId,
            String accessKey, String secretKey, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey, securityToken);
        this.accountId = accountId;
        this.userId = userId;
    }

    public BceInternalRequest createDbscRequest() {
        BceClientConfig bceClientConfig = new BceClientConfig()
                .withReadTimeout(60000)
                .withMaxConnTotal(400)
                .withMaxConnPerRoute(400)
                ;

        Locale locale = LocaleContextHolder.getLocale();
        String localeVal;
        if (locale.getCountry() != null && !locale.getCountry().isEmpty()) {
            localeVal = locale.getLanguage() + "-" + StringUtils.lowerCase(locale.getCountry());
        } else {
            localeVal = locale.getLanguage();
        }

        BceInternalRequest bceInternalRequest = BceInternalClient.request(endpoint, bceClientConfig)
                .authorization(accessKey, secretKey);
        if (StringUtils.isNotEmpty(securityToken)) {
            bceInternalRequest.securityToken(securityToken);
        }
        bceInternalRequest.queryParam("locale", localeVal);
        bceInternalRequest.header("x-bce-dbsc-account-id", accountId);
        bceInternalRequest.header("x-bce-dbsc-user-id", userId);
        return bceInternalRequest;
    }

    public SlowsqlListResponse getSlowSqlList(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createDbscRequest()
                .path("/v1/slow-sql/detail/rds/" + instanceId);
        if (request.getPage() != null) {
            bceInternalRequest.queryParam("page", request.getPage() );
        }

        if (request.getPageSize() != null) {
            bceInternalRequest.queryParam("pageSize", request.getPageSize());
        }

        if (request.getOffset() != null) {
            bceInternalRequest.queryParam("offset", request.getOffset());
        }

        if (request.getSort() != null) {
            bceInternalRequest.queryParam("sort", request.getSort());
        }

        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getDigest() != null) {
            bceInternalRequest.queryParam("digest", request.getDigest());
        }

        if (request.getStart() != null) {
            bceInternalRequest.queryParam("start", request.getStart());
        }

        if (request.getEnd() != null) {
            bceInternalRequest.queryParam("end", request.getEnd());
        }

        return bceInternalRequest.get(SlowsqlListResponse.class);
    }

    public SlowsqlTemplateResponse getSlowSqlTemplate(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createDbscRequest()
                .path("/v1/slow-sql/stats/digest/rds/" + instanceId);
        if (request.getPage() != null) {
            bceInternalRequest.queryParam("page", request.getPage() );
        }

        if (request.getPageSize() != null) {
            bceInternalRequest.queryParam("pageSize", request.getPageSize());
        }

        if (request.getOffset() != null) {
            bceInternalRequest.queryParam("offset", request.getOffset());
        }

        if (request.getSort() != null) {
            bceInternalRequest.queryParam("sort", request.getSort());
        }

        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getDigest() != null) {
            bceInternalRequest.queryParam("digest", request.getDigest());
        }

        if (request.getStart() != null) {
            bceInternalRequest.queryParam("start", request.getStart());
        }

        if (request.getEnd() != null) {
            bceInternalRequest.queryParam("end", request.getEnd());
        }

        return bceInternalRequest.get(SlowsqlTemplateResponse.class);
    }

    public LogSlowDetails getSlowLogDetails(LogDetailRequest logDetailRequest) {
        BceInternalRequest bceInternalRequest = createDbscRequest();
        return bceInternalRequest
                .path("/v1/slow-sql/rds/logs/logSlowDetail")
                .post(Entity.json(logDetailRequest), LogSlowDetails.class);
    }

    public LogErrorDetails getErrorLogDetails(LogDetailRequest logDetailRequest) {
        BceInternalRequest bceInternalRequest = createDbscRequest();
        return bceInternalRequest
                .path("/v1/error-log/rds/logs/logErrorDetail")
                .post(Entity.json(logDetailRequest), LogErrorDetails.class);

    }


}

