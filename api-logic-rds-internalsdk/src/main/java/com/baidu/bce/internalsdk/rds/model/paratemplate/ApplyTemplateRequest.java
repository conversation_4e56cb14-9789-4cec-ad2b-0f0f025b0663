package com.baidu.bce.internalsdk.rds.model.paratemplate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ApplyTemplateRequest {

    @JsonProperty("instance_id")
    private String instanceId;
    private String effectiveTime;
    private boolean switchover;

    public ApplyTemplateRequest() {
    }

    public ApplyTemplateRequest(String instanceId, String effectiveTime, boolean switchover) {
        this.instanceId = instanceId;
        this.effectiveTime = effectiveTime;
        this.switchover = switchover;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public boolean isSwitchover() {
        return switchover;
    }

    public void setSwitchover(boolean switchover) {
        this.switchover = switchover;
    }
}
