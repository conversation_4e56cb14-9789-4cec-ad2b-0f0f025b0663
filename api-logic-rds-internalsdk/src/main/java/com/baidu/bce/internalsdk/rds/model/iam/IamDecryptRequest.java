package com.baidu.bce.internalsdk.rds.model.iam;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/8/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class IamDecryptRequest {

    @JsonProperty("accesskey_id")
    private String accesskeyId;

    @JsonProperty("cipher_hex")
    private String cipherHex;

    public String getAccesskeyId() {
        return accesskeyId;
    }

    public void setAccesskeyId(String accesskeyId) {
        this.accesskeyId = accesskeyId;
    }

    public IamDecryptRequest withAccesskeyId(String accesskeyId) {
        this.accesskeyId = accesskeyId;
        return this;
    }

    public String getCipherHex() {
        return cipherHex;
    }

    public void setCipherHex(String cipherHex) {
        this.cipherHex = cipherHex;
    }

    public IamDecryptRequest withCipherHex(String cipherHex) {
        this.cipherHex = cipherHex;
        return this;
    }

    @Override
    public String toString() {
        return "IamEncryptResponse{"
                + "accesskeyId='" + accesskeyId + '\''
                + ", cipherHex='" + cipherHex + '\''
                + '}';
    }
}
