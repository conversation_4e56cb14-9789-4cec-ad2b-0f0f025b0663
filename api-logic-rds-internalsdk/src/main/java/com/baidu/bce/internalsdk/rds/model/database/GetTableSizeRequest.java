package com.baidu.bce.internalsdk.rds.model.database;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/5/17 11:29
 */
public class GetTableSizeRequest {
    @NotNull
    @IdPermission
    private String instanceId;
    private Map<String, List> tableInfo;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Map<String, List> getTableInfo() {
        return tableInfo;
    }

    public void setTableInfo(Map<String, List> tableInfo) {
        this.tableInfo = tableInfo;
    }
}
