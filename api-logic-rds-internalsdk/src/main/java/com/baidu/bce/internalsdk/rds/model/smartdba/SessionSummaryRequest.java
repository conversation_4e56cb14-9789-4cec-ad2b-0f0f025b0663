package com.baidu.bce.internalsdk.rds.model.smartdba;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SessionSummaryRequest {
    private Long activeTotalCount;
    private Double cpuUtilizationRate;
    private Double maxExecuteTime;
    private Long totalCount;

    public Long getActiveTotalCount() {
        return activeTotalCount;
    }

    public void setActiveTotalCount(Long activeTotalCount) {
        this.activeTotalCount = activeTotalCount;
    }

    public Double getCpuUtilizationRate() {
        return cpuUtilizationRate;
    }

    public void setCpuUtilizationRate(Double cpuUtilizationRate) {
        this.cpuUtilizationRate = cpuUtilizationRate;
    }

    public Double getMaxExecuteTime() {
        return maxExecuteTime;
    }

    public void setMaxExecuteTime(Double maxExecuteTime) {
        this.maxExecuteTime = maxExecuteTime;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }
}
