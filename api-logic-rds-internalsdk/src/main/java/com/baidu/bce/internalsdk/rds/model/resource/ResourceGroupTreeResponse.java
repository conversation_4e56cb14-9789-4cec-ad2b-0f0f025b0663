package com.baidu.bce.internalsdk.rds.model.resource;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResourceGroupTreeResponse {

    private List<GroupTree> groups;

    public ResourceGroupTreeResponse() {
    }

    public List<GroupTree> getGroups() {
        return groups;
    }

    public void setGroups(List<GroupTree> groups) {
        this.groups = groups;
    }
}
