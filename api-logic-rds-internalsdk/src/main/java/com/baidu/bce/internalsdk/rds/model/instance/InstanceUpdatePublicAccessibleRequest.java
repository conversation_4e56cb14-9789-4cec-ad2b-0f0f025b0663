package com.baidu.bce.internalsdk.rds.model.instance;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
public class InstanceUpdatePublicAccessibleRequest {

    // OpenAPI 专用参数
    private Boolean publicAccess;

    private boolean publiclyAccessible;

    @Override
    public String toString() {
        return "InstanceUpdatePublicAccessible{" +
                "publiclyAccessible=" + publiclyAccessible +
                '}';
    }

    public Boolean getPublicAccess() {
        return publicAccess;
    }

    public void setPublicAccess(Boolean publicAccess) {
        this.publicAccess = publicAccess;
    }

    public boolean isPubliclyAccessible() {
        return publiclyAccessible;
    }

    public void setPubliclyAccessible(boolean publiclyAccessible) {
        this.publiclyAccessible = publiclyAccessible;
    }
}
