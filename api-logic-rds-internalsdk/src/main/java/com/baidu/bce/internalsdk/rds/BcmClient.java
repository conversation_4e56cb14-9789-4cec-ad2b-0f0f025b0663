package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.rds.model.instance.BcmGroupResponses;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;

public class BcmClient extends BceClient {

    private static final String SERVICE_NAME = "BCM";
    private boolean isHostWithPort;
    private String token;

    public BcmClient(String accessKey, String secretKey, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey, securityToken);
        this.isHostWithPort = false;
    }

    public BcmClient(String endpoint, String accessKey, String secretKey, String token) {
        super(endpoint, accessKey, secretKey);
        this.isHostWithPort = false;
        this.token = token;
    }

    public BcmClient(String accessKey, String secretKey) {
        this(EndpointManager.getEndpoint(SERVICE_NAME), access<PERSON>ey, secretKey, (String) null);
    }

    public BceInternalRequest createInternalRequest() {
        return super.createAuthorizedRequest();
    }


    public BcmGroupResponses bcmGroupList(String userId, String serviceName, String region,
                                          Integer pageNo, Integer pageSize) {

        BceInternalRequest request = this.createInternalRequest();
        request.path("/csm/api/v1/userId/" + userId + "/instance-group/list");
        if (StringUtils.isNotEmpty(userId)) {
            request.queryParam("userId", userId);
        }
        if (StringUtils.isNotEmpty(serviceName)) {
            request.queryParam("serviceName", serviceName);
        }
        if (StringUtils.isNotEmpty(region)) {
            request.queryParam("region", region);
        }
        if (pageNo != null) {
            request.queryParam("pageNo", pageNo);
        }
        if (pageSize != null) {
            request.queryParam("pageSize", pageSize);
        }

        return request.get(BcmGroupResponses.class);
    }
}
