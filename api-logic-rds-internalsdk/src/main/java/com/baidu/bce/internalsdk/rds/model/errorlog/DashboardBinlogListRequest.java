package com.baidu.bce.internalsdk.rds.model.errorlog;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DashboardBinlogListRequest extends CommonListRequest {
    @NotNull
    private Date date;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DashboardBinlogListRequest{");
        sb.append("date=").append(date);
        sb.append('}');
        return sb.toString();
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getDate() {
        return date;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setDate(Date date) {
        this.date = date;
    }
}
