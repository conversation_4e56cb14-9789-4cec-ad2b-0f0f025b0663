package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/12/22-16:15.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardInstanceUpdateConfigModel {
    @NotNull
    private String instanceId;

    @Min(1)
    private Integer allocatedMemoryInMB;

    @Min(1)
    private Integer allocatedMemoryInGB;

    @Min(1)
    private Integer allocatedStorageInGB;

    private Integer nodeAmount;

    private Integer cpuCount;

    private String category;

    @NotNull
    private String productType;

    private Boolean isEnhanced; // 上海金融专区三节点增强版标识

    private Boolean oldFlavor = false; // 是否是老套餐（上一代）

    private String diskIoType;

    private Integer forceHotUpgrade;

    private String masterAzone;

    private String backupAzone;

    private String subnetId;

    private String effectiveTime;

    private String edgeSubnetId;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DashboardInstanceUpdateConfigModel{");
        sb.append("instanceId='").append(instanceId).append('\'');
        sb.append(", allocatedMemoryInMB=").append(allocatedMemoryInMB);
        sb.append(", allocatedStorageInGB=").append(allocatedStorageInGB);
        sb.append(", productType=").append(productType);
        sb.append(", isEnhanced=").append(isEnhanced);
        sb.append('}');
        return sb.toString();
    }

    public Boolean getOldFlavor() {
        return oldFlavor;
    }

    public void setOldFlavor(Boolean oldFlavor) {
        this.oldFlavor = oldFlavor;
    }

    public Integer getCpuCount() {
        return cpuCount;
    }

    public void setCpuCount(Integer cpuCount) {
        this.cpuCount = cpuCount;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Integer getNodeAmount() {
        return nodeAmount;
    }

    public void setNodeAmount(Integer nodeAmount) {
        this.nodeAmount = nodeAmount;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getAllocatedMemoryInMB() {
        return allocatedMemoryInMB;
    }

    public void setAllocatedMemoryInMB(Integer allocatedMemoryInMB) {
        this.allocatedMemoryInMB = allocatedMemoryInMB;
    }

    public Integer getAllocatedStorageInGB() {
        return allocatedStorageInGB;
    }

    public void setAllocatedStorageInGB(Integer allocatedStorageInGB) {
        this.allocatedStorageInGB = allocatedStorageInGB;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public Integer getAllocatedMemoryInGB() {
        return allocatedMemoryInGB;
    }

    public void setAllocatedMemoryInGB(Integer allocatedMemoryInGB) {
        this.allocatedMemoryInGB = allocatedMemoryInGB;
    }

    public Boolean getIsEnhanced() {
        return isEnhanced;
    }

    public void setIsEnhanced(Boolean enhanced) {
        isEnhanced = enhanced;
    }

    public String getDiskIoType() {
        return diskIoType;
    }

    public void setDiskIoType(String diskIoType) {
        this.diskIoType = diskIoType;
    }

    public Integer getForceHotUpgrade() {
        return forceHotUpgrade;
    }

    public void setForceHotUpgrade(Integer forceHotUpgrade) {
        this.forceHotUpgrade = forceHotUpgrade;
    }

    public String getMasterAzone() {
        return masterAzone;
    }

    public void setMasterAzone(String masterAzone) {
        this.masterAzone = masterAzone;
    }

    public String getBackupAzone() {
        return backupAzone;
    }

    public void setBackupAzone(String backupAzone) {
        this.backupAzone = backupAzone;
    }

    public String getSubnetId() {
        return subnetId;
    }

    public void setSubnetId(String subnetId) {
        this.subnetId = subnetId;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public String getEdgeSubnetId() {
        return edgeSubnetId;
    }

    public void setEdgeSubnetId(String edgeSubnetId) {
        this.edgeSubnetId = edgeSubnetId;
    }
}
