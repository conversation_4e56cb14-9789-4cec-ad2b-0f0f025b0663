package com.baidu.bce.internalsdk.rds.model.security;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BlbIdsResponses {

    private List<BlbIdResponse> appBLBs;

    public List<BlbIdResponse> getAppBLBs() {
        return appBLBs;
    }

    public void setAppBLBs(List<BlbIdResponse> appBLBs) {
        this.appBLBs = appBLBs;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BlbIdResponse {
        private String appId;
        private String blbName;
        private String blbId;
        private String blbShortId;
        private String endpointId;
        private String resourceType;
        private String  blbUserId;
        private String vpcId;


        public String getVpcId() {
            return vpcId;
        }

        public void setVpcId(String vpcId) {
            this.vpcId = vpcId;
        }

        public String getEndpointId() {
            return endpointId;
        }

        public void setEndpointId(String endpointId) {
            this.endpointId = endpointId;
        }

        public String getResourceType() {
            return resourceType;
        }

        public void setResourceType(String resourceType) {
            this.resourceType = resourceType;
        }

        public String getBlbUserId() {
            return blbUserId;
        }

        public void setBlbUserId(String blbUserId) {
            this.blbUserId = blbUserId;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getBlbName() {
            return blbName;
        }

        public void setBlbName(String blbName) {
            this.blbName = blbName;
        }

        public String getBlbId() {
            return blbId;
        }

        public void setBlbId(String blbId) {
            this.blbId = blbId;
        }

        public String getBlbShortId() {
            return blbShortId;
        }

        public void setBlbShortId(String blbShortId) {
            this.blbShortId = blbShortId;
        }
    }
}
