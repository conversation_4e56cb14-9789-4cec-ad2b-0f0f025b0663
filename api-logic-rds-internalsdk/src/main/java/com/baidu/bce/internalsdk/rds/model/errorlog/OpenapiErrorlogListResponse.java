package com.baidu.bce.internalsdk.rds.model.errorlog;


import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Collection;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/9.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenapiErrorlogListResponse {
    private Collection<OpenapiErrorlog> errorlogs;

    public Collection<OpenapiErrorlog> getErrorlogs() {
        return errorlogs;
    }

    public void setErrorlogs(Collection<OpenapiErrorlog> errorlogs) {
        this.errorlogs = errorlogs;
    }

    public void setLogs(Collection<OpenapiErrorlog> logs) {
        this.errorlogs = logs;
    }
}
