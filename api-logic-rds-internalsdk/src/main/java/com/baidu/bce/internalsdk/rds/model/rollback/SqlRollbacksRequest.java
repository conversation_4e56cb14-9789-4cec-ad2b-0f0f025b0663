package com.baidu.bce.internalsdk.rds.model.rollback;

import com.baidu.bce.internalsdk.rds.util.PatternString;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.Pattern;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SqlRollbacksRequest {

    @Pattern(regexp = PatternString.PATTERN_INSTANCE_NAME)
    private String name;
    private List<String> sqlType;
    private String binlogFlashType;
    private String binlogFlashStartTime;
    private String binlogFlashEndTime;
    private List<SchemaTables> schemaTables;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getSqlType() {
        return sqlType;
    }

    public void setSqlType(List<String> sqlType) {
        this.sqlType = sqlType;
    }

    public String getBinlogFlashType() {
        return binlogFlashType;
    }

    public void setBinlogFlashType(String binlogFlashType) {
        this.binlogFlashType = binlogFlashType;
    }

    public String getBinlogFlashStartTime() {
        return binlogFlashStartTime;
    }

    public void setBinlogFlashStartTime(String binlogFlashStartTime) {
        this.binlogFlashStartTime = binlogFlashStartTime;
    }

    public String getBinlogFlashEndTime() {
        return binlogFlashEndTime;
    }

    public void setBinlogFlashEndTime(String binlogFlashEndTime) {
        this.binlogFlashEndTime = binlogFlashEndTime;
    }

    public List<SchemaTables> getSchemaTables() {
        return schemaTables;
    }

    public void setSchemaTables(List<SchemaTables> schemaTables) {
        this.schemaTables = schemaTables;
    }
}
