package com.baidu.bce.internalsdk.rds.model.group;

import javax.validation.constraints.NotNull;

/**
 * Created by luping03 on 17/6/19.
 */
public class DashboardGroupDataBaseCheckRequest extends GroupIdRequest {
    @NotNull
    private String dbName;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DashboardDataBaseCheckRequest{");
        sb.append("dbName=").append(dbName);
        sb.append('}');
        return sb.toString();
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }
}
