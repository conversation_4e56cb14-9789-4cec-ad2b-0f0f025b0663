package com.baidu.bce.internalsdk.rds.model.migration;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/9.
 */
public class DashboardMigrationCommitRequest extends MigrationConfig {
    @NotNull
    @IdPermission
    private String instanceId;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DashboardMigrationCommitRequest{");
        sb.append("instanceId='").append(instanceId).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }
}
