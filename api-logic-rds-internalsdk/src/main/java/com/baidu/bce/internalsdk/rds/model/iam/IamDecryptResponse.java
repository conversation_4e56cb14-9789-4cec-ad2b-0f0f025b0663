package com.baidu.bce.internalsdk.rds.model.iam;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/8/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class IamDecryptResponse {

    @JsonProperty("raw_hex")
    private String rawHex;

    @JsonProperty("user_id")
    private String userId;

    public String getRawHex() {
        return rawHex;
    }

    public void setRawHex(String rawHex) {
        this.rawHex = rawHex;
    }

    public IamDecryptResponse withRawHex(String rawHex) {
        this.rawHex = rawHex;
        return this;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public IamDecryptResponse withUserId(String userId) {
        this.userId = userId;
        return this;
    }

    @Override
    public String toString() {
        return "IamEncryptRequest{"
                + "rawHex='" + rawHex + '\''
                + ", userId='" + userId + '\''
                + '}';
    }
}
