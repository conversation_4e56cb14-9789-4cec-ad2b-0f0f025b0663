package com.baidu.bce.internalsdk.rds.model.group;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class CommonGroupListRequest {
    @NotNull
    private String groupId;
    private String orderBy;
    private String order;
    private int pageNo;
    private int pageSize;

    @Override
    public String toString() {
        return "CommonListRequest{"
                + "groupId='" + getGroupId() + '\''
                + ", orderBy='" + orderBy + '\''
                + ", order='" + order + '\''
                + ", pageNo=" + pageNo
                + ", pageSize=" + pageSize
                + '}';
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
}
