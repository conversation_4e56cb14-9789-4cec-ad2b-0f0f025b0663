package com.baidu.bce.internalsdk.rds.model.paratemplate;

public class DatebaseParameters {

    private String name;

    private Boolean dynamic;

    private Integer defaultValue;

    private String attention;

    private Boolean modifiable;

    private String allowedValues;

    private String configType;

    private Boolean ifCluster;

    private Integer bestValue;

    private String descriptionCN;

    private String solidifiedName;

    private String type;

    public DatebaseParameters() {
    }

    public DatebaseParameters(String name, Boolean dynamic, Integer defaultValue, String attention, Boolean modifiable,
                              String allowedValues, String configType, Boolean ifCluster, Integer bestValue,
                              String descriptionCN, String solidifiedName, String type) {
        this.name = name;
        this.dynamic = dynamic;
        this.defaultValue = defaultValue;
        this.attention = attention;
        this.modifiable = modifiable;
        this.allowedValues = allowedValues;
        this.configType = configType;
        this.ifCluster = ifCluster;
        this.bestValue = bestValue;
        this.descriptionCN = descriptionCN;
        this.solidifiedName = solidifiedName;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getDynamic() {
        return dynamic;
    }

    public void setDynamic(Boolean dynamic) {
        this.dynamic = dynamic;
    }

    public Integer getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(Integer defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getAttention() {
        return attention;
    }

    public void setAttention(String attention) {
        this.attention = attention;
    }

    public Boolean getModifiable() {
        return modifiable;
    }

    public void setModifiable(Boolean modifiable) {
        this.modifiable = modifiable;
    }

    public String getAllowedValues() {
        return allowedValues;
    }

    public void setAllowedValues(String allowedValues) {
        this.allowedValues = allowedValues;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public Boolean getIfCluster() {
        return ifCluster;
    }

    public void setIfCluster(Boolean ifCluster) {
        this.ifCluster = ifCluster;
    }

    public Integer getBestValue() {
        return bestValue;
    }

    public void setBestValue(Integer bestValue) {
        this.bestValue = bestValue;
    }

    public String getDescriptionCN() {
        return descriptionCN;
    }

    public void setDescriptionCN(String descriptionCN) {
        this.descriptionCN = descriptionCN;
    }

    public String getSolidifiedName() {
        return solidifiedName;
    }

    public void setSolidifiedName(String solidifiedName) {
        this.solidifiedName = solidifiedName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "DatebaseParameters{" +
                "name='" + name + '\'' +
                ", dynamic=" + dynamic +
                ", defaultValue=" + defaultValue +
                ", attention='" + attention + '\'' +
                ", modifiable=" + modifiable +
                ", allowedValues='" + allowedValues + '\'' +
                ", configType='" + configType + '\'' +
                ", ifCluster=" + ifCluster +
                ", bestValue=" + bestValue +
                ", descriptionCN='" + descriptionCN + '\'' +
                ", solidifiedName='" + solidifiedName + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}
