package com.baidu.bce.internalsdk.rds.model.database;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/17 10:13
 */
public class ListDatabasesResponse {
    private List<String> databases;
    private Integer pageSize;
    private Integer pageNo;
    private Integer count;
    private Long restDisk;
    private Long usedDisk;


    @Override
    public String toString() {
        return "ListDatabasesResponse{" +
                "databases=" + databases +
                ", pageSize=" + pageSize +
                ", pageNo=" + pageNo +
                ", count=" + count +
                ", restDisk=" + restDisk +
                ", usedDisk=" + usedDisk +
                '}';
    }

    public List<String> getDatabases() {
        return databases;
    }

    public void setDatabases(List<String> databases) {
        this.databases = databases;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Long getRestDisk() {
        return restDisk;
    }

    public void setRestDisk(Long restDisk) {
        this.restDisk = restDisk;
    }

    public Long getUsedDisk() {
        return usedDisk;
    }

    public void setUsedDisk(Long usedDisk) {
        this.usedDisk = usedDisk;
    }
}
