package com.baidu.bce.internalsdk.rds.model;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AutoResizeConfigResponse {

    private Integer autoResizeDisk;
    private Integer freeSpaceThreshold;
    private Integer diskMaxLimit;
    private Integer extendStepPercent;

    public Integer getAutoResizeDisk() {
        return autoResizeDisk;
    }

    public void setAutoResizeDisk(Integer autoResizeDisk) {
        this.autoResizeDisk = autoResizeDisk;
    }

    public Integer getFreeSpaceThreshold() {
        return freeSpaceThreshold;
    }

    public void setFreeSpaceThreshold(Integer freeSpaceThreshold) {
        this.freeSpaceThreshold = freeSpaceThreshold;
    }

    public Integer getDiskMaxLimit() {
        return diskMaxLimit;
    }

    public void setDiskMaxLimit(Integer diskMaxLimit) {
        this.diskMaxLimit = diskMaxLimit;
    }

    public Integer getExtendStepPercent() {
        return extendStepPercent;
    }

    public void setExtendStepPercent(Integer extendStepPercent) {
        this.extendStepPercent = extendStepPercent;
    }
}
