package com.baidu.bce.internalsdk.rds.model.security;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.LinkedList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DashboardWhitelistModifyRequest {
    @NotNull
    @IdPermission
    private String instanceId;
    private Collection<String> securityIps = new LinkedList<String>();
    @NotNull
    private String ETag;

    @Override
    public String toString() {
        return "DashboardWhitelistModifyRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", securityIps=" + securityIps +
                ", ETag='" + ETag + '\'' +
                '}';
    }

    @JsonProperty("ETag")
    public String getETag() {
        return ETag;
    }

    @JsonProperty("ETag")
    public void setETag(String ETag) {
        this.ETag = ETag;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Collection<String> getSecurityIps() {
        return securityIps;
    }

    public void setSecurityIps(Collection<String> securityIps) {
        this.securityIps = securityIps;
    }

}
