package com.baidu.bce.internalsdk.rds.model.account;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
public class AccountUpdatePasswordRequest {
    private String password;

    private String encryptedPassword;

    @Override
    public String toString() {
        return "AccountUpdatePasswordResponse{" +
                "password='" + password + '\'' +
                '}';
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEncryptedPassword() {
        return encryptedPassword;
    }

    public void setEncryptedPassword(String encryptedPassword) {
        this.encryptedPassword = encryptedPassword;
    }
}
