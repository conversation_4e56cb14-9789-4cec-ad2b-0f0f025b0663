package com.baidu.bce.internalsdk.rds.model.smartdba;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;
import java.util.Map;
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SmartDbaTopoResponse {


    private BaseResponse.Message message = new BaseResponse.Message();

    private SmartDbaClusterResult result;

    private Boolean success;

    private String status;

    public SmartDbaTopoResponse() {
    }

    public SmartDbaTopoResponse(BaseResponse.Message message,
                                SmartDbaClusterResult result, Boolean success, String status) {
        this.message = message;
        this.result = result;
        this.success = success;
        this.status = status;
    }

    public BaseResponse.Message getMessage() {
        return message;
    }

    public void setMessage(BaseResponse.Message message) {
        this.message = message;
    }

    public SmartDbaClusterResult getResult() {
        return result;
    }

    public void setResult(SmartDbaClusterResult result) {
        this.result = result;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SmartDbaTopoResponse{" +
                "message='" + message + '\'' +
                ", result=" + result +
                ", success=" + success +
                ", status='" + status + '\'' +
                '}';
    }

    public class SmartDbaClusterResult {

        private String instanceId;

        private List<SmartDbaClusterResponse> cluster;

        private Map instanceList;

        private List<SmartDbaTopoRogroupResponse> roGroupList;

        public SmartDbaClusterResult(String instanceId, List<SmartDbaClusterResponse> cluster
                , Map instanceList, List<SmartDbaTopoRogroupResponse> roGroupList) {
            this.instanceId = instanceId;
            this.cluster = cluster;
            this.instanceList = instanceList;
            this.roGroupList = roGroupList;
        }

        public SmartDbaClusterResult() {
        }

        public String getInstanceId() {
            return instanceId;
        }

        public void setInstanceId(String instanceId) {
            this.instanceId = instanceId;
        }

        public List<SmartDbaClusterResponse> getCluster() {
            return cluster;
        }

        public void setCluster(List<SmartDbaClusterResponse> cluster) {
            this.cluster = cluster;
        }

        public Map getInstanceList() {
            return instanceList;
        }

        public void setInstanceList(Map instanceList) {
            this.instanceList = instanceList;
        }

        public List<SmartDbaTopoRogroupResponse> getRoGroupList() {
            return roGroupList;
        }

        public void setRoGroupList(List<SmartDbaTopoRogroupResponse> roGroupList) {
            this.roGroupList = roGroupList;
        }

        @Override
        public String toString() {
            return "SmartDbaClusterResult{" +
                    "instanceId='" + instanceId + '\'' +
                    ", cluster=" + cluster +
                    ", instanceList=" + instanceList +
                    ", roGroupList=" + roGroupList +
                    '}';
        }
    }


}
