package com.baidu.bce.internalsdk.rds.model.account;

import com.baidu.bce.internalsdk.rds.util.PatternString;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TableLevelAccountDetailRequest {
    @NotEmpty
    @Pattern(regexp = PatternString.PATTERN_ACCOUNT_NAME)
    private String user;
    private String host;
    private String password;
    private String type;
    private String superUserFlag;
    private String remark;
    private TableLevelPrivilegeRequest privilege;


    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSuperUserFlag() {
        return superUserFlag;
    }

    public void setSuperUserFlag(String superUserFlag) {
        this.superUserFlag = superUserFlag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public TableLevelPrivilegeRequest getPrivilege() {
        return privilege;
    }

    public void setPrivilege(TableLevelPrivilegeRequest privilege) {
        this.privilege = privilege;
    }
}
