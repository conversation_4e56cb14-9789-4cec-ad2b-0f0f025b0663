package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/19.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DashboardInstanceModifyPublicRequest {
    @NotNull
    @IdPermission
    private String instanceId;
    private boolean publiclyAccessible;

    @Override
    public String toString() {
        return "DashboardInstanceModifyPublicRequest{"
                + "instanceId='" + instanceId + '\''
                + ", publiclyAccessible=" + publiclyAccessible
                + '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public boolean isPubliclyAccessible() {
        return publiclyAccessible;
    }

    public void setPubliclyAccessible(boolean publiclyAccessible) {
        this.publiclyAccessible = publiclyAccessible;
    }
}
