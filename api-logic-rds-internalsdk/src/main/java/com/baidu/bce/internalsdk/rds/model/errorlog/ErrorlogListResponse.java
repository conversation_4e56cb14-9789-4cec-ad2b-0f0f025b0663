package com.baidu.bce.internalsdk.rds.model.errorlog;


import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Collection;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/9.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorlogListResponse {
    private Collection<Errorlog> errorlogs;

    public Collection<Errorlog> getErrorlogs() {
        return errorlogs;
    }

    public void setErrorlogs(Collection<Errorlog> errorlogs) {
        this.errorlogs = errorlogs;
    }

    public void setLogs(Collection<Errorlog> logs) {
        this.errorlogs = logs;
    }
}
