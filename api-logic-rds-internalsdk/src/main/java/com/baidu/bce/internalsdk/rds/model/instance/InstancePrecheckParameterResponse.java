package com.baidu.bce.internalsdk.rds.model.instance;

import java.util.Map;

public class InstancePrecheckParameterResponse {
    private Map<String,Parameter> parameters;

    public Map<String, Parameter> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Parameter> parameters) {
        this.parameters = parameters;
    }

    public static class Parameter {
        private String beforeValue;
        private String afterValue;

        public String getBeforeValue() {
            return beforeValue;
        }

        public void setBeforeValue(String beforeValue) {
            this.beforeValue = beforeValue;
        }

        public String getAfterValue() {
            return afterValue;
        }

        public void setAfterValue(String afterValue) {
            this.afterValue = afterValue;
        }
    }
}
