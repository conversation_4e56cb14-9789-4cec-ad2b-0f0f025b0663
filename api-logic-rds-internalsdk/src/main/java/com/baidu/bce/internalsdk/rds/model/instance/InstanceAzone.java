package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class InstanceAzone {
    @IdPermission
    private String instanceId;

    private String azone;

    @JsonProperty("master_azone")
    private String logicalZoneMaster;

    @JsonProperty("backup_azone")
    private String logicalZoneBackup;

    @JsonProperty("subnet_id")
    private Map<String, String> subnetId = new HashMap<>();

    private List<String> zoneNames;

    private List<SubnetMap> subnets;

    private String effectiveTime;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getAzone() {
        return azone;
    }

    public void setAzone(String azone) {
        this.azone = azone;
    }

    public String getLogicalZoneMaster() {
        return logicalZoneMaster;
    }

    public void setLogicalZoneMaster(String logicalZoneMaster) {
        this.logicalZoneMaster = logicalZoneMaster;
    }

    public String getLogicalZoneBackup() {
        return logicalZoneBackup;
    }

    public void setLogicalZoneBackup(String logicalZoneBackup) {
        this.logicalZoneBackup = logicalZoneBackup;
    }

    public Map<String, String> getSubnetId() {
        return subnetId;
    }

    public void setSubnetId(Map<String, String> subnetId) {
        this.subnetId = subnetId;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public List<String> getZoneNames() {
        return zoneNames;
    }

    public void setZoneNames(List<String> zoneNames) {
        this.zoneNames = zoneNames;
    }

    public List<SubnetMap> getSubnets() {
        return subnets;
    }

    public void setSubnets(List<SubnetMap> subnets) {
        this.subnets = subnets;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static final class SubnetMap {
        private String zoneName;
        private String subnetId;

        public String getZoneName() {
            return zoneName;
        }

        public void setZoneName(String zoneName) {
            this.zoneName = zoneName;
        }

        public String getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(String subnetId) {
            this.subnetId = subnetId;
        }
    }
}
