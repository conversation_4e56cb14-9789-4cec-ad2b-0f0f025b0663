package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.wordnik.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/12/23-14:30.
 */
public class InstanceUpdateFlavorRequest {

    /**
     * 该类 Copy 自 {@link com.baidu.bce.internalsdk.rds.model.instance.InstanceCreateRequest.InstanceParameters}。
     * 之前创建实例请求和变配变配请求共用了该类，这种做法不太好，不容易区分哪些是创建实例字段和变配实例字段；并且，当两者同时需要一个字段，
     * 且类型不同时，将无法进行下次，现在为了解决这个问题（edgeSubnetId 字段），将两者分开处理。
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InstanceParameters {
        private String sourceInstanceId;

        @Valid
        private SnapshotPolicy backupPolicy; // 后端反馈：创建时不可设置备份参数
        private String engine;
        private String engineVersion;

        private Date instanceExpireTime;
        private String instanceClass;
        private String instanceName;
        private InstanceCreateRequest.InitialDataReference initialDataReference;
        private List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> data;
        private Boolean publiclyAccessible;
        private Integer allocatedStorageInGB;
        private Integer allocatedMemoryInMB;
        private Integer cpuCount;

        private String characterSetName;

        private String azone;

        private String vpcId;

        private Map<String, String> subnetId;

        private Map<String, String> physicalZone;

        // 三节点增强版MySQL rds新增字段
        private Boolean isSingle;

        // 三节点增强版MySQL rds新增字段
        private Boolean isEnhanced;

        private Integer nodeAmount;

        private String replicationType; // 不可以设置

        private String instanceType;    // 表示产品类型，financial为raft版

        private InstanceCreateRequest.DccHostInfo dccHostIds = new InstanceCreateRequest.DccHostInfo();

        private String machineType;

        private String diskIoType;

        private String tags;

        private String diskType;

        private String cdsType;
        private String ovip;

        /* 专属集群ID。如果bgwGroupExclusive为true，要将blb分配到哪个专属集群，如果不传默认分配到用户名下BLB实例最少的专属集群 */
        private String bgwGroupId;

        /* 是否使用专属集群 */
        private boolean bgwGroupExclusive;

        private Integer entryPort;

        private Integer lowerCaseTableNames;

        private Integer forceHotUpgrade;

        private String masterAzone;

        private String backupAzone;

        // 参数模板
        private String parameterTemplateId;

        private String category;

        private String effectiveTime;

        private String edgeSubnetId;

        private List<ResizeDiskRelatedInstance> resizeDiskRelatedInstanceList;

        public List<ResizeDiskRelatedInstance> getResizeDiskRelatedInstanceList() {
            return resizeDiskRelatedInstanceList;
        }

        public void setResizeDiskRelatedInstanceList(List<ResizeDiskRelatedInstance> resizeDiskRelatedInstanceList) {
            this.resizeDiskRelatedInstanceList = resizeDiskRelatedInstanceList;
        }

        public String getEdgeSubnetId() {
            return edgeSubnetId;
        }

        public void setEdgeSubnetId(String edgeSubnetId) {
            this.edgeSubnetId = edgeSubnetId;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("InstanceParameters{");
            sb.append("sourceInstanceId='").append(sourceInstanceId).append('\'');
            sb.append(", backupPolicy=").append(backupPolicy);
            sb.append(", engine='").append(engine).append('\'');
            sb.append(", engineVersion='").append(engineVersion).append('\'');
            sb.append(", instanceExpireTime=").append(instanceExpireTime);
            sb.append(", instanceClass='").append(instanceClass).append('\'');
            sb.append(", instanceName='").append(instanceName).append('\'');
            sb.append(", publiclyAccessible=").append(publiclyAccessible);
            sb.append(", allocatedStorageInGB=").append(allocatedStorageInGB);
            sb.append(", allocatedMemoryInMB=").append(allocatedMemoryInMB);
            sb.append(", characterSetName='").append(characterSetName).append('\'');
            sb.append(", azone='").append(azone).append('\'');
            sb.append(", isEnhanced='").append(isEnhanced).append('\'');
            sb.append(", instanceType='").append(instanceType).append('\'');
            sb.append(", isSingle='").append(isSingle).append('\'');
            sb.append(", diskType='").append(diskType).append('\'');
            sb.append(", cdsType='").append(cdsType).append('\'');
            sb.append(", initialDataReference='").append(initialDataReference).append('\'');
            sb.append(", data='").append(data).append('\'');
            sb.append('}');
            return sb.toString();
        }

        public InstanceCreateRequest.DccHostInfo getDccHostIds() {
            return dccHostIds;
        }

        public void setDccHostIds(InstanceCreateRequest.DccHostInfo dccHostIds) {
            this.dccHostIds = dccHostIds;
        }


        public String getMachineType() {
            return machineType;
        }

        public void setMachineType(String machineType) {
            this.machineType = machineType;
        }

        public Map<String, String> getPhysicalZone() {
            return physicalZone;
        }

        public void setPhysicalZone(Map<String, String> physicalZone) {
            this.physicalZone = physicalZone;
        }

        public Integer getCpuCount() {
            return cpuCount;
        }

        public void setCpuCount(Integer cpuCount) {
            this.cpuCount = cpuCount;
        }

        public InstanceParameters withCpuCount(Integer cpuCount) {
            this.cpuCount = cpuCount;
            return this;
        }

        public String getReplicationType() {
            return replicationType;
        }

        public void setReplicationType(String replicationType) {
            this.replicationType = replicationType;
        }

        public Boolean getEnhanced() {
            return isEnhanced;
        }

        public void setEnhanced(Boolean enhanced) {
            isEnhanced = enhanced;
        }

        public Integer getNodeAmount() {
            return nodeAmount;
        }

        public void setNodeAmount(Integer nodeAmount) {
            this.nodeAmount = nodeAmount;
        }

        public String getSourceInstanceId() {
            return sourceInstanceId;
        }

        public void setSourceInstanceId(String sourceInstanceId) {
            this.sourceInstanceId = sourceInstanceId;
        }

        @ApiModelProperty(
                value = "备份策略",
                required = false)
        public SnapshotPolicy getBackupPolicy() {
            return backupPolicy;
        }

        public void setBackupPolicy(SnapshotPolicy backupPolicy) {
            this.backupPolicy = backupPolicy;
        }

        @ApiModelProperty(
                value = "引擎类型",
                required = true)
        public String getEngine() {
            return engine;
        }

        public void setEngine(String engine) {
            this.engine = engine;
        }

        public String getEngineVersion() {
            return engineVersion;
        }

        public void setEngineVersion(String engineVersion) {
            this.engineVersion = engineVersion;
        }

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        public Date getInstanceExpireTime() {
            return instanceExpireTime;
        }

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        public void setInstanceExpireTime(Date instanceExpireTime) {
            this.instanceExpireTime = instanceExpireTime;
        }

        public String getInstanceClass() {
            return instanceClass;
        }

        public void setInstanceClass(String instanceClass) {
            this.instanceClass = instanceClass;
        }

        public String getInstanceName() {
            return instanceName;
        }

        public void setInstanceName(String instanceName) {
            this.instanceName = instanceName;
        }

        public Boolean getPubliclyAccessible() {
            return publiclyAccessible;
        }

        public void setPubliclyAccessible(Boolean publiclyAccessible) {
            this.publiclyAccessible = publiclyAccessible;
        }

        public Integer getAllocatedStorageInGB() {
            return allocatedStorageInGB;
        }

        public void setAllocatedStorageInGB(Integer allocatedStorageInGB) {
            this.allocatedStorageInGB = allocatedStorageInGB;
        }

        public Integer getAllocatedMemoryInMB() {
            return allocatedMemoryInMB;
        }

        public void setAllocatedMemoryInMB(Integer allocatedMemoryInMB) {
            this.allocatedMemoryInMB = allocatedMemoryInMB;
        }

        public String getAzone() {
            return azone;
        }

        public void setAzone(String azone) {
            this.azone = azone;
        }

        public String getCharacterSetName() {
            return characterSetName;
        }

        public void setCharacterSetName(String characterSetName) {
            this.characterSetName = characterSetName;
        }

        public String getVpcId() {
            return vpcId;
        }

        public void setVpcId(String vpcId) {
            this.vpcId = vpcId;
        }

        public Map<String, String> getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(Map<String, String> subnetId) {
            this.subnetId = subnetId;
        }

        public Boolean getIsEnhanced() {
            return isEnhanced;
        }

        public void setIsEnhanced(Boolean enhanced) {
            isEnhanced = enhanced;
        }


        public InstanceParameters withDiskType(final String diskType) {
            this.diskType = diskType;
            return this;
        }

        public InstanceParameters withCdskType(final String cdsType) {
            this.cdsType = cdsType;
            return this;
        }

        public InstanceParameters withSourceInstanceId(final String sourceInstanceId) {
            this.sourceInstanceId = sourceInstanceId;
            return this;
        }

        public InstanceParameters withBackupPolicy(final SnapshotPolicy backupPolicy) {
            this.backupPolicy = backupPolicy;
            return this;
        }

        public InstanceParameters withEngine(final String engine) {
            this.engine = engine;
            return this;
        }

        public InstanceParameters withEngineVersion(final String engineVersion) {
            this.engineVersion = engineVersion;
            return this;
        }

        public InstanceParameters withInstanceExpireTime(final Date instanceExpireTime) {
            this.instanceExpireTime = instanceExpireTime;
            return this;
        }

        public InstanceParameters withInstanceClass(final String instanceClass) {
            this.instanceClass = instanceClass;
            return this;
        }

        public InstanceParameters withInstanceName(final String instanceName) {
            this.instanceName = instanceName;
            return this;
        }

        public InstanceParameters withPubliclyAccessible(final boolean publiclyAccessible) {
            this.publiclyAccessible = publiclyAccessible;
            return this;
        }

        public InstanceParameters withAllocatedStorageInGB(final Integer allocatedStorageInGB) {
            this.allocatedStorageInGB = allocatedStorageInGB;
            return this;
        }

        public InstanceParameters withAllocatedMemoryInMB(final Integer allocatedMemoryInMB) {
            this.allocatedMemoryInMB = allocatedMemoryInMB;
            return this;
        }

        public InstanceParameters withCharacterSetName(final String characterSetName) {
            this.characterSetName = characterSetName;
            return this;
        }

        public InstanceParameters withAzone(final String azone) {
            this.azone = azone;
            return this;
        }

        public InstanceParameters whitIsEnhanced(final Boolean isEnhanced) {
            this.isEnhanced = isEnhanced;
            return this;
        }

        public InstanceParameters withInstanceType(final String instanceType) {
            this.instanceType = instanceType;
            return this;
        }

        public InstanceCreateRequest.InitialDataReference getInitialDataReference() {
            return initialDataReference;
        }

        public void setInitialDataReference(InstanceCreateRequest.InitialDataReference initialDataReference) {
            this.initialDataReference = initialDataReference;
        }

        public String getInstanceType() {
            return instanceType;
        }

        public void setInstanceType(String instanceType) {
            this.instanceType = instanceType;
        }

        public Boolean getIsSingle() {
            return getSingle();
        }

        public void setIsSingle(Boolean single) {
            setSingle(single);
        }

        public Boolean getSingle() {
            return isSingle;
        }

        public void setSingle(Boolean single) {
            isSingle = single;
        }

        public String getTags() {
            return tags;
        }

        public void setTags(String tags) {
            this.tags = tags;
        }

        public String getDiskIoType() {
            return diskIoType;
        }

        public void setDiskIoType(String diskIoType) {
            this.diskIoType = diskIoType;
        }

        public String getDiskType() {
            return diskType;
        }

        public void setDiskType(String diskType) {
            this.diskType = diskType;
        }

        public String getCdsType() {
            return cdsType;
        }

        public void setCdsType(String cdsType) {
            this.cdsType = cdsType;
        }

        public String getOvip() {
            return ovip;
        }

        public void setOvip(String ovip) {
            this.ovip = ovip;
        }
        public InstanceParameters withOvip(final String ovip) {
            this.ovip = ovip;
            return this;
        }

        public String getBgwGroupId() {
            return bgwGroupId;
        }

        public InstanceParameters withBgwGroupId(String bgwGroupId) {
            this.bgwGroupId = bgwGroupId;
            return this;
        }

        public boolean isBgwGroupExclusive() {
            return bgwGroupExclusive;
        }

        public InstanceParameters withBgwGroupExclusive(boolean bgwGroupExclusive) {
            this.bgwGroupExclusive = bgwGroupExclusive;
            return this;
        }

        public Integer getEntryPort() {
            return entryPort;
        }

        public void setEntryPort(Integer entryPort) {
            this.entryPort = entryPort;
        }

        public Integer getLowerCaseTableNames() {
            return lowerCaseTableNames;
        }

        public void setLowerCaseTableNames(Integer lowerCaseTableNames) {
            this.lowerCaseTableNames = lowerCaseTableNames;
        }

        public Integer getForceHotUpgrade() {
            return forceHotUpgrade;
        }

        public void setForceHotUpgrade(Integer forceHotUpgrade) {
            this.forceHotUpgrade = forceHotUpgrade;
        }

        public String getMasterAzone() {
            return masterAzone;
        }

        public void setMasterAzone(String masterAzone) {
            this.masterAzone = masterAzone;
        }

        public String getBackupAzone() {
            return backupAzone;
        }

        public void setBackupAzone(String backupAzone) {
            this.backupAzone = backupAzone;
        }

        public String getParameterTemplateId() {
            return parameterTemplateId;
        }

        public void setParameterTemplateId(String parameterTemplateId) {
            this.parameterTemplateId = parameterTemplateId;
        }

        public String getEffectiveTime() {
            return effectiveTime;
        }

        public void setEffectiveTime(String effectiveTime) {
            this.effectiveTime = effectiveTime;
        }

        public List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> getData() {
            return data;
        }

        public void setData(List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> data) {
            this.data = data;
        }

    }
    private String subject = "instanceClass";
    private String orderId;
    private InstanceParameters instanceParameters = new InstanceParameters();
    private String effectiveTime;
    private String env;
    private String batchInstances = "";

    @Override
    public String toString() {
        return "InstanceUpdateFlavorRequest{" +
                "subject='" + subject + '\'' +
                ", orderId='" + orderId + '\'' +
                ", instanceParameters=" + instanceParameters +
                ", effectiveTime='" + effectiveTime + '\'' +
                ", env='" + env + '\'' +
                ", batchInstances='" + batchInstances + '\'' +
                '}';
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public InstanceParameters getInstanceParameters() {
        return instanceParameters;
    }

    public void setInstanceParameters(InstanceParameters instanceParameters) {
        this.instanceParameters = instanceParameters;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getBatchInstances() {
        return batchInstances;
    }

    public void setBatchInstances(String batchInstances) {
        this.batchInstances = batchInstances;
    }
}
