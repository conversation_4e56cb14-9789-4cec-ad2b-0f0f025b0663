package com.baidu.bce.internalsdk.rds.model.security;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/30.
 */
public class SecurityIp {
    private Collection<String> ip = new ArrayList<>();
    private String ETag;
    private SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

    public SecurityIp() {
        this.ETag = formatter.format(new Date()).toString();
    }

    @JsonProperty("ETag")
    public String getETag() {
        return this.ETag;
    }

    @JsonProperty("ETag")
    public void setETag(String ETag) {
        this.ETag = ETag;
    }

    public Collection<String> getIp() {
        return ip;
    }

    public void setIp(Collection<String> ip) {
        this.ip = ip;
    }

    public SecurityIp withIps(final Collection<String> ips) {
        this.setIp(ips);
        return this;
    }

    public SecurityIp withETag(final String ETag) {
        this.ETag = ETag;
        return this;
    }

}
