package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.rds.model.bcchan.GetIsInternalUserResponse;
import endpoint.EndpointManager;

/**
 * Bcc Han API Client.
 *
 * Wiki: https://wiki.baidu.com/pages/viewpage.action?pageId=1359119021
 * Wiki: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/Pk9qRIMo58/-AcazFLBfmo6f2
 *
 * @since 2022/05/25
 * <AUTHOR>
 */
public class BccHanClient extends BceClient {

    private static final String SERVICE_NAME = "BccHan";

    public BccHanClient(String accessKey, String secretKey, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey, securityToken);
    }

    public BceInternalRequest createBccHanRequest() {
        return super.createAuthorizedRequest();
    }

    /**
     * 查询用户是否为内部用户
     *
     * @param userId 用户账号 ID（account id)
     * @return GetIsInternalUserResponse
     */
    public GetIsInternalUserResponse getIsInternalUser(String userId) {
        return createBccHanRequest()
                .path("/v1/bcc/isInternalUser/" + userId)
                .get(GetIsInternalUserResponse.class);
    }
}
