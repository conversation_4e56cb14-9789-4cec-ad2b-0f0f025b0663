package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceClientConfig;
import com.baidu.bce.internalsdk.core.BceInternalClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogDetailRequest;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogErrorDetails;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogSlowDetails;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.PostSqlflowRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSlowSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SqlIdResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTemplateResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTrend;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSessionKillTypesResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionSummaryRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionDetailResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionDetailRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionKillAuthorityRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.OpenAPIGetSlowSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionKillHistoryResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionStatisticsResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.ErrorLogDetailRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.ErrorLogDetailResponse;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;

/**
 * SmartDBA 服务 Client
 *
 * <AUTHOR>
 * @since 3/11/22
 */
public class SmartDbaClient extends BceClient {

    private static final String SERVICE_NAME = "SmartDba";

    public SmartDbaClient(String accessKey, String secretKey, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey, securityToken);
    }

    public BceInternalRequest createSmartDbaRequest() {
        BceClientConfig config = new BceClientConfig();
        config.withReadTimeout(30000)
                .withMaxConnTotal(400)
                .withMaxConnPerRoute(400);
        BceInternalRequest bceInternalRequest = BceInternalClient.request(endpoint, config)
                .authorization(accessKey, secretKey);
        if (!StringUtils.isEmpty(securityToken)) {
            bceInternalRequest.securityToken(securityToken);
        }
        return bceInternalRequest;
    }

    public GetSqlflowResponse getSqlflow(String instanceId) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/sql-flow/rds/smartdba");
        if (instanceId != null) {
            bceInternalRequest.queryParam("instanceId", instanceId);
        }
        return bceInternalRequest.get(GetSqlflowResponse.class);
    }

    public GetSqlflowResponse postSqlflow(PostSqlflowRequest postSqlflowRequest) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/sql-flow/rds/smartdba");
        return bceInternalRequest.post(Entity.json(postSqlflowRequest), GetSqlflowResponse.class);
    }

    public GetSqlflowResponse deleteSqlflow(String instanceId) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/sql-flow/rds/smartdba");
        if (instanceId != null) {
            bceInternalRequest.queryParam("instanceId", instanceId);
        }
        return bceInternalRequest.delete(GetSqlflowResponse.class);
    }

    public GetSqlResponse getSql(GetSqlRequest getSqlRequest) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/full-sql");
        return bceInternalRequest.post(Entity.json(getSqlRequest), GetSqlResponse.class);
    }

    public GetSlowSqlflowResponse getSlowSqlflow(String instanceId) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql-flow/rds/" + instanceId);
        return bceInternalRequest.get(GetSlowSqlflowResponse.class);
    }

    public GetSlowSqlflowResponse putSlowSqlflow(String instanceId) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql-flow/rds/" + instanceId);
        return bceInternalRequest.put(GetSlowSqlflowResponse.class);
    }

    public GetSlowSqlflowResponse deleteSlowSqlflow(String instanceId) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql-flow/rds/" + instanceId);
        return bceInternalRequest.delete(GetSlowSqlflowResponse.class);
    }

    public SqlIdResponse getSlowSqlBySqlId(String instanceId, String sqlId, String engine) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/rds/" + instanceId + "/" + sqlId);
        if (engine != null) {
            bceInternalRequest.queryParam("engine", engine);
        }
        return bceInternalRequest.get(SqlIdResponse.class);
    }

    public SlowsqlListResponse getSlowSqlList(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/detail/rds/" + instanceId);
        if (request.getPage() != null) {
            bceInternalRequest.queryParam("page", request.getPage() );
        }

        if (request.getPageSize() != null) {
            bceInternalRequest.queryParam("pageSize", request.getPageSize());
        }

        if (request.getOffset() != null) {
            bceInternalRequest.queryParam("offset", request.getOffset());
        }

        if (request.getSort() != null) {
            bceInternalRequest.queryParam("sort", request.getSort());
        }

        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getDigest() != null) {
            bceInternalRequest.queryParam("digest", request.getDigest());
        }

        if (request.getStart() != null) {
            bceInternalRequest.queryParam("start", request.getStart());
        }

        if (request.getEnd() != null) {
            bceInternalRequest.queryParam("end", request.getEnd());
        }

        return bceInternalRequest.get(SlowsqlListResponse.class);
    }

    public String  getSlowSqlExplain(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/explain/rds/" + instanceId + "/" + request.getSqlId());

        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getSqlId() != null) {
            bceInternalRequest.queryParam("sqlId", request.getSqlId());
        }

        return bceInternalRequest.get(String.class);
    }

    public SlowsqlTemplateResponse getSlowSqlTemplate(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/stats/digest/rds/" + instanceId);
        if (request.getPage() != null) {
            bceInternalRequest.queryParam("page", request.getPage() );
        }

        if (request.getPageSize() != null) {
            bceInternalRequest.queryParam("pageSize", request.getPageSize());
        }

        if (request.getOffset() != null) {
            bceInternalRequest.queryParam("offset", request.getOffset());
        }

        if (request.getSort() != null) {
            bceInternalRequest.queryParam("sort", request.getSort());
        }

        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getDigest() != null) {
            bceInternalRequest.queryParam("digest", request.getDigest());
        }

        if (request.getStart() != null) {
            bceInternalRequest.queryParam("start", request.getStart());
        }

        if (request.getEnd() != null) {
            bceInternalRequest.queryParam("end", request.getEnd());
        }

        return bceInternalRequest.get(SlowsqlTemplateResponse.class);
    }

    public String getSlowSqlStatsDuration(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/stats/duration/rds/" + instanceId);

        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getDigest() != null) {
            bceInternalRequest.queryParam("digest", request.getDigest());
        }

        if (request.getStart() != null) {
            bceInternalRequest.queryParam("start", request.getStart());
        }

        if (request.getEnd() != null) {
            bceInternalRequest.queryParam("end", request.getEnd());
        }

        return bceInternalRequest.get(String.class);
    }

    public String getSlowSqlStatsSource(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/stats/source/rds/" + instanceId);

        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getDigest() != null) {
            bceInternalRequest.queryParam("digest", request.getDigest());
        }

        if (request.getStart() != null) {
            bceInternalRequest.queryParam("start", request.getStart());
        }

        if (request.getEnd() != null) {
            bceInternalRequest.queryParam("end", request.getEnd());
        }

        return bceInternalRequest.get(String.class);
    }

    public String getSlowSqlTable(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/table/rds/" + instanceId + "/" + request.getSqlId());
        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        return bceInternalRequest.get(String.class);
    }

    public String getSlowSqlTableColumn(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/table/column/rds/" + instanceId + "/" + request.getSqlId());
        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getTable() != null) {
            bceInternalRequest.queryParam("table", request.getTable());
        }
        return bceInternalRequest.get(String.class);
    }

    public String getSlowSqlTableIndex(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/table/index/rds/" + instanceId + "/" + request.getSqlId());
        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getTable() != null) {
            bceInternalRequest.queryParam("table", request.getTable());
        }

        if (request.getIndex() != null) {
            bceInternalRequest.queryParam("index", request.getIndex());
        }
        return bceInternalRequest.get(String.class);
    }

    public SlowsqlTrend getSlowSqlTrend(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/trend/rds/" + instanceId);
        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getInterval() != null) {
            bceInternalRequest.queryParam("interval", request.getInterval());
        }

        if (request.getStart() != null) {
            bceInternalRequest.queryParam("start", request.getStart());
        }

        if (request.getEnd() != null) {
            bceInternalRequest.queryParam("end", request.getEnd());
        }
        return bceInternalRequest.get(SlowsqlTrend.class);
    }

    public SlowsqlTrend getSlowSqlTuning(String instanceId, SlowsqlListRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/slow-sql/tuning/rds/" + instanceId + "/" + request.getSqlId());
        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }

        if (request.getSchema() != null) {
            bceInternalRequest.queryParam("schema", request.getSchema());
        }

        if (request.getSqlId() != null) {
            bceInternalRequest.queryParam("sqlId", request.getSqlId());
        }

        return bceInternalRequest.get(SlowsqlTrend.class);
    }

    public GetSessionKillTypesResponse getSessionKillTypesRequest(String instanceId, String type) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/session/no-auth/kill/types/rds/" + instanceId);
        if (type != null) {
            bceInternalRequest.queryParam("type", type);
        }

        return bceInternalRequest.get(GetSessionKillTypesResponse.class);
    }

    public SessionSummaryRequest getSessionSummaryRequest(String instanceId) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/session/no-auth/summary/rds/" + instanceId);
        return bceInternalRequest.get(SessionSummaryRequest.class);
    }

    public SessionDetailResponse getSessionDetailRequest(String instanceId, SessionDetailRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/session/no-auth/detail/rds/" + instanceId);
        if (request.getExecuteTime() != null) {
            bceInternalRequest.queryParam("executeTime", request.getExecuteTime());
        }

        if (request.getOperator() != null) {
            bceInternalRequest.queryParam("operator", request.getOperator());
        }

        if (request.getPage() != null) {
            bceInternalRequest.queryParam("page", request.getPage());
        }

        if (request.getPageSize() != null) {
            bceInternalRequest.queryParam("pageSize", request.getPageSize());
        }
        if (request.getOffset() != null) {
            bceInternalRequest.queryParam("offset", request.getOffset());
        }
        if (request.getSort() != null) {
            bceInternalRequest.queryParam("sort", request.getSort());
        }
        if (request.getActive() != null) {
            bceInternalRequest.queryParam("isActive", request.getActive());
        }
        if (request.getSessionId() != null) {
            bceInternalRequest.queryParam("sessionId", request.getSessionId());
        }
        if (request.getUser() != null) {
            bceInternalRequest.queryParam("user", request.getUser());
        }
        if (request.getHost() != null) {
            bceInternalRequest.queryParam("host", request.getHost());
        }
        if (request.getDb() != null) {
            bceInternalRequest.queryParam("db", request.getDb());
        }
        if (request.getCommand() != null) {
            bceInternalRequest.queryParam("command", request.getCommand());
        }
        if (request.getState() != null) {
            bceInternalRequest.queryParam("state", request.getState());
        }
        if (request.getSqlStmt() != null) {
            bceInternalRequest.queryParam("sqlStmt", request.getSqlStmt());
        }
        return bceInternalRequest.get(SessionDetailResponse.class);
    }

    public OpenAPIGetSlowSqlflowResponse postSessionKillAuthorityRequest(String instanceId,
                                                                         SessionKillAuthorityRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/session/no-auth/kill/authority/rds/" + instanceId);
        if (request.getDbHost() != null) {
            bceInternalRequest.queryParam("dbHost", request.getDbHost());
        }

        if (request.getDbPort() != null) {
            bceInternalRequest.queryParam("dbPort", request.getDbPort());
        }

        if (request.getDbUser() != null) {
            bceInternalRequest.queryParam("dbUser", request.getDbUser());
        }

        if (request.getDbPassword() != null) {
            bceInternalRequest.queryParam("dbPassword", request.getDbPassword());
        }

        return bceInternalRequest.post(Entity.json(request), OpenAPIGetSlowSqlflowResponse.class);
    }

    public SessionKillHistoryResponse getSessionKillHistoryRequest(String instanceId, SessionDetailRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/session/no-auth/kill/rds/" + instanceId);
        if (request.getPage() != null) {
            bceInternalRequest.queryParam("page", request.getPage());
        }
        if (request.getPageSize() != null) {
            bceInternalRequest.queryParam("pageSize", request.getPageSize());
        }
        if (request.getOffset() != null) {
            bceInternalRequest.queryParam("offset", request.getOffset());
        }
        if (request.getSort() != null) {
            bceInternalRequest.queryParam("sort", request.getSort());
        }
        if (request.getStart() != null) {
            bceInternalRequest.queryParam("start", request.getStart());
        }
        if (request.getEnd() != null) {
            bceInternalRequest.queryParam("end", request.getEnd());
        }
        return bceInternalRequest.get(SessionKillHistoryResponse.class);
    }

    public OpenAPIGetSlowSqlflowResponse postSessionKillRequest(String instanceId,
                                                                SessionKillAuthorityRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/session/no-auth/kill/rds/" + instanceId);
        if (request.getDbHost() != null) {
            bceInternalRequest.queryParam("dbHost", request.getDbHost());
        }

        if (request.getDbPort() != null) {
            bceInternalRequest.queryParam("dbPort", request.getDbPort());
        }
        return bceInternalRequest.post(Entity.json(request), OpenAPIGetSlowSqlflowResponse.class);
    }

    public SessionStatisticsResponse getSessionStatisticsRequest(String instanceId) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/session/no-auth/statistics/rds/" + instanceId);
        return bceInternalRequest.get(SessionStatisticsResponse.class);
    }

    public GetSlowSqlflowResponse getErrorLogFlowRequest(String instanceId) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/error-log-flow/no-auth/rds/" + instanceId);
        return bceInternalRequest.get(GetSlowSqlflowResponse.class);
    }
    public GetSlowSqlflowResponse putErrorLogFlowRequest(String instanceId) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/error-log-flow/no-auth/rds/" + instanceId);
        return bceInternalRequest.put(GetSlowSqlflowResponse.class);
    }
    public GetSlowSqlflowResponse deleteErrorLogFlowRequest(String instanceId) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/error-log-flow/no-auth/rds/" + instanceId);
        return bceInternalRequest.delete(GetSlowSqlflowResponse.class);
    }

    public ErrorLogDetailResponse getErrorLogDetailRequest(String instanceId, ErrorLogDetailRequest request) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest()
                .path("/v1/error-log/no-auth/detail/rds/" + instanceId);
        if (request.getNodeId() != null) {
            bceInternalRequest.queryParam("nodeId", request.getNodeId());
        }
        if (request.getEngine() != null) {
            bceInternalRequest.queryParam("engine", request.getEngine());
        }
        if (request.getLabel() != null) {
            bceInternalRequest.queryParam("label", request.getLabel());
        }
        if (request.getPage() != null) {
            bceInternalRequest.queryParam("page", request.getPage());
        }
        if (request.getPageSize() != null) {
            bceInternalRequest.queryParam("pageSize", request.getPageSize());
        }
        if (request.getOffset() != null) {
            bceInternalRequest.queryParam("offset", request.getOffset());
        }
        if (request.getSort() != null) {
            bceInternalRequest.queryParam("sort", request.getSort());
        }
        if (request.getStart() != null) {
            bceInternalRequest.queryParam("start", request.getStart());
        }
        if (request.getEnd() != null) {
            bceInternalRequest.queryParam("end", request.getEnd());
        }
        return bceInternalRequest.get(ErrorLogDetailResponse.class);
    }

    public LogErrorDetails getErrorLogDetails(LogDetailRequest logDetailRequest) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest();
        return bceInternalRequest
                .path("/v1/error-log/rds/logs/logErrorDetail")
                .post(Entity.json(logDetailRequest), LogErrorDetails.class);

    }

    public LogSlowDetails getSlowLogDetails(LogDetailRequest logDetailRequest) {
        BceInternalRequest bceInternalRequest = createSmartDbaRequest();
        return bceInternalRequest
                .path("/v1/slow-sql/rds/logs/logSlowDetail")
                .post(Entity.json(logDetailRequest), LogSlowDetails.class);
    }
}
