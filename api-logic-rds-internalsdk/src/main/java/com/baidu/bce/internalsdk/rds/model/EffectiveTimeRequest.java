package com.baidu.bce.internalsdk.rds.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EffectiveTimeRequest {

    private String effectiveTime;

    private boolean switchover = false;


    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public boolean getSwitchover() {
        return switchover;
    }

    public void setSwitchover(boolean switchover) {
        this.switchover = switchover;
    }
}
