package com.baidu.bce.internalsdk.rds.model.instance;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
public class InstanceUpdateReplicationTypeRequest {
    private String replicationType;

    // OpenAPI 专用参数
    private String syncMode;

    @Override
    public String toString() {
        return "InstanceUpdateReplicationTypeRequest{"
                + "replicationType=" + replicationType
                + '}';
    }

    public String getReplicationType() {
        return replicationType;
    }

    public void setReplicationType(String replicationType) {
        this.replicationType = replicationType;
    }

    public String getSyncMode() {
        return syncMode;
    }

    public void setSyncMode(String syncMode) {
        this.syncMode = syncMode;
    }
}
