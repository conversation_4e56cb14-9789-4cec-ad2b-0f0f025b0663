package com.baidu.bce.internalsdk.rds.model.group;


import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DashboardGroupAccountDetailRequest {
    @NotNull
    private String groupId;

    @NotNull
    private String accountName;

    @Override
    public String toString() {
        return "DashboardAccountDetailRequest{" +
                "groupId='" + getGroupId() + '\'' +
                ", accountName='" + getAccountName() + '\'' +
                '}';
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
}
