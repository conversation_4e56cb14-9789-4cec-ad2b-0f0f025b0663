package com.baidu.bce.internalsdk.rds.model.slowquery;

import java.util.List;

/**
 * Created by luping03 on 17/12/25.
 */
public class SlowqueryDetailResponse extends SlowqueryBaseResponse {

    private SlowqueryBasePageResponseModel<List<SlowqueryDetailPageResult>> page;

    public SlowqueryBasePageResponseModel<List<SlowqueryDetailPageResult>> getPage() {
        return page;
    }

    public void setPage(SlowqueryBasePageResponseModel<List<SlowqueryDetailPageResult>> page) {
        this.page = page;
    }

    private static class SlowqueryDetailPageResult {
        private String accessIP;

        private String accessUser;

        private String defaultdb;

        private String executionTime;

        private Double queryTime;

        private Double lockLatencyTime;

        private int returnRows;

        private int scanRows;

        private String originSql;

        private String sqlMd5;

        private String templateSqlMd5;

        public String getAccessIP() {
            return accessIP;
        }

        public void setAccessIP(String accessIP) {
            this.accessIP = accessIP;
        }

        public String getExecutionTime() {
            return executionTime;
        }

        public void setExecutionTime(String executionTime) {
            this.executionTime = executionTime;
        }

        public int getReturnRows() {
            return returnRows;
        }

        public void setReturnRows(int returnRows) {
            this.returnRows = returnRows;
        }

        public int getScanRows() {
            return scanRows;
        }

        public void setScanRows(int scanRows) {
            this.scanRows = scanRows;
        }

        public String getOriginSql() {
            return originSql;
        }

        public void setOriginSql(String originSql) {
            this.originSql = originSql;
        }

        public String getSqlMd5() {
            return sqlMd5;
        }

        public void setSqlMd5(String sqlMd5) {
            this.sqlMd5 = sqlMd5;
        }

        public String getTemplateSqlMd5() {
            return templateSqlMd5;
        }

        public void setTemplateSqlMd5(String templateSqlMd5) {
            this.templateSqlMd5 = templateSqlMd5;
        }

        public Double getQueryTime() {
            return queryTime;
        }

        public void setQueryTime(Double queryTime) {
            this.queryTime = queryTime;
        }

        public Double getLockLatencyTime() {
            return lockLatencyTime;
        }

        public void setLockLatencyTime(Double lockLatencyTime) {
            this.lockLatencyTime = lockLatencyTime;
        }

        public String getDefaultdb() {
            return defaultdb;
        }

        public void setDefaultdb(String defaultdb) {
            this.defaultdb = defaultdb;
        }

        public String getAccessUser() {
            return accessUser;
        }

        public void setAccessUser(String accessUser) {
            this.accessUser = accessUser;
        }
    }


}
