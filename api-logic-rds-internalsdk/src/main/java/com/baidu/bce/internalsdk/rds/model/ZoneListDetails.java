package com.baidu.bce.internalsdk.rds.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class ZoneListDetails {

    List<ZoneMapDetail> zoneMapDetailList = new LinkedList();

    public List<ZoneMapDetail> getZoneMapDetailList() {
        return this.zoneMapDetailList;
    }

    public void setZoneMapDetailList(List<ZoneMapDetail> zoneMapDetailList) {
        this.zoneMapDetailList = zoneMapDetailList;
    }

    @JsonIgnoreProperties(
            ignoreUnknown = true
    )
    public static class ZoneMapDetail {
        private long id;
        private String zoneId;
        private String accountId;
        private String logicalZone;
        private String physicalZone;
        private String subnetUuid;
        private String type;
        private Map<String, String> unavailableFunc;

        public Map<String, String> getUnavailableFunc() {
            return unavailableFunc;
        }

        public void setUnavailableFunc(Map<String, String> unavailableFunc) {
            this.unavailableFunc = unavailableFunc;
        }

        public String getType() {
            return this.type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public long getId() {
            return this.id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getZoneId() {
            return this.zoneId;
        }

        public void setZoneId(String zoneId) {
            this.zoneId = zoneId;
        }

        public String getAccountId() {
            return this.accountId;
        }

        public void setAccountId(String accountId) {
            this.accountId = accountId;
        }

        public String getLogicalZone() {
            return this.logicalZone;
        }

        public void setLogicalZone(String logicalZone) {
            this.logicalZone = logicalZone;
        }

        public String getPhysicalZone() {
            return this.physicalZone;
        }

        public void setPhysicalZone(String physicalZone) {
            this.physicalZone = physicalZone;
        }

        public String getSubnetUuid() {
            return this.subnetUuid;
        }

        public void setSubnetUuid(String subnetUuid) {
            this.subnetUuid = subnetUuid;
        }

    }


}
