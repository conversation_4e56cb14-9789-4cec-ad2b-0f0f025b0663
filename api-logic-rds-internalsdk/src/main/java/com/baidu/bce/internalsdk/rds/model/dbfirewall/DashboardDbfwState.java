package com.baidu.bce.internalsdk.rds.model.dbfirewall;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/9.
 */
public class DashboardDbfwState {

    private int id;             // 自增主键

    private String appId;       // 代理实例ID

    private Boolean dbfwStatus; // DBFW是否开启 true 开启 false 关闭

    private int dbfwRule;       // DBFW状态 0关闭 1告警 2阻断

    private String updateTime;  // DBFW状态更新时间 yyyy-MM-dd hh:mm:ss

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public int getDbfwRule() {
        return dbfwRule;
    }

    public void setDbfwRule(int dbfwRule) {
        this.dbfwRule = dbfwRule;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDbfwStatus() {
        return dbfwStatus;
    }

    public void setDbfwStatus(Boolean dbfwStatus) {
        this.dbfwStatus = dbfwStatus;
    }

    @Override
    public String toString() {
        return "DashboardDbfwState{"
                + "id=" + id
                + ", appId='" + appId + '\''
                + ", dbfwStatus=" + dbfwStatus
                + ", dbfwRule=" + dbfwRule
                + ", updateTime='" + updateTime + '\''
                + '}';
    }
}
