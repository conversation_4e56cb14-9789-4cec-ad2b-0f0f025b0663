package com.baidu.bce.internalsdk.rds.model.slowquery;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class SlowquerySqlOptimizerResponse extends SlowqueryBaseResponse {

    private SqlOptimizerResult result;

    public SqlOptimizerResult getResult() {
        return result;
    }

    public void setResult(SqlOptimizerResult result) {
        this.result = result;
    }

    private static class SqlOptimizerResult {
        private String optimizerId;

        private String optimizerTime;

        private Map<String, List<SuggestionContent>> suggestions;

        public String getOptimizerId() {
            return optimizerId;
        }

        public void setOptimizerId(String optimizerId) {
            this.optimizerId = optimizerId;
        }

        public String getOptimizerTime() {
            return optimizerTime;
        }

        public void setOptimizerTime(String optimizerTime) {
            this.optimizerTime = optimizerTime;
        }

        public Map<String, List<SuggestionContent>> getSuggestions() {
            return suggestions;
        }

        public void setSuggestions(Map<String, List<SuggestionContent>> suggestions) {
            this.suggestions = suggestions;
        }
    }

    private static class SuggestionContent {
        private String id;

        private String suggestionDesc;

        private String suggestionSql;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getSuggestionDesc() {
            return suggestionDesc;
        }

        public void setSuggestionDesc(String suggestionDesc) {
            this.suggestionDesc = suggestionDesc;
        }

        public String getSuggestionSql() {
            return suggestionSql;
        }

        public void setSuggestionSql(String suggestionSql) {
            this.suggestionSql = suggestionSql;
        }
    }

}
