package com.baidu.bce.internalsdk.rds.model.rogroup;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

public class DashboardRoGroupUpdateRequest {
    @IdPermission
    private String sourceAppId;
    private String roGroupId;
    private String roGroupName;
    private InstanceDict endpoint;
    private boolean publiclyAccessible;
    private boolean enableDelayOff;
    private boolean balanceReload;
    private Integer leastAppAmount;
    private Integer delayThreshold;

    public String getSourceAppId() {
        return sourceAppId;
    }

    public void setSourceAppId(String sourceAppId) {
        this.sourceAppId = sourceAppId;
    }

    public String getRoGroupId() {
        return roGroupId;
    }

    public void setRoGroupId(String roGroupId) {
        this.roGroupId = roGroupId;
    }

    public String getRoGroupName() {
        return roGroupName;
    }

    public void setRoGroupName(String roGroupName) {
        this.roGroupName = roGroupName;
    }

    public InstanceDict getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(InstanceDict endpoint) {
        this.endpoint = endpoint;
    }

    public boolean isPubliclyAccessible() {
        return publiclyAccessible;
    }

    public void setPubliclyAccessible(boolean publiclyAccessible) {
        this.publiclyAccessible = publiclyAccessible;
    }

    public boolean isEnableDelayOff() {
        return enableDelayOff;
    }

    public void setEnableDelayOff(boolean enableDelayOff) {
        this.enableDelayOff = enableDelayOff;
    }

    public boolean isBalanceReload() {
        return balanceReload;
    }

    public void setBalanceReload(boolean balanceReload) {
        this.balanceReload = balanceReload;
    }

    public Integer getLeastAppAmount() {
        return leastAppAmount;
    }

    public void setLeastAppAmount(Integer leastAppAmount) {
        this.leastAppAmount = leastAppAmount;
    }

    public Integer getDelayThreshold() {
        return delayThreshold;
    }

    public void setDelayThreshold(Integer delayThreshold) {
        this.delayThreshold = delayThreshold;
    }

    public static class Dict{
        private String address;
        private Integer port;

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }
    }
}
