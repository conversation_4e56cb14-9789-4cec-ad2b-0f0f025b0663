package com.baidu.bce.internalsdk.rds.model.dbfirewall;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/6.
 */
public class SqlWhiteDetail {

    private int sqlId;          // 自增主键

    private String appId;       // 代理实例id

    private String dbName;

    private String sqlMd5;      // sql MD5

    private String sqlFingerprint; // sql 签名

    private String createTime;

    public int getSqlId() {
        return sqlId;
    }

    public void setSqlId(int sqlId) {
        this.sqlId = sqlId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getSqlMd5() {
        return sqlMd5;
    }

    public void setSqlMd5(String sqlMd5) {
        this.sqlMd5 = sqlMd5;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getSqlFingerprint() {
        return sqlFingerprint;
    }

    public void setSqlFingerprint(String sqlFingerprint) {
        this.sqlFingerprint = sqlFingerprint;
    }

    @Override
    public String toString() {
        return "SqlWhiteDetail{"
                + "sqlId=" + sqlId
                + ", appId='" + appId + '\''
                + ", dbName='" + dbName + '\''
                + ", sqlMd5='" + sqlMd5 + '\''
                + ", sqlFingerprint='" + sqlFingerprint + '\''
                + ", createTime='" + createTime + '\''
                + '}';
    }
}
