package com.baidu.bce.internalsdk.rds.model.strategy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateExchangeStrategyRequest {

    private Integer strategy;
    private Integer exchangeEnable;
    private Integer exchangeInterval;
    private Integer longTransactionThreshold;
    private Integer localDelay;
    private Integer localReplayTimeout;
    private Integer remoteReplayTimeout;

    public Integer getStrategy() {
        return strategy;
    }

    public void setStrategy(Integer strategy) {
        this.strategy = strategy;
    }

    public Integer getExchangeEnable() {
        return exchangeEnable;
    }

    public void setExchangeEnable(Integer exchangeEnable) {
        this.exchangeEnable = exchangeEnable;
    }

    public Integer getExchangeInterval() {
        return exchangeInterval;
    }

    public void setExchangeInterval(Integer exchangeInterval) {
        this.exchangeInterval = exchangeInterval;
    }

    public Integer getLongTransactionThreshold() {
        return longTransactionThreshold;
    }

    public void setLongTransactionThreshold(Integer longTransactionThreshold) {
        this.longTransactionThreshold = longTransactionThreshold;
    }

    public Integer getLocalDelay() {
        return localDelay;
    }

    public void setLocalDelay(Integer localDelay) {
        this.localDelay = localDelay;
    }

    public Integer getLocalReplayTimeout() {
        return localReplayTimeout;
    }

    public void setLocalReplayTimeout(Integer localReplayTimeout) {
        this.localReplayTimeout = localReplayTimeout;
    }

    public Integer getRemoteReplayTimeout() {
        return remoteReplayTimeout;
    }

    public void setRemoteReplayTimeout(Integer remoteReplayTimeout) {
        this.remoteReplayTimeout = remoteReplayTimeout;
    }
}