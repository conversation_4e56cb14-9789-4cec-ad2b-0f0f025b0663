package com.baidu.bce.internalsdk.rds.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class ModifyCpuAutoResizeReq {
    private int scaleUpThreshold;
    private int scaleDownThreshold;
    private int scaleUpObservationTime;
    private int scaleDownObservationTime;
    private String scaleSpec;
    private String effectiveTime;

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public int getScaleUpThreshold() {
        return scaleUpThreshold;
    }

    public void setScaleUpThreshold(int scaleUpThreshold) {
        this.scaleUpThreshold = scaleUpThreshold;
    }

    public int getScaleDownThreshold() {
        return scaleDownThreshold;
    }

    public void setScaleDownThreshold(int scaleDownThreshold) {
        this.scaleDownThreshold = scaleDownThreshold;
    }

    public int getScaleUpObservationTime() {
        return scaleUpObservationTime;
    }

    public void setScaleUpObservationTime(int scaleUpObservationTime) {
        this.scaleUpObservationTime = scaleUpObservationTime;
    }

    public int getScaleDownObservationTime() {
        return scaleDownObservationTime;
    }

    public void setScaleDownObservationTime(int scaleDownObservationTime) {
        this.scaleDownObservationTime = scaleDownObservationTime;
    }

    public String getScaleSpec() {
        return scaleSpec;
    }

    public void setScaleSpec(String scaleSpec) {
        this.scaleSpec = scaleSpec;
    }
}
