package com.baidu.bce.internalsdk.rds.model.security;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Collection;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/11.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SecurityIpPutRequest {
    private String instanceId;
    private Collection<String> securityIps;
    private String securityIpGroup;
    private String eTag;
    private String tag;

    public String getETag() {
        return eTag;
    }

    public void setETag(String eTag) {
        this.eTag = eTag;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Collection<String> getSecurityIps() {
        return securityIps;
    }

    public void setSecurityIps(Collection<String> securityIps) {
        this.securityIps = securityIps;
    }

    public String getSecurityIpGroup() {
        return securityIpGroup;
    }

    public void setSecurityIpGroup(String securityIpGroup) {
        this.securityIpGroup = securityIpGroup;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }
}
