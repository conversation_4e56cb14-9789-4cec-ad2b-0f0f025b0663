package com.baidu.bce.internalsdk.rds.model.migration;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/10/27.
 */
public class Migration {
    private int migrationId;
    private String remoteIp;
    private String remotePort;
    private String remoteUser;
    private Date createTime;
    private Date endTime;
    private String migrationType;
    private String migrationStatus;
    private String checkStatus;
    private String migrationMethod;
    private String description;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Migration{");
        sb.append("migrationId=").append(migrationId);
        sb.append(", remoteIp='").append(remoteIp).append('\'');
        sb.append(", remotePort='").append(remotePort).append('\'');
        sb.append(", remoteUser='").append(remoteUser).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", migrationType='").append(migrationType).append('\'');
        sb.append(", migrationStatus='").append(migrationStatus).append('\'');
        sb.append(", checkStatus='").append(checkStatus).append('\'');
        sb.append(", migrationMethod='").append(migrationMethod).append('\'');
        sb.append(", description='").append(description).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public String getRemotePort() {
        return remotePort;
    }

    public void setRemotePort(String remotePort) {
        this.remotePort = remotePort;
    }

    public String getRemoteUser() {
        return remoteUser;
    }

    public void setRemoteUser(String remoteUser) {
        this.remoteUser = remoteUser;
    }

    public int getMigrationId() {
        return migrationId;
    }

    public void setMigrationId(int migrationId) {
        this.migrationId = migrationId;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getCreateTime() {
        return createTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getMigrationType() {
        return migrationType;
    }

    public void setMigrationType(String migrationType) {
        this.migrationType = migrationType;
    }

    public String getMigrationStatus() {
        return migrationStatus;
    }

    public void setMigrationStatus(String migrationStatus) {
        this.migrationStatus = migrationStatus;
    }

    public String getMigrationMethod() {
        return migrationMethod;
    }

    public void setMigrationMethod(String migrationMethod) {
        this.migrationMethod = migrationMethod;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getEndTime() {
        return endTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }
}
