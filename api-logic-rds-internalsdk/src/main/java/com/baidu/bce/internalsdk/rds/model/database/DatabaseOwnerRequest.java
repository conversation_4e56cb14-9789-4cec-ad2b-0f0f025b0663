package com.baidu.bce.internalsdk.rds.model.database;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

public class DatabaseOwnerRequest {
    @IdPermission
    private String instanceId;
    private String dbName;
    private String owner;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }
}
