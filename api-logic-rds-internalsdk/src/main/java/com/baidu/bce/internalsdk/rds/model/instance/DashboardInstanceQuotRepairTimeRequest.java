package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardInstanceQuotRepairTimeRequest {

    @IdPermission
    private String instanceId;

    private String type;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
