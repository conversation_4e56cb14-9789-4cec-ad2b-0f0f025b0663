package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.wordnik.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/4.
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class InstanceCreateRequest {
    private String orderId;
    private String childUserId;
    private int instanceAmount;
    private List<String> instanceIds; // raft后端支持长短ID操作
    private InstanceParameters instanceParameters = new InstanceParameters();

    @Override
    public String toString() {
        return "InstanceCreateRequest{" +
                "orderId='" + orderId + '\'' +
                ", instanceAmount=" + instanceAmount +
                ", instanceParameters=" + instanceParameters +
                '}';
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getChildUserId() {
        return childUserId;
    }

    public void setChildUserId(String childUserId) {
        this.childUserId = childUserId;
    }

    public int getInstanceAmount() {
        return instanceAmount;
    }

    public void setInstanceAmount(int instanceAmount) {
        this.instanceAmount = instanceAmount;
    }

    public InstanceParameters getInstanceParameters() {
        return instanceParameters;
    }

    public void setInstanceParameters(InstanceParameters instanceParameters) {
        this.instanceParameters = instanceParameters;
    }

    public InstanceCreateRequest withOrderId(final String orderId) {
        this.orderId = orderId;
        return this;
    }

    public InstanceCreateRequest withChildUserId(final String userId) {
        this.childUserId = userId;
        return this;
    }

    public InstanceCreateRequest withInstanceAmount(final int instanceAmount) {
        this.instanceAmount = instanceAmount;
        return this;
    }

    public InstanceCreateRequest withInstanceParameters(final InstanceParameters instanceParameters) {
        this.instanceParameters = instanceParameters;
        return this;
    }

    public InstanceCreateRequest withInstanceIds(final List<String> instanceIds) {
        this.instanceIds = instanceIds;
        return this;
    }

    public List<String> getInstanceIds() {
        return instanceIds;
    }

    public void setInstanceIds(List<String> instanceIds) {
        this.instanceIds = instanceIds;
    }

    public static class InitialDataReference {
        private String instanceId;
        private String referenceType;
        private String datetime;
        private String snapshotId;

        public String getInstanceId() {
            return instanceId;
        }

        public void setInstanceId(String instanceId) {
            this.instanceId = instanceId;
        }

        public String getReferenceType() {
            return referenceType;
        }

        public void setReferenceType(String referenceType) {
            this.referenceType = referenceType;
        }

        public String getDatetime() {
            return datetime;
        }

        public void setDatetime(String datetime) {
            this.datetime = datetime;
        }

        public String getSnapshotId() {
            return snapshotId;
        }

        public void setSnapshotId(String snapshotId) {
            this.snapshotId = snapshotId;
        }
    }


    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InstanceParameters {
        private String sourceInstanceId;

        @Valid
        private SnapshotPolicy backupPolicy; // 后端反馈：创建时不可设置备份参数
        private String engine;
        private String engineVersion;

        private Date instanceExpireTime;
        private String instanceClass;
        private String instanceName;
        private InitialDataReference initialDataReference;
        private List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> data;
        private Boolean publiclyAccessible;
        private Integer allocatedStorageInGB;
        private Integer allocatedMemoryInMB;
        private Integer cpuCount;

        private String characterSetName;

        private String azone;

        private String vpcId;

        private Map<String, String> subnetId;

        private Map<String, String> physicalZone;

        // 三节点增强版MySQL rds新增字段
        private Boolean isSingle;

        // 三节点增强版MySQL rds新增字段
        private Boolean isEnhanced;

        private Integer nodeAmount;

        private String replicationType; // 不可以设置

        private String instanceType;    // 表示产品类型，financial为raft版

        private DccHostInfo dccHostIds = new DccHostInfo();

        private String machineType;

        private String diskIoType;

        private String tags;

        private String diskType;

        private String cdsType;
        private String ovip;

        /* 专属集群ID。如果bgwGroupExclusive为true，要将blb分配到哪个专属集群，如果不传默认分配到用户名下BLB实例最少的专属集群 */
        private String bgwGroupId;

        /* 是否使用专属集群 */
        private boolean bgwGroupExclusive;

        private Integer entryPort;

        private Integer lowerCaseTableNames;

        private Integer forceHotUpgrade;

        private String masterAzone;

        private String backupAzone;

        // 参数模板
        private String parameterTemplateId;

        private String category;

        private String effectiveTime;

        // Map<edgeRegionId, Count> 每个节点购买的数量
        private Map<String, Integer> edgeRegion;

        private Map<String, String> edgeVpcId;

        private Map<String, String> edgeSubnetId;

        private String replicaType;

        private String bcmGroupName;

        private String leaderAppId;

        private String resourceType;

        @ApiModelProperty("只读实例是否继承主实例 IP 白名单，默认不继承")
        private Boolean isInheritMasterAuthip;

        @ApiModelProperty("kms ID")
        private String cdsEncryptKey;

        private String resourcePlatform;

        private String supportStorageEngine;

        private boolean isDataBackupCopy;

        public boolean getIsDataBackupCopy() {
            return isDataBackupCopy;
        }

        public void setIsDataBackupCopy(boolean dataBackupCopy) {
            isDataBackupCopy = dataBackupCopy;
        }

        public String getResourcePlatform() {
            return resourcePlatform;
        }

        public void setResourcePlatform(String resourcePlatform) {
            this.resourcePlatform = resourcePlatform;
        }

        public String getSupportStorageEngine() {
            return supportStorageEngine;
        }

        public void setSupportStorageEngine(String supportStorageEngine) {
            this.supportStorageEngine = supportStorageEngine;
        }

        public String getCdsEncryptKey() {
            return cdsEncryptKey;
        }

        public void setCdsEncryptKey(String cdsEncryptKey) {
            this.cdsEncryptKey = cdsEncryptKey;
        }

        public Boolean getIsInheritMasterAuthip() {
            return isInheritMasterAuthip;
        }

        public void setIsInheritMasterAuthip(Boolean inheritMasterAuthip) {
            isInheritMasterAuthip = inheritMasterAuthip;
        }

        public String getLeaderAppId() {
            return leaderAppId;
        }

        public void setLeaderAppId(String leaderAppId) {
            this.leaderAppId = leaderAppId;
        }



        public String getResourceType() {
            return resourceType;
        }

        public void setResourceType(String resourceType) {
            this.resourceType = resourceType;
        }

        public String getBcmGroupName() {
            return bcmGroupName;
        }

        public void setBcmGroupName(String bcmGroupName) {
            this.bcmGroupName = bcmGroupName;
        }

        public String getReplicaType() {
            return replicaType;
        }

        public void setReplicaType(String replicaType) {
            this.replicaType = replicaType;
        }

        public Map<String, Integer> getEdgeRegion() {
            return edgeRegion;
        }

        public void setEdgeRegion(Map<String, Integer> edgeRegion) {
            this.edgeRegion = edgeRegion;
        }

        public Map<String, String> getEdgeVpcId() {
            return edgeVpcId;
        }

        public void setEdgeVpcId(Map<String, String> edgeVpcId) {
            this.edgeVpcId = edgeVpcId;
        }

        public Map<String, String> getEdgeSubnetId() {
            return edgeSubnetId;
        }

        public void setEdgeSubnetId(Map<String, String> edgeSubnetId) {
            this.edgeSubnetId = edgeSubnetId;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("InstanceParameters{");
            sb.append("sourceInstanceId='").append(sourceInstanceId).append('\'');
            sb.append(", backupPolicy=").append(backupPolicy);
            sb.append(", engine='").append(engine).append('\'');
            sb.append(", engineVersion='").append(engineVersion).append('\'');
            sb.append(", instanceExpireTime=").append(instanceExpireTime);
            sb.append(", instanceClass='").append(instanceClass).append('\'');
            sb.append(", instanceName='").append(instanceName).append('\'');
            sb.append(", publiclyAccessible=").append(publiclyAccessible);
            sb.append(", allocatedStorageInGB=").append(allocatedStorageInGB);
            sb.append(", allocatedMemoryInMB=").append(allocatedMemoryInMB);
            sb.append(", characterSetName='").append(characterSetName).append('\'');
            sb.append(", azone='").append(azone).append('\'');
            sb.append(", isEnhanced='").append(isEnhanced).append('\'');
            sb.append(", instanceType='").append(instanceType).append('\'');
            sb.append(", isSingle='").append(isSingle).append('\'');
            sb.append(", diskType='").append(diskType).append('\'');
            sb.append(", cdsType='").append(cdsType).append('\'');
            sb.append(", initialDataReference='").append(initialDataReference).append('\'');
            sb.append(", data='").append(data).append('\'');
            sb.append('}');
            return sb.toString();
        }

        public DccHostInfo getDccHostIds() {
            return dccHostIds;
        }

        public void setDccHostIds(DccHostInfo dccHostIds) {
            this.dccHostIds = dccHostIds;
        }


        public String getMachineType() {
            return machineType;
        }

        public void setMachineType(String machineType) {
            this.machineType = machineType;
        }

        public Map<String, String> getPhysicalZone() {
            return physicalZone;
        }

        public void setPhysicalZone(Map<String, String> physicalZone) {
            this.physicalZone = physicalZone;
        }

        public Integer getCpuCount() {
            return cpuCount;
        }

        public void setCpuCount(Integer cpuCount) {
            this.cpuCount = cpuCount;
        }

        public InstanceParameters withCpuCount(Integer cpuCount) {
            this.cpuCount = cpuCount;
            return this;
        }

        public String getReplicationType() {
            return replicationType;
        }

        public void setReplicationType(String replicationType) {
            this.replicationType = replicationType;
        }

        public Boolean getEnhanced() {
            return isEnhanced;
        }

        public void setEnhanced(Boolean enhanced) {
            isEnhanced = enhanced;
        }

        public Integer getNodeAmount() {
            return nodeAmount;
        }

        public void setNodeAmount(Integer nodeAmount) {
            this.nodeAmount = nodeAmount;
        }

        public String getSourceInstanceId() {
            return sourceInstanceId;
        }

        public void setSourceInstanceId(String sourceInstanceId) {
            this.sourceInstanceId = sourceInstanceId;
        }

        @ApiModelProperty(
                value = "备份策略",
                required = false)
        public SnapshotPolicy getBackupPolicy() {
            return backupPolicy;
        }

        public void setBackupPolicy(SnapshotPolicy backupPolicy) {
            this.backupPolicy = backupPolicy;
        }

        @ApiModelProperty(
                value = "引擎类型",
                required = true)
        public String getEngine() {
            return engine;
        }

        public void setEngine(String engine) {
            this.engine = engine;
        }

        public String getEngineVersion() {
            return engineVersion;
        }

        public void setEngineVersion(String engineVersion) {
            this.engineVersion = engineVersion;
        }

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        public Date getInstanceExpireTime() {
            return instanceExpireTime;
        }

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        public void setInstanceExpireTime(Date instanceExpireTime) {
            this.instanceExpireTime = instanceExpireTime;
        }

        public String getInstanceClass() {
            return instanceClass;
        }

        public void setInstanceClass(String instanceClass) {
            this.instanceClass = instanceClass;
        }

        public String getInstanceName() {
            return instanceName;
        }

        public void setInstanceName(String instanceName) {
            this.instanceName = instanceName;
        }

        public Boolean getPubliclyAccessible() {
            return publiclyAccessible;
        }

        public void setPubliclyAccessible(Boolean publiclyAccessible) {
            this.publiclyAccessible = publiclyAccessible;
        }

        public Integer getAllocatedStorageInGB() {
            return allocatedStorageInGB;
        }

        public void setAllocatedStorageInGB(Integer allocatedStorageInGB) {
            this.allocatedStorageInGB = allocatedStorageInGB;
        }

        public Integer getAllocatedMemoryInMB() {
            return allocatedMemoryInMB;
        }

        public void setAllocatedMemoryInMB(Integer allocatedMemoryInMB) {
            this.allocatedMemoryInMB = allocatedMemoryInMB;
        }

        public String getAzone() {
            return azone;
        }

        public void setAzone(String azone) {
            this.azone = azone;
        }

        public String getCharacterSetName() {
            return characterSetName;
        }

        public void setCharacterSetName(String characterSetName) {
            this.characterSetName = characterSetName;
        }

        public String getVpcId() {
            return vpcId;
        }

        public void setVpcId(String vpcId) {
            this.vpcId = vpcId;
        }

        public Map<String, String> getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(Map<String, String> subnetId) {
            this.subnetId = subnetId;
        }

        public Boolean getIsEnhanced() {
            return isEnhanced;
        }

        public void setIsEnhanced(Boolean enhanced) {
            isEnhanced = enhanced;
        }


        public InstanceParameters withDiskType(final String diskType) {
            this.diskType = diskType;
            return this;
        }

        public InstanceParameters withCdskType(final String cdsType) {
            this.cdsType = cdsType;
            return this;
        }

        public InstanceParameters withSourceInstanceId(final String sourceInstanceId) {
            this.sourceInstanceId = sourceInstanceId;
            return this;
        }

        public InstanceParameters withReplicaType(final String replicaType) {
            this.replicaType = replicaType;
            return this;
        }

        public InstanceParameters withBcmGroupName(final String bcmGroupName) {
            this.bcmGroupName = bcmGroupName;
            return this;
        }

        public InstanceParameters withLeaderAppId(final String leaderAppId) {
            this.leaderAppId = leaderAppId;
            return this;
        }

        public InstanceParameters withResourceType(final String resourceType) {
            this.resourceType = resourceType;
            return this;
        }

        public InstanceParameters withIsInheritMasterAuthip(final Boolean isInheritMasterAuthip) {
            this.isInheritMasterAuthip = isInheritMasterAuthip;
            return this;
        }

        public InstanceParameters withBackupPolicy(final SnapshotPolicy backupPolicy) {
            this.backupPolicy = backupPolicy;
            return this;
        }

        public InstanceParameters withEngine(final String engine) {
            this.engine = engine;
            return this;
        }

        public InstanceParameters withEngineVersion(final String engineVersion) {
            this.engineVersion = engineVersion;
            return this;
        }

        public InstanceParameters withInstanceExpireTime(final Date instanceExpireTime) {
            this.instanceExpireTime = instanceExpireTime;
            return this;
        }

        public InstanceParameters withInstanceClass(final String instanceClass) {
            this.instanceClass = instanceClass;
            return this;
        }

        public InstanceParameters withInstanceName(final String instanceName) {
            this.instanceName = instanceName;
            return this;
        }

        public InstanceParameters withPubliclyAccessible(final boolean publiclyAccessible) {
            this.publiclyAccessible = publiclyAccessible;
            return this;
        }

        public InstanceParameters withAllocatedStorageInGB(final Integer allocatedStorageInGB) {
            this.allocatedStorageInGB = allocatedStorageInGB;
            return this;
        }

        public InstanceParameters withAllocatedMemoryInMB(final Integer allocatedMemoryInMB) {
            this.allocatedMemoryInMB = allocatedMemoryInMB;
            return this;
        }

        public InstanceParameters withCharacterSetName(final String characterSetName) {
            this.characterSetName = characterSetName;
            return this;
        }

        public InstanceParameters withAzone(final String azone) {
            this.azone = azone;
            return this;
        }

        public InstanceParameters whitIsEnhanced(final Boolean isEnhanced) {
            this.isEnhanced = isEnhanced;
            return this;
        }

        public InstanceParameters withInstanceType(final String instanceType) {
            this.instanceType = instanceType;
            return this;
        }

        public InitialDataReference getInitialDataReference() {
            return initialDataReference;
        }

        public void setInitialDataReference(InitialDataReference initialDataReference) {
            this.initialDataReference = initialDataReference;
        }

        public String getInstanceType() {
            return instanceType;
        }

        public void setInstanceType(String instanceType) {
            this.instanceType = instanceType;
        }

        public Boolean getIsSingle() {
            return getSingle();
        }

        public void setIsSingle(Boolean single) {
            setSingle(single);
        }

        public Boolean getSingle() {
            return isSingle;
        }

        public void setSingle(Boolean single) {
            isSingle = single;
        }

        public String getTags() {
            return tags;
        }

        public void setTags(String tags) {
            this.tags = tags;
        }

        public String getDiskIoType() {
            return diskIoType;
        }

        public void setDiskIoType(String diskIoType) {
            this.diskIoType = diskIoType;
        }

        public String getDiskType() {
            return diskType;
        }

        public void setDiskType(String diskType) {
            this.diskType = diskType;
        }

        public String getCdsType() {
            return cdsType;
        }

        public void setCdsType(String cdsType) {
            this.cdsType = cdsType;
        }

        public String getOvip() {
            return ovip;
        }

        public void setOvip(String ovip) {
            this.ovip = ovip;
        }
        public InstanceParameters withOvip(final String ovip) {
            this.ovip = ovip;
            return this;
        }

        public String getBgwGroupId() {
            return bgwGroupId;
        }

        public InstanceParameters withBgwGroupId(String bgwGroupId) {
            this.bgwGroupId = bgwGroupId;
            return this;
        }

        public boolean isBgwGroupExclusive() {
            return bgwGroupExclusive;
        }

        public InstanceParameters withBgwGroupExclusive(boolean bgwGroupExclusive) {
            this.bgwGroupExclusive = bgwGroupExclusive;
            return this;
        }

        public Integer getEntryPort() {
            return entryPort;
        }

        public void setEntryPort(Integer entryPort) {
            this.entryPort = entryPort;
        }

        public Integer getLowerCaseTableNames() {
            return lowerCaseTableNames;
        }

        public void setLowerCaseTableNames(Integer lowerCaseTableNames) {
            this.lowerCaseTableNames = lowerCaseTableNames;
        }

        public Integer getForceHotUpgrade() {
            return forceHotUpgrade;
        }

        public void setForceHotUpgrade(Integer forceHotUpgrade) {
            this.forceHotUpgrade = forceHotUpgrade;
        }

        public String getMasterAzone() {
            return masterAzone;
        }

        public void setMasterAzone(String masterAzone) {
            this.masterAzone = masterAzone;
        }

        public String getBackupAzone() {
            return backupAzone;
        }

        public void setBackupAzone(String backupAzone) {
            this.backupAzone = backupAzone;
        }

        public String getParameterTemplateId() {
            return parameterTemplateId;
        }

        public void setParameterTemplateId(String parameterTemplateId) {
            this.parameterTemplateId = parameterTemplateId;
        }

        public String getEffectiveTime() {
            return effectiveTime;
        }

        public void setEffectiveTime(String effectiveTime) {
            this.effectiveTime = effectiveTime;
        }

        public List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> getData() {
            return data;
        }

        public void setData(List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> data) {
            this.data = data;
        }
    }

    public static class DccHostInfo {
        private DccHostId master;
        private DccHostId backup;

        public DccHostId getMaster() {
            return master;
        }

        public void setMaster(DccHostId master) {
            this.master = master;
        }

        public DccHostId getBackup() {
            return backup;
        }

        public void setBackup(DccHostId backup) {
            this.backup = backup;
        }
    }

    public static class DccHostId {
        private String hostId;

        private String azone;

        public DccHostId() {
        }

        public DccHostId(String hostId, String azone) {
            this.hostId = hostId;
            this.azone = azone;
        }

        public String getHostId() {
            return hostId;
        }

        public void setHostId(String hostId) {
            this.hostId = hostId;
        }

        public String getAzone() {
            return azone;
        }

        public void setAzone(String azone) {
            this.azone = azone;
        }
    }
}
