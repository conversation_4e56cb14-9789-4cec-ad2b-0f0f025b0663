package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceClientConfig;
import com.baidu.bce.internalsdk.core.BceInternalClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.rds.model.instance.ExtensionHistoryResp;
import com.baidu.bce.internalsdk.rds.model.instance.ExtensionListRequest;
import com.baidu.bce.internalsdk.rds.model.instance.ExtenstionListResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdsRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceListResponse;
import com.baidu.bce.internalsdk.rds.model.instance.OperateExtensionRequest;
import com.baidu.bce.internalsdk.rds.model.security.BlbIdsResponses;
import com.baidu.bce.internalsdk.rds.model.slowlog.PgLogListResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.PglogDownloadResponse;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;

public class RDSClient3 extends BceClient {
    private static final String SERVICE_NAME = "RDS3";

    private String baseURL = "";
    private String instanceBase = baseURL + "/instance";
    private String flashBase = baseURL + "/flashback";
    private String snapshotBase = "/backup";

    public RDSClient3(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    public RDSClient3(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public RDSClient3(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public BceInternalRequest createRdsRequest() {
        BceClientConfig config = new BceClientConfig();
        config.withReadTimeout(30000)
                .withMaxConnTotal(400)
                .withMaxConnPerRoute(400);
        BceInternalRequest bceInternalRequest = BceInternalClient.request(endpoint, config)
                .authorization(accessKey, secretKey);
        if (!StringUtils.isEmpty(securityToken)) {
            bceInternalRequest.securityToken(securityToken);
        }
        return bceInternalRequest;
    }

    public PgLogListResponse pglogList(String instanceId, String date) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/pglog");
        if (StringUtils.isNotEmpty(date)) {
            internalRequest.queryParam("date", date);
        }
        return internalRequest.get(PgLogListResponse.class);
    }

    public PglogDownloadResponse downloadPglog(String instanceId, String pglogId, Integer downloadValidTimeInSec) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/pglog/" + pglogId);
        if (downloadValidTimeInSec != null) {
            internalRequest.queryParam("downloadValidTimeInSec", downloadValidTimeInSec);
        }
        return internalRequest.get(PglogDownloadResponse.class);
    }

    public ExtenstionListResponse listExtensions(ExtensionListRequest request) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId() + "/extension");
        if (StringUtils.isNotEmpty(request.getDbName())) {
            internalRequest.queryParam("dbName", request.getDbName());
        }
        if (StringUtils.isNotEmpty(request.getExtName())) {
            internalRequest.queryParam("extName", request.getExtName());
        }

        internalRequest.queryParam("limit", request.getPageSize());
        String offset = String.valueOf((Integer.parseInt(request.getPageNo()) - 1)
                * Integer.parseInt(request.getPageSize()));
        internalRequest.queryParam("offset", offset);
        internalRequest.queryParam("installed", request.getInstalled());
        return internalRequest.get(ExtenstionListResponse.class);
    }

    public void operateExtension(OperateExtensionRequest request) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId() + "/extension");
        if (StringUtils.isNotEmpty(request.getDbName())) {
            internalRequest.queryParam("dbName", request.getDbName());
        }
        internalRequest.post(Entity.json(request));
    }

    public ExtensionHistoryResp historyList(String instanceId) {
        return this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/extensionHistory")
                .get(ExtensionHistoryResp.class);
    }

    public BlbIdsResponses getBlbIds(InstanceIdsRequest request) {
        return this.createRdsRequest()
                .path("/blb/getBLBByAppID")
                .post(Entity.json(request), BlbIdsResponses.class);
    }

    public InstanceListResponse instanceList(String type) {
        BceInternalRequest request = createRdsRequest().path(this.instanceBase);
        if (StringUtils.isNotEmpty(type)) {
            request.keyOnlyQueryParam(type);
        }
        return request.get(InstanceListResponse.class);
    }
}
