package com.baidu.bce.internalsdk.rds.model.group;


import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DashboardGroupAccountUpdateDescRequest {
    @NotNull
    private String groupId;

    @NotNull
    private String accountName;

    @NotNull
    private String remark;


    /*-----------------------auto generated--------------------------------*/
    @Override
    public String toString() {
        return "DashboardAccountUpdateDescRequest{" +
                "groupId='" + getGroupId() + '\'' +
                ", accountName='" + accountName + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
}
