package com.baidu.bce.internalsdk.rds.model.group;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardGroupAccountDeleteRequest {
    @NotNull
    private String groupId;
    @NotNull
    private String accountName;
    private String code;


    @Override
    public String toString() {
        return "DashboardAccountDeleteRequest{" +
                "groupId='" + getGroupId() + '\'' +
                ", accountName='" + getAccountName() + '\'' +
                ", code='" + getCode() + '\'' +
                '}';
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
