package com.baidu.bce.internalsdk.rds.model.paratemplate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ParaTemplateCompareRequest {

    @NotNull
    @Valid
    private String templateId;

    private String instanceId;

    private String effectiveTime;

    private boolean switchover = false;

    private List<String> instanceIds;

    public ParaTemplateCompareRequest() {
    }

    public ParaTemplateCompareRequest(String templateId, String instanceId, List<String> instanceIds) {
        this.templateId = templateId;
        this.instanceId = instanceId;
        this.instanceIds = instanceIds;

    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public boolean getSwitchover() {
        return switchover;
    }

    public void setSwitchover(boolean switchover) {
        this.switchover = switchover;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public List<String> getInstanceIds() {
        return instanceIds;
    }

    public void setInstanceIds(List<String> instanceIds) {
        this.instanceIds = instanceIds;
    }

    @Override
    public String toString() {
        return "ParaTemplateCompareRequest{" +
                "templateId='" + templateId + '\'' +
                ", instanceId='" + instanceId + '\'' +
                ", instanceIds=" + instanceIds +
                '}';
    }
}
