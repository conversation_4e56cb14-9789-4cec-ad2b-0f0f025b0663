package com.baidu.bce.internalsdk.rds.model;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MaintainDurationRequest {
    @IdPermission
    private String instanceId;
    private String maintainPeriod;
    private String maintainStartTime;
    private Integer maintainDuration;

    public String getMaintainPeriod() {
        return maintainPeriod;
    }

    public void setMaintainPeriod(String maintainPeriod) {
        this.maintainPeriod = maintainPeriod;
    }

    public String getMaintainStartTime() {
        return maintainStartTime;
    }

    public void setMaintainStartTime(String maintainStartTime) {
        this.maintainStartTime = maintainStartTime;
    }

    public Integer getMaintainDuration() {
        return maintainDuration;
    }

    public void setMaintainDuration(Integer maintainDuration) {
        this.maintainDuration = maintainDuration;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }
}
