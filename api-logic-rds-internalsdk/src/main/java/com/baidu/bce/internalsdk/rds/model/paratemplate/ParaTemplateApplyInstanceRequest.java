package com.baidu.bce.internalsdk.rds.model.paratemplate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ParaTemplateApplyInstanceRequest {

    @NotNull
    private String engine;

    @NotNull
    private String engineVersion;

    private String instanceType;

    public ParaTemplateApplyInstanceRequest() {
    }

    public ParaTemplateApplyInstanceRequest(String engine, String engineVersion, String instanceType) {
        this.engine = engine;
        this.engineVersion = engineVersion;
        this.instanceType = instanceType;
    }

    public String getInstanceType() {
        return instanceType;
    }

    public void setInstanceType(String instanceType) {
        this.instanceType = instanceType;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getEngineVersion() {
        return engineVersion;
    }

    public void setEngineVersion(String engineVersion) {
        this.engineVersion = engineVersion;
    }

    @Override
    public String toString() {
        return "ParaTemplateApplyInstanceRequest{" +
                "engine='" + engine + '\'' +
                ", engineVersion='" + engineVersion + '\'' +
                ", instanceType='" + instanceType + '\'' +
                '}';
    }
}
