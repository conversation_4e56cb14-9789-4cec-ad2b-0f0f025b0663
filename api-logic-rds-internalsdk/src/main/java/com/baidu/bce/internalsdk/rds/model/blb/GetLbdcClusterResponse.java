package com.baidu.bce.internalsdk.rds.model.blb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by yangxin on 2021/9/24
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetLbdcClusterResponse {

    private List<LbdcCluster> clusterList;
    private String marker;
    private String nextMarker;
    private boolean isTruncated;
    private int maxKeys;

    public List<LbdcCluster> getClusterList() {
        return clusterList;
    }

    public void setClusterList(List<LbdcCluster> clusterList) {
        this.clusterList = clusterList;
    }

    public String getMarker() {
        return marker;
    }

    public void setMarker(String marker) {
        this.marker = marker;
    }

    public String getNextMarker() {
        return nextMarker;
    }

    public void setNextMarker(String nextMarker) {
        this.nextMarker = nextMarker;
    }

    public boolean isIsTruncated() {
        return isTruncated;
    }

    public void setIsTruncated(boolean isTruncated) {
        this.isTruncated = isTruncated;
    }

    public int getMaxKeys() {
        return maxKeys;
    }

    public void setMaxKeys(int maxKeys) {
        this.maxKeys = maxKeys;
    }
}
