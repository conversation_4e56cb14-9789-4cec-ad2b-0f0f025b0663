package com.baidu.bce.internalsdk.rds.model.slowquery;

/**
 * Created by luping03 on 17/12/25.
 */
public class SlowqueryDownloadResponse extends SlowqueryBaseResponse {

    private DownloadUrlMode result;

    public DownloadUrlMode getResult() {
        return result;
    }

    public void setResult(DownloadUrlMode result) {
        this.result = result;
    }

    private static class DownloadUrlMode {
        private String downloadurl;

        public String getDownloadurl() {
            return downloadurl;
        }

        public void setDownloadurl(String downloadurl) {
            this.downloadurl = downloadurl;
        }
    }

}
