package com.baidu.bce.internalsdk.rds.model.rollback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SqlTaskDetailResponse {


    private String dataType;
    private String groupId = "baidu";
    private List<BinlogFlashTasks> binlogFlashTasks;


    public String getGroupId() {
        return groupId;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }


    public List<BinlogFlashTasks> getBinlogFlashTasks() {
        return binlogFlashTasks;
    }

    public void setBinlogFlashTasks(List<BinlogFlashTasks> binlogFlashTasks) {
        this.binlogFlashTasks = binlogFlashTasks;
    }
}
