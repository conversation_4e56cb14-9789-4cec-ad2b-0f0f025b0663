package com.baidu.bce.internalsdk.rds.model.group;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardGroupListRequest {

    private String orderBy;
    private String order;
    private int pageNo;
    private int pageSize;

    private String showMode = "all";
    private String engine;
    private String azone = "all";
    private List<ListFilter> filters;

    private String machineType;

    @Override
    public String toString() {
        return "DashboardInstanceListRequest{"
                + "orderBy='" + orderBy + '\''
                + ", order='" + order + '\''
                + ", pageNo=" + pageNo
                + ", pageSize=" + pageSize
                + "showMode='" + showMode + '\''
                + "engine='" + engine + '\''
                + "azone='" + azone + '\''
                + '}';
    }

    public String getMachineType() {
        return machineType;
    }

    public void setMachineType(String machineType) {
        this.machineType = machineType;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getShowMode() {
        return showMode;
    }

    public void setShowMode(String showMode) {
        this.showMode = showMode;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getAzone() {
        return azone;
    }

    public void setAzone(String azone) {
        this.azone = azone;
    }

    public List<ListFilter> getFilters() {
        return filters;
    }

    public void setFilters(List<ListFilter> filters) {
        this.filters = filters;
    }
}
