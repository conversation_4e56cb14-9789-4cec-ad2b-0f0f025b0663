package com.baidu.bce.internalsdk.rds.model.slowquery;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class SlowqueryInstanceIdRequest {
    @IdPermission
    private String instanceId;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

}
