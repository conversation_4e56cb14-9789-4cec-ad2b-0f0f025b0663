package com.baidu.bce.internalsdk.rds.model.migration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/10/27.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class MigrationConfig {
    private String remoteIp;
    private Integer remotePort;
    private String remoteUser;
    private String remotePassword;
    private List<String> databases;
    private String migrationType;
    private String migrationMethod;
    private Boolean lockTables;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("MigrationConfig{");
        sb.append("remoteIp='").append(remoteIp).append('\'');
        sb.append(", remotePort=").append(remotePort);
        sb.append(", remoteUser='").append(remoteUser).append('\'');
        sb.append(", remotePassword='").append(remotePassword).append('\'');
        sb.append(", databases=").append(databases);
        sb.append(", migrationType='").append(migrationType).append('\'');
        sb.append(", migrationMethod='").append(migrationMethod).append('\'');
        sb.append(", lockTables=").append(lockTables);
        sb.append('}');
        return sb.toString();
    }

    public String getRemotePassword() {
        return remotePassword;
    }

    public void setRemotePassword(String remotePassword) {
        this.remotePassword = remotePassword;
    }

    @NotNull
    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    @NotNull
    public Integer getRemotePort() {
        return remotePort;
    }

    public void setRemotePort(Integer remotePort) {
        this.remotePort = remotePort;
    }

    @NotNull
    public String getRemoteUser() {
        return remoteUser;
    }

    public void setRemoteUser(String remoteUser) {
        this.remoteUser = remoteUser;
    }

    public List<String> getDatabases() {
        return databases;
    }

    public void setDatabases(List<String> databases) {
        this.databases = databases;
    }

    public String getMigrationType() {
        return migrationType;
    }

    public void setMigrationType(String migrationType) {
        this.migrationType = migrationType;
    }

    public String getMigrationMethod() {
        return migrationMethod;
    }

    public void setMigrationMethod(String migrationMethod) {
        this.migrationMethod = migrationMethod;
    }

    public Boolean getLockTables() {
        return lockTables;
    }

    public void setLockTables(Boolean lockTables) {
        this.lockTables = lockTables;
    }
}
