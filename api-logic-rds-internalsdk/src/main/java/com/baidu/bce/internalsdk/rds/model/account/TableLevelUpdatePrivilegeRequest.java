package com.baidu.bce.internalsdk.rds.model.account;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TableLevelUpdatePrivilegeRequest {

    @IdPermission
    @NotNull
    private String instanceId;
    @NotNull
    private String user;
    @NotNull
    private String host;
    private TableLevelPrivilegeRequest privilege;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public TableLevelPrivilegeRequest getPrivilege() {
        return privilege;
    }

    public void setPrivilege(TableLevelPrivilegeRequest privilege) {
        this.privilege = privilege;
    }
}
