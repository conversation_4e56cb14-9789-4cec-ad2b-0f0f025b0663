package com.baidu.bce.internalsdk.rds.model.instance;

import java.util.List;

public class ResizeParams {
    public String appId;
    public String masterSubnetId;
    public String backupSubnetId;
    public String slaveSubnetId;
    public List<String> rdsproxySubnetIds;
    public Integer nodeAmount;
    public Integer cpuCount;
    public Integer allocatedMemoryInMB;
    public Integer allocatedStorageInGB;
    public Integer forceHotResize;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getMasterSubnetId() {
        return masterSubnetId;
    }

    public void setMasterSubnetId(String masterSubnetId) {
        this.masterSubnetId = masterSubnetId;
    }

    public String getBackupSubnetId() {
        return backupSubnetId;
    }

    public void setBackupSubnetId(String backupSubnetId) {
        this.backupSubnetId = backupSubnetId;
    }

    public String getSlaveSubnetId() {
        return slaveSubnetId;
    }

    public void setSlaveSubnetId(String slaveSubnetId) {
        this.slaveSubnetId = slaveSubnetId;
    }

    public Integer getNodeAmount() {
        return nodeAmount;
    }

    public void setNodeAmount(Integer nodeAmount) {
        this.nodeAmount = nodeAmount;
    }

    public Integer getCpuCount() {
        return cpuCount;
    }

    public void setCpuCount(Integer cpuCount) {
        this.cpuCount = cpuCount;
    }

    public Integer getAllocatedMemoryInMB() {
        return allocatedMemoryInMB;
    }

    public void setAllocatedMemoryInMB(Integer allocatedMemoryInMB) {
        this.allocatedMemoryInMB = allocatedMemoryInMB;
    }

    public Integer getAllocatedStorageInGB() {
        return allocatedStorageInGB;
    }

    public void setAllocatedStorageInGB(Integer allocatedStorageInGB) {
        this.allocatedStorageInGB = allocatedStorageInGB;
    }

    public Integer getForceHotResize() {
        return forceHotResize;
    }

    public void setForceHotResize(Integer forceHotResize) {
        this.forceHotResize = forceHotResize;
    }

    public List<String> getRdsproxySubnetIds() {
        return rdsproxySubnetIds;
    }

    public void setRdsproxySubnetIds(List<String> rdsproxySubnetIds) {
        this.rdsproxySubnetIds = rdsproxySubnetIds;
    }
}
