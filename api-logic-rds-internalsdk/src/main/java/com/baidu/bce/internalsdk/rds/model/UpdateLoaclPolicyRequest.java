package com.baidu.bce.internalsdk.rds.model;


import io.swagger.annotations.ApiModelProperty;

public class UpdateLoaclPolicyRequest {

    @ApiModelProperty("本地binlog最大存储空间使用率，单位为% ，范围是 [30, 50]\n" +
            "eg. 要设置30% 该参数传30   ")
    private long binlogSizePercent;
    @ApiModelProperty("本地binlog保存周期 单位为小时，范围是 [72, 168]")
    private long binlogExpiredHour;

    public long getBinlogSizePercent() {
        return binlogSizePercent;
    }

    public void setBinlogSizePercent(long binlogSizePercent) {
        this.binlogSizePercent = binlogSizePercent;
    }

    public long getBinlogExpiredHour() {
        return binlogExpiredHour;
    }

    public void setBinlogExpiredHour(long binlogExpiredHour) {
        this.binlogExpiredHour = binlogExpiredHour;
    }
}
