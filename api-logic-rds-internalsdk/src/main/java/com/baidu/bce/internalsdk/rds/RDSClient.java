package com.baidu.bce.internalsdk.rds;

import com.alibaba.fastjson.JSONObject;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceClientConfig;
import com.baidu.bce.internalsdk.core.BceInternalClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponse;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.rds.model.CheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.CommonResponse;
import com.baidu.bce.internalsdk.rds.model.FailinjectRequest;
import com.baidu.bce.internalsdk.rds.model.EffectiveTimeRequest;
import com.baidu.bce.internalsdk.rds.model.DeploymentPointRequest;
import com.baidu.bce.internalsdk.rds.model.PreCheckVersionRequest;
import com.baidu.bce.internalsdk.rds.model.RdsMinorVersionList;
import com.baidu.bce.internalsdk.rds.model.UpdateTaskRequest;
import com.baidu.bce.internalsdk.rds.model.UpdateVersionRequest;
import com.baidu.bce.internalsdk.rds.model.UpgradeMajorVersion;
import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.AccountCheckRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountGetResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountListResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePrivilegesRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateLockRequest;
import com.baidu.bce.internalsdk.rds.model.account.BnsListResponse;
import com.baidu.bce.internalsdk.rds.model.account.UpdateBnsRequest;
import com.baidu.bce.internalsdk.rds.model.account.UpdateBnsResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogDateTime;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogListResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.DashboardBinlogListRequest;
import com.baidu.bce.internalsdk.rds.model.binlog.OpenapiBinlogListResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.OpenapiBinlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.config.ConfigItem;
import com.baidu.bce.internalsdk.rds.model.config.ConfigItemListResponse;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyHistoryResponse;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyRequest;
import com.baidu.bce.internalsdk.rds.model.database.DataBaseCheckRequest;
import com.baidu.bce.internalsdk.rds.model.database.Database;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCheckExistRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseChecksizeResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCreateRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseGetResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListV2Response;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseOwnerRequest;
import com.baidu.bce.internalsdk.rds.model.database.TableListResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwModifyStateRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwStateGetResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwStateListResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectGetResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectListRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectListResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteListCreateRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteListResponse;
import com.baidu.bce.internalsdk.rds.model.errorlog.ErrorlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.errorlog.ErrorlogListResponse;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogErrorDetails;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogSlowDetails;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogDetailRequest;
import com.baidu.bce.internalsdk.rds.model.errorlog.OpenapiErrorlogListResponse;
import com.baidu.bce.internalsdk.rds.model.group.BatchJoinRequest;
import com.baidu.bce.internalsdk.rds.model.group.CreateGroupRequest;
import com.baidu.bce.internalsdk.rds.model.group.FollowerIdRequest;
import com.baidu.bce.internalsdk.rds.model.group.GroupForceChangeLeaderRequest;
import com.baidu.bce.internalsdk.rds.model.group.GroupForceChangeLeaderResponse;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupDetailResponse;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupListResponse;
import com.baidu.bce.internalsdk.rds.model.instance.GlobalInstanceResponses;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCreateRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceListResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceLockRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceOpenTdeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePnetIpResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceQuotChangeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceQuotTimeDetailResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceReplicaDelayMaster;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceRestoreRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceTdeStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateAddressRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateBackupPolicyRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateFlavorRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateNameRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdatePublicAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateReplicaOnLineRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateReplicationTypeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdatePortRequest;
import com.baidu.bce.internalsdk.rds.model.instance.OrderInfo;
import com.baidu.bce.internalsdk.rds.model.instance.ProxyTopoInfo;
import com.baidu.bce.internalsdk.rds.model.instance.PutFlowRequest;
import com.baidu.bce.internalsdk.rds.model.instance.AzoneInfo;
import com.baidu.bce.internalsdk.rds.model.instance.RecoveryToSourceInstanceRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCheckPortResponse;
import com.baidu.bce.internalsdk.rds.model.instance.HotupgradeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.HotUpgradeResponse;
import com.baidu.bce.internalsdk.rds.model.instance.SingletonToNormalRequest;
import com.baidu.bce.internalsdk.rds.model.migration.DatabaseConfig;
import com.baidu.bce.internalsdk.rds.model.migration.MigrationConfig;
import com.baidu.bce.internalsdk.rds.model.migration.MigrationDbList;
import com.baidu.bce.internalsdk.rds.model.migration.MigrationId;
import com.baidu.bce.internalsdk.rds.model.migration.MigrationStatus;
import com.baidu.bce.internalsdk.rds.model.migration.MigrationStatusResponse;
import com.baidu.bce.internalsdk.rds.model.migration.MigrationsResponse;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ApplyTemplateRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.DuplicateTempRequest;
import com.baidu.bce.internalsdk.rds.model.performance.ConnectionListResponse;
import com.baidu.bce.internalsdk.rds.model.performance.KillProcessRequest;
import com.baidu.bce.internalsdk.rds.model.performance.TransactionListResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.CreateRoGroupRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.CreateRoGroupResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateWeightRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupDetailResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupListResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupLeaveRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdatePubliclyAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateEnableDelayOffRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateIsBalanceReloadRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.UpdateRoGroupPropertyRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityIp;
import com.baidu.bce.internalsdk.rds.model.security.SecurityIpPutRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityIpResponse;
import com.baidu.bce.internalsdk.rds.model.security.SslAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.security.SslGetCaResponse;
import com.baidu.bce.internalsdk.rds.model.security.SslInfoResponse;
import com.baidu.bce.internalsdk.rds.model.security.UpdateProxyIpMsg;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogListResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.OpenapiSlowlogListResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogSummaryRequest;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogSummaryResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.DiskInfoResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaInstanceResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaPageRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaPageResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaResultResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaTopoResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotGetResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotListResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotListWithTimeResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.baidu.bce.internalsdk.rds.model.StatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceProcesslistResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceInnodbStatusResponse;
import com.baidu.bce.internalsdk.rds.model.SqlFilterList;
import com.baidu.bce.internalsdk.rds.model.SqlFilterResponse;
import com.baidu.bce.internalsdk.rds.model.SqlFilterRequest;
import com.baidu.bce.internalsdk.rds.model.SqlFilterAllowedResponse;
import com.baidu.bce.internalsdk.rds.model.MaintainDurationRequest;
import com.baidu.bce.internalsdk.rds.model.SwitchMasterBackupRequest;
import com.baidu.bce.internalsdk.rds.model.TaskRequest;
import com.baidu.bce.internalsdk.rds.model.MaintaintimeTasks;
import com.baidu.bce.internalsdk.rds.model.strategy.ExchangeStrategyResponse;
import com.baidu.bce.internalsdk.rds.model.strategy.SwitchPrecheckResponse;
import com.baidu.bce.internalsdk.rds.model.strategy.TaskStatusResponses;
import com.baidu.bce.internalsdk.rds.model.strategy.UpdateExchangeStrategyRequest;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;

import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by hejianbin on 2014/6/4.
 */
public class RDSClient extends BceClient {
    private static final String SERVICE_NAME = "RDS";

    private String baseURL = "";
    private String instanceBase = baseURL + "/instance";
    private String recycleBase = baseURL + "/recycle";
    private String groupBase = baseURL + "/group";
    private String snapshotBase = "/snapshot";
    private String dbfwBase = "/dbfirewall";
    private String smartDbaBase = "/smartdba";
    private String paraTemplateBase = "/para-tpl";
    private String roGroupBase = baseURL + "/rogroup";

    private String xAuthToken;

    public void setxAuthToken(String xAuthToken) {
        this.xAuthToken = xAuthToken;
    }

    public RDSClient(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    public RDSClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public RDSClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public RDSClient(String authToken, String accessKey, String secretKey, String endpoint, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
        this.setxAuthToken(authToken);
        BceInternalRequest.setThreadTokenId(null);
    }

    public BceInternalRequest createRdsServiceRequest() {
        BceInternalRequest request = createAuthorizedRequest();
        if (this.xAuthToken != null) {
            request.token(xAuthToken);
        }
        return request;
    }

    public BceInternalRequest createRdsRequest() {
        BceClientConfig config = new BceClientConfig();
        config.withReadTimeout(30000)
                .withMaxConnTotal(400)
                .withMaxConnPerRoute(400);
        BceInternalRequest bceInternalRequest = BceInternalClient.request(endpoint, config)
                .authorization(accessKey, secretKey);
        if (!StringUtils.isEmpty(securityToken)) {
            bceInternalRequest.securityToken(securityToken);
        }
        return bceInternalRequest;
    }

    /*-----------------------------------instance----------------------------------*/
    public InstanceListResponse instanceCreate(InstanceCreateRequest request) {
        BceInternalRequest internalRequest = createRdsRequest()
                .path(instanceBase);
        if (request.getInstanceParameters().getSourceInstanceId() != null
                && !"".equals(request.getInstanceParameters().getSourceInstanceId())) {
            if (request.getInstanceParameters().getEngine().equals("rdsproxy")) {
                internalRequest.keyOnlyQueryParam("rdsproxy");
            } else {
                internalRequest.keyOnlyQueryParam("readReplica");
            }

        }
        return internalRequest.post(Entity.json(request), InstanceListResponse.class);
    }

    public InstanceListResponse instanceList(String type) {
        BceInternalRequest request = createRdsRequest().path(this.instanceBase);
        if (StringUtils.isNotEmpty(type)) {
            request.keyOnlyQueryParam(type);
        }
        return request.get(InstanceListResponse.class);
    }

    public InstanceListResponse quotInstanceList(String type) {
        BceInternalRequest request = createRdsRequest().path(this.instanceBase);
        if (StringUtils.isNotEmpty(type)) {
            request.keyOnlyQueryParam(type);
        }
        return request.get(InstanceListResponse.class);
    }

    public InstanceListResponse instanceListByDccId(String hostId) {
        BceInternalRequest request = createRdsRequest().path(this.baseURL + "/dcc/" + hostId + "/instance");
        return request.get(InstanceListResponse.class);
    }

    public InstanceGetResponse instanceDescribe(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .get(InstanceGetResponse.class);
    }

    public void instanceDelete(String instanceId) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .delete(CommonResponse.class);
    }

    public void instanceDeleteBatch(String instanceIds) {
        createRdsRequest()
                .path(this.instanceBase + "/batchdelete")
                .queryParam("instanceId", instanceIds)
                .delete(CommonResponse.class);
    }

    public void instanceModifyName(String instanceId, InstanceUpdateNameRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("name")
                .put(Entity.json(request));
    }

    public void instanceModifyNameRaft(String instanceId, InstanceUpdateNameRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .queryParam("update", "instanceName")
                .put(Entity.json(request));
    }

    public void instanceModifyPubliclyAccessible(String instanceId, InstanceUpdatePublicAccessibleRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("publiclyAccessible")
                .put(Entity.json(request));
    }

    public void instanceModifyPubliclyAccessibleRaft(String instanceId, InstanceUpdatePublicAccessibleRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .queryParam("update", "publiclyAccessible")
                .put(Entity.json(request));
    }

    public void instanceModifyBackupPolicy(String instanceId, SnapshotPolicy snapshotPolicy) {
        InstanceUpdateBackupPolicyRequest request = new InstanceUpdateBackupPolicyRequest();
        request.setBackupPolicy(snapshotPolicy);
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("backupPolicy")
                .put(Entity.json(request));
    }

    public void instanceModifyDomain(String instanceId, InstanceUpdateAddressRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("endpoint")
                .put(Entity.json(request));
    }

    public void instanceModifyDomainRaft(String instanceId, InstanceUpdateAddressRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .queryParam("update", "endpoint")
                .put(Entity.json(request));
    }

    public void instanceModifyReplicationType(String instanceId, InstanceUpdateReplicationTypeRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("replicationType")
                .put(Entity.json(request));
    }

    public void instanceReboot(InstanceIdRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId())
                .keyOnlyQueryParam("reboot")
                .put(Entity.json(request));
    }

    public void instanceReboot(String instanceId, EffectiveTimeRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("reboot")
                .put(Entity.json(request));
    }

    public void instanceRebootRaft(String instanceId) {
        createRdsRequest()
                .path(taskBase(instanceId) + "/restart")
                .post();
    }

    public void instanceLock(String instanceId, String lockMode) {
        InstanceLockRequest request = new InstanceLockRequest();
        request.setLockMode(lockMode);
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("lock")
                .put(Entity.json(request));
    }

    public void instanceLockBatch(String instanceIds, String lockMode) {
        InstanceLockRequest request = new InstanceLockRequest();
        request.setLockMode(lockMode);
        request.setInstanceIds(instanceIds);
        createRdsRequest()
                .path(this.instanceBase + "/batch/lockmode")
                .put(Entity.json(request));
    }

    public void instanceRestoreSnapshot(String instanceId, InstanceRestoreRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("restore")
                .put(Entity.json(request));
    }

    public void instanceUpdateFlavor(String instanceId, String clientToken, InstanceUpdateFlavorRequest instanceUpdateFlavorRequest) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .queryParam("clientToken", clientToken)
                .put(Entity.json(instanceUpdateFlavorRequest));
    }

    public void instanceSingletonToNormal(String instanceId, SingletonToNormalRequest instanceUpdateFlavorRequest) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("singleChangeNormal")
                .put(Entity.json(instanceUpdateFlavorRequest));
    }

    public InstanceReplicaDelayMaster getReplicaDelay(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("secondsBehindMaster")
                .get(InstanceReplicaDelayMaster.class);
    }

    public OrderInfo getOrderInfo(String orderId) {
        return createRdsRequest().path("/order/" + orderId).get(OrderInfo.class);
    }

    public void onOrOfflineReplica(InstanceUpdateReplicaOnLineRequest request) {
        createRdsRequest()
                .path(this.baseURL + "/rdsproxy")
                .put(Entity.json(request));
    }

    public void putFlow(PutFlowRequest request) {
        createRdsRequest()
                .path(this.baseURL + "/rdsproxy/flow")
                .put(Entity.json(request));
    }

    // 获取过保可替换时间
    public InstanceQuotTimeDetailResponse quotFindRepairTime(String instanceId, String type) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("quotRepairTime")
                .queryParam("type", type)
                .get(InstanceQuotTimeDetailResponse.class);
    }

    // 设置过保时间
    public void quotChangeRepairTime(InstanceQuotChangeRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId())
                .keyOnlyQueryParam("quot")
                .put(Entity.json(request));
    }

    public InstanceProcesslistResponse getInstanceProcesslist(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/information/processlist")
                .get(InstanceProcesslistResponse.class);
    }
    public InstanceInnodbStatusResponse getInstanceInnodbStatus(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/information/innodbstatus")
                .get(InstanceInnodbStatusResponse.class);
    }

    public void instanceModifyPort(String instanceId, InstanceUpdatePortRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("port")
                .put(Entity.json(request));
    }

    public InstanceCheckPortResponse instanceEnrtyCheck(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/entry/check")
                .get(InstanceCheckPortResponse.class);
    }

    /*------------------------------account ---------------------------------*/
    public CheckExistResponse accountCheck(String instanceId, String accountName) {
        AccountCheckRequest request = new AccountCheckRequest();
        request.setAccountName(accountName);

        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/account/checkname")
                .post(Entity.json(request), CheckExistResponse.class);
    }

    public CheckExistResponse accountCheckRaft(String instanceId, String accountName) {
        CheckExistResponse response = new CheckExistResponse();
        BceInternalResponse internalResponse = createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/account/" + accountName)
                .headWithResponse();
        if (internalResponse.getStatus() == 200) { // 200表示名字存在，404表示不存在
            response.setIsExist(1);
        }
        return response;
    }

    public AccountListResponse accountList(String instanceID) {
        return createRdsRequest()
                .path(this.accountBase(instanceID))
                .get(AccountListResponse.class);
    }

    public AccountGetResponse accountDescribe(String instanceId, String accountName) {
        BceInternalResponse response = createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .getWitResponse();
        String eTag = response.header("Etag");
        if (eTag == null) {
            eTag = response.header("ETag");
        }
        AccountGetResponse result = response.getEntity(AccountGetResponse.class).withETag(eTag);
        response.close();
        return result;
    }

    public void accountCreate(String instanceId, Account request) {
        if (request.getRemark() == null) {
            request.setRemark("");
        }
        createRdsRequest()
                .path(this.accountBase(instanceId))
                .post(Entity.json(request));
    }

    public void accountModifyPassword(String instanceId, String accountName, String password, String encryptedPassword) {
        AccountUpdatePasswordRequest request = new AccountUpdatePasswordRequest();
        request.setPassword(password);
        request.setEncryptedPassword(encryptedPassword);
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .keyOnlyQueryParam("password")
                .put(Entity.json(request));
    }

    public void accountModifyLock(String instanceId, String accountName, String lockMode) {
        AccountUpdateLockRequest request = new AccountUpdateLockRequest();
        request.setLockMode(lockMode);
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .keyOnlyQueryParam("lock")
                .put(Entity.json(request));
    }

    public void accountModifyPasswordRaft(String instanceId, String accountName, String password, String encryptedPassword) {
        AccountUpdatePasswordRequest request = new AccountUpdatePasswordRequest();
        request.setPassword(password);
        request.setEncryptedPassword(encryptedPassword);
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .queryParam("update", "password")
                .put(Entity.json(request));
    }

    public void accountModifyPrivilege(String instanceId,
                                       String accountName,
                                       Collection<Account.DatabasePrivilege> databasePrivileges,
                                       String eTag) {
        AccountUpdatePrivilegesRequest request = new AccountUpdatePrivilegesRequest();
        request.setDatabasePrivileges(databasePrivileges);
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .keyOnlyQueryParam("privileges")
                .header("x-bce-if-match", eTag)
                .put(Entity.json(request));
    }

    public void accountModifyPrivilegeRaft(String instanceId,
                                           String accountName,
                                           Collection<Account.DatabasePrivilege> databasePrivileges,
                                           String eTag) {
        AccountUpdatePrivilegesRequest request = new AccountUpdatePrivilegesRequest();
        request.setDatabasePrivileges(databasePrivileges);
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .queryParam("update", "databasePrivileges")
                .header("x-bce-if-match", eTag)
                .put(Entity.json(request));
    }

    public void accountModifyRemark(String instanceId, String accountName, String remark) {
        AccountUpdateRemarkRequest request = new AccountUpdateRemarkRequest();
        request.setRemark(remark);
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .keyOnlyQueryParam("remark")
                .put(Entity.json(request));
    }

    // raft版使用
    public void accountModifyRemarkRaft(String instanceId, String accountName, String remark) {
        AccountUpdateRemarkRequest request = new AccountUpdateRemarkRequest();
        request.setRemark(remark);
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .queryParam("update", "remark")
                .put(Entity.json(request));
    }

    public void accountDelete(String instanceId, String accountName) {
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .delete();
    }

    /*----------------------------database -----------------------*/
    public CheckExistResponse dataBaseCheck(String instanceId, String dbName) {
        DataBaseCheckRequest request = new DataBaseCheckRequest();
        request.setDbName(dbName);
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/database/checkname")
                .post(Entity.json(request), CheckExistResponse.class);
    }

    public CheckExistResponse dataBaseCheckRaft(String instanceId, String dbName) {
        CheckExistResponse response = new CheckExistResponse();
        BceInternalResponse internalResponse = createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/database/" + dbName)
                .headWithResponse();
        if (internalResponse.getStatus() == 200) { // 200表示名字存在，404表示不存在
            response.setIsExist(1);
        }
        return response;
    }

    public DatabaseListResponse databaseList(String instanceId) {
        return createRdsRequest()
                .path(this.databaseBase(instanceId))
                .get(DatabaseListResponse.class);
    }

    public DatabaseGetResponse databaseDescribe(String instanceId, String dbName) {
        DatabaseUpdateRemarkRequest databaseUpdateRemarkRequest = new DatabaseUpdateRemarkRequest();
        databaseUpdateRemarkRequest.setDbName(dbName);
        return createRdsRequest()
                .path(this.databaseBase(instanceId) + "/" + "show")
                .post(Entity.json(databaseUpdateRemarkRequest), DatabaseGetResponse.class);
    }

    public void databaseDelete(String instanceId, String dbName) {
        DatabaseUpdateRemarkRequest databaseUpdateRemarkRequest = new DatabaseUpdateRemarkRequest();
        databaseUpdateRemarkRequest.setDbName(dbName);
        createRdsRequest()
                .path(this.databaseBase(instanceId) + "/" + "delete")
                .post(Entity.json(databaseUpdateRemarkRequest));
    }

    public void pgDatabaseDelete(String instanceId, String dbName) {
        createRdsRequest()
                .path(this.databaseBase(instanceId) + "/" + dbName)
                .delete();
    }

    public void databaseCreate(String instanceId, DatabaseCreateRequest request) {
        createRdsRequest()
                .path(this.databaseBase(instanceId))
                .post(Entity.json(request));
    }

    public void databaseModifyRemark(String instanceId, String dbName, DatabaseUpdateRemarkRequest request) {
        request.setDbName(dbName);
        createRdsRequest()
                .path(this.databaseBase(instanceId) + "/" + "update")
                .put(Entity.json(request));
    }

    public void databaseModifyRemarkRaft(String instanceId, String dbName, DatabaseUpdateRemarkRequest request) {
        createRdsRequest()
                .path(this.databaseBase(instanceId) + "/" + dbName)
                .queryParam("update", "remark")
                .put(Entity.json(request));
    }

    public void databaseModifyOwner(String instanceId, String dbName, DatabaseOwnerRequest request) {
        createRdsRequest()
                .path(this.databaseBase(instanceId) + "/update")
                .keyOnlyQueryParam("owner")
                .put(Entity.json(request));
    }

    /*----------------------------snapshot -------------------------*/
    public SnapshotListWithTimeResponse snapshotListWithTime(String instanceId, String subPath) {
        return createRdsRequest()
                .path(snapshotBase + "/" + subPath)
                .queryParam("instanceId", instanceId)
                .get(SnapshotListWithTimeResponse.class);
    }

    public SnapshotListResponse snapshotList(String instanceId) {
        return createRdsRequest()
                .path(snapshotBase)
                .queryParam("instanceId", instanceId)
                .get(SnapshotListResponse.class);
    }

    public SnapshotGetResponse snapshotGet(String instanceId, String snapshotId, Integer downloadValidTimeInSec) {
        if (downloadValidTimeInSec == null) {
            return createRdsRequest()
                    .path(snapshotBase + "/" + snapshotId)
                    .queryParam("instanceId", instanceId)
                    .get(SnapshotGetResponse.class);
        } else {
            return createRdsRequest()
                    .path(snapshotBase + "/" + snapshotId)
                    .queryParam("instanceId", instanceId)
                    .queryParam("downloadValidTimeInSec", downloadValidTimeInSec)
                    .get(SnapshotGetResponse.class);
        }
    }

    public void snapshotCreate(String instanceId) {
        createRdsRequest()
                .path(snapshotBase)
                .queryParam("instanceId", instanceId)
                .post();
    }

    public SlowlogSummaryResponse slowlogSummary(SlowlogSummaryRequest request) {
        return createRdsRequest()
                .path(slowlogBase(request.getInstanceId()) + "/summary")
                .post(Entity.json(request), SlowlogSummaryResponse.class);
    }

    /*--------------------------------binlog-----------------------*/
    public BinlogListResponse binlogList(String instanceId, String date) {
        return createRdsRequest()
                .path(binlogBase(instanceId))
                .queryParam("datetime", date)
                .get(BinlogListResponse.class);
    }

    /**
     * 方法重载
     * @param request
     * @return
     */
    public BinlogListResponse binlogList(DashboardBinlogListRequest request) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(binlogBase(request.getInstanceId()))
                .queryParam("datetime", request.getStartDateTime());
        if (StringUtils.isNotEmpty(request.getEndDateTime())) {
            internalRequest.queryParam("binlogEndTime", request.getEndDateTime());
        }
        if (request.getPageNo() != 0) {
            internalRequest.queryParam("marker", request.getPageNo());
        }
        if (request.getPageSize() != 0) {
            internalRequest.queryParam("maxKeys", request.getPageSize());
        }

        return internalRequest.get(BinlogListResponse.class);

    }

    // raft版，暂时不用
    @Deprecated
    public BinlogListResponse binlogListRaft(String instanceId, String date) {
        return createRdsRequest()
                .path(backupBase(instanceId) + "/binlog")
                .queryParam("datetime", date)
                .get(BinlogListResponse.class);
    }

    public BinlogGetResponse binlogGet(String instanceId, String binlogId, Integer downloadValidTimeInSec) {
        return createRdsRequest()
                .path(binlogBase(instanceId) + "/" + binlogId)
                .queryParam("downloadValidTimeInSec", downloadValidTimeInSec)
                .get(BinlogGetResponse.class);
    }

    public void binlogCheck(String instanceId, Date date) {
        BinlogDateTime dateTime = new BinlogDateTime();
        dateTime.setDatetime(date);
        createRdsRequest()
                .path(binlogCheck(instanceId))
                .post(Entity.json(dateTime));
    }

    public void checkPgWal(String instanceId, Date date) {
        BinlogDateTime dateTime = new BinlogDateTime();
        dateTime.setDatetime(date);
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/wal/check")
                .post(Entity.json(dateTime));
    }

    public OpenapiBinlogListResponse binlogList2(String instanceId, String date) {
        return createRdsRequest()
                .path(binlogBase(instanceId))
                .queryParam("datetime", date)
                .get(OpenapiBinlogListResponse.class);
    }

    public OpenapiBinlogGetResponse binlogGet2(String instanceId, String binlogId, Integer downloadValidTimeInSec) {
        return createRdsRequest()
                .path(binlogBase(instanceId) + "/" + binlogId)
                .queryParam("downloadValidTimeInSec", downloadValidTimeInSec)
                .get(OpenapiBinlogGetResponse.class);
    }

    public void binlogFlush(String instanceId) {
        createRdsRequest()
                .path(binlogBase(instanceId) + "/flush")
                .post();
    }

    /*-------------------------------slowlog---------------------------*/
    public SlowlogListResponse slowlogList(String instanceId, String date) {
        return createRdsRequest()
                .path(slowlogBase(instanceId))
                .queryParam("datetime", date)
                .get(SlowlogListResponse.class);
    }

    public SlowlogGetResponse slowlogGet(String instanceId, String slowlogId, Integer downloadValidTimeInSec) {
        return createRdsRequest()
                .path(slowlogBase(instanceId) + "/" + slowlogId)
                .queryParam("downloadValidTimeInSec", downloadValidTimeInSec)
                .get(SlowlogGetResponse.class);
    }

    public OpenapiSlowlogListResponse slowlogList2(String instanceId, String date) {
        return createRdsRequest()
                .path(slowlogBase(instanceId))
                .queryParam("datetime", date)
                .get(OpenapiSlowlogListResponse.class);
    }

    /*-------------------------------log---------------------------*/
//    public ApiLogListResponse apiLogList(String instanceId, Date startTime, Date endTime) {
//        return createRdsRequest()
//                .path(instanceBase + "/" + instanceId + "/apiLog")
//                .queryParam("startTime", BceFormat.getDateTimeFormat().format(startTime))
//                .queryParam("endTime", BceFormat.getDateTimeFormat().format(endTime))
//                .get(ApiLogListResponse.class);
//    }
    /*-------------------------------errorlog---------------------------*/
    public ErrorlogListResponse errorlogList(String instanceId, String date) {
        return createRdsRequest()
                .path(errorlogBase(instanceId))
                .queryParam("datetime", date)
                .get(ErrorlogListResponse.class);
    }

    public ErrorlogGetResponse errorlogGet(String instanceId, String errorlogId, Integer downloadValidTimeInSec) {
        return createRdsRequest()
                .path(errorlogBase(instanceId) + "/" + errorlogId)
                .queryParam("downloadValidTimeInSec", downloadValidTimeInSec)
                .get(ErrorlogGetResponse.class);
    }
    public LogErrorDetails getErrorLogDetails(String instanceId, LogDetailRequest logDetailRequest) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/errorlog/detail")
                .post(Entity.json(logDetailRequest), LogErrorDetails.class);
    }
    public LogSlowDetails getSlowLogDetails(String instanceId, LogDetailRequest logDetailRequest) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/slowlog/detail")
                .post(Entity.json(logDetailRequest), LogSlowDetails.class);
    }

    public OpenapiErrorlogListResponse errorlogList2(String instanceId, String date) {
        return createRdsRequest()
                .path(errorlogBase(instanceId))
                .queryParam("datetime", date)
                .get(OpenapiErrorlogListResponse.class);
    }

    /*-------------------------------white list--------------------*/
    public SecurityIp queryWhiteList(String instanceID) {
        BceInternalResponse response = createRdsRequest()
                .path(securityIpBase(instanceID))
                .getWitResponse();
        SecurityIpResponse securityIpResponse = response.getEntity(SecurityIpResponse.class);
        SecurityIp securityIp = new SecurityIp();
        String eTag = response.header("Etag");
        if (eTag == null) {
            eTag = response.header("ETag");
        }
        securityIp.withETag(eTag)
                .withIps(securityIpResponse.getSecurityIps());
        response.close();
        return securityIp;
    }

    public void updateWhiteList(String instanceId, Collection<String> ips, String etag) {
        SecurityIpPutRequest request = new SecurityIpPutRequest();
        request.setSecurityIps(ips);
        createRdsRequest()
                .path(securityIpBase(instanceId))
                .header("x-bce-if-match", etag)
                .put(Entity.json(request));
    }

    public SslInfoResponse querySslInfo(String instanceID) {
        SslInfoResponse response = createRdsRequest()
                .path(this.baseURL + "/ssl/" + instanceID)
                .get(SslInfoResponse.class);
        return response;
    }

    public SslInfoResponse updateSslAccessible(String instanceID, SslAccessibleRequest sslAccessibleRequest) {
        SslInfoResponse response = createRdsRequest()
                .path(this.baseURL + "/ssl/" + instanceID)
                .keyOnlyQueryParam("sslAccessible")
                .put(Entity.json(sslAccessibleRequest), SslInfoResponse.class);
        return response;
    }

    public SslGetCaResponse queryCa() {
        SslGetCaResponse response = createRdsRequest()
                .path(this.baseURL + "/ssl/static/ca")
                .get(SslGetCaResponse.class);
        return response;
    }

    /*----------------------------migration-----------------------------------*/

    public MigrationsResponse getMigrationList(String instanceId, String startTime, String endTime) {
        BceInternalRequest request = this.createRdsRequest()
                .path(migrationBase(instanceId));
        if (startTime != null) {
            request.queryParam("startTime", startTime);
        }
        if (endTime != null) {
            request.queryParam("endTime", endTime);
        }

        return request.get(MigrationsResponse.class);
    }


    public MigrationStatus getMigrationStatus(String instanceId, String migrationId) {
        return this.createRdsRequest()
                .path(migrationBase(instanceId))
                .path("/" + migrationId)
                .get(MigrationStatusResponse.class).getMigration();
    }

    public MigrationDbList getMigrationDbList(String instanceId, DatabaseConfig migrationConfig) {
        return this.createRdsRequest()
                .path(migrationBase(instanceId))
                .queryParam("action", "getDbList")
                .queryParam("remoteIp", migrationConfig.getRemoteIp())
                .queryParam("remotePort", migrationConfig.getRemotePort())
                .queryParam("remoteUser", migrationConfig.getRemoteUser())
                .queryParam("remotePassword", migrationConfig.getRemotePassword())
                .get(MigrationDbList.class);
    }

    public MigrationId migrationCheck(String instanceId, MigrationConfig migrationConfig) {
        return this.createRdsRequest()
                .path(migrationBase(instanceId))
                .queryParam("action", "check")
                .post(Entity.json(migrationConfig), MigrationId.class);
    }

    public void createMigrationTask(String instanceId, String migrationId) {
        this.createRdsRequest()
                .path(migrationBase(instanceId))
                .path("/" + migrationId)
                .post();
    }

    public void cancelMigrationTask(String instanceId, String migrationId) {
        this.createRdsRequest()
                .path(migrationBase(instanceId))
                .path("/" + migrationId)
                .queryParam("action", "cancel")
                .put();
    }

    public void stopMigrationTask(String instanceId, String migrationId) {
        this.createRdsRequest()
                .path(migrationBase(instanceId))
                .path("/" + migrationId)
                .queryParam("action", "stopReplicate")
                .put();
    }

    /*------------------------------config------------------------------------*/
    public ConfigItem.ConfigItemList getConfigItems(String instanceId) {
        BceInternalResponse response = this.createRdsRequest()
                .path(configBase(instanceId))
                .getWitResponse();
        ConfigItem.ConfigItemList parameters = response.getEntity(ConfigItemListResponse.class).getParameters();
        String eTag = response.header("Etag");
        if (eTag == null) {
            eTag = response.header("ETag");
        }
        response.close();
        for (ConfigItem configItem : parameters) {
            configItem.setEtag(eTag);
        }
        return parameters;
    }

    public void modifyConfigItem(String instanceId, ConfigModifyRequest request, String eTag) {
        this.createRdsRequest()
                .path(configBase(instanceId))
                .header("x-bce-if-match", eTag)
                .put(Entity.json(request));
    }

    public ConfigModifyHistoryResponse getConfigModifyHistory(String instanceId) {
        return this.createRdsRequest()
                .path(configBase(instanceId))
                .keyOnlyQueryParam("history")
                .get(ConfigModifyHistoryResponse.class);
    }

    /*---------------------------dbfirewall-----------------------------------*/
    public DbfwStateListResponse dbfwStateList() {
        return createRdsRequest()
                .path(dbfwBase)
                .get(DbfwStateListResponse.class);

    }

    public DbfwStateGetResponse dbfwStateDescribe(String instanceId) {
        return createRdsRequest()
                .path(dbfwBase + "/" + instanceId)
                .get(DbfwStateGetResponse.class);
    }

    public void dbfwModifyState(String instanceId, DbfwModifyStateRequest request) {
        createRdsRequest()
                .path(dbfwBase + "/" + instanceId)
                .put(Entity.json(request));
    }

//    // 获取主备切换信息
//    public InstanceMasterBackupInfoResponse masterBackupInfo(String instanceId) {
//        return createRdsRequest()
//                .path(this.instanceBase + "/" + instanceId)
//                .keyOnlyQueryParam("masterBackupInfo")
//                .get(InstanceMasterBackupInfoResponse.class);
//    }
//
    // 主备切换接口
    public void switchMasterBackup(String instanceId, SwitchMasterBackupRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("switch")
                .put(Entity.json(request));
    }

    public SqlInjectListResponse sqlInjectList(String instanceId, SqlInjectListRequest request) {
        BceInternalRequest internalRequest = createRdsRequest()
                .path(dbfwBase + "/" + instanceId + "/sqlinjectlist");
        if (StringUtils.isNotBlank(request.getStartTime())) {
            internalRequest.queryParam("startTime", request.getStartTime());
        }
        if (StringUtils.isNotBlank(request.getEndTime())) {
            internalRequest.queryParam("endTime", request.getEndTime());
        }
        if (StringUtils.isNotBlank(request.getDbName())) {
            internalRequest.queryParam("dbName", request.getDbName());
        }
        if (StringUtils.isNotBlank(request.getAccountName())) {
            internalRequest.queryParam("accountName", request.getAccountName());
        }
        return internalRequest.get(SqlInjectListResponse.class);
    }

    public SqlInjectGetResponse sqlInjectDescribe(String instanceId, String sqlId) {
        return createRdsRequest()
                .path(dbfwBase + "/" + instanceId + "/sqlinjectlist" + "/" + sqlId)
                .get(SqlInjectGetResponse.class);
    }

    public SqlWhiteListResponse sqlWhiteList(String instanceId) {
        return createRdsRequest()
                .path(sqlWhiteListBase(instanceId))
                .get(SqlWhiteListResponse.class);
    }

    public void sqlWhiteListCreate(String instanceId, SqlWhiteListCreateRequest request) {
        createRdsRequest()
                .path(sqlWhiteListBase(instanceId))
                .post(Entity.json(request));
    }

    public void sqlWhiteListDelete(String instanceId, String sqlMd5) {
        createRdsRequest().path(sqlWhiteListBase(instanceId))
                .queryParam("sqlMd5", sqlMd5)
                .delete();
    }

    public InstancePnetIpResponse pnetIp(String instanceId) {
        return createRdsRequest().path(this.baseURL + "/instance/" + instanceId)
                .keyOnlyQueryParam("pnetIp")
                .get(InstancePnetIpResponse.class);
    }

    public void openTde(String instanceId, InstanceOpenTdeRequest request) {
        createRdsRequest()
                .path(this.baseURL + "/instance/" + instanceId + "/tde")
                .post(Entity.json(request));
    }

    public InstanceTdeStatusResponse checkTde(String instanceId) {
        return createRdsRequest().path(this.baseURL + "/instance/" + instanceId + "/tde")
                .get(InstanceTdeStatusResponse.class);
    }

    public StatusResponse checkFdisk(String instanceId){
        return createRdsRequest()
                .path(this.baseURL + "/instance/" + instanceId + "/check-fdisk")
                .get(StatusResponse.class);
    }

    public void azoneMigration(String instanceId, AzoneInfo request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("azone")
                .put(Entity.json(request));

    }

    public HotUpgradeResponse checkHotUpgrade(String instanceId, HotupgradeRequest request) {
        return createRdsRequest()
                .path(this.baseURL + "/instance/" + instanceId + "/precheck/hotupgrade")
                .post(Entity.json(request), HotUpgradeResponse.class);
    }

    /*----------------------------end-----------------------------------------*/

    private String backupBase(String instanceId) {
        return this.instanceBase + instanceId + "/backup";
    }

    private String accountBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/account";
    }

    private String databaseBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/database";
    }

    private String groupAccountBase(String groupId) {
        return this.groupBase + "/" + groupId + "/account";
    }

    private String groupDatabaseBase(String instanceId) {
        return this.groupBase + "/" + instanceId + "/database";
    }

    private String binlogBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/binlog";
    }

    private String binlogCheck(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/binlog/check";
    }

    private String slowlogBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/slowlog";
    }

    private String errorlogBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/errorlog";
    }

    private String securityIpBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/securityIp";
    }

    private String migrationBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/migration";
    }

    private String configBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/parameter";
    }

    private String sqlWhiteListBase(String instanceId) {
        return this.dbfwBase + "/" + instanceId + "/sqlwhitelist";
    }

    private String taskBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/task";
    }



    /*----------------------------group -----------------------------------------*/
    public InstanceGroupListResponse instanceGroupList() {
        BceInternalRequest request = createRdsRequest().path(this.groupBase);
        return request.get(InstanceGroupListResponse.class);
    }

    public InstanceGroupDetailResponse instanceGroupDetail(String groupId) {
        return createRdsRequest()
                .path(groupBase + "/" + groupId)
                .get(InstanceGroupDetailResponse.class);
    }

    public void instanceGroupCheckGtid(String instanceId) {

        Map body = new HashMap();
        body.put("instanceId", instanceId);
        createRdsRequest()
                .path(groupBase + "/check-gtid")
                .post(Entity.json(body));
    }


    public void instanceGroupCheckPing(String sourceId, String targetId) {

        Map body = new HashMap();
        body.put("sourceId", sourceId);
        body.put("targetId", targetId);

        createRdsRequest()
                .path(groupBase + "/check-ping")
                .post(Entity.json(body));
    }

    public void instanceGroupCheckData(String instanceId) {

        Map body = new HashMap();
        body.put("instanceId", instanceId);

        createRdsRequest()
                .path(groupBase + "/check-data")
                .post(Entity.json(body));
    }


    public void instanceGroupCreate(CreateGroupRequest request) {

        createRdsRequest()
                .path(groupBase)
                .post(Entity.json(request));
    }

    public void instanceGroupJoin(FollowerIdRequest request) {

        createRdsRequest()
                .path(groupBaseUrlWithId(request.getGroupId())+ "/instance")
                .post(Entity.json(request));
    }

    public void instanceGroupChangeName(String groupId, String name) {

        Map body = new HashMap();
        body.put("name", name);

        createRdsRequest()
                .path(groupBaseUrlWithId(groupId))
                .keyOnlyQueryParam("name")
                .put(Entity.json(body));
    }

    public void instanceGroupDelete(String groupId) {
        createRdsRequest()
                .path(groupBaseUrlWithId(groupId))
                .delete();
    }

    public void instanceGroupChangeLeader(String groupId, String newLeader) {

        Map body = new HashMap();
        body.put("leaderId", newLeader);

        createRdsRequest()
                .path(groupBaseUrlWithId(groupId) + "/instance")
                .put(Entity.json(body));
    }

    public GroupForceChangeLeaderResponse instanceGroupForceChangeLeader(String groupId,
                                                             GroupForceChangeLeaderRequest changeLeaderRequest) {
        return createRdsRequest()
                .path(groupBaseUrlWithId(groupId) + "/instance")
                .put(Entity.json(changeLeaderRequest), GroupForceChangeLeaderResponse.class);
    }

    public void instanceGroupSignOut(String groupId, String instanceId) {
        createRdsRequest()
                .path(groupBaseUrlWithId(groupId) + "/instance/" + instanceId)
                .delete();
    }

    public AccountGetResponse instanceGroupAccountDetail(String groupId, String accountName) {
        return createRdsRequest()
                .path(groupBaseUrlWithId(groupId) + "/account/" + accountName)
                .get(AccountGetResponse.class);
    }

    /*------------------------------account ---------------------------------*/
    public CheckExistResponse instanceGroupAccountCheckName(String groupId, String accountName) {
        AccountCheckRequest request = new AccountCheckRequest();
        request.setAccountName(accountName);

        return createRdsRequest()
                .path(this.groupBase + "/" + groupId + "/account/checkname")
                .post(Entity.json(request), CheckExistResponse.class);
    }

    public AccountListResponse instanceGroupAccountList(String groupId) {
        return createRdsRequest()
                .path(this.groupAccountBase(groupId))
                .get(AccountListResponse.class);
    }

    public AccountGetResponse instanceGroupAccountDescribe(String groupId, String accountName) {
        BceInternalResponse response = createRdsRequest()
                .path(this.groupAccountBase(groupId) + "/" + accountName)
                .getWitResponse();
        AccountGetResponse result = response.getEntity(AccountGetResponse.class);
        response.close();
        return result;
    }

    public void instanceGroupAccountCreate(String groupId, Account request) {
        createRdsRequest()
                .path(this.groupAccountBase(groupId))
                .post(Entity.json(request));
    }

    public void instanceGroupAccountModifyPassword(String groupId, String accountName, String encryptedPassword) {
        AccountUpdatePasswordRequest request = new AccountUpdatePasswordRequest();
        request.setEncryptedPassword(encryptedPassword);
        createRdsRequest()
                .path(this.groupAccountBase(groupId) + "/" + accountName)
                .keyOnlyQueryParam("password")
                .put(Entity.json(request));
    }

    public void instanceGroupAccountModifyPasswordRaft(String groupId, String accountName, String password, String encryptedPassword) {
        AccountUpdatePasswordRequest request = new AccountUpdatePasswordRequest();
        request.setPassword(password);
        request.setEncryptedPassword(encryptedPassword);
        createRdsRequest()
                .path(this.groupAccountBase(groupId) + "/" + accountName)
                .queryParam("update", "password")
                .put(Entity.json(request));
    }

    public void instanceGroupAccountModifyPrivilege(String groupId,
                                       String accountName,
                                       Collection<Account.DatabasePrivilege> databasePrivileges) {
        AccountUpdatePrivilegesRequest request = new AccountUpdatePrivilegesRequest();
        request.setDatabasePrivileges(databasePrivileges);
        createRdsRequest()
                .path(this.groupAccountBase(groupId) + "/" + accountName)
                .keyOnlyQueryParam("privileges")
                .put(Entity.json(request));
    }

    public void instanceGroupAccountModifyRemark(String groupId, String accountName, String remark) {
        AccountUpdateRemarkRequest request = new AccountUpdateRemarkRequest();
        request.setRemark(remark);
        createRdsRequest()
                .path(this.groupAccountBase(groupId) + "/" + accountName)
                .keyOnlyQueryParam("remark")
                .put(Entity.json(request));
    }

    // raft版使用
    public void instanceGroupAccountModifyRemarkRaft(String groupId, String accountName, String remark) {
        AccountUpdateRemarkRequest request = new AccountUpdateRemarkRequest();
        request.setRemark(remark);
        createRdsRequest()
                .path(this.groupAccountBase(groupId) + "/" + accountName)
                .queryParam("update", "remark")
                .put(Entity.json(request));
    }

    public void instanceGroupAccountDelete(String groupId, String accountName) {
        createRdsRequest()
                .path(this.groupAccountBase(groupId) + "/" + accountName)
                .delete();
    }

    /*----------------------------database -----------------------*/
    public CheckExistResponse instanceGroupDatabaseCheck(String groupId, String dbName) {
        DataBaseCheckRequest request = new DataBaseCheckRequest();
        request.setDbName(dbName);
        return createRdsRequest()
                .path(this.groupBase + "/" + groupId + "/database/checkname")
                .post(Entity.json(request), CheckExistResponse.class);
    }

    public CheckExistResponse instanceGroupDataBaseCheckRaft(String groupId, String dbName) {
        CheckExistResponse response = new CheckExistResponse();
        BceInternalResponse internalResponse = createRdsRequest()
                .path(this.groupBase + "/" + groupId + "/database/" + dbName)
                .headWithResponse();
        if (internalResponse.getStatus() == 200) { // 200表示名字存在，404表示不存在
            response.setIsExist(1);
        }
        return response;
    }

    public DatabaseListResponse instanceGroupDatabaseList(String groupId) {
        return createRdsRequest()
                .path(this.groupDatabaseBase(groupId))
                .get(DatabaseListResponse.class);
    }

    public DatabaseGetResponse instanceGroupDatabaseDescribe(String groupId, String dbName) {
        return createRdsRequest()
                .path(this.groupDatabaseBase(groupId) + "/" + dbName)
                .get(DatabaseGetResponse.class);
    }

    public void instanceGroupDatabaseDelete(String groupId, String dbName) {
        createRdsRequest()
                .path(this.groupDatabaseBase(groupId) + "/" + dbName)
                .delete();
    }

    public void instanceGroupDatabaseCreate(String groupId, Database request) {
        createRdsRequest()
                .path(this.groupDatabaseBase(groupId))
                .post(Entity.json(request));
    }

    public void instanceGroupDatabaseModifyRemark(String groupId, String dbName, DatabaseUpdateRemarkRequest request) {
        createRdsRequest()
                .path(this.groupDatabaseBase(groupId) + "/" + dbName)
                .keyOnlyQueryParam("remark")
                .put(Entity.json(request));
    }

    public String groupBaseUrlWithId(String groupId) {
        return this.groupBase + "/" + groupId;
    }


    public void recoveryToSourceInstanceByTime(String instanceId, RecoveryToSourceInstanceRequest request) {

        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .queryParam("tableRestore", "datetime")
                .put(Entity.json(request));
    }

    public void recoveryToSourceInstanceBySnapshot(String instanceId, RecoveryToSourceInstanceRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .queryParam("tableRestore", "snapshot")
                .put(Entity.json(request));
    }

    public DatabaseChecksizeResponse checksize(String instanceId) {

        Map body = new HashMap();
        body.put("appId", instanceId);

        return createRdsRequest()
                .path(this.databaseBase(instanceId) + "/checksize")
                .post(Entity.json(body), DatabaseChecksizeResponse.class);

    }

    public DatabaseListV2Response listdatabases(String instanceId) {

        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/listdatabases")
                .get(DatabaseListV2Response.class);
    }

    public TableListResponse listtables(String instanceId,
                                           String pattern, String dbName) {

        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/listtables")
                .queryParam("pattern", pattern)
                .queryParam("dbName", dbName)
                .get(TableListResponse.class);
    }

    public DatabaseCheckExistResponse checkdbexist(String instanceId, DatabaseCheckExistRequest request) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/checkdbexist")
                .post(Entity.json(request), DatabaseCheckExistResponse.class);
    }

    /*--------------------------------smartDba-----------------------*/

    public DiskInfoResponse instanceDisk(String instanceId) {
        return createRdsRequest()
                .path(smartDbaBase + "/disk/list" + "/" + instanceId)
                .get(DiskInfoResponse.class);
    }

    public SmartDbaInstanceResponse instance(String instanceId) {
        return createRdsRequest()
                .path(smartDbaBase + "/instance" + "/" + instanceId)
                .get(SmartDbaInstanceResponse.class);
    }

    public SmartDbaResultResponse getDbList(String instanceId, Integer listNo) {
        return createRdsRequest()
                .path(smartDbaBase + "/db/list" + "/" + instanceId)
                .queryParam("listNo", listNo)
                .get(SmartDbaResultResponse.class);
    }


    public SmartDbaPageResponse getSmartDbaPage(String instanceId, SmartDbaPageRequest smartDbaPageRequest) {
        BceInternalRequest request = createRdsRequest().path(smartDbaBase + "/tb/list" + "/" + instanceId);
        if (smartDbaPageRequest.getDbName() != null) {
            request.queryParam("dbName", smartDbaPageRequest.getDbName());
        }
        if (smartDbaPageRequest.getOrderBy() != null) {
            request.queryParam("orderBy", smartDbaPageRequest.getOrderBy());
        }
        if (smartDbaPageRequest.getSort() != null) {
            request.queryParam("sort", smartDbaPageRequest.getSort());
        }
        if (smartDbaPageRequest.getPageNo() != null) {
            request.queryParam("pageNo", smartDbaPageRequest.getPageNo());
        }
        if (smartDbaPageRequest.getPageSize() != null) {
            request.queryParam("pageSize", smartDbaPageRequest.getPageSize());
        }
        if (StringUtils.isNotEmpty(smartDbaPageRequest.getSearchKey())) {
            request.queryParam("searchKey", smartDbaPageRequest.getSearchKey());
        }
        return request.get(SmartDbaPageResponse.class);
    }

    public SmartDbaInstanceResponse getConnList(String instanceId) {
        return createRdsRequest()
                .path(smartDbaBase + "/conn/list" + "/" + instanceId)
                .get(SmartDbaInstanceResponse.class);
    }


    public SmartDbaTopoResponse getTopoList(String instanceId) {
        return createRdsRequest()
                .path(smartDbaBase + "/topo/list" + "/" + instanceId)
                .get(SmartDbaTopoResponse.class);
    }

    public SmartDbaInstanceResponse getConnStati(String instanceId) {
        return createRdsRequest()
                .path(smartDbaBase + "/conn/stati" + "/" + instanceId)
                .get(SmartDbaInstanceResponse.class);
    }

    /*----------------------------ParaTemplate  -----------------------*/

    public JSONObject listParaTemplate(Integer pageNo, Integer pageSize, String dbType, String dbVersion, String type,
                                       String tplName, String showId) {
        BceInternalRequest request = createRdsRequest()
                .path(this.paraTemplateBase);
        if (pageNo != null) {
            request.queryParam("page", pageNo);
        }
        if (pageSize != null ) {
            request.queryParam("perpage", pageSize);
        }
        if (dbType != null) {
            request.queryParam("db_type", dbType);
        }
        if (dbVersion != null ) {
            request.queryParam("db_version", dbVersion);
        }
        if (type != null) {
            request.queryParam("type", type);
        }
        if (tplName != null) {
            request.queryParam("tpl_name", tplName);
        }
        if (showId != null) {
            request.queryParam("id_show", showId);
        }
        return request.get(JSONObject.class);
    }

    public JSONObject listParaTemplateApplyHistory(String templateId, Integer pageNo, Integer pageSize) {
        BceInternalRequest request = createRdsRequest()
                .path(this.paraTemplateBase + "/apply" + "/" + templateId);
        if (pageNo != null) {
            request.queryParam("page", pageNo);
        }
        if (pageSize != null ) {
            request.queryParam("perpage", pageSize);
        }
        return request.get(JSONObject.class);
    }

    public JSONObject getParaTemplateApplyDetail(String applyId) {
        return createRdsRequest()
                .path(this.paraTemplateBase + "/apply-detail" + "/" + applyId)
                .get(JSONObject.class);
    }

    public JSONObject getParaTemplateDetail(String templateId) {
        return createRdsRequest()
                .path(this.paraTemplateBase + "/" + templateId)
                .get(JSONObject.class);
    }


    public JSONObject addParaTemplate(Map<String, Object> map) {
        return createRdsRequest()
                .path(this.paraTemplateBase)
                .post(Entity.json(map), JSONObject.class);
    }

    public void deleteTemplate(String templateId) {
        createRdsRequest()
                .path(this.paraTemplateBase + "/" + templateId)
                .delete();
    }

    public void updateTemplate(String templateId, JSONObject jsonObject) {
        createRdsRequest()
                .path(this.paraTemplateBase + "/" + templateId)
                .put(Entity.json(jsonObject));
    }

    public void applyTemplate(String templateId, String instanceId, String effectiveTime, boolean switchover) {
        ApplyTemplateRequest request = new ApplyTemplateRequest();
        if (StringUtils.isNotEmpty(instanceId)) {
            request.setInstanceId(instanceId);
        }
        if (StringUtils.isNotEmpty(effectiveTime)) {
            request.setEffectiveTime(effectiveTime);
        }
        request.setSwitchover(switchover);
        createRdsRequest()
                .path(this.paraTemplateBase + "/apply" + "/" + templateId)
                .post(Entity.json(request));
    }

    public List<LinkedHashMap> listDatebaseParameters(String dbType, String dbVersion, String templateId) {
        BceInternalRequest request = createRdsRequest()
                .path(this.instanceBase + "/" + dbType + "/" + dbVersion + "/parameter");
        if (templateId != null) {
            request.queryParam("tpl_id", templateId);
        }
        return request.get(List.class);
    }

    public InstanceListResponse listInstanceResource(String userIds) {
        Map<String, String> body = new HashMap<>();
        body.put("userIds", userIds);
        BceInternalRequest request = createRdsServiceRequest()
                .path("/instance/resources");
        return request.post(Entity.json(body), InstanceListResponse.class);
    }
    /*----------------------------RoGroup  -----------------------*/

    public CreateRoGroupResponse roGroupCreate(CreateRoGroupRequest request) {
        return createRdsRequest()
                .path(this.roGroupBase)
                .post(Entity.json(request), CreateRoGroupResponse.class);
    }

    public RoGroupDetailResponse roGroupDetail(String sourceAppId, String roGroupId) {
        return createRdsRequest()
                .path(this.roGroupBase + "/show"
                        + "/" + sourceAppId + "/" + roGroupId)
                .get(RoGroupDetailResponse.class);
    }

    public RoGroupListResponse roGroupList(String instanceId) {
        return createRdsRequest()
                .path(this.roGroupBase + "/list"
                        + "/" + instanceId)
                .get(RoGroupListResponse.class);
    }

    public void deleteRoGroup(String instanceId, String roGroupId) {
        createRdsRequest()
                .path(this.roGroupBase + "/" + instanceId + "/" + roGroupId)
                .delete();
    }

    public void roGroupUpdateName(String sourceAppId, String roGroupId, RoGroupUpdateRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "updateName")
                .put(Entity.json(request));
    }

    public void roGroupUpdateEndpoint(String sourceAppId, String roGroupId, RoGroupUpdateRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "updateEndpoint")
                .put(Entity.json(request));
    }

    public void roGroupUpdatePubliclyAccessible(String sourceAppId, String roGroupId
            , RoGroupUpdatePubliclyAccessibleRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "updatePubliclyAccessible")
                .put(Entity.json(request));
    }

    public void roGroupUpdateRoGroupProperty(String sourceAppId, String roGroupId
            , UpdateRoGroupPropertyRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "updateRoGroupProperty")
                .put(Entity.json(request));
    }


    public void roGroupUpdateEnableDelayOff(String sourceAppId, String roGroupId
            , RoGroupUpdateEnableDelayOffRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "updateEnableDelayOff")
                .put(Entity.json(request));
    }

    public void roGroupUpdateIsBalanceReload(String sourceAppId, String roGroupId
            , RoGroupUpdateIsBalanceReloadRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "updateIsBalanceReload")
                .put(Entity.json(request));
    }

    public void roGroupUpdateLeastAppAmount(String sourceAppId, String roGroupId, RoGroupUpdateRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "updateLeastAppAmount")
                .put(Entity.json(request));
    }

    public void roGroupUpdateDelayThreshold(String sourceAppId, String roGroupId, RoGroupUpdateRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "updateDelayThreshold")
                .put(Entity.json(request));
    }

    public void roGroupUpdateReload(String sourceAppId, String roGroupId) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "reload")
                .put();
    }


    public void roGroupUpdateWeight(String sourceAppId, String roGroupId, RoGroupUpdateWeightRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "updateWeight")
                .put(Entity.json(request));
    }

    public void roGroupUpdateJoin(String sourceAppId, String roGroupId, RoGroupUpdateWeightRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "join")
                .put(Entity.json(request));
    }

    public void roGroupLeave(String sourceAppId, String roGroupId, RoGroupLeaveRequest request) {
        this.createRdsRequest()
                .path(this.roGroupBase + "/" + sourceAppId + "/" + roGroupId)
                .queryParam("action", "leave")
                .put(Entity.json(request));
    }

    /**
     * 杀死指定指定会话session ID，该接口为异步接口，会立即下发到MySQL上返回，需要通过processlist接口check是否执行完成
     * @param instanceId
     * @param request
     */
    public void killProcess(String instanceId, KillProcessRequest request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/information/processkill")
                .post(Entity.json(request));
    }

    /**
     * 获取指定实例的事务快照列表
     * @param instanceId
     * @return
     */
    public TransactionListResponse transactionList(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/information/innodbtrxlist")
                .get(TransactionListResponse.class);
    }

    /**
     * 获取指定实例的connnectlist快照
     * netstat -tn | grep `hostname -i`:3306
     * @param instanceId
     * @return
     */
    public ConnectionListResponse connectionList(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/information/connectlist")
                .get(ConnectionListResponse.class);
    }

    public void bindShortId(String instanceLongId, String instanceShortId) {

        Map<String, String> body = new HashMap<String, String>();
        body.put("shortId", instanceShortId);

        createRdsRequest()
                .path(this.instanceBase + "/" + instanceLongId)
                .keyOnlyQueryParam("shortId")
                .put(Entity.json(body));
    }
    /*---------------------------- SQL限流  -----------------------*/
    public SqlFilterList sqlFilterList(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/sqlfilter")
                .get(SqlFilterList.class);
    }

    public SqlFilterResponse sqlFilterDetail(String instanceId, String sqlFilterId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/sqlfilter/" + sqlFilterId)
                .get(SqlFilterResponse.class);
    }

    public void addSqlFilter(String instanceId, SqlFilterRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/sqlfilter")
                .post(Entity.json(request));
    }

    public void updateSqlFilter(String instanceId, String sqlFilterId, SqlFilterRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/sqlfilter/" + sqlFilterId)
                .put(Entity.json(request));
    }

    public void actionSqlFilter(String instanceId, String sqlFilterId, SqlFilterRequest request) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/sqlfilter/" + sqlFilterId)
                .post(Entity.json(request));
    }

    public void deleteSqlFilter(String instanceId, String sqlFilterId) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/sqlfilter/" + sqlFilterId)
                .delete();
    }

    public SqlFilterAllowedResponse allowedSqlFilter(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/sqlfilter/allowed")
                .get(SqlFilterAllowedResponse.class);
    }

    /*------------------------------maintaintime------------------------------------*/

    public void updateMaintaintime(String instanceId, MaintainDurationRequest request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("maintaintime")
                .put(Entity.json(request));
    }

    public MaintaintimeTasks getMaintaintimeTasks(TaskRequest taskRequest) {
        BceInternalRequest request = createRdsRequest().path(this.baseURL + "/maintaintime/task");
        if (StringUtils.isNotEmpty(taskRequest.getPageNo())) {
            request.queryParam("pageNo", taskRequest.getPageNo());
        }
        if (StringUtils.isNotEmpty(taskRequest.getPageSize())) {
            request.queryParam("pageSize", taskRequest.getPageSize());
        }
        if (StringUtils.isNotEmpty(taskRequest.getAppId())) {
            request.queryParam("appId", taskRequest.getAppId());
        }
        if (StringUtils.isNotEmpty(taskRequest.getAppName())) {
            request.queryParam("appName", taskRequest.getAppName());
        }
        if (StringUtils.isNotEmpty(taskRequest.getTaskId())) {
            request.queryParam("taskId", taskRequest.getTaskId());
        }
        if (StringUtils.isNotEmpty(taskRequest.getStartTime())) {
            request.queryParam("startTime", taskRequest.getStartTime());
        }
        if (StringUtils.isNotEmpty(taskRequest.getEndTime())) {
            request.queryParam("endTime", taskRequest.getEndTime());
        }
        if (StringUtils.isNotEmpty(taskRequest.getTaskType())) {
            request.queryParam("taskType", taskRequest.getTaskType());
        }
        if (StringUtils.isNotEmpty(taskRequest.getTaskStatus())) {
            request.queryParam("taskStatus", taskRequest.getTaskStatus());
        }
        // 后端接收参数为appId, 将instanceId转为appId
        if (StringUtils.isNotEmpty(taskRequest.getInstanceId())) {
            request.queryParam("appId", taskRequest.getInstanceId());
        }

        return request.get(MaintaintimeTasks.class);
    }

    public void cancelMaintaintimeTask(String taskId) {
        this.createRdsRequest()
                .path(this.baseURL + "/maintaintime/task/" + taskId + "/cancel")
                .put();
    }

    public void suspend(String instanceId) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("suspend")
                .put();
    }

    public void start(String instanceId) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("start")
                .put();
    }

    public ExchangeStrategyResponse detail(String instanceId) {
        return this.createRdsRequest()
                .path(this.baseURL + "/haStrategy/" + instanceId)
                .getWitResponse()
                .getEntity(ExchangeStrategyResponse.class);
    }

    public void updateExchangeStrategy(String instanceId, UpdateExchangeStrategyRequest request) {
        this.createRdsRequest()
                .path(this.baseURL + "/haStrategy/" + instanceId)
                .post(Entity.json(request));
    }



    public void stopSingleInstance(String instanceId) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("recycle")
                .delete();


    }

    public void stopPrepaySingleInstance(String instanceId) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .queryParam("recycle", "recycle")
                .queryParam("updateStatus", "updateStatus")
                .delete();


    }

    public void stopBatchInstances(String instanceIds) {
        this.createRdsRequest()
                .path(this.instanceBase + "/batchdelete")
                .keyOnlyQueryParam("recycle")
                .queryParam("instanceId", instanceIds)
                .delete();
    }

    public void rebootInstance(String instanceId) {

        this.createRdsRequest()
                .path(this.recycleBase + "/instance/" + instanceId)
                .put();

    }

    public void deleteIsolatedInstance(String instanceId) {
        this.createRdsRequest()
                .path(this.recycleBase + "/instance/" + instanceId)
                .delete();
    }

    public void deleteNonMySQLIsolatedInstance(String instanceId) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .delete();
    }

//    public InstanceListResponse getRecyclerInstances() {
//        return this.createRdsRequest()
//                .path(this.instanceBase)
//                .keyOnlyQueryParam("recycle")
//                .getWitResponse()
//                .getEntity(InstanceListResponse.class);
//    }

    public void batchjoin(BatchJoinRequest request) {
        this.createRdsRequest()
                .path(this.groupBase + "/batchjoin")
                .post(Entity.json(request));
    }

    public RdsMinorVersionList getVersionList(String instanceId) {
        return this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/upgradeMinorVersionList")
                .getWitResponse()
                .getEntity(RdsMinorVersionList.class);

    }

    public void updateVersion(String instanceId, UpdateVersionRequest request) {

        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("upgradeMinorVersion")
                .put(Entity.json(request));
    }

    public void preCheckOfGroupVersion(PreCheckVersionRequest request) {
        this.createRdsRequest()
                .path(this.groupBase + "/check-version")
                .post(Entity.json(request));
    }

    public SwitchPrecheckResponse strategyService(String instanceId) {
        return this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("switchoverPrecheck")
                .put(SwitchPrecheckResponse.class);
    }

    public TaskStatusResponses taskStatus(Integer taskId) {
        return this.createRdsRequest()
                .path(this.baseURL + "/precheck/task")
                .queryParam("taskId", taskId)
                .getWitResponse()
                .getEntity(TaskStatusResponses.class);
    }

    public void operateDeploymentPoint(String instanceId, DeploymentPointRequest request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("blbService")
                .put(Entity.json(request));
    }

    public FailinjectRequest getFailinjectWhitelist() {
        return this.createRdsRequest()
                .path(this.baseURL + "/failinject/whitelist")
                .get(FailinjectRequest.class);
    }

    public void putFailinjectWhitelist(FailinjectRequest request) {
        this.createRdsRequest()
                .path(this.baseURL + "/failinject/whitelist")
                .queryParam("action", "add")
                .put(Entity.json(request));
    }

    public void removeFailinjectWhitelist(FailinjectRequest request) {
        this.createRdsRequest()
                .path(this.baseURL + "/failinject/whitelist")
                .queryParam("action", "remove")
                .put(Entity.json(request));
    }

    public FailinjectResponse postFailinject(String appId) {
        return this.createRdsRequest()
                .path(this.baseURL + "/failinject/" + appId)
                .post(FailinjectResponse.class);
    }

    public void duplicateTemplate(String templateId, DuplicateTempRequest request) {
        this.createRdsRequest()
                .path(this.paraTemplateBase + "/" + templateId)
                .post(Entity.json(request));

    }

    public void updateMaintaintimeTasks(String taskId, UpdateTaskRequest request) {
        this.createRdsRequest()
                .path(this.baseURL + "/maintaintime/task/" + taskId + "/update")
                .put(Entity.json(request));

    }

    public GlobalInstanceResponses overviewList() {
        return this.createRdsRequest()
                .path(this.instanceBase + "/global")
                .get(GlobalInstanceResponses.class);
    }

    public void upgradeMajorVerison(String instanceId, UpgradeMajorVersion request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("upgradeMajorVersion")
                .put(Entity.json(request));
    }

    public BnsListResponse accountBnsList(String instanceId, String accountName) {
        return this.createRdsRequest()
                .path(this.baseURL + "/rdsproxy/accountBnsList")
                .queryParam("rdsproxyId", instanceId)
                .queryParam("accountName", accountName)
                .get(BnsListResponse.class);
    }

    public UpdateBnsResponse accountBns(UpdateBnsRequest request) {
        return this.createRdsRequest()
                .path(this.baseURL + "/rdsproxy/accountBnsUpdate")
                .put(Entity.json(request), UpdateBnsResponse.class);
    }

    public ProxyTopoInfo proxyTopoInfo(String instanceId) {
        BceInternalRequest request = this.createRdsRequest()
                .path(this.baseURL + "/rdsproxy/topology");
        return request.queryParam("rdsproxyId", instanceId).get(ProxyTopoInfo.class);
    }

    public FailinjectResponse updateProxyAccountIp(UpdateProxyIpMsg requestBody) {
        BceInternalRequest request = this.createRdsRequest()
                .path(this.baseURL + "/rdsproxy/accountIpUpdate");
        return request.put(Entity.json(requestBody), FailinjectResponse.class);
    }

    public UpdateProxyIpMsg getProxyAccountIp(String instanceId, String accountName) {
        BceInternalRequest request = this.createRdsRequest()
                .path(this.baseURL + "/rdsproxy/accountIpList");
        if (StringUtils.isNotEmpty(instanceId)) {
            request.queryParam("rdsproxyId", instanceId);
        }
        if (StringUtils.isNotEmpty(accountName)) {
            request.queryParam("accountName", accountName);
        }
        return request.get(UpdateProxyIpMsg.class);
    }
}
