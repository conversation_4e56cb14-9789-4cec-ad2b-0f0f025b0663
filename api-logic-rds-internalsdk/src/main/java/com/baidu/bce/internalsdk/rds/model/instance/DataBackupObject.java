package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataBackupObject {

    private String type;
    private String name;
    private List<SubTableObject> subObjects;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<SubTableObject> getSubObjects() {
        return subObjects;
    }

    public void setSubObjects(List<SubTableObject> subObjects) {
        this.subObjects = subObjects;
    }


}
