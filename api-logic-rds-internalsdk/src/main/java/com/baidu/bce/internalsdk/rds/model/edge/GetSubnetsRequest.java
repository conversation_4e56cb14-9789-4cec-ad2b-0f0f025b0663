package com.baidu.bce.internalsdk.rds.model.edge;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GetSubnetsRequest {

    private List<String> subnetIds;

    private List<String> subnetUuids;

    public List<String> getSubnetIds() {
        return subnetIds;
    }

    public void setSubnetIds(List<String> subnetIds) {
        this.subnetIds = subnetIds;
    }

    public List<String> getSubnetUuids() {
        return subnetUuids;
    }

    public void setSubnetUuids(List<String> subnetUuids) {
        this.subnetUuids = subnetUuids;
    }
}
