package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.Valid;
import java.util.Date;

/**
 * Created by luping on 2017/4/14.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SnapshotListWithTimeResponse extends SnapshotListResponse {
    @Valid
    private Period period;

    @Override
    public String toString() {
        return "SnapshotListWithTimeResponse{"
                + "snapshots=" + super.toString()
                + ",period=" + period
                + '}';
    }

    public static class Period {
        private Date begin;
        private Date end;

        @Override
        public String toString() {
            return "Period{"
                    + "begin='" + begin + '\''
                    + "end='" + end + '\''
                    + "}";
        }

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        public Date getBegin() {
            return begin;
        }

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        public void setBegin(Date begin) {
            this.begin = begin;
        }

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        public Date getEnd() {
            return end;
        }

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        public void setEnd(Date end) {
            this.end = end;
        }

        public Period withEnd(Date end) {
            this.end = end;
            return this;
        }

        public Period withBegin(Date begin) {
            this.begin = begin;
            return this;
        }
    }

    public Period getPeriod() {
        return period;
    }

    public void setPeriod(Period period) {
        this.period = period;
    }
}
