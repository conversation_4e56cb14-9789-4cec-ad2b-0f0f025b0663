package com.baidu.bce.internalsdk.rds.model.rogroup;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.SimpleVpcVo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RoGroupDetailResponse {
    private String roGroupId;
    private String status;
    private String roGroupName;
    private Dict endpoint;
    private String sourceAppId;
    private String instanceShortId;
    private Integer appAmount;
    private List<AppList> appList;
    private String vpcId;
    private String subnetId;
    private boolean enableDelayOff;
    private Integer delayThreshold;
    private Integer leastAppAmount;
    private boolean balanceReload;
    private boolean bgwGroupExclusive;
    private String bgwGroupId;
    private SimpleVpcVo vpcVo;
    private SubnetVo subnetVo;
    private String region;

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getRoGroupId() {
        return roGroupId;
    }

    public void setRoGroupId(String roGroupId) {
        this.roGroupId = roGroupId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRoGroupName() {
        return roGroupName;
    }

    public void setRoGroupName(String roGroupName) {
        this.roGroupName = roGroupName;
    }

    public Dict getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(Dict endpoint) {
        this.endpoint = endpoint;
    }

    public String getSourceAppId() {
        return sourceAppId;
    }

    public void setSourceAppId(String sourceAppId) {
        this.sourceAppId = sourceAppId;
    }

    public Integer getAppAmount() {
        return appAmount;
    }

    public void setAppAmount(Integer appAmount) {
        this.appAmount = appAmount;
    }

    public List<AppList> getAppList() {
        return appList;
    }

    public void setAppList(List<AppList> appList) {
        this.appList = appList;
    }

    public String getVpcId() {
        return vpcId;
    }

    public void setVpcId(String vpcId) {
        this.vpcId = vpcId;
    }

    public String getSubnetId() {
        return subnetId;
    }

    public void setSubnetId(String subnetId) {
        this.subnetId = subnetId;
    }

    public boolean isEnableDelayOff() {
        return enableDelayOff;
    }

    public void setEnableDelayOff(boolean enableDelayOff) {
        this.enableDelayOff = enableDelayOff;
    }

    public Integer getDelayThreshold() {
        return delayThreshold;
    }

    public void setDelayThreshold(Integer delayThreshold) {
        this.delayThreshold = delayThreshold;
    }

    public Integer getLeastAppAmount() {
        return leastAppAmount;
    }

    public void setLeastAppAmount(Integer leastAppAmount) {
        this.leastAppAmount = leastAppAmount;
    }

    public boolean isBalanceReload() {
        return balanceReload;
    }

    public void setBalanceReload(boolean balanceReload) {
        this.balanceReload = balanceReload;
    }

    public boolean isBgwGroupExclusive() {
        return bgwGroupExclusive;
    }

    public void setBgwGroupExclusive(boolean bgwGroupExclusive) {
        this.bgwGroupExclusive = bgwGroupExclusive;
    }

    public String getBgwGroupId() {
        return bgwGroupId;
    }

    public void setBgwGroupId(String bgwGroupId) {
        this.bgwGroupId = bgwGroupId;
    }

    public SimpleVpcVo getVpcVo() {
        return vpcVo;
    }

    public void setVpcVo(SimpleVpcVo vpcVo) {
        this.vpcVo = vpcVo;
    }

    public SubnetVo getSubnetVo() {
        return subnetVo;
    }

    public void setSubnetVo(SubnetVo subnetVo) {
        this.subnetVo = subnetVo;
    }

    public String getInstanceShortId() {
        return instanceShortId;
    }

    public void setInstanceShortId(String instanceShortId) {
        this.instanceShortId = instanceShortId;
    }
}
