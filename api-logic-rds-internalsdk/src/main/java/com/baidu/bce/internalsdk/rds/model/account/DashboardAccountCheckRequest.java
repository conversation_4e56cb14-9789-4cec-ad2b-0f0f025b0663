package com.baidu.bce.internalsdk.rds.model.account;

import javax.validation.constraints.NotNull;

/**
 * Created by luping03 on 17/6/13.
 */
public class DashboardAccountCheckRequest extends InstanceIdRequest {
    @NotNull
    private String accountName;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DashboardAccountCheckRequest{");
        sb.append("accountName=").append(accountName);
        sb.append('}');
        return sb.toString();
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
}
