package com.baidu.bce.internalsdk.rds.model.bcc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BccInstanceDetail {

    private BccInstanceModel instance;

    public BccInstanceModel getInstance() {
        return instance;
    }

    public void setInstance(BccInstanceModel instance) {
        this.instance = instance;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BccInstanceModel {
        private String id;
        private String name;
        private String instanceType;
        private String status;
        private String internalIp;
        private String publicIp;
        private String subnetId;
        private String vpcId;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getInstanceType() {
            return instanceType;
        }

        public void setInstanceType(String instanceType) {
            this.instanceType = instanceType;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getInternalIp() {
            return internalIp;
        }

        public void setInternalIp(String internalIp) {
            this.internalIp = internalIp;
        }

        public String getPublicIp() {
            return publicIp;
        }

        public void setPublicIp(String publicIp) {
            this.publicIp = publicIp;
        }

        public String getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(String subnetId) {
            this.subnetId = subnetId;
        }

        public String getVpcId() {
            return vpcId;
        }

        public void setVpcId(String vpcId) {
            this.vpcId = vpcId;
        }
    }
}
