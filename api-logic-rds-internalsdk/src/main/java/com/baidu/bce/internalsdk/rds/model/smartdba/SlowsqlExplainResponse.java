package com.baidu.bce.internalsdk.rds.model.smartdba;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SlowsqlExplainResponse {
        private Long explainId;
        private String extra;
        private Double filtered;
        private String key;
        private String keyLen;
        private String partitions;
        private String possibleKeys;
        private String ref;
        private Long rows;
        private String selectType;
        private String table;
        private String type;

        public Long getExplainId() {
            return explainId;
        }

        public void setExplainId(Long explainId) {
            this.explainId = explainId;
        }

        public Double getFiltered() {
            return filtered;
        }

        public void setFiltered(Double filtered) {
            this.filtered = filtered;
        }

        public Long getRows() {
            return rows;
        }

        public void setRows(Long rows) {
            this.rows = rows;
        }

        public String getExtra() {
            return extra;
        }

        public void setExtra(String extra) {
            this.extra = extra;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getKeyLen() {
            return keyLen;
        }

        public void setKeyLen(String keyLen) {
            this.keyLen = keyLen;
        }

        public String getPartitions() {
            return partitions;
        }

        public void setPartitions(String partitions) {
            this.partitions = partitions;
        }

        public String getPossibleKeys() {
            return possibleKeys;
        }

        public void setPossibleKeys(String possibleKeys) {
            this.possibleKeys = possibleKeys;
        }

        public String getRef() {
            return ref;
        }

        public void setRef(String ref) {
            this.ref = ref;
        }

        public String getSelectType() {
            return selectType;
        }

        public void setSelectType(String selectType) {
            this.selectType = selectType;
        }

        public String getTable() {
            return table;
        }

        public void setTable(String table) {
            this.table = table;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
}
