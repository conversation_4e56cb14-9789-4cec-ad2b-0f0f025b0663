package com.baidu.bce.internalsdk.rds.model.account;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DashboardAccountUpdateDescRequest {
    @NotNull
    @IdPermission
    private String instanceId;

    @NotNull
    private String accountName;

    @NotNull
    private String remark;


    /*-----------------------auto generated--------------------------------*/
    @Override
    public String toString() {
        return "DashboardAccountUpdateDescRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", accountName='" + accountName + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }


    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
