package com.baidu.bce.internalsdk.rds.model.account;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TableLevelGlobalScopeResponse {
    private TableLevelSubAllScopeResponse all;
    private List<String> readOnly;
    private List<String> readWrite;

    public TableLevelSubAllScopeResponse getAll() {
        return all;
    }

    public void setAll(TableLevelSubAllScopeResponse all) {
        this.all = all;
    }

    public List<String> getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(List<String> readOnly) {
        this.readOnly = readOnly;
    }

    public List<String> getReadWrite() {
        return readWrite;
    }

    public void setReadWrite(List<String> readWrite) {
        this.readWrite = readWrite;
    }

    public class TableLevelSubAllScopeResponse {
        private List<String> databaseTable;
        private List<String> viewProcedure;
        private List<String> replication;
        private List<String> administration;

        public List<String> getDatabaseTable() {
            return databaseTable;
        }

        public void setDatabaseTable(List<String> databaseTable) {
            this.databaseTable = databaseTable;
        }

        public List<String> getViewProcedure() {
            return viewProcedure;
        }

        public void setViewProcedure(List<String> viewProcedure) {
            this.viewProcedure = viewProcedure;
        }

        public List<String> getReplication() {
            return replication;
        }

        public void setReplication(List<String> replication) {
            this.replication = replication;
        }

        public List<String> getAdministration() {
            return administration;
        }

        public void setAdministration(List<String> administration) {
            this.administration = administration;
        }
    }


}
