package com.baidu.bce.internalsdk.rds.model;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateEncryptPolicyReq {

    @IdPermission
    private String instanceId;
    private UpdateEncryptPolicyRequest backupPolicy;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public UpdateEncryptPolicyRequest getBackupPolicy() {
        return backupPolicy;
    }

    public void setBackupPolicy(UpdateEncryptPolicyRequest backupPolicy) {
        this.backupPolicy = backupPolicy;
    }
}
