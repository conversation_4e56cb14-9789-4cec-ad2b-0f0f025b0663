package com.baidu.bce.internalsdk.rds.model.migration;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/10/27.
 */
public class MigrationTaskCreateResponse {
    private Boolean success;
    private List<ItemResult> checkList;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("MigrationTaskCreateResponse{");
        sb.append("success=").append(success);
        sb.append(", checkList=").append(checkList);
        sb.append('}');
        return sb.toString();
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public List<ItemResult> getCheckList() {
        return checkList;
    }

    public void setCheckList(List<ItemResult> checkList) {
        this.checkList = checkList;
    }

    public MigrationTaskCreateResponse withSuccess(final Boolean success) {
        this.success = success;
        return this;
    }

    public MigrationTaskCreateResponse withCheckList(final List<ItemResult> checkList) {
        this.checkList = checkList;
        return this;
    }

    public static class ItemResult {
        private String checkItem;
        private String checkMethod;
        private Boolean result;
        private String repairMethod;
    }

}
