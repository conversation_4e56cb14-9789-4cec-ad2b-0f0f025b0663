package com.baidu.bce.internalsdk.rds.model.instance;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.RDSRegexp;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/19.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DashboardInstanceModifyNameRequest {
    @IdPermission
    private String instanceId;

    @Pattern(regexp = RDSRegexp.INSTANCE_NAME)
    private String instanceName;

    @Override
    public String toString() {
        return "DashboardInstanceModifyNameRequest{"
                + "instanceId='" + instanceId + '\''
                + ", instanceName='" + instanceName + '\''
                + '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

}
