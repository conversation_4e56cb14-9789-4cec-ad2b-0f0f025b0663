package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.externalsdk.logical.network.common.utils.InternalClientUtil;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.rds.model.ViewPageResponse;
import endpoint.EndpointManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

public class RDSLogicClient extends BceClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(RDSLogicClient.class);

    private static final String SERVICE_NAME = "RDS_Logic";
    @Deprecated
    private String token;

    public RDSLogicClient(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    public RDSLogicClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public RDSLogicClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public BceInternalRequest createRdsRequest() {
        return super.createAuthorizedRequest();
    }

    public BceInternalRequest createInternalRequest() {
        return createAuthorizedRequestWithSignedHeaders(Arrays.asList(BceConstant.HOST,
                BceConstant.X_BCE_DATE)).token(token);
    }

    public BceInternalRequest createAdvacedRequest() {
        return InternalClientUtil.createAdvancedInternalRequest(endpoint,
                accessKey, secretKey, token, 100 * 1000, 1, securityToken);
    }

    public ViewPageResponse showOverviewPage() {
        return createRdsRequest()
                .path("/v1/rds/instances/showOverviewPageEachRegion")
                .get(ViewPageResponse.class);
    }

}
