package com.baidu.bce.internalsdk.rds.model.dbfirewall;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/4/21.
 */
public class ListFilter {
    private String keyword;
    private String keywordType;
    private String subKeywordType;

    public ListFilter() {
    }

    public ListFilter(String keyword, String keywordType) {
        new ListFilter(keyword, keywordType, null);
    }

    public ListFilter(String keyword, String keywordType, String subKeywordType) {
        this.keyword = keyword;
        this.keywordType = keywordType;
        this.subKeywordType = subKeywordType;
    }

    public String getSubKeywordType() {
        return subKeywordType;
    }

    public void setSubKeywordType(String subKeywordType) {
        this.subKeywordType = subKeywordType;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getKeywordType() {
        return keywordType;
    }

    public void setKeywordType(String keywordType) {
        this.keywordType = keywordType;
    }
}
