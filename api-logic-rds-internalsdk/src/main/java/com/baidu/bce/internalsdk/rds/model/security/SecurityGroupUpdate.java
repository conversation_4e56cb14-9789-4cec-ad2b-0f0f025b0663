package com.baidu.bce.internalsdk.rds.model.security;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdMapper;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class SecurityGroupUpdate {

    private String instanceUuid;

    private List<String> securityGroupUuids;

    private List<String> instanceIds;

    @NotEmpty
    private List<String> securityGroupIds;

    private String instanceType = "RDS";
    @IdMapper
    @IdPermission
    private String instanceId;

    private String subInstanceType;

    public SecurityGroupUpdate() {
    }

    public String getInstanceUuid() {
        return instanceUuid;
    }

    public void setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
    }

    public List<String> getSecurityGroupUuids() {
        return securityGroupUuids;
    }

    public void setSecurityGroupUuids(List<String> securityGroupUuids) {
        this.securityGroupUuids = securityGroupUuids;
    }

    public List<String> getSecurityGroupIds() {
        return securityGroupIds;
    }

    public void setSecurityGroupIds(List<String> securityGroupIds) {
        this.securityGroupIds = securityGroupIds;
    }

    public String getInstanceType() {
        return instanceType;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public List<String> getInstanceIds() {
        return instanceIds;
    }

    public void setInstanceIds(List<String> instanceIds) {
        this.instanceIds = instanceIds;
    }

    public String getSubInstanceType() {
        return subInstanceType;
    }

    public void setSubInstanceType(String subInstanceType) {
        this.subInstanceType = subInstanceType;
    }
}
