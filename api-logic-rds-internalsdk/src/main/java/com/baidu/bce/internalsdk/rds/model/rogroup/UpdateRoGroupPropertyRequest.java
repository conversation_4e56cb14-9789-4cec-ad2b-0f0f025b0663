package com.baidu.bce.internalsdk.rds.model.rogroup;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.util.PatternString;

import javax.validation.constraints.Pattern;
import java.util.List;

public class UpdateRoGroupPropertyRequest {
    @IdPermission
    private String sourceAppId;
    private String roGroupId;
    @Pattern(regexp = PatternString.PATTERN_INSTANCE_NAME)
    private String roGroupName;
    private boolean enableDelayOff;
    private Integer delayThreshold;
    private boolean balanceReload;
    private Integer leastAppAmount;
    private List<ReadReplica> readReplicaList;

    public String getSourceAppId() {
        return sourceAppId;
    }

    public void setSourceAppId(String sourceAppId) {
        this.sourceAppId = sourceAppId;
    }

    public String getRoGroupId() {
        return roGroupId;
    }

    public void setRoGroupId(String roGroupId) {
        this.roGroupId = roGroupId;
    }

    public String getRoGroupName() {
        return roGroupName;
    }

    public void setRoGroupName(String roGroupName) {
        this.roGroupName = roGroupName;
    }

    public boolean isEnableDelayOff() {
        return enableDelayOff;
    }

    public void setEnableDelayOff(boolean enableDelayOff) {
        this.enableDelayOff = enableDelayOff;
    }

    public Integer getDelayThreshold() {
        return delayThreshold;
    }

    public void setDelayThreshold(Integer delayThreshold) {
        this.delayThreshold = delayThreshold;
    }

    public boolean isBalanceReload() {
        return balanceReload;
    }

    public void setBalanceReload(boolean balanceReload) {
        this.balanceReload = balanceReload;
    }

    public List<ReadReplica> getReadReplicaList() {
        return readReplicaList;
    }

    public void setReadReplicaList(List<ReadReplica> readReplicaList) {
        this.readReplicaList = readReplicaList;
    }

    public Integer getLeastAppAmount() {
        return leastAppAmount;
    }

    public void setLeastAppAmount(Integer leastAppAmount) {
        this.leastAppAmount = leastAppAmount;
    }

    public static class ReadReplica{
        private String appId;
        private Integer weight;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public Integer getWeight() {
            return weight;
        }

        public void setWeight(Integer weight) {
            this.weight = weight;
        }
    }
}
