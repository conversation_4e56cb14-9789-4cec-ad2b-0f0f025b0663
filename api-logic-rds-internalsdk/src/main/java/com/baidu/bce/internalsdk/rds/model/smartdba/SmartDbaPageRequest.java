package com.baidu.bce.internalsdk.rds.model.smartdba;

public class SmartDbaPageRequest {

    private String instanceId;

    private String dbName;

    private Integer pageNo;

    private Integer pageSize;

    private String orderBy;

    private String sort;

    private String searchKey;

    public SmartDbaPageRequest() {
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }


    @Override
    public String toString() {
        return "SmartDbaPageRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", dbName='" + dbName + '\'' +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", orderBy='" + orderBy + '\'' +
                ", sort='" + sort + '\'' +
                ", searchKey='" + searchKey + '\'' +
                '}';
    }
}
