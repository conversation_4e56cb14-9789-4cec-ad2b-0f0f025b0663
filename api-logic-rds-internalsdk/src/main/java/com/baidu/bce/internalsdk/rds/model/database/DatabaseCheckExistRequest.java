package com.baidu.bce.internalsdk.rds.model.database;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
public class DatabaseCheckExistRequest {

    private String instanceId;

    private List<DatabaseDataModel> data;

    public List<DatabaseDataModel> getData() {
        return data;
    }

    public void setData(List<DatabaseDataModel> data) {
        this.data = data;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public static class DatabaseDataModel {
        private String dbName;

        private String newDbname;

        private String restoreMode;

        private List<DatabaseDataTablesModel> tables;

        public String getDbName() {
            return dbName;
        }

        public void setDbName(String dbName) {
            this.dbName = dbName;
        }

        public String getNewDbname() {
            return newDbname;
        }

        public void setNewDbname(String newDbname) {
            this.newDbname = newDbname;
        }

        public String getRestoreMode() {
            return restoreMode;
        }

        public void setRestoreMode(String restoreMode) {
            this.restoreMode = restoreMode;
        }

        public List<DatabaseDataTablesModel> getTables() {
            return tables;
        }

        public void setTables(List<DatabaseDataTablesModel> tables) {
            this.tables = tables;
        }
    }

    public static class DatabaseDataTablesModel {
        private String tableName;

        private String newTablename;

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public String getNewTablename() {
            return newTablename;
        }

        public void setNewTablename(String newTablename) {
            this.newTablename = newTablename;
        }
    }

}
