package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/18.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResultResponse<T> extends BaseResponse {
    private T result;

    @Override
    public String toString() {
        return "ResultResponse{" + "result=" + result + '}';
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public ResultResponse withResult(final T result) {
        this.result = result;
        return this;
    }
}
