package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by luping03 on 17/6/12.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderItemExtraInfo {

    @NotNull
    @Valid
    private InstanceCreateRequest instanceCreateRequest;

    private String autoRenewTimeUnit = "month";

    private int autoRenewTime = 0;

    private List<Tag> tags;

    private String resourceGroupId;

    private String env;

    public InstanceCreateRequest getInstanceCreateRequest() {
        return instanceCreateRequest;
    }

    public void setInstanceCreateRequest(InstanceCreateRequest instanceCreateRequest) {
        this.instanceCreateRequest = instanceCreateRequest;
    }

    public String getAutoRenewTimeUnit() {
        return autoRenewTimeUnit;
    }

    public void setAutoRenewTimeUnit(String autoRenewTimeUnit) {
        this.autoRenewTimeUnit = autoRenewTimeUnit;
    }

    public int getAutoRenewTime() {
        return autoRenewTime;
    }

    public void setAutoRenewTime(int autoRenewTime) {
        this.autoRenewTime = autoRenewTime;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public String getResourceGroupId() {
        return resourceGroupId;
    }

    public void setResourceGroupId(String resourceGroupId) {
        this.resourceGroupId = resourceGroupId;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }
}
