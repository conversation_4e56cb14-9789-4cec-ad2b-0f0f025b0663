package com.baidu.bce.internalsdk.rds.model.dbfirewall;

/**
 * Created by liu<PERSON>isen on 2017/11/6.
 */
public class SqlInjectGetResponse {

    private SqlInjectDetail sqlInjectDetail;

    public SqlInjectDetail getSqlInjectDetail() {
        return sqlInjectDetail;
    }

    public void setSqlInjectDetail(SqlInjectDetail sqlInjectDetail) {
        this.sqlInjectDetail = sqlInjectDetail;
    }

    @Override
    public String toString() {
        return "SqlInjectGetResponse{"
                + "sqlInjectDetail=" + sqlInjectDetail
                + '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        SqlInjectGetResponse response = (SqlInjectGetResponse) o;

        return sqlInjectDetail != null
                ? sqlInjectDetail.equals(response.sqlInjectDetail) : response.sqlInjectDetail == null;
    }

    @Override
    public int hashCode() {
        return sqlInjectDetail != null ? sqlInjectDetail.hashCode() : 0;
    }
}
