package com.baidu.bce.internalsdk.rds.model.account;

import com.baidu.bce.internalsdk.rds.util.PatternString;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/4.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Account {
    @NotEmpty
    @Pattern(regexp = PatternString.PATTERN_ACCOUNT_NAME)
    private String accountName;
    private String remark = "";
    private Collection<DatabasePrivilege> databasePrivileges = new LinkedList<DatabasePrivilege>();
    private String ETag;
    private SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
    private String type;
    private String accountStatus;
    private String superUserFlag;

    // OpenAPI 专用参数
    private String accountType;

    // OpenAPI 专用参数
    private String desc;

    // OpenAPI 专用参数
    private String status;

    private String password;

    private String encryptedPassword;

    private String rolcanlogin;

    public void removeDatabasePrivilege(String dbName) {
        for (DatabasePrivilege databasePrivilege : this.databasePrivileges) {
            if (databasePrivilege.getDbName().equals(dbName)) {
                this.databasePrivileges.remove(databasePrivilege);
                break;
            }
        }
    }

    @JsonProperty("ETag")
    public String getETag() {
        return this.ETag;
    }

    @JsonProperty("ETag")
    public void setETag(String ETag) {
        this.ETag = ETag;
    }

    private void updateETag() {
        this.ETag = formatter.format(new Date()).toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Account account = (Account) o;

        if (accountName != null ? !accountName.equals(account.accountName) : account.accountName != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return accountName != null ? accountName.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "Account{" +
                "accountName='" + accountName + '\'' +
                ", remark='" + remark + '\'' +
                ", type='" + type + '\'' +
                ", accountStatus='" + accountStatus + '\'' +
                ", databasePrivileges=" + databasePrivileges +
                ", superUserFlag=" + superUserFlag +
                '}';
    }

    public Account withAccountName(final String accountName) {
        this.accountName = accountName;
        return this;
    }

    public Account withRemark(final String remark) {
        this.remark = remark;
        return this;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getSuperUserFlag() {
        return superUserFlag;
    }

    public void setSuperUserFlag(String superUserFlag) {
        this.superUserFlag = superUserFlag;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Account withDatabasePrivileges(final Collection<DatabasePrivilege> databasePrivileges) {
        this.databasePrivileges = databasePrivileges;
        this.updateETag();
        return this;
    }

    public Account withPassword(final String password) {
        this.password = password;
        return this;
    }


    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Collection<DatabasePrivilege> getDatabasePrivileges() {
        return databasePrivileges;
    }

    public void setDatabasePrivileges(Collection<DatabasePrivilege> databasePrivileges) {
        this.updateETag();
        this.databasePrivileges = databasePrivileges;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Account accountName(final String accountName) {
        this.accountName = accountName;
        return this;
    }

    public Account remark(final String remark) {
        this.remark = remark;
        return this;
    }

    public Account type(final String type) {
        this.type = type;
        return this;
    }

    public Account databasePrivileges(final Collection<DatabasePrivilege> databasePrivileges) {
        this.databasePrivileges = databasePrivileges;
        return this;
    }

    public Account password(final String password) {
        this.password = password;
        return this;
    }

    public Account superUserFlag(final String superUserFlag) {
        this.superUserFlag = superUserFlag;
        return this;
    }

    public String getEncryptedPassword() {
        return encryptedPassword;
    }

    public void setEncryptedPassword(String encryptedPassword) {
        this.encryptedPassword = encryptedPassword;
    }

    public String getRolcanlogin() {
        return rolcanlogin;
    }

    public void setRolcanlogin(String rolcanlogin) {
        this.rolcanlogin = rolcanlogin;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DatabasePrivilege {
        private String dbName;
        private String authType;

        @Override
        public String toString() {
            return "DatabasePrivilege{" +
                    "dbName='" + dbName + '\'' +
                    ", authType='" + authType + '\'' +
                    '}';
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            DatabasePrivilege that = (DatabasePrivilege) o;

            if (dbName != null ? !dbName.equals(that.dbName) : that.dbName != null) return false;

            return true;
        }

        @Override
        public int hashCode() {
            return dbName != null ? dbName.hashCode() : 0;
        }

        public String getDbName() {
            return dbName;
        }

        public void setDbName(String dbName) {
            this.dbName = dbName;
        }

        public String getAuthType() {
            return authType;
        }

        public void setAuthType(String authType) {
            this.authType = authType;
        }

        public DatabasePrivilege withDbName(final String dbName) {
            this.dbName = dbName;
            return this;
        }

        public DatabasePrivilege withAuthType(final String authType) {
            this.authType = authType;
            return this;
        }


    }
}
