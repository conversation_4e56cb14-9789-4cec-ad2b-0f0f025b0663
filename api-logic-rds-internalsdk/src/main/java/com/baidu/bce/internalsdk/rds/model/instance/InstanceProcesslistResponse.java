package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.internalsdk.rds.model.performance.ProcessListResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;
import java.util.Map;

public class InstanceProcesslistResponse {
    private String datetime;
    private List<Processlist> processList;

    public String getDatetime() {
        return datetime;
    }

    public void setDatetime(String datetime) {
        this.datetime = datetime;
    }

    public List<Processlist> getProcessList() {
        return processList;
    }

    public void setProcessList(List<Processlist> processList) {
        this.processList = processList;
    }


    public static class Processlist {
        private String sql;
        private String info;
        private String db;
        private String state;
        private String host;
        private String command;
        private String user;
        private Integer time;
        private Long id;
        private Map<String, List<Integer>> lockHold;
        private List<ProcessListResponse.Process.LockWait> lockWait;

        public String getInfo() {
            return info;
        }

        public void setInfo(String info) {
            this.info = info;
        }

        public String getDb() {
            return db;
        }

        public void setDb(String db) {
            this.db = db;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public String getCommand() {
            return command;
        }

        public void setCommand(String command) {
            this.command = command;
        }

        public String getUser() {
            return user;
        }

        public void setUser(String user) {
            this.user = user;
        }

        public Integer getTime() {
            return time;
        }

        public void setTime(Integer time) {
            this.time = time;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getSql() {
            return sql;
        }

        public void setSql(String sql) {
            this.sql = sql;
        }

        public Map<String, List<Integer>> getLockHold() {
            return lockHold;
        }

        public void setLockHold(Map<String, List<Integer>> lockHold) {
            this.lockHold = lockHold;
        }

        public List<ProcessListResponse.Process.LockWait> getLockWait() {
            return lockWait;
        }

        public void setLockWait(List<ProcessListResponse.Process.LockWait> lockWait) {
            this.lockWait = lockWait;
        }
    }
}
