package com.baidu.bce.internalsdk.rds.model.database;

import java.util.List;

public class TableLevelListDatabaseResponse {
    private List<TableSchema> databases;
    private Integer pageSize;
    private Integer pageNo;
    private Integer count;
    private Long restDisk;
    private Long usedDisk;

    public List<TableSchema> getDatabases() {
        return databases;
    }

    public void setDatabases(List<TableSchema> databases) {
        this.databases = databases;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Long getRestDisk() {
        return restDisk;
    }

    public void setRestDisk(Long restDisk) {
        this.restDisk = restDisk;
    }

    public Long getUsedDisk() {
        return usedDisk;
    }

    public void setUsedDisk(Long usedDisk) {
        this.usedDisk = usedDisk;
    }

    public static class TableSchema {
        private String db;
        private int tbCnt;

        public String getDb() {
            return db;
        }

        public void setDb(String db) {
            this.db = db;
        }

        public int getTbCnt() {
            return tbCnt;
        }

        public void setTbCnt(int tbCnt) {
            this.tbCnt = tbCnt;
        }
    }
}
