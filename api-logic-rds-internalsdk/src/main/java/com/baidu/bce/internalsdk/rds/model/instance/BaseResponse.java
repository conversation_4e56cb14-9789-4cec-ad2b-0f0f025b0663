package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseResponse {
    private Boolean success = true;
    private Message message = new Message();

    @Override
    public String toString() {
        return "BaseResponse{"
                + "success=" + success
                + ", message='" + message
                + '\'' + '}';
    }

    public BaseResponse() {}

    public BaseResponse(boolean isSuccess, String message) {
        this.message.setGlobal(message);
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    public BaseResponse success(final Boolean success) {
        this.success = success;
        return this;
    }

    public BaseResponse message(final Message message) {
        this.message = message;
        return this;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Message {
        private String global;

        public String getGlobal() {
            return global;
        }

        public void setGlobal(String global) {
            this.global = global;
        }
    }
}
