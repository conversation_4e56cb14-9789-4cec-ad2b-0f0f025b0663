package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.iam.model.Acl;

import java.util.Objects;

public class AssumeRoleRequest {

    private String accountId;
    private String userId;
    private String roleName;
    private int durationSeconds;
    private boolean withToken;
    private Acl acl;
    private String attachment;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public int getDurationSeconds() {
        return durationSeconds;
    }

    public void setDurationSeconds(int durationSeconds) {
        this.durationSeconds = durationSeconds;
    }

    public boolean isWithToken() {
        return withToken;
    }

    public void setWithToken(boolean withToken) {
        this.withToken = withToken;
    }

    public Acl getAcl() {
        return acl;
    }

    public void setAcl(Acl acl) {
        this.acl = acl;
    }

    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    @Override
    public int hashCode() {
        return Objects.hash(accountId, userId, roleName, durationSeconds, withToken, acl, attachment);
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof AssumeRoleRequest)) {
            return false;
        }
        AssumeRoleRequest target = (AssumeRoleRequest) obj;
        if ((accountId == null && target.getAccountId() != null)
                || (accountId != null && !accountId.equals(target.getAccountId()))) {
            return false;
        }
        if ((userId == null && target.getUserId() != null)
                || (userId != null && !userId.equals(target.getUserId()))) {
            return false;
        }
        if ((roleName == null && target.getRoleName() != null)
                || (roleName != null && !roleName.equals(target.getRoleName()))) {
            return false;
        }
        if (durationSeconds != target.getDurationSeconds()) {
            return false;
        }
        if (withToken != target.isWithToken()) {
            return false;
        }
        if ((acl == null && target.getAcl() != null)
                || (acl != null && !acl.equals(target.getAcl()))) {
            return false;
        }
        if ((attachment == null && target.getAttachment() != null)
                || (attachment != null && !attachment.equals(target.getAttachment()))) {
            return false;
        }
        return true;
    }
}
