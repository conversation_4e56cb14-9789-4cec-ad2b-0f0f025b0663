package com.baidu.bce.internalsdk.rds.model.security;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/7/1.
 */
public class SecurityIpResponse {
    private Collection<String> securityIps = new LinkedList<String>();

    @Override
    public String toString() {
        return "SecurityIpResponse{" +
                "securityIps=" + securityIps +
                '}';
    }

    public SecurityIpResponse withSecurityIps(final Collection<String> securityIps) {
        this.securityIps = securityIps;
        return this;
    }

    public Collection<String> getSecurityIps() {
        return securityIps;
    }

    public void setSecurityIps(Collection<String> securityIps) {
        this.securityIps = securityIps;
    }
}
