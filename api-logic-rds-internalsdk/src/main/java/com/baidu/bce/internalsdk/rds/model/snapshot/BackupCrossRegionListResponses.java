package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BackupCrossRegionListResponses {
    private List<BackupCrossRegionSnapshot> snapshots;
    private double usedSpaceInMB;
    private String pageNo;
    private int pageSize;
    private int totalCount;

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public double getUsedSpaceInMB() {
        return usedSpaceInMB;
    }

    public void setUsedSpaceInMB(double usedSpaceInMB) {
        this.usedSpaceInMB = usedSpaceInMB;
    }

    public List<BackupCrossRegionSnapshot> getSnapshots() {
        return snapshots;
    }

    public void setSnapshots(List<BackupCrossRegionSnapshot> snapshots) {
        this.snapshots = snapshots;
    }
}
