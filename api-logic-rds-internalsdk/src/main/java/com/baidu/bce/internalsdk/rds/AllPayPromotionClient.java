package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.logic.rds.service.model.instance.AllPayPriceResultResponse;
import com.baidu.bce.logic.rds.service.model.instance.BatchPriceAllPayRequest;

public class AllPayPromotionClient extends BceClient {

    public AllPayPromotionClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public AllPayPriceResultResponse getPriceAllPayBatch(BatchPriceAllPayRequest request) {
        return createAuthorizedRequest().path("/v1/promotion/allPay/getPrice/batch")
                .post(Entity.json(request), AllPayPriceResultResponse.class);
    }
}
