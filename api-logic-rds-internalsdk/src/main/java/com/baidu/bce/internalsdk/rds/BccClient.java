package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.rds.model.bcc.BccInstanceDetail;
import endpoint.EndpointManager;

public class BccClient extends BceClient {

    private static final String SERVICE_NAME = "BCC";

    public BccClient(String accessKey, String secretKey, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey, securityToken);
    }

    public BccClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public BceInternalRequest createBccRequest() {
        return super.createAuthorizedRequest();
    }

    public BccInstanceDetail bccDetail(String bccId) {
        return this.createBccRequest()
                .path("/v2/instance/" + bccId)
                .get(BccInstanceDetail.class);
    }
}
