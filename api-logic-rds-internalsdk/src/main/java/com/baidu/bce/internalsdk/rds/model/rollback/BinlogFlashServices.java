package com.baidu.bce.internalsdk.rds.model.rollback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BinlogFlashServices {


    private String name;
    private String status;
    private String createTime;
    private String binlogFlashServiceID;
    private float binlogFlashProcess;
    private String binlogFlashType;
    private String binlogFlashStartTime;
    private String binlogFlashEndTime;
    private int affectRowsNumber;
    private int downloadUrlExpireSeconds;

    public int getAffectRowsNumber() {
        return affectRowsNumber;
    }

    public void setAffectRowsNumber(int affectRowsNumber) {
        this.affectRowsNumber = affectRowsNumber;
    }

    public int getDownloadUrlExpireSeconds() {
        return downloadUrlExpireSeconds;
    }

    public void setDownloadUrlExpireSeconds(int downloadUrlExpireSeconds) {
        this.downloadUrlExpireSeconds = downloadUrlExpireSeconds;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getBinlogFlashServiceID() {
        return binlogFlashServiceID;
    }

    public void setBinlogFlashServiceID(String binlogFlashServiceID) {
        this.binlogFlashServiceID = binlogFlashServiceID;
    }

    public float getBinlogFlashProcess() {
        return binlogFlashProcess;
    }

    public void setBinlogFlashProcess(float binlogFlashProcess) {
        this.binlogFlashProcess = binlogFlashProcess;
    }

    public String getBinlogFlashType() {
        return binlogFlashType;
    }

    public void setBinlogFlashType(String binlogFlashType) {
        this.binlogFlashType = binlogFlashType;
    }

    public String getBinlogFlashStartTime() {
        return binlogFlashStartTime;
    }

    public void setBinlogFlashStartTime(String binlogFlashStartTime) {
        this.binlogFlashStartTime = binlogFlashStartTime;
    }

    public String getBinlogFlashEndTime() {
        return binlogFlashEndTime;
    }

    public void setBinlogFlashEndTime(String binlogFlashEndTime) {
        this.binlogFlashEndTime = binlogFlashEndTime;
    }
}
