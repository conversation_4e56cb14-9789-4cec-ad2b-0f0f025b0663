package com.baidu.bce.internalsdk.rds.model.smartdba;

public class SlowsqlTemplate {
    private Long avgExamRows;
    private Long avgLockTime;
    private Long avgNumRows;
    private Double avgTime;
    private String digest;
    private Long executeTimes;
    private Long maxExamRows;
    private Double maxLockTime;
    private Long maxNumRows;
    private Double maxTime;
    private String normalSql;
    private String schema;
    private Long totalExamRows;
    private Double totalLockTime;
    private Long totalNumRows;
    private Double totalTime;

    public Long getAvgExamRows() {
        return avgExamRows;
    }

    public void setAvgExamRows(Long avgExamRows) {
        this.avgExamRows = avgExamRows;
    }

    public Long getAvgLockTime() {
        return avgLockTime;
    }

    public void setAvgLockTime(Long avgLockTime) {
        this.avgLockTime = avgLockTime;
    }

    public Long getAvgNumRows() {
        return avgNumRows;
    }

    public void setAvgNumRows(Long avgNumRows) {
        this.avgNumRows = avgNumRows;
    }

    public Double getAvgTime() {
        return avgTime;
    }

    public void setAvgTime(Double avgTime) {
        this.avgTime = avgTime;
    }

    public String getDigest() {
        return digest;
    }

    public void setDigest(String digest) {
        this.digest = digest;
    }

    public Long getExecuteTimes() {
        return executeTimes;
    }

    public void setExecuteTimes(Long executeTimes) {
        this.executeTimes = executeTimes;
    }

    public Long getMaxExamRows() {
        return maxExamRows;
    }

    public void setMaxExamRows(Long maxExamRows) {
        this.maxExamRows = maxExamRows;
    }

    public Double getMaxLockTime() {
        return maxLockTime;
    }

    public void setMaxLockTime(Double maxLockTime) {
        this.maxLockTime = maxLockTime;
    }

    public Long getMaxNumRows() {
        return maxNumRows;
    }

    public void setMaxNumRows(Long maxNumRows) {
        this.maxNumRows = maxNumRows;
    }

    public Double getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(Double maxTime) {
        this.maxTime = maxTime;
    }

    public String getNormalSql() {
        return normalSql;
    }

    public void setNormalSql(String normalSql) {
        this.normalSql = normalSql;
    }

    public String getSchema() {
        return schema;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }

    public Long getTotalExamRows() {
        return totalExamRows;
    }

    public void setTotalExamRows(Long totalExamRows) {
        this.totalExamRows = totalExamRows;
    }

    public Double getTotalLockTime() {
        return totalLockTime;
    }

    public void setTotalLockTime(Double totalLockTime) {
        this.totalLockTime = totalLockTime;
    }

    public Long getTotalNumRows() {
        return totalNumRows;
    }

    public void setTotalNumRows(Long totalNumRows) {
        this.totalNumRows = totalNumRows;
    }

    public Double getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(Double totalTime) {
        this.totalTime = totalTime;
    }
}
