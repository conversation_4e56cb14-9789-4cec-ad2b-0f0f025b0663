package com.baidu.bce.internalsdk.rds.model.database;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/5/16 20:29
 */
public class ListDatabasesRequest {
    private Integer pageNo;
    private Integer pageSize;
    private String pattern;
    @NotNull
    @IdPermission
    private String instanceId;

    @Override
    public String toString() {
        return "ListDatabasesRequest{" +
                "pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", pattern='" + pattern + '\'' +
                ", instanceId='" + instanceId + '\'' +
                '}';
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

}
