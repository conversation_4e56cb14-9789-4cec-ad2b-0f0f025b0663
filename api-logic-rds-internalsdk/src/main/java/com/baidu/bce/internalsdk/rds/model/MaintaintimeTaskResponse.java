package com.baidu.bce.internalsdk.rds.model;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/24 14:48
 */
public class MaintaintimeTaskResponse {
    private Integer taskId;
    private String taskType;
    private String taskName;
    private String instanceId; //返回给用户的统一用instanceId  短ID
    private String instanceName;
    private String userId;
    private String region;
    private String taskStatus;
    private String createTime;
    // startTime客户会有异议，openapi去掉此字段
//  private String startTime;
    private String updateTime;
    private String finishTime;
    private Integer cancelFlag;
    private List<MaintaintimeTaskProgress> progress;

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

//    public String getStartTime() {
//        return startTime;
//    }
//
//    public void setStartTime(String startTime) {
//        this.startTime = startTime;
//    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }

    public Integer getCancelFlag() {
        return cancelFlag;
    }

    public void setCancelFlag(Integer cancelFlag) {
        this.cancelFlag = cancelFlag;
    }

    public List<MaintaintimeTaskProgress> getProgress() {
        return progress;
    }

    public void setProgress(List<MaintaintimeTaskProgress> progress) {
        this.progress = progress;
    }
}
