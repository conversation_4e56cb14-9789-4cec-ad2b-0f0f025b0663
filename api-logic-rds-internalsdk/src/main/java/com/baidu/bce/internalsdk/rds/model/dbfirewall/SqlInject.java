package com.baidu.bce.internalsdk.rds.model.dbfirewall;

import javax.validation.constraints.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/3.
 */
public class SqlInject {

    private int sqlId;          // 自增主键

    private String appId;       // 代理实例ID

    private String userIp;      // SQL注入来源IP

    private String userPort;    // SQL注入来源端口

    @Pattern(regexp = "[a-zA-Z]\\w{0,15}")
    private String accountName; // SQL注入来源用户

    @Pattern(regexp = "[a-zA-Z]\\w{0,63}")
    private String dbName;      // SQL注入数据库

    private String  sqlMd5;     // SQL注入签名MD5

    private String sqlFingerprint; // SQL注入签名

    private  String createTime;     // SQL注入发生时间

    public int getSqlId() {
        return sqlId;
    }

    public void setSqlId(int sqlId) {
        this.sqlId = sqlId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getUserIp() {
        return userIp;
    }

    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }

    public String getUserPort() {
        return userPort;
    }

    public void setUserPort(String userPort) {
        this.userPort = userPort;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getSqlMd5() {
        return sqlMd5;
    }

    public void setSqlMd5(String sqlMd5) {
        this.sqlMd5 = sqlMd5;
    }

    public String getSqlFingerprint() {
        return sqlFingerprint;
    }

    public void setSqlFingerprint(String sqlFingerprint) {
        this.sqlFingerprint = sqlFingerprint;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "SqlInject{"
                + "sqlId=" + sqlId
                + ", appId='" + appId + '\''
                + ", userIp='" + userIp + '\''
                + ", userPort='" + userPort + '\''
                + ", accountName='" + accountName + '\''
                + ", dbName='" + dbName + '\''
                + ", sqlMd5='" + sqlMd5 + '\''
                + ", sqlFingerprint='" + sqlFingerprint + '\''
                + ", createTime='" + createTime + '\''
                + '}';
    }
}
