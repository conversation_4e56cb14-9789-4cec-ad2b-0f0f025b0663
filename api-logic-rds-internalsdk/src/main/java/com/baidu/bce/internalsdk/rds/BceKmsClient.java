package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import endpoint.EndpointManager;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by luping03 on 17/10/18.
 */
public class BceKmsClient extends BceClient {

    private static final String SERVICE_NAME = "slowquery";

    private String baseURL = "/json-api/v1/slowquery";
    private String chartBase = "/chart";
    private String detailBase = "/detail";
    private String summaryBase = "/summary";
    private String downloadfileBase = "/downloadfile";
    private String getSqlBase = "/getsql";
    private String statusBase = "/status";
    private String startBase = "/start";
    private String stopBase = "/stop";
    private String taskBase = "/download/task";
    private String taskListBase = taskBase + "/list";
    private String taskDetailBase =  "/download/task";

    public BceInternalRequest createKmsRequest() {
        return super.createAuthorizedRequest();
    }

    public BceKmsClient(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    public BceKmsClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public BceKmsClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public void keyList() {

        Map<String, String> params = new HashMap<String, String>();
        params.put("limit", "100");
        params.put("marker", "");

        createKmsRequest()
                .path("")
                .queryParam("action", "ListKeys")
                .post(Entity.json(params));
    }

}
