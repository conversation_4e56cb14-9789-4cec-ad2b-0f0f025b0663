package com.baidu.bce.internalsdk.rds;

import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/26.
 */
public class RDSRegexp {
    public static final String ip = "(^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(/(\\d|[1-2]\\d|3[0-2]))?$)|(^%$)";
    public static final Pattern IpPattern = Pattern.compile(ip);
    public static final String INSTANCE_NAME = "^\\p{L}[\\p{L}0-9._\\-\\/]{0,63}$";
    public static final String domainPrefix = "^[a-z][0-9a-z\\-]{1,28}[0-9a-z]\\..*$";
    public static final String PARAMETER_TEMPLATE_NAME = "^[a-zA-Z](?=.*\\d)(?=.*[a-zA-Z])" +
            "(?=.*[_.()])[\\da-zA-Z_.()]{7,63}$";
}
