package com.baidu.bce.internalsdk.rds.model.group;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * Created by shiyuxin on 19/6/11.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroupInfo {

    private String groupId;
    private String name;
    private Integer count;
    private GroupMember leader;
    private String dbType;
    private String createTime;
    private List<GroupMember> followers;

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public GroupMember getLeader() {
        return leader;
    }

    public void setLeader(GroupMember leader) {
        this.leader = leader;
    }

    public List<GroupMember> getFollowers() {
        return followers;
    }

    public void setFollowers(List<GroupMember> followers) {
        this.followers = followers;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GroupMember {
        private String instanceId;
        private String instanceIdShort;
        private String region;
        private String azone;
        private String status;
        private String lockMode;
        private String name;
        private String resourceType;


        public String getResourceType() {
            return resourceType;
        }

        public void setResourceType(String resourceType) {
            this.resourceType = resourceType;
        }

        public String getInstanceId() {
            return instanceId;
        }

        public void setInstanceId(String instanceId) {
            this.instanceId = instanceId;
        }

        public String getRegion() {
            return region;
        }

        public void setRegion(String region) {
            this.region = region;
        }

        public String getAzone() {
            return azone;
        }

        public void setAzone(String azone) {
            this.azone = azone;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getLockMode() {
            return lockMode;
        }

        public void setLockMode(String lockMode) {
            this.lockMode = lockMode;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getInstanceIdShort() {
            return instanceIdShort;
        }

        public void setInstanceIdShort(String instanceIdShort) {
            this.instanceIdShort = instanceIdShort;
        }
    }


}
