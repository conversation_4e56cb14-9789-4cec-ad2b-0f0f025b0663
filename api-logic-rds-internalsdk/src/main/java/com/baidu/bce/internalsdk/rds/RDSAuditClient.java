package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceClientConfig;
import com.baidu.bce.internalsdk.core.BceInternalClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.logic.rds.service.model.instance.AuditStopRequest;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;

public class RDSAuditClient extends BceClient {

    private static final String SERVICE_NAME = "RDSAUDIT";

    private String baseURL = "";

    private String xAuthToken;

    public void setxAuthToken(String xAuthToken) {
        this.xAuthToken = xAuthToken;
    }

    public RDSAuditClient(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    public RDSAuditClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public RDSAuditClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public RDSAuditClient(String authToken, String accessKey, String secretKey, String endpoint, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
        this.setxAuthToken(authToken);
        BceInternalRequest.setThreadTokenId(null);
    }

    public BceInternalRequest createRdsServiceRequest() {
        BceInternalRequest request = createAuthorizedRequest();
        if (this.xAuthToken != null) {
            request.token(xAuthToken);
        }
        return request;
    }

    public BceInternalRequest createRdsRequest() {
        BceClientConfig config = new BceClientConfig();
        config.withReadTimeout(30000)
                .withMaxConnTotal(400)
                .withMaxConnPerRoute(400);
        BceInternalRequest bceInternalRequest = BceInternalClient.request(endpoint, config)
                .authorization(accessKey, secretKey);
        if (!StringUtils.isEmpty(securityToken)) {
            bceInternalRequest.securityToken(securityToken);
        }
        return bceInternalRequest;
    }

    public void startInstance(String userId, String resourceId, String service, AuditStopRequest auditStopRequest) {
        this.createRdsRequest()
                .path(this.baseURL + "/" + userId + "/" + service + "/resources/" + resourceId)
                .queryParam("action", "START")
//                .keyOnlyQueryParam("action=START")
                .put(Entity.json(auditStopRequest));
    }

    public void stopInstance(String userId, String resourceId, String service, AuditStopRequest auditStopRequest) {
        this.createRdsRequest()
                .path(this.baseURL + "/" + userId + "/" + service + "/resources/" + resourceId)
                .queryParam("action", "STOP")
//                .keyOnlyQueryParam("action=STOP")
                .put(Entity.json(auditStopRequest));
    }

    public void deleteInstance(String userId, String resourceId, String service, AuditStopRequest auditStopRequest) {
        this.createRdsRequest()
                .path(this.baseURL + "/" + userId + "/" + service + "/resources/" + resourceId)
                .queryParam("action", "DELETE")
//                .keyOnlyQueryParam("action=DELETE")
                .put(Entity.json(auditStopRequest));
    }
}
