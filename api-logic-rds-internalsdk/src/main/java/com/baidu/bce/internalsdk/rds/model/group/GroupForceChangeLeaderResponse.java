package com.baidu.bce.internalsdk.rds.model.group;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroupForceChangeLeaderResponse {

    private Integer behindMaster;

    public GroupForceChangeLeaderResponse() {
    }

    public Integer getBehindMaster() {
        return behindMaster;
    }

    public void setBehindMaster(Integer behindMaster) {
        this.behindMaster = behindMaster;
    }
}
