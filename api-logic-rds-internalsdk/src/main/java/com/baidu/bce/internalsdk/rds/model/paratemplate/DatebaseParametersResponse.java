package com.baidu.bce.internalsdk.rds.model.paratemplate;

import java.util.ArrayList;
import java.util.List;

public class DatebaseParametersResponse {

    private List<DatebaseParameters> list = new ArrayList<>();

    public DatebaseParametersResponse() {
    }

    public DatebaseParametersResponse(List<DatebaseParameters> list) {
        this.list = list;
    }

    public List<DatebaseParameters> getList() {
        return list;
    }

    public void setList(List<DatebaseParameters> list) {
        this.list = list;
    }

    @Override
    public String toString() {
        return "DatebaseParametersResponse{" +
                "list=" + list +
                '}';
    }
}
