package com.baidu.bce.internalsdk.rds.model.slowquery;

import java.util.List;

/**
 * Created by luping03 on 17/12/25.
 */
public class SlowquerySummaryResponse extends SlowqueryBaseResponse {

    private SlowqueryBasePageResponseModel<List<SlowqueryDetailPageResult>> page;

    public SlowqueryBasePageResponseModel<List<SlowqueryDetailPageResult>> getPage() {
        return page;
    }

    public void setPage(SlowqueryBasePageResponseModel<List<SlowqueryDetailPageResult>> page) {
        this.page = page;
    }

    private static class SlowqueryDetailPageResult {

        private String templateSql;

        private Integer executionTimes;

        private Double avgQueryTime;

        private Double maxQueryTime;

        private Double avgLockTime;

        private Double maxLockTime;

        private Integer avgScanRows;

        private Integer maxScanRows;

        private Integer avgReturnRows;

        private Integer maxReturnRows;

        private String templateSqlMd5;

        private String defaultdb;

        public String getTemplateSql() {
            return templateSql;
        }

        public void setTemplateSql(String templateSql) {
            this.templateSql = templateSql;
        }

        public Integer getExecutionTimes() {
            return executionTimes;
        }

        public void setExecutionTimes(Integer executionTimes) {
            this.executionTimes = executionTimes;
        }

        public Double getAvgQueryTime() {
            return avgQueryTime;
        }

        public void setAvgQueryTime(Double avgQueryTime) {
            this.avgQueryTime = avgQueryTime;
        }

        public Double getMaxQueryTime() {
            return maxQueryTime;
        }

        public void setMaxQueryTime(Double maxQueryTime) {
            this.maxQueryTime = maxQueryTime;
        }

        public Double getAvgLockTime() {
            return avgLockTime;
        }

        public void setAvgLockTime(Double avgLockTime) {
            this.avgLockTime = avgLockTime;
        }

        public Double getMaxLockTime() {
            return maxLockTime;
        }

        public void setMaxLockTime(Double maxLockTime) {
            this.maxLockTime = maxLockTime;
        }

        public Integer getAvgScanRows() {
            return avgScanRows;
        }

        public void setAvgScanRows(Integer avgScanRows) {
            this.avgScanRows = avgScanRows;
        }

        public Integer getMaxScanRows() {
            return maxScanRows;
        }

        public void setMaxScanRows(Integer maxScanRows) {
            this.maxScanRows = maxScanRows;
        }

        public Integer getAvgReturnRows() {
            return avgReturnRows;
        }

        public void setAvgReturnRows(Integer avgReturnRows) {
            this.avgReturnRows = avgReturnRows;
        }

        public Integer getMaxReturnRows() {
            return maxReturnRows;
        }

        public void setMaxReturnRows(Integer maxReturnRows) {
            this.maxReturnRows = maxReturnRows;
        }

        public String getTemplateSqlMd5() {
            return templateSqlMd5;
        }

        public void setTemplateSqlMd5(String templateSqlMd5) {
            this.templateSqlMd5 = templateSqlMd5;
        }

        public String getDefaultdb() {
            return defaultdb;
        }

        public void setDefaultdb(String defaultdb) {
            this.defaultdb = defaultdb;
        }
    }


}
