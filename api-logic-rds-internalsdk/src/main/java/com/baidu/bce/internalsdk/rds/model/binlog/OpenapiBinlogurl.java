package com.baidu.bce.internalsdk.rds.model.binlog;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/9.
 */
public class OpenapiBinlogurl {

    private String downloadUrl = "www.baidu.com";
    private Date downloadExpires = new Date(System.currentTimeMillis() + 600000);



    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getDownloadExpires() {
        return downloadExpires;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setDownloadExpires(Date downloadExpires) {
        this.downloadExpires = downloadExpires;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }


    public OpenapiBinlogurl downloadUrl(final String downloadUrl) {
        this.downloadUrl = downloadUrl;
        return this;
    }

    public OpenapiBinlogurl downloadExpires(final Date downloadExpires) {
        this.downloadExpires = downloadExpires;
        return this;
    }
}
