package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/19.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DashboardInstanceModifyReplicationTypeRequest {
    @NotNull
    @IdPermission
    private String instanceId;
    private String replicationType;

    @Override
    public String toString() {
        return "DashboardInstanceModifyReplicationTypeRequest{"
                + "instanceId='" + instanceId + '\''
                + ", replicationType=" + replicationType
                + '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getReplicationType() {
        return replicationType;
    }

    public void setReplicationType(String replicationType) {
        this.replicationType = replicationType;
    }
}
