package com.baidu.bce.internalsdk.rds.model.smartdba;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GetSqlResponse {

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Item {
        private String action;
        private Long affectedRows;
        private String clientIP;
        private Integer clientPort;
        private String cluster;
        private String communityID;
        private Long connectionID;
        private String currentDB;
        private Long duration;
        private String end;
        private Long insertID;
        private String instance;
        private String method;
        private Long numRows;
        private String role;
        private String slice;
        private String sql;
        private String start;
        private String status;
        private Long totalBytes;
        private String user;

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public Long getAffectedRows() {
            return affectedRows;
        }

        public void setAffectedRows(Long affectedRows) {
            this.affectedRows = affectedRows;
        }

        public String getClientIP() {
            return clientIP;
        }

        public void setClientIP(String clientIP) {
            this.clientIP = clientIP;
        }

        public Integer getClientPort() {
            return clientPort;
        }

        public void setClientPort(Integer clientPort) {
            this.clientPort = clientPort;
        }

        public String getCluster() {
            return cluster;
        }

        public void setCluster(String cluster) {
            this.cluster = cluster;
        }

        public String getCommunityID() {
            return communityID;
        }

        public void setCommunityID(String communityID) {
            this.communityID = communityID;
        }

        public Long getConnectionID() {
            return connectionID;
        }

        public void setConnectionID(Long connectionID) {
            this.connectionID = connectionID;
        }

        public String getCurrentDB() {
            return currentDB;
        }

        public void setCurrentDB(String currentDB) {
            this.currentDB = currentDB;
        }

        public Long getDuration() {
            return duration;
        }

        public void setDuration(Long duration) {
            this.duration = duration;
        }

        public String getEnd() {
            return end;
        }

        public void setEnd(String end) {
            this.end = end;
        }

        public Long getInsertID() {
            return insertID;
        }

        public void setInsertID(Long insertID) {
            this.insertID = insertID;
        }

        public String getInstance() {
            return instance;
        }

        public void setInstance(String instance) {
            this.instance = instance;
        }

        public String getMethod() {
            return method;
        }

        public void setMethod(String method) {
            this.method = method;
        }

        public Long getNumRows() {
            return numRows;
        }

        public void setNumRows(Long numRows) {
            this.numRows = numRows;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getSlice() {
            return slice;
        }

        public void setSlice(String slice) {
            this.slice = slice;
        }

        public String getSql() {
            return sql;
        }

        public void setSql(String sql) {
            this.sql = sql;
        }

        public String getStart() {
            return start;
        }

        public void setStart(String start) {
            this.start = start;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public Long getTotalBytes() {
            return totalBytes;
        }

        public void setTotalBytes(Long totalBytes) {
            this.totalBytes = totalBytes;
        }

        public String getUser() {
            return user;
        }

        public void setUser(String user) {
            this.user = user;
        }
    }

    private List<Item> items;

    private Long totalCount;

    public List<Item> getItems() {
        return items;
    }

    public void setItems(List<Item> items) {
        this.items = items;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }
}
