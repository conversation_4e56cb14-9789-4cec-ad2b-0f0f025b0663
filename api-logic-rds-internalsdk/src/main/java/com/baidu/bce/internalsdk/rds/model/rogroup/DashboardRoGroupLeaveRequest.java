package com.baidu.bce.internalsdk.rds.model.rogroup;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import java.util.List;

public class DashboardRoGroupLeaveRequest {
    @IdPermission
    private String sourceAppId;
    private String roGroupId;
    private List<String> readReplicaList;

    public String getSourceAppId() {
        return sourceAppId;
    }

    public void setSourceAppId(String sourceAppId) {
        this.sourceAppId = sourceAppId;
    }

    public String getRoGroupId() {
        return roGroupId;
    }

    public void setRoGroupId(String roGroupId) {
        this.roGroupId = roGroupId;
    }

    public List<String> getReadReplicaList() {
        return readReplicaList;
    }

    public void setReadReplicaList(List<String> readReplicaList) {
        this.readReplicaList = readReplicaList;
    }
}
