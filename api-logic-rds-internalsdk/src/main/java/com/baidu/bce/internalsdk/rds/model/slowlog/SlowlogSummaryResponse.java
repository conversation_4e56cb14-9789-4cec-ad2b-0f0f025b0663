package com.baidu.bce.internalsdk.rds.model.slowlog;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SlowlogSummaryResponse {
    private Integer pageNo;
    private Integer pageSize;
    private Integer count;
    private List<SlowlogSummary> slowLogSummary;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<SlowlogSummary> getSlowLogSummary() {
        return slowLogSummary;
    }

    public void setSlowLogSummary(List<SlowlogSummary> slowLogSummary) {
        this.slowLogSummary = slowLogSummary;
    }
}
