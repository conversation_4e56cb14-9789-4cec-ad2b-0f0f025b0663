package com.baidu.bce.internalsdk.rds.model.account;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/11.
 */
public class AccountPrivilege {
    private String accountName;
    private String authType;

    @Override
    public String toString() {
        return "AccountPrivilege{" +
                "accountName='" + accountName + '\'' +
                ", authType='" + authType + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AccountPrivilege that = (AccountPrivilege) o;

        if (accountName != null ? !accountName.equals(that.accountName) : that.accountName != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return accountName != null ? accountName.hashCode() : 0;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }
}
