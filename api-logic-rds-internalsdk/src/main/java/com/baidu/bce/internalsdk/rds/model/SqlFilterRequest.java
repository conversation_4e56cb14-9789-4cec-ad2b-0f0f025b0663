package com.baidu.bce.internalsdk.rds.model;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

public class SqlFilterRequest {
    @IdPermission
    private String instanceId;
    private String sqlFilterId;
    private String filterType;
    private String filterKey;
    private Integer filterLimit;
    private String action;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getSqlFilterId() {
        return sqlFilterId;
    }

    public void setSqlFilterId(String sqlFilterId) {
        this.sqlFilterId = sqlFilterId;
    }

    public String getFilterType() {
        return filterType;
    }

    public void setFilterType(String filterType) {
        this.filterType = filterType;
    }

    public String getFilterKey() {
        return filterKey;
    }

    public void setFilterKey(String filterKey) {
        this.filterKey = filterKey;
    }

    public Integer getFilterLimit() {
        return filterLimit;
    }

    public void setFilterLimit(Integer filterLimit) {
        this.filterLimit = filterLimit;
    }
}
