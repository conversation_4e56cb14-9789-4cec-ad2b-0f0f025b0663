package com.baidu.bce.internalsdk.rds.model.smartdba;

import java.util.List;

public class SlowsqlTemplateResponse {
    private List<SlowsqlTemplate> items;
    private SlowsqlTemplate summary;
    private Integer totalCount;

    public List<SlowsqlTemplate> getItems() {
        return items;
    }

    public void setItems(List<SlowsqlTemplate> items) {
        this.items = items;
    }

    public SlowsqlTemplate getSummary() {
        return summary;
    }

    public void setSummary(SlowsqlTemplate summary) {
        this.summary = summary;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }
}
