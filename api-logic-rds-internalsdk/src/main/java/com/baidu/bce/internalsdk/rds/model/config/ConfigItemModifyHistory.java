package com.baidu.bce.internalsdk.rds.model.config;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.ArrayList;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/19.
 */
public class ConfigItemModifyHistory implements Comparable<ConfigItemModifyHistory> {
    private String name;
    private String beforeValue;
    private String afterValue;
    private String status;
    private Date updateTime;

    @Override
    public int compareTo(ConfigItemModifyHistory o) {
        return o.updateTime.compareTo(this.updateTime);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ConfigItemModifyHistory{");
        sb.append("name='").append(name).append('\'');
        sb.append(", beforeValue='").append(beforeValue).append('\'');
        sb.append(", afterValue='").append(afterValue).append('\'');
        sb.append(", status='").append(status).append('\'');
        sb.append(", updateTime=").append(updateTime);
        sb.append('}');
        return sb.toString();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBeforeValue() {
        return beforeValue;
    }

    public void setBeforeValue(String beforeValue) {
        this.beforeValue = beforeValue;
    }

    public String getAfterValue() {
        return afterValue;
    }

    public void setAfterValue(String afterValue) {
        this.afterValue = afterValue;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getUpdateTime() {
        return updateTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public static class ConfigItemModifyHistoryList extends ArrayList<ConfigItemModifyHistory> {
    }
}
