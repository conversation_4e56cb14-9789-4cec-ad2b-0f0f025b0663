package com.baidu.bce.internalsdk.rds.model.migration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/18.
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResponse<T> extends BaseResponse {
    private Page<T> page = new Page<T>();

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PageResponse{");
        sb.append("page=").append(page);
        sb.append('}');
        return sb.toString();
    }

    public Page<T> getPage() {
        return page;
    }

    public void setPage(Page<T> page) {
        this.page = page;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Page<T> {
        private String orderBy = "";
        private String order = "";
        private int pageNo = 1;
        private int pageSize = 0;
        private Collection<T> result = new LinkedList<T>();
        private int totalCount = 0;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("Page{");
            sb.append("orderBy='").append(orderBy).append('\'');
            sb.append(", order='").append(order).append('\'');
            sb.append(", pageNo=").append(pageNo);
            sb.append(", pageSize=").append(pageSize);
            sb.append(", result=").append(result);
            sb.append(", totalCount=").append(totalCount);
            sb.append('}');
            return sb.toString();
        }

        public void setPageParams(int pageNo, int pageSize, List<T> all) {
            this.pageNo = pageNo;
            this.pageSize = pageSize;
            this.totalCount = all.size();
            this.result = all.subList((pageNo - 1) * pageSize,
                    (pageNo * pageSize > all.size() ? all.size() : pageNo * pageSize));
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public int getPageNo() {
            return pageNo;
        }

        public void setPageNo(int pageNo) {
            this.pageNo = pageNo;
        }

        public String getOrderBy() {
            return orderBy;
        }

        public void setOrderBy(String orderBy) {
            this.orderBy = orderBy;
        }

        public String getOrder() {
            return order;
        }

        public void setOrder(String order) {
            this.order = order;
        }

        public Collection<T> getResult() {
            return result;
        }

        public void setResult(Collection<T> result) {
            if (result == null) {
                this.result = new LinkedList<T>();
            } else {
                this.result = result;
            }
        }

        public Page withOrderBy(final String orderBy) {
            this.orderBy = orderBy;
            return this;
        }

        public Page withOrder(final String order) {
            this.order = order;
            return this;
        }

        public Page withPageNo(final int pageNo) {
            this.pageNo = pageNo;
            return this;
        }

        public Page withResult(final Collection<T> result) {
            this.result = result;
            return this;
        }

        public Page withTotalCount(final int totalCount) {
            this.totalCount = totalCount;
            return this;
        }
    }
}

