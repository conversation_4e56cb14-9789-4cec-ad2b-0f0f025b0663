package com.baidu.bce.internalsdk.rds.model.paratemplate;


public class ParaTemplateCompare {

    private String name;

    private String defaultValue;

    private String value;

    private String type;

    private Boolean dynamic;

    private Boolean modifiable;

    private String allowedValues;

    private String description;

    private String etag;

    private String configType;

    private String bestValue;

    private String attention;

    private String postValue;

    private Boolean isChange;

    public ParaTemplateCompare() {
    }

    public ParaTemplateCompare(String name, String defaultValue, String value, String type,
                               Boolean dynamic, Boolean modifiable, String allowedValues,
                               String description, String etag, String configType, String bestValue,
                               String attention, String postValue, Boolean isChange) {
        this.name = name;
        this.defaultValue = defaultValue;
        this.value = value;
        this.type = type;
        this.dynamic = dynamic;
        this.modifiable = modifiable;
        this.allowedValues = allowedValues;
        this.description = description;
        this.etag = etag;
        this.configType = configType;
        this.bestValue = bestValue;
        this.attention = attention;
        this.postValue = postValue;
        this.isChange = isChange;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getDynamic() {
        return dynamic;
    }

    public void setDynamic(Boolean dynamic) {
        this.dynamic = dynamic;
    }

    public Boolean getModifiable() {
        return modifiable;
    }

    public void setModifiable(Boolean modifiable) {
        this.modifiable = modifiable;
    }

    public String getAllowedValues() {
        return allowedValues;
    }

    public void setAllowedValues(String allowedValues) {
        this.allowedValues = allowedValues;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEtag() {
        return etag;
    }

    public void setEtag(String etag) {
        this.etag = etag;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getBestValue() {
        return bestValue;
    }

    public void setBestValue(String bestValue) {
        this.bestValue = bestValue;
    }

    public String getAttention() {
        return attention;
    }

    public void setAttention(String attention) {
        this.attention = attention;
    }

    public String getPostValue() {
        return postValue;
    }

    public void setPostValue(String postValue) {
        this.postValue = postValue;
    }

    public Boolean getChange() {
        return isChange;
    }

    public void setChange(Boolean change) {
        isChange = change;
    }

    @Override
    public String toString() {
        return "ParaTemplateCompare{" +
                "name='" + name + '\'' +
                ", defaultValue='" + defaultValue + '\'' +
                ", value='" + value + '\'' +
                ", type='" + type + '\'' +
                ", dynamic=" + dynamic +
                ", modifiable=" + modifiable +
                ", allowedValues='" + allowedValues + '\'' +
                ", description='" + description + '\'' +
                ", etag='" + etag + '\'' +
                ", configType='" + configType + '\'' +
                ", bestValue='" + bestValue + '\'' +
                ", attention='" + attention + '\'' +
                ", postValue='" + postValue + '\'' +
                ", isChange=" + isChange +
                '}';
    }
}
