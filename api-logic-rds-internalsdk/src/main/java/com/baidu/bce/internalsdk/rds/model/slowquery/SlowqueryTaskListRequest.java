package com.baidu.bce.internalsdk.rds.model.slowquery;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class SlowqueryTaskListRequest {
    @NotNull
    private String instanceId;

    private String filterStartTime;

    private String filterEndTime;

    private String order = "desc";

    private String pageNo = "1";

    private String pageSize = "";

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getFilterStartTime() {
        return filterStartTime;
    }

    public void setFilterStartTime(String filterStartTime) {
        this.filterStartTime = filterStartTime;
    }

    public String getFilterEndTime() {
        return filterEndTime;
    }

    public void setFilterEndTime(String filterEndTime) {
        this.filterEndTime = filterEndTime;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }
}
