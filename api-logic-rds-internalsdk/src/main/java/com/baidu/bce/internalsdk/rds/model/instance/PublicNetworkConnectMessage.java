package com.baidu.bce.internalsdk.rds.model.instance;

import java.util.List;

public class PublicNetworkConnectMessage {

    private List<InetConnectList> inetConnectList;

    public List<InetConnectList> getInetConnectList() {
        return inetConnectList;
    }

    public void setInetConnectList(List<InetConnectList> inetConnectList) {
        this.inetConnectList = inetConnectList;
    }

    public static class InetConnectList {
        private String inetIp;
        private String connectStatus;
        private String updateTime;
        private String connectId;

        public String getInetIp() {
            return inetIp;
        }

        public void setInetIp(String inetIp) {
            this.inetIp = inetIp;
        }

        public String getConnectStatus() {
            return connectStatus;
        }

        public void setConnectStatus(String connectStatus) {
            this.connectStatus = connectStatus;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getConnectId() {
            return connectId;
        }

        public void setConnectId(String connectId) {
            this.connectId = connectId;
        }
    }
}
