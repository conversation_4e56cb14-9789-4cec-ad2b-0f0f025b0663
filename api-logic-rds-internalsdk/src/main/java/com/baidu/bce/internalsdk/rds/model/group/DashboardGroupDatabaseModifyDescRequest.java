package com.baidu.bce.internalsdk.rds.model.group;


import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class DashboardGroupDatabaseModifyDescRequest {
    @NotNull
    private String groupId;
    @NotNull
    private String dbName;
    @NotNull
    private String remark;

    @Override
    public String toString() {
        return "DashboardDatabaseModifyDescRequest{" +
                "groupId='" + getGroupId() + '\'' +
                ", dbName='" + getDbName() + '\'' +
                ", remark='" + getRemark() + '\'' +
                '}';
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
}
