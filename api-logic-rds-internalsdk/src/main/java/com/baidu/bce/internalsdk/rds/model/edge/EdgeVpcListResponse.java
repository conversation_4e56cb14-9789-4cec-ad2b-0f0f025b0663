package com.baidu.bce.internalsdk.rds.model.edge;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EdgeVpcListResponse {

    private String marker;
    private String nextMarker;
    private Boolean isTruncated;
    private Integer maxKeys;
    private List<EdgeVpc> vpcs;

    public String getMarker() {
        return marker;
    }

    public void setMarker(String marker) {
        this.marker = marker;
    }

    public String getNextMarker() {
        return nextMarker;
    }

    public void setNextMarker(String nextMarker) {
        this.nextMarker = nextMarker;
    }

    public Boolean getIsTruncated() {
        return isTruncated;
    }

    public void setIsTruncated(Boolean isTruncated) {
        this.isTruncated = isTruncated;
    }

    public Integer getMaxKeys() {
        return maxKeys;
    }

    public void setMaxKeys(Integer maxKeys) {
        this.maxKeys = maxKeys;
    }

    public List<EdgeVpc> getVpcs() {
        return vpcs;
    }

    public void setVpcs(List<EdgeVpc> vpcs) {
        this.vpcs = vpcs;
    }
}
