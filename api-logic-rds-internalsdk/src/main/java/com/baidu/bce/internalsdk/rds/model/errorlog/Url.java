package com.baidu.bce.internalsdk.rds.model.errorlog;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class Url {
    private String url;
    private Date downloadExpires;


    @Override
    public String toString() {
        return "Url{"
                + "url='" + url + '\''
                + ", downloadExpires="
                + downloadExpires + '}';
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getDownloadExpires() {
        return downloadExpires;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setDownloadExpires(Date downloadExpires) {
        this.downloadExpires = downloadExpires;
    }

    public Url url(final String url) {
        this.url = url;
        return this;
    }

    public Url expire(final Date expire) {
        this.downloadExpires = expire;
        return this;
    }

}
