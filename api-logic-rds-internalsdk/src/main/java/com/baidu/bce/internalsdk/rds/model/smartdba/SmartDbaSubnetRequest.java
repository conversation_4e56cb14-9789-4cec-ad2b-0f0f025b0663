package com.baidu.bce.internalsdk.rds.model.smartdba;


import java.util.LinkedHashMap;

public class SmartDbaSubnetRequest {

    private String instance;

    private String role;

    private LinkedHashMap<String, String> subnetId;

    public SmartDbaSubnetRequest() {
    }

    public String getInstance() {
        return instance;
    }

    public void setInstance(String instance) {
        this.instance = instance;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public LinkedHashMap<String, String> getSubnetId() {
        return subnetId;
    }

    public void setSubnetId(LinkedHashMap<String, String> subnetId) {
        this.subnetId = subnetId;
    }

    @Override
    public String toString() {
        return "SmartDbaSubnetRequest{" +
                "instance='" + instance + '\'' +
                ", role='" + role + '\'' +
                ", subnetId=" + subnetId +
                '}';
    }
}
