package com.baidu.bce.internalsdk.rds.model.security;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class SecurityGroupResponse {

    private String eniId;

    private String eniUuid;

    private String eniName;

    private Boolean primary;

    private List<SecurityGroup> securityGroups;

    private List<SecurityGroupRule> activeRules;

    public SecurityGroupResponse() {
    }

    public String getEniId() {
        return eniId;
    }

    public void setEniId(String eniId) {
        this.eniId = eniId;
    }

    public String getEniUuid() {
        return eniUuid;
    }

    public void setEniUuid(String eniUuid) {
        this.eniUuid = eniUuid;
    }

    public String getEniName() {
        return eniName;
    }

    public void setEniName(String eniName) {
        this.eniName = eniName;
    }

    public Boolean getPrimary() {
        return primary;
    }

    public void setPrimary(Boolean primary) {
        this.primary = primary;
    }

    public List<SecurityGroup> getSecurityGroups() {
        return securityGroups;
    }

    public void setSecurityGroups(List<SecurityGroup> securityGroups) {
        this.securityGroups = securityGroups;
    }

    public List<SecurityGroupRule> getActiveRules() {
        return activeRules;
    }

    public void setActiveRules(List<SecurityGroupRule> activeRules) {
        this.activeRules = activeRules;
    }
}
