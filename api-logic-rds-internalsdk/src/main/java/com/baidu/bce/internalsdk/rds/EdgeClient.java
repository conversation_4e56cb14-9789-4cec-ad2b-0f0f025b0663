package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeRegionListResponse;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeSubnetListResponse;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeSubnetResponse;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeVpcListResponse;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeVpcResponse;
import com.baidu.bce.internalsdk.rds.model.edge.GetSubnetsRequest;
import com.baidu.bce.internalsdk.rds.model.edge.GetVpcsRequest;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * 参考wiki：
 *  <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/bPDEaFBbnd/04hxQCVwYK/EJb0xT35QMomSI">边缘计算节点-内部文档</a>
 *  <a href="https://cloud.baidu.com/doc/BEC/s/ul2zrclz9">边缘计算-OpenAPI 文档</a>
 */
public class EdgeClient extends BceClient {

    private static final String SERVICE_NAME = "Edge";

    public EdgeClient(String accessKey, String secretKey, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey, securityToken);
    }

    private BceInternalRequest createEdgeRequest() {
        return super.createAuthorizedRequest();
    }

    /**
     * 查询可以创建 type 类型资源的节点。types 表示取多种 type 的交集
     *
     * @param types types
     * @return EdgeRegionListResponse
     */
    public EdgeRegionListResponse getRegionList(List<String> types) {
        BceInternalRequest bceInternalRequest = createEdgeRequest()
                .path("/v1/node/type/" + types.get(0));
        if (types.size() > 1) {
            bceInternalRequest.queryParam("capabilities",
                    StringUtils.join(types.subList(1, types.size()), ","));
        }
        return bceInternalRequest.get(EdgeRegionListResponse.class);
    }

    public EdgeVpcResponse getVpc(String vpcId) {
        return createEdgeRequest()
                .path("/v2/internal/vpc/" + vpcId)
                .get(EdgeVpcResponse.class);
    }

    public EdgeVpcResponse getVpcByVpcUuid(String vpcUuid) {
        return createEdgeRequest()
                .path("/v2/internal/vpc/" + vpcUuid)
                .get(EdgeVpcResponse.class);
    }

    public EdgeVpcListResponse getVpcs(List<String> vpcIds) {
        GetVpcsRequest getVpcsRequest = new GetVpcsRequest();
        getVpcsRequest.setVpcIds(vpcIds);
        return createEdgeRequest()
                .path("/v2/internal/vpc/list")
                .post(Entity.json(getVpcsRequest), EdgeVpcListResponse.class);
    }

    public EdgeVpcListResponse getVpcsByVpcUuids(List<String> vpcUuids) {
        GetVpcsRequest getVpcsRequest = new GetVpcsRequest();
        getVpcsRequest.setVpcUuids(vpcUuids);
        return createEdgeRequest()
                .path("/v2/internal/vpc/list")
                .post(Entity.json(getVpcsRequest), EdgeVpcListResponse.class);
    }

    public EdgeVpcListResponse getVpcList(String marker, Integer maxKeys, String regionId) {
        BceInternalRequest bceInternalRequest = createEdgeRequest()
                .path("/v2/internal/vpc");
        if (marker != null) {
            bceInternalRequest.queryParam("marker", marker);
        }
        if (maxKeys != null) {
            bceInternalRequest.queryParam("maxKeys", maxKeys);
        }
        if (regionId != null) {
            bceInternalRequest.queryParam("regionId", regionId);
        }
        return bceInternalRequest.get(EdgeVpcListResponse.class);
    }

    public EdgeSubnetResponse getSubnet(String subnetId) {
        return createEdgeRequest()
                .path("/v2/internal/vpc/subnet/" + subnetId)
                .get(EdgeSubnetResponse.class);
    }

    public EdgeSubnetResponse getSubnetBySubnetUuid(String subnetUuid) {
        return createEdgeRequest()
                .path("/v2/internal/vpc/subnet/" + subnetUuid)
                .get(EdgeSubnetResponse.class);
    }

    public EdgeSubnetListResponse getSubnets(List<String> subnetIds) {
        GetSubnetsRequest getSubnetsRequest = new GetSubnetsRequest();
        getSubnetsRequest.setSubnetIds(subnetIds);
        return createEdgeRequest()
                .path("/v2/internal/vpc/subnet/list")
                .post(Entity.json(getSubnetsRequest), EdgeSubnetListResponse.class);
    }

    public EdgeSubnetListResponse getSubnetUuids(List<String> subnetUuids) {
        GetSubnetsRequest getSubnetsRequest = new GetSubnetsRequest();
        getSubnetsRequest.setSubnetUuids(subnetUuids);
        return createEdgeRequest()
                .path("/v2/internal/vpc/subnet/list")
                .post(Entity.json(getSubnetsRequest), EdgeSubnetListResponse.class);
    }
}
