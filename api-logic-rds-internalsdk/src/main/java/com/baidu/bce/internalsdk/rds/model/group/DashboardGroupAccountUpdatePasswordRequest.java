package com.baidu.bce.internalsdk.rds.model.group;


import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DashboardGroupAccountUpdatePasswordRequest {
    @NotNull
    private String groupId;

    @NotNull
    private String accountName;
    private String code;

    private String encryptedPassword;

    @Override
    public String toString() {
        return "DashboardAccountUpdatePasswordRequest{" +
                "groupId='" + getGroupId() + '\'' +
                ", accountName='" + accountName + '\'' +
                ", code='" + code + '\'' +
                ", encryptedPassword='" + encryptedPassword + '\'' +
                '}';
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEncryptedPassword() {
        return encryptedPassword;
    }

    public void setEncryptedPassword(String encryptedPassword) {
        this.encryptedPassword = encryptedPassword;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
}
