package com.baidu.bce.internalsdk.rds.model.snapshot;


import javax.validation.Valid;

/**
 * Created by luping03 on 17/7/11.
 */
public class BackupListResponse<T> extends BaseResponse {

    private BackupInfo<T> result =  new BackupInfo<>();

    public BackupInfo<T> getResult() {
        return result;
    }

    public void setResult(BackupInfo<T> result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "BackupListResponse{"
                + ",result=" + result
                + '}';
    }

    public static class BackupInfo<T> {
        private PageResponse.Page<T> page = new PageResponse.Page<T>();

        private int freeSpaceInMB;

        private int usedSpaceInMB;
        @Valid
        private SnapshotListWithTimeResponse.Period period;

        @Override
        public String toString() {
            return "BackupInfo{"
                    + ",page=" + page
                    + ",freeSpaceInMB=" + freeSpaceInMB
                    + ",usedSpaceInMB=" + usedSpaceInMB
                    + ",period=" + period
                    + '}';
        }

        public PageResponse.Page<T> getPage() {
            return page;
        }

        public void setPage(PageResponse.Page<T> page) {
            this.page = page;
        }

        public SnapshotListWithTimeResponse.Period getPeriod() {
            return period;
        }

        public void setPeriod(SnapshotListWithTimeResponse.Period period) {
            this.period = period;
        }

        public int getFreeSpaceInMB() {
            return freeSpaceInMB;
        }

        public void setFreeSpaceInMB(int freeSpaceInMB) {
            this.freeSpaceInMB = freeSpaceInMB;
        }

        public int getUsedSpaceInMB() {
            return usedSpaceInMB;
        }

        public void setUsedSpaceInMB(int usedSpaceInMB) {
            this.usedSpaceInMB = usedSpaceInMB;
        }

    }

}
