package com.baidu.bce.internalsdk.rds.model.rogroup;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateRoGroupResponse {
    private String roGroupId;
    private String status;
    private String roGroupName;
    private Dict endpoint;
    private String sourceAppId;
    private Integer appAmount;
    private String vpcId;
    private String subnetId;
    private boolean enableDelayOff;
    private Integer delayThreshold;
    private Integer leastAppAmount;
    private boolean balanceReload;
    private boolean bgwGroupExclusive;
    private String bgwGroupId;

    public String getRoGroupId() {
        return roGroupId;
    }

    public void setRoGroupId(String roGroupId) {
        this.roGroupId = roGroupId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRoGroupName() {
        return roGroupName;
    }

    public void setRoGroupName(String roGroupName) {
        this.roGroupName = roGroupName;
    }

    public Dict getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(Dict endpoint) {
        this.endpoint = endpoint;
    }

    public String getSourceAppId() {
        return sourceAppId;
    }

    public void setSourceAppId(String sourceAppId) {
        this.sourceAppId = sourceAppId;
    }

    public Integer getAppAmount() {
        return appAmount;
    }

    public void setAppAmount(Integer appAmount) {
        this.appAmount = appAmount;
    }

    public String getVpcId() {
        return vpcId;
    }

    public void setVpcId(String vpcId) {
        this.vpcId = vpcId;
    }

    public String getSubnetId() {
        return subnetId;
    }

    public void setSubnetId(String subnetId) {
        this.subnetId = subnetId;
    }

    public boolean isEnableDelayOff() {
        return enableDelayOff;
    }

    public void setEnableDelayOff(boolean enableDelayOff) {
        this.enableDelayOff = enableDelayOff;
    }

    public Integer getDelayThreshold() {
        return delayThreshold;
    }

    public void setDelayThreshold(Integer delayThreshold) {
        this.delayThreshold = delayThreshold;
    }

    public Integer getLeastAppAmount() {
        return leastAppAmount;
    }

    public void setLeastAppAmount(Integer leastAppAmount) {
        this.leastAppAmount = leastAppAmount;
    }

    public boolean isBalanceReload() {
        return balanceReload;
    }

    public void setBalanceReload(boolean balanceReload) {
        this.balanceReload = balanceReload;
    }

    public boolean isBgwGroupExclusive() {
        return bgwGroupExclusive;
    }

    public void setBgwGroupExclusive(boolean bgwGroupExclusive) {
        this.bgwGroupExclusive = bgwGroupExclusive;
    }

    public String getBgwGroupId() {
        return bgwGroupId;
    }

    public void setBgwGroupId(String bgwGroupId) {
        this.bgwGroupId = bgwGroupId;
    }

}
