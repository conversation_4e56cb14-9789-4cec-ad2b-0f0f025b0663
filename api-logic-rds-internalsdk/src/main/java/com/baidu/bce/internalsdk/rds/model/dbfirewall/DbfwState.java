package com.baidu.bce.internalsdk.rds.model.dbfirewall;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/03.
 */
public class DbfwState {

    private int id;             // 自增主键

    private String appId;       // 代理实例ID

    private int dbfwState;      // DBFW状态，0关闭，1告警，2阻断

    private String updateTime;  // DBFW状态更新时间 yyyy-MM-ddThh:mm:ssZ

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public int getDbfwState() {
        return dbfwState;
    }

    public void setDbfwState(int dbfwState) {
        this.dbfwState = dbfwState;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "DbfwState{"
                + "id=" + id
                + ", appId='" + appId + '\''
                + ", dbfwState=" + dbfwState
                + ", updateTime='" + updateTime + '\''
                + '}';
    }
}
