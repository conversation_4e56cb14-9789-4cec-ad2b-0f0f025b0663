package com.baidu.bce.internalsdk.rds.model.slowlog;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DownloadUrlRequest {
    @IdPermission
    private String instanceId;
    private Integer downloadValidTime = 300;

    private Integer downloadValidTimeInSec = 43200;

    @Override
    public String toString() {
        return "DownloadUrlRequest{"
                + "instanceId='" + instanceId + '\''
                + ", downloadValidTime=" + downloadValidTime
                + '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getDownloadValidTime() {
        return downloadValidTime;
    }

    public void setDownloadValidTime(Integer downloadValidTime) {
        this.downloadValidTime = downloadValidTime;
    }

    public Integer getDownloadValidTimeInSec() {
        return downloadValidTimeInSec;
    }

    public void setDownloadValidTimeInSec(Integer downloadValidTimeInSec) {
        this.downloadValidTimeInSec = downloadValidTimeInSec;
    }
}
