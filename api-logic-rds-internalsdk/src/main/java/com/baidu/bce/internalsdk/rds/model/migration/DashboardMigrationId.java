package com.baidu.bce.internalsdk.rds.model.migration;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/9.
 */
public class DashboardMigrationId {
    @NotNull
    @IdPermission
    private String instanceId;

    @NotNull
    private String migrationId;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DashboardMigrationId{");
        sb.append("instanceId='").append(instanceId).append('\'');
        sb.append(", migrationId='").append(migrationId).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getMigrationId() {
        return migrationId;
    }

    public void setMigrationId(String migrationId) {
        this.migrationId = migrationId;
    }
}
