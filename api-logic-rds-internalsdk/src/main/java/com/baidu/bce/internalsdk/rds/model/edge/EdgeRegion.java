package com.baidu.bce.internalsdk.rds.model.edge;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * 参考wiki：
 *  <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/bPDEaFBbnd/04hxQCVwYK/EJb0xT35QMomSI">边缘计算节点-内部文档</a>
 *  <a href="https://cloud.baidu.com/doc/BEC/s/ul2zrclz9">边缘计算-OpenAPI 文档</a>
 *
 * 整体结构如下：
 *  {
 *     "region": "EAST_CHINA",
 *     "name": "华东",
 *     "country": "CHINA",
 *     "countryName": "中国",
 *     "cityList": [
 *         {
 *             "city": "HANGZHOU",
 *             "name": "杭州",
 *             "serviceProviderList": [
 *                 {
 *                     "serviceProvider": "CHINA_MOBILE",
 *                     "name": "移动",
 *                     "regionId": "cn-hangzhou-cm"
 *                 }
 *             ]
 *         }
 *     ]
 * }
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class EdgeRegion {

    private String region;

    private String name;

    private String country;

    private String countryName;

    private List<EdgeCity> cityList;

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public List<EdgeCity> getCityList() {
        return cityList;
    }

    public void setCityList(List<EdgeCity> cityList) {
        this.cityList = cityList;
    }

}
