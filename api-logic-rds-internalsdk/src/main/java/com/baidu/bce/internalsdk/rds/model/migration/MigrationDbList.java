package com.baidu.bce.internalsdk.rds.model.migration;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/10/27.
 */
public class MigrationDbList {
    private List<String> databases;
    private Boolean lockTablesPrivilege;
    private Boolean fullIncreasePrivilege;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("MigrationDbList{");
        sb.append("databases=").append(databases);
        sb.append(", lockTablesPrivilege=").append(lockTablesPrivilege);
        sb.append(", fullIncreasePrivilege=").append(fullIncreasePrivilege);
        sb.append('}');
        return sb.toString();
    }

    public List<String> getDatabases() {
        return databases;
    }

    public void setDatabases(List<String> databases) {
        this.databases = databases;
    }

    public Boolean getLockTablesPrivilege() {
        return lockTablesPrivilege;
    }

    public void setLockTablesPrivilege(Boolean lockTablesPrivilege) {
        this.lockTablesPrivilege = lockTablesPrivilege;
    }

    public Boolean getFullIncreasePrivilege() {
        return fullIncreasePrivilege;
    }

    public void setFullIncreasePrivilege(Boolean fullIncreasePrivilege) {
        this.fullIncreasePrivilege = fullIncreasePrivilege;
    }

    public MigrationDbList withDatabases(final List<String> databases) {
        this.databases = databases;
        return this;
    }

    public MigrationDbList withLockTablesPriv(final Boolean lockTablesPriv) {
        this.lockTablesPrivilege = lockTablesPriv;
        return this;
    }

    public MigrationDbList withFullIncreasePriv(final Boolean fullIncreasePriv) {
        this.fullIncreasePrivilege = fullIncreasePriv;
        return this;
    }

}
