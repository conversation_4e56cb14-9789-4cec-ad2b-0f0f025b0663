package com.baidu.bce.internalsdk.rds.model.resource;


import javax.validation.constraints.NotNull;
import java.util.List;

public class BindResourceRequest {

    private boolean force = true;

    @NotNull
    private String resourceGroupId;

    private List<BindResource> bindings;

    public BindResourceRequest() {
    }

    public boolean getForce() {
        return force;
    }

    public void setForce(boolean force) {
        this.force = force;
    }

    public String getResourceGroupId() {
        return resourceGroupId;
    }

    public void setResourceGroupId(String resourceGroupId) {
        this.resourceGroupId = resourceGroupId;
    }

    public List<BindResource> getBindings() {
        return bindings;
    }

    public void setBindings(List<BindResource> bindings) {
        this.bindings = bindings;
    }
}
