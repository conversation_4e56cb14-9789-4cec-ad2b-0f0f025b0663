package com.baidu.bce.internalsdk.rds.model.slowlog;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.UUID;

/**
 * Created by baidu on 15/11/27.
 */
public class OpenapiSlowlog implements Comparable<OpenapiSlowlog> {
    private String slowlogId = UUID.randomUUID().toString();
    private Long slowlogSizeInBytes;

    private Date slowlogStartTime = new Date(System.currentTimeMillis());
    private Date slowlogEndTime = new Date(System.currentTimeMillis() + 10000);


    @Override
    public int compareTo(@NotNull OpenapiSlowlog o) {
        return this.slowlogStartTime.compareTo(o.slowlogStartTime);
    }

    public String getSlowlogId() {
        return slowlogId;
    }

    public void setSlowlogId(String slowlogId) {
        this.slowlogId = slowlogId;
    }

    public Long getSlowlogSizeInBytes() {
        return slowlogSizeInBytes;
    }

    public void setSlowlogSizeInBytes(Long slowlogSizeInBytes) {
        this.slowlogSizeInBytes = slowlogSizeInBytes;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getSlowlogStartTime() {
        return slowlogStartTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setSlowlogStartTime(Date slowlogStartTime) {
        this.slowlogStartTime = slowlogStartTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getSlowlogEndTime() {
        return slowlogEndTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setSlowlogEndTime(Date slowlogEndTime) {
        this.slowlogEndTime = slowlogEndTime;
    }

    public OpenapiSlowlog slowlogId(final String slowlogId) {
        this.slowlogId = slowlogId;
        return this;
    }

    public OpenapiSlowlog slowlogSizeInBytes(final Long slowlogSizeInBytes) {
        this.slowlogSizeInBytes = slowlogSizeInBytes;
        return this;
    }

    public OpenapiSlowlog slowStartTime(final Date slowStartTime) {
        this.slowlogStartTime = slowStartTime;
        return this;
    }

    public OpenapiSlowlog slowEndTime(final Date slowEndTime) {
        this.slowlogEndTime = slowEndTime;
        return this;
    }

}
