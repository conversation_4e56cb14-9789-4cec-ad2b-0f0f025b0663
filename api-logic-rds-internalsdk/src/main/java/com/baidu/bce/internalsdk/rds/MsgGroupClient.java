package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.rds.model.msg.SendGroupMsgRequest;
import endpoint.EndpointManager;

public class MsgGroupClient extends BceClient {

    private static final String SERVICE_NAME = "GroupMsg";

    private String accessToken;


    public MsgGroupClient(String accessToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME));
        this.accessToken = accessToken;
    }

    public BceInternalRequest createMsgGroupRequest() {
        return super.createRequest();
    }


    public void sendGroupMessage(SendGroupMsgRequest sendMessageRequest) {
        this.createMsgGroupRequest()
                .path("/msg/groupmsgsend")
                .queryParam("access_token", accessToken)
                .post(Entity.json(sendMessageRequest));
    }
}
