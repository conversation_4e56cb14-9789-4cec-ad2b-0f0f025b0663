package com.baidu.bce.internalsdk.rds.model.smartdba;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DiskInfoResponse {

    private Boolean success;

    private BaseResponse.Message message = new BaseResponse.Message();

    private String status;

    private BaseResponse result;

    public DiskInfoResponse() {
    }

    public DiskInfoResponse(Boolean success, BaseResponse.Message message, String status, BaseResponse result) {
        this.success = success;
        this.message = message;
        this.status = status;
        this.result = result;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public BaseResponse.Message getMessage() {
        return message;
    }

    public void setMessage(BaseResponse.Message message) {
        this.message = message;
    }

    public BaseResponse getResult() {
        return result;
    }

    public void setResult(BaseResponse result) {
        this.result = result;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "DiskInfoResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", status='" + status + '\'' +
                ", result=" + result +
                '}';
    }
}
