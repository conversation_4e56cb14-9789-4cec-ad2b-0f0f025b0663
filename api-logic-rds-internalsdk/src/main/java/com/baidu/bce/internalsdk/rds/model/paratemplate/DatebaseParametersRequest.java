package com.baidu.bce.internalsdk.rds.model.paratemplate;

public class DatebaseParametersRequest {

    private String dbType;

    private String dbVersion;

    private String templateId;

    public DatebaseParametersRequest() {
    }


    public DatebaseParametersRequest(String dbType, String dbVersion, String templateId) {
        this.dbType = dbType;
        this.dbVersion = dbVersion;
        this.templateId = templateId;
    }

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public String getDbVersion() {
        return dbVersion;
    }

    public void setDbVersion(String dbVersion) {
        this.dbVersion = dbVersion;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    @Override
    public String toString() {
        return "DatebaseParametersRequest{" +
                "dbType='" + dbType + '\'' +
                ", dbVersion='" + dbVersion + '\'' +
                ", templateId='" + templateId + '\'' +
                '}';
    }
}
