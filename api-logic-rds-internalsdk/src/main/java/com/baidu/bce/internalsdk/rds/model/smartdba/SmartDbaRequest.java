package com.baidu.bce.internalsdk.rds.model.smartdba;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

public class SmartDbaRequest {

    @NotNull
    @IdPermission
    private String instanceId;

    private Integer listNo;

    public SmartDbaRequest() {
    }

    public SmartDbaRequest(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getListNo() {
        return listNo;
    }

    public void setListNo(Integer listNo) {
        this.listNo = listNo;
    }

    @Override
    public String toString() {
        return "SmartDbaRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", listNo=" + listNo +
                '}';
    }
}
