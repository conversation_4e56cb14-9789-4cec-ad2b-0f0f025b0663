package com.baidu.bce.internalsdk.rds.model.paratemplate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ParaTemplateApplyHistory {

    private String appIdShort;

    private String appId;

    private String status;

    private String applyTime;

    private String errMsg;

    private String applyId;

    private String instanceId;

    private String azone;

    private String engine;

    private String engineVersion;

    private String instanceName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Date instanceCreateTime;

    private String applicationType;

    private String instanceStatus;

    private String region;

    public ParaTemplateApplyHistory() {
    }


    public String getAppIdShort() {
        return appIdShort;
    }

    public void setAppIdShort(String appIdShort) {
        this.appIdShort = appIdShort;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }


    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getAzone() {
        return azone;
    }

    public void setAzone(String azone) {
        this.azone = azone;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getEngineVersion() {
        return engineVersion;
    }

    public void setEngineVersion(String engineVersion) {
        this.engineVersion = engineVersion;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getInstanceCreateTime() {
        return instanceCreateTime;
    }
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setInstanceCreateTime(Date instanceCreateTime) {
        this.instanceCreateTime = instanceCreateTime;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }

    public String getInstanceStatus() {
        return instanceStatus;
    }

    public void setInstanceStatus(String instanceStatus) {
        this.instanceStatus = instanceStatus;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public String toString() {
        return "ParaTemplateApplyHistory{" +
                "appIdShort='" + appIdShort + '\'' +
                ", appId='" + appId + '\'' +
                ", status='" + status + '\'' +
                ", applyTime='" + applyTime + '\'' +
                ", errMsg='" + errMsg + '\'' +
                ", applyId='" + applyId + '\'' +
                ", instanceId='" + instanceId + '\'' +
                ", azone='" + azone + '\'' +
                ", engine='" + engine + '\'' +
                ", engineVersion='" + engineVersion + '\'' +
                ", instanceName='" + instanceName + '\'' +
                ", instanceCreateTime=" + instanceCreateTime +
                ", applicationType='" + applicationType + '\'' +
                ", instanceStatus='" + instanceStatus + '\'' +
                ", region='" + region + '\'' +
                '}';
    }
}
