package com.baidu.bce.internalsdk.rds.model.paratemplate;


import javax.validation.Valid;
import javax.validation.constraints.NotNull;

public class ParaTemplateApplyRequest {

    @NotNull
    @Valid
    private String ApplyId;

    public ParaTemplateApplyRequest() {
    }

    public ParaTemplateApplyRequest(String applyId) {
        ApplyId = applyId;
    }

    public String getApplyId() {
        return ApplyId;
    }

    public void setApplyId(String applyId) {
        ApplyId = applyId;
    }

    @Override
    public String toString() {
        return "ParaTemplateApplyRequest{" +
                "ApplyId='" + ApplyId + '\'' +
                '}';
    }
}
