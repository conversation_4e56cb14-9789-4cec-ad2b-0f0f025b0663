package com.baidu.bce.internalsdk.rds.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RdsMinorVersion {

    private String dbVersion;
    private String minorVersion;
    private String rdsMinorVersion;
    private String featureDescription;


    public String getDbVersion() {
        return dbVersion;
    }

    public void setDbVersion(String dbVersion) {
        this.dbVersion = dbVersion;
    }

    public String getMinorVersion() {
        return minorVersion;
    }

    public void setMinorVersion(String minorVersion) {
        this.minorVersion = minorVersion;
    }

    public String getRdsMinorVersion() {
        return rdsMinorVersion;
    }

    public void setRdsMinorVersion(String rdsMinorVersion) {
        this.rdsMinorVersion = rdsMinorVersion;
    }

    public String getFeatureDescription() {
        return featureDescription;
    }

    public void setFeatureDescription(String featureDescription) {
        this.featureDescription = featureDescription;
    }
}