package com.baidu.bce.internalsdk.rds.model.account;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TableLevelDbScopeResponse {

    private List<String> all;
    private List<String> readOnly;
    private List<String> readWrite;

    public List<String> getAll() {
        return all;
    }

    public void setAll(List<String> all) {
        this.all = all;
    }

    public List<String> getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(List<String> readOnly) {
        this.readOnly = readOnly;
    }

    public List<String> getReadWrite() {
        return readWrite;
    }

    public void setReadWrite(List<String> readWrite) {
        this.readWrite = readWrite;
    }
}
