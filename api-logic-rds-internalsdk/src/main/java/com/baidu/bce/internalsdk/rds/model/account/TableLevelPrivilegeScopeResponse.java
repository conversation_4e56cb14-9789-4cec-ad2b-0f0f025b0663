package com.baidu.bce.internalsdk.rds.model.account;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TableLevelPrivilegeScopeResponse {

    private TableLevelGlobalScopeResponse global;
    private TableLevelDbScopeResponse database;
    private TableLevelDbScopeResponse table;


    public TableLevelGlobalScopeResponse getGlobal() {
        return global;
    }

    public void setGlobal(TableLevelGlobalScopeResponse global) {
        this.global = global;
    }

    public TableLevelDbScopeResponse getDatabase() {
        return database;
    }

    public void setDatabase(TableLevelDbScopeResponse database) {
        this.database = database;
    }

    public TableLevelDbScopeResponse getTable() {
        return table;
    }

    public void setTable(TableLevelDbScopeResponse table) {
        this.table = table;
    }
}
