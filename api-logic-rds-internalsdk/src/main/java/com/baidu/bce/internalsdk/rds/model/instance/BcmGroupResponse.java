package com.baidu.bce.internalsdk.rds.model.instance;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BcmGroupResponse {

    private Integer id;
    private String name;
    private String serviceName;
    private String typeName;
    private String region;
    private String userId;
    private String uuid;
    private Integer count;
    private String serviceNameAlias;
    private String typeNameAlias;
    private String regionAlias;
    private String tagKey;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getServiceNameAlias() {
        return serviceNameAlias;
    }

    public void setServiceNameAlias(String serviceNameAlias) {
        this.serviceNameAlias = serviceNameAlias;
    }

    public String getTypeNameAlias() {
        return typeNameAlias;
    }

    public void setTypeNameAlias(String typeNameAlias) {
        this.typeNameAlias = typeNameAlias;
    }

    public String getRegionAlias() {
        return regionAlias;
    }

    public void setRegionAlias(String regionAlias) {
        this.regionAlias = regionAlias;
    }

    public String getTagKey() {
        return tagKey;
    }

    public void setTagKey(String tagKey) {
        this.tagKey = tagKey;
    }
}
