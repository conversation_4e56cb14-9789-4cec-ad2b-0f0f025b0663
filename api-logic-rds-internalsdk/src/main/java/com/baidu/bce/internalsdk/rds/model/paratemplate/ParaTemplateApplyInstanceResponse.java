package com.baidu.bce.internalsdk.rds.model.paratemplate;


import java.util.ArrayList;
import java.util.List;

public class ParaTemplateApplyInstanceResponse {

    private List<ApplyInstance> applyInstanceList = new ArrayList<>();

    public ParaTemplateApplyInstanceResponse() {
    }

    public ParaTemplateApplyInstanceResponse(List<ApplyInstance> applyInstanceList) {
        this.applyInstanceList = applyInstanceList;
    }

    public List<ApplyInstance> getApplyInstanceList() {
        return applyInstanceList;
    }

    public void setApplyInstanceList(List<ApplyInstance> applyInstanceList) {
        this.applyInstanceList = applyInstanceList;
    }

    @Override
    public String toString() {
        return "ParaTemplateApplyInstanceResponse{" +
                "applyInstanceList=" + applyInstanceList +
                '}';
    }
}
