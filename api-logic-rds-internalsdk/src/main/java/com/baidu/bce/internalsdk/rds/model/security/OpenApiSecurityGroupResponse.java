package com.baidu.bce.internalsdk.rds.model.security;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class OpenApiSecurityGroupResponse {

    private String eniId;

    private String eniUuid;

    private String eniName;

    private Boolean primary;

    private List<OpenApiSecurityGroup> groups;

    private List<SecurityGroupRule> activeRules;

    public OpenApiSecurityGroupResponse() {
    }

    public String getEniId() {
        return eniId;
    }

    public void setEniId(String eniId) {
        this.eniId = eniId;
    }

    public String getEniUuid() {
        return eniUuid;
    }

    public void setEniUuid(String eniUuid) {
        this.eniUuid = eniUuid;
    }

    public String getEniName() {
        return eniName;
    }

    public void setEniName(String eniName) {
        this.eniName = eniName;
    }

    public Boolean getPrimary() {
        return primary;
    }

    public void setPrimary(Boolean primary) {
        this.primary = primary;
    }

    public List<OpenApiSecurityGroup> getGroups() {
        return groups;
    }

    public void setGroups(List<OpenApiSecurityGroup> groups) {
        this.groups = groups;
    }

    public List<SecurityGroupRule> getActiveRules() {
        return activeRules;
    }

    public void setActiveRules(List<SecurityGroupRule> activeRules) {
        this.activeRules = activeRules;
    }
}
