package com.baidu.bce.internalsdk.rds.model.database;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/8.
 */
public class DatabaseCreateRequest {
    private String dbName;
    private String characterSetName;
    private String remark;
    // pg新增参数
    private String collate;
    private String ctype;
    private String owner;

    public  DatabaseCreateRequest() {

    }

    public  DatabaseCreateRequest(Database db) {
        this.dbName = db.getDbName();
        this.characterSetName = db.getCharacterSetName();
        this.remark = db.getRemark();
        this.collate = db.getCollate();
        this.ctype = db.getCtype();
        this.owner = db.getOwner();
    }

    @Override
    public String toString() {
        return "DatabaseCreateRequest{" +
                "dbName='" + dbName + '\'' +
                ", characterSetName='" + characterSetName + '\'' +
                ", remark='" + remark + '\'' +
                ", collate='" + collate + '\'' +
                ", ctype='" + ctype + '\'' +
                ", owner='" + owner + '\'' +
                '}';
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getCharacterSetName() {
        return characterSetName;
    }

    public void setCharacterSetName(String characterSetName) {
        this.characterSetName = characterSetName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCollate() {
        return collate;
    }

    public void setCollate(String collate) {
        this.collate = collate;
    }

    public String getCtype() {
        return ctype;
    }

    public void setCtype(String ctype) {
        this.ctype = ctype;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }
}
