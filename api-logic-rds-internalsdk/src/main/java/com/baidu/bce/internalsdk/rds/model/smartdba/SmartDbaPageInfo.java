package com.baidu.bce.internalsdk.rds.model.smartdba;

public class SmartDbaPageInfo {

    private String indexLength;

    private String tableSchema;

    private String tableName;

    private String dataLength;

    private String engine;

    private String tableRows;

    private String dataFree;

    private String dataFreePer;

    private String avgRowLength;

    private String tableLength;

    private String tableLengthPer;

    public SmartDbaPageInfo() {
    }

    public SmartDbaPageInfo(String indexLength, String tableSchema, String tableName,
                            String dataLength, String engine, String tableRows, String dataFree,
                            String dataFreePer, String avgRowLength, String tableLength, String tableLengthPer) {
        this.indexLength = indexLength;
        this.tableSchema = tableSchema;
        this.tableName = tableName;
        this.dataLength = dataLength;
        this.engine = engine;
        this.tableRows = tableRows;
        this.dataFree = dataFree;
        this.dataFreePer = dataFreePer;
        this.avgRowLength = avgRowLength;
        this.tableLength = tableLength;
        this.tableLengthPer = tableLengthPer;
    }

    public String getIndexLength() {
        return indexLength;
    }

    public void setIndexLength(String indexLength) {
        this.indexLength = indexLength;
    }

    public String getTableSchema() {
        return tableSchema;
    }

    public void setTableSchema(String tableSchema) {
        this.tableSchema = tableSchema;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getDataLength() {
        return dataLength;
    }

    public void setDataLength(String dataLength) {
        this.dataLength = dataLength;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getTableRows() {
        return tableRows;
    }

    public void setTableRows(String tableRows) {
        this.tableRows = tableRows;
    }

    public String getDataFree() {
        return dataFree;
    }

    public void setDataFree(String dataFree) {
        this.dataFree = dataFree;
    }

    public String getDataFreePer() {
        return dataFreePer;
    }

    public void setDataFreePer(String dataFreePer) {
        this.dataFreePer = dataFreePer;
    }

    public String getAvgRowLength() {
        return avgRowLength;
    }

    public void setAvgRowLength(String avgRowLength) {
        this.avgRowLength = avgRowLength;
    }

    public String getTableLength() {
        return tableLength;
    }

    public void setTableLength(String tableLength) {
        this.tableLength = tableLength;
    }

    public String getTableLengthPer() {
        return tableLengthPer;
    }

    public void setTableLengthPer(String tableLengthPer) {
        this.tableLengthPer = tableLengthPer;
    }

    @Override
    public String toString() {
        return "SmartDbaPageInfo{" +
                "indexLength='" + indexLength + '\'' +
                ", tableSchema='" + tableSchema + '\'' +
                ", tableName='" + tableName + '\'' +
                ", dataLength='" + dataLength + '\'' +
                ", engine='" + engine + '\'' +
                ", tableRows='" + tableRows + '\'' +
                ", dataFree='" + dataFree + '\'' +
                ", dataFreePer='" + dataFreePer + '\'' +
                ", avgRowLength='" + avgRowLength + '\'' +
                ", tableLength='" + tableLength + '\'' +
                ", tableLengthPer='" + tableLengthPer + '\'' +
                '}';
    }
}
