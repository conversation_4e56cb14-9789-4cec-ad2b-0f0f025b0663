package com.baidu.bce.internalsdk.rds.model.database;


import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/19.
 */
public class DashboardDatabaseCreateRequest {
    @NotNull
    private String instanceId;
    @Valid
    private Database database;


    @Override
    public String toString() {
        return "DashboardDatabaseCreateRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", database=" + database +
                '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Database getDatabase() {
        return database;
    }

    public void setDatabase(Database database) {
        this.database = database;
    }
}
