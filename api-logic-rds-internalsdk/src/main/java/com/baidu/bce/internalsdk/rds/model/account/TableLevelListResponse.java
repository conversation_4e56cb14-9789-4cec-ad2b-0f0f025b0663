package com.baidu.bce.internalsdk.rds.model.account;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TableLevelListResponse {

    private List<TableLevelAccountListResponse> users;

    public List<TableLevelAccountListResponse> getUsers() {
        return users;
    }

    public void setUsers(List<TableLevelAccountListResponse> users) {
        this.users = users;
    }
}
