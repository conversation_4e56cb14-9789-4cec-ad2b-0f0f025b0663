package com.baidu.bce.internalsdk.rds.model.smartdba;

import java.util.ArrayList;
import java.util.List;

public class SmartDbaClusterResponse {

    private String region;

    private Boolean showEnable;

    private SmartDbaTopo topology;

    public SmartDbaClusterResponse() {
    }

    public SmartDbaClusterResponse(String region, Boolean showEnable, SmartDbaTopo topology) {
        this.region = region;
        this.showEnable = showEnable;
        this.topology = topology;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Boolean getShowEnable() {
        return showEnable;
    }

    public void setShowEnable(Boolean showEnable) {
        this.showEnable = showEnable;
    }

    public SmartDbaTopo getTopology() {
        return topology;
    }

    public void setTopology(SmartDbaTopo topology) {
        this.topology = topology;
    }

    @Override
    public String toString() {
        return "SmartDbaClusterResponse{" +
                "region='" + region + '\'' +
                ", showEnable=" + showEnable +
                ", topology=" + topology +
                '}';
    }

    public class SmartDbaTopo {

        private List<String> rdsproxy = new ArrayList<>();

        private List<String> master = new ArrayList<>();

        private List<String> readReplica = new ArrayList<>();

        public SmartDbaTopo() {
        }

        public SmartDbaTopo(List<String> rdsproxy, List<String> master, List<String> readReplica) {
            this.rdsproxy = rdsproxy;
            this.master = master;
            this.readReplica = readReplica;
        }

        public List<String> getRdsproxy() {
            return rdsproxy;
        }

        public void setRdsproxy(List<String> rdsproxy) {
            this.rdsproxy = rdsproxy;
        }

        public List<String> getMaster() {
            return master;
        }

        public void setMaster(List<String> master) {
            this.master = master;
        }

        public List<String> getReadReplica() {
            return readReplica;
        }

        public void setReadReplica(List<String> readReplica) {
            this.readReplica = readReplica;
        }

        @Override
        public String toString() {
            return "SmartDbaTopo{" +
                    "rdsproxy=" + rdsproxy +
                    ", master=" + master +
                    ", readReplica=" + readReplica +
                    '}';
        }
    }


}
