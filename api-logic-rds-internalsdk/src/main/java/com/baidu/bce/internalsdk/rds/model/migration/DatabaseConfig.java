package com.baidu.bce.internalsdk.rds.model.migration;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/9.
 */
public class DatabaseConfig {
    @NotNull
    private String remoteIp;
    private Integer remotePort;
    @NotNull
    private String remoteUser;
    @NotNull
    private String remotePassword;

    public DatabaseConfig() {

    }

    public DatabaseConfig(String remoteIp, Integer remotePort, String remoteUser, String remotePassword) {
        this.remoteIp = remoteIp;
        this.remotePort = remotePort;
        this.remoteUser = remoteUser;
        this.remotePassword = remotePassword;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DatabaseConfig{");
        sb.append("remoteIp='").append(remoteIp).append('\'');
        sb.append(", remotePort=").append(remotePort);
        sb.append(", remoteUser='").append(remoteUser).append('\'');
        sb.append(", remotePassword='").append(remotePassword).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public Integer getRemotePort() {
        return remotePort;
    }

    public void setRemotePort(Integer remotePort) {
        this.remotePort = remotePort;
    }

    public String getRemoteUser() {
        return remoteUser;
    }

    public void setRemoteUser(String remoteUser) {
        this.remoteUser = remoteUser;
    }

    public String getRemotePassword() {
        return remotePassword;
    }

    public void setRemotePassword(String remotePassword) {
        this.remotePassword = remotePassword;
    }

    public DatabaseConfig withRemoteIp(final String remoteIp) {
        this.remoteIp = remoteIp;
        return this;
    }

    public DatabaseConfig withRemotePort(final Integer remotePort) {
        this.remotePort = remotePort;
        return this;
    }

    public DatabaseConfig withRemoteUser(final String remoteUser) {
        this.remoteUser = remoteUser;
        return this;
    }

    public DatabaseConfig withRemotePassword(final String remotePassword) {
        this.remotePassword = remotePassword;
        return this;
    }


}
