package com.baidu.bce.internalsdk.rds.model.edge;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * 参考wiki：
 *  <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/bPDEaFBbnd/04hxQCVwYK/EJb0xT35QMomSI">边缘计算节点-内部文档</a>
 *  <a href="https://cloud.baidu.com/doc/BEC/s/ul2zrclz9">边缘计算-OpenAPI 文档</a>
 *
 * 整体结构如下：
 *  {
 *     "city": "HANGZHOU",
 *     "name": "杭州",
 *     "serviceProviderList": [
 *         {
 *             "serviceProvider": "CHINA_MOBILE",
 *             "name": "移动",
 *             "regionId": "cn-hangzhou-cm"
 *         }
 *     ]
 * }
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class EdgeCity {

    private String city;

    private String name;

    private List<EdgeServiceProvider> serviceProviderList;

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<EdgeServiceProvider> getServiceProviderList() {
        return serviceProviderList;
    }

    public void setServiceProviderList(List<EdgeServiceProvider> serviceProviderList) {
        this.serviceProviderList = serviceProviderList;
    }

}
