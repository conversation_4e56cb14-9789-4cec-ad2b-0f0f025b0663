package com.baidu.bce.internalsdk.rds.model.paratemplate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ParaTemplateModel {

    private String id;

    private String name;

    private String dbType;

    private String dbVersion;

    private Integer count;

    private String desc;

    private Boolean dynamic;

    private String createTime;

    private String updateTime;

    private String idShow;

    private List<ParaTemplateDetail> para;

    private String type;

    private String property;

    public ParaTemplateModel() {
    }

    public ParaTemplateModel(String id, String name, String dbType, String dbVersion, Integer count, String desc,
                             Boolean dynamic, String createTime, String updateTime, List<ParaTemplateDetail> para,
                             String idShow) {
        this.id = id;
        this.name = name;
        this.dbType = dbType;
        this.dbVersion = dbVersion;
        this.count = count;
        this.desc = desc;
        this.dynamic = dynamic;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.para = para;
        this.idShow = idShow;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getDbVersion() {
        return dbVersion;
    }

    public void setDbVersion(String dbVersion) {
        this.dbVersion = dbVersion;
    }

    public List<ParaTemplateDetail> getPara() {
        return para;
    }

    public void setPara(List<ParaTemplateDetail> para) {
        this.para = para;
    }

    public Boolean getDynamic() {
        return dynamic;
    }

    public void setDynamic(Boolean dynamic) {
        this.dynamic = dynamic;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getIdShow() {
        return idShow;
    }

    public void setIdShow(String idShow) {
        this.idShow = idShow;
    }

    @Override
    public String toString() {
        return "ParaTemplateModel{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", dbType='" + dbType + '\'' +
                ", dbVersion='" + dbVersion + '\'' +
                ", count=" + count +
                ", desc='" + desc + '\'' +
                ", dynamic=" + dynamic +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", idShow='" + idShow + '\'' +
                ", para=" + para +
                '}';
    }
}
