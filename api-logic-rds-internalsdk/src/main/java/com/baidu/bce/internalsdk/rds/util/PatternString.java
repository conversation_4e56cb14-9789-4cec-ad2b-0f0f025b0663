package com.baidu.bce.internalsdk.rds.util;

/**
 * Created by luping03 on 18/5/31.
 */
public class PatternString {

    // 1.由小写字母和数字组成；2.以小写字母开头；3.长度在3-30之间。
    public static final String PATTERN_ADDRESS = "[a-z][a-z0-9\\-]{2,29}";

    // 支持大小写字母、数字以及-_ /.等特殊字符，必须以字母开头，长度1-64
    public static final String PATTERN_INSTANCE_NAME = "^\\p{L}[\\p{L}0-9._\\-\\/]{0,63}$";

    public static final String OPENAPI_PATTERN_INSTANCE_NAME = "[a-z][a-z0-9\\-_]{2,29}";

    // 由字母、数字或下划线组成，长度6～32位
    public static final String PATTERN_ACCOUNT_PW = "\\w{6,32}"; // "[a-zA-Z1-9_]{6,32}";

    // 由小写字母、数字、下划线组成、字母开头，字母或数字结尾，最长16个字符
    public static final String PATTERN_ACCOUNT_NAME = "[a-z][a-z0-9_]{0,14}[a-z0-9]";

    public static final String PARAMETER_TEMPLATE_NAME = "^[a-zA-Z](?=.*\\d)(?=.*[a-zA-Z])" +
            "(?=.*[_.()])[\\da-zA-Z_.()]{7,63}$";

//    public static void main(String[] args) {
//        Boolean address = Pattern.matches(PatternString.PATTERN_ADDRESS, "q1./");
//        Boolean name = Pattern.matches(PatternString.PATTERN_INSTANCE_NAME, "name-01");
//        Boolean pw = Pattern.matches(PatternString.PATTERN_ACCOUNT_PW, "1234567");
//        Boolean accountName = Pattern.matches(PatternString.PATTERN_ACCOUNT_NAME, "b123456789abcd_2");
//        System.out.println(name);
//    }
}
