package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.internalsdk.rds.model.ViewPageResponse;

public class ViewPageBaseResponse extends BaseResponse {

    private com.baidu.bce.internalsdk.rds.model.ViewPageResponse result;

    public ViewPageBaseResponse() {
    }

    public com.baidu.bce.internalsdk.rds.model.ViewPageResponse getResult() {
        return result;
    }

    public void setResult(ViewPageResponse result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "ViewPageBaseResponse{" +
                "result=" + result +
                '}';
    }
}
