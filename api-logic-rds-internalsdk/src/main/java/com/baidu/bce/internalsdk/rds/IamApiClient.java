package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.rds.model.iam.IamDecryptRequest;
import com.baidu.bce.internalsdk.rds.model.iam.IamDecryptResponse;
import com.baidu.bce.internalsdk.rds.model.iam.IamEncryptRequest;
import com.baidu.bce.internalsdk.rds.model.iam.IamEncryptResponse;
import endpoint.EndpointManager;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/8/18.
 */
public class IamApiClient extends BceClient {
    public static final String SERVICE_NAME = "IAM";

    public IamApiClient(String ak, String sk) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), ak, sk);
    }

    public IamApiClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public IamApiClient(String endpoint) {
        super(endpoint);
    }

    public IamEncryptResponse encrypt(IamEncryptRequest encryptRequest) {
        return createAuthorizedRequest().path("/BCE-CRED/ciphers").keyOnlyQueryParam("encrypt")
                .post(Entity.json(encryptRequest), IamEncryptResponse.class);
    }

    public IamDecryptResponse decrypt(IamDecryptRequest decryptRequest) {
        return createAuthorizedRequest().path("/BCE-CRED/ciphers").keyOnlyQueryParam("decrypt")
                .post(Entity.json(decryptRequest), IamDecryptResponse.class);
    }
}
