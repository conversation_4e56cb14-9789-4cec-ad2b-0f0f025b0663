package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.internalsdk.rds.model.account.V2Account;

import java.util.List;

public class AccountShowResponse {
    private List<V2Account> accounts;
    private List<V2Account> globalAccounts;

    public List<V2Account> getAccounts() {
        return accounts;
    }

    public void setAccounts(List<V2Account> accounts) {
        this.accounts = accounts;
    }

    public List<V2Account> getGlobalAccounts() {
        return globalAccounts;
    }

    public void setGlobalAccounts(List<V2Account> globalAccounts) {
        this.globalAccounts = globalAccounts;
    }

    public class Account{
        private String dbName;
        private String accountName;
        private List<String> privileges;
        private String hostIp;

        public String getDbName() {
            return dbName;
        }

        public void setDbName(String dbName) {
            this.dbName = dbName;
        }

        public String getAccountName() {
            return accountName;
        }

        public void setAccountName(String accountName) {
            this.accountName = accountName;
        }

        public List<String> getPrivileges() {
            return privileges;
        }

        public void setPrivileges(List<String> privileges) {
            this.privileges = privileges;
        }

        public String getHostIp() {
            return hostIp;
        }

        public void setHostIp(String hostIp) {
            this.hostIp = hostIp;
        }
    }
}
