package com.baidu.bce.internalsdk.rds.model.slowquery;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SlowquerySqlExplainResponse extends SlowqueryBaseResponse {

    private SqlExplainResult result;

    public SqlExplainResult getResult() {
        return result;
    }

    public void setResult(SqlExplainResult result) {
        this.result = result;
    }

    public static class SqlExplainResult {
        private List<ExplainSql> explainSql;

        public List<ExplainSql> getExplainSql() {
            return explainSql;
        }

        public void setExplainSql(List<ExplainSql> explainSql) {
            this.explainSql = explainSql;
        }
    }

    public static class ExplainSql {
        private String id;

        @JsonProperty("select_type")
        private String selectType;

        private String table;

        private String partitions;

        private String type;

        @JsonProperty("possible_keys")
        private String possibleKeys;

        private String key;

        @JsonProperty("key_len")
        private String keyLen;

        private String ref;

        private String rows;

        private String filtered;

        private String extraInfo;

        private String Extra;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getTable() {
            return table;
        }

        public void setTable(String table) {
            this.table = table;
        }

        public String getPartitions() {
            return partitions;
        }

        public void setPartitions(String partitions) {
            this.partitions = partitions;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getRef() {
            return ref;
        }

        public void setRef(String ref) {
            this.ref = ref;
        }

        public String getRows() {
            return rows;
        }

        public void setRows(String rows) {
            this.rows = rows;
        }

        public String getFiltered() {
            return filtered;
        }

        public void setFiltered(String filtered) {
            this.filtered = filtered;
        }

        public String getSelectType() {
            return selectType;
        }

        public void setSelectType(String selectType) {
            this.selectType = selectType;
        }

        public String getPossibleKeys() {
            return possibleKeys;
        }

        public void setPossibleKeys(String possibleKeys) {
            this.possibleKeys = possibleKeys;
        }

        public String getKeyLen() {
            return keyLen;
        }

        public void setKeyLen(String keyLen) {
            this.keyLen = keyLen;
        }


        public String getExtraInfo() {
            return extraInfo;
        }

        public void setExtraInfo(String extraInfo) {
            this.extraInfo = extraInfo;
        }

        public String getExtra() {
            return Extra;
        }

        public void setExtra(String extra) {
            Extra = extra;
        }
    }

}
