package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.baidu.bce.internalsdk.rds.model.instance.DataBackupObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by luping03 on 17/7/10.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BackupModeRequest {

    private String backupMode = "snapshot";

    private String dataBackupType = "physical";

    private List<DataBackupObject> dataBackupObjects;

    public List<DataBackupObject> getDataBackupObjects() {
        return dataBackupObjects;
    }

    public void setDataBackupObjects(List<DataBackupObject> dataBackupObjects) {
        this.dataBackupObjects = dataBackupObjects;
    }

    public BackupModeRequest(String backupMode) {
        this.backupMode = backupMode;
    }

    public BackupModeRequest(String backupMode, String dataBackupType) {
        this.backupMode = backupMode;
        this.dataBackupType = dataBackupType;
    }

    public BackupModeRequest(String backupMode, String dataBackupType, List<DataBackupObject> dataBackupObjects) {
        this.backupMode = backupMode;
        this.dataBackupType = dataBackupType;
        this.dataBackupObjects = dataBackupObjects;
    }

    public String getDataBackupType() {
        return dataBackupType;
    }

    public void setDataBackupType(String dataBackupType) {
        this.dataBackupType = dataBackupType;
    }

    public String getBackupMode() {
        return backupMode;
    }

    public void setBackupMode(String backupMode) {
        this.backupMode = backupMode;
    }
}
