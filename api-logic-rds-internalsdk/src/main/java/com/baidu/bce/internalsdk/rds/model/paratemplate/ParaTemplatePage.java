package com.baidu.bce.internalsdk.rds.model.paratemplate;


import java.util.List;

public class ParaTemplatePage<T> {

    private List<T> list;

    private Integer page;

    private Integer perpage;

    private Integer total;

    public ParaTemplatePage() {
    }

    public ParaTemplatePage(List<T> list, Integer page, Integer perpage, Integer total) {
        this.list = list;
        this.page = page;
        this.perpage = perpage;
        this.total = total;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPerpage() {
        return perpage;
    }

    public void setPerpage(Integer perpage) {
        this.perpage = perpage;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return "ParaTemplatePage{" +
                "list=" + list +
                ", page=" + page +
                ", perpage=" + perpage +
                ", total=" + total +
                '}';
    }
}
