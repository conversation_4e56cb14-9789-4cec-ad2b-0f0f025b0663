package com.baidu.bce.internalsdk.rds.model.rollback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskParamResponse {


    private String dataType;
    private String groupId = "baidu";
    private int schemaCount;
    private int tableCount;

    private List<BinlogFlashTasksInformation> binlogFlashTasksInformation;


    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getGroupId() {
        return groupId;
    }


    public int getSchemaCount() {
        return schemaCount;
    }

    public void setSchemaCount(int schemaCount) {
        this.schemaCount = schemaCount;
    }

    public int getTableCount() {
        return tableCount;
    }

    public void setTableCount(int tableCount) {
        this.tableCount = tableCount;
    }

    public void setBinlogFlashTasksInformation(List<BinlogFlashTasksInformation> binlogFlashTasksInformation) {
        this.binlogFlashTasksInformation = binlogFlashTasksInformation;
    }

    public List<BinlogFlashTasksInformation> getBinlogFlashTasksInformation() {
        return binlogFlashTasksInformation;
    }
}
