package com.baidu.bce.internalsdk.rds.model.slowquery;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class SlowqueryTaskListMarkerRequest {
    @NotNull
    private String instanceId;

    private String filterStartTime;

    private String filterEndTime;

    private String marker;

    private String maxKeys;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getFilterStartTime() {
        return filterStartTime;
    }

    public void setFilterStartTime(String filterStartTime) {
        this.filterStartTime = filterStartTime;
    }

    public String getFilterEndTime() {
        return filterEndTime;
    }

    public void setFilterEndTime(String filterEndTime) {
        this.filterEndTime = filterEndTime;
    }

}
