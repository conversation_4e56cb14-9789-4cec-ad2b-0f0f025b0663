package com.baidu.bce.internalsdk.rds.model;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BasicCategoryRequest {

    @IdPermission
    private String instanceId;
    @Valid
    private BasicBackUpPolicy backupPolicy;

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BasicBackUpPolicy {

        @Pattern(regexp = "^((([0-1]?[0-9])|([2][0-3])):([0-5]?[0-9])(:([0-5]?[0-9]))?Z)$", message = "19:00:00Z")
        private String backupTime = "00:00:00Z";

        @Pattern(regexp = "^([0-6],){0,6}[0-6]$", message = "0,1,2,3,4,5,6")
        private String backupDays = "0,1,2,3,4,5,6";

        private String dataBackupType;

        // 是否开启高频备份，默认为 false
        private boolean incrementalDataBackupEnable;

        // 增量数据备份间隔（秒）最小粒度是小时级别
        private Integer incrementalDataBackupInterval;

        public String getBackupTime() {
            return backupTime;
        }

        public void setBackupTime(String backupTime) {
            this.backupTime = backupTime;
        }

        public String getBackupDays() {
            return backupDays;
        }

        public void setBackupDays(String backupDays) {
            this.backupDays = backupDays;
        }

        public String getDataBackupType() {
            return dataBackupType;
        }

        public void setDataBackupType(String dataBackupType) {
            this.dataBackupType = dataBackupType;
        }

        public boolean getIncrementalDataBackupEnable() {
            return incrementalDataBackupEnable;
        }

        public void setIncrementalDataBackupEnable(boolean incrementalDataBackupEnable) {
            this.incrementalDataBackupEnable = incrementalDataBackupEnable;
        }

        public Integer getIncrementalDataBackupInterval() {
            return incrementalDataBackupInterval;
        }

        public void setIncrementalDataBackupInterval(Integer incrementalDataBackupInterval) {
            this.incrementalDataBackupInterval = incrementalDataBackupInterval;
        }
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public BasicBackUpPolicy getBackupPolicy() {
        return backupPolicy;
    }

    public void setBackupPolicy(BasicBackUpPolicy backupPolicy) {
        this.backupPolicy = backupPolicy;
    }
}
