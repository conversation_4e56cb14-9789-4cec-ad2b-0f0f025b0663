package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PublicNetworkConnDetail {

    private String rdsInstanceStatus;
    private String inetStatus;
    private String bccInWhiteList;

    public String getRdsInstanceStatus() {
        return rdsInstanceStatus;
    }

    public void setRdsInstanceStatus(String rdsInstanceStatus) {
        this.rdsInstanceStatus = rdsInstanceStatus;
    }

    public String getInetStatus() {
        return inetStatus;
    }

    public void setInetStatus(String inetStatus) {
        this.inetStatus = inetStatus;
    }

    public String getBccInWhiteList() {
        return bccInWhiteList;
    }

    public void setBccInWhiteList(String bccInWhiteList) {
        this.bccInWhiteList = bccInWhiteList;
    }
}
