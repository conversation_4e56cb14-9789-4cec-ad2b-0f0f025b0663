package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtenstionListResponse {

    private List<ExtensionInfo> extensions;
    private int totalCount;

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<ExtensionInfo> getExtensions() {
        return extensions;
    }

    public void setExtensions(List<ExtensionInfo> extensions) {
        this.extensions = extensions;
    }

    public static class ExtensionInfo {
        private String name;
        private String installedVersion;
        private String comment;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getInstalledVersion() {
            return installedVersion;
        }

        public void setInstalledVersion(String installedVersion) {
            this.installedVersion = installedVersion;
        }

        public String getComment() {
            return comment;
        }

        public void setComment(String comment) {
            this.comment = comment;
        }
    }

}
