package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BccInnerConnectDetail {

    private String rdsInstanceStatus;
    private String bccInstanceStatus;
    private String connectStatus;
    private String identicalVpc;
    private String bccInWhiteList;

    public String getRdsInstanceStatus() {
        return rdsInstanceStatus;
    }

    public void setRdsInstanceStatus(String rdsInstanceStatus) {
        this.rdsInstanceStatus = rdsInstanceStatus;
    }

    public String getBccInstanceStatus() {
        return bccInstanceStatus;
    }

    public void setBccInstanceStatus(String bccInstanceStatus) {
        this.bccInstanceStatus = bccInstanceStatus;
    }

    public String getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(String connectStatus) {
        this.connectStatus = connectStatus;
    }

    public String getIdenticalVpc() {
        return identicalVpc;
    }

    public void setIdenticalVpc(String identicalVpc) {
        this.identicalVpc = identicalVpc;
    }

    public String getBccInWhiteList() {
        return bccInWhiteList;
    }

    public void setBccInWhiteList(String bccInWhiteList) {
        this.bccInWhiteList = bccInWhiteList;
    }
}
