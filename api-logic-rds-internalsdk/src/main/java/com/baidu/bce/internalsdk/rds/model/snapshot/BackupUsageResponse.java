package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BackupUsageResponse {

    private String appId;
    @ApiModelProperty("免费备份存储空间（单位 Bytes）")
    private long freeSpaceSizeBytes;
    @ApiModelProperty("数据备份大小（单位 Bytes）")
    private long dataBackupSizeBytes;
    @ApiModelProperty("跨地域数据备份（数据备份副本）大小（单位 Bytes）")
    private long dataBackupCopySizeBytes;
    @ApiModelProperty("跨地域数据备份（数据备份副本）大小（单位 Bytes）")
    private long binlogBackupSizeBytes;
    @ApiModelProperty("免费物理备份存储空间（单位 Bytes）")
    private long physicalFreeSpaceSizeBytes;
    @ApiModelProperty("免费快照备份存储空间（单位 Bytes）")
    private long snapshotFreeSpaceSizeBytes;
    @ApiModelProperty("物理数据备份大小（单位 Bytes）")
    private long physicalDataBackupSizeBytes;
    @ApiModelProperty("快照数据备份大小（单位 Bytes）")
    private long snapshotDataBackupSizeBytes;
    @ApiModelProperty("跨地域物理数据备份（物理数据备份副本）大小（单位 Bytes）")
    private long physicalDataBackupCopySizeBytes;
    @ApiModelProperty("跨地域快照数据备份（快照数据备份副本）大小（单位 Bytes）")
    private long snapshotDataBackupCopySizeBytes;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public long getFreeSpaceSizeBytes() {
        return freeSpaceSizeBytes;
    }

    public void setFreeSpaceSizeBytes(long freeSpaceSizeBytes) {
        this.freeSpaceSizeBytes = freeSpaceSizeBytes;
    }

    public long getDataBackupSizeBytes() {
        return dataBackupSizeBytes;
    }

    public void setDataBackupSizeBytes(long dataBackupSizeBytes) {
        this.dataBackupSizeBytes = dataBackupSizeBytes;
    }

    public long getDataBackupCopySizeBytes() {
        return dataBackupCopySizeBytes;
    }

    public void setDataBackupCopySizeBytes(long dataBackupCopySizeBytes) {
        this.dataBackupCopySizeBytes = dataBackupCopySizeBytes;
    }

    public long getBinlogBackupSizeBytes() {
        return binlogBackupSizeBytes;
    }

    public void setBinlogBackupSizeBytes(long binlogBackupSizeBytes) {
        this.binlogBackupSizeBytes = binlogBackupSizeBytes;
    }

    public long getPhysicalFreeSpaceSizeBytes() {
        return physicalFreeSpaceSizeBytes;
    }

    public void setPhysicalFreeSpaceSizeBytes(long physicalFreeSpaceSizeBytes) {
        this.physicalFreeSpaceSizeBytes = physicalFreeSpaceSizeBytes;
    }

    public long getSnapshotFreeSpaceSizeBytes() {
        return snapshotFreeSpaceSizeBytes;
    }

    public void setSnapshotFreeSpaceSizeBytes(long snapshotFreeSpaceSizeBytes) {
        this.snapshotFreeSpaceSizeBytes = snapshotFreeSpaceSizeBytes;
    }

    public long getPhysicalDataBackupSizeBytes() {
        return physicalDataBackupSizeBytes;
    }

    public void setPhysicalDataBackupSizeBytes(long physicalDataBackupSizeBytes) {
        this.physicalDataBackupSizeBytes = physicalDataBackupSizeBytes;
    }

    public long getSnapshotDataBackupSizeBytes() {
        return snapshotDataBackupSizeBytes;
    }

    public void setSnapshotDataBackupSizeBytes(long snapshotDataBackupSizeBytes) {
        this.snapshotDataBackupSizeBytes = snapshotDataBackupSizeBytes;
    }

    public long getPhysicalDataBackupCopySizeBytes() {
        return physicalDataBackupCopySizeBytes;
    }

    public void setPhysicalDataBackupCopySizeBytes(long physicalDataBackupCopySizeBytes) {
        this.physicalDataBackupCopySizeBytes = physicalDataBackupCopySizeBytes;
    }

    public long getSnapshotDataBackupCopySizeBytes() {
        return snapshotDataBackupCopySizeBytes;
    }

    public void setSnapshotDataBackupCopySizeBytes(long snapshotDataBackupCopySizeBytes) {
        this.snapshotDataBackupCopySizeBytes = snapshotDataBackupCopySizeBytes;
    }
}
