package com.baidu.bce.internalsdk.rds.model.smartdba;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SmartDbaInstanceResponse {

    private Boolean success;

    private BaseResponse.Message message = new BaseResponse.Message();

    private JSONObject result;

    private String status;

    public Boolean getSuccess() {
        return success;
    }

    public SmartDbaInstanceResponse() {
    }

    public SmartDbaInstanceResponse(Boolean success, BaseResponse.Message message, JSONObject result, String status) {
        this.success = success;
        this.message = message;
        this.result = result;
        this.status = status;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public BaseResponse.Message getMessage() {
        return message;
    }

    public void setMessage(BaseResponse.Message message) {
        this.message = message;
    }

    public JSONObject getResult() {
        return result;
    }

    public void setResult(JSONObject result) {
        this.result = result;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SmartDbaInstanceResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", result=" + result +
                ", status='" + status + '\'' +
                '}';
    }
}
