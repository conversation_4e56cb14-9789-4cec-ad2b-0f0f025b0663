package com.baidu.bce.internalsdk.rds.model.rogroup;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonIgnoreProperties(ignoreUnknown = true)
@J<PERSON>Include(JsonInclude.Include.NON_NULL)
public class Dict {
    private String entryHost;
    private Integer entryPort;
    private String vnetIp;
    private String inetIp;
    private String eipStatus;

    public String getEntryHost() {
        return entryHost;
    }

    public void setEntryHost(String entryHost) {
        this.entryHost = entryHost;
    }

    public Integer getEntryPort() {
        return entryPort;
    }

    public void setEntryPort(Integer entryPort) {
        this.entryPort = entryPort;
    }

    public String getVnetIp() {
        return vnetIp;
    }

    public void setVnetIp(String vnetIp) {
        this.vnetIp = vnetIp;
    }

    public String getInetIp() {
        return inetIp;
    }

    public void setInetIp(String inetIp) {
        this.inetIp = inetIp;
    }

    public String getEipStatus() {
        return eipStatus;
    }

    public void setEipStatus(String eipStatus) {
        this.eipStatus = eipStatus;
    }
}
