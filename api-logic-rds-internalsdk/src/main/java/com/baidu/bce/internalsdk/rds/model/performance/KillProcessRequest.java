package com.baidu.bce.internalsdk.rds.model.performance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by yangxin on 2022/1/14
 */
@JsonIgnoreProperties
public class KillProcessRequest {

    private String action = "kill";
    @NotEmpty
    private List<Integer> ids;

    public String getAction() {
        return action;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }
}
