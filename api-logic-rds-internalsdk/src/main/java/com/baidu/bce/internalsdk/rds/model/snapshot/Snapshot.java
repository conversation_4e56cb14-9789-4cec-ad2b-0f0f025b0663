package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/4.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Snapshot implements Comparable<Snapshot> {
    private String instanceId;
    private String snapshotId = UUID.randomUUID().toString();

    private String snapshotSizeInBytes;
    private String snapshotSizeInMBytes;
    private String snapshotType;
//    private String backupType;
    private String snapshotStatus;

    private Date snapshotStartTime;
    private Date snapshotEndTime;
    private String downloadUrl;
    private Date downloadExpires;
    private Date snapshotDataTime;
    private String dataBackupType;
    private String dataBackupMethod;
    private List<MultiDownloads> multiDownloadUrl;
    private String dbVersion;

    @Override
    public String toString() {
        return "snapshot{"
                + "instanceId='" + instanceId + '\''
                + ", snapshotId='" + snapshotId + '\''
                + ", snapshotSizeInBytes='" + snapshotSizeInBytes + '\''
                + ", snapshotSizeInMBytes='" + snapshotSizeInMBytes + '\''
                + ", snapshotType='" + snapshotType + '\''
//                ", backupType='" + backupType + '\''
                + ", snapshotStatus='" + snapshotStatus + '\''
                + ", snapshotStartTime='" + snapshotStartTime + '\''
                + ", snapshotEndTime='" + snapshotEndTime + '\''
                + ", downloadUrl='" + downloadUrl + '\''
                + ", downloadExpires=" + downloadExpires
                + ", snapshotDataTime=" + snapshotDataTime
                + '}';
    }

    public List<MultiDownloads> getMultiDownloadUrl() {
        return multiDownloadUrl;
    }

    public void setMultiDownloadUrl(List<MultiDownloads> multiDownloadUrl) {
        this.multiDownloadUrl = multiDownloadUrl;
    }

    public String getDataBackupMethod() {
        return dataBackupMethod;
    }

    public void setDataBackupMethod(String dataBackupMethod) {
        this.dataBackupMethod = dataBackupMethod;
    }

    public String getDbVersion() {
        return dbVersion;
    }

    public void setDbVersion(String dbVersion) {
        this.dbVersion = dbVersion;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getDownloadExpires() {
        return downloadExpires;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setDownloadExpires(Date downloadExpires) {
        this.downloadExpires = downloadExpires;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getSnapshotStartTime() {
        return snapshotStartTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setSnapshotStartTime(Date snapshotStartTime) {
        this.snapshotStartTime = snapshotStartTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getSnapshotEndTime() {
        return snapshotEndTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setSnapshotEndTime(Date snapshotEndTime) {
        this.snapshotEndTime = snapshotEndTime;
    }

    public Date getSnapshotDataTime() {
        return snapshotDataTime;
    }
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setSnapshotDataTime(Date snapshotDataTime) {
        this.snapshotDataTime = snapshotDataTime;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getDataBackupType() {
        return dataBackupType;
    }

    public void setDataBackupType(String dataBackupType) {
        this.dataBackupType = dataBackupType;
    }

    public String getSnapshotId() {
        return snapshotId;
    }

    public void setSnapshotId(String snapshotId) {
        this.snapshotId = snapshotId;
    }

    public String getSnapshotSizeInBytes() {
        return snapshotSizeInBytes;
    }

    public void setSnapshotSizeInBytes(String snapshotSizeInBytes) {
        this.snapshotSizeInBytes = snapshotSizeInBytes;
    }

    public String getSnapshotType() {
        return snapshotType;
    }

    public void setSnapshotType(String snapshotType) {
        this.snapshotType = snapshotType;
    }

    public String getSnapshotStatus() {
        return snapshotStatus;
    }

    public void setSnapshotStatus(String snapshotStatus) {
        this.snapshotStatus = snapshotStatus;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public Snapshot instanceId(final String instanceId) {
        this.instanceId = instanceId;
        return this;
    }

    public Snapshot snapshotId(final String snapshotId) {
        this.snapshotId = snapshotId;
        return this;
    }

    public Snapshot snapshotSizeInBytes(final String snapshotSizeInBytes) {
        this.snapshotSizeInBytes = snapshotSizeInBytes;
        return this;
    }

    public Snapshot snapshotType(final String snapshotType) {
        this.snapshotType = snapshotType;
        return this;
    }

    public Snapshot snapshotStatus(final String snapshotStatus) {
        this.snapshotStatus = snapshotStatus;
        return this;
    }

    public Snapshot snapshotStartTime(final Date snapshotStartTime) {
        this.snapshotStartTime = snapshotStartTime;
        return this;
    }

    public Snapshot snapshotEndTime(final Date snapshotEndTime) {
        this.snapshotEndTime = snapshotEndTime;
        return this;
    }

    public Snapshot downloadUrl(final String downloadUrl) {
        this.downloadUrl = downloadUrl;
        return this;
    }

    @Override
    public int compareTo(Snapshot o) {
        return this.snapshotStartTime.compareTo(o.snapshotStartTime);
    }

    public String getSnapshotSizeInMBytes() {
        return snapshotSizeInMBytes;
    }

    public void setSnapshotSizeInMBytes(String snapshotSizeInMBytes) {
        this.snapshotSizeInMBytes = snapshotSizeInMBytes;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static final class MultiDownloads {
        private String dbName;
        private String  downloadName;
        private long downloadSize;
        private String downloadUrl;

        public String getDbName() {
            return dbName;
        }

        public void setDbName(String dbName) {
            this.dbName = dbName;
        }

        public String getDownloadName() {
            return downloadName;
        }

        public void setDownloadName(String downloadName) {
            this.downloadName = downloadName;
        }

        public long getDownloadSize() {
            return downloadSize;
        }

        public void setDownloadSize(long downloadSize) {
            this.downloadSize = downloadSize;
        }

        public String getDownloadUrl() {
            return downloadUrl;
        }

        public void setDownloadUrl(String downloadUrl) {
            this.downloadUrl = downloadUrl;
        }
    }

}
