package com.baidu.bce.internalsdk.rds.model.instance;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/7/18.
 */
public class CheckCodeRequest {
    @NotNull
    private CheckCodeAction action;

    @Override
    public String toString() {
        return "CheckCodeRequest{" + "action=" + action + '}';
    }

    public CheckCodeAction getAction() {
        return action;
    }

    public void setAction(CheckCodeAction action) {
        this.action = action;
    }

    public static enum CheckCodeAction {
        updatePassword("更新密码"), deleteAccount("删除帐户"), deleteDatabase("删除数据库");

        private String desc;

        private CheckCodeAction(String desc) {
            this.desc = desc;
        }

        @Override
        public String toString() {
            return desc;
        }
    }
}
