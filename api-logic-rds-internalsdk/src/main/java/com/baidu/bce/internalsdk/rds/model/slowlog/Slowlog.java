package com.baidu.bce.internalsdk.rds.model.slowlog;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.UUID;

/**
 * Created by baidu on 15/11/27.
 */
public class Slowlog implements Comparable<Slowlog> {
    private String slowlogId = UUID.randomUUID().toString();
    private Long slowlogSizeInBytes;

    private Date slowlogStartTime = new Date(System.currentTimeMillis());
    private Date slowlogEndTime = new Date(System.currentTimeMillis() + 10000);

    private String downloadUrl = "www.baidu.com";
    private Date downloadExpires = new Date(System.currentTimeMillis() + 600000);

    @Override
    public int compareTo(@NotNull Slowlog o) {
        return this.slowlogStartTime.compareTo(o.slowlogStartTime);
    }

    @Override
    public String toString() {
        return "Slowlog{"
                + "slowlogId='" + slowlogId + '\''
                + ", slowlogSizeInBytes=" + slowlogSizeInBytes
                + ", slowlogStartTime='" + slowlogStartTime + '\''
                + ", slowlogEndTime='" + slowlogEndTime + '\''
                + ", downloadUrl='" + downloadUrl + '\''
                + ", downloadExpires=" + downloadExpires
                + '}';
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getDownloadExpires() {
        return downloadExpires;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setDownloadExpires(Date downloadExpires) {
        this.downloadExpires = downloadExpires;
    }

    public String getSlowlogId() {
        return slowlogId;
    }

    public void setSlowlogId(String slowlogId) {
        this.slowlogId = slowlogId;
    }

    public Long getSlowlogSizeInBytes() {
        return slowlogSizeInBytes;
    }

    public void setSlowlogSizeInBytes(Long slowlogSizeInBytes) {
        this.slowlogSizeInBytes = slowlogSizeInBytes;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getSlowlogStartTime() {
        return slowlogStartTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setSlowlogStartTime(Date slowlogStartTime) {
        this.slowlogStartTime = slowlogStartTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getSlowlogEndTime() {
        return slowlogEndTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setSlowlogEndTime(Date slowlogEndTime) {
        this.slowlogEndTime = slowlogEndTime;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public Slowlog slowlogId(final String slowlogId) {
        this.slowlogId = slowlogId;
        return this;
    }

    public Slowlog slowlogSizeInBytes(final Long slowlogSizeInBytes) {
        this.slowlogSizeInBytes = slowlogSizeInBytes;
        return this;
    }

    public Slowlog slowStartTime(final Date slowStartTime) {
        this.slowlogStartTime = slowStartTime;
        return this;
    }

    public Slowlog slowEndTime(final Date slowEndTime) {
        this.slowlogEndTime = slowEndTime;
        return this;
    }

    public Slowlog downloadUrl(final String downloadUrl) {
        this.downloadUrl = downloadUrl;
        return this;
    }

    public Slowlog downloadExpires(final Date downloadExpires) {
        this.downloadExpires = downloadExpires;
        return this;
    }
}
