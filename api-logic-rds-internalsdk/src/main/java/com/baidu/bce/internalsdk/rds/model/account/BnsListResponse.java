package com.baidu.bce.internalsdk.rds.model.account;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BnsListResponse {
    private String rdsproxyId;
    private String accountName;
    private List<String> bnsList;

    public String getRdsproxyId() {
        return rdsproxyId;
    }

    public void setRdsproxyId(String rdsproxyId) {
        this.rdsproxyId = rdsproxyId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public List<String> getBnsList() {
        return bnsList;
    }

    public void setBnsList(List<String> bnsList) {
        this.bnsList = bnsList;
    }
}
