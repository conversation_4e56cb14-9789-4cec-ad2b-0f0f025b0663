package com.baidu.bce.internalsdk.rds.model.config;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardConfigModifyRequest {
    @IdPermission
    private String instanceId;
    private ConfigModifyItem.ConfigModifyItemList modified;
    private String effectiveTime;
    private boolean switchover = false;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DashboardModifyRequest{");
        sb.append("instanceId='").append(instanceId).append('\'');
        sb.append(", modified=").append(modified);
        sb.append('}');
        return sb.toString();
    }

    public boolean getSwitchover() {
        return switchover;
    }

    public void setSwitchover(boolean switchover) {
        this.switchover = switchover;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public ConfigModifyItem.ConfigModifyItemList getModified() {
        return modified;
    }

    public void setModified(ConfigModifyItem.ConfigModifyItemList modified) {
        this.modified = modified;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }
}
