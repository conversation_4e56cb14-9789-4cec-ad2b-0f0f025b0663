package com.baidu.bce.internalsdk.rds.model.group;


import com.baidu.bce.internalsdk.rds.model.database.Database;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/19.
 */
public class DashboardGroupDatabaseCreateRequest {
    @NotNull
    private String groupId;
    @Valid
    private Database database;


    @Override
    public String toString() {
        return "DashboardDatabaseCreateRequest{" +
                "groupId='" + getGroupId() + '\'' +
                ", database=" + getDatabase() +
                '}';
    }

    public Database getDatabase() {
        return database;
    }

    public void setDatabase(Database database) {
        this.database = database;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
}
