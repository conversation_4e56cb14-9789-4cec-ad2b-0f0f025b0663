package com.baidu.bce.internalsdk.rds.model.dbfirewall;

import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/9.
 */
public class DashboardSqlWhiteAddRequest extends InstanceIdRequest {

    @NotNull
    private String dbName;

    @NotNull
    private String sqlMd5;

    @NotNull
    private String sqlFingerprint;

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getSqlMd5() {
        return sqlMd5;
    }

    public void setSqlMd5(String sqlMd5) {
        this.sqlMd5 = sqlMd5;
    }

    public String getSqlFingerprint() {
        return sqlFingerprint;
    }

    public void setSqlFingerprint(String sqlFingerprint) {
        this.sqlFingerprint = sqlFingerprint;
    }
}
