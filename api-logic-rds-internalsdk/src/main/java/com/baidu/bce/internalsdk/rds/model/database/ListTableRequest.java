package com.baidu.bce.internalsdk.rds.model.database;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListTableRequest {
    @NotNull
    @IdPermission
    private String instanceId;

    private String pattern;

    private String dbName;

    private Integer pageNo;

    private Integer pageSize;

    private String patternTable = "";

    @Override
    public String toString() {
        return "InstanceIdRequest{"
                + "instanceId='" + getInstanceId() + '\''
                + '}';
    }

    public String getPatternTable() {
        return patternTable;
    }

    public void setPatternTable(String patternTable) {
        this.patternTable = patternTable;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
