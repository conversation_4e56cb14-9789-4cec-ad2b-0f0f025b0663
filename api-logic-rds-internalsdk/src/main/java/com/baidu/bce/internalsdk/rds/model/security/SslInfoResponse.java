package com.baidu.bce.internalsdk.rds.model.security;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by luping03 on 18/4/18.
 */
public class SslInfoResponse {
    private SslState sslState;

    public SslState getSslState() {
        return sslState;
    }

    public void setSslState(SslState sslState) {
        this.sslState = sslState;
    }

    private static class SslState {
        private String instanceId;

        private String sslStatus;

        private String sslAddress;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
        private Date startTime;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
        private Date endTime;

        public String getInstanceId() {
            return instanceId;
        }

        public void setInstanceId(String instanceId) {
            this.instanceId = instanceId;
        }

        public String getSslStatus() {
            return sslStatus;
        }

        public void setSslStatus(String sslStatus) {
            this.sslStatus = sslStatus;
        }

        public String getSslAddress() {
            return sslAddress;
        }

        public void setSslAddress(String sslAddress) {
            this.sslAddress = sslAddress;
        }

        public Date getStartTime() {
            return startTime;
        }

        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }
    }
}
