package com.baidu.bce.internalsdk.rds.model.instance;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class InstancePutFlowRequest {
    @IdPermission
    private String rdsproxyId;

    private String replicaId;

    private String weight;

    public String getRdsproxyId() {
        return rdsproxyId;
    }

    public void setRdsproxyId(String rdsproxyId) {
        this.rdsproxyId = rdsproxyId;
    }

    public String getReplicaId() {
        return replicaId;
    }

    public void setReplicaId(String replicaId) {
        this.replicaId = replicaId;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }
}