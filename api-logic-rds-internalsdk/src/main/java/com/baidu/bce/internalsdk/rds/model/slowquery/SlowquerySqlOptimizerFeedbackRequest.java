package com.baidu.bce.internalsdk.rds.model.slowquery;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class SlowquerySqlOptimizerFeedbackRequest {
    @NotNull
    @IdPermission
    private String instanceId;

    private String optimizerId;

    private String score;

    private String userAdvice;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getOptimizerId() {
        return optimizerId;
    }

    public void setOptimizerId(String optimizerId) {
        this.optimizerId = optimizerId;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getUserAdvice() {
        return userAdvice;
    }

    public void setUserAdvice(String userAdvice) {
        this.userAdvice = userAdvice;
    }
}
