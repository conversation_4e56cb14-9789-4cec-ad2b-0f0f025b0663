package com.baidu.bce.internalsdk.rds.model.account;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardAccountDeleteRequest {
    @NotNull
    @IdPermission
    private String instanceId;
    @NotNull
    private String accountName;
    private String code;


    @Override
    public String toString() {
        return "DashboardAccountDeleteRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", accountName='" + accountName + '\'' +
                ", code='" + code + '\'' +
                '}';
    }


    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
