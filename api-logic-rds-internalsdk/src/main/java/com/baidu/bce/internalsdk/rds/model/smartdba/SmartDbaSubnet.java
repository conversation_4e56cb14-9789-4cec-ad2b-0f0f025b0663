package com.baidu.bce.internalsdk.rds.model.smartdba;

import com.baidu.bce.internalsdk.rds.model.instance.Instance;

import java.util.ArrayList;
import java.util.List;

public class SmartDbaSubnet {

    private String instanceId;

    private String instanceType;

    private Instance.RoleInfo nodeMaster;

    private Instance.RoleInfo nodeSlave;

    List<Instance.RoleInfo> subnets = new ArrayList<>();

    public SmartDbaSubnet() {
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Instance.RoleInfo getNodeMaster() {
        return nodeMaster;
    }

    public void setNodeMaster(Instance.RoleInfo nodeMaster) {
        this.nodeMaster = nodeMaster;
    }

    public Instance.RoleInfo getNodeSlave() {
        return nodeSlave;
    }

    public void setNodeSlave(Instance.RoleInfo nodeSlave) {
        this.nodeSlave = nodeSlave;
    }

    public List<Instance.RoleInfo> getSubnets() {
        return subnets;
    }

    public void setSubnets(List<Instance.RoleInfo> subnets) {
        this.subnets = subnets;
    }

    public String getInstanceType() {
        return instanceType;
    }

    public void setInstanceType(String instanceType) {
        this.instanceType = instanceType;
    }

    @Override
    public String toString() {
        return "SmartDbaSubnet{" +
                "instanceId='" + instanceId + '\'' +
                ", instanceType='" + instanceType + '\'' +
                ", nodeMaster=" + nodeMaster +
                ", nodeSlave=" + nodeSlave +
                ", subnets=" + subnets +
                '}';
    }
}
