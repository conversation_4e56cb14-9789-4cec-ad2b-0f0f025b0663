package com.baidu.bce.internalsdk.rds.model.binlog;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/4/5.
 */
public class BinlogDateTime {
    private Date datetime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getDatetime() {
        return datetime;
    }
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setDatetime(Date datetime) {
        this.datetime = datetime;
    }
}
