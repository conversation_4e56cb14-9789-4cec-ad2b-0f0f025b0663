package com.baidu.bce.internalsdk.rds.model.rollback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckTimeResponse {

    private String dataType;
    private Boolean checkResult;
    private PreCheckTimeRequest recoverableDateTimes;



    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Boolean getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(Boolean checkResult) {
        this.checkResult = checkResult;
    }

    public PreCheckTimeRequest getRecoverableDateTimes() {
        return recoverableDateTimes;
    }

    public void setRecoverableDateTimes(PreCheckTimeRequest recoverableDateTimes) {
        this.recoverableDateTimes = recoverableDateTimes;
    }
}
