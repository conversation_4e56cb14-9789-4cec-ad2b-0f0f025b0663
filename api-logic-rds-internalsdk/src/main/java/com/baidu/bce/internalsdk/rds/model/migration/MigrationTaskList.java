package com.baidu.bce.internalsdk.rds.model.migration;

import java.util.ArrayList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/10/27.
 */
public class MigrationTaskList extends ArrayList<MigrationStatus> {

    public static class MigrationTaskListResponse {
        private MigrationTaskList migrations;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("MigrationTaskListResponse{");
            sb.append("migrations=").append(migrations);
            sb.append('}');
            return sb.toString();
        }

        public MigrationTaskList getMigrations() {
            return migrations;
        }

        public void setMigrations(MigrationTaskList migrations) {
            this.migrations = migrations;
        }

    }
}
