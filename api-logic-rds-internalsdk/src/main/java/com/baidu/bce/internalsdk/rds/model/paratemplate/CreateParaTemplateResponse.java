package com.baidu.bce.internalsdk.rds.model.paratemplate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateParaTemplateResponse {
    @JsonIgnore
    private int code;
//    @JsonIgnore
//    private String requestId;
    @JsonIgnore
    private String msg;
    private Data data;

    // Getters and Setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

//    @JsonProperty("request_id")
//    public String getRequestId() {
//        return requestId;
//    }
//
//    @JsonProperty("request_id")
//    public void setRequestId(String requestId) {
//        this.requestId = requestId;
//    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public static class Data {
        private String id;
        private String idShow;

        @JsonCreator
        public Data(
                @JsonProperty("id") String id,
                @JsonProperty("id_show") String idShow
        ) {
            this.id = id;
            this.idShow = idShow;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getIdShow() {
            return idShow;
        }

        public void setIdShow(String idShow) {
            this.idShow = idShow;
        }
    }
}
