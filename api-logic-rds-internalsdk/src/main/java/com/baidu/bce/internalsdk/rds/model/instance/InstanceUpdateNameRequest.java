package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.internalsdk.rds.util.PatternString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
public class InstanceUpdateNameRequest {
    @NotNull
    @Pattern(regexp = PatternString.PATTERN_INSTANCE_NAME)
    private String instanceName;

    @Override
    public String toString() {
        return "InstanceUpdateName{" +
                "instanceName='" + instanceName + '\'' +
                '}';
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }
}
