package com.baidu.bce.internalsdk.rds.model.resource;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Group {

    // 资源组对应的父分组ID，为空的时候表示一级资源组
    private String parentId;

    // 资源组的备注，可以为空
    private String groupId;

    // 资源组的备注，可以为空
    private String extra;

    // 资源组的名字，同一用户下不能重复，支持中英文及常见符号，0～20个字符，必填
    private String name;


}
