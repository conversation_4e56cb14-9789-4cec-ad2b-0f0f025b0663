package com.baidu.bce.internalsdk.rds.model.blb;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by yangxin on 2021/9/24
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class LbdcCluster {
    private String id;
    private String name;
    private String type;
    private String status;
    private int ccuCount;
    private String createTime;
    private String expireTime;
    private String desc;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getCcuCount() {
        return ccuCount;
    }

    public void setCcuCount(int ccuCount) {
        this.ccuCount = ccuCount;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
