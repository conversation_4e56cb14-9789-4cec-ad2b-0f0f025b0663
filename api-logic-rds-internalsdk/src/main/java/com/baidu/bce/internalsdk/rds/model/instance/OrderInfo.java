package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by gengguangming on 2016/1/12.
 */
public class OrderInfo {

    // 接口修改前返回字段
    private String status;

    // 接口修改后返回字段
    private RdsOrder order;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public RdsOrder getOrder() {
        return order;
    }

    public void setOrder(RdsOrder order) {
        this.order = order;
    }

    public static class RdsOrder {

        private String userId;

        private String orderId;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        private Date createTime;

        private List<String> applications = new LinkedList<>();

        private Integer instanceAmount;

        private String status;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getOrderId() {
            return orderId;
        }

        public void setOrderId(String orderId) {
            this.orderId = orderId;
        }

        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }

        public List<String> getApplications() {
            return applications;
        }

        public void setApplications(List<String> applications) {
            this.applications = applications;
        }

        public Integer getInstanceAmount() {
            return instanceAmount;
        }

        public void setInstanceAmount(Integer instanceAmount) {
            this.instanceAmount = instanceAmount;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }
}
