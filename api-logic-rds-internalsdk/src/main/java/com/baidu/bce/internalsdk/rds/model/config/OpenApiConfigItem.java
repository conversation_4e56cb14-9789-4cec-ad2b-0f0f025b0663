package com.baidu.bce.internalsdk.rds.model.config;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/13.
 */
public class OpenApiConfigItem {
    private String name;
    private String defaultValue;
    private String value;
    private String pendingValue;
    private String type;

    // OpenAPI 专用参数
    private String dynamic;

    // OpenAPI 专用参数
    private String modifiable;

    private String allowedValues;

    // OpenAPI 专用参数
    private String desc;

    private String etag;
    private String configType;
    private String bestValue;
    private String attention;
    private Boolean ifCluster;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getPendingValue() {
        return pendingValue;
    }

    public void setPendingValue(String pendingValue) {
        this.pendingValue = pendingValue;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDynamic() {
        return dynamic;
    }

    public void setDynamic(String dynamic) {
        this.dynamic = dynamic;
    }

    public String getModifiable() {
        return modifiable;
    }

    public void setModifiable(String modifiable) {
        this.modifiable = modifiable;
    }

    public String getAllowedValues() {
        return allowedValues;
    }

    public void setAllowedValues(String allowedValues) {
        this.allowedValues = allowedValues;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getEtag() {
        return etag;
    }

    public void setEtag(String etag) {
        this.etag = etag;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getBestValue() {
        return bestValue;
    }

    public void setBestValue(String bestValue) {
        this.bestValue = bestValue;
    }

    public String getAttention() {
        return attention;
    }

    public void setAttention(String attention) {
        this.attention = attention;
    }

    public Boolean getIfCluster() {
        return ifCluster;
    }

    public void setIfCluster(Boolean ifCluster) {
        this.ifCluster = ifCluster;
    }
}
