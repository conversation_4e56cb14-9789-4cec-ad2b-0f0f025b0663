package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;
import java.util.List;

public class BccInstanceListRequest {

    @IdPermission
    @NotNull
    private List<BccListRequest> bccInstanceList;

    public List<BccListRequest> getBccInstanceList() {
        return bccInstanceList;
    }

    public void setBccInstanceList(List<BccListRequest> bccInstanceList) {
        this.bccInstanceList = bccInstanceList;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BccListRequest {
        @IdPermission
        @NotNull
        private String instanceId;
        @NotNull
        private String bccInstanceId;
        @NotNull
        private String bccInstanceUuid;
        private String bccInstanceName;
        private String vpcId;
        private String vpcName;
        private String vpcCidr;

        public String getInstanceId() {
            return instanceId;
        }

        public void setInstanceId(String instanceId) {
            this.instanceId = instanceId;
        }

        public String getBccInstanceId() {
            return bccInstanceId;
        }

        public void setBccInstanceId(String bccInstanceId) {
            this.bccInstanceId = bccInstanceId;
        }

        public String getBccInstanceName() {
            return bccInstanceName;
        }

        public void setBccInstanceName(String bccInstanceName) {
            this.bccInstanceName = bccInstanceName;
        }

        public String getVpcId() {
            return vpcId;
        }

        public void setVpcId(String vpcId) {
            this.vpcId = vpcId;
        }

        public String getVpcName() {
            return vpcName;
        }

        public void setVpcName(String vpcName) {
            this.vpcName = vpcName;
        }

        public String getVpcCidr() {
            return vpcCidr;
        }

        public void setVpcCidr(String vpcCidr) {
            this.vpcCidr = vpcCidr;
        }

        public String getBccInstanceUuid() {
            return bccInstanceUuid;
        }

        public void setBccInstanceUuid(String bccInstanceUuid) {
            this.bccInstanceUuid = bccInstanceUuid;
        }
    }
}
