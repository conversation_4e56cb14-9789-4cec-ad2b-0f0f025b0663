package com.baidu.bce.internalsdk.rds.model.database;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class DashboardDatabaseDeleteRequest {
    @NotNull
    @IdPermission
    private String instanceId;
    @NotNull
    private String dbName;
    private String code;
    private String authCode;

    @Override
    public String toString() {
        return "DashboardDatabaseDeleteRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", dbName='" + dbName + '\'' +
                ", code='" + code + '\'' +
                '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }
}
