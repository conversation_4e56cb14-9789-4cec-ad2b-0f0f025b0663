package com.baidu.bce.internalsdk.rds.model.instance;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;
import java.util.List;

public class PublicNetworkRequest {

    @IdPermission
    private String instanceId;
    @NotNull
    private List<String> inetIp;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public List<String> getInetIp() {
        return inetIp;
    }

    public void setInetIp(List<String> inetIp) {
        this.inetIp = inetIp;
    }
}
