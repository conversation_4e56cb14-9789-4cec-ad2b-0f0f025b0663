package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/11.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SnapshotListResponse {
    private Collection<Snapshot> snapshots = new LinkedList<>();

    private int freeSpaceInMB;
    private long usedBytes;
    // MB
    private int usedSpaceInMB;

    private String pageNo;

    private int pageSize;

    private int totalCount;

    @Override
    public String toString() {
        return "SnapshotListResponse{" +
                "snapshots=" + snapshots +
                '}';
    }

    public long getUsedBytes() {
        return usedBytes;
    }

    public void setUsedBytes(long usedBytes) {
        this.usedBytes = usedBytes;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public Collection<Snapshot> getSnapshots() {
        return snapshots;
    }

    public void setSnapshots(Collection<Snapshot> snapshots) {
        this.snapshots = snapshots;
    }

    public int getFreeSpaceInMB() {
        return freeSpaceInMB;
    }

    public void setFreeSpaceInMB(int freeSpaceInMB) {
        this.freeSpaceInMB = freeSpaceInMB;
    }

    public int getUsedSpaceInMB() {
        return usedSpaceInMB;
    }

    public void setUsedSpaceInMB(int usedSpaceInMB) {
        this.usedSpaceInMB = usedSpaceInMB;
    }
}
