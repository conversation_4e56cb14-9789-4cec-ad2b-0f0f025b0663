package com.baidu.bce.internalsdk.rds.model.instance;

/**
 * Created by luping03 on 17/5/5.
 */
public enum FeRegion {
    GLOBAL("global", "全局"),
    CN_N1("cn-n1", "华北－北京"),
    BJ("bj", "华北－北京"),
    GZ("gz", "华南－广州"),
    HK("hk", "香港"),
    HK02("hk02", "香港2区"),
    SU("su", "华东－苏州"),
    FSH("fsh", "金融华东－上海"),
    HKG("hkg", "香港"),
    BD("bd", "保定"),
    HBFSG("hb-fsg", "华北-FSG专区"),
    SIN("sin", "新加坡"),
    FWH("fwh", "金融华中－武汉"),
    BJFSG("bjfsg", "北京－FSG专区"),
    EDGE("edge", "边缘节点"),
    YQ("yq", " 阳泉"),
    CD("cd", "成都"),
    NJ("nj", "南京")
    ;

    private final String status;
    private final String text;

    private FeRegion(String value, String text) {
        this.status = value;
        this.text = text;
    }

    public String toText() {
        return this.text;
    }

    public static FeRegion statusOf(String statusCode) {
        FeRegion[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            FeRegion status = var1[var3];
            if (status.status.equalsIgnoreCase(statusCode) ) {
                return status;
            }
        }

        throw new IllegalArgumentException("No matching constant for [" + statusCode + "]");
    }
}
