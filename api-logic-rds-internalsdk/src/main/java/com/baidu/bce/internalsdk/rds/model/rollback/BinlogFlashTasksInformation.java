package com.baidu.bce.internalsdk.rds.model.rollback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BinlogFlashTasksInformation {


    private String dataType;
    private String groupId = "baidu";
    private String binlogFlashStartTime;
    private String binlogFlashEndTime;
    private String binlogFlashType;
    private List<SchemaTables> schemaTables;
    private List<ExistSchemaTable> existSchemaTables;
    private List<String> sqlType;
    private String name;
    private String workScenario;


    public List<ExistSchemaTable> getExistSchemaTables() {
        return existSchemaTables;
    }

    public void setExistSchemaTables(List<ExistSchemaTable> existSchemaTables) {
        this.existSchemaTables = existSchemaTables;
    }

    public String getBinlogFlashType() {
        return binlogFlashType;
    }

    public void setBinlogFlashType(String binlogFlashType) {
        this.binlogFlashType = binlogFlashType;
    }

    public List<String> getSqlType() {
        return sqlType;
    }

    public void setSqlType(List<String> sqlType) {
        this.sqlType = sqlType;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getGroupId() {
        return groupId;
    }

    public String getBinlogFlashStartTime() {
        return binlogFlashStartTime;
    }

    public void setBinlogFlashStartTime(String binlogFlashStartTime) {
        this.binlogFlashStartTime = binlogFlashStartTime;
    }

    public String getBinlogFlashEndTime() {
        return binlogFlashEndTime;
    }

    public void setBinlogFlashEndTime(String binlogFlashEndTime) {
        this.binlogFlashEndTime = binlogFlashEndTime;
    }

    public List<SchemaTables> getSchemaTables() {
        return schemaTables;
    }

    public void setSchemaTables(List<SchemaTables> schemaTables) {
        this.schemaTables = schemaTables;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWorkScenario() {
        return workScenario;
    }

    public void setWorkScenario(String workScenario) {
        this.workScenario = workScenario;
    }
}
