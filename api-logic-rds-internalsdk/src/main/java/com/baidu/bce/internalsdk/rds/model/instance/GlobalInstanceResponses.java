package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;
import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GlobalInstanceResponses {

    private List<GlobalInstanceResponse> instances;
    private Map<String, Integer> totalCount;
    private Map<String, Integer> availableCount;
    private Map<String, Integer> expiringSoonCoune;
    private Map<String, Integer> expiredCount;

    public List<GlobalInstanceResponse> getInstances() {
        return instances;
    }

    public void setInstances(List<GlobalInstanceResponse> instances) {
        this.instances = instances;
    }

    public Map<String, Integer> getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Map<String, Integer> totalCount) {
        this.totalCount = totalCount;
    }

    public Map<String, Integer> getAvailableCount() {
        return availableCount;
    }

    public void setAvailableCount(Map<String, Integer> availableCount) {
        this.availableCount = availableCount;
    }

    public Map<String, Integer> getExpiringSoonCoune() {
        return expiringSoonCoune;
    }

    public void setExpiringSoonCoune(Map<String, Integer> expiringSoonCoune) {
        this.expiringSoonCoune = expiringSoonCoune;
    }

    public Map<String, Integer> getExpiredCount() {
        return expiredCount;
    }

    public void setExpiredCount(Map<String, Integer> expiredCount) {
        this.expiredCount = expiredCount;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GlobalInstanceResponse {
        private String instanceId;
        private String instanceShortId;
        private String instanceStatus;
        private Date instanceCreateTime;
        private String region;
        private String instanceType;
        private Date instanceExpireTime;
        private int expireDate;
        private String lockMode;
        private String dbVersion;

        public String getDbVersion() {
            return dbVersion;
        }

        public void setDbVersion(String dbVersion) {
            this.dbVersion = dbVersion;
        }

        public String getLockMode() {
            return lockMode;
        }

        public void setLockMode(String lockMode) {
            this.lockMode = lockMode;
        }

        public int getExpireDate() {
            return expireDate;
        }

        public void setExpireDate(int expireDate) {
            this.expireDate = expireDate;
        }

        public Date getInstanceExpireTime() {
            return instanceExpireTime;
        }

        public void setInstanceExpireTime(Date instanceExpireTime) {
            this.instanceExpireTime = instanceExpireTime;
        }

        public String getInstanceId() {
            return instanceId;
        }

        public void setInstanceId(String instanceId) {
            this.instanceId = instanceId;
        }

        public String getInstanceShortId() {
            return instanceShortId;
        }

        public void setInstanceShortId(String instanceShortId) {
            this.instanceShortId = instanceShortId;
        }

        public String getInstanceStatus() {
            return instanceStatus;
        }

        public void setInstanceStatus(String instanceStatus) {
            this.instanceStatus = instanceStatus;
        }

        public Date getInstanceCreateTime() {
            return instanceCreateTime;
        }

        public void setInstanceCreateTime(Date instanceCreateTime) {
            this.instanceCreateTime = instanceCreateTime;
        }

        public String getRegion() {
            return region;
        }

        public void setRegion(String region) {
            this.region = region;
        }

        public String getInstanceType() {
            return instanceType;
        }

        public void setInstanceType(String instanceType) {
            this.instanceType = instanceType;
        }
    }

}
