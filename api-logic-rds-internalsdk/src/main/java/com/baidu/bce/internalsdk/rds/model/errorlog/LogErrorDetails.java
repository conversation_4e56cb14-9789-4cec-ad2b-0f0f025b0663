package com.baidu.bce.internalsdk.rds.model.errorlog;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LogErrorDetails {
    private Integer count;
    private List<LogErrorDetail> errorLogs;

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<LogErrorDetail> getErrorLogs() {
        return errorLogs;
    }

    public void setErrorLogs(List<LogErrorDetail> errorLogs) {
        this.errorLogs = errorLogs;
    }
}
