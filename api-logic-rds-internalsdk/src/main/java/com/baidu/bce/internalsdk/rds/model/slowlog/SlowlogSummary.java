package com.baidu.bce.internalsdk.rds.model.slowlog;

public class SlowlogSummary {
    private String checksum;
    private String sqlFingerprint;
    private String db;
    private Integer count;
    private Double sumQueryTime;
    private Double avgQueryTime;
    private Double maxQueryTime;
    private Double sumLockTime;
    private Double avgLockTime;
    private Double maxLockTime;
    private Long sumRowsExamined;
    private Long avgRowsExamined;
    private Long maxRowsExamined;
    private Long sumRowsSent;
    private Long avgRowsSent;
    private Long maxRowsSent;

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public String getSqlFingerprint() {
        return sqlFingerprint;
    }

    public void setSqlFingerprint(String sqlFingerprint) {
        this.sqlFingerprint = sqlFingerprint;
    }

    public String getDb() {
        return db;
    }

    public void setDb(String db) {
        this.db = db;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Double getSumQueryTime() {
        return sumQueryTime;
    }

    public void setSumQueryTime(Double sumQueryTime) {
        this.sumQueryTime = sumQueryTime;
    }

    public Double getAvgQueryTime() {
        return avgQueryTime;
    }

    public void setAvgQueryTime(Double avgQueryTime) {
        this.avgQueryTime = avgQueryTime;
    }

    public Double getMaxQueryTime() {
        return maxQueryTime;
    }

    public void setMaxQueryTime(Double maxQueryTime) {
        this.maxQueryTime = maxQueryTime;
    }

    public Double getSumLockTime() {
        return sumLockTime;
    }

    public void setSumLockTime(Double sumLockTime) {
        this.sumLockTime = sumLockTime;
    }

    public Double getAvgLockTime() {
        return avgLockTime;
    }

    public void setAvgLockTime(Double avgLockTime) {
        this.avgLockTime = avgLockTime;
    }

    public Double getMaxLockTime() {
        return maxLockTime;
    }

    public void setMaxLockTime(Double maxLockTime) {
        this.maxLockTime = maxLockTime;
    }

    public Long getSumRowsExamined() {
        return sumRowsExamined;
    }

    public void setSumRowsExamined(Long sumRowsExamined) {
        this.sumRowsExamined = sumRowsExamined;
    }

    public Long getAvgRowsExamined() {
        return avgRowsExamined;
    }

    public void setAvgRowsExamined(Long avgRowsExamined) {
        this.avgRowsExamined = avgRowsExamined;
    }

    public Long getMaxRowsExamined() {
        return maxRowsExamined;
    }

    public void setMaxRowsExamined(Long maxRowsExamined) {
        this.maxRowsExamined = maxRowsExamined;
    }

    public Long getSumRowsSent() {
        return sumRowsSent;
    }

    public void setSumRowsSent(Long sumRowsSent) {
        this.sumRowsSent = sumRowsSent;
    }

    public Long getAvgRowsSent() {
        return avgRowsSent;
    }

    public void setAvgRowsSent(Long avgRowsSent) {
        this.avgRowsSent = avgRowsSent;
    }

    public Long getMaxRowsSent() {
        return maxRowsSent;
    }

    public void setMaxRowsSent(Long maxRowsSent) {
        this.maxRowsSent = maxRowsSent;
    }
}
