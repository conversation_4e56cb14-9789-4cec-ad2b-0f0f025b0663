
package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ProxyTopoInfo {
    private TopoProxy proxy;
    private TopoMaster master;
    private List<TopoReplica> readReplicas;

    public TopoProxy getProxy() {
        return proxy;
    }

    public void setProxy(TopoProxy proxy) {
        this.proxy = proxy;
    }

    public TopoMaster getMaster() {
        return master;
    }

    public void setMaster(TopoMaster master) {
        this.master = master;
    }

    public List<TopoReplica> getReadReplicas() {
        return readReplicas;
    }

    public void setReadReplicas(List<TopoReplica> readReplicas) {
        this.readReplicas = readReplicas;
    }

    public static class TopoProxy {
        private String appId;
        private String ip;
        private int port;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }
    }

    public static class TopoMaster {
        private String appId;
        private String appIdShort;
        private String  appName;
        private int cpu;
        private int memSize;
        private String status;
        private String serveStatus;
        private int weight;
        private String ip;
        private int port;
        private String offlineIp;
        private int offlinePort;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getAppIdShort() {
            return appIdShort;
        }

        public void setAppIdShort(String appIdShort) {
            this.appIdShort = appIdShort;
        }

        public String getAppName() {
            return appName;
        }

        public void setAppName(String appName) {
            this.appName = appName;
        }

        public int getCpu() {
            return cpu;
        }

        public void setCpu(int cpu) {
            this.cpu = cpu;
        }

        public int getMemSize() {
            return memSize;
        }

        public void setMemSize(int memSize) {
            this.memSize = memSize;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getServeStatus() {
            return serveStatus;
        }

        public void setServeStatus(String serveStatus) {
            this.serveStatus = serveStatus;
        }

        public int getWeight() {
            return weight;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getOfflineIp() {
            return offlineIp;
        }

        public void setOfflineIp(String offlineIp) {
            this.offlineIp = offlineIp;
        }

        public int getOfflinePort() {
            return offlinePort;
        }

        public void setOfflinePort(int offlinePort) {
            this.offlinePort = offlinePort;
        }
    }

    public static class TopoReplica {
        private String appId;
        private String appIdShort;
        private String appName;
        private int cpu;
        private int memSize;
        private String status;
        private String ip;
        private int port;
        private String serveStatus;
        private int weight;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getAppIdShort() {
            return appIdShort;
        }

        public void setAppIdShort(String appIdShort) {
            this.appIdShort = appIdShort;
        }

        public String getAppName() {
            return appName;
        }

        public void setAppName(String appName) {
            this.appName = appName;
        }

        public int getCpu() {
            return cpu;
        }

        public void setCpu(int cpu) {
            this.cpu = cpu;
        }

        public int getMemSize() {
            return memSize;
        }

        public void setMemSize(int memSize) {
            this.memSize = memSize;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getServeStatus() {
            return serveStatus;
        }

        public void setServeStatus(String serveStatus) {
            this.serveStatus = serveStatus;
        }

        public int getWeight() {
            return weight;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }
    }
}