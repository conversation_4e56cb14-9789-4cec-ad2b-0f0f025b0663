package com.baidu.bce.internalsdk.rds.model.slowquery;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class SlowquerySqlOptimizerFeedbackResponse extends SlowqueryBaseResponse {

    private TaskDetailModel result;

    public TaskDetailModel getResult() {
        return result;
    }

    public void setResult(TaskDetailModel result) {
        this.result = result;
    }

    public static class TaskDetailModel {
        private Integer taskId;

        private String instanceId;

        private String status;

        private String downloadUrl;

        private String expireDate;

        public Integer getTaskId() {
            return taskId;
        }

        public void setTaskId(Integer taskId) {
            this.taskId = taskId;
        }

        public String getInstanceId() {
            return instanceId;
        }

        public void setInstanceId(String instanceId) {
            this.instanceId = instanceId;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDownloadUrl() {
            return downloadUrl;
        }

        public void setDownloadUrl(String downloadUrl) {
            this.downloadUrl = downloadUrl;
        }

        public String getExpireDate() {
            return expireDate;
        }

        public void setExpireDate(String expireDate) {
            this.expireDate = expireDate;
        }
    }

}
