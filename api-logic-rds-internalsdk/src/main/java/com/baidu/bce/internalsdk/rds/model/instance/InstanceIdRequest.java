package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdMapper;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class InstanceIdRequest {
    @IdPermission
    @IdMapper
    private String instanceId;

    private String effectiveTime;

    private String dataBackupType = "physical";

    private boolean switchover = false;

    private String backupMode;

    private List<DataBackupObject> dataBackupObjects;

    @Override
    public String toString() {
        return "InstanceIdRequest{" +
                "instanceId='" + instanceId + '\'' +
                '}';
    }

    public boolean getSwitchover() {
        return switchover;
    }

    public void setSwitchover(boolean switchover) {
        this.switchover = switchover;
    }

    public List<DataBackupObject> getDataBackupObjects() {
        return dataBackupObjects;
    }

    public void setDataBackupObjects(List<DataBackupObject> dataBackupObjects) {
        this.dataBackupObjects = dataBackupObjects;
    }

    public String getBackupMode() {
        return backupMode;
    }

    public void setBackupMode(String backupMode) {
        this.backupMode = backupMode;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getDataBackupType() {
        return dataBackupType;
    }

    public void setDataBackupType(String dataBackupType) {
        this.dataBackupType = dataBackupType;
    }
}
