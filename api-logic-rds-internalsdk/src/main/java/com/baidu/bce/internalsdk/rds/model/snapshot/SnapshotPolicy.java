package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/6.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SnapshotPolicy {

    @Pattern(regexp = "^((([0-1]?[0-9])|([2][0-3])):([0-5]?[0-9])(:([0-5]?[0-9]))?Z)$", message = "19:00:00Z")
    private String backupTime = "00:00:00Z";

    @Pattern(regexp = "^([0-6],){0,6}[0-6]$", message = "0,1,2,3,4,5,6")
    private String backupDays = "0,1,2,3,4,5,6";

    private Boolean persistent = false;

    @Range(min = 1, max = 730, message = "数据备份保留时长支持1-730天")
    private int expireInDays = 7;

    @Range(min = 1, max = 730, message = "日志备份保留时长支持1-730天")
    private int logBackupRetainDays = 7;

    private int freeSpaceInGB;

    private String dataBackupType;
    // OpenAPI 专用参数
    private Integer freeSpace;

    // console 与 管控侧 专用字段，前端不感知
    private List<DataBackupRetainStrategy> dataBackupRetainStrategys;

    // 是否开启高频备份，默认为 false
    private boolean incrementalDataBackupEnable;

    // 增量数据备份间隔（秒）最小粒度是小时级别
    private Integer incrementalDataBackupInterval;

    // 最新数据备份保留天数（实例删除后），天级别
    private Integer latestDataBackupRetainDays;

    // console 与 前端专用字段，管控不感知，
    private Integer retentionPeriod;

    // console 与 前端专用字段，管控不感知
    private String timeUnit = "week";

    // 标识是否开启跨地域备份
    private boolean dataBackupCopyEnable;

    private List<String> dataBackupCopyStoragesRegions;

    @Range(min = 1, max = 730, message = "跨地域备份保留时长支持1-730天")
    private Integer dataBackupCopyRetainDays;

    // console 与 管控侧 专用字段，前端不感知
    private List<DataBackupRetainStrategy> dataBackupCopyRetainStrategys;

    private EncryptStrategy encryptStrategy;

    // 本地保留策略字段
    private String binlogSizePercent;
    private String binlogExpiredHour;

    public SnapshotPolicy() {
    }

    public SnapshotPolicy(String backupTime, String backupDays, Boolean persistent, int expireInDays,
                          int freeSpaceInGB, int logBackupRetainDays, String dataBackupType) {
        this.backupTime = backupTime;
        this.backupDays = backupDays;
        this.persistent = persistent;
        this.expireInDays = expireInDays;
        this.logBackupRetainDays = logBackupRetainDays;
        this.freeSpaceInGB = freeSpaceInGB;
        this.dataBackupType = dataBackupType;
    }

    public SnapshotPolicy(String backupTime, String backupDays, Boolean persistent, int expireInDays,
                          int freeSpaceInGB, int logBackupRetainDays, String dataBackupType,
                          Integer incrementalDataBackupInterval, Boolean incrementalDataBackupEnable,
                          Integer latestDataBackupRetainDays) {
        this.backupTime = backupTime;
        this.backupDays = backupDays;
        this.persistent = persistent;
        this.expireInDays = expireInDays;
        this.logBackupRetainDays = logBackupRetainDays;
        this.freeSpaceInGB = freeSpaceInGB;
        this.dataBackupType = dataBackupType;
        this.incrementalDataBackupEnable = incrementalDataBackupEnable == null ? false : incrementalDataBackupEnable;
        this.incrementalDataBackupInterval = incrementalDataBackupInterval;
        this.latestDataBackupRetainDays = latestDataBackupRetainDays;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SnapshotPolicy that = (SnapshotPolicy) o;

        if (!backupDays.equals(that.backupDays)) {
            return false;
        }
        if (!backupTime.equals(that.backupTime)) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = backupTime.hashCode();
        result = 31 * result + backupDays.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "SnapshotPolicy{" +
                "backupTime='" + backupTime + '\'' +
                ", backupDays='" + backupDays + '\'' +
                ", persistent=" + persistent +
                ", expireInDays=" + expireInDays +
                ", logBackupRetainDays=" + logBackupRetainDays +
                ", freeSpaceInGB=" + freeSpaceInGB +
                ", freeSpace=" + freeSpace +
                '}';
    }

    public String getBinlogExpiredHour() {
        return binlogExpiredHour;
    }

    public void setBinlogExpiredHour(String binlogExpiredHour) {
        this.binlogExpiredHour = binlogExpiredHour;
    }

    public String getBinlogSizePercent() {
        return binlogSizePercent;
    }

    public void setBinlogSizePercent(String binlogSizePercent) {
        this.binlogSizePercent = binlogSizePercent;
    }

    public EncryptStrategy getEncryptStrategy() {
        return encryptStrategy;
    }

    public void setEncryptStrategy(EncryptStrategy encryptStrategy) {
        this.encryptStrategy = encryptStrategy;
    }

    public boolean getDataBackupCopyEnable() {
        return dataBackupCopyEnable;
    }

    public void setDataBackupCopyEnable(boolean dataBackupCopyEnable) {
        this.dataBackupCopyEnable = dataBackupCopyEnable;
    }

    public List<String> getDataBackupCopyStoragesRegions() {
        return dataBackupCopyStoragesRegions;
    }

    public void setDataBackupCopyStoragesRegions(List<String> dataBackupCopyStoragesRegions) {
        // 此处需兼容当前仅允许设置一个地域的逻辑
        if (dataBackupCopyStoragesRegions != null && !dataBackupCopyStoragesRegions.isEmpty()) {
            List<String> dataCopyRegions = new ArrayList<>(1);
            for (int i = 0; i < 1; i++) {
                dataCopyRegions.add(dataBackupCopyStoragesRegions.get(0));
            }
            this.dataBackupCopyStoragesRegions = dataCopyRegions;
        } else {
            this.dataBackupCopyStoragesRegions = dataBackupCopyStoragesRegions;
        }

    }

    public Integer getDataBackupCopyRetainDays() {
        return dataBackupCopyRetainDays;
    }

    public void setDataBackupCopyRetainDays(Integer dataBackupCopyRetainDays) {
        this.dataBackupCopyRetainDays = dataBackupCopyRetainDays;
    }

    public List<DataBackupRetainStrategy> getDataBackupCopyRetainStrategys() {
        return dataBackupCopyRetainStrategys;
    }

    public void setDataBackupCopyRetainStrategys(List<DataBackupRetainStrategy> dataBackupCopyRetainStrategys) {
        this.dataBackupCopyRetainStrategys = dataBackupCopyRetainStrategys;
    }

    public List<DataBackupRetainStrategy> getDataBackupRetainStrategys() {
        return dataBackupRetainStrategys;
    }

    public void setDataBackupRetainStrategys(List<DataBackupRetainStrategy> dataBackupRetainStrategys) {
        this.dataBackupRetainStrategys = dataBackupRetainStrategys;
    }

    public boolean getIncrementalDataBackupEnable() {
        return incrementalDataBackupEnable;
    }

    public void setIncrementalDataBackupEnable(boolean incrementalDataBackupEnable) {
        this.incrementalDataBackupEnable = incrementalDataBackupEnable;
    }

    public Integer getIncrementalDataBackupInterval() {
        return incrementalDataBackupInterval;
    }

    public void setIncrementalDataBackupInterval(Integer incrementalDataBackupInterval) {
        this.incrementalDataBackupInterval = incrementalDataBackupInterval;
    }

    public Integer getLatestDataBackupRetainDays() {
        return latestDataBackupRetainDays;
    }

    public void setLatestDataBackupRetainDays(Integer latestDataBackupRetainDays) {
        this.latestDataBackupRetainDays = latestDataBackupRetainDays;
    }

    public Integer getRetentionPeriod() {
        return retentionPeriod;
    }

    public void setRetentionPeriod(Integer retentionPeriod) {
        this.retentionPeriod = retentionPeriod;
    }

    public String getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(String timeUnit) {
        this.timeUnit = timeUnit;
    }

    public int getFreeSpaceInGB() {
        return freeSpaceInGB;
    }

    public void setFreeSpaceInGB(int freeSpaceInGB) {
        this.freeSpaceInGB = freeSpaceInGB;
    }

    public Integer getFreeSpace() {
        return freeSpace;
    }

    public void setFreeSpace(Integer freeSpace) {
        this.freeSpace = freeSpace;
    }

    public String getBackupDays() {
        return backupDays;
    }

    public void setBackupDays(String backupDays) {
        this.backupDays = backupDays;
    }

    public Boolean getPersistent() {
        return persistent;
    }

    public void setPersistent(Boolean persistent) {
        this.persistent = persistent;
    }

    public String getBackupTime() {
        return backupTime;
    }

    public void setBackupTime(String backupTime) {
        this.backupTime = backupTime;
    }

    public int getExpireInDays() {
        return expireInDays;
    }

    public void setExpireInDays(int expireInDays) {
        this.expireInDays = expireInDays;
    }

    public SnapshotPolicy withBackupTime(final String backupTime) {
        this.backupTime = backupTime;
        return this;
    }

    public SnapshotPolicy withBackupDays(final String backupDays) {
        this.backupDays = backupDays;
        return this;
    }

    public int getLogBackupRetainDays() {
        return logBackupRetainDays;
    }

    public void setLogBackupRetainDays(int logBackupRetainDays) {
        this.logBackupRetainDays = logBackupRetainDays;
    }

    public String getDataBackupType() {
        return dataBackupType;
    }

    public void setDataBackupType(String dataBackupType) {
        this.dataBackupType = dataBackupType;
    }
}
