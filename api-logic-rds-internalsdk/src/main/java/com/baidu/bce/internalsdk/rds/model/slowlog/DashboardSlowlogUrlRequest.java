package com.baidu.bce.internalsdk.rds.model.slowlog;

import javax.validation.constraints.NotNull;

/**
 * Created by chensilan on 15/11/27.
 */
public class DashboardSlowlogUrlRequest extends DownloadUrlRequest {
    @NotNull
    private String slowlogId;

    @Override
    public String toString() {
        return "DashboardSlowlogUrlRequest{"
                + "slowlogId='" + slowlogId + '\''
                + super.toString()
                + '}';
    }

    public String getSlowlogId() {
        return slowlogId;
    }

    public void setSlowlogId(String slowlogId) {
        this.slowlogId = slowlogId;
    }
}
