package com.baidu.bce.internalsdk.rds.model.instance;


import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
public class InstanceUpdateBackupPolicyRequest {
    private SnapshotPolicy backupPolicy;

    @Override
    public String toString() {
        return "InstanceUpdateBackupPolicy{" +
                "backupPolicy=" + backupPolicy +
                '}';
    }

    public SnapshotPolicy getBackupPolicy() {
        return backupPolicy;
    }

    public void setBackupPolicy(SnapshotPolicy backupPolicy) {
        this.backupPolicy = backupPolicy;
    }
}
