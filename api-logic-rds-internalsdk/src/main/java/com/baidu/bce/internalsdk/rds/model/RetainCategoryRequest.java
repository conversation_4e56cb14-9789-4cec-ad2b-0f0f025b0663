package com.baidu.bce.internalsdk.rds.model;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.snapshot.DataBackupRetainStrategy;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RetainCategoryRequest {
    @IdPermission
    private String instanceId;
    @Valid
    private RetainPolicy backupPolicy;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public RetainPolicy getBackupPolicy() {
        return backupPolicy;
    }

    public void setBackupPolicy(RetainPolicy backupPolicy) {
        this.backupPolicy = backupPolicy;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RetainPolicy {

        @Range(min = 1, max = 730, message = "日志备份保留时长支持1-730天")
        private int logBackupRetainDays = 7;

        @Range(min = 1, max = 730, message = "数据备份保留时长支持1-730天")
        private int expireInDays = 7;

        // 最新数据备份保留天数（实例删除后），天级别
        private Integer latestDataBackupRetainDays;

        // console 与 管控侧 专用字段，前端不感知
        private List<DataBackupRetainStrategy> dataBackupRetainStrategys;

        // 增量数据备份间隔（秒）最小粒度是小时级别
        private Integer incrementalDataBackupInterval;

        // console 与 前端专用字段，管控不感知，
        private Integer retentionPeriod;

        // console 与 前端专用字段，管控不感知
        private String timeUnit = "week";

        public int getExpireInDays() {
            return expireInDays;
        }

        public void setExpireInDays(int expireInDays) {
            this.expireInDays = expireInDays;
        }

        public int getLogBackupRetainDays() {
            return logBackupRetainDays;
        }

        public void setLogBackupRetainDays(int logBackupRetainDays) {
            this.logBackupRetainDays = logBackupRetainDays;
        }

        public Integer getLatestDataBackupRetainDays() {
            return latestDataBackupRetainDays;
        }

        public void setLatestDataBackupRetainDays(Integer latestDataBackupRetainDays) {
            this.latestDataBackupRetainDays = latestDataBackupRetainDays;
        }

        public List<DataBackupRetainStrategy> getDataBackupRetainStrategys() {
            return dataBackupRetainStrategys;
        }

        public void setDataBackupRetainStrategys(List<DataBackupRetainStrategy> dataBackupRetainStrategys) {
            this.dataBackupRetainStrategys = dataBackupRetainStrategys;
        }


        public Integer getIncrementalDataBackupInterval() {
            return incrementalDataBackupInterval;
        }

        public void setIncrementalDataBackupInterval(Integer incrementalDataBackupInterval) {
            this.incrementalDataBackupInterval = incrementalDataBackupInterval;
        }

        public Integer getRetentionPeriod() {
            return retentionPeriod;
        }

        public void setRetentionPeriod(Integer retentionPeriod) {
            this.retentionPeriod = retentionPeriod;
        }

        public String getTimeUnit() {
            return timeUnit;
        }

        public void setTimeUnit(String timeUnit) {
            this.timeUnit = timeUnit;
        }
    }
}
