package com.baidu.bce.internalsdk.rds.model.dbfirewall;

import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;

/**
 * Created by liuruisen on 2017/11/8.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardDbfwUpdateRequest extends InstanceIdRequest {

    @NotNull
    private Boolean dbfwStatus = false;

    @NotNull
    private int dbfwRule;

    public Boolean getDbfwStatus() {
        return dbfwStatus;
    }

    public void setDbfwStatus(Boolean dbfwStatus) {
        this.dbfwStatus = dbfwStatus;
    }

    public int getDbfwRule() {
        return dbfwRule;
    }

    public void setDbfwRule(int dbfwRule) {
        this.dbfwRule = dbfwRule;
    }
}
