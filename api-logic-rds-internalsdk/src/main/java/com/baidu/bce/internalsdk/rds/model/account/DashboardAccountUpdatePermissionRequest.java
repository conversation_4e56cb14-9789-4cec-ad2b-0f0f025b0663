package com.baidu.bce.internalsdk.rds.model.account;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import java.util.Collection;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DashboardAccountUpdatePermissionRequest {
    @NotNull
    @IdPermission
    private String instanceId;

    @NotNull
    private String accountName;

    private String ETag;
    private Collection<Account.DatabasePrivilege> databasePrivileges;

    @Override
    public String toString() {
        return "DashboardAccountUpdatePermissionRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", accountName='" + accountName + '\'' +
                ", ETag='" + ETag + '\'' +
                ", databasePrivileges=" + databasePrivileges +
                '}';
    }

    @JsonProperty("ETag")
    public String getETag() {
        return ETag;
    }

    @JsonProperty("ETag")
    public void setETag(String ETag) {
        this.ETag = ETag;
    }


    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Collection<Account.DatabasePrivilege> getDatabasePrivileges() {
        return databasePrivileges;
    }

    public void setDatabasePrivileges(Collection<Account.DatabasePrivilege> databasePrivileges) {
        this.databasePrivileges = databasePrivileges;
    }
}
