package com.baidu.bce.internalsdk.rds.model.account;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.NotNull;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class V2AccountUpdatePrivilegesRequest {
    @IdPermission
    private String instanceId;
    private String accountName;
    private String hostIp;
    private List<DatabasePrivilege> databasePrivileges;
    private List<String> globalPrivileges;

    public V2AccountUpdatePrivilegesRequest() {
    }

    public V2AccountUpdatePrivilegesRequest(String instanceId, String accountName, String hostIp
            , List<DatabasePrivilege> databasePrivileges, List<String> globalPrivileges) {
        this.instanceId = instanceId;
        this.accountName = accountName;
        this.hostIp = hostIp;
        this.databasePrivileges = databasePrivileges;
        this.globalPrivileges = globalPrivileges;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getHostIp() {
        return hostIp;
    }

    public void setHostIp(String hostIp) {
        this.hostIp = hostIp;
    }

    public List<DatabasePrivilege> getDatabasePrivileges() {
        return databasePrivileges;
    }

    public void setDatabasePrivileges(List<DatabasePrivilege> databasePrivileges) {
        this.databasePrivileges = databasePrivileges;
    }

    public List<String> getGlobalPrivileges() {
        return globalPrivileges;
    }

    public void setGlobalPrivileges(List<String> globalPrivileges) {
        this.globalPrivileges = globalPrivileges;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DatabasePrivilege{
        private String dbName;
        private List<String> privileges;

        public DatabasePrivilege() {
        }

        public DatabasePrivilege(String dbName, List<String> privileges) {
            this.dbName = dbName;
            this.privileges = privileges;
        }

        public String getDbName() {
            return dbName;
        }

        public void setDbName(String dbName) {
            this.dbName = dbName;
        }

        public List<String> getPrivileges() {
            return privileges;
        }

        public void setPrivileges(List<String> privileges) {
            this.privileges = privileges;
        }
    }
}
