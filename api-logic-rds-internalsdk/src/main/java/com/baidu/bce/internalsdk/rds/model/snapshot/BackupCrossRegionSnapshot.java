package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;
import java.util.UUID;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BackupCrossRegionSnapshot {
    private String instanceId;
    private String snapshotId = UUID.randomUUID().toString();
    private String dbVersion;
    private String snapshotSizeInBytes;
    private String snapshotType;
    private String snapshotStatus;
    private Date snapshotStartTime;
    private Date snapshotEndTime;
    private Date snapshotDataTime;
    private String dataBackupType;
    private String dataBackupMethod;
    private String storageRegion;

    public String getStorageRegion() {
        return storageRegion;
    }

    public void setStorageRegion(String storageRegion) {
        this.storageRegion = storageRegion;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getSnapshotId() {
        return snapshotId;
    }

    public void setSnapshotId(String snapshotId) {
        this.snapshotId = snapshotId;
    }

    public String getDbVersion() {
        return dbVersion;
    }

    public void setDbVersion(String dbVersion) {
        this.dbVersion = dbVersion;
    }

    public String getSnapshotSizeInBytes() {
        return snapshotSizeInBytes;
    }

    public void setSnapshotSizeInBytes(String snapshotSizeInBytes) {
        this.snapshotSizeInBytes = snapshotSizeInBytes;
    }

    public String getSnapshotType() {
        return snapshotType;
    }

    public void setSnapshotType(String snapshotType) {
        this.snapshotType = snapshotType;
    }

    public String getSnapshotStatus() {
        return snapshotStatus;
    }

    public void setSnapshotStatus(String snapshotStatus) {
        this.snapshotStatus = snapshotStatus;
    }

    public Date getSnapshotStartTime() {
        return snapshotStartTime;
    }

    public void setSnapshotStartTime(Date snapshotStartTime) {
        this.snapshotStartTime = snapshotStartTime;
    }

    public Date getSnapshotEndTime() {
        return snapshotEndTime;
    }

    public void setSnapshotEndTime(Date snapshotEndTime) {
        this.snapshotEndTime = snapshotEndTime;
    }

    public Date getSnapshotDataTime() {
        return snapshotDataTime;
    }

    public void setSnapshotDataTime(Date snapshotDataTime) {
        this.snapshotDataTime = snapshotDataTime;
    }

    public String getDataBackupType() {
        return dataBackupType;
    }

    public void setDataBackupType(String dataBackupType) {
        this.dataBackupType = dataBackupType;
    }

    public String getDataBackupMethod() {
        return dataBackupMethod;
    }

    public void setDataBackupMethod(String dataBackupMethod) {
        this.dataBackupMethod = dataBackupMethod;
    }
}
