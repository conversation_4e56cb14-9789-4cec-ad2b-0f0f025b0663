package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtensionHistoryResp {

    private List<ExtensionHistory> extensionHistories;

    public List<ExtensionHistory> getExtensionHistories() {
        return extensionHistories;
    }

    public void setExtensionHistories(List<ExtensionHistory> extensionHistories) {
        this.extensionHistories = extensionHistories;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExtensionHistory {
        public String name;
        private String opType;
        private String opSuccess;
        private String updateTime;
        private String remark;
        private String dbName;

        public String getDbName() {
            return dbName;
        }

        public void setDbName(String dbName) {
            this.dbName = dbName;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getOpType() {
            return opType;
        }

        public void setOpType(String opType) {
            this.opType = opType;
        }

        public String getOpSuccess() {
            return opSuccess;
        }

        public void setOpSuccess(String opSuccess) {
            this.opSuccess = opSuccess;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}
