package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;

public class StsClient extends BceClient {

    public static final String SERVICE_NAME = "STS";

    public StsClient() {
        super(EndpointManager.getEndpoint(SERVICE_NAME));
    }

    public StsClient(String ak, String sk) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), ak, sk);
    }

    public StsClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public StsClient(String endpoint) {
        super(endpoint);
    }

    /**
     * 使用AssumeRole获取临时访问凭证，包括临时AKSK和Security Token
     * @param request 调用AssumeRole的参数，包括需要获取的账户Id，角色名称，临时凭证有效时间等
     * @return 获取的临时凭证
     */
    public StsCredential assumeRole(AssumeRoleRequest request) {
        BceInternalRequest stsRequest = createAuthorizedRequest().path("/credential")
                .queryParam("assumeRole", "")
                .queryParam("accountId", request.getAccountId())
                .queryParam("roleName", request.getRoleName());
        if (request.getDurationSeconds() > 0) {
            stsRequest.queryParam("durationSeconds", request.getDurationSeconds());
        }
        if (!StringUtils.isEmpty(request.getUserId())) {
            stsRequest.queryParam("userId", request.getUserId());
        }
        if (request.isWithToken()) {
            stsRequest.queryParam("withToken", "");
        }
        stsRequest.setIsHostWithPort(true);

        return stsRequest.post(StsCredential.class);
    }

}
