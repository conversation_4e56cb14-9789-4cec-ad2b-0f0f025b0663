package com.baidu.bce.internalsdk.rds.model.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.ArrayList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/13.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfigItem implements Comparable<ConfigItem> {
    private String name;
    private String defaultValue;
    private String value;
    private String pendingValue;
    private String type;
    private Boolean dynamic;
    private Boolean modifiable;
    private String allowedValues;
    private String description;
    private String etag;
    private String configType;
    private String bestValue;
    private String attention;
    private Boolean ifCluster;
    private Boolean required;

    @Override
    public int compareTo(ConfigItem o) {
        return this.name.compareTo(o.name);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ConfigItem{");
        sb.append("name='").append(name).append('\'');
        sb.append(", defaultValue='").append(defaultValue).append('\'');
        sb.append(", value='").append(value).append('\'');
        sb.append(", pendingValue='").append(pendingValue).append('\'');
        sb.append(", type='").append(type).append('\'');
        sb.append(", dynamic=").append(dynamic);
        sb.append(", modifiable=").append(modifiable);
        sb.append(", allowedValues='").append(allowedValues).append('\'');
        sb.append(", description='").append(description).append('\'');
        sb.append(", ifCluster='").append(ifCluster).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public String getEtag() {
        return etag;
    }

    public void setEtag(String etag) {
        this.etag = etag;
    }

    public String getPendingValue() {
        return pendingValue;
    }

    public void setPendingValue(String pendingValue) {
        this.pendingValue = pendingValue;
    }

    public Boolean getDynamic() {
        return dynamic;
    }

    public void setDynamic(Boolean dynamic) {
        this.dynamic = dynamic;
    }

    public Boolean getModifiable() {
        return modifiable;
    }

    public void setModifiable(Boolean modifiable) {
        this.modifiable = modifiable;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAllowedValues() {
        return allowedValues;
    }

    public void setAllowedValues(String allowedValues) {
        this.allowedValues = allowedValues;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getBestValue() {
        return bestValue;
    }

    public void setBestValue(String bestValue) {
        this.bestValue = bestValue;
    }

    public String getAttention() {
        return attention;
    }

    public void setAttention(String attention) {
        this.attention = attention;
    }

    public Boolean getIfCluster() {
        return ifCluster;
    }

    public void setIfCluster(Boolean ifCluster) {
        this.ifCluster = ifCluster;
    }

    public static class ConfigItemList extends ArrayList<ConfigItem> {
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }
}
