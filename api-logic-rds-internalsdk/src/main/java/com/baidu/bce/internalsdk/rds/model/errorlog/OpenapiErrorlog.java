package com.baidu.bce.internalsdk.rds.model.errorlog;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.UUID;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/9.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenapiErrorlog implements Comparable<OpenapiErrorlog> {
    private String errorlogId = UUID.randomUUID().toString();
    private long errorlogSizeInBytes;

    private Date errorlogStartTime = new Date(System.currentTimeMillis());
    private Date errorlogEndTime = new Date(System.currentTimeMillis() + 10000);

    @Override
    public int compareTo(@NotNull OpenapiErrorlog o) {
        return this.errorlogStartTime.compareTo(o.errorlogStartTime);
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getErrorlogStartTime() {
        return errorlogStartTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setErrorlogStartTime(Date errorlogStartTime) {
        this.errorlogStartTime = errorlogStartTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getErrorlogEndTime() {
        return errorlogEndTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setErrorlogEndTime(Date errorlogEndTime) {
        this.errorlogEndTime = errorlogEndTime;
    }

    public String getErrorlogId() {
        return errorlogId;
    }

    public void setErrorlogId(String errorlogId) {
        this.errorlogId = errorlogId;
    }

    public long getErrorlogSizeInBytes() {
        return errorlogSizeInBytes;
    }

    public void setErrorlogSizeInBytes(long errorlogSizeInBytes) {
        this.errorlogSizeInBytes = errorlogSizeInBytes;
    }
}
