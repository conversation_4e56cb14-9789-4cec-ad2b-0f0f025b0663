package com.baidu.bce.internalsdk.rds.model.instance;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

public class DashboardInstanceModifyPortRequest {
    @IdPermission
    private String instanceId;
    private Integer entryPort;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getEntryPort() {
        return entryPort;
    }

    public void setEntryPort(Integer entryPort) {
        this.entryPort = entryPort;
    }
}
