package com.baidu.bce.internalsdk.rds.model;

import com.wordnik.swagger.annotations.ApiModelProperty;

public class UpdateEncryptPolicyRequest {

    private EncryptPolicyRequest encryptStrategy;

    public EncryptPolicyRequest getEncryptStrategy() {
        return encryptStrategy;
    }

    public void setEncryptStrategy(EncryptPolicyRequest encryptStrategy) {
        this.encryptStrategy = encryptStrategy;
    }

    public static final class EncryptPolicyRequest {

        @ApiModelProperty("是否开启存储加密")
        private boolean encryptEnable;
        @ApiModelProperty("密钥管理方式, 自托管(self_kms)/百度KMS(baidu_kms)")
        private String keyManagementType;
        @ApiModelProperty("密钥管理服务名称, 当前为rds")
        private String keyManagementServiceName;
        @ApiModelProperty("密钥ID")
        private String secretKeyID;

        public boolean getEncryptEnable() {
            return encryptEnable;
        }

        public void setEncryptEnable(boolean encryptEnable) {
            this.encryptEnable = encryptEnable;
        }

        public String getKeyManagementType() {
            return keyManagementType;
        }

        public void setKeyManagementType(String keyManagementType) {
            this.keyManagementType = keyManagementType;
        }

        public String getKeyManagementServiceName() {
            return keyManagementServiceName;
        }

        public void setKeyManagementServiceName(String keyManagementServiceName) {
            this.keyManagementServiceName = keyManagementServiceName;
        }

        public String getSecretKeyID() {
            return secretKeyID;
        }

        public void setSecretKeyID(String secretKeyID) {
            this.secretKeyID = secretKeyID;
        }
    }
}
