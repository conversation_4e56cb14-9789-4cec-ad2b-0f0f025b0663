package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OpenApiSnapshotsResponse {

    private String marker;

    private Boolean isTruncated = false;

    private String nextMarker;

    private Integer maxKeys; // 每页包含的最大数量

    private List<OpenApiBackup> backups;

    public String getMarker() {
        return marker;
    }

    public void setMarker(String marker) {
        this.marker = marker;
    }

    public Boolean getIsTruncated() {
        return isTruncated;
    }

    public void setIsTruncated(Boolean isTruncated) {
        this.isTruncated = isTruncated;
    }

    public String getNextMarker() {
        return nextMarker;
    }

    public void setNextMarker(String nextMarker) {
        this.nextMarker = nextMarker;
    }

    public Integer getMaxKeys() {
        return maxKeys;
    }

    public void setMaxKeys(Integer maxKeys) {
        this.maxKeys = maxKeys;
    }

    public List<OpenApiBackup> getBackups() {
        return backups;
    }

    public void setBackups(List<OpenApiBackup> backups) {
        this.backups = backups;
    }
}
