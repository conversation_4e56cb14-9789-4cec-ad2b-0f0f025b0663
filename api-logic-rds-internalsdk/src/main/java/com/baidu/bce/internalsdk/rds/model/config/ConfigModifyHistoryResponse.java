package com.baidu.bce.internalsdk.rds.model.config;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/19.
 */
public class ConfigModifyHistoryResponse {
    private ConfigItemModifyHistory.ConfigItemModifyHistoryList parameters;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ConfigModifyHistoryResponse{");
        sb.append("parameters=").append(parameters);
        sb.append('}');
        return sb.toString();
    }

    public ConfigItemModifyHistory.ConfigItemModifyHistoryList getParameters() {
        return parameters;
    }

    public void setParameters(ConfigItemModifyHistory.ConfigItemModifyHistoryList parameters) {
        this.parameters = parameters;
    }
}
