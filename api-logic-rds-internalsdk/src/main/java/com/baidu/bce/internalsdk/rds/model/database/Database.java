package com.baidu.bce.internalsdk.rds.model.database;


import com.baidu.bce.internalsdk.rds.model.account.AccountPrivilege;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.Pattern;
import java.util.Collection;
import java.util.LinkedList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/4.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Database {
    private String dbName;
    private String characterSetName;
    private String remark;
    private String dbStatus;

    private Collection<AccountPrivilege> accountPrivileges = new LinkedList<AccountPrivilege>();
    // pg新增参数
    private String collate;
    private String ctype;
    private String owner;

    public void addAccountPrivilege(AccountPrivilege accountPrivilege) {
        this.accountPrivileges.add(accountPrivilege);
    }

    public void removeAccountPrivilege(String accountName) {
        for (AccountPrivilege accountPrivilege : this.accountPrivileges) {
            if (accountPrivilege.getAccountName().equals(accountName)) {
                this.accountPrivileges.remove(accountPrivilege);
                break;
            }
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Database database = (Database) o;

        if (dbName != null ? !dbName.equals(database.dbName) : database.dbName != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return dbName != null ? dbName.hashCode() : 0;
    }

    /*-----------------------------------auto generated----------------------------*/
    @Override
    public String toString() {
        return "Database{" +
                "dbName='" + dbName + '\'' +
                ", characterSetName='" + characterSetName + '\'' +
                ", remark='" + remark + '\'' +
                ", dbStatus='" + dbStatus + '\'' +
                ", accountPrivileges=" + accountPrivileges +
                '}';
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getCharacterSetName() {
        return characterSetName;
    }

    public void setCharacterSetName(String characterSetName) {
        this.characterSetName = characterSetName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDbStatus() {
        return dbStatus;
    }

    public void setDbStatus(String dbStatus) {
        this.dbStatus = dbStatus;
    }

    public Collection<AccountPrivilege> getAccountPrivileges() {
        return accountPrivileges;
    }

    public void setAccountPrivileges(Collection<AccountPrivilege> accountPrivileges) {
        this.accountPrivileges = accountPrivileges;
    }

    public String getCollate() {
        return collate;
    }

    public void setCollate(String collate) {
        this.collate = collate;
    }

    public String getCtype() {
        return ctype;
    }

    public void setCtype(String ctype) {
        this.ctype = ctype;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }
}
