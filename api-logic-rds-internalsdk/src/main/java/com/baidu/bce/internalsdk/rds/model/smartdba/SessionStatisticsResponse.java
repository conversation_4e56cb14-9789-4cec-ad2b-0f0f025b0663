package com.baidu.bce.internalsdk.rds.model.smartdba;

import java.util.List;
import java.util.Map;

public class SessionStatisticsResponse {
    Map<String, List<Statistic>> statistics;

    public Map<String, List<Statistic>> getStatistics() {
        return statistics;
    }

    public void setStatistics(Map<String, List<Statistic>> statistics) {
        this.statistics = statistics;
    }

    public static class Statistic {
        private Double activeAverageExecuteTime;
        private Long activeTotalCount;
        private Double activeTotalExecuteTime;
        private String dimensionKey;
        private String dimensionValue;
        private Long totalCount;

        public Double getActiveAverageExecuteTime() {
            return activeAverageExecuteTime;
        }

        public void setActiveAverageExecuteTime(Double activeAverageExecuteTime) {
            this.activeAverageExecuteTime = activeAverageExecuteTime;
        }

        public Long getActiveTotalCount() {
            return activeTotalCount;
        }

        public void setActiveTotalCount(Long activeTotalCount) {
            this.activeTotalCount = activeTotalCount;
        }

        public Double getActiveTotalExecuteTime() {
            return activeTotalExecuteTime;
        }

        public void setActiveTotalExecuteTime(Double activeTotalExecuteTime) {
            this.activeTotalExecuteTime = activeTotalExecuteTime;
        }

        public String getDimensionKey() {
            return dimensionKey;
        }

        public void setDimensionKey(String dimensionKey) {
            this.dimensionKey = dimensionKey;
        }

        public String getDimensionValue() {
            return dimensionValue;
        }

        public void setDimensionValue(String dimensionValue) {
            this.dimensionValue = dimensionValue;
        }

        public Long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Long totalCount) {
            this.totalCount = totalCount;
        }
    }
}
