package com.baidu.bce.internalsdk.rds.model.migration;

import java.util.ArrayList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/9.
 */


public class CheckItem {
    private String checkItem;
    private String checkMethod;
    private Boolean result;
    private String repairMethod;
    private String reason;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("CheckItem{");
        sb.append("checkItem='").append(checkItem).append('\'');
        sb.append(", checkMethod='").append(checkMethod).append('\'');
        sb.append(", result=").append(result);
        sb.append(", repairMethod='").append(repairMethod).append('\'');
        sb.append(", reason='").append(reason).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getCheckItem() {
        return checkItem;
    }

    public void setCheckItem(String checkItem) {
        this.checkItem = checkItem;
    }

    public String getCheckMethod() {
        return checkMethod;
    }

    public void setCheckMethod(String checkMethod) {
        this.checkMethod = checkMethod;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String getRepairMethod() {
        return repairMethod;
    }

    public void setRepairMethod(String repairMethod) {
        this.repairMethod = repairMethod;
    }

    public static class CheckItemList extends ArrayList<CheckItem> {
    }
}
