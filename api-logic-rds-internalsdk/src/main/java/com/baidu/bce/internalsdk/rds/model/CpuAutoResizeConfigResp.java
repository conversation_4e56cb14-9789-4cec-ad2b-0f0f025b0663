package com.baidu.bce.internalsdk.rds.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CpuAutoResizeConfigResp {
    private int autoResizeCpu;
    private int scaleUpThreshold;
    private int scaleDownThreshold;
    private int scaleUpObservationTime;
    private int scaleDownObservationTime;
    private String scaleSpec;
    private String originalSpec;
    private String effectiveTime;

    public String getOriginalSpec() {
        return originalSpec;
    }

    public void setOriginalSpec(String originalSpec) {
        this.originalSpec = originalSpec;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public int getAutoResizeCpu() {
        return autoResizeCpu;
    }

    public void setAutoResizeCpu(int autoResizeCpu) {
        this.autoResizeCpu = autoResizeCpu;
    }

    public int getScaleUpThreshold() {
        return scaleUpThreshold;
    }

    public void setScaleUpThreshold(int scaleUpThreshold) {
        this.scaleUpThreshold = scaleUpThreshold;
    }

    public int getScaleDownThreshold() {
        return scaleDownThreshold;
    }

    public void setScaleDownThreshold(int scaleDownThreshold) {
        this.scaleDownThreshold = scaleDownThreshold;
    }

    public int getScaleUpObservationTime() {
        return scaleUpObservationTime;
    }

    public void setScaleUpObservationTime(int scaleUpObservationTime) {
        this.scaleUpObservationTime = scaleUpObservationTime;
    }

    public int getScaleDownObservationTime() {
        return scaleDownObservationTime;
    }

    public void setScaleDownObservationTime(int scaleDownObservationTime) {
        this.scaleDownObservationTime = scaleDownObservationTime;
    }

    public String getScaleSpec() {
        return scaleSpec;
    }

    public void setScaleSpec(String scaleSpec) {
        this.scaleSpec = scaleSpec;
    }
}
