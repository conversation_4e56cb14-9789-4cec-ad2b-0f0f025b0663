package com.baidu.bce.internalsdk.rds.model.account;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DashboardAccountUpdatePasswordRequest {
    @NotNull
    @IdPermission
    private String instanceId;

    @NotNull
    private String accountName;
    private String code;

    private String password;

    private String encryptedPassword;

    @Override
    public String toString() {
        return "DashboardAccountUpdatePasswordRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", accountName='" + accountName + '\'' +
                ", code='" + code + '\'' +
                ", password='" + password + '\'' +
                '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEncryptedPassword() {
        return encryptedPassword;
    }

    public void setEncryptedPassword(String encryptedPassword) {
        this.encryptedPassword = encryptedPassword;
    }
}
