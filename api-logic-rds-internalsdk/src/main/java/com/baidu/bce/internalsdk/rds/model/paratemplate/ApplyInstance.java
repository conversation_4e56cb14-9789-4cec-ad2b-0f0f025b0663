package com.baidu.bce.internalsdk.rds.model.paratemplate;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class ApplyInstance {

    private String shortId;

    private String instanceId;

    private String engine;

    private String engineVersion;

    private String instanceName;

    private Date instanceCreateTime;

    private String applicationType;

    private String instanceStatus;

    private String azone;

    private String Region;

    private String instanceType;

    private String sourceInstanceId;

    public ApplyInstance() {
    }

    public ApplyInstance(String shortId, String instanceId, String engine, String engineVersion, String instanceName,
                         Date instanceCreateTime, String applicationType, String instanceStatus, String azone,
                         String region) {
        this.shortId = shortId;
        this.instanceId = instanceId;
        this.engine = engine;
        this.engineVersion = engineVersion;
        this.instanceName = instanceName;
        this.instanceCreateTime = instanceCreateTime;
        this.applicationType = applicationType;
        this.instanceStatus = instanceStatus;
        this.azone = azone;
        Region = region;
    }

    public String getShortId() {
        return shortId;
    }

    public void setShortId(String shortId) {
        this.shortId = shortId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getEngineVersion() {
        return engineVersion;
    }

    public void setEngineVersion(String engineVersion) {
        this.engineVersion = engineVersion;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getInstanceCreateTime() {
        return instanceCreateTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setInstanceCreateTime(Date instanceCreateTime) {
        this.instanceCreateTime = instanceCreateTime;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }

    public String getInstanceStatus() {
        return instanceStatus;
    }

    public void setInstanceStatus(String instanceStatus) {
        this.instanceStatus = instanceStatus;
    }

    public String getAzone() {
        return azone;
    }

    public void setAzone(String azone) {
        this.azone = azone;
    }

    public String getRegion() {
        return Region;
    }

    public void setRegion(String region) {
        Region = region;
    }

    public String getInstanceType() {
        return instanceType;
    }

    public void setInstanceType(String instanceType) {
        this.instanceType = instanceType;
    }

    public String getSourceInstanceId() {
        return sourceInstanceId;
    }

    public void setSourceInstanceId(String sourceInstanceId) {
        this.sourceInstanceId = sourceInstanceId;
    }

    @Override
    public String toString() {
        return "ApplyInstance{" +
                "shortId='" + shortId + '\'' +
                ", instanceId='" + instanceId + '\'' +
                ", engine='" + engine + '\'' +
                ", engineVersion='" + engineVersion + '\'' +
                ", instanceName='" + instanceName + '\'' +
                ", instanceCreateTime='" + instanceCreateTime + '\'' +
                ", applicationType='" + applicationType + '\'' +
                ", instanceStatus='" + instanceStatus + '\'' +
                ", azone='" + azone + '\'' +
                ", Region='" + Region + '\'' +
                '}';
    }
}
