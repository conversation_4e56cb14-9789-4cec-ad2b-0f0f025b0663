package com.baidu.bce.internalsdk.rds.model;

public class InstanceRegion {

    private String region;

    private int instanceTotalCount;

    private int instanceRunningCount;

    private int instanceWillExpireCount; // 即将过期

    private int instanceExpiredCount; // 已过期

    private int InstanceMysqlCount;

    private int InstanceSqlServerCount;

    private int InstancePGCount;

    public InstanceRegion() {
    }

    public InstanceRegion(String region, int instanceTotalCount, int instanceRunningCount,
                          int instanceWillExpireCount, int instanceExpiredCount,
                          int instanceMysqlCount, int instanceSqlServerCount, int instancePGCount) {
        this.region = region;
        this.instanceTotalCount = instanceTotalCount;
        this.instanceRunningCount = instanceRunningCount;
        this.instanceWillExpireCount = instanceWillExpireCount;
        this.instanceExpiredCount = instanceExpiredCount;
        InstanceMysqlCount = instanceMysqlCount;
        InstanceSqlServerCount = instanceSqlServerCount;
        InstancePGCount = instancePGCount;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public int getInstanceTotalCount() {
        return instanceTotalCount;
    }

    public void setInstanceTotalCount(int instanceTotalCount) {
        this.instanceTotalCount = instanceTotalCount;
    }

    public int getInstanceRunningCount() {
        return instanceRunningCount;
    }

    public void setInstanceRunningCount(int instanceRunningCount) {
        this.instanceRunningCount = instanceRunningCount;
    }

    public int getInstanceWillExpireCount() {
        return instanceWillExpireCount;
    }

    public void setInstanceWillExpireCount(int instanceWillExpireCount) {
        this.instanceWillExpireCount = instanceWillExpireCount;
    }

    public int getInstanceExpiredCount() {
        return instanceExpiredCount;
    }

    public void setInstanceExpiredCount(int instanceExpiredCount) {
        this.instanceExpiredCount = instanceExpiredCount;
    }

    public int getInstanceMysqlCount() {
        return InstanceMysqlCount;
    }

    public void setInstanceMysqlCount(int instanceMysqlCount) {
        InstanceMysqlCount = instanceMysqlCount;
    }

    public int getInstanceSqlServerCount() {
        return InstanceSqlServerCount;
    }

    public void setInstanceSqlServerCount(int instanceSqlServerCount) {
        InstanceSqlServerCount = instanceSqlServerCount;
    }

    public int getInstancePGCount() {
        return InstancePGCount;
    }

    public void setInstancePGCount(int instancePGCount) {
        InstancePGCount = instancePGCount;
    }

    @Override
    public String toString() {
        return "InstanceRegion{" +
                "region='" + region + '\'' +
                ", instanceTotalCount=" + instanceTotalCount +
                ", instanceRunningCount=" + instanceRunningCount +
                ", instanceWillExpireCount=" + instanceWillExpireCount +
                ", instanceExpiredCount=" + instanceExpiredCount +
                ", InstanceMysqlCount=" + InstanceMysqlCount +
                ", InstanceSqlServerCount=" + InstanceSqlServerCount +
                ", InstancePGCount=" + InstancePGCount +
                '}';
    }
}
