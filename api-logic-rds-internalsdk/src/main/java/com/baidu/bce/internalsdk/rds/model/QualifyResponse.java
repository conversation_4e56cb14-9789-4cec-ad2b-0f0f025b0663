package com.baidu.bce.internalsdk.rds.model;


public class QualifyResponse<T> {

    private Integer status = 200;

    private T result;

    public QualifyResponse() {
    }

    public QualifyResponse(Integer status, T result) {
        this.status = status;
        this.result = result;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "ParaTemplateResponse{" +
                "status=" + status +
                ", result=" + result +
                '}';
    }
}
