package com.baidu.bce.internalsdk.rds.model.performance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by yangxin on 2022/1/14
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProcessListResponse {

    private String datetime;
    private List<Process> processList;

    public String getDatetime() {
        return datetime;
    }

    public void setDatetime(String datetime) {
        this.datetime = datetime;
    }

    public List<Process> getProcessList() {
        return processList;
    }

    public void setProcessList(List<Process> processList) {
        this.processList = processList;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Process {
        private String sql;
        private String db;
        private String state;
        private String host;
        private String command;
        private String user;
        private Integer time;
        private Integer id;
        private Map<String, List<Integer>> lockHold;
        private Set<Map.Entry<String, List<Integer>>> lockHoldEntrySet;
        private List<Process.LockWait> lockWait;

        public Object getSql() {
            return sql;
        }

        public void setSql(String sql) {
            this.sql = sql;
        }

        public Object getDb() {
            return db;
        }

        public void setDb(String db) {
            this.db = db;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public String getCommand() {
            return command;
        }

        public void setCommand(String command) {
            this.command = command;
        }

        public String getUser() {
            return user;
        }

        public void setUser(String user) {
            this.user = user;
        }

        public Integer getTime() {
            return time;
        }

        public void setTime(Integer time) {
            this.time = time;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public List<Process.LockWait> getLockWait() {
            return lockWait;
        }

        public void setLockWait(List<Process.LockWait> lockWait) {
            this.lockWait = lockWait;
        }

        public Map<String, List<Integer>> getLockHold() {
            return lockHold;
        }

        public void setLockHold(Map<String, List<Integer>> lockHold) {
            this.lockHold = lockHold;
        }

        public Set<Map.Entry<String, List<Integer>>> getLockHoldEntrySet() {
            if (lockHold != null) {
                return lockHold.entrySet();
            }
            return lockHoldEntrySet;
        }

        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class LockWait {
            private String lockId;
            private int id;

            public String getLockId() {
                return lockId;
            }

            public void setLockId(String lockId) {
                this.lockId = lockId;
            }

            public int getId() {
                return id;
            }

            public void setId(int id) {
                this.id = id;
            }
        }
    }
}
