package com.baidu.bce.internalsdk.rds.model.group;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GroupForceChangeLeaderRequest {

    private String leaderId;

    private Integer force = 0;

    private Integer maxBehind;

    public GroupForceChangeLeaderRequest() {
    }

    public String getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(String leaderId) {
        this.leaderId = leaderId;
    }

    public Integer getForce() {
        return force;
    }

    public void setForce(Integer force) {
        this.force = force;
    }

    public Integer getMaxBehind() {
        return maxBehind;
    }

    public void setMaxBehind(Integer maxBehind) {
        this.maxBehind = maxBehind;
    }
}
