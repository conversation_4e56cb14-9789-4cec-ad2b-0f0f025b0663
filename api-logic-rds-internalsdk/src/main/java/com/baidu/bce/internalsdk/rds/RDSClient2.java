package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.*;
import com.baidu.bce.internalsdk.rds.model.AutoResizeConfigResponse;
import com.baidu.bce.internalsdk.rds.model.BasicCategoryRequest;
import com.baidu.bce.internalsdk.rds.model.CommonResponse;
import com.baidu.bce.internalsdk.rds.model.CpuAutoResizeConfigResp;
import com.baidu.bce.internalsdk.rds.model.CpuAutoResizeEnableResp;
import com.baidu.bce.internalsdk.rds.model.MajorVersionPrecheckResponse;
import com.baidu.bce.internalsdk.rds.model.ModifyCpuAutoResizeReq;
import com.baidu.bce.internalsdk.rds.model.RetainCategoryRequest;
import com.baidu.bce.internalsdk.rds.model.SourceCheckResponse;
import com.baidu.bce.internalsdk.rds.model.SplitCopyRegionPolicyRequest;
import com.baidu.bce.internalsdk.rds.model.SupportEnabledDiskAutoResizeResponse;
import com.baidu.bce.internalsdk.rds.model.UpdateAutoExpansionConfigRequest;
import com.baidu.bce.internalsdk.rds.model.UpdateEncryptPolicyReq;
import com.baidu.bce.internalsdk.rds.model.UpdateLoaclPolicyRequest;
import com.baidu.bce.internalsdk.rds.model.account.*;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogDateTime;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogListResponse;
import com.baidu.bce.internalsdk.rds.model.config.*;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCreateRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseGetResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.database.GetTableSizeRequest;
import com.baidu.bce.internalsdk.rds.model.database.GetTableSizeResponse;
import com.baidu.bce.internalsdk.rds.model.database.ListDatabasesRequest;
import com.baidu.bce.internalsdk.rds.model.database.ListDatabasesResponse;
import com.baidu.bce.internalsdk.rds.model.database.ListTableRequest;
import com.baidu.bce.internalsdk.rds.model.database.ListTableResponse;
import com.baidu.bce.internalsdk.rds.model.database.TableLevelListDatabaseResponse;
import com.baidu.bce.internalsdk.rds.model.instance.*;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.migration.*;
import com.baidu.bce.internalsdk.rds.model.rollback.CheckTimeResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.PreCheckTimeRequest;
import com.baidu.bce.internalsdk.rds.model.rollback.RollbackListResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlRollbackResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlRollbacksRequest;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlTaskDetailResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.TaskParamResponse;
import com.baidu.bce.internalsdk.rds.model.security.SecurityIp;
import com.baidu.bce.internalsdk.rds.model.security.SecurityIpPutRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityIpResponse;
import com.baidu.bce.internalsdk.rds.model.security.V2ListRequest;
import com.baidu.bce.internalsdk.rds.model.security.V2SecurityIpResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogListResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.*;
import endpoint.EndpointManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.ws.rs.core.GenericType;
import java.util.Collection;
import java.util.Date;

/**
 * Created by hejianbin on 2014/6/4.
 */
public class RDSClient2 extends BceClient {
    private static final String SERVICE_NAME = "RDS2";

    private String baseURL = "";
    private String instanceBase = baseURL + "/instance";
    private String flashBase = baseURL + "/flashback";
    private String snapshotBase = "/backup";

    public RDSClient2(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    public RDSClient2(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public RDSClient2(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public BceInternalRequest createRdsRequest() {
        BceClientConfig config = new BceClientConfig();
        config.withReadTimeout(30000)
                .withMaxConnTotal(400)
                .withMaxConnPerRoute(400);
        BceInternalRequest bceInternalRequest = BceInternalClient.request(endpoint, config)
                .authorization(accessKey, secretKey);
        if (!StringUtils.isEmpty(securityToken)) {
            bceInternalRequest.securityToken(securityToken);
        }
        return bceInternalRequest;
    }

    /*-----------------------------------instance----------------------------------*/
    public InstanceListResponse instanceCreate(InstanceCreateRequest request) {
        BceInternalRequest internalRequest = createRdsRequest()
                .path(instanceBase);
        if (request.getInstanceParameters().getSourceInstanceId() != null
                && !"".equals(request.getInstanceParameters().getSourceInstanceId())) {
            if (request.getInstanceParameters().getEngine().equals("rdsproxy")) {
                internalRequest.keyOnlyQueryParam("rdsproxy");
            } else {
                internalRequest.keyOnlyQueryParam("readReplica");
            }

        }
        return internalRequest.post(Entity.json(request), InstanceListResponse.class);
    }

    public InstanceListResponse instanceList() {
        return createRdsRequest()
                .path(this.instanceBase)
                .get(InstanceListResponse.class);
    }

    public InstanceGetResponse instanceDescribe(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .get(InstanceGetResponse.class);
    }

    public void instanceDelete(String instanceId) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .delete(CommonResponse.class);
    }

    public void instanceModifyName(String instanceId, String name) {
        InstanceUpdateNameRequest request = new InstanceUpdateNameRequest();
        request.setInstanceName(name);
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("name")
                .put(Entity.json(request));
    }

    public void instanceModifyPubliclyAccessible(String instanceId, boolean publiclyAccessible) {
        InstanceUpdatePublicAccessibleRequest request = new InstanceUpdatePublicAccessibleRequest();
        request.setPubliclyAccessible(publiclyAccessible);
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("publiclyAccessible")
                .put(Entity.json(request));
    }

    public void instanceModifyBackupPolicy(String instanceId, SnapshotPolicy snapshotPolicy) {
        InstanceUpdateBackupPolicyRequest request = new InstanceUpdateBackupPolicyRequest();
        request.setBackupPolicy(snapshotPolicy);
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
//                .keyOnlyQueryParam("backupPolicy")
                .put(Entity.json(request));
    }

    public void instanceModifyDomain(String instanceId, String domain) {
        InstanceUpdateAddressRequest request = new InstanceUpdateAddressRequest();
        request.endpoint(new Instance.Endpoint().address(domain));
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("endpoint")
                .put(Entity.json(request));
    }

    public void instanceReboot(String instanceId) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("reboot")
                .put();
    }

    public void instanceLock(String instanceId, String lockMode) {
        InstanceLockRequest request = new InstanceLockRequest();
        request.setLockMode(lockMode);
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("lock")
                .put(Entity.json(request));
    }

    public void instanceLockBatch(String instanceIds, String lockMode) {
        InstanceLockRequest request = new InstanceLockRequest();
        request.setLockMode(lockMode);
        request.setInstanceIds(instanceIds);
        createRdsRequest()
                .path(this.instanceBase + "/batch/lockmode")
                .put(Entity.json(request));
    }

    public void instanceRestoreSnapshot(String instanceId, String snapshotId) {
        InstanceRestoreRequest request = new InstanceRestoreRequest();
        request.setSnapshotId(snapshotId);
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("restore")
                .put(Entity.json(request));
    }

    public void instanceUpdateFlavor(String instanceId, String clientToken,
                                     InstanceUpdateFlavorRequest instanceUpdateFlavorRequest) {
        createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .queryParam("clientToken", clientToken)
                .put(Entity.json(instanceUpdateFlavorRequest));
    }

    public InstanceReplicaDelayMaster getReplicaDelay(String instanceId) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceId)
                .keyOnlyQueryParam("secondsBehindMaster")
                .get(InstanceReplicaDelayMaster.class);
    }

    public OrderInfo getOrderInfo(String orderId) {
        return createRdsRequest().path("/order/" + orderId).get(OrderInfo.class);
    }

    public HotUpgradeResponse checkHotUpgrade(String instanceId, HotupgradeRequest request) {
        return createRdsRequest()
                .path(this.baseURL + "/instance/" + instanceId + "/precheck/hotupgrade")
                .post(Entity.json(request), HotUpgradeResponse.class);
    }

    public InstanceChangeDiskTypeResponse changeDiskType(String instanceId, String targetDiskType) {
        return createRdsRequest()
                .path(this.baseURL + "/instance/" + instanceId + "/precheck/changeDiskType")
                .queryParam("targetDiskType", targetDiskType)
                .get(InstanceChangeDiskTypeResponse.class);
    }

    public InstancePrecheckParameterResponse precheckParameter(String instanceId,
                                                               InstancePrecheckParameterRequest request) {
        return createRdsRequest()
                .path(this.baseURL + "/instance/" + instanceId + "/precheck/parameter")
                .post(Entity.json(request), InstancePrecheckParameterResponse.class);
    }

    public PrecheckResourceCreateResponse precheckResourceCreate(PrecheckResourceCreateRequest request) {
        return createRdsRequest()
                .path(this.baseURL + "/instance/" + "precheck/resourceCreate")
                .post(Entity.json(request), PrecheckResourceCreateResponse.class);
    }

    /*------------------------------account ---------------------------------*/
    public V2AccountListResponse accountList(String instanceID) {
        return createRdsRequest()
                .path(this.instanceBase + "/" + instanceID + "/accounts")
                .get(V2AccountListResponse.class);
    }

    public AccountShowResponse accountDescribe(String instanceId, String accountName) {
        return createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .get(AccountShowResponse.class);
    }

    public void accountCreate(String instanceId, Account request) {
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/create")
                .post(Entity.json(request));
    }

    public void accountModifyPassword(String instanceId, String accountName, AccountUpdatePasswordRequest request) {
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName+ "/password")
                .put(Entity.json(request));
    }

    public void accountModifyPrivilege(String instanceId,
                                       V2AccountUpdatePrivilegesRequest request) {

        createRdsRequest()
                .path(this.accountBase(instanceId) + "/privileges")
                .put(Entity.json(request));

    }

    public void accountModifyRemark(String instanceId, String accountName, String remark) {
        AccountUpdateRemarkRequest request = new AccountUpdateRemarkRequest();
        request.setRemark(remark);
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .keyOnlyQueryParam("remark")
                .put(Entity.json(request));
    }

    public void accountDelete(String instanceId, String accountName) {
        createRdsRequest()
                .path(this.accountBase(instanceId) + "/" + accountName)
                .delete();
    }

    /*----------------------------database -----------------------*/
    public DatabaseListResponse databaseList(String instanceId) {
        return createRdsRequest()
                .path(this.databaseBase(instanceId))
                .get(DatabaseListResponse.class);
    }

    public DatabaseGetResponse databaseDescribe(String instanceId, String dbName) {
        return createRdsRequest()
                .path(this.databaseBase(instanceId) + "/" + dbName)
                .get(DatabaseGetResponse.class);
    }

    public void databaseDelete(String instanceId, String dbName) {
        createRdsRequest()
                .path(this.databaseBase(instanceId) + "/" + dbName)
                .delete();
    }

    public void databaseCreate(String instanceId, DatabaseCreateRequest request) {
        createRdsRequest()
                .path(this.databaseBase(instanceId))
                .post(Entity.json(request));
    }

    public void databaseModifyRemark(String instanceId, String dbName, String remark) {
        DatabaseUpdateRemarkRequest request = new DatabaseUpdateRemarkRequest();
        request.setRemark(remark);
        createRdsRequest()
                .path(this.databaseBase(instanceId) + "/" + dbName)
                .keyOnlyQueryParam("remark")
                .put(Entity.json(request));
    }

    /*----------------------------snapshot -------------------------*/
    public SnapshotListWithTimeResponse snapshotListWithTime(String instanceId, String subPath) {
        return createRdsRequest()
                .path(snapshotBase + "/" + subPath)
                .queryParam("instanceId", instanceId)
                .get(SnapshotListWithTimeResponse.class);
    }

    // 供raft使用
    public SnapshotListWithTimeResponse snapshotList(String instanceId, String startTime, String endTime) {
        return createRdsRequest()
                .path(backupBase(instanceId) + "/snapshot")
                .queryParam("startDatetime", startTime)
                .queryParam("stopDatetime", endTime)
                .get(SnapshotListWithTimeResponse.class);
    }

    public SnapshotListWithTimeResponse snapshotList(String instanceId) {
        return createRdsRequest()
                .path(snapshotBase + "/" + instanceId + "/snapshot")
                .get(SnapshotListWithTimeResponse.class);
    }

    public SnapshotListWithTimeResponse snapshotList(DashboardbackupListRequest request) {
        BceInternalRequest internalRequest = createRdsRequest()
                .path(snapshotBase + "/" + request.getInstanceId() + "/snapshot");
        if (StringUtils.isNotEmpty(request.getStartDatetime())) {
            internalRequest.queryParam("startDateTime", request.getStartDatetime());
        }
        if (StringUtils.isNotEmpty(request.getStopDatetime())) {
            internalRequest.queryParam("endDateTime", request.getStopDatetime());
        }
        if (request.getPageNo() != 0) {
            internalRequest.queryParam("marker", request.getPageNo());
        }
        if (request.getPageSize() != 0) {
            internalRequest.queryParam("maxKeys", request.getPageSize());
        }
        return internalRequest.get(SnapshotListWithTimeResponse.class);
    }

    public SnapshotGetResponse snapshotGet(String instanceId, String snapshotId,
                                           Integer downloadValidTimeInSec) {
        return createRdsRequest()
                .path(snapshotBase + "/" + instanceId)
                .path("/snapshot" + "/" + snapshotId)
                .queryParam("downloadValidTimeInSec", downloadValidTimeInSec)
                .get(SnapshotGetResponse.class);
    }

    public void snapshotCreate(InstanceIdRequest request) {
        if (CollectionUtils.isNotEmpty(request.getDataBackupObjects())) {
            createRdsRequest()
                    .path(snapshotBase + "/" + request.getInstanceId())
                    .post(Entity.json(new BackupModeRequest(
                            "snapshot",request.getDataBackupType(), request.getDataBackupObjects())));
        } else {
            createRdsRequest()
                    .path(snapshotBase + "/" + request.getInstanceId())
                    .post(Entity.json(new BackupModeRequest("snapshot",request.getDataBackupType())));
        }


    }

    public void snapshotCreateRaft(String instanceId) {
        createRdsRequest()
                .path(backupBase(instanceId) + "/snapshot")
                .post();
    }

    public void snapshotDelete(String instanceId, String snapshotId) {
        createRdsRequest()
                .path(snapshotBase + "/" + instanceId + "/snapshot/" + snapshotId )
                .delete();
    }

    /*--------------------------------binlog-----------------------*/
    public BinlogListResponse binlogList(String instanceId, Date date) {
        return createRdsRequest()
                .path(binlogBase(instanceId))
                .queryParam("datetime", BceFormat.getDateTimeFormat().format(date))
                .get(BinlogListResponse.class);
    }

    public BinlogGetResponse binlogGet(String instanceId, String binlogId, Integer downloadValidTimeInSec) {
        return createRdsRequest()
                .path(binlogBase(instanceId) + "/" + binlogId)
                .queryParam("downloadValidTimeInSec", downloadValidTimeInSec)
                .get(BinlogGetResponse.class);
    }

    public void binlogCheck(String instanceId, Date date) {
        BinlogDateTime dateTime = new BinlogDateTime();
        dateTime.setDatetime(date);
        createRdsRequest()
                .path(binlogCheck(instanceId))
                .post(Entity.json(dateTime));
    }

    /*-------------------------------slowlog---------------------------*/
    public SlowlogListResponse slowlogList(String instanceId, Date date) {
        return createRdsRequest()
                .path(slowlogBase(instanceId))
                .queryParam("datetime", BceFormat.getDateTimeFormat().format(date))
                .get(SlowlogListResponse.class);
    }

    public SlowlogGetResponse slowlogGet(String instanceId, String slowlogId, Integer downloadValidTimeInSec) {
        return createRdsRequest()
                .path(slowlogBase(instanceId) + "/" + slowlogId)
                .queryParam("downloadValidTimeInSec", downloadValidTimeInSec)
                .get(SlowlogGetResponse.class);
    }

//    /*-------------------------------log---------------------------*/
//    public ApiLogListResponse apiLogList(String instanceId, Date startTime, Date endTime) {
//        return createRdsRequest()
//                .path(instanceBase + "/" + instanceId + "/apiLog")
//                .queryParam("startTime", BceFormat.getDateTimeFormat().format(startTime))
//                .queryParam("endTime", BceFormat.getDateTimeFormat().format(endTime))
//                .get(ApiLogListResponse.class);
//    }

    /*-------------------------------white list--------------------*/
    public SecurityIp queryWhiteList(String instanceID) {
        BceInternalResponse response = createRdsRequest()
                .path(securityIpBase(instanceID))
                .getWitResponse();
        SecurityIpResponse securityIpResponse = response.getEntity(SecurityIpResponse.class);
        SecurityIp securityIp = new SecurityIp();
        String ETag = response.header("Etag");
        if (ETag == null) {
            ETag = response.header("ETag");
        }
        securityIp.withETag(ETag)
                .withIps(securityIpResponse.getSecurityIps());
        response.close();
        return securityIp;
    }

    public void updateWhiteList(String instanceId, Collection<String> ips, String etag) {
        SecurityIpPutRequest request = new SecurityIpPutRequest();
        request.setSecurityIps(ips);
        createRdsRequest()
                .path(securityIpBase(instanceId))
                .put(Entity.json(request));
    }

    /*-------------------------------white list v2--------------------*/
    public V2SecurityIpResponse v2QueryWhiteList(String instanceID, V2ListRequest requestBody) {
        BceInternalResponse response =  createRdsRequest()
                .path(securityIpBase(instanceID))
                .postWithResponse(Entity.json(requestBody));
        V2SecurityIpResponse v2SecurityIpResponse = response.getEntity(V2SecurityIpResponse.class);
        String eTag = response.header("Etag");
        if (eTag == null) {
            eTag = response.header("ETag");
        }
        v2SecurityIpResponse.setETag(eTag);
        response.close();
        return v2SecurityIpResponse;
    }

    public void v2updateWhiteList(String instanceId, SecurityIpPutRequest request) {
        createRdsRequest()
                .path(securityIpBase(instanceId))
                .header("x-bce-if-match", request.getETag())
                .put(Entity.json(request));
    }

    public void updateWhiteListNew(String instanceId, SecurityIpPutRequest request) {
        createRdsRequest()
                .path(securityIpBase(instanceId))
                .keyOnlyQueryParam("new")
                .put(Entity.json(request));
    }

    public void deleteWhiteList(String instanceId, SecurityIpPutRequest request) {
        createRdsRequest()
                .path(securityIpBase(instanceId))
                .keyOnlyQueryParam("delete")
                .header("x-bce-if-match", request.getETag())
                .put(Entity.json(request));
    }

    /*----------------------------migration-----------------------------------*/

    public MigrationsResponse.MigrationList getMigrationList(String instanceId, Date startTime, Date endTime) {
        BceInternalRequest request = this.createRdsRequest()
                .path(migrationBase(instanceId));
        if (startTime != null) {
            request.queryParam("startTime", BceFormat.getDateTimeFormat().format(startTime));
        }
        if (endTime != null) {
            request.queryParam("endTime", BceFormat.getDateTimeFormat().format(endTime));
        }

        return request.get(MigrationsResponse.class).getMigrations();
    }


    public MigrationStatus getMigrationStatus(String instanceId, String migrationId) {
        return this.createRdsRequest()
                .path(migrationBase(instanceId))
                .path("/" + migrationId)
                .get(MigrationStatusResponse.class).getMigration();
    }

    public MigrationDbList getMigrationDbList(String instanceId, DatabaseConfig migrationConfig) {
        return this.createRdsRequest()
                .path(migrationBase(instanceId))
                .queryParam("action", "getDbList")
                .queryParam("remoteIp", migrationConfig.getRemoteIp())
                .queryParam("remotePort", migrationConfig.getRemotePort())
                .queryParam("remoteUser", migrationConfig.getRemoteUser())
                .queryParam("remotePassword", migrationConfig.getRemotePassword())
                .get(MigrationDbList.class);
    }

    public String migrationCheck(String instanceId, MigrationConfig migrationConfig) {
        return this.createRdsRequest()
                .path(migrationBase(instanceId))
                .queryParam("action", "check")
                .post(Entity.json(migrationConfig), MigrationId.class).getMigrationId();
    }

    public void createMigrationTask(String instanceId, String migrationId) {
        this.createRdsRequest()
                .path(migrationBase(instanceId))
                .path("/" + migrationId)
                .post();
    }

    public void cancelMigrationTask(String instanceId, String migrationId) {
        this.createRdsRequest()
                .path(migrationBase(instanceId))
                .path("/" + migrationId)
                .queryParam("action", "cancel")
                .put();
    }

    public void stopMigrationTask(String instanceId, String migrationId) {
        this.createRdsRequest()
                .path(migrationBase(instanceId))
                .path("/" + migrationId)
                .queryParam("action", "stopReplicate")
                .put();
    }

    /*------------------------------config------------------------------------*/
    public ConfigItem.ConfigItemList getConfigItems(String instanceId) {
        BceInternalResponse response = this.createRdsRequest()
                .path(configBase(instanceId))
                .getWitResponse();
        ConfigItem.ConfigItemList parameters = response.getEntity(ConfigItemListResponse.class).getParameters();
        String eTag = response.header("Etag");
        if (eTag == null) {
            eTag = response.header("ETag");
        }
        response.close();
        for (ConfigItem configItem : parameters) {
            configItem.setEtag(eTag);
        }
        return parameters;
    }

    public void modifyConfigItem(String instanceId, ConfigModifyItem.ConfigModifyItemList items) {
        ConfigModifyRequest configModifyRequest = new ConfigModifyRequest();
        configModifyRequest.setParameters(items);
        this.createRdsRequest()
                .path(configBase(instanceId))
                .header("x-bce-if-match", items.get(0).getEtag())
                .put(Entity.json(configModifyRequest));
    }

    public ConfigItemModifyHistory.ConfigItemModifyHistoryList getConfigModifyHistory(String instanceId) {
        return this.createRdsRequest()
                .path(configBase(instanceId))
                .keyOnlyQueryParam("history")
                .get(ConfigModifyHistoryResponse.class).getParameters();
    }

    /*----------------------------end-----------------------------------------*/

    private String backupBase(String instanceId) {
        return this.instanceBase + instanceId + "/backup";
    }

    private String accountBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/account";
    }

    private String databaseBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/database";
    }

    private String binlogBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/binlog";
    }

    private String binlogCheck(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/binlog/check";
    }

    private String slowlogBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/slowlog";
    }

    private String securityIpBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/securityIp";
    }

    private String migrationBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/migration";
    }

    private String configBase(String instanceId) {
        return this.instanceBase + "/" + instanceId + "/parameter";
    }

    public SupportEnabledDiskAutoResizeResponse supportAutoExpansion(String instanceId) {

        return this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/precheck/enableDiskAutoResize")
                .get(SupportEnabledDiskAutoResizeResponse.class);
    }

    public AutoResizeConfigResponse getAutoExpansionConfig(String instanceId) {
        return this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/disk/autoresize/config")
                .get(AutoResizeConfigResponse.class);
    }

    public void updateAutoExpansionConfig(String instanceId, String action, UpdateAutoExpansionConfigRequest request) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/disk/autoresize/config");
        if (action != null) {
            internalRequest.queryParam("action", action);
        }

        internalRequest.put(Entity.json(request));
    }


    public InstanceListResponse getRecyclerInstances() {
        return this.createRdsRequest()
                .path(this.instanceBase)
                .keyOnlyQueryParam("recycle")
                .get(InstanceListResponse.class);
    }

    public SqlRollbackResponse rollbackTask(String instanceId, SqlRollbacksRequest request) {
        return this.createRdsRequest()
                .path(this.flashBase + "/create/" + instanceId)
                .post(Entity.json(request), SqlRollbackResponse.class);

    }

    public CheckTimeResponse checkTime(String instanceId, PreCheckTimeRequest request) {

        return this.createRdsRequest()
                .path(this.flashBase + "/checkTime/" + instanceId)
                .post(Entity.json(request), CheckTimeResponse.class);
    }

    public RollbackListResponse taskList(String instanceId, Integer marker, Integer maxKeys) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.flashBase + "/list/" + instanceId);
        if (marker != null) {
            internalRequest.queryParam("marker", marker);
        }
        if (maxKeys != null) {
            internalRequest.queryParam("maxKeys", maxKeys);
        }

        return internalRequest.getWitResponse().getEntity(RollbackListResponse.class);



    }

    public SqlTaskDetailResponse detail(String instanceId, String taskID) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.flashBase + "/process/" + instanceId);

        if (taskID != null) {
            internalRequest.queryParam("taskID", taskID);
        }

        return internalRequest.getWitResponse().getEntity(new GenericType<SqlTaskDetailResponse>(){});
    }

    public TaskParamResponse getTaskParameters(String instanceId, String taskID) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.flashBase + "/parameter/" + instanceId);

        if (taskID != null) {
            internalRequest.queryParam("taskID", taskID);
        }

        return internalRequest.getWitResponse().getEntity(TaskParamResponse.class);
    }

    public ListDatabasesResponse listDatabasesV2(ListDatabasesRequest listDatabasesRequest) {
        BceInternalRequest request = createRdsRequest()
                .path(this.instanceBase + "/" + listDatabasesRequest.getInstanceId() + "/listdatabases");
        if (listDatabasesRequest.getPageNo() != null) {
            request.queryParam("pageNo", listDatabasesRequest.getPageNo());
        }
        if (listDatabasesRequest.getPageSize() != null ) {
            request.queryParam("pageSize", listDatabasesRequest.getPageSize());
        }
        if (listDatabasesRequest.getPattern() != null ) {
            request.queryParam("pattern", listDatabasesRequest.getPattern());
        }
        return request.get(ListDatabasesResponse.class);
    }

    public TableLevelListDatabaseResponse tableLevelListdatabasesV2(ListTableRequest listDatabasesRequest) {
        BceInternalRequest request = createRdsRequest()
                .path(this.instanceBase + "/" + listDatabasesRequest.getInstanceId() + "/listdatabases");
        if (listDatabasesRequest.getPageNo() != null) {
            request.queryParam("pageNo", listDatabasesRequest.getPageNo());
        }
        if (listDatabasesRequest.getPageSize() != null ) {
            request.queryParam("pageSize", listDatabasesRequest.getPageSize());
        }
        if (listDatabasesRequest.getPattern() != null ) {
            request.queryParam("pattern", listDatabasesRequest.getPattern());
        }
        if (StringUtils.isNotEmpty(listDatabasesRequest.getPatternTable())
                || listDatabasesRequest.getPatternTable().equals("")) {
            request.queryParam("patternTable", listDatabasesRequest.getPatternTable());
        }
        return request.get(TableLevelListDatabaseResponse.class);
    }

    public ListTableResponse listTableV2(ListTableRequest listTableRequest) {

        BceInternalRequest request = createRdsRequest()
                .path(this.instanceBase + "/" + listTableRequest.getInstanceId() + "/listtables");
        if (listTableRequest.getPageNo() != null) {
            request.queryParam("pageNo", listTableRequest.getPageNo());
        }
        if (listTableRequest.getPageSize() != null ) {
            request.queryParam("pageSize", listTableRequest.getPageSize());
        }
        if (listTableRequest.getPattern() != null ) {
            request.queryParam("pattern", listTableRequest.getPattern());
        }
        if (listTableRequest.getDbName() != null ) {
            request.queryParam("dbName", listTableRequest.getDbName());
        }
        if (StringUtils.isNotEmpty(listTableRequest.getPatternTable())
                || listTableRequest.getPatternTable().equals("")) {
            request.queryParam("patternTable", listTableRequest.getPatternTable());
        }
        return request.get(ListTableResponse.class);
    }

    public GetTableSizeResponse getTableSize(GetTableSizeRequest getTableSizeRequest) {

        BceInternalRequest request = createRdsRequest()
                .path(this.instanceBase + "/" + getTableSizeRequest.getInstanceId() + "/gettablesize");
        return request.post(Entity.json(getTableSizeRequest.getTableInfo()), GetTableSizeResponse.class);
    }

    public ClusterStatusResponce clusterStatus(String instanceId) {
        return this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/cluster/status")
                .get(ClusterStatusResponce.class);
    }

    public ProbeInstanceResponse probeInstance(String instanceId) {
        BceInternalRequest rdsRequest = this.createRdsRequest();
        return rdsRequest.path(this.instanceBase + "/" + instanceId + "/probeInstanceAlive")
                .get(ProbeInstanceResponse.class);

    }

    public SlaInstanceResponse availableInstance(String instanceId) {
        return this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/availability")
                .get(SlaInstanceResponse.class);
    }

    /*--------------------------------table level client--------------------------------------*/
    public TableLevelListResponse listTableLevel(String instanceId) {
        BceInternalRequest internalRequest = this.createRdsRequest();
        return internalRequest.path(this.instanceBase + "/" + instanceId + "/mysql/account")
                .get(TableLevelListResponse.class);
    }

    public void accountCreateTableLevel(String instanceId, TableLevelAccountDetailRequest account) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/mysql/account")
                .post(Entity.json(account));
    }

    public TableLevelAccountDetailRequest detailTableLevel(TableLevelDeleteAccountRequest request) {
        BceInternalRequest internalRequest = this.createRdsRequest()
        .path(this.instanceBase + "/" + request.getInstanceId() + "/mysql/account/privilege");
        if (StringUtils.isNotEmpty(request.getHost())) {
            internalRequest.queryParam("host", request.getHost());
        }
        if (StringUtils.isNotEmpty(request.getUser())) {
            internalRequest.queryParam("user", request.getUser());
        }

        return internalRequest.get(TableLevelAccountDetailRequest.class);

    }

    public void updateRemarkTableLevel(TableLevelUpdateDescRequest request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId() + "/mysql/account/remark")
                .put(Entity.json(request));
    }

    public void accountModifyPasswordTableLevel(TableLevelUpdatePasswordRequest request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId() + "/mysql/account/password")
                .put(Entity.json(request));
    }

    public void accountModifyPrivilegeTableLevel(TableLevelUpdatePrivilegeRequest request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId() + "/mysql/account/privilege")
                .put(Entity.json(request));
    }

    public void deleteAccountTableLevel(TableLevelDeleteAccountRequest request) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId() + "/mysql/account");
        if (StringUtils.isNotEmpty(request.getHost())) {
            internalRequest.queryParam("host", request.getHost());
        }
        if (StringUtils.isNotEmpty(request.getUser())) {
            internalRequest.queryParam("user", request.getUser());
        }
        internalRequest.delete();
    }

    public TableLevelPrivilegeScopeResponse permissionScope(String instanceId) {
        return this.createRdsRequest().
                path(this.instanceBase + "/" + instanceId + "/mysql/account/privilege/scope")
                .get(TableLevelPrivilegeScopeResponse.class);
    }

    public void updateBasicBackupPolicy(BasicCategoryRequest request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId() + "/updateBasePolicy")
                .put(Entity.json(request));

    }

    public void updateRetainBackupPolicy(RetainCategoryRequest request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId() + "/updateRetainPolicy")
                .put(Entity.json(request));
    }

    public void updateCopyRegionBackupPolicy(SplitCopyRegionPolicyRequest request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId() + "/updateCopyPolicy")
                .put(Entity.json(request));
    }

    public void updateEncryptPolicy(UpdateEncryptPolicyReq request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + request.getInstanceId() + "/updateEncryptPolicy")
                .put(Entity.json(request));
    }

    public BackupCrossRegionListResponses getCrossRegionList(String instanceId, String startDataTime,
                                                             String endDataTime, String marker, String maxKeys,
                                                             String storageRegion) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.snapshotBase + "/" + instanceId + "/snapshotCopy");
        if (StringUtils.isNotEmpty(startDataTime)) {
            internalRequest.queryParam("startDataTime", startDataTime);
        }
        if (StringUtils.isNotEmpty(endDataTime)) {
            internalRequest.queryParam("endDataTime", endDataTime);
        }
        if (StringUtils.isNotEmpty(marker)) {
            internalRequest.queryParam("marker", marker);
        }
        if (StringUtils.isNotEmpty(maxKeys)) {
            internalRequest.queryParam("maxKeys", maxKeys);
        }
        if (StringUtils.isNotEmpty(storageRegion)) {
            internalRequest.queryParam("storageRegion", storageRegion);
        }
        return internalRequest.get(BackupCrossRegionListResponses.class);
    }

    public CopyRegionSnapshotDetail getCrossRegionDetail(String instanceId, String snapshotId) {
        return this.createRdsRequest()
                .path(this.snapshotBase + "/" + instanceId + "/snapshotCopy/" + snapshotId)
                .get(CopyRegionSnapshotDetail.class);
    }

    public BackupTargetRegionResponse getTargetRegionList(String instanceId) {
        return this.createRdsRequest()
                .path(this.snapshotBase + "/" + instanceId + "/snapshotRegions")
                .get(BackupTargetRegionResponse.class);
    }

    public BackupUsageResponse getBackupUsage(String instanceId) {
        return this.createRdsRequest()
                .path(this.snapshotBase + "/" + instanceId + "/usage")
                .get(BackupUsageResponse.class);
    }

    public void updateLocalBinlogPolicy(String instanceId, UpdateLoaclPolicyRequest request) {
        this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/updateLocalBinlogPolicy")
                .put(Entity.json(request));
    }

    public MajorVersionPrecheckResponse precheckOfMajorVersion(String instanceId, String checkType) {
        return this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/upgradePrecheck")
                .queryParam("checkType", checkType)
                .get(MajorVersionPrecheckResponse.class);
    }

    public SourceCheckResponse dtsSourceCheck(String instanceId) {
        return this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/dtsSourceCheck")
                .get(SourceCheckResponse.class);
    }

    public ListAllCrossRegions getListCopyRegions(String instanceId, String startDataTime,
                                                  String endDataTime, String dataBackupType) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.snapshotBase + "/" + instanceId + "/listCopyRegions");
        if (StringUtils.isNotEmpty(startDataTime)) {
            internalRequest.queryParam("startDataTime", startDataTime);
        }
        if (StringUtils.isNotEmpty(endDataTime)) {
            internalRequest.queryParam("endDataTime", endDataTime);
        }
        if (StringUtils.isNotEmpty(dataBackupType)) {
            internalRequest.queryParam("dataBackupType", dataBackupType);
        }
        return internalRequest.get(ListAllCrossRegions.class);
    }

    public CpuAutoResizeEnableResp enableCpuAutoResize(String instanceId) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/precheck/enableCpuAutoResize");
        return internalRequest.get(CpuAutoResizeEnableResp.class);
    }

    public CpuAutoResizeConfigResp getCpuAutoResize(String instanceId) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/cpu/autoresize/config");
        return internalRequest.get(CpuAutoResizeConfigResp.class);
    }

    public void modifyCpuAutoResize(String instanceId, String action, ModifyCpuAutoResizeReq request) {
        BceInternalRequest internalRequest = this.createRdsRequest()
                .path(this.instanceBase + "/" + instanceId + "/cpu/autoresize/config");

        if (StringUtils.isNotEmpty(action)) {
            internalRequest.queryParam("action", action);
        }

        internalRequest.put(Entity.json(request));
    }
}
