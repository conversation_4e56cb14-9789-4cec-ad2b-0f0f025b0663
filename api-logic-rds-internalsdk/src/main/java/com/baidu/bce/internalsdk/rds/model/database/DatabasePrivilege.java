package com.baidu.bce.internalsdk.rds.model.database;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/11.
 */
public class DatabasePrivilege {
    private String dbName;
    private String authType;

    @Override
    public String toString() {
        return "DatabasePrivilege{" +
                "dbName='" + dbName + '\'' +
                ", authType='" + authType + '\'' +
                '}';
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }
}
