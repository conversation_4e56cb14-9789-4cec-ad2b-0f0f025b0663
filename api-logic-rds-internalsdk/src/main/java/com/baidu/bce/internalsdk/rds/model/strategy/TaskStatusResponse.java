package com.baidu.bce.internalsdk.rds.model.strategy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskStatusResponse {

    private CheckStatusResponse topoCheck;
    private CheckStatusResponse delayCheck;
    private CheckStatusResponse trxCheck;


    public CheckStatusResponse getTopoCheck() {
        return topoCheck;
    }

    public void setTopoCheck(CheckStatusResponse topoCheck) {
        this.topoCheck = topoCheck;
    }

    public CheckStatusResponse getDelayCheck() {
        return delayCheck;
    }

    public void setDelayCheck(CheckStatusResponse delayCheck) {
        this.delayCheck = delayCheck;
    }

    public CheckStatusResponse getTrxCheck() {
        return trxCheck;
    }

    public void setTrxCheck(CheckStatusResponse trxCheck) {
        this.trxCheck = trxCheck;
    }
}
