package com.baidu.bce.internalsdk.rds.model;


import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateAutoExpansionConfigRequest {

    private Integer freeSpaceThreshold;
    private Integer diskMaxLimit;

    public Integer getFreeSpaceThreshold() {
        return freeSpaceThreshold;
    }

    public void setFreeSpaceThreshold(Integer freeSpaceThreshold) {
        this.freeSpaceThreshold = freeSpaceThreshold;
    }

    public Integer getDiskMaxLimit() {
        return diskMaxLimit;
    }

    public void setDiskMaxLimit(Integer diskMaxLimit) {
        this.diskMaxLimit = diskMaxLimit;
    }
}
