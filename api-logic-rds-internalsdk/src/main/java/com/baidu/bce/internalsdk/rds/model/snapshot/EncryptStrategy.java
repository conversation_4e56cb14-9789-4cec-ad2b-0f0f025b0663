package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wordnik.swagger.annotations.ApiModelProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EncryptStrategy {
    @ApiModelProperty("开关状态")
    private Boolean encryptEnable;
    @ApiModelProperty("密钥管理服务名称")
    private String keyManagementServiceName;
    @ApiModelProperty("密钥管理方式,自托管(self_kms)/百度KMS(baidu_kms)")
    private String keyManagementType;
    @ApiModelProperty("密钥ID")
    private String secretKeyID;

    public Boolean getEncryptEnable() {
        return encryptEnable;
    }

    public void setEncryptEnable(Boolean encryptEnable) {
        this.encryptEnable = encryptEnable;
    }

    public String getKeyManagementServiceName() {
        return keyManagementServiceName;
    }

    public void setKeyManagementServiceName(String keyManagementServiceName) {
        this.keyManagementServiceName = keyManagementServiceName;
    }

    public String getKeyManagementType() {
        return keyManagementType;
    }

    public void setKeyManagementType(String keyManagementType) {
        this.keyManagementType = keyManagementType;
    }

    public String getSecretKeyID() {
        return secretKeyID;
    }

    public void setSecretKeyID(String secretKeyID) {
        this.secretKeyID = secretKeyID;
    }
}
