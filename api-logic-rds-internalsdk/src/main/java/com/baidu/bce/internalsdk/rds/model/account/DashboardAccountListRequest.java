package com.baidu.bce.internalsdk.rds.model.account;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdMapper;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardAccountListRequest {
    @IdMapper
    @IdPermission
    private String instanceId;
    private String orderBy;
    private String order;
    private int pageNo;

    @Override
    public String toString() {
        return "DashboardAccountListRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", orderBy='" + orderBy + '\'' +
                ", order='" + order + '\'' +
                ", pageNo=" + pageNo +
                '}';
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}
