package com.baidu.bce.internalsdk.rds.model.instance;

/**
 * Created by luping03 on 17/5/5.
 */
public enum FeProductType {
    PREPAY("prepay", "预付费"),
    POSTPAY("postpay", "后付费");

    private final String status;
    private final String text;

    private FeProductType(String value, String text) {
        this.status = value;
        this.text = text;
    }

    public String toText() {
        return this.text;
    }

    public static FeProductType statusOf(String statusCode) {
        FeProductType[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            FeProductType status = var1[var3];
            if (status.status.equalsIgnoreCase(statusCode) ) {
                return status;
            }
        }

        throw new IllegalArgumentException("No matching constant for [" + statusCode + "]");
    }
}
