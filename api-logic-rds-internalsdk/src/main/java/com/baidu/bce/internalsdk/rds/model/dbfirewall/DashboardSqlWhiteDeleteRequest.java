package com.baidu.bce.internalsdk.rds.model.dbfirewall;

import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;

import javax.validation.constraints.NotNull;

/**
 * Created by liuruisen on 2017/11/9.
 */
public class DashboardSqlWhiteDeleteRequest extends InstanceIdRequest {

    @NotNull
    private String sqlMd5;

    public String getSqlMd5() {
        return sqlMd5;
    }

    public void setSqlMd5(String sqlMd5) {
        this.sqlMd5 = sqlMd5;
    }
}
