package com.baidu.bce.internalsdk.rds.model.errorlog;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LogSlowDetails {
    private Integer count;
    private List<LogSlowDetail> slowLogs;

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<LogSlowDetail> getSlowLogs() {
        return slowLogs;
    }

    public void setSlowLogs(List<LogSlowDetail> slowLogs) {
        this.slowLogs = slowLogs;
    }
}
