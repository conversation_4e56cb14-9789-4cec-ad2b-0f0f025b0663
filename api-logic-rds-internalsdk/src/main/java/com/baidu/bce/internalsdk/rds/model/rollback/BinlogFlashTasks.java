package com.baidu.bce.internalsdk.rds.model.rollback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BinlogFlashTasks {

    private String status;
    private String failedReason;
    private String startDateTime;
    private String endDateTime;
    private Integer affectRowsNumber;
    private String downloadUrl;
    private String taskName;
    private List<SchemaTables> schemaTables;


    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFailedReason() {
        return failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }

    public String getStartDateTime() {
        return startDateTime;
    }

    public void setStartDateTime(String startDateTime) {
        this.startDateTime = startDateTime;
    }

    public String getEndDateTime() {
        return endDateTime;
    }

    public void setEndDateTime(String endDateTime) {
        this.endDateTime = endDateTime;
    }

    public Integer getAffectRowsNumber() {
        return affectRowsNumber;
    }

    public void setAffectRowsNumber(Integer affectRowsNumber) {
        this.affectRowsNumber = affectRowsNumber;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public List<SchemaTables> getSchemaTables() {
        return schemaTables;
    }

    public void setSchemaTables(List<SchemaTables> schemaTables) {
        this.schemaTables = schemaTables;
    }
}
