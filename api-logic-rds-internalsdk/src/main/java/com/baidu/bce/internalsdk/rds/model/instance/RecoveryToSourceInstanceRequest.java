package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecoveryToSourceInstanceRequest {

    private String sourceInstanceId;

    @IdPermission
    private String targetInstanceId;

    private String sourceAppId;

    private String datetime;

    private String snapshotId;

    private List<RecoveryToSourceInstanceModel> data;

    public String getDatetime() {
        return datetime;
    }

    public void setDatetime(String datetime) {
        this.datetime = datetime;
    }

    public List<RecoveryToSourceInstanceModel> getData() {
        return data;
    }

    public void setData(List<RecoveryToSourceInstanceModel> data) {
        this.data = data;
    }


    public String getSnapshotId() {
        return snapshotId;
    }

    public void setSnapshotId(String snapshotId) {
        this.snapshotId = snapshotId;
    }



    public void setSourceInstanceId(String sourceInstanceId) {
        this.sourceInstanceId = sourceInstanceId;
    }

    public String getTargetInstanceId() {
        return targetInstanceId;
    }

    public void setTargetInstanceId(String targetInstanceId) {
        this.targetInstanceId = targetInstanceId;
    }

    public String getSourceAppId() {
        return sourceInstanceId;
    }

    public static class RecoveryToSourceInstanceModel {
        private String dbName;

        private String newDbname;

        private String restoreMode;

        private List<RecoveryToSourceInstanceTableModel> tables;

        public String getDbName() {
            return dbName;
        }

        public void setDbName(String dbName) {
            this.dbName = dbName;
        }

        public String getNewDbname() {
            return newDbname;
        }

        public void setNewDbname(String newDbname) {
            this.newDbname = newDbname;
        }

        public String getRestoreMode() {
            return restoreMode;
        }

        public void setRestoreMode(String restoreMode) {
            this.restoreMode = restoreMode;
        }

        public List<RecoveryToSourceInstanceTableModel> getTables() {
            return tables;
        }

        public void setTables(List<RecoveryToSourceInstanceTableModel> tables) {
            this.tables = tables;
        }
    }

    public static class RecoveryToSourceInstanceTableModel {
        private String tableName;

        private String newTablename;

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public String getNewTablename() {
            return newTablename;
        }

        public void setNewTablename(String newTablename) {
            this.newTablename = newTablename;
        }
    }



}
