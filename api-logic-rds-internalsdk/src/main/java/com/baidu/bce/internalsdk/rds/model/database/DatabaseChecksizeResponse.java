package com.baidu.bce.internalsdk.rds.model.database;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/11.
 */
public class DatabaseChecksizeResponse {

    private Map<String, Map<String, Long>> databases;

    private Long restSize;

    private Integer totalAmount;

    private Integer returnAmount;

    public Long getRestSize() {
        return restSize;
    }

    public void setRestSize(Long restSize) {
        this.restSize = restSize;
    }

    public Integer getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Integer totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(Integer returnAmount) {
        this.returnAmount = returnAmount;
    }


    public Map<String, Map<String, Long>> getDatabases() {
        return databases;
    }

    public void setDatabases(Map<String, Map<String, Long>> databases) {
        this.databases = databases;
    }
}
