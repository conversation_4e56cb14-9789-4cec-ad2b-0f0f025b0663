package com.baidu.bce.internalsdk.rds.model.binlog;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.UUID;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/9.
 */
public class OpenapiBinlog implements Comparable<OpenapiBinlog> {
    private String binlogId = UUID.randomUUID().toString();
    private long binlogSizeInBytes;
    private String binlogStatus;


    private Date binlogStartTime = new Date(System.currentTimeMillis());
    private Date binlogEndTime = new Date(System.currentTimeMillis() + 10000);
    private String binlogRealId;

    @Override
    public int compareTo(@NotNull OpenapiBinlog o) {
        return this.binlogStartTime.compareTo(o.binlogStartTime);
    }

    public void setLogId(String logId) {
        this.binlogId = logId;
    }

    public void setLogSizeInBytes(long logSizeInBytes) {
        this.binlogSizeInBytes = logSizeInBytes;
    }

    public void setLogStatus(String logStatus) {
        this.binlogStatus = logStatus;
    }

    public void setLogStartTime(Date logStartTime) {
        this.binlogStartTime = logStartTime;
    }

    public void setLogEndTime(Date logEndTime) {
        this.binlogEndTime = logEndTime;
    }

    public String getBinlogId() {
        return binlogId;
    }

    public void setBinlogId(String binlogId) {
        this.binlogId = binlogId;
    }

    public String getBinlogStatus() {
        return binlogStatus;
    }

    public long getBinlogSizeInBytes() {
        return binlogSizeInBytes;
    }

    public void setBinlogSizeInBytes(long binlogSizeInBytes) {
        this.binlogSizeInBytes = binlogSizeInBytes;
    }

    public void setBinlogStatus(String binlogStatus) {
        this.binlogStatus = binlogStatus;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getBinlogStartTime() {
        return binlogStartTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setBinlogStartTime(Date binlogStartTime) {
        this.binlogStartTime = binlogStartTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getBinlogEndTime() {
        return binlogEndTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setBinlogEndTime(Date binlogEndTime) {
        this.binlogEndTime = binlogEndTime;
    }

    public OpenapiBinlog binlogId(final String binlogId) {
        this.binlogId = binlogId;
        return this;
    }

    public OpenapiBinlog binlogSizeInBytes(final long binlogSizeInBytes) {
        this.binlogSizeInBytes = binlogSizeInBytes;
        return this;
    }

    public OpenapiBinlog binlogStatus(final String binlogStatus) {
        this.binlogStatus = binlogStatus;
        return this;
    }

    public OpenapiBinlog binStartTime(final Date binStartTime) {
        this.binlogStartTime = binStartTime;
        return this;
    }

    public OpenapiBinlog binEndTime(final Date binEndTime) {
        this.binlogEndTime = binEndTime;
        return this;
    }

    public String getBinlogRealId() {
        return binlogRealId;
    }

    public void setBinlogRealId(String binlogRealId) {
        this.binlogRealId = binlogRealId;
    }
}

