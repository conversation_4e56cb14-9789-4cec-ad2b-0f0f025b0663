package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryBaseResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryChartRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryChartResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryCreateTaskRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryCreateTaskResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDetailRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDetailResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDownloadRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDownloadResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryGetSqlRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryGetSqlResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryInstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryOpenStatusResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlExplainResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerFeedbackRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryStartOrStopResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySummaryRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySummaryResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskDetailResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskListRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskListResponse;
import endpoint.EndpointManager;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;

/**
 * Created by luping03 on 17/10/18.
 */
public class SlowqueryClient extends BceClient {

    private static final String SERVICE_NAME = "slowquery";

    private String baseURL = "/json-api/v1/slowquery";
    private String baseURLV2 = "/json-api/v1/slowquery";
    private String chartBase = "/chart";
    private String detailBase = "/detail";
    private String summaryBase = "/summary";
    private String downloadfileBase = "/downloadfile";
    private String getSqlBase = "/sql";
    private String statusBase = "/status";
    private String startBase = "/start";
    private String stopBase = "/stop";
    private String taskBase = "/download/task";
    private String taskListBase = taskBase + "/list";
    private String taskDetailBase =  "/download/task";

    // sqlOptimizer
    private String  sqlOptimizerBase =  "/sql/optimizer";

    private String  sqlExplainBase =  "/sql/explain";

    private String sqlOptimizerFeedbackBase =  "/sql/optimizer/feedback";

    public BceInternalRequest createSlowqueryRequest() {
        return super.createAuthorizedRequest();
    }

    public SlowqueryClient(String endpoint, String accessKey, String secretKey, String securityToken) {
        super(endpoint, accessKey, secretKey, securityToken);
    }

    public SlowqueryClient(String endpoint, String accessKey, String secretKey) {
        super(endpoint, accessKey, secretKey);
    }

    public SlowqueryClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public SlowqueryChartResponse chart(SlowqueryChartRequest request, String from) {

//        StringBuilder sb = new StringBuilder();
//        sb.append("?").append("instanceId=").append(request.getInstanceId())
//                .append("&startTime=").append(request.getStartTime())
//                .append("&endTime=").append(request.getEndTime())
//                .append("&summaryType=").append(request.getSummaryType());

        if (StringUtils.isNotBlank(from) && ObjectUtils.equals(from, "api")) {
            return createSlowqueryRequest()
                    .path(this.baseURLV2 + "/" + request.getInstanceId() + chartBase)
                    .queryParams(request)
                    .get( SlowqueryChartResponse.class);
        } else {
            return createSlowqueryRequest()
                    .path(this.baseURL + "/" + request.getInstanceId() + chartBase)
                    .queryParams(request)
                    .get( SlowqueryChartResponse.class);
        }

    }

    public SlowqueryDetailResponse detail(SlowqueryDetailRequest request, String from) {

        if (StringUtils.isNotBlank(from) && ObjectUtils.equals(from, "api")) {
            return createSlowqueryRequest()
                    .path(this.baseURLV2 + "/" + request.getInstanceId() + detailBase)
                    .queryParams(request)
                    .get( SlowqueryDetailResponse.class);
        } else {
            return createSlowqueryRequest()
                    .path(this.baseURL + "/" + request.getInstanceId() + detailBase)
                    .queryParams(request)
                    .get( SlowqueryDetailResponse.class);
        }


    }

    public SlowquerySummaryResponse summary(SlowquerySummaryRequest request, String from) {

        if (StringUtils.isNotBlank(from) && ObjectUtils.equals(from, "api")) {
            return createSlowqueryRequest()
                    .path(this.baseURLV2 + "/" + request.getInstanceId() + summaryBase)
                    .queryParams(request)
                    .get( SlowquerySummaryResponse.class);
        } else {
            return createSlowqueryRequest()
                    .path(this.baseURL + "/" + request.getInstanceId() + summaryBase)
                    .queryParams(request)
                    .get( SlowquerySummaryResponse.class);
        }

    }

    public SlowqueryGetSqlResponse getSql(SlowqueryGetSqlRequest request) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + request.getInstanceId() + getSqlBase)
                .queryParams(request)
                .get(SlowqueryGetSqlResponse.class);

    }
    public SlowqueryDownloadResponse downloadfile(SlowqueryDownloadRequest request) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + request.getInstanceId() + downloadfileBase)
                .queryParams(request)
                .get(SlowqueryDownloadResponse.class);
    }

    public SlowqueryOpenStatusResponse getOpenStatus(String instanceId) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + instanceId + statusBase)
//                .queryParams(request)
                .get(SlowqueryOpenStatusResponse.class);
    }

    public SlowqueryStartOrStopResponse start(SlowqueryInstanceIdRequest request) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + request.getInstanceId() + startBase)
                .queryParams(request)
                .post(SlowqueryStartOrStopResponse.class);
    }

    public SlowqueryStartOrStopResponse stop(SlowqueryInstanceIdRequest request) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + request.getInstanceId() + stopBase)
                .queryParams(request)
                .post(SlowqueryStartOrStopResponse.class);
    }


    public SlowqueryCreateTaskResponse createTask(SlowqueryCreateTaskRequest request) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + request.getInstanceId() + taskBase)
                .queryParams(request)
                .post(SlowqueryCreateTaskResponse.class);
    }

    public SlowqueryTaskListResponse taskList(SlowqueryTaskListRequest request) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + request.getInstanceId() + taskListBase)
                .queryParams(request)
                .get(SlowqueryTaskListResponse.class);
    }

    public SlowqueryTaskDetailResponse taskDetail(String instanceId, String taskId) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + instanceId + taskDetailBase + "/" + taskId)
                .get(SlowqueryTaskDetailResponse.class);
    }

    public SlowquerySqlOptimizerResponse sqlOptimizer(SlowquerySqlOptimizerRequest request) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + request.getInstanceId() + sqlOptimizerBase)
                .queryParams(request)
                .get(SlowquerySqlOptimizerResponse.class);
    }

    public SlowquerySqlExplainResponse sqlExplain(SlowquerySqlOptimizerRequest request) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + request.getInstanceId() + sqlExplainBase)
                .queryParams(request)
                .get(SlowquerySqlExplainResponse.class);
    }


    public SlowqueryBaseResponse sqlOptimizerFeedback(SlowquerySqlOptimizerFeedbackRequest request) {
        return createSlowqueryRequest()
                .path(this.baseURL + "/" + request.getInstanceId() + sqlOptimizerFeedbackBase)
                .queryParams(request)
                .post(SlowqueryBaseResponse.class);
    }
}
