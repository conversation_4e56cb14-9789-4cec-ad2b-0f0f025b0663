package com.baidu.bce.internalsdk.rds.model.config;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;

/**
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 16/7/29.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardConfigListRequest {
    @NotNull
    @IdPermission
    private String instanceId;
    private String keyword;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
}
