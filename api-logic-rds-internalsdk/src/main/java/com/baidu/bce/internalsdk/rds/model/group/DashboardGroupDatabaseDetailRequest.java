package com.baidu.bce.internalsdk.rds.model.group;


import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DashboardGroupDatabaseDetailRequest {
    @NotNull
    private String groupId;

    @NotNull
    private String dbName;

    @Override
    public String toString() {
        return "DashboardAccountDetailRequest{" +
                "groupId='" + getGroupId() + '\'' +
                ", dbName='" + getDbName() + '\'' +
                '}';
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }


    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }
}
