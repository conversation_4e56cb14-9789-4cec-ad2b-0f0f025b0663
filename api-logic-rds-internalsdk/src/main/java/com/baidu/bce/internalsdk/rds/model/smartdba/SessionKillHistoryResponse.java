package com.baidu.bce.internalsdk.rds.model.smartdba;

import java.util.List;

public class SessionKillHistoryResponse {
    private List<Item> items;
    private Long totalCount;

    public List<Item> getItems() {
        return items;
    }

    public void setItems(List<Item> items) {
        this.items = items;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public static class Item {
        private String appId;
        private String nodeId;
        private String operateDbUser;
        private String operateTime;
        private String operateUser;
        private String product;
        private String sessionCommand;
        private String sessionDb;
        private Long sessionExecuteTime;
        private String sessionHost;
        private Long sessionId;
        private String sessionSql;
        private String sessionState;
        private String sessionUser;
        private Long status;
        private String statusDesc;
        private String statusInfo;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getNodeId() {
            return nodeId;
        }

        public void setNodeId(String nodeId) {
            this.nodeId = nodeId;
        }

        public String getOperateDbUser() {
            return operateDbUser;
        }

        public void setOperateDbUser(String operateDbUser) {
            this.operateDbUser = operateDbUser;
        }

        public String getOperateTime() {
            return operateTime;
        }

        public void setOperateTime(String operateTime) {
            this.operateTime = operateTime;
        }

        public String getOperateUser() {
            return operateUser;
        }

        public void setOperateUser(String operateUser) {
            this.operateUser = operateUser;
        }

        public String getProduct() {
            return product;
        }

        public void setProduct(String product) {
            this.product = product;
        }

        public String getSessionCommand() {
            return sessionCommand;
        }

        public void setSessionCommand(String sessionCommand) {
            this.sessionCommand = sessionCommand;
        }

        public String getSessionDb() {
            return sessionDb;
        }

        public void setSessionDb(String sessionDb) {
            this.sessionDb = sessionDb;
        }

        public Long getSessionExecuteTime() {
            return sessionExecuteTime;
        }

        public void setSessionExecuteTime(Long sessionExecuteTime) {
            this.sessionExecuteTime = sessionExecuteTime;
        }

        public String getSessionHost() {
            return sessionHost;
        }

        public void setSessionHost(String sessionHost) {
            this.sessionHost = sessionHost;
        }

        public Long getSessionId() {
            return sessionId;
        }

        public void setSessionId(Long sessionId) {
            this.sessionId = sessionId;
        }

        public String getSessionSql() {
            return sessionSql;
        }

        public void setSessionSql(String sessionSql) {
            this.sessionSql = sessionSql;
        }

        public String getSessionState() {
            return sessionState;
        }

        public void setSessionState(String sessionState) {
            this.sessionState = sessionState;
        }

        public String getSessionUser() {
            return sessionUser;
        }

        public void setSessionUser(String sessionUser) {
            this.sessionUser = sessionUser;
        }

        public Long getStatus() {
            return status;
        }

        public void setStatus(Long status) {
            this.status = status;
        }

        public String getStatusDesc() {
            return statusDesc;
        }

        public void setStatusDesc(String statusDesc) {
            this.statusDesc = statusDesc;
        }

        public String getStatusInfo() {
            return statusInfo;
        }

        public void setStatusInfo(String statusInfo) {
            this.statusInfo = statusInfo;
        }
    }
}
