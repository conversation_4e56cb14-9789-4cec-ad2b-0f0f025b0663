package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
public class InstanceUpdateAddressRequest {
    private Instance.Endpoint endpoint;

    // OpenAPI 专属参数
    private String address;

    @Override
    public String toString() {
        return "InstanceUpdateAddressRequest{" +
                "endpoint=" + endpoint +
                '}';
    }

    public InstanceUpdateAddressRequest endpoint(final Instance.Endpoint endpoint) {
        this.endpoint = endpoint;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Instance.Endpoint getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(Instance.Endpoint endpoint) {
        this.endpoint = endpoint;
    }
}
