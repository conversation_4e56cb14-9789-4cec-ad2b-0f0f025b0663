package com.baidu.bce.internalsdk.rds.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckStatusResponse {
    private String clusterInstanceStatus;
    private String clusterTopology;
    private String clusterMySQLIsAlived;
    private String clusterDelay;
    private String leaderReadOnly;


    public String getClusterInstanceStatus() {
        return clusterInstanceStatus;
    }

    public void setClusterInstanceStatus(String clusterInstanceStatus) {
        this.clusterInstanceStatus = clusterInstanceStatus;
    }

    public String getClusterTopology() {
        return clusterTopology;
    }

    public void setClusterTopology(String clusterTopology) {
        this.clusterTopology = clusterTopology;
    }

    public String getClusterMySQLIsAlived() {
        return clusterMySQLIsAlived;
    }

    public void setClusterMySQLIsAlived(String clusterMySQLIsAlived) {
        this.clusterMySQLIsAlived = clusterMySQLIsAlived;
    }

    public String getClusterDelay() {
        return clusterDelay;
    }

    public void setClusterDelay(String clusterDelay) {
        this.clusterDelay = clusterDelay;
    }

    public String getLeaderReadOnly() {
        return leaderReadOnly;
    }

    public void setLeaderReadOnly(String leaderReadOnly) {
        this.leaderReadOnly = leaderReadOnly;
    }
}
