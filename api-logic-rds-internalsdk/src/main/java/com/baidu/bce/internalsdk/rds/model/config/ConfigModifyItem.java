package com.baidu.bce.internalsdk.rds.model.config;

import java.util.ArrayList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/19.
 */
public class ConfigModifyItem {
    private String name;
    private String value;
    private String applyMethod;
    private String etag;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ConfigModifyItem{");
        sb.append("name='").append(name).append('\'');
        sb.append(", value='").append(value).append('\'');
        sb.append(", applyMethod='").append(applyMethod).append('\'');
        sb.append('}');
        return sb.toString();
    }

    public String getEtag() {
        return etag;
    }

    public void setEtag(String etag) {
        this.etag = etag;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getApplyMethod() {
        return applyMethod;
    }

    public void setApplyMethod(String applyMethod) {
        this.applyMethod = applyMethod;
    }

    public static class ConfigModifyItemList extends ArrayList<ConfigModifyItem> {
    }
}
