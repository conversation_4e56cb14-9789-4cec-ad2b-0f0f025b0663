package com.baidu.bce.internalsdk.rds.model.paratemplate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ParaTemplateDetail {

    private String key;

    private String value;

    private String range;

    private Integer dynamic; // 是否需要重启 1（不需要）0（需要）

    private String desc;

    private String attention; // 注意事项

    private String type;

    private String bestValue;

    private Integer precision;

    private Boolean required;

    public ParaTemplateDetail() {
    }

    public ParaTemplateDetail(String key, String value, String range, Integer dynamic, String desc, String attention) {
        this.key = key;
        this.value = value;
        this.range = range;
        this.dynamic = dynamic;
        this.desc = desc;
        this.attention = attention;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public Integer getDynamic() {
        return dynamic;
    }

    public void setDynamic(Integer dynamic) {
        this.dynamic = dynamic;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getAttention() {
        return attention;
    }

    public void setAttention(String attention) {
        this.attention = attention;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBestValue() {
        return bestValue;
    }

    public void setBestValue(String bestValue) {
        this.bestValue = bestValue;
    }

    public Integer getPrecision() {
        return precision;
    }

    public void setPrecision(Integer precision) {
        this.precision = precision;
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }

    @Override
    public String toString() {
        return "ParaTemplateDetail{" +
                "key='" + key + '\'' +
                ", value='" + value + '\'' +
                ", range='" + range + '\'' +
                ", dynamic=" + dynamic +
                ", desc='" + desc + '\'' +
                ", attention='" + attention + '\'' +
                '}';
    }
}
