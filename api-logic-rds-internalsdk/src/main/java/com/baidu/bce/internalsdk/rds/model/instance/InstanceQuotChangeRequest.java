package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

/**
 * Created by luping03 on 17/11/6.
 */
public class InstanceQuotChangeRequest {

    @IdPermission
    private String instanceId;

    private String repairStartTime;

    private String repairEndTime;

    private String type;

    public String getRepairStartTime() {
        return repairStartTime;
    }

    public void setRepairStartTime(String repairStartTime) {
        this.repairStartTime = repairStartTime;
    }

    public String getRepairEndTime() {
        return repairEndTime;
    }

    public void setRepairEndTime(String repairEndTime) {
        this.repairEndTime = repairEndTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }
}
