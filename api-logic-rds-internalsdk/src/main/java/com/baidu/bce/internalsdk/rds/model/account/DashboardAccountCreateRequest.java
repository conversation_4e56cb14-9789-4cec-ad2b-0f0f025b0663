package com.baidu.bce.internalsdk.rds.model.account;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardAccountCreateRequest {
    @NotNull
    @IdPermission
    private String instanceId;

    private Account account;

    @Override
    public String toString() {
        return "DashboardAccountCreateRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", account=" + account +
                '}';
    }

    public DashboardAccountCreateRequest withInstanceId(final String instanceId) {
        this.instanceId = instanceId;
        return this;
    }

    public DashboardAccountCreateRequest withAccount(final Account account) {
        this.account = account;
        return this;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Account getAccount() {
        return account;
    }

    public void setAccount(Account account) {
        this.account = account;
    }
}
