package com.baidu.bce.internalsdk.rds.model.database;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatabaseUpdateRemarkRequest {
    private String remark;

    private String dbName;

    @Override
    public String toString() {
        return "DatabaseUpdateRemarkRequest{" +
                "remark='" + remark + '\'' +
                '}';
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }
}
