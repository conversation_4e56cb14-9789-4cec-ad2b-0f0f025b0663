package com.baidu.bce.internalsdk.rds.model.slowquery;

import java.util.List;

/**
 * Created by luping03 on 17/12/25.
 */
public class SlowqueryChartResponse extends SlowqueryBaseResponse {

    private ChartResult result;

    public ChartResult getResult() {
        return result;
    }

    public void setResult(ChartResult result) {
        this.result = result;
    }

    private static class ChartResultSeries {
        private String name;

        private List<Integer> data;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<Integer> getData() {
            return data;
        }

        public void setData(List<Integer> data) {
            this.data = data;
        }
    }

    private static class ChartResult {
        private List<String> category;

        private List<ChartResultSeries> series;

        public List<String> getCategory() {
            return category;
        }

        public void setCategory(List<String> category) {
            this.category = category;
        }

        public List<ChartResultSeries> getSeries() {
            return series;
        }

        public void setSeries(List<ChartResultSeries> series) {
            this.series = series;
        }
    }
}
