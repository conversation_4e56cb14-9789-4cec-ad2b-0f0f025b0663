package com.baidu.bce.internalsdk.rds.model.rollback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RollbackListResponse {


    private String dataType;
    private String groupId = "baidu";
    private Boolean checkResult;
    private List<BinlogFlashServices> binlogFlashServices;
    private Pagination pagination;
    private int totalCount;

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public String getGroupId() {
        return groupId;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }


    public Boolean getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(Boolean checkResult) {
        this.checkResult = checkResult;
    }

    public List<BinlogFlashServices> getBinlogFlashServices() {
        return binlogFlashServices;
    }

    public void setBinlogFlashServices(List<BinlogFlashServices> binlogFlashServices) {
        this.binlogFlashServices = binlogFlashServices;
    }

    public Pagination getPagination() {
        return pagination;
    }

    public void setPagination(Pagination pagination) {
        this.pagination = pagination;
    }
}
