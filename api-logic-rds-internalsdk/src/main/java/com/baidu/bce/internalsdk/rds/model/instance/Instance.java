package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.externalsdk.logical.network.vpc.model.SimpleVpcVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.internalsdk.rds.model.rogroup.AppList;
import com.baidu.bce.internalsdk.rds.model.rogroup.InstanceDict;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/4.
 */

public class Instance {
    private String instanceId;
    @IdPermission
    private String instanceShortId;
    private String instanceName;

    private String engine;
    private String engineVersion;
    private String rdsMinorVersion;
    private String characterSetName;
    private Endpoint endpoint = new Endpoint();

    private String instanceClass;

    @Min(1)
    private int allocatedMemoryInMB;
    private double allocatedMemoryInGB;

    // OpenAPI 专用参数
    private Double memoryCapacity;

    @Min(1)
    private int allocatedStorageInGB;

    // OpenAPI 专用参数
    private Integer volumeCapacity;

    private int totalStorageInGB;
    private int usedStorageInMB;
    private double usedStorageInGB;

    // OpenAPI 专用参数
    private Double usedStorage;

    private int cpuCount;

    private String instanceType;
    private String sourceInstanceId;
    private Integer readReplicaNum;
    private List<String> readReplica;
    private String instanceStatus;
    private String lockMode;
    private String eipStatus;

    // OpenAPI 专用参数
    private String publicAccessStatus;

    private String superUserFlag;
    private String replicationType;

    // OpenAPI 专用参数
    private String syncMode;

    @Valid
    private SnapshotPolicy backupPolicy = new SnapshotPolicy();

    private BlbService blbService = new BlbService();

    private boolean publiclyAccessible;

    public Date instanceCreateTime;
    private Date instanceExpireTime;
    private String productType;

    // OpenAPI 专用参数
    private String paymentTiming;

    private Topology topology = new Topology();

    private String azone;

    private String vpcId;

    private Map<String, String> subnetId = new HashMap<>();

    private Map<String, String> physicalZone = new HashMap<>();

    private String region;

    private String sourceRegion;

//    private int instanceAmount;

    private String billingStatus;

    private String applicationType; // 三节点增强版RDS：enhanced

    private Integer nodeAmount;

    private int oldInstance = 0; // 是否是历史代理实例  1：是－nodeAmount不展示

    private int onlineStatus = 0; // 0是下线，1是在线

    private InstanceCreateRequest.DccHostInfo dccHostIds;

    private String machineType;

    private Boolean isSingle;

    private Integer weight;

    private String nodeType;

    private String repairStartTime;

    private String repairEndTime;

    private String quotStatus;

    private RoleInfo nodeReadReplica;

    private RoleInfo nodeMaster;

    private RoleInfo nodeSlave;

    private RoleInfoProxy nodeProxy;

    private String diskIoType;

    private String groupId;

    private String groupName;

    private String tdeStatus;

    private String userId;

    /* 专属blbId */
    private String bgwGroupId;

    /* 是否使用专属集群 */
    private boolean bgwGroupExclusive;

    private List<RoGroup> roGroupList;

    private boolean roGroupAbnormal;

    private String diskType;

    private String cdsType;

    private String maintainStartTime;

    private Integer maintainDuration;

    private String maintainPeriod;

    private String instanceSubStatus;

    private String bcmSendCycle;

    private Integer autoResizeDisk;

    private int autoResizeCpu;

    private String edgeRegion;

    // Console 层为前端设置该字段，后端仅返回 edgeRegion
    private String edgeRegionName;

    // 后端返回的是 vpcUuid，Console 层转换为 vpcId
    private String edgeVpcId;

    // 后端返回的是 subnetUuid，Console 层转换为 subnetId
    private String edgeSubnetId;

    private Integer gtidType;

    private Integer haStrategy;

    private boolean isDBRSEnable;

    private String replicaType;

    private RoleInfo nodeMasterReadReplica;

    private RoleInfo nodeBackupReadReplica;

    private String resourceType;

    private int cdsEncrypt;

    private String cdsEncryptKey;

    private String resourcePlatform;


    public boolean getIsDBRSEnable() {
        return isDBRSEnable;
    }

    public void setIsDBRSEnable(boolean isDbrsEnable) {
        isDBRSEnable = isDbrsEnable;
    }

    public int getCdsEncrypt() {
        return cdsEncrypt;
    }

    public void setCdsEncrypt(int cdsEncrypt) {
        this.cdsEncrypt = cdsEncrypt;
    }

    public String getCdsEncryptKey() {
        return cdsEncryptKey;
    }

    public void setCdsEncryptKey(String cdsEncryptKey) {
        this.cdsEncryptKey = cdsEncryptKey;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getResourcePlatform() {
        return resourcePlatform;
    }

    public void setResourcePlatform(String resourcePlatform) {
        this.resourcePlatform = resourcePlatform;
    }

    public String getReplicaType() {
        return replicaType;
    }

    public void setReplicaType(String replicaType) {
        this.replicaType = replicaType;
    }

    public RoleInfo getNodeMasterReadReplica() {
        return nodeMasterReadReplica;
    }

    public void setNodeMasterReadReplica(RoleInfo nodeMasterReadReplica) {
        this.nodeMasterReadReplica = nodeMasterReadReplica;
    }

    public RoleInfo getNodeBackupReadReplica() {
        return nodeBackupReadReplica;
    }

    public void setNodeBackupReadReplica(RoleInfo nodeBackupReadReplica) {
        this.nodeBackupReadReplica = nodeBackupReadReplica;
    }

    public Integer getGtidType() {
        return gtidType;
    }

    public void setGtidType(Integer gtidType) {
        this.gtidType = gtidType;
    }

    public Integer getHaStrategy() {
        return haStrategy;
    }

    public void setHaStrategy(Integer haStrategy) {
        this.haStrategy = haStrategy;
    }

    public Instance() {

    }

    public Instance(Instance instance) {
        this.withInstanceId(instance.getInstanceId())
                .withInstanceShortId(instance.getInstanceShortId())
                .withInstanceName(instance.getInstanceName())
                .withEngine(instance.getEngine())
                .withEngineVersion(instance.getEngineVersion())
                .withCharacterSetName(instance.getCharacterSetName())
                .withEndpoint(instance.getEndpoint())
                .withInstanceClass(instance.getInstanceClass())
                .withAllocatedMemoryInMB(instance.getAllocatedMemoryInMB())
                .withAllocatedMemoryInGB(instance.getAllocatedMemoryInGB())
                .withAllocatedStorageInGB(instance.getAllocatedStorageInGB())
                .withUsedStorageInMB(instance.getUsedStorageInMB())
                .withUsedStorageInGB(instance.getUsedStorageInGB())
                .withCpuCount(instance.getCpuCount())
                .withInstanceType(instance.getInstanceType())
                .withSourceInstanceId(instance.getSourceInstanceId())
                .withReadReplicaNum(instance.getReadReplicaNum())
                .withInstanceStatus(instance.getInstanceStatus())
                .withLockMode(instance.getLockMode())
                .withEipStatus(instance.getEipStatus())
                .withBackupPolicy(instance.getBackupPolicy())
                .withBlbService(instance.getBlbService())
                .withPubliclyAccessible(instance.isPubliclyAccessible())
                .withInstanceCreateTime(instance.getInstanceCreateTime())
                .withInstanceExpireTime(instance.getInstanceExpireTime())
                .withTopoly(instance.getTopology())
                .withProductType(instance.getProductType())
                .withAzone(instance.getAzone())
                .withSuperUserFlag(instance.getSuperUserFlag())
                .withVpcId(instance.getVpcId())
                .withSubnetId(instance.getSubnetId())
                .withRegion(instance.getRegion())
                .withSourceRegion(instance.getSourceRegion())
                .withBillingStatus(instance.getBillingStatus())
                .withReplicationType(instance.getReplicationType())
                .withNodeAmount(instance.getNodeAmount())
                .withApplicationType(instance.getApplicationType())
                .withOldInstance(instance.getOldInstance())
                .withOnlineStatus(instance.getOnlineStatus())
                .withMachineType(instance.getMachineType())
                .withDccHostIds(instance.getDccHostIds())
                .withTotalStorageInGB(instance.getTotalStorageInGB())
                .withIsSingle(instance.getIsSingle())
                .withWeight(instance.getWeight())
                .withNodeType(instance.getNodeType())
                .withRepairStartTime(instance.getRepairStartTime())
                .withRepairEndTime(instance.getRepairEndTime())
                .withQuotStatus(instance.getQuotStatus())
                .withNodeReadReplica(instance.getNodeReadReplica())
                .withNodeMaster(instance.getNodeMaster())
                .withNodeSlave(instance.getNodeSlave())
                .withNodeProxy(instance.getNodeProxy())
                .withDiskIoType(instance.getDiskIoType())
                .withGroupId(instance.getGroupId())
                .withTdeStatus(instance.getTdeStatus())
                .withUserId(instance.getUserId())
                .withGroupName(instance.getGroupName())
                .withBgwGroupExclusive(instance.isBgwGroupExclusive())
                .withBgwGroupId(instance.bgwGroupId)
                .withDiskType(instance.getDiskType())
                .withCdsType(instance.getCdsType())
                .withRoGroupList(instance.getRoGroupList())
                .withRoGroupAbnormal(instance.isRoGroupAbnormal())
                .withMaintainStartTime(instance.getMaintainStartTime())
                .withMaintainDuration(instance.getMaintainDuration())
                .withMaintainPeriod(instance.getMaintainPeriod())
                .withInstanceSubStatus(instance.getInstanceSubStatus())
                .withBcmSendCycle(instance.getBcmSendCycle())
                .withAutoResizeDisk(instance.getAutoResizeDisk())
                .withAutoResizeCpu(instance.getAutoResizeCpu())
                .withRdsMinorVersion(instance.getRdsMinorVersion())
                .withHaStrategy(instance.getHaStrategy())
                .withIsDBRSEnable(instance.getIsDBRSEnable())
                .withReplicaType(instance.getReplicaType())
                .withResourceType(instance.getResourceType())
                .withResourcePlatform(instance.getResourcePlatform())
                .withGtidType(instance.getGtidType())
                .withNodeMasterReadReplica(instance.getNodeMasterReadReplica())
                .withNodeBackupReadReplica(instance.getNodeBackupReadReplica())
                .withCdsEncrypt(instance.getCdsEncrypt())
                .withCdsEncryptKey(instance.getCdsEncryptKey())
                .withPhysicalZone(instance.getPhysicalZone())
        ;
        this.setEdgeRegion(instance.getEdgeRegion());
        this.setEdgeVpcId(instance.getEdgeVpcId());
        this.setEdgeSubnetId(instance.getEdgeSubnetId());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Instance instance = (Instance) o;

        if (instanceId != null ? !instanceId.equals(instance.instanceId) : instance.instanceId != null) return false;
        if (instanceName != null ? !instanceName.equals(instance.instanceName) : instance.instanceName != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = instanceId != null ? instanceId.hashCode() : 0;
        result = 31 * result + (instanceName != null ? instanceName.hashCode() : 0);
        return result;
    }

    public Integer getAutoResizeDisk() {
        return autoResizeDisk;
    }

    public void setAutoResizeDisk(Integer autoResizeDisk) {
        this.autoResizeDisk = autoResizeDisk;
    }

    public int getAutoResizeCpu() {
        return autoResizeCpu;
    }

    public void setAutoResizeCpu(int autoResizeCpu) {
        this.autoResizeCpu = autoResizeCpu;
    }

    public String getEdgeRegion() {
        return edgeRegion;
    }

    public void setEdgeRegion(String edgeRegion) {
        this.edgeRegion = edgeRegion;
    }

    public String getEdgeRegionName() {
        return edgeRegionName;
    }

    public void setEdgeRegionName(String edgeRegionName) {
        this.edgeRegionName = edgeRegionName;
    }

    public String getEdgeVpcId() {
        return edgeVpcId;
    }

    public void setEdgeVpcId(String edgeVpcId) {
        this.edgeVpcId = edgeVpcId;
    }

    public String getEdgeSubnetId() {
        return edgeSubnetId;
    }

    public void setEdgeSubnetId(String edgeSubnetId) {
        this.edgeSubnetId = edgeSubnetId;
    }

    public Map<String, String> getPhysicalZone() {
        return physicalZone;
    }

    public void setPhysicalZone(Map<String, String> physicalZone) {
        this.physicalZone = physicalZone;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Instance withUserId(String userId) {
        this.userId = userId;
        return this;
    }

    public Instance withPhysicalZone(Map<String, String> physicalZone) {
        this.physicalZone = physicalZone;
        return this;
    }

    public String getBgwGroupId() {
        return bgwGroupId;
    }

    public void setBgwGroupId(String bgwGroupId) {
        this.bgwGroupId = bgwGroupId;
    }

    public Instance withBgwGroupId(String bgwGroupId) {
        this.bgwGroupId = bgwGroupId;
        return this;
    }

    public boolean isBgwGroupExclusive() {
        return bgwGroupExclusive;
    }

    public void setBgwGroupExclusive(boolean bgwGroupExclusive) {
        this.bgwGroupExclusive = bgwGroupExclusive;
    }

    public Instance withBgwGroupExclusive(boolean bgwGroupExclusive) {
        this.bgwGroupExclusive = bgwGroupExclusive;
        return this;
    }

    public int getTotalStorageInGB() {
        return totalStorageInGB;
    }

    public void setTotalStorageInGB(int totalStorageInGB) {
        this.totalStorageInGB = totalStorageInGB;
    }

    public Instance withTotalStorageInGB(int totalStorageInGB) {
        this.totalStorageInGB = totalStorageInGB;
        return this;
    }

    public void setDccHostIds(InstanceCreateRequest.DccHostInfo dccHostIds) {
        this.dccHostIds = dccHostIds;
    }

    public void setMachineType(String machineType) {
        this.machineType = machineType;
    }

    public InstanceCreateRequest.DccHostInfo getDccHostIds() {
        return dccHostIds;
    }

    public Instance withDccHostIds(InstanceCreateRequest.DccHostInfo dccHostIds) {
        this.dccHostIds = dccHostIds;
        return this;
    }


    private Instance withHaStrategy(final Integer haStrategy) {
        this.haStrategy = haStrategy;
        return this;
    }

    private Instance withIsDBRSEnable(final boolean isDBRSEnable) {
        this.isDBRSEnable = isDBRSEnable;
        return this;
    }

    private Instance withReplicaType(final String replicaType) {
        this.replicaType = replicaType;
        return this;
    }

    private Instance withGtidType(final Integer gtidType) {
        this.gtidType = gtidType;
        return this;
    }

    private Instance withResourceType(final String resourceType) {
        this.resourceType = resourceType;
        return this;
    }

    private Instance withResourcePlatform(final String resourcePlatform) {
        this.resourcePlatform = resourcePlatform;
        return this;
    }

    private Instance withCdsEncryptKey(final String cdsEncryptKey) {
        this.cdsEncryptKey = cdsEncryptKey;
        return this;
    }


    private Instance withCdsEncrypt(final int cdsEncrypt) {
        this.cdsEncrypt = cdsEncrypt;
        return this;
    }

    private Instance withNodeMasterReadReplica(final RoleInfo nodeMasterReadReplica) {
        this.nodeMasterReadReplica = nodeMasterReadReplica;
        return this;
    }

    private Instance withNodeBackupReadReplica(final RoleInfo nodeBackupReadReplica) {
        this.nodeBackupReadReplica = nodeBackupReadReplica;
        return this;
    }

    public String getMachineType() {
        return machineType;
    }

    public Instance withMachineType(String machineType) {
        this.machineType = machineType;
        return this;
    }

    public void setAllocatedMemoryInGB(double allocatedMemoryInGB) {
        this.allocatedMemoryInGB = allocatedMemoryInGB;
    }

    public void setUsedStorageInGB(double usedStorageInGB) {
        this.usedStorageInGB = usedStorageInGB;
    }

    public int getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(int onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public Instance withOnlineStatus(int onlineStatus) {
        this.onlineStatus = onlineStatus;
        return this;
    }

    public double getAllocatedMemoryInGB() {
        return allocatedMemoryInGB;
    }

    public Double getMemoryCapacity() {
        return memoryCapacity;
    }

    public void setMemoryCapacity(Double memoryCapacity) {
        this.memoryCapacity = memoryCapacity;
    }

    public Instance withAllocatedMemoryInGB(double allocatedMemoryInGB) {
        this.allocatedMemoryInGB = allocatedMemoryInGB;
        return this;
    }

    public double getUsedStorageInGB() {
        return usedStorageInGB;
    }

    public Double getUsedStorage() {
        return usedStorage;
    }

    public void setUsedStorage(Double usedStorage) {
        this.usedStorage = usedStorage;
    }

    public String getInstanceShortId() {
        return instanceShortId;
    }

    public void setInstanceShortId(String instanceShortId) {
        this.instanceShortId = instanceShortId;
    }

    public Instance withInstanceShortId(String instanceShortId) {
        this.instanceShortId = instanceShortId;
        return this;
    }

    public Integer getNodeAmount() {
        return nodeAmount;
    }

    public void setNodeAmount(Integer nodeAmount) {
        this.nodeAmount = nodeAmount;
    }

    public Instance withNodeAmount(Integer nodeAmount) {
        this.nodeAmount = nodeAmount;
        return this;
    }

    public Instance withInstanceType(final String instanceType) {
        this.instanceType = instanceType;
        return this;
    }

    public int getOldInstance() {
        return oldInstance;
    }

    public void setOldInstance(int oldInstance) {
        this.oldInstance = oldInstance;
    }

    public Instance withOldInstance(int oldInstance) {
        this.oldInstance = oldInstance;
        return this;
    }

    public Instance withInstanceId(final String instanceId) {
        this.instanceId = instanceId;
        return this;
    }

    public Instance withInstanceName(final String instanceName) {
        this.instanceName = instanceName;
        return this;
    }

    public Instance withEngine(final String engine) {
        this.engine = engine;
        return this;
    }

    public Instance withEngineVersion(final String engineVersion) {
        this.engineVersion = engineVersion;
        return this;
    }

    public Instance withRdsMinorVersion(final String rdsMinorVersion) {
        this.rdsMinorVersion = rdsMinorVersion;
        return this;
    }

    public Instance withCharacterSetName(final String characterSetName) {
        this.characterSetName = characterSetName;
        return this;
    }

    public Instance withEndpoint(final Endpoint endpoint) {
        this.endpoint = endpoint;
        return this;
    }

    public Instance withInstanceClass(final String instanceClass) {
        this.instanceClass = instanceClass;
        return this;
    }

    public Instance withAllocatedMemoryInMB(final int allocatedMemoryInMB) {
        this.allocatedMemoryInMB = allocatedMemoryInMB;
        return this;
    }

    public Instance withAllocatedStorageInGB(final int allocatedStorageInGB) {
        this.allocatedStorageInGB = allocatedStorageInGB;
        return this;
    }

    public Instance withUsedStorageInMB(final int usedStorageInMB) {
        this.usedStorageInMB = usedStorageInMB;
        return this;
    }

    public Instance withUsedStorageInGB(final double usedStorageInGB) {
        this.usedStorageInGB = usedStorageInGB;
        return this;
    }

    public Instance withCpuCount(final int cpuCount) {
        this.cpuCount = cpuCount;
        return this;
    }

    public Instance withInstanceStatus(final String instanceStatus) {
        this.instanceStatus = instanceStatus;
        return this;
    }

    public Instance withLockMode(final String lockMode) {
        this.lockMode = lockMode;
        return this;
    }

    public Instance withEipStatus(final String eipStatus) {
        this.eipStatus = eipStatus;
        return this;
    }

    public Instance withBackupPolicy(final SnapshotPolicy backupPolicy) {
        this.backupPolicy = backupPolicy;
        return this;
    }

    public Instance withBlbService(final BlbService blbService) {
        this.blbService = blbService;
        return this;
    }

    public Instance withGroupName(String groupName) {
        this.groupName = groupName;
        return this;
    }

    public Instance withSuperUserFlag(final String superUserFlag) {
        this.superUserFlag = superUserFlag;
        return this;
    }

    public Instance withReplicationType(final String replicationType) {
        this.replicationType = replicationType;
        return this;
    }

//    public Instance withSuperUserFlag(final String superUserFlag) {
//        this.superUserFlag = superUserFlag;
//        return this;
//    }

    public Instance withPubliclyAccessible(final boolean publiclyAccessible) {
        this.publiclyAccessible = publiclyAccessible;
        return this;
    }

    public Instance withSourceInstanceId(final String sourceInstanceId) {
        this.sourceInstanceId = sourceInstanceId;
        return this;
    }

    public Instance withReadReplicaNum(final Integer readReplicaNum) {
        this.readReplicaNum = readReplicaNum;
        return this;
    }

    public Instance withInstanceCreateTime(final Date instanceCreateTime) {
        this.instanceCreateTime = instanceCreateTime;
        return this;
    }

    public Instance withInstanceExpireTime(final Date instanceExpireTime) {
        this.instanceExpireTime = instanceExpireTime;
        return this;
    }

    public Instance withProductType(String productType) {
        setProductType(productType);
        return this;
    }

    public Instance withTopoly(final Topology topoly) {
        this.setTopology(topoly);
        return this;
    }

    public Instance withAzone(final String azone) {
        this.setAzone(azone);
        return this;
    }

    public Instance withVpcId(final String vpcId) {
        this.setVpcId(vpcId);
        return this;
    }

    public Instance withSubnetId(final Map<String, String> subnetId) {
        this.setSubnetId(subnetId);
        return this;
    }

    public Instance withRegion(final String region) {
        this.setRegion(region);
        return this;
    }

    public Instance withSourceRegion(final String sourceRegion) {
        this.setSourceRegion(sourceRegion);
        return this;
    }

    public Instance withBillingStatus(final String billingStatus) {
        this.setBillingStatus(billingStatus);
        return this;
    }

    public Instance withApplicationType(final String applicationType) {
        this.applicationType = applicationType;
        return this;
    }

    public Instance withIsSingle(final Boolean isSingle) {
        this.isSingle = isSingle;
        return this;
    }

    public Instance withWeight(final Integer weight) {
        this.weight = weight;
        return this;
    }

    public Instance withNodeType(final String nodeType) {
        this.nodeType = nodeType;
        return this;
    }

    public Instance withRepairStartTime(final String repairStartTime) {
        this.repairStartTime = repairStartTime;
        return this;
    }

    public Instance withRepairEndTime(final String repairEndTime) {
        this.repairEndTime = repairEndTime;
        return this;
    }

    public Instance withQuotStatus(final String quotStatus) {
        this.quotStatus = quotStatus;
        return this;
    }

    public Instance withNodeReadReplica(final RoleInfo nodeReadReplica) {
        this.nodeReadReplica = nodeReadReplica;
        return this;
    }

    public Instance withNodeMaster(final RoleInfo nodeMaster) {
        this.setNodeMaster(nodeMaster);
        return this;
    }

    public Instance withDiskIoType(final String diskIoType) {
        this.setDiskIoType(diskIoType);
        return this;
    }

    public Instance withNodeSlave(final RoleInfo nodeSlave) {
        this.nodeSlave = nodeSlave;
        return this;
    }

    public Instance withNodeProxy(final RoleInfoProxy nodeProxy) {
        this.nodeProxy = nodeProxy;
        return this;
    }


    public Instance withGroupId(final String groupId) {
        this.groupId = groupId;
        return this;
    }

    public Instance withTdeStatus(final String tdeStatus) {
        this.setTdeStatus(tdeStatus);
        return this;
    }

    public Instance withRoGroupList(final List<RoGroup> roGroupList) {
        this.setRoGroupList(roGroupList);
        return this;
    }

    public Instance withRoGroupAbnormal(final Boolean roGroupAbnormal) {
        this.roGroupAbnormal = roGroupAbnormal;
        return this;
    }

    public Instance withDiskType(final String diskType) {
        this.diskType = diskType;
        return this;
    }

    public Instance withCdsType(final String cdsType) {
        this.cdsType = cdsType;
        return this;
    }

    public Instance withMaintainStartTime(final String maintainStartTime) {
        this.maintainStartTime = maintainStartTime;
        return this;
    }

    public Instance withMaintainPeriod(final String maintainPeriod) {
        this.maintainPeriod = maintainPeriod;
        return this;
    }

    public Instance withInstanceSubStatus(final String instanceSubStatus) {
        this.instanceSubStatus = instanceSubStatus;
        return this;
    }

    public Instance withMaintainDuration(final Integer maintainDuration) {
        this.maintainDuration = maintainDuration;
        return this;
    }

    public Instance withBcmSendCycle(final String bcmSendCycle) {
        this.bcmSendCycle = bcmSendCycle;
        return this;
    }

    public Instance withAutoResizeDisk(final Integer autoResizeDisk) {
        this.autoResizeDisk = autoResizeDisk;
        return this;
    }

    public Instance withAutoResizeCpu(final int autoResizeCpu) {
        this.autoResizeCpu = autoResizeCpu;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Instance{");
        sb.append("instanceId='").append(instanceId).append('\'');
        sb.append(", instanceName='").append(instanceName).append('\'');
        sb.append(", engine='").append(engine).append('\'');
        sb.append(", engineVersion='").append(engineVersion).append('\'');
        sb.append(", characterSetName='").append(characterSetName).append('\'');
        sb.append(", endpoint=").append(endpoint);
        sb.append(", instanceClass='").append(instanceClass).append('\'');
        sb.append(", allocatedMemoryInMB=").append(allocatedMemoryInMB);
        sb.append(", allocatedStorageInGB=").append(allocatedStorageInGB);
        sb.append(", usedStorageInMB=").append(usedStorageInMB);
        sb.append(", instanceType='").append(instanceType).append('\'');
        sb.append(", sourceInstanceId='").append(sourceInstanceId).append('\'');
        sb.append(", readReplicaNum=").append(readReplicaNum);
        sb.append(", instanceStatus='").append(instanceStatus).append('\'');
        sb.append(", lockMode='").append(lockMode).append('\'');
        sb.append(", eipStatus='").append(eipStatus).append('\'');
        sb.append(", backupPolicy=").append(backupPolicy);
        sb.append(", blbService=").append(blbService);
        sb.append(", publiclyAccessible=").append(publiclyAccessible);
        sb.append(", instanceCreateTime=").append(instanceCreateTime);
        sb.append(", instanceExpireTime=").append(instanceExpireTime);
        sb.append(", productType=").append(productType);
        sb.append(", topoly=").append(topology);
        sb.append(", azone=").append(azone);
        sb.append(", vpcId=").append(vpcId);
        sb.append(", subnetId=").append(subnetId);
        sb.append(", superUserFlag=").append(superUserFlag);
        sb.append(", replicationType='").append(replicationType).append('\'');
        sb.append(", cpuCount=").append(cpuCount);
        sb.append(", applicationType=").append(applicationType);
        sb.append(", isSingle=").append(isSingle);
        sb.append('}');
        return sb.toString();
    }

    public String getReplicationType() {
        return replicationType;
    }

    public void setReplicationType(String replicationType) {
        this.replicationType = replicationType;
    }

    public String getSyncMode() {
        return syncMode;
    }

    public void setSyncMode(String syncMode) {
        this.syncMode = syncMode;
    }

    public int getCpuCount() {
        return cpuCount;
    }

    public void setCpuCount(int cpuCount) {
        this.cpuCount = cpuCount;
    }

    public Integer getReadReplicaNum() {
        return readReplicaNum;
    }

    public void setReadReplicaNum(Integer readReplicaNum) {
        this.readReplicaNum = readReplicaNum;
    }

    public String getInstanceType() {
        return instanceType;
    }

    public void setInstanceType(String instanceType) {
        this.instanceType = instanceType;
    }

    public String getEipStatus() {
        return eipStatus;
    }

    public void setEipStatus(String eipStatus) {
        this.eipStatus = eipStatus;
    }

    public String getPublicAccessStatus() {
        return publicAccessStatus;
    }

    public void setPublicAccessStatus(String publicAccessStatus) {
        this.publicAccessStatus = publicAccessStatus;
    }

    public int getUsedStorageInMB() {
        return usedStorageInMB;
    }

    public void setUsedStorageInMB(int usedStorageInMB) {
        this.usedStorageInMB = usedStorageInMB;
        this.usedStorageInGB = usedStorageInMB / 1024.00;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getEngineVersion() {
        return engineVersion;
    }

    public void setEngineVersion(String engineVersion) {
        this.engineVersion = engineVersion;
    }

    public String getRdsMinorVersion() {
        return rdsMinorVersion;
    }

    public void setRdsMinorVersion(String rdsMinorVersion) {
        this.rdsMinorVersion = rdsMinorVersion;
    }

    public String getCharacterSetName() {
        return characterSetName;
    }

    public void setCharacterSetName(String characterSetName) {
        this.characterSetName = characterSetName;
    }

    public Endpoint getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(Endpoint endpoint) {
        this.endpoint = endpoint;
    }

    public String getInstanceClass() {
        return instanceClass;
    }

    public void setInstanceClass(String instanceClass) {
        this.instanceClass = instanceClass;
    }

    public int getAllocatedMemoryInMB() {
        return allocatedMemoryInMB;
    }

    public void setAllocatedMemoryInMB(int allocatedMemoryInMB) {
        this.allocatedMemoryInMB = allocatedMemoryInMB;
        this.allocatedMemoryInGB = allocatedMemoryInMB / 1024.00;
    }

    public int getAllocatedStorageInGB() {
        return allocatedStorageInGB;
    }

    public Integer getVolumeCapacity() {
        return volumeCapacity;
    }

    public void setVolumeCapacity(Integer volumeCapacity) {
        this.volumeCapacity = volumeCapacity;
    }

    public void setAllocatedStorageInGB(int allocatedStorageInGB) {
        this.allocatedStorageInGB = allocatedStorageInGB;
    }

    public String getInstanceStatus() {
        return instanceStatus;
    }

    public void setInstanceStatus(String instanceStatus) {
        this.instanceStatus = instanceStatus;
    }

    public String getLockMode() {
        return lockMode;
    }

    public void setLockMode(String lockMode) {
        this.lockMode = lockMode;
    }

    public String getSuperUserFlag() {
        return superUserFlag;
    }

    public void setSuperUserFlag(String superUserFlag) {
        this.superUserFlag = superUserFlag;
    }

    public SnapshotPolicy getBackupPolicy() {
        return backupPolicy;
    }

    public void setBackupPolicy(SnapshotPolicy backupPolicy) {
        this.backupPolicy = backupPolicy;
    }

    public BlbService getBlbService() {
        return blbService;
    }

    public void setBlbService(BlbService blbService) {
        this.blbService = blbService;
    }

    public boolean isPubliclyAccessible() {
        return publiclyAccessible;
    }

    public void setPubliclyAccessible(boolean publiclyAccessible) {
        this.publiclyAccessible = publiclyAccessible;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getInstanceCreateTime() {
        return instanceCreateTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setInstanceCreateTime(Date instanceCreateTime) {
        this.instanceCreateTime = instanceCreateTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getInstanceExpireTime() {
        return instanceExpireTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setInstanceExpireTime(Date instanceExpireTime) {
        this.instanceExpireTime = instanceExpireTime;
    }

    public String getSourceInstanceId() {
        return sourceInstanceId;
    }

    public void setSourceInstanceId(String sourceInstanceId) {
        this.sourceInstanceId = sourceInstanceId;
    }

    public List<String> getReadReplica() {
        return readReplica;
    }

    public void setReadReplica(List<String> readReplica) {
        this.readReplica = readReplica;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getPaymentTiming() {
        return paymentTiming;
    }

    public void setPaymentTiming(String paymentTiming) {
        this.paymentTiming = paymentTiming;
    }

    public Topology getTopology() {
        return topology;
    }

    public void setTopology(Topology topology) {
        this.topology = topology;
    }

    public String getAzone() {
        return azone;
    }

    public void setAzone(String azone) {
        this.azone = azone;
    }

    public String getVpcId() {
        return vpcId;
    }

    public void setVpcId(String vpcId) {
        this.vpcId = vpcId;
    }

    public Map<String, String> getSubnetId() {
        return subnetId;
    }

    public void setSubnetId(Map<String, String> subnetId) {
        this.subnetId = subnetId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getSourceRegion() {
        return sourceRegion;
    }

    public void setSourceRegion(String sourceRegion) {
        this.sourceRegion = sourceRegion;
    }

    public String getBillingStatus() {
        return billingStatus;
    }

    public void setBillingStatus(String billingStatus) {
        this.billingStatus = billingStatus;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }

    public Boolean getIsSingle() {
        return isSingle;
    }

    public void setIsSingle(Boolean isSingle) {
        this.isSingle = isSingle;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getQuotStatus() {
        return quotStatus;
    }

    public void setQuotStatus(String quotStatus) {
        this.quotStatus = quotStatus;
    }

    public String getRepairStartTime() {
        return repairStartTime;
    }

    public void setRepairStartTime(String repairStartTime) {
        this.repairStartTime = repairStartTime;
    }

    public String getRepairEndTime() {
        return repairEndTime;
    }

    public void setRepairEndTime(String repairEndTime) {
        this.repairEndTime = repairEndTime;
    }

    public RoleInfo getNodeSlave() {
        return nodeSlave;
    }

    public void setNodeSlave(RoleInfo nodeSlave) {
        this.nodeSlave = nodeSlave;
    }

    public RoleInfo getNodeMaster() {
        return nodeMaster;
    }

    public void setNodeMaster(RoleInfo nodeMaster) {
        this.nodeMaster = nodeMaster;
    }

    public RoleInfo getNodeReadReplica() {
        return nodeReadReplica;
    }

    public void setNodeReadReplica(RoleInfo nodeReadReplica) {
        this.nodeReadReplica = nodeReadReplica;
    }

    public String getDiskIoType() {
        return diskIoType;
    }

    public void setDiskIoType(String diskIoType) {
        this.diskIoType = diskIoType;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getTdeStatus() {
        return tdeStatus;
    }

    public void setTdeStatus(String tdeStatus) {
        this.tdeStatus = tdeStatus;
    }

    public List<RoGroup> getRoGroupList() {
        return roGroupList;
    }

    public void setRoGroupList(List<RoGroup> roGroupList) {
        this.roGroupList = roGroupList;
    }

    public Boolean getSingle() {
        return isSingle;
    }

    public void setSingle(Boolean single) {
        isSingle = single;
    }

    public boolean isRoGroupAbnormal() {
        return roGroupAbnormal;
    }

    public void setRoGroupAbnormal(boolean roGroupAbnormal) {
        this.roGroupAbnormal = roGroupAbnormal;
    }

    public String getDiskType() {
        return diskType;
    }

    public void setDiskType(String diskType) {
        this.diskType = diskType;
    }

    public String getCdsType() {
        return cdsType;
    }

    public void setCdsType(String cdsType) {
        this.cdsType = cdsType;
    }

    public String getMaintainStartTime() {
        return maintainStartTime;
    }

    public void setMaintainStartTime(String maintainStartTime) {
        this.maintainStartTime = maintainStartTime;
    }

    public Integer getMaintainDuration() {
        return maintainDuration;
    }

    public void setMaintainDuration(Integer maintainDuration) {
        this.maintainDuration = maintainDuration;
    }

    public String getMaintainPeriod() {
        return maintainPeriod;
    }

    public void setMaintainPeriod(String maintainPeriod) {
        this.maintainPeriod = maintainPeriod;
    }

    public String getInstanceSubStatus() {
        return instanceSubStatus;
    }

    public void setInstanceSubStatus(String instanceSubStatus) {
        this.instanceSubStatus = instanceSubStatus;
    }

    public String getBcmSendCycle() {
        return bcmSendCycle;
    }

    public void setBcmSendCycle(String bcmSendCycle) {
        this.bcmSendCycle = bcmSendCycle;
    }

    public RoleInfoProxy getNodeProxy() {
        return nodeProxy;
    }

    public void setNodeProxy(RoleInfoProxy nodeProxy) {
        this.nodeProxy = nodeProxy;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Endpoint {
        private Integer port;
        private String address;
        private String vnetIp;
        private String inetIp;
        private String inetIpV6;
        private String vnetIpV6;


        public Endpoint() {
        }

        public Endpoint(Integer port, String address, String vnetIp, String inetIp) {
            this.port = port;
            this.address = address;
            this.vnetIp = vnetIp;
            this.inetIp = inetIp;
        }

        @Override
        public String toString() {
            return "Endpoint{"
                    + "port=" + port
                    + ", address='" + address + '\''
                    + ", vnetIp='" + vnetIp + '\''
                    + ", inetIp='" + inetIp + '\''
                    + '}';
        }

        public String getVnetIp() {
            return vnetIp;
        }

        public void setVnetIp(String vnetIp) {
            this.vnetIp = vnetIp;
        }

        public String getInetIp() {
            return inetIp;
        }

        public void setInetIp(String inetIp) {
            this.inetIp = inetIp;
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public Endpoint port(final int port) {
            this.port = port;
            return this;
        }

        public Endpoint address(final String address) {
            this.address = address;
            return this;
        }

        public String getInetIpV6() {
            return inetIpV6;
        }

        public void setInetIpV6(String inetIpV6) {
            this.inetIpV6 = inetIpV6;
        }

        public String getVnetIpV6() {
            return vnetIpV6;
        }

        public void setVnetIpV6(String vnetIpV6) {
            this.vnetIpV6 = vnetIpV6;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Topology {
        List<String> master = new ArrayList<>();
        List<String> readReplica = new ArrayList<>();
        List<String> rdsproxy = new ArrayList<>();
        List<ReadReplicaIdMapping> readReplicaIdMapping = new ArrayList<>();
        private ReadReplicaIdMapping rdsproxyIdMapping;
        private MasterIdMapping masterIdMapping;
        private List<ReadReplicaIdMapping> rdsproxyIdMappings;

        public Topology() {
        }

        public Topology(List<String> master, List<String> readReplica, List<String> rdsproxy) {
            this.master = master;
            this.readReplica = readReplica;
            this.rdsproxy = rdsproxy;
        }

        public List<ReadReplicaIdMapping> getRdsproxyIdMappings() {
            return rdsproxyIdMappings;
        }

        public void setRdsproxyIdMappings(List<ReadReplicaIdMapping> rdsproxyIdMappings) {
            this.rdsproxyIdMappings = rdsproxyIdMappings;
        }


        public ReadReplicaIdMapping getRdsproxyIdMapping() {
            return rdsproxyIdMapping;
        }

        public void setRdsproxyIdMapping(ReadReplicaIdMapping rdsproxyIdMapping) {
            this.rdsproxyIdMapping = rdsproxyIdMapping;
        }

        public List<String> getMaster() {
            return master;
        }

        public void setMaster(List<String> master) {
            this.master = master;
        }

        public List<String> getReadReplica() {
            return readReplica;
        }

        public void setReadReplica(List<String> readReplica) {
            this.readReplica = readReplica;
        }

        public List<String> getRdsproxy() {
            return rdsproxy;
        }

        public void setRdsproxy(List<String> rdsproxy) {
            this.rdsproxy = rdsproxy;
        }

        public List<ReadReplicaIdMapping> getReadReplicaIdMapping() {
            return readReplicaIdMapping;
        }

        public void setReadReplicaIdMapping(List<ReadReplicaIdMapping> readReplicaIdMapping) {
            this.readReplicaIdMapping = readReplicaIdMapping;
        }

        public MasterIdMapping getMasterIdMapping() {
            return masterIdMapping;
        }

        public void setMasterIdMapping(MasterIdMapping masterIdMapping) {
            this.masterIdMapping = masterIdMapping;
        }
    }

    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    public static class ReadReplicaIdMapping{
        private String appId;
        private String appIdShort;
        private String appName;
        private String diskType;
        private String azone;
        private String status;
        private String subStatus;
        private int allocatedMemoryInMB;
        private int cpuCount;
        private int allocatedStorageInGB;
        private double usedStorageInGB;
        private List<SubnetMapping> subnetMapping;

        public List<SubnetMapping> getSubnetMapping() {
            return subnetMapping;
        }

        public void setSubnetMapping(List<SubnetMapping> subnetMapping) {
            this.subnetMapping = subnetMapping;
        }

        public String getSubStatus() {
            return subStatus;
        }

        public void setSubStatus(String subStatus) {
            this.subStatus = subStatus;
        }


        public String getAppName() {
            return appName;
        }

        public void setAppName(String appName) {
            this.appName = appName;
        }

        public String getDiskType() {
            return diskType;
        }

        public void setDiskType(String diskType) {
            this.diskType = diskType;
        }

        public String getAzone() {
            return azone;
        }

        public void setAzone(String azone) {
            this.azone = azone;
        }

        public int getAllocatedMemoryInMB() {
            return allocatedMemoryInMB;
        }

        public void setAllocatedMemoryInMB(int allocatedMemoryInMB) {
            this.allocatedMemoryInMB = allocatedMemoryInMB;
        }

        public int getCpuCount() {
            return cpuCount;
        }

        public void setCpuCount(int cpuCount) {
            this.cpuCount = cpuCount;
        }

        public int getAllocatedStorageInGB() {
            return allocatedStorageInGB;
        }

        public void setAllocatedStorageInGB(int allocatedStorageInGB) {
            this.allocatedStorageInGB = allocatedStorageInGB;
        }

        public double getUsedStorageInGB() {
            return usedStorageInGB;
        }

        public void setUsedStorageInGB(double usedStorageInGB) {
            this.usedStorageInGB = usedStorageInGB;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getAppIdShort() {
            return appIdShort;
        }

        public void setAppIdShort(String appIdShort) {
            this.appIdShort = appIdShort;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public static class SubnetMapping {
            private String azone;
            private String subnetId;

            public String getAzone() {
                return azone;
            }

            public void setAzone(String azone) {
                this.azone = azone;
            }

            public String getSubnetId() {
                return subnetId;
            }

            public void setSubnetId(String subnetId) {
                this.subnetId = subnetId;
            }
        }
    }

    public static class MasterIdMapping{
        private String appId;
        private String appIdShort;
        private String status;
        private int allocatedMemoryInMB;
        private int cpuCount;
        private int allocatedStorageInGB;
        private double usedStorageInGB;

        public int getAllocatedMemoryInMB() {
            return allocatedMemoryInMB;
        }

        public void setAllocatedMemoryInMB(int allocatedMemoryInMB) {
            this.allocatedMemoryInMB = allocatedMemoryInMB;
        }

        public int getCpuCount() {
            return cpuCount;
        }

        public void setCpuCount(int cpuCount) {
            this.cpuCount = cpuCount;
        }

        public int getAllocatedStorageInGB() {
            return allocatedStorageInGB;
        }

        public void setAllocatedStorageInGB(int allocatedStorageInGB) {
            this.allocatedStorageInGB = allocatedStorageInGB;
        }

        public double getUsedStorageInGB() {
            return usedStorageInGB;
        }

        public void setUsedStorageInGB(double usedStorageInGB) {
            this.usedStorageInGB = usedStorageInGB;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getAppIdShort() {
            return appIdShort;
        }

        public void setAppIdShort(String appIdShort) {
            this.appIdShort = appIdShort;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RoleInfo {
        private String id;
        private String azone;
        private String subnetId;

        private String name;
        private String vpcCidr;
        private String ShortId;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getAzone() {
            return azone;
        }

        public void setAzone(String azone) {
            this.azone = azone;
        }

        public String getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(String subnetId) {
            this.subnetId = subnetId;
        }

        public String getVpcCidr() {
            return vpcCidr;
        }

        public void setVpcCidr(String vpcCidr) {
            this.vpcCidr = vpcCidr;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getShortId() {
            return ShortId;
        }

        public void setShortId(String shortId) {
            ShortId = shortId;
        }
    }

    public static class RoleInfoProxy {
        private List<String> subnetId;

        public List<String> getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(List<String> subnetId) {
            this.subnetId = subnetId;
        }
    }
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RoGroup {
        private String roGroupId;
        private String roGroupName;
        private InstanceDict roGroupEndpoint;
        private String vpcId;
        private String subnetId;
        private String eipStatus;
        private List<AppList>roGroupAppList;
        private SimpleVpcVo vpcVo;
        private SubnetVo subnetVo;
        private String status;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getRoGroupId() {
            return roGroupId;
        }

        public void setRoGroupId(String roGroupId) {
            this.roGroupId = roGroupId;
        }

        public String getRoGroupName() {
            return roGroupName;
        }

        public void setRoGroupName(String roGroupName) {
            this.roGroupName = roGroupName;
        }

        public InstanceDict getRoGroupEndpoint() {
            return roGroupEndpoint;
        }

        public void setRoGroupEndpoint(InstanceDict roGroupEndpoint) {
            this.roGroupEndpoint = roGroupEndpoint;
        }

        public String getVpcId() {
            return vpcId;
        }

        public void setVpcId(String vpcId) {
            this.vpcId = vpcId;
        }

        public String getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(String subnetId) {
            this.subnetId = subnetId;
        }

        public String getEipStatus() {
            return eipStatus;
        }

        public void setEipStatus(String eipStatus) {
            this.eipStatus = eipStatus;
        }

        public List<AppList> getRoGroupAppList() {
            return roGroupAppList;
        }

        public void setRoGroupAppList(List<AppList> roGroupAppList) {
            this.roGroupAppList = roGroupAppList;
        }

        public SimpleVpcVo getVpcVo() {
            return vpcVo;
        }

        public void setVpcVo(SimpleVpcVo vpcVo) {
            this.vpcVo = vpcVo;
        }

        public SubnetVo getSubnetVo() {
            return subnetVo;
        }

        public void setSubnetVo(SubnetVo subnetVo) {
            this.subnetVo = subnetVo;
        }
    }

}


