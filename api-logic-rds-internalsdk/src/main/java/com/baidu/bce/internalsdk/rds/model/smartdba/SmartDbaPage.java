package com.baidu.bce.internalsdk.rds.model.smartdba;

import java.util.ArrayList;
import java.util.List;

public class SmartDbaPage {

    private List<SmartDbaPageInfo> data = new ArrayList<>();

    private Integer totalCount;

    public SmartDbaPage() {
    }

    public SmartDbaPage(List<SmartDbaPageInfo> data, Integer totalCount) {
        this.data = data;
        this.totalCount = totalCount;
    }

    public List<SmartDbaPageInfo> getData() {
        return data;
    }

    public void setData(List<SmartDbaPageInfo> data) {
        this.data = data;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    @Override
    public String toString() {
        return "SmartDbaPage{" +
                "data=" + data +
                ", totalCount=" + totalCount +
                '}';
    }
}
