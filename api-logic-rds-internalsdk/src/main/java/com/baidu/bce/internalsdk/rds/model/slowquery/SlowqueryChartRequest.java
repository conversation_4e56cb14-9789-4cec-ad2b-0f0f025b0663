package com.baidu.bce.internalsdk.rds.model.slowquery;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class SlowqueryChartRequest {
    @IdPermission
    private String instanceId;

    private String startTime;

    private String endTime;

    private String summaryType;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }


    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getSummaryType() {
        return summaryType;
    }

    public void setSummaryType(String summaryType) {
        this.summaryType = summaryType;
    }
}
