package com.baidu.bce.internalsdk.rds.model;

import java.util.ArrayList;
import java.util.List;

public class ViewPageResponse {

    private Integer instanceTotalCount;

    private Integer instanceRunningCount;

    private Integer instanceWillExpireCount; // 即将过期

    private Integer instanceExpiredCount; // 已过期

    private Integer instanceType = 3;

    private Integer InstanceMysqlCount;

    private Integer InstanceSqlServerCount;

    private Integer InstancePGCount;

    private Integer instancePayType = 2;

    private Integer prePayCount;

    private Integer postPayCount;

    private List<InstanceRegion> instanceRegionList = new ArrayList<>();

    public ViewPageResponse() {
    }

    public ViewPageResponse(Integer instanceTotalCount, Integer instanceRunningCount, Integer instanceWillExpireCount,
                            Integer instanceExpiredCount, Integer instanceType, Integer instanceMysqlCount,
                            Integer instanceSqlServerCount, Integer instancePGCount, Integer instancePayType,
                            Integer prePayCount, Integer postPayCount, List<InstanceRegion> instanceRegionList) {
        this.instanceTotalCount = instanceTotalCount;
        this.instanceRunningCount = instanceRunningCount;
        this.instanceWillExpireCount = instanceWillExpireCount;
        this.instanceExpiredCount = instanceExpiredCount;
        this.instanceType = instanceType;
        InstanceMysqlCount = instanceMysqlCount;
        InstanceSqlServerCount = instanceSqlServerCount;
        InstancePGCount = instancePGCount;
        this.instancePayType = instancePayType;
        this.prePayCount = prePayCount;
        this.postPayCount = postPayCount;
        this.instanceRegionList = instanceRegionList;
    }

    public Integer getInstanceTotalCount() {
        return instanceTotalCount;
    }

    public void setInstanceTotalCount(Integer instanceTotalCount) {
        this.instanceTotalCount = instanceTotalCount;
    }

    public Integer getInstanceRunningCount() {
        return instanceRunningCount;
    }

    public void setInstanceRunningCount(Integer instanceRunningCount) {
        this.instanceRunningCount = instanceRunningCount;
    }

    public Integer getInstanceWillExpireCount() {
        return instanceWillExpireCount;
    }

    public void setInstanceWillExpireCount(Integer instanceWillExpireCount) {
        this.instanceWillExpireCount = instanceWillExpireCount;
    }

    public Integer getInstanceExpiredCount() {
        return instanceExpiredCount;
    }

    public void setInstanceExpiredCount(Integer instanceExpiredCount) {
        this.instanceExpiredCount = instanceExpiredCount;
    }

    public Integer getInstanceType() {
        return instanceType;
    }

    public void setInstanceType(Integer instanceType) {
        this.instanceType = instanceType;
    }

    public Integer getInstanceMysqlCount() {
        return InstanceMysqlCount;
    }

    public void setInstanceMysqlCount(Integer instanceMysqlCount) {
        InstanceMysqlCount = instanceMysqlCount;
    }

    public Integer getInstanceSqlServerCount() {
        return InstanceSqlServerCount;
    }

    public void setInstanceSqlServerCount(Integer instanceSqlServerCount) {
        InstanceSqlServerCount = instanceSqlServerCount;
    }

    public Integer getInstancePGCount() {
        return InstancePGCount;
    }

    public void setInstancePGCount(Integer instancePGCount) {
        InstancePGCount = instancePGCount;
    }

    public Integer getInstancePayType() {
        return instancePayType;
    }

    public void setInstancePayType(Integer instancePayType) {
        this.instancePayType = instancePayType;
    }

    public Integer getPrePayCount() {
        return prePayCount;
    }

    public void setPrePayCount(Integer prePayCount) {
        this.prePayCount = prePayCount;
    }

    public Integer getPostPayCount() {
        return postPayCount;
    }

    public void setPostPayCount(Integer postPayCount) {
        this.postPayCount = postPayCount;
    }

    public List<InstanceRegion> getInstanceRegionList() {
        return instanceRegionList;
    }

    public void setInstanceRegionList(List<InstanceRegion> instanceRegionList) {
        this.instanceRegionList = instanceRegionList;
    }

    @Override
    public String toString() {
        return "ViewPageResponse{" +
                "instanceTotalCount=" + instanceTotalCount +
                ", instanceRunningCount=" + instanceRunningCount +
                ", instanceWillExpireCount=" + instanceWillExpireCount +
                ", instanceExpiredCount=" + instanceExpiredCount +
                ", instanceType=" + instanceType +
                ", InstanceMysqlCount=" + InstanceMysqlCount +
                ", InstanceSqlServerCount=" + InstanceSqlServerCount +
                ", InstancePGCount=" + InstancePGCount +
                ", instancePayType=" + instancePayType +
                ", prePayCount=" + prePayCount +
                ", postPayCount=" + postPayCount +
                ", instanceRegionList=" + instanceRegionList +
                '}';
    }
}
