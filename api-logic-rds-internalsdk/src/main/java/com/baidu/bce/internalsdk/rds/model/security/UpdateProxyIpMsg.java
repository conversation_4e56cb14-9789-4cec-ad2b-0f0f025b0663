package com.baidu.bce.internalsdk.rds.model.security;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateProxyIpMsg {
    @IdPermission
    private String rdsproxyId;
    private String accountName;
    private List<String> ipList;

    public String getRdsproxyId() {
        return rdsproxyId;
    }

    public void setRdsproxyId(String rdsproxyId) {
        this.rdsproxyId = rdsproxyId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public List<String> getIpList() {
        return ipList;
    }

    public void setIpList(List<String> ipList) {
        this.ipList = ipList;
    }
}
