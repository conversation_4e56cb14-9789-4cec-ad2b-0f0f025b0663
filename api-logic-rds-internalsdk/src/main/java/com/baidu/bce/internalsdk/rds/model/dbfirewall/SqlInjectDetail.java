package com.baidu.bce.internalsdk.rds.model.dbfirewall;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/3.
 */
public class SqlInjectDetail extends SqlInject {

    private String dbproxyIp;       // SQL注入来源代理实例IP

    private String dbproxyPort;

    private String secLevel;        // DBFW在拦截该条SQL主收入时的安全级别 告警WARNING 阻断FORBID

    public String getDbproxyIp() {
        return dbproxyIp;
    }

    public void setDbproxyIp(String dbproxyIp) {
        this.dbproxyIp = dbproxyIp;
    }

    public String getDbproxyPort() {
        return dbproxyPort;
    }

    public void setDbproxyPort(String dbproxyPort) {
        this.dbproxyPort = dbproxyPort;
    }

    public String getSecLevel() {
        return secLevel;
    }

    public void setSecLevel(String secLevel) {
        this.secLevel = secLevel;
    }

    @Override
    public String toString() {
        return "SqlInjectDetail{"
                + "dbproxyIp='" + dbproxyIp + '\''
                + ", dbproxyPort='" + dbproxyPort + '\''
                + ", secLevel='" + secLevel + '\''
                + '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        SqlInjectDetail that = (SqlInjectDetail) o;

        if (dbproxyIp != null ? !dbproxyIp.equals(that.dbproxyIp) : that.dbproxyIp != null) {
            return false;
        }
        if (dbproxyPort != null ? !dbproxyPort.equals(that.dbproxyPort) : that.dbproxyPort != null) {
            return false;
        }
        return secLevel != null ? secLevel.equals(that.secLevel) : that.secLevel == null;
    }

    @Override
    public int hashCode() {
        int result = dbproxyIp != null ? dbproxyIp.hashCode() : 0;
        result = 31 * result + (dbproxyPort != null ? dbproxyPort.hashCode() : 0);
        result = 31 * result + (secLevel != null ? secLevel.hashCode() : 0);
        return result;
    }
}
