package com.baidu.bce.internalsdk.rds.model.dbfirewall;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Created by liuruisen on 2017/11/6.
 */
public class SqlWhiteListResponse {

    private Collection<SqlWhiteDetail> sqlWhiteList = new LinkedList<>();

    public Collection<SqlWhiteDetail> getSqlWhiteList() {
        return sqlWhiteList;
    }

    public void setSqlWhiteList(Collection<SqlWhiteDetail> sqlWhiteList) {
        this.sqlWhiteList = sqlWhiteList;
    }

    @Override
    public String toString() {
        return "SqlWhiteListResponse{"
                + "sqlWhiteList=" + sqlWhiteList
                + '}';
    }
}
