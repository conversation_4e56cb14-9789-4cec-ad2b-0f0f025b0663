package com.baidu.bce.internalsdk.rds.model.security;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdMapper;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SecurityGroupBindRequest {

    private List<String> instanceUuids;
    @IdMapper
    @IdPermission
    private List<String> instanceIds;

    private List<String> securityGroupIds;

    private List<String> securityGroupUuids;

    private String instanceType = "RDS";

    private String subInstanceType;

    public SecurityGroupBindRequest() {
    }

    public List<String> getInstanceUuids() {
        return instanceUuids;
    }

    public void setInstanceUuids(List<String> instanceUuids) {
        this.instanceUuids = instanceUuids;
    }

    public List<String> getInstanceIds() {
        return instanceIds;
    }

    public void setInstanceIds(List<String> instanceIds) {
        this.instanceIds = instanceIds;
    }

    public List<String> getSecurityGroupIds() {
        return securityGroupIds;
    }

    public void setSecurityGroupIds(List<String> securityGroupIds) {
        this.securityGroupIds = securityGroupIds;
    }

    public List<String> getSecurityGroupUuids() {
        return securityGroupUuids;
    }

    public void setSecurityGroupUuids(List<String> securityGroupUuids) {
        this.securityGroupUuids = securityGroupUuids;
    }

    public String getInstanceType() {
        return instanceType;
    }

    public String getSubInstanceType() {
        return subInstanceType;
    }

    public void setSubInstanceType(String subInstanceType) {
        this.subInstanceType = subInstanceType;
    }
}