package com.baidu.bce.internalsdk.rds.model.group;


import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class DashboardGroupDatabaseDeleteRequest {
    @NotNull
    private String groupId;
    @NotNull
    private String dbName;
    private String code;
    private String authCode;

    @Override
    public String toString() {
        return "DashboardDatabaseDeleteRequest{" +
                "groupId='" + getGroupId() + '\'' +
                ", dbName='" + getDbName() + '\'' +
                ", code='" + getCode() + '\'' +
                '}';
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
}
