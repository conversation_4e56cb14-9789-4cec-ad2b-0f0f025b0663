package com.baidu.bce.internalsdk.rds.model.edge;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * 参考wiki：
 *  <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/bPDEaFBbnd/04hxQCVwYK/EJb0xT35QMomSI">边缘计算节点-内部文档</a>
 *  <a href="https://cloud.baidu.com/doc/BEC/s/ul2zrclz9">边缘计算-OpenAPI 文档</a>
 *
 * 整体结构如下：
 *  {
 *     "regionList": [
 *         {
 *             "region": "EAST_CHINA",
 *             "name": "华东",
 *             "country": "CHINA",
 *             "countryName": "中国",
 *             "cityList": [
 *                 {
 *                     "city": "HANGZHOU",
 *                     "name": "杭州",
 *                     "serviceProviderList": [
 *                         {
 *                             "serviceProvider": "CHINA_MOBILE",
 *                             "name": "移动",
 *                             "regionId": "cn-hangzhou-cm"
 *                         }
 *                     ]
 *                 }
 *             ]
 *         }
 *     ]
 * }
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class EdgeRegionListResponse {

    private List<EdgeRegion> regionList;

    public List<EdgeRegion> getRegionList() {
        return regionList;
    }

    public void setRegionList(List<EdgeRegion> regionList) {
        this.regionList = regionList;
    }
}
