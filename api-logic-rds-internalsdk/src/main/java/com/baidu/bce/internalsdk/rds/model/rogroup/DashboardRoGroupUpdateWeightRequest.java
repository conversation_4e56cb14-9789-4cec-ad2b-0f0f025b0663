package com.baidu.bce.internalsdk.rds.model.rogroup;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import java.util.List;

public class DashboardRoGroupUpdateWeightRequest {
    @IdPermission
    private String sourceAppId;
    private String roGroupId;
    private List<AppList> readReplicaList;

    public String getSourceAppId() {
        return sourceAppId;
    }

    public void setSourceAppId(String sourceAppId) {
        this.sourceAppId = sourceAppId;
    }

    public String getRoGroupId() {
        return roGroupId;
    }

    public void setRoGroupId(String roGroupId) {
        this.roGroupId = roGroupId;
    }

    public List<AppList> getReadReplicaList() {
        return readReplicaList;
    }

    public void setReadReplicaList(List<AppList> readReplicaList) {
        this.readReplicaList = readReplicaList;
    }
}
