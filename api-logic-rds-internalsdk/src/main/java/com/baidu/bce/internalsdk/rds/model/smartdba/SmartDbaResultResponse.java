package com.baidu.bce.internalsdk.rds.model.smartdba;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SmartDbaResultResponse {

    private Boolean success;

    private BaseResponse.Message message = new BaseResponse.Message();

    private String status;

    private SmartDbaDbResponse result;

    private String info;

    private DiskInfo data;

    public SmartDbaResultResponse() {
    }

    public SmartDbaResultResponse(Boolean success, BaseResponse.Message message,
                                  String status, SmartDbaDbResponse result, String info, DiskInfo data) {
        this.success = success;
        this.message = message;
        this.status = status;
        this.result = result;
        this.info = info;
        this.data = data;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public BaseResponse.Message getMessage() {
        return message;
    }

    public void setMessage(BaseResponse.Message message) {
        this.message = message;
    }

    public SmartDbaDbResponse getResult() {
        return result;
    }

    public void setResult(SmartDbaDbResponse result) {
        this.result = result;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public DiskInfo getData() {
        return data;
    }

    public void setData(DiskInfo data) {
        this.data = data;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    @Override
    public String toString() {
        return "SmartDbaResultResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", status='" + status + '\'' +
                ", result=" + result +
                ", info='" + info + '\'' +
                ", data=" + data +
                '}';
    }
}

