package com.baidu.bce.internalsdk.rds.model.security;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenApiSecurityGroup {

    private String securityGroupRemark;

    private String projectId;

    private String securityGroupName;

    private String securityGroupId;

    private String securityGroupUuid;

    private String tenantId;

    private String vpcId;

    private String vpcName;

    private List<SecurityGroupRule> outbound;

    private List<SecurityGroupRule> inbound;

    private List<SecurityGroupRule> rules;

    public OpenApiSecurityGroup() {
    }

    public String getSecurityGroupRemark() {
        return securityGroupRemark;
    }

    public void setSecurityGroupRemark(String securityGroupRemark) {
        this.securityGroupRemark = securityGroupRemark;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getSecurityGroupName() {
        return securityGroupName;
    }

    public void setSecurityGroupName(String securityGroupName) {
        this.securityGroupName = securityGroupName;
    }

    public List<SecurityGroupRule> getOutbound() {
        return outbound;
    }

    public void setOutbound(List<SecurityGroupRule> outbound) {
        this.outbound = outbound;
    }

    public List<SecurityGroupRule> getInbound() {
        return inbound;
    }

    public void setInbound(List<SecurityGroupRule> inbound) {
        this.inbound = inbound;
    }

    public String getSecurityGroupId() {
        return securityGroupId;
    }

    public void setSecurityGroupId(String securityGroupId) {
        this.securityGroupId = securityGroupId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getVpcId() {
        return vpcId;
    }

    public void setVpcId(String vpcId) {
        this.vpcId = vpcId;
    }

    public String getVpcName() {
        return vpcName;
    }

    public void setVpcName(String vpcName) {
        this.vpcName = vpcName;
    }

    public List<SecurityGroupRule> getRules() {
        return rules;
    }

    public void setRules(List<SecurityGroupRule> rules) {
        this.rules = rules;
    }

    public String getSecurityGroupUuid() {
        return securityGroupUuid;
    }

    public void setSecurityGroupUuid(String securityGroupUuid) {
        this.securityGroupUuid = securityGroupUuid;
    }
}
