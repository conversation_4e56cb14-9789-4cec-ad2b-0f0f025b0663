package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/23.
 */
public class DashboardBackupCommonRequest {
    @NotNull
    @IdPermission
    private String instanceId;
    @NotNull
    private String snapshotId;

    @Override
    public String toString() {
        return "DashboardBackupCommonRequest{" +
                "instanceId='" + instanceId + '\'' +
                ", snapshotId='" + snapshotId + '\'' +
                '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getSnapshotId() {
        return snapshotId;
    }

    public void setSnapshotId(String snapshotId) {
        this.snapshotId = snapshotId;
    }
}
