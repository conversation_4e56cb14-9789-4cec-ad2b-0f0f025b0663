package com.baidu.bce.internalsdk.rds.model.slowquery;

/**
 * Created by luping03 on 17/12/25.
 */
public class SlowqueryStartOrStopResponse extends SlowqueryBaseResponse {

    private StartOrStopResult result;

    public StartOrStopResult getResult() {
        return result;
    }

    public void setResult(StartOrStopResult result) {
        this.result = result;
    }

    public static class StartOrStopResult {

        private String startSlowQuery;

        private String stopSlowQuery;


        public String getStartSlowQuery() {
            return startSlowQuery;
        }

        public void setStartSlowQuery(String startSlowQuery) {
            this.startSlowQuery = startSlowQuery;
        }

        public String getStopSlowQuery() {
            return stopSlowQuery;
        }

        public void setStopSlowQuery(String stopSlowQuery) {
            this.stopSlowQuery = stopSlowQuery;
        }
    }


}
