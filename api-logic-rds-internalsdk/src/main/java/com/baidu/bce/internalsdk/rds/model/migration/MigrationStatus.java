package com.baidu.bce.internalsdk.rds.model.migration;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/10/27.
 */
public class MigrationStatus implements Comparable<MigrationStatus> {
    private String remoteIp;
    private int remotePort;
    private String remoteUser;
    private String migrationId;
    private Date createTime;
    private String migrationType;
    private String migrationStatus;
    private CheckItem.CheckItemList checkList;
    private String checkStatus;
    private String migrationMethod;
    private String description;
    private Boolean lockTables;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("MigrationStatus{");
        sb.append("remoteIp='").append(remoteIp).append('\'');
        sb.append(", remotePort=").append(remotePort);
        sb.append(", remoteUser='").append(remoteUser).append('\'');
        sb.append(", migrationId='").append(migrationId).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", migrationType='").append(migrationType).append('\'');
        sb.append(", migrationStatus='").append(migrationStatus).append('\'');
        sb.append(", checkList=").append(checkList);
        sb.append(", checkStatus='").append(checkStatus).append('\'');
        sb.append(", migrationMethod='").append(migrationMethod).append('\'');
        sb.append(", description='").append(description).append('\'');
        sb.append(", lockTables=").append(lockTables);
        sb.append('}');
        return sb.toString();
    }

    @Override
    public int compareTo(MigrationStatus o) {
        return this.createTime.compareTo(o.createTime);
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getMigrationId() {
        return migrationId;
    }

    public void setMigrationId(String migrationId) {
        this.migrationId = migrationId;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Date getCreateTime() {
        return createTime;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getMigrationType() {
        return migrationType;
    }

    public void setMigrationType(String migrationType) {
        this.migrationType = migrationType;
    }

    public String getMigrationStatus() {
        return migrationStatus;
    }

    public void setMigrationStatus(String migrationStatus) {
        this.migrationStatus = migrationStatus;
    }

    public CheckItem.CheckItemList getCheckList() {
        return checkList;
    }

    public void setCheckList(CheckItem.CheckItemList checkList) {
        this.checkList = checkList;
    }

    public String getMigrationMethod() {
        return migrationMethod;
    }

    public void setMigrationMethod(String migrationMethod) {
        this.migrationMethod = migrationMethod;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRemoteIp() {
        return remoteIp;
    }

    public void setRemoteIp(String remoteIp) {
        this.remoteIp = remoteIp;
    }

    public int getRemotePort() {
        return remotePort;
    }

    public void setRemotePort(int remotePort) {
        this.remotePort = remotePort;
    }

    public String getRemoteUser() {
        return remoteUser;
    }

    public void setRemoteUser(String remoteUser) {
        this.remoteUser = remoteUser;
    }

    public Boolean getLockTables() {
        return lockTables;
    }

    public void setLockTables(Boolean lockTables) {
        this.lockTables = lockTables;
    }

    public MigrationStatus withIp(final String ip) {
        this.remoteIp = ip;
        return this;
    }

    public MigrationStatus withPort(final int port) {
        this.remotePort = port;
        return this;
    }

    public MigrationStatus withUser(final String user) {
        this.remoteUser = user;
        return this;
    }

    public MigrationStatus withMigrationId(final String migrationId) {
        this.migrationId = migrationId;
        return this;
    }

    public MigrationStatus withCreateTime(final Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public MigrationStatus withMigrationType(final String migrationType) {
        this.migrationType = migrationType;
        return this;
    }

    public MigrationStatus withMigrationStatus(final String migrationStatus) {
        this.migrationStatus = migrationStatus;
        return this;
    }

    public MigrationStatus withCheckList(final CheckItem.CheckItemList checkList) {
        this.checkList = checkList;
        return this;
    }

    public MigrationStatus withCheckStatus(final String checkStatus) {
        this.checkStatus = checkStatus;
        return this;
    }

    public MigrationStatus withMigrationMethod(final String migrationMethod) {
        this.migrationMethod = migrationMethod;
        return this;
    }

    public MigrationStatus withDescription(final String description) {
        this.description = description;
        return this;
    }

    public MigrationStatus withLockTables(final Boolean lockTables) {
        this.lockTables = lockTables;
        return this;
    }

}
