package com.baidu.bce.internalsdk.rds.model.performance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by yangxin on 2022/1/14
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConnectionListResponse {

    private String datetime;
    private List<RDSConnection> connectList;

    public String getDatetime() {
        return datetime;
    }

    public void setDatetime(String datetime) {
        this.datetime = datetime;
    }

    public List<RDSConnection> getConnectList() {
        return connectList;
    }

    public void setConnectList(List<RDSConnection> connectList) {
        this.connectList = connectList;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RDSConnection {
        private String proto;
        private Integer recvQ;
        private Integer sendQ;
        private String localAddress;
        private String foreignAddress;
        private String state;

        public String getProto() {
            return proto;
        }

        public void setProto(String proto) {
            this.proto = proto;
        }

        public Integer getRecvQ() {
            return recvQ;
        }

        public void setRecvQ(Integer recvQ) {
            this.recvQ = recvQ;
        }

        public Integer getSendQ() {
            return sendQ;
        }

        public void setSendQ(Integer sendQ) {
            this.sendQ = sendQ;
        }

        public String getLocalAddress() {
            return localAddress;
        }

        public void setLocalAddress(String localAddress) {
            this.localAddress = localAddress;
        }

        public String getForeignAddress() {
            return foreignAddress;
        }

        public void setForeignAddress(String foreignAddress) {
            this.foreignAddress = foreignAddress;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }
    }
}
