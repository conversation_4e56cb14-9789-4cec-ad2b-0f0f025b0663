package com.baidu.bce.internalsdk.rds.model.slowquery;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class SlowqueryTaskListMarkerResponse extends SlowqueryBaseResponse{

    private TaskListPageModel page;

    public TaskListPageModel getPage() {
        return page;
    }

    public void setPage(TaskListPageModel page) {
        this.page = page;
    }

    public static class TaskListPageModel extends SlowqueryPageBaseRequest{
        private int totalCount;

        private List<SlowqueryTaskModel> result;

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public List<SlowqueryTaskModel> getResult() {
            return result;
        }

        public void setResult(List<SlowqueryTaskModel> result) {
            this.result = result;
        }
    }

}
