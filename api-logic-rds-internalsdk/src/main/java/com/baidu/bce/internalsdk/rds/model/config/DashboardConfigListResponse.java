package com.baidu.bce.internalsdk.rds.model.config;

import com.baidu.bce.plat.webframework.model.edp.EdpResultResponse;

import java.util.ArrayList;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/19.
 */

public class DashboardConfigListResponse extends EdpResultResponse<DashboardConfigListResponse.ConfigList> {
    public static class ConfigList {
        private ArrayList<ConfigItem> items = new ArrayList<ConfigItem>();
        private int totalCount = 0;

        public ArrayList<ConfigItem> getItems() {
            return items;
        }

        public void setItems(ArrayList<ConfigItem> items) {
            this.items = items;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }
    }
}