package com.baidu.bce.internalsdk.rds.model.instance;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.RDSRegexp;

import javax.validation.constraints.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class DashboardInstanceDomainRequest {
    @IdPermission
    private String instanceId;

    @Pattern(regexp = RDSRegexp.domainPrefix)
    private String domain;


    @Override
    public String toString() {
        return "DashboardInstanceDomainRequest{"
                + "instanceId='" + instanceId + '\''
                + ", domain='" + domain + '\''
                + '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }
}
