package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BackupTypeRequest {


    private String dataBackupType;

    public BackupTypeRequest(String dataBackupType) {
        this.dataBackupType = dataBackupType;
    }

    public String getDataBackupType() {
        return dataBackupType;
    }

    public void setDataBackupType(String dataBackupType) {
        this.dataBackupType = dataBackupType;
    }
}
