package com.baidu.bce.internalsdk.rds.model.bcchan;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GetIsInternalUserResponse {

    private Boolean internalUser;

    public Boolean getInternalUser() {
        return internalUser;
    }

    public void setInternalUser(Boolean internalUser) {
        this.internalUser = internalUser;
    }
}
