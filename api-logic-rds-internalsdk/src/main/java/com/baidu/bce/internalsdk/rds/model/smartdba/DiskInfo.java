package com.baidu.bce.internalsdk.rds.model.smartdba;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


@JsonIgnoreProperties(ignoreUnknown = true)
public class DiskInfo {

    private Double grow; // 日均增长MB
    private Double useDay; // 剩余可用天数
    private Double diskFree; // 剩余空间MB
    private Double diskUse; // 已用空间MB
    private Double diskQuota; // 套餐大小MB


    public DiskInfo() {
    }

    public DiskInfo(Double grow, Double useDay, Double diskFree, Double diskUse, Double diskQuota) {
        this.grow = grow;
        this.useDay = useDay;
        this.diskFree = diskFree;
        this.diskUse = diskUse;
        this.diskQuota = diskQuota;
    }

    public Double getGrow() {
        return grow;
    }

    public void setGrow(Double grow) {
        this.grow = grow;
    }

    public Double getUseDay() {
        return useDay;
    }

    public void setUseDay(Double useDay) {
        this.useDay = useDay;
    }

    public Double getDiskFree() {
        return diskFree;
    }

    public void setDiskFree(Double diskFree) {
        this.diskFree = diskFree;
    }

    public Double getDiskUse() {
        return diskUse;
    }

    public void setDiskUse(Double diskUse) {
        this.diskUse = diskUse;
    }

    public Double getDiskQuota() {
        return diskQuota;
    }

    public void setDiskQuota(Double diskQuota) {
        this.diskQuota = diskQuota;
    }


    @Override
    public String toString() {
        return "DiskInfo{" +
                "grow=" + grow +
                ", useDay=" + useDay +
                ", diskFree=" + diskFree +
                ", diskUse=" + diskUse +
                ", diskQuota=" + diskQuota +
                '}';
    }
}
