package com.baidu.bce.internalsdk.rds.model.instance;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SlaInstanceResponse {

    private String weekly;

    private String ratio;

    public void setWeekly(String weekly) {
        this.ratio = weekly;
    }

    public String getRatio() {
        return ratio;
    }
}
