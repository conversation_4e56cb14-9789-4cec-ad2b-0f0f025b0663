package com.baidu.bce.internalsdk.rds.model.group;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */
public class DashboardGroupAccountListRequest {
    private String groupId;
    private String orderBy;
    private String order;
    private int pageNo;

    @Override
    public String toString() {
        return "DashboardAccountListRequest{" +
                "groupId='" + getGroupId() + '\'' +
                ", orderBy='" + orderBy + '\'' +
                ", order='" + order + '\'' +
                ", pageNo=" + pageNo +
                '}';
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }


    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
}
