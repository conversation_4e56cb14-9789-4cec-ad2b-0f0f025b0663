package com.baidu.bce.internalsdk.rds.model.paratemplate;

import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

public class ParaTemplateCreateRequest {

    @NotEmpty
//    @Pattern(regexp = PatternString.PARAMETER_TEMPLATE_NAME)
    private String name;

    private String dbType;

    private String desc;

    private String dbVersion;

    private List<ParaTemplateCreateModel> parameters;

    public ParaTemplateCreateRequest() {
    }

    public ParaTemplateCreateRequest(String name, String dbType,
                                     String desc, String dbVersion, List<ParaTemplateCreateModel> parameters) {
        this.name = name;
        this.dbType = dbType;
        this.desc = desc;
        this.dbVersion = dbVersion;
        this.parameters = parameters;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDbVersion() {
        return dbVersion;
    }

    public void setDbVersion(String dbVersion) {
        this.dbVersion = dbVersion;
    }

    public List<ParaTemplateCreateModel> getParameters() {
        return parameters;
    }

    public void setParameters(List<ParaTemplateCreateModel> parameters) {
        this.parameters = parameters;
    }

    @Override
    public String toString() {
        return "ParaTemplateCreateRequest{" +
                "name='" + name + '\'' +
                ", dbType='" + dbType + '\'' +
                ", desc='" + desc + '\'' +
                ", dbVersion='" + dbVersion + '\'' +
                ", parameters=" + parameters +
                '}';
    }
}
