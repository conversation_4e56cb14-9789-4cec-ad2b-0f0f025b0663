package com.baidu.bce.internalsdk.rds.model.security;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SecurityGroupListResp {
    private Boolean success = true;
    private Message message = new Message();

    private SecurityGroupResponse result;


    @Override
    public String toString() {
        return "BaseResponse{"
                + "success=" + success
                + ", message='" + message
                + '\'' + '}';
    }

    public SecurityGroupListResp() {

    }

    public SecurityGroupListResp(boolean isSuccess, String message) {
        this.message.setGlobal(message);
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    public SecurityGroupListResp success(final Boolean success) {
        this.success = success;
        return this;
    }

    public SecurityGroupListResp message(final Message message) {
        this.message = message;
        return this;
    }

    public SecurityGroupResponse getResult() {
        return result;
    }

    public void setResult(SecurityGroupResponse result) {
        this.result = result;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Message {
        private String global;

        public String getGlobal() {
            return global;
        }

        public void setGlobal(String global) {
            this.global = global;
        }
    }
}
