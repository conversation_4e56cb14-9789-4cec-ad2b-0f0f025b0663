package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/12.
 */
public class InstanceUpdateReplicaOnLineRequest {
    @IdPermission
    private String rdsproxyId;

    private String onlineReadRepicIds;

    private String offlineReadRepicIds;

    @Override
    public String toString() {
        return "InstanceUpdateReplicaOnLineRequest{"
                + "rdsproxyId=" + rdsproxyId
                + "onlineReadRepicIds" + onlineReadRepicIds
                + "offlineReadRepicIds" + offlineReadRepicIds
                + '}';
    }

    public String getRdsproxyId() {
        return rdsproxyId;
    }

    public void setRdsproxyId(String rdsproxyId) {
        this.rdsproxyId = rdsproxyId;
    }

    public String getOnlineReadRepicIds() {
        return onlineReadRepicIds;
    }

    public void setOnlineReadRepicIds(String onlineReadRepicIds) {
        this.onlineReadRepicIds = onlineReadRepicIds;
    }

    public String getOfflineReadRepicIds() {
        return offlineReadRepicIds;
    }

    public void setOfflineReadRepicIds(String offlineReadRepicIds) {
        this.offlineReadRepicIds = offlineReadRepicIds;
    }
}
