package com.baidu.bce.internalsdk.rds;

import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.rds.model.blb.GetLbdcClusterResponse;
import endpoint.EndpointManager;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by yangxin16 on 21/09/24.
 */
public class BLBClient extends BceClient {

    private static final String SERVICE_NAME = "BLB";

    public BLBClient(String accessKey, String secretKey, String securityToken) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey, securityToken);
    }

    public BLBClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(SERVICE_NAME), accessKey, secretKey);
    }

    public BceInternalRequest createKmsRequest() {
        return super.createAuthorizedRequest();
    }

    /**
     * 获取专属集群Lbdc列表
     * API文档链接 https://cloud.baidu.com/doc/BLB/s/6kszzygx4#lbdc%E5%88%97%E8%A1%A8
     *
     * @param name 搜索名称 仅支持精确搜索
     * @return
     */
    public GetLbdcClusterResponse getLbdc(String name) {
        BceInternalRequest request = createAuthorizedRequest().path("/lbdc");
        if (StringUtils.isNotBlank(name)) {
            request.queryParam("name", name);
        }
        return request.get(GetLbdcClusterResponse.class);
    }
}
