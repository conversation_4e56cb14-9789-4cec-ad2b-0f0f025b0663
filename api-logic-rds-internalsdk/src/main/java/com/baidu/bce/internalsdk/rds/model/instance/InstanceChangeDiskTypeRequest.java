package com.baidu.bce.internalsdk.rds.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdMapper;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

public class InstanceChangeDiskTypeRequest {
    @IdMapper
    @IdPermission
    private String instanceId;
    private String targetDiskType;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getTargetDiskType() {
        return targetDiskType;
    }

    public void setTargetDiskType(String targetDiskType) {
        this.targetDiskType = targetDiskType;
    }
}
