package com.baidu.bce.internalsdk.rds.model.instance;

public class InstancePageForApiRequest {

    private Integer pageNo = 1;

    private Integer pageSize = 500;

    private String keywordType;

    private String keyword;

    public InstancePageForApiRequest() {
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getKeywordType() {
        return keywordType;
    }

    public void setKeywordType(String keywordType) {
        this.keywordType = keywordType;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String toString() {
        return "InstancePageForApiRequest{" +
                "pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", keywordType='" + keywordType + '\'' +
                ", keyword='" + keyword + '\'' +
                '}';
    }
}
