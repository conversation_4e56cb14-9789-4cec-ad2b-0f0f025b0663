package com.baidu.bce.internalsdk.rds.model.paratemplate;

import java.util.List;

public class ParaTemplateIdRequest {

    private String applyId;

    private String templateId;

    private String name;

    private String instanceId;

    private List<ParaTemplateUpdateRequest> updateRequestList;

    public ParaTemplateIdRequest() {
    }

    public ParaTemplateIdRequest(String applyId, String templateId, String instanceId,
                                 List<ParaTemplateUpdateRequest> updateRequestList) {
        this.applyId = applyId;
        this.templateId = templateId;
        this.instanceId = instanceId;
        this.updateRequestList = updateRequestList;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public List<ParaTemplateUpdateRequest> getUpdateRequestList() {
        return updateRequestList;
    }

    public void setUpdateRequestList(List<ParaTemplateUpdateRequest> updateRequestList) {
        this.updateRequestList = updateRequestList;
    }

    @Override
    public String toString() {
        return "ParaTemplateIdRequest{" +
                "applyId='" + applyId + '\'' +
                ", templateId='" + templateId + '\'' +
                ", instanceId='" + instanceId + '\'' +
                ", updateRequestList=" + updateRequestList +
                '}';
    }
}
