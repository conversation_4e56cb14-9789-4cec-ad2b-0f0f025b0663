package com.baidu.bce.internalsdk.rds.model.autorenew;

/**
 * Created by luping03 on 17/6/8.
 */
public class ListAutoRenew {
    private String order;
    private String orderBy;
    private int pageNo;
    private int pageSize;
    private int daysToExpiration = -1; // 最近多少天过期，< 0为不限制，＝ 0为已过期
    private String resourceName; // 服务名称
    private String resourceId; // 服务id

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getDaysToExpiration() {
        return daysToExpiration;
    }

    public void setDaysToExpiration(int daysToExpiration) {
        this.daysToExpiration = daysToExpiration;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    @Override
    public String toString() {
        return "ListAutoRenewRequest{"
                + "order='" + order + '\''
                + ", orderBy='" + orderBy + '\''
                + ", pageNo=" + pageNo
                + ", pageSize=" + pageSize
                + ", daysToExpiration=" + daysToExpiration
                + ", resourceName='" + resourceName + '\''
                + ", resourceId='" + resourceId + '\''
                + '}';
    }
}
