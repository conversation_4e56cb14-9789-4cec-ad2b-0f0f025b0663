package com.baidu.bce.internalsdk.rds.model;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.internalsdk.rds.model.snapshot.DataBackupRetainStrategy;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SplitCopyRegionPolicyRequest {

    @IdPermission
    private String instanceId;
    @Valid
    private SplitCopyRegionPolicy backupPolicy;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public SplitCopyRegionPolicy getBackupPolicy() {
        return backupPolicy;
    }

    public void setBackupPolicy(SplitCopyRegionPolicy backupPolicy) {
        this.backupPolicy = backupPolicy;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SplitCopyRegionPolicy{
        // 标识是否开启跨地域备份
        private boolean dataBackupCopyEnable;

        private List<String> dataBackupCopyStoragesRegions;

        @Range(min = 1, max = 730, message = "跨地域备份保留时长支持1-730天")
        private Integer dataBackupCopyRetainDays;

        // console 与 管控侧 专用字段，前端不感知
        private List<DataBackupRetainStrategy> dataBackupCopyRetainStrategys;

        public boolean getDataBackupCopyEnable() {
            return dataBackupCopyEnable;
        }

        public void setDataBackupCopyEnable(boolean dataBackupCopyEnable) {
            this.dataBackupCopyEnable = dataBackupCopyEnable;
        }

        public List<String> getDataBackupCopyStoragesRegions() {
            return dataBackupCopyStoragesRegions;
        }

        public void setDataBackupCopyStoragesRegions(List<String> dataBackupCopyStoragesRegions) {
            this.dataBackupCopyStoragesRegions = dataBackupCopyStoragesRegions;
        }

        public Integer getDataBackupCopyRetainDays() {
            return dataBackupCopyRetainDays;
        }

        public void setDataBackupCopyRetainDays(Integer dataBackupCopyRetainDays) {
            this.dataBackupCopyRetainDays = dataBackupCopyRetainDays;
        }

        public List<DataBackupRetainStrategy> getDataBackupCopyRetainStrategys() {
            return dataBackupCopyRetainStrategys;
        }

        public void setDataBackupCopyRetainStrategys(List<DataBackupRetainStrategy> dataBackupCopyRetainStrategys) {
            this.dataBackupCopyRetainStrategys = dataBackupCopyRetainStrategys;
        }
    }
}
