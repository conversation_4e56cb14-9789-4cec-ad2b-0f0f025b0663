package com.baidu.bce.internalsdk.rds.model.binlog;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Collection;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/9.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BinlogListResponse {
    private Collection<Binlog> binlogs;
    private String pageNo;
    private int pageSize;
    private int totalCount;

    @Override
    public String toString() {
        return "BinlogGetResponse{" +
                "binlogs=" + binlogs +
                '}';
    }

    public Collection<Binlog> getBinlogs() {
        return binlogs;
    }

    public void setBinlogs(Collection<Binlog> binlogs) {
        this.binlogs = binlogs;
    }

    public void setLogs(Collection<Binlog> logs) {
        this.binlogs = logs;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }
}
