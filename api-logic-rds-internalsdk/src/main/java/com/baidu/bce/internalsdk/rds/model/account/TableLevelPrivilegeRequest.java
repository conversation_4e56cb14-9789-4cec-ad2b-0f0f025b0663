package com.baidu.bce.internalsdk.rds.model.account;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TableLevelPrivilegeRequest {
    private List<String> globals;
    private List<TableLevelDbPrivilege> databases;
    private List<TableLevelTbPrivilege> tables;

    public List<String> getGlobals() {
        return globals;
    }

    public void setGlobals(List<String> globals) {
        this.globals = globals;
    }

    public List<TableLevelDbPrivilege> getDatabases() {
        return databases;
    }

    public void setDatabases(List<TableLevelDbPrivilege> databases) {
        this.databases = databases;
    }

    public List<TableLevelTbPrivilege> getTables() {
        return tables;
    }

    public void setTables(List<TableLevelTbPrivilege> tables) {
        this.tables = tables;
    }
}
