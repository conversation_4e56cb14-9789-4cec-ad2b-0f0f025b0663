package com.baidu.bce.internalsdk.rds.model.dbfirewall;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Created by liuruisen on 2017/11/6.
 */
public class SqlInjectListResponse {

    private Collection<SqlInject> sqlInjectList = new LinkedList<>();

    public Collection<SqlInject> getSqlInjectList() {
        return sqlInjectList;
    }

    public void setSqlInjectList(Collection<SqlInject> sqlInjectList) {
        this.sqlInjectList = sqlInjectList;
    }

    @Override
    public String toString() {
        return "SqlInjectListResponse{"
                + "sqlinjectList=" + sqlInjectList
                + '}';
    }
}
