package com.baidu.bce.internalsdk.rds.model.smartdba;


import java.util.ArrayList;
import java.util.List;

public class SmartDbaDbResponse {

    private List<SmartDbaInfo> dbList = new ArrayList<>();

    private Integer info;

    private Integer tbCountLimit;

    public SmartDbaDbResponse() {
    }

    public SmartDbaDbResponse(List<SmartDbaInfo> dbList, Integer info, Integer tbCountLimit) {
        this.dbList = dbList;
        this.info = info;
        this.tbCountLimit = tbCountLimit;
    }

    public List<SmartDbaInfo> getDbList() {
        return dbList;
    }

    public void setDbList(List<SmartDbaInfo> dbList) {
        this.dbList = dbList;
    }

    public Integer getInfo() {
        return info;
    }

    public void setInfo(Integer info) {
        this.info = info;
    }

    public Integer getTbCountLimit() {
        return tbCountLimit;
    }

    public void setTbCountLimit(Integer tbCountLimit) {
        this.tbCountLimit = tbCountLimit;
    }

    @Override
    public String toString() {
        return "SmartDbaDbResponse{" +
                "dbList=" + dbList +
                ", info=" + info +
                ", tbCountLimit=" + tbCountLimit +
                '}';
    }
}



