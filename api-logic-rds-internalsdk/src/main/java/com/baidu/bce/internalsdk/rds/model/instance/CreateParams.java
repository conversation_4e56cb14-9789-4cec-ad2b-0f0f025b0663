package com.baidu.bce.internalsdk.rds.model.instance;

import java.util.List;

public class CreateParams {
    public String dbType;
    public String type;
    public String applicationType;
    public Integer instanceCount;
    public String masterSubnetId;
    public String backupSubnetId;
    public String slaveSubnetId;
    public Integer nodeAmount;
    public List<String> rdsproxySubnetIds;
    public Integer cpuCount;
    public Integer allocatedMemoryInMB;
    public Integer allocatedStorageInGB;
    public String diskType;

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }

    public Integer getInstanceCount() {
        return instanceCount;
    }

    public void setInstanceCount(Integer instanceCount) {
        this.instanceCount = instanceCount;
    }

    public String getMasterSubnetId() {
        return masterSubnetId;
    }

    public void setMasterSubnetId(String masterSubnetId) {
        this.masterSubnetId = masterSubnetId;
    }

    public String getBackupSubnetId() {
        return backupSubnetId;
    }

    public void setBackupSubnetId(String backupSubnetId) {
        this.backupSubnetId = backupSubnetId;
    }

    public String getSlaveSubnetId() {
        return slaveSubnetId;
    }

    public void setSlaveSubnetId(String slaveSubnetId) {
        this.slaveSubnetId = slaveSubnetId;
    }

    public Integer getNodeAmount() {
        return nodeAmount;
    }

    public void setNodeAmount(Integer nodeAmount) {
        this.nodeAmount = nodeAmount;
    }

    public List<String> getRdsproxySubnetIds() {
        return rdsproxySubnetIds;
    }

    public void setRdsproxySubnetIds(List<String> rdsproxySubnetIds) {
        this.rdsproxySubnetIds = rdsproxySubnetIds;
    }

    public Integer getCpuCount() {
        return cpuCount;
    }

    public void setCpuCount(Integer cpuCount) {
        this.cpuCount = cpuCount;
    }

    public Integer getAllocatedMemoryInMB() {
        return allocatedMemoryInMB;
    }

    public void setAllocatedMemoryInMB(Integer allocatedMemoryInMB) {
        this.allocatedMemoryInMB = allocatedMemoryInMB;
    }

    public Integer getAllocatedStorageInGB() {
        return allocatedStorageInGB;
    }

    public void setAllocatedStorageInGB(Integer allocatedStorageInGB) {
        this.allocatedStorageInGB = allocatedStorageInGB;
    }

    public String getDiskType() {
        return diskType;
    }

    public void setDiskType(String diskType) {
        this.diskType = diskType;
    }
}
