package com.baidu.bce.internalsdk.rds.model.snapshot;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DataBackupRetainStrategy {

    // 阶梯备份开始时间，单位是秒
    private long startSeconds;
    // 阶梯备份结束时间，单位是秒
    private long endSeconds;
    // 阶梯备份保留份数，当前默认值为1
    private Integer retainCount = 1;
    // 时间精度，单位是秒
    private long precision = 1;

    public long getStartSeconds() {
        return startSeconds;
    }

    public void setStartSeconds(long startSeconds) {
        this.startSeconds = startSeconds;
    }

    public long getEndSeconds() {
        return endSeconds;
    }

    public void setEndSeconds(long endSeconds) {
        this.endSeconds = endSeconds;
    }

    public Integer getRetainCount() {
        return retainCount;
    }

    public void setRetainCount(Integer retainCount) {
        this.retainCount = retainCount;
    }

    public long getPrecision() {
        return precision;
    }

    public void setPrecision(long precision) {
        this.precision = precision;
    }
}
