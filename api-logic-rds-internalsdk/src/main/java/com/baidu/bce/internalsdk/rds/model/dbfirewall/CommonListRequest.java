package com.baidu.bce.internalsdk.rds.model.dbfirewall;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/20.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class CommonListRequest {
    @NotNull
    @IdPermission
    private String instanceId;
    private String orderBy;
    private String order;
    private int pageNo;
    private int pageSize;

    @Override
    public String toString() {
        return "CommonListRequest{"
                + "instanceId='" + instanceId + '\''
                + ", orderBy='" + orderBy + '\''
                + ", order='" + order + '\''
                + ", pageNo=" + pageNo
                + ", pageSize=" + pageSize
                + '}';
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}
