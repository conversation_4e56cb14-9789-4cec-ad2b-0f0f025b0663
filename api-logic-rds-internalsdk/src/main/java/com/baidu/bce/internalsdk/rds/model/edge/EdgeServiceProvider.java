package com.baidu.bce.internalsdk.rds.model.edge;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 参考wiki：
 *  <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/bPDEaFBbnd/04hxQCVwYK/EJb0xT35QMomSI">边缘计算节点-内部文档</a>
 *  <a href="https://cloud.baidu.com/doc/BEC/s/ul2zrclz9">边缘计算-OpenAPI 文档</a>
 *
 * 整体结构如下：
 *  {
 *     "serviceProvider": "CHINA_MOBILE",
 *     "name": "移动",
 *     "regionId": "cn-hangzhou-cm"
 * }
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class EdgeServiceProvider {

    private String name;

    private String serviceProvider;

    private String regionId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(String serviceProvider) {
        this.serviceProvider = serviceProvider;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }
}
