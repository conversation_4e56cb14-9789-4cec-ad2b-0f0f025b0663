package com.baidu.bce.internalsdk.rds.model.account;


import com.baidu.bce.externalsdk.logical.network.common.annotation.IdMapper;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;

import javax.validation.constraints.NotNull;

public class TableLevelDetailRequest {

    @IdMapper
    @IdPermission
    @NotNull
    private String instanceId;
    private TableLevelAccountDetailRequest account;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public TableLevelAccountDetailRequest getAccount() {
        return account;
    }

    public void setAccount(TableLevelAccountDetailRequest account) {
        this.account = account;
    }
}
