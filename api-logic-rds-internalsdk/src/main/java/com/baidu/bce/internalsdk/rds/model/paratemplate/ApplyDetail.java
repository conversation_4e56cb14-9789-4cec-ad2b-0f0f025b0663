package com.baidu.bce.internalsdk.rds.model.paratemplate;

public class ApplyDetail {

    private String para;

    private String newPara;

    private String oldPara;

    public ApplyDetail() {
    }

    public ApplyDetail(String para, String newPara, String oldPara) {
        this.para = para;
        this.newPara = newPara;
        this.oldPara = oldPara;
    }

    public String getPara() {
        return para;
    }

    public void setPara(String para) {
        this.para = para;
    }

    public String getNewPara() {
        return newPara;
    }

    public void setNewPara(String newPara) {
        this.newPara = newPara;
    }

    public String getOldPara() {
        return oldPara;
    }

    public void setOldPara(String oldPara) {
        this.oldPara = oldPara;
    }

    @Override
    public String toString() {
        return "ApplyDetail{" +
                "para='" + para + '\'' +
                ", newPara='" + newPara + '\'' +
                ", oldPara='" + oldPara + '\'' +
                '}';
    }
}
