package com.baidu.bce.internalsdk.rds.model.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/11/19.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfigModifyRequest {
    private ConfigModifyItem.ConfigModifyItemList parameters;
    private String effectiveTime;
    private boolean switchover  = false;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ConfigModifyRequest{");
        sb.append("parameters=").append(parameters);
        sb.append('}');
        return sb.toString();
    }


    public boolean getSwitchover() {
        return switchover;
    }

    public void setSwitchover(boolean switchover) {
        this.switchover = switchover;
    }

    public ConfigModifyItem.ConfigModifyItemList getParameters() {
        return parameters;
    }

    public void setParameters(ConfigModifyItem.ConfigModifyItemList parameters) {
        this.parameters = parameters;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }
}
