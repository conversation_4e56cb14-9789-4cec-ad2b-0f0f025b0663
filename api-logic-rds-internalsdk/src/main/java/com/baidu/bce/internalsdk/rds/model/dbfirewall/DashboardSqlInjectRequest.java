package com.baidu.bce.internalsdk.rds.model.dbfirewall;

import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;

import javax.validation.constraints.NotNull;

/**
 * Created by liuruisen on 2017/11/9.
 */
public class DashboardSqlInjectRequest extends InstanceIdRequest {

    @NotNull
    private String sqlId;

    public String getSqlId() {
        return sqlId;
    }

    public void setSqlId(String sqlId) {
        this.sqlId = sqlId;
    }
}
