package com.baidu.bce.internalsdk.rds.model.instance;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2015/1/26-10:58.
 */
public class InstanceReplicaDelayMaster {
    private Integer secondsBehindMaster;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("InstanceReplicaDelayMaster{");
        sb.append("secondsBehindMaster=").append(secondsBehindMaster);
        sb.append('}');
        return sb.toString();
    }

    public Integer getSecondsBehindMaster() {
        return secondsBehindMaster;
    }

    public void setSecondsBehindMaster(Integer secondsBehindMaster) {
        this.secondsBehindMaster = secondsBehindMaster;
    }

    public InstanceReplicaDelayMaster withSecondsBehindMaster(Integer secondsBehindMaster) {
        this.secondsBehindMaster = secondsBehindMaster;
        return this;
    }
}
