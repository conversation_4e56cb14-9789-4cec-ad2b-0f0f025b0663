package com.baidu.bce.logic.rds.service.dbfirewall;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInject;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectListRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectListResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.service.model.LogicCommonListRequest;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;


import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 2017/11/13.
 */
public class SqlInjectListGetTest extends DbFireWallServiceTest {

    String filters = "[{\"keyword\":\"2017-01-26T11:05:49Z\",\"keywordType\":\"startTime\",\"subKeywordType\":null},"
            + "{\"keyword\":\"2017-10-26T11:05:49Z\",\"keywordType\":\"endTime\",\"subKeywordType\":null},"
            + "{\"keyword\":\"dbfw\",\"keywordType\":\"dbName\",\"subKeywordType\":null},"
            + "{\"keyword\":\"secdb_new\",\"keywordType\":\"accountName\",\"subKeywordType\":null}]";

    String instanceId = "rdsmxiaozhiwen0";

    @Before
    public void initSqlInjectList() {
        RDSClient rdsClient = mockRdsClient();
        when(rdsClient.sqlInjectList(anyString(), any(SqlInjectListRequest.class))).thenReturn(mockSqlInjectList());
    }

    @Test(expected = BceException.class)
    public void sqlInjectListWhitNullInstanceId() {  // whitout order orderby pageNo pageSize filters
        LogicCommonListRequest request = mockLogicCommonListRequest("accountName", "desc",
                3, 2, filters);
        dbFireWallService.sqlInjectList(null, request);
    }

    @Test
    public void sqlInjectListWhitNullOrder() {  // whitout order orderby pageNo pageSize filters
        LogicCommonListRequest request = mockLogicCommonListRequest("accountName", null,
                1, 2, filters);
        LogicPageResultResponse<SqlInject> response = dbFireWallService.sqlInjectList(instanceId, request);
        Assert.assertEquals(2, response.getResult().size());
    }

    @Test
    public void sqlInjectListWhitNullOrderBy() {
        LogicCommonListRequest request = mockLogicCommonListRequest(null, "desc",
                1, 10, filters);
        LogicPageResultResponse<SqlInject> response = dbFireWallService.sqlInjectList(instanceId, request);
        Assert.assertEquals(4, response.getResult().size());
    }

    @Test
    public void sqlInjectListWhitNullPageNo() {
        LogicCommonListRequest request = mockLogicCommonListRequest("accountName", "desc",
                0, 3, filters);
        LogicPageResultResponse<SqlInject> response = dbFireWallService.sqlInjectList(instanceId, request);
        Assert.assertEquals(4, response.getResult().size());
    }

    @Test
    public void sqlInjectListWhitNullPageSize() {
        LogicCommonListRequest request = mockLogicCommonListRequest("createTime", "desc",
                1, 0, filters);
        LogicPageResultResponse<SqlInject> response = dbFireWallService.sqlInjectList(instanceId, request);
        Assert.assertEquals(4, response.getResult().size());
    }

    @Test
    public void sqlInjectListWhitNullFilters() {
        LogicCommonListRequest request = mockLogicCommonListRequest("dbName", "desc",
                2, 2, null);
        LogicPageResultResponse<SqlInject> response = dbFireWallService.sqlInjectList(instanceId, request);
        Assert.assertEquals(2, response.getResult().size());
    }

    @Test
    public void sqlInjectListWhitNullRequest() {
        LogicCommonListRequest request = null;
        LogicPageResultResponse<SqlInject> response = dbFireWallService.sqlInjectList(instanceId, request);
        Assert.assertEquals(4, response.getResult().size());
    }

    @Test
    public void sqlInjectList() {
        LogicCommonListRequest request = mockLogicCommonListRequest("dbName", "asc",
                3, 2, filters);
        LogicPageResultResponse<SqlInject> response = dbFireWallService.sqlInjectList(instanceId, request);
        Assert.assertEquals(0, response.getResult().size());
    }

    private SqlInjectListResponse mockSqlInjectList() {
        SqlInjectListResponse response = new SqlInjectListResponse();
        SqlInject sqlInject1 = new SqlInject();
        sqlInject1.setSqlId(641);
        sqlInject1.setAppId("rdsmxiaozhiwen0");
        sqlInject1.setUserIp("***********");
        sqlInject1.setUserPort("28899");
        sqlInject1.setAccountName("secdb_new");
        sqlInject1.setDbName("dbfw");
        sqlInject1.setSqlMd5("**********");
        sqlInject1.setSqlFingerprint("SELECT * FROM user");
        sqlInject1.setCreateTime("2017-07-26T11:05:49Z");


        SqlInject sqlInject2 = new SqlInject();
        sqlInject2.setSqlId(644);
        sqlInject2.setAppId("rdsmxiaozhiwen0");
        sqlInject2.setUserIp("***********");
        sqlInject2.setUserPort("28899");
        sqlInject2.setAccountName("secdb_new");
        sqlInject2.setDbName("dbfw");
        sqlInject2.setSqlMd5("**********");
        sqlInject2.setSqlFingerprint("SELECT * FROM user");
        sqlInject2.setCreateTime("2017-06-26T11:05:49Z");


        SqlInject sqlInject3 = new SqlInject();
        sqlInject3.setSqlId(644);
        sqlInject3.setAppId("rdsmxiaozhiwen1");
        sqlInject3.setUserIp("***********");
        sqlInject3.setUserPort("28899");
        sqlInject3.setAccountName("secdb_new2");
        sqlInject3.setDbName("dbfw1");
        sqlInject3.setSqlMd5("**********");
        sqlInject3.setSqlFingerprint("SELECT * FROM user");
        sqlInject3.setCreateTime("2017-07-26T12:05:49Z");


        SqlInject sqlInject4 = new SqlInject();
        sqlInject4.setSqlId(644);
        sqlInject4.setAppId("rdsmxiaozhiwen2");
        sqlInject4.setUserIp("***********");
        sqlInject4.setUserPort("28899");
        sqlInject4.setAccountName("secdb_new1");
        sqlInject4.setDbName("dbfw2");
        sqlInject4.setSqlMd5("**********");
        sqlInject4.setSqlFingerprint("SELECT * FROM user");
        sqlInject4.setCreateTime("2017-09-26T12:05:49Z");


        response.getSqlInjectList().add(sqlInject1);
        response.getSqlInjectList().add(sqlInject2);
        response.getSqlInjectList().add(sqlInject3);
        response.getSqlInjectList().add(sqlInject4);

        return response;
    }
}
