package com.baidu.bce.logic.rds.service.dbfirewall;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwState;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwStateListResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.service.model.LogicCommonListRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Mockito.when;

/**
 * Created by liuruisen on 2017/11/13.
 */
public class DbfwStateListGetTest extends DbFireWallServiceTest {

    @Before
    public void mockDbfwListResponse() {
        RDSClient rdsClient = mockRdsClient();
        when(rdsClient.dbfwStateList()).thenReturn(mockDbfwStateListResponse());
    }

    @Test
    public void dbfwListWithNullOrderBy() {
        LogicCommonListRequest request = mockLogicCommonListRequest(null, "desc",
                1, 2, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(2, response.getResult().size());
    }

    @Test
    public void dbfwListWithNullOrder() {
        LogicCommonListRequest request = mockLogicCommonListRequest("id", null,
                2, 2, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(2, response.getResult().size());
    }

    @Test
    public void dbfwListWithNullPageNo() {
        LogicCommonListRequest request = mockLogicCommonListRequest("id", "desc",
                0, 2, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(6, response.getResult().size());
    }

    @Test
    public void dbfwListWithNullPageSize() {
        LogicCommonListRequest request = mockLogicCommonListRequest("id", "asc",
                2, 0, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(6, response.getResult().size());
    }

    @Test
    public void dbfwListWithoutOrderAndPageAndFiltersTest() {
        LogicCommonListRequest request = mockLogicCommonListRequest(null, null,
                0, 0, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(6, response.getResult().size());
    }

    @Test
    public void dbfwListOrderByIdAscWithoutFiltersTest() {
        LogicCommonListRequest request = mockLogicCommonListRequest("id", "asc",
                0, 0, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(6, response.getResult().size());
    }

    @Test
    public void dbfwListOrderbyIdDescWithoutFiltersTest() {
        LogicCommonListRequest request = mockLogicCommonListRequest("id", "desc",
                0, 0, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(6, response.getResult().size());
    }

    @Test
    public void dbfwListOrderbyAppidAscWithoutFiltersTest() {
        LogicCommonListRequest request = mockLogicCommonListRequest("appId", "asc",
                0, 0, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(6, response.getResult().size());
    }

    @Test
    public void dbfwListOrderbyAppidDescWithoutFiltersTest() {
        LogicCommonListRequest request = mockLogicCommonListRequest("appId", "desc",
                0, 0, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(6, response.getResult().size());
    }

    @Test
    public void dbfwListOrderbyTimeDescWithoutFiltersTest() {
        LogicCommonListRequest request = mockLogicCommonListRequest("updateTime", "desc",
                1, 3, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(3, response.getResult().size());
    }

    @Test
    public void dbfwListOrderbyTimeAscWithoutFiltersTest() {
        LogicCommonListRequest request = mockLogicCommonListRequest("updateTime", "asc",
                2, 3, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(3, response.getResult().size());
    }

    @Test
    public void dbfwListOrderByTimeDescWithoutFiltersPageTest() {
        LogicCommonListRequest request = mockLogicCommonListRequest("updateTime", "desc",
                2, 4, null);
        LogicPageResultResponse<DbfwState> response = dbFireWallService.dbfwList(request);
        Assert.assertEquals(2, response.getResult().size());
    }

    private DbfwStateListResponse mockDbfwStateListResponse() {
        DbfwStateListResponse response = new DbfwStateListResponse();

        DbfwState dbfwState1 = new DbfwState();
        dbfwState1.setId(6);
        dbfwState1.setAppId("rdsmxiaozhiwen0");
        dbfwState1.setDbfwState(1);
        dbfwState1.setUpdateTime("2017-05-22T11:28:36Z");

        DbfwState dbfwState2 = new DbfwState();
        dbfwState2.setId((-1));
        dbfwState2.setAppId("rdspr1uyvz73fv6");
        dbfwState2.setDbfwState(0);
        dbfwState2.setUpdateTime("0000-00-00T00:00:00Z");

        DbfwState dbfwState3 = new DbfwState();
        dbfwState3.setId(1);
        dbfwState3.setAppId("rdspr1uyvz73fv1");
        dbfwState3.setDbfwState(2);
        dbfwState3.setUpdateTime("2017-05-22T11:29:36Z");

        DbfwState dbfwState4 = new DbfwState();
        dbfwState4.setId(3);
        dbfwState4.setAppId("rdspr1uyvz73fv4");
        dbfwState4.setDbfwState(2);
        dbfwState4.setUpdateTime("2017-05-21T11:29:36Z");

        DbfwState dbfwState5 = new DbfwState();
        dbfwState5.setId(4);
        dbfwState5.setAppId("rdspr1uyvz73fv5");
        dbfwState5.setDbfwState(2);
        dbfwState5.setUpdateTime("2017-10-22T11:29:36Z");

        DbfwState dbfwState6 = new DbfwState();
        dbfwState6.setId(2);
        dbfwState6.setAppId("rdspr1uyvz73fv6");
        dbfwState6.setDbfwState(2);
        dbfwState6.setUpdateTime("2017-09-22T11:29:36Z");

        response.getDbfwStates().add(dbfwState1);
        response.getDbfwStates().add(dbfwState2);
        response.getDbfwStates().add(dbfwState3);
        response.getDbfwStates().add(dbfwState4);
        response.getDbfwStates().add(dbfwState5);
        response.getDbfwStates().add(dbfwState6);
        return response;
    }
}
