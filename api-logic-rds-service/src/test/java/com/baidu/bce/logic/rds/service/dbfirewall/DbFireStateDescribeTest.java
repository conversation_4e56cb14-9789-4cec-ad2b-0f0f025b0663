package com.baidu.bce.logic.rds.service.dbfirewall;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwState;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwStateGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Created by liuruisen on 2017/11/13.
 */
public class DbFireStateDescribeTest extends DbFireWallServiceTest {

    @Before
    public void dbfwStateDescribeInit() {
        RDSClient rdsClient = mockRdsClient();
        when(rdsClient.dbfwStateDescribe(anyString())).thenReturn(mockDbfwState());
    }
    @Test(expected = BceException.class)
    public void dbfwStateDescribeWhitNullInstanceId() {
        dbFireWallService.dbfwState(null);
    }

    @Test(expected = BceException.class)
    public void dbfwStateDescribeWhitMasterInstanceId() {
        String instanceId = "rdsmxiaozhiwen1";
        RDSClient2 rdsClient2 = mockRdsClient2();
        when(rdsClient2.instanceDescribe(instanceId)).thenReturn(mockInstance(instanceId,
                RDSConstant.RDS_ENGINE_MYSQL, "master"));
        dbFireWallService.dbfwState(instanceId);
    }

    @Test(expected = BceException.class)
    public void dbfwStateDescribeWhitSqlServerInstanceId() {
        String instanceId = "rdsmxiaozhiwen3";
        RDSClient2 rdsClient2 = mockRdsClient2();
        when(rdsClient2.instanceDescribe(instanceId)).thenReturn(mockInstance(instanceId,
                RDSConstant.RDS_ENGINE_SQLSERVER, RDSConstant.RDS_ENGINE_PROXY));
        dbFireWallService.dbfwState(instanceId);
    }

    @Test
    public void dbfwStateDescribe() {
        String instanceId = "rdsmxiaozhiwen0";
        RDSClient2 rdsClient2 = mockRdsClient2();
        when(rdsClient2.instanceDescribe(instanceId)).thenReturn(mockInstance(instanceId,
                RDSConstant.RDS_ENGINE_MYSQL, RDSConstant.RDS_ENGINE_PROXY));
        dbFireWallService.dbfwState(instanceId);
    }

    private DbfwStateGetResponse mockDbfwState() {
        DbfwStateGetResponse response = new DbfwStateGetResponse();
        DbfwState dbfwState1 = new DbfwState();
        dbfwState1.setId(6);
        dbfwState1.setAppId("rdsmxiaozhiwen0");
        dbfwState1.setDbfwState(1);
        dbfwState1.setUpdateTime("2017-05-22T11:28:36Z");
        response.setDbfwState(dbfwState1);
        return response;
    }

    private InstanceGetResponse mockInstance(String instanceId, String engine, String instanceType) {
        InstanceGetResponse response = new InstanceGetResponse();
        Instance instance = new Instance();
        instance.setInstanceId(instanceId);
        instance.setEngine(engine);
        instance.setInstanceType(instanceType);
        response.setInstance(instance);
        return response;
    }

}
