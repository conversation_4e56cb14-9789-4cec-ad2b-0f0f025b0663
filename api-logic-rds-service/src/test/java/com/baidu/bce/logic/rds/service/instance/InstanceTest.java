package com.baidu.bce.logic.rds.service.instance;

import com.baidu.bce.externalsdk.logical.network.vpc.ExternalVpcClient;
import com.baidu.bce.externalsdk.logical.network.vpc.model.SimpleVpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.VpcIdsRequest;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.RdsOrderService;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import com.baidu.bce.logic.rds.service.model.order.RdsCreateOrderRequestVo;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.plat.servicecatalog.ServiceCatalogOrderClient;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.OrdersResult;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Created by luping03 on 18/2/26.
 */
public class InstanceTest {
    @Mock
    LogicRdsClientFactory logicRdsClientFactory;

    @Mock
    InstanceService instanceService;

    @InjectMocks
    RdsOrderService rdsOrderService;

    @Mock
    RegionConfiguration regionConfiguration;

    @InjectMocks
    @Spy
    OthersService othersService;

    @Before
    public void setUp() throws Exception {
//        instanceService = new InstanceService();
        MockitoAnnotations.initMocks(this);

        ExternalVpcClient vpcClient = mockVpcClient();
        when(vpcClient.get(any(VpcIdsRequest.class))).thenReturn(mockSimpleVpcVoMap());
        when(vpcClient.getAndCreateDefaultVpc()).thenReturn(mockDefaultVpcVo());

//        when(logicRdsClientFactory.isRoot()).thenReturn(true);
        when(regionConfiguration.getCurrentRegion()).thenReturn("bj");
        mockServiceCatalogOrderClient();
    }

    @Test
    public void createTest() throws Exception {
        InstanceCreateModel.DashCreateInstance createInstance = new InstanceCreateModel.DashCreateInstance();
        InstanceCreateModel instanceCreateModel = new InstanceCreateModel();
        instanceCreateModel.setInstance(createInstance);
        BaseCreateOrderRequestVo.Item<InstanceCreateModel> objectItem = new BaseCreateOrderRequestVo.Item<>();
        objectItem.setConfig(instanceCreateModel);
        BaseCreateOrderRequestVo<InstanceCreateModel> createOrderRequestVo = new BaseCreateOrderRequestVo<>();
        createOrderRequestVo.setItems(Collections.singletonList(objectItem));

        instanceCreateModel.setNumber(1);
        instanceCreateModel.setProductType("Postpaid");
        createInstance.setEngine("MySQL");
        createInstance.setEngineVersion("5.6");
        createInstance.setAllocatedMemoryInGB(1);
        createInstance.setAllocatedStorageInGB(5);
        createInstance.setCpuCount(1);

//        instanceService.createInstances(createOrderRequestVo, "api");
    }

    @Test
    public void resizeTest() throws Exception {
        PriceDiffModel instanceResizeModel = new PriceDiffModel();
        RdsCreateOrderRequestVo.Item<PriceDiffModel> objectItem = new RdsCreateOrderRequestVo.Item<>();
        objectItem.setConfig(instanceResizeModel);
        RdsCreateOrderRequestVo<PriceDiffModel> resizeOrderRequestVo = new RdsCreateOrderRequestVo<>();
        resizeOrderRequestVo.setItems(Collections.singletonList(objectItem));

        instanceResizeModel.setInstanceId("rds12345678");
        instanceResizeModel.setIsEnhanced(false);
        instanceResizeModel.setOldFlavor(true);
        instanceResizeModel.setAllocatedMemoryInMB(512);
        instanceResizeModel.setAllocatedMemoryInGB(0);
        instanceResizeModel.setAllocatedStorageInGB(50);
        instanceResizeModel.setCpuCount(1);

//        InstanceService instanceService = mock(InstanceService.class);
        when(instanceService.getInstanceExtension(anyString())).thenReturn(mockInstanceDetail());
        when(instanceService.detail(anyString(), anyString())).thenReturn(mockInstanceDetail());
        rdsOrderService.createResizeOrder(resizeOrderRequestVo);
    }

    public InstanceExtension mockInstanceDetail() {
        InstanceExtension instanceExtension = new InstanceExtension();
        instanceExtension.setInstanceType("");
        instanceExtension.setEngineVersion("5.6");
        instanceExtension.setApplicationType("");
        instanceExtension.setProductType("Prepay");
        instanceExtension.setAllocatedMemoryInMB(512);
        instanceExtension.setAllocatedStorageInGB(5);
        instanceExtension.setCpuCount(1);
        return instanceExtension;
    }

    public RDSClient mockRdsClient() {
        RDSClient rdsClient = mock(RDSClient.class);
        when(logicRdsClientFactory.createRdsClient()).thenReturn(rdsClient);
        return rdsClient;
    }

    public RDSClient2 mockRdsClient2() {
        RDSClient2 rdsClient2 = mock(RDSClient2.class);
        when(logicRdsClientFactory.createRdsClient2()).thenReturn(rdsClient2);
        return rdsClient2;
    }

    public ExternalVpcClient mockVpcClient() {
        ExternalVpcClient vpcClient = mock(ExternalVpcClient.class);
        when(logicRdsClientFactory.createVpcClient()).thenReturn(vpcClient);
        return vpcClient;
    }

    public IAMClient mockIAMClient() {
        IAMClient iamClient = mock(IAMClient.class);
        when(logicRdsClientFactory.createIamClient()).thenReturn(iamClient);
        return iamClient;
    }

    public ServiceCatalogOrderClient mockServiceCatalogOrderClient() {
        ServiceCatalogOrderClient orderClient = mock(ServiceCatalogOrderClient.class);
        when(logicRdsClientFactory.createServiceCatalogOrderClient()).thenReturn(orderClient);
        when(orderClient.batchCreateOrder(any(CreateOrderRequest.class))).thenReturn(new OrdersResult());
        return orderClient;
    }

    public Map<String, SimpleVpcVo> mockSimpleVpcVoMap() {
        Map<String, SimpleVpcVo> simpleVpcVoMap = new HashMap<>();

        SimpleVpcVo vpcVo1 = new SimpleVpcVo();
        vpcVo1.setVpcId("11111-11111-11111");
        vpcVo1.setShortId("v-test1");
        simpleVpcVoMap.put(vpcVo1.getVpcId(), vpcVo1);

        SimpleVpcVo vpcVo2 = new SimpleVpcVo();
        vpcVo2.setVpcId("22222-22222-22222");
        vpcVo2.setShortId("v-test2");
        simpleVpcVoMap.put(vpcVo1.getVpcId(), vpcVo2);

        return simpleVpcVoMap;
    }

    public VpcVo mockDefaultVpcVo() {
        VpcVo vpcVo = new VpcVo();
        vpcVo.setVpcId("11111-11111-11111");
        vpcVo.setShortId("v-test1");

        return vpcVo;
    }
}
