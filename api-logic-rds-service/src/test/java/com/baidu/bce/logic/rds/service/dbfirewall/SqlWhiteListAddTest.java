package com.baidu.bce.logic.rds.service.dbfirewall;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteListCreateRequest;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doNothing;

/**
 * Created by liu<PERSON>isen on 2017/11/13.
 */
public class SqlWhiteListAddTest extends DbFireWallServiceTest {

    @Before
    public void initSqlWhiteListAdd() {
        RDSClient rdsClient = mockRdsClient();
        doNothing().when(rdsClient).sqlWhiteListCreate(anyString(), any(SqlWhiteListCreateRequest.class));
    }

    @Test(expected = BceException.class)
    public void sqlWhiteListAddWhitNullInstanceId() {
        dbFireWallService.sqlWhiteListAdd(null, any(SqlWhiteListCreateRequest.class));
    }

    @Test
    public void sqlWhiteListAdd() {
        SqlWhiteListCreateRequest request = new SqlWhiteListCreateRequest();
        request.setSqlFingerprint("select * from user");
        request.setSqlMd5("12314");
        request.setDbName("dbfw");
        dbFireWallService.sqlWhiteListAdd("rdsmxiaozhiwen0", request);
    }

}
