package com.baidu.bce.logic.rds.service.dbfirewall;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doNothing;

/**
 * Created by <PERSON><PERSON><PERSON>isen on 2017/11/13.
 */
public class SqlWhiteListDeleteTest extends DbFireWallServiceTest {

    @Before
    public void initSqlWhiteListDelete() {
        RDSClient rdsClient = mockRdsClient();
        doNothing().when(rdsClient).sqlWhiteListDelete(anyString(), anyString());
    }

    @Test(expected = BceException.class)
    public void sqlWhiteListDeleteWithNoInstanceId() {
        dbFireWallService.sqlWhiteListDelete(null, "2363445");
    }

    @Test(expected = BceException.class)
    public void sqlWhiteListDeleteWhithNoSqlMd5() {
        dbFireWallService.sqlWhiteListDelete("rdsmxiaozhiwen0", null);
    }

    @Test
    public void sqlWhiteListDelete() {
        dbFireWallService.sqlWhiteListDelete("rdsmxiaozhiwen0", "2363445");
    }
}
