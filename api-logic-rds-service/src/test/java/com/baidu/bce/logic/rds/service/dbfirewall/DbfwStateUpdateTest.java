package com.baidu.bce.logic.rds.service.dbfirewall;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwModifyStateRequest;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.junit.Test;

import static org.mockito.Mockito.doNothing;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/13.
 */
public class DbfwStateUpdateTest extends DbFireWallServiceTest {

    @Test(expected = BceException.class)
    public void updateStateIllegalState() {
        String instanceId = "rdsmxiaozhiwen0";
        DbfwModifyStateRequest request = new DbfwModifyStateRequest();
        request.setDbfwState(5);  // illegal state
        dbFireWallService.updateState(instanceId, request);
    }

    @Test(expected = BceException.class)
    public void updateStateWithNullInstanceId() {
        DbfwModifyStateRequest request = new DbfwModifyStateRequest();
        request.setDbfwState(2);  // illegal state
        dbFireWallService.updateState(null, request);
    }

    @Test
    public void updateState() {
        String instanceId = "rdsmxiaozhiwen0";
        DbfwModifyStateRequest request = new DbfwModifyStateRequest();
        request.setDbfwState(2);  // legal state
        RDSClient rdsClient = mockRdsClient();
        doNothing().when(rdsClient).dbfwModifyState(instanceId, request);
        dbFireWallService.updateState(instanceId, request);
    }
}
