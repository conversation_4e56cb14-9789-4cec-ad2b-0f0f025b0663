package com.baidu.bce.logic.rds.service.dbfirewall;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteDetail;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteListResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.service.model.LogicCommonListRequest;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;


import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Created by liuruisen on 2017/11/13.
 */
public class SqlWhiteListGetTest extends DbFireWallServiceTest {

    String instanceId = "rdsmxiaozhiwen0";

    String sqlIdfilter = "[{\"keyword\":\"3\",\"keywordType\":\"sqlId\",\"subKeywordType\":null}]";

    String sqlFingerprintFilter =

            "[{\"keyword\":\"SELECT * FROM user\",\"keywordType\":\"sqlFingerprint\",\"subKeywordType\":null}]";

    @Before
    public void initSqlWhiteList() {
        RDSClient rdsClient = mockRdsClient();
        when(rdsClient.sqlWhiteList(anyString())).thenReturn(mockSqlWhiteList());
    }

    @Test(expected = BceException.class)
    public void sqlWhiteListPageWhitNullInstanceId() {
        LogicCommonListRequest request = mockLogicCommonListRequest("sqlId", "desc",
                1, 2, sqlIdfilter);
        dbFireWallService.sqlWhiteList(null, request);
    }

    @Test
    public void sqlWhiteListPageWhitNullOrderBy() {
        LogicCommonListRequest request = mockLogicCommonListRequest(null, "desc",
                1, 2, sqlIdfilter);
        dbFireWallService.sqlWhiteList(instanceId, request);
    }

    @Test
    public void sqlWhiteListPageWhitNullOrder() {
        LogicCommonListRequest request = mockLogicCommonListRequest("sqlId", null,
                1, 2, sqlIdfilter);
        dbFireWallService.sqlWhiteList(instanceId, request);
    }

    @Test
    public void sqlWhiteListPageWhitNullPageNo() {
        LogicCommonListRequest request = mockLogicCommonListRequest("sqlId", "asc",
                0, 2, sqlIdfilter);
        dbFireWallService.sqlWhiteList(instanceId, request);
    }

    @Test
    public void sqlWhiteListPageWhitNullPageSize() {
        LogicCommonListRequest request = mockLogicCommonListRequest("sqlId", "desc",
                1, 0, sqlFingerprintFilter);
        LogicPageResultResponse<SqlWhiteDetail> response = dbFireWallService.sqlWhiteList(instanceId, request);
        Assert.assertEquals(2, response.getResult().size());
    }

    @Test
    public void sqlWhiteListPageWhitNullFilters() {
        LogicCommonListRequest request = mockLogicCommonListRequest("sqlId", "desc",
                2, 2, null);
        dbFireWallService.sqlWhiteList(instanceId, request);
    }

    @Test
    public void sqlWhiteListPageWhitNullRequest() {
        LogicCommonListRequest request = null;
        dbFireWallService.sqlWhiteList(instanceId, request);
    }

    @Test
    public void sqlWhiteListPageWhitoutFiltersAndOrder() {
        LogicCommonListRequest request = mockLogicCommonListRequest(null, null,
                1, 2, null);
        dbFireWallService.sqlWhiteList(instanceId, request);
    }

    @Test
    public void sqlWhiteListPageOrderBySqlIdAsc() {
        LogicCommonListRequest request = mockLogicCommonListRequest("sqlId", "asc",
                1, 2, null);
        dbFireWallService.sqlWhiteList(instanceId, request);
    }

    @Test
    public void sqlWhiteListPageOrderBySqlIdDesc() {
        LogicCommonListRequest request = mockLogicCommonListRequest("sqlId", "desc",
                1, 2, null);
        dbFireWallService.sqlWhiteList(instanceId, request);
    }

    @Test
    public void sqlWhiteListPageFilterSqlId() {
        LogicCommonListRequest request = mockLogicCommonListRequest("sqlId", "asc",
                1, 2, sqlFingerprintFilter);
        dbFireWallService.sqlWhiteList(instanceId, request);
    }

    @Test
    public void sqlWhiteListPageFilterSqlFingerprint() {
        LogicCommonListRequest request = mockLogicCommonListRequest("sqlId", "asc",
                1, 2, sqlFingerprintFilter);
        dbFireWallService.sqlWhiteList(instanceId, request);
    }

    private SqlWhiteListResponse mockSqlWhiteList() {
        SqlWhiteListResponse response = new SqlWhiteListResponse();
        SqlWhiteDetail sqlWhiteDetail = new SqlWhiteDetail();
        sqlWhiteDetail.setSqlId(2);
        sqlWhiteDetail.setAppId("rdsmxiaozhiwen0");
        sqlWhiteDetail.setDbName("dbfw");
        sqlWhiteDetail.setSqlMd5("2574330544");
        sqlWhiteDetail.setSqlFingerprint("SELECT * FROM user WHERE 1=1");
        sqlWhiteDetail.setCreateTime("2017-05-23T11:28:36Z");


        SqlWhiteDetail sqlWhiteDetail1 = new SqlWhiteDetail();
        sqlWhiteDetail1.setSqlId(3);
        sqlWhiteDetail1.setAppId("rdsmxiaozhiwen0");
        sqlWhiteDetail1.setDbName("dbfw2");
        sqlWhiteDetail1.setSqlMd5("2574330544");
        sqlWhiteDetail1.setSqlFingerprint("SELECT * FROM user WHERE 1=2");
        sqlWhiteDetail1.setCreateTime("2017-05-23T11:20:36Z");


        SqlWhiteDetail sqlWhiteDetail2 = new SqlWhiteDetail();
        sqlWhiteDetail2.setSqlId(1);
        sqlWhiteDetail2.setAppId("rdsmxiaozhiwen0");
        sqlWhiteDetail2.setDbName("dbfw");
        sqlWhiteDetail2.setSqlMd5("2574330544");
        sqlWhiteDetail2.setSqlFingerprint("SELECT * FROM user WHERE 1=3");
        sqlWhiteDetail2.setCreateTime("2017-05-21T11:28:36Z");

        SqlWhiteDetail sqlWhiteDetail3 = new SqlWhiteDetail();
        sqlWhiteDetail3.setSqlId(4);
        sqlWhiteDetail3.setAppId("rdsmxiaozhiwen0");
        sqlWhiteDetail3.setDbName("dbfw2");
        sqlWhiteDetail3.setSqlMd5("2574330544");
        sqlWhiteDetail3.setSqlFingerprint("SELECT * FROM user WHERE 1=1");
        sqlWhiteDetail3.setCreateTime("2017-07-23T11:28:36Z");

        response.getSqlWhiteList().add(sqlWhiteDetail);
        response.getSqlWhiteList().add(sqlWhiteDetail1);
        response.getSqlWhiteList().add(sqlWhiteDetail2);
        response.getSqlWhiteList().add(sqlWhiteDetail3);

        return response;
    }


}
