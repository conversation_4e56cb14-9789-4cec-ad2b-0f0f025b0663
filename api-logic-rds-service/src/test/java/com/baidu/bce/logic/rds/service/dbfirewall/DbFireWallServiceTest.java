package com.baidu.bce.logic.rds.service.dbfirewall;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.logic.rds.service.DbFireWallService;
import com.baidu.bce.logic.rds.service.model.LogicCommonListRequest;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;


import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Created by liu<PERSON>isen on 2017/11/13.
 */
public class DbFireWallServiceTest {

    @Mock
    LogicRdsClientFactory logicRdsClientFactory;

    @InjectMocks
    DbFireWallService dbFireWallService;

    @Before
    public void setUp() throws Exception {
        dbFireWallService = new DbFireWallService();
        MockitoAnnotations.initMocks(this);
    }

    public RDSClient mockRdsClient() {
        RDSClient rdsClient = mock(RDSClient.class);
        when(logicRdsClientFactory.createRdsClient()).thenReturn(rdsClient);
        return rdsClient;
    }

    public RDSClient2 mockRdsClient2() {
        RDSClient2 rdsClient2 = mock(RDSClient2.class);
        when(logicRdsClientFactory.createRdsClient2()).thenReturn(rdsClient2);
        return rdsClient2;
    }

    public LogicCommonListRequest mockLogicCommonListRequest(String orderBy, String order, int pageNo, int pageSize,
                                                             String filters) {
        LogicCommonListRequest request = new LogicCommonListRequest();
        request.setOrder(order);
        request.setOrderBy(orderBy);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setFilters(filters);
        return request;
    }

    public String convertToStr(Object obj) {
        String str = "";
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            str = objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return str;
    }

}
