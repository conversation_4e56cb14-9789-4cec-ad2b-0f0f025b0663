package com.baidu.bce.logic.rds.service.dbfirewall;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectDetail;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectGetResponse;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Created by liuruisen on 2017/11/13.
 */
public class SqlInjectDetailTest extends DbFireWallServiceTest {

    @Before
    public void initSsqlInjectDetail() {
        RDSClient rdsClient = mockRdsClient();
        when(rdsClient.sqlInjectDescribe(anyString(), anyString())).thenReturn(mockSqlInejctDetail());
    }

    @Test(expected = BceException.class)
    public void sqlInjectDetialWithNullInstanceId() {
        String instanceId = null;
        String sqlId = "641";
        dbFireWallService.sqlInjectDetail(instanceId, sqlId);
    }

    @Test(expected = BceException.class)
    public void sqlInjectDetialWithNullSqlId() {
        String instanceId = "rdsmxiaozhiwen0";
        String sqlId = null;
        dbFireWallService.sqlInjectDetail(instanceId, sqlId);
    }

    @Test
    public void sqlInjectDetial() {
        String instanceId = "rdsmxiaozhiwen0";
        String sqlId = "641";
        SqlInjectGetResponse response = dbFireWallService.sqlInjectDetail(instanceId, sqlId);
        Assert.assertTrue(response.equals(mockSqlInejctDetail()));
    }

    private SqlInjectGetResponse mockSqlInejctDetail() {
        SqlInjectGetResponse response = new SqlInjectGetResponse();
        SqlInjectDetail detail = new SqlInjectDetail();
        detail.setSqlId(641);
        detail.setAppId("rdsmxiaozhiwen0");
        detail.setUserIp("***********");
        detail.setUserPort("28899");
        detail.setAccountName("secdb_new");
        detail.setDbName("dbfw");
        detail.setSqlMd5("**********");
        detail.setSqlFingerprint("SELECT * FROM user");
        detail.setCreateTime("2017-07-26T11:05:49Z");
        detail.setDbproxyIp("*******");
        detail.setDbproxyPort("122213");
        detail.setSecLevel("FORBID");
        response.setSqlInjectDetail(detail);
        return response;
    }
}
