package com.baidu.bce.logic.rds.service.model.pricing;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.logic.rds.service.idmapper.IdMapper;
import com.baidu.bce.logic.rds.service.model.instance.InstanceResizeRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by luping03 on 17/11/8.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PriceDiffModel extends InstanceResizeRequest {
    @NotNull
    @IdPermission
    @IdMapper
    private String instanceId;

    private String category;

    private Boolean isEnhanced; // 上海金融专区三节点增强版标识

    private List<PriceDiffModel> readReplicas;

    @ApiModelProperty("一键配置批量变配的参数规格")
    private boolean isBatchResize;

    @ApiModelProperty("一键开启是否进行子网前置检查，供内部自动扩容使用")
    private boolean needPrecheck = true;

//    @NotNull
//    private String productType;


    public boolean getNeedPrecheck() {
        return needPrecheck;
    }

    public void setNeedPrecheck(boolean needPrecheck) {
        this.needPrecheck = needPrecheck;
    }

    public boolean getIsBatchResize() {
        return isBatchResize;
    }

    public void setIsBatchResize(boolean batchResize) {
        isBatchResize = batchResize;
    }

    public List<PriceDiffModel> getReadReplicas() {
        return readReplicas;
    }

    public void setReadReplicas(List<PriceDiffModel> readReplicas) {
        this.readReplicas = readReplicas;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Boolean getIsEnhanced() {
        return isEnhanced;
    }

    public void setIsEnhanced(Boolean enhanced) {
        isEnhanced = enhanced;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    //    public String getProductType() {
//        return productType;
//    }
//
//    public void setProductType(String productType) {
//        this.productType = productType;
//    }
}
