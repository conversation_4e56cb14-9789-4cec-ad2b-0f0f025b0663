package com.baidu.bce.logic.rds.service.interceptor;

public class RdsRequestParam {

    private static ThreadLocal<String> threadSecurityToken = new ThreadLocal<>();

    public static String getThreadSecurityToken() {
        return threadSecurityToken.get();
    }
    public static void setThreadSecurityToken(String securityToken) {
        threadSecurityToken.set(securityToken);
    }
    public static void removeThreadSecurityToken() {
        threadSecurityToken.remove();
    }
}
