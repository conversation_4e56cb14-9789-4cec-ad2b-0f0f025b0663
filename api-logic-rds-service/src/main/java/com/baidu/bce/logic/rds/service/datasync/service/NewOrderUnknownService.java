package com.baidu.bce.logic.rds.service.datasync.service;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.OrderNeedToSyncPO;
import com.baidu.bce.logic.rds.service.constant.RdsInstanceStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * Created by luping03 on 18/1/9.
 */
@Service
public class NewOrderUnknownService extends OrderSyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(NewOrderUnknownService.class);

    @Override
    public int syncData(OrderNeedToSyncPO orderSyncPO) {
        LOGGER.debug(LOG_PREFIX + "synchronize unknown rds,, order id is {}", orderSyncPO.getOrderUuid());
        List<InstancePO> unknownInstanceList = instanceDao
                .getInstanceListByOrderUuid(orderSyncPO.getOrderUuid(), orderSyncPO.getUserId());
        if (unknownInstanceList == null || unknownInstanceList.isEmpty()) {
            return 0;
        }
        OrderClient orderClient = clientFactory.getOrderClient();
        ResourceClient resourceClient = clientFactory.getResourceClient();

        try {
            String orderId = orderSyncPO.getOrderUuid();
            if (StringUtils.isEmpty(orderId)) {
                return 0;
            }

            Order instanceOrder;
            try {
                instanceOrder = orderClient.get(orderId);
                if (instanceOrder == null) {
                    LOGGER.warn(LOG_PREFIX + "Query order return null, order id is {}", orderId);
                    return 0;
                }
            } catch (BceInternalResponseException e) {
                if ("OrderNotFoundException".equalsIgnoreCase(e.getCode())) {
                    LOGGER.warn(LOG_PREFIX + "Query order catch OrderNotFoundException, order id is {}", orderId);
//                    instanceDao.deleteCreateFailedInstance(orderId, orderSyncPO.getUserId());
//                    orderNeedToSyncService.updateOrderStatus(orderSyncPO.withOrderStatus("OrderNotFoundException"));
//                    return 1;
                }
                LOGGER.warn(LOG_PREFIX + "Query order catch BceInternalResponseException, order id is {}", orderId);
                return 0;
            } catch (Exception ex) {
                LOGGER.warn(LOG_PREFIX + "Query order catch exception, order id is {}", orderId);
                return 0;
            }

            OrderStatus orderStatus = instanceOrder.getStatus();
            if (orderStatus == OrderStatus.READY_FOR_CREATE || orderStatus == OrderStatus.CREATING) {
                instanceDao.batchUpdateInstanceStatusByOrderId(orderId,
                        RdsInstanceStatus.CREATING.getValue(), orderSyncPO.getUserId());
                orderNeedToSyncService
                        .updateOrderStatus(orderSyncPO.withOrderStatus(RdsInstanceStatus.CREATING.getValue()));
            }

            // 创建失败，删除本地资源：将所有订单id为orderId的资源都删除
            if (orderStatus == OrderStatus.CREATE_FAILED || orderStatus == OrderStatus.REFUND_SUCC
                    || orderStatus == OrderStatus.REFUND_FAILED || orderStatus == OrderStatus.EXPIRED
                    || orderStatus == OrderStatus.CANCELLED) {
                instanceDao.deleteCreateFailedInstance(orderId, orderSyncPO.getUserId());
                orderNeedToSyncService.updateOrderStatus(orderSyncPO.withOrderStatus(orderStatus.name()));
            }
            if (orderStatus == OrderStatus.CREATED) {
                try {
                    updateInstance(instanceOrder, resourceClient, unknownInstanceList);
                    orderNeedToSyncService.updateOrderStatus(orderSyncPO.withOrderStatus(orderStatus.name()));
                } catch (Exception e) {
                    LOGGER.error(LOG_PREFIX + "sync in creating rds failed, order id is {}, exception is {}",
                            instanceOrder.getUuid(), e);
                    return 0;
                }
            }
        } catch (Exception e) {
            LOGGER.error("synchronize unknown rds failed, exception is {}", e);
            return 0;
        }
        return 1;
    }
}
