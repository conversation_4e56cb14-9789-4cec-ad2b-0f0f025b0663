package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogDateTime;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogListResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.DashboardBinlogListRequest;
import com.baidu.bce.internalsdk.rds.model.binlog.OpenapiBinlogListResponse;
import com.baidu.bce.internalsdk.rds.model.binlog.OpenapiBinlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotListWithTimeResponse;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by luping03 on 17/11/3.
 */
@Service
public class BinlogService {

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private InstanceService instanceService;

    public BinlogListResponse list(String instanceId, String datetime) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClientByInstanceId(instanceId).binlogList(instanceId, datetime);
    }

    /**
     * 日志备份列表方法重载
     * @param request
     * @return
     */
    public BinlogListResponse list(DashboardBinlogListRequest request) {
        if (BasisUtils.isShortId(request.getInstanceId())) {
            request.setInstanceId(instanceService.findInsntaceUUidByShortId(request.getInstanceId()));
        }
        return clientFactory.createRdsClientByInstanceId(request.getInstanceId()).binlogList(request);
    }

    public BinlogGetResponse detail(String instanceId, String logId, Integer downloadValidTimeInSec) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClient().binlogGet(instanceId, logId, downloadValidTimeInSec);
    }

    public OpenapiBinlogListResponse list2(String instanceId, String datetime) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClientByInstanceId(instanceId).binlogList2(instanceId, datetime);
    }

    public OpenapiBinlogGetResponse detail2(String instanceId, String logId, Integer downloadValidTimeInSec) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClient().binlogGet2(instanceId, logId, downloadValidTimeInSec);
    }

    public void check(String instanceId, BinlogDateTime dateTime) {
        RDSClient2 client2 = clientFactory.createRdsClient2();
        SnapshotListWithTimeResponse snapshotResponse = client2.snapshotList(instanceId);
        if (dateTime.getDatetime().compareTo(snapshotResponse.getPeriod().getBegin()) < 0
                || dateTime.getDatetime().compareTo(snapshotResponse.getPeriod().getEnd()) > 0) {
            throw new RDSExceptions.BinlogTimeInvalidException();
        }
        RDSClient client = clientFactory.createRdsClient();
        client.binlogCheck(instanceId, dateTime.getDatetime());
    }

    public void binlogFlush(String instanceId) {
        RDSClient client = clientFactory.createRdsClient();
        client.binlogFlush(instanceId);
    }
}
