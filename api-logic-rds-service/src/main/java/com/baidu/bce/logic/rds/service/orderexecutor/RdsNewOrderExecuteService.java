package com.baidu.bce.logic.rds.service.orderexecutor;

import com.baidu.bce.console.settings.service.UserSettingsService;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Flavor;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ResourceMapping;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCreateRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceListResponse;
import com.baidu.bce.internalsdk.rds.model.instance.OrderInfo;
import com.baidu.bce.internalsdk.rds.model.instance.OrderItemExtraInfo;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.RdsOrderService;
import com.baidu.bce.logic.rds.service.RdsResourceManagerService;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.OrderEnvUtil;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import com.baidu.bce.plat.resource.manager.sdk.ResManagerClient;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupResInfo;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceBindRequest;
import com.baidu.bce.plat.servicecatalog.model.order.FlavorItem;
import com.baidu.bce.sdk.renew.AutoRenewClient;
import com.baidu.bce.sdk.renew.model.AutoRenewCreateRequest;
import com.baidubce.util.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;

/**
 * Created by luping03 on 18/3/9.
 */
@Service
public class RdsNewOrderExecuteService extends AbstractRdsExecutorService {

    @Value("${rds.order.retrytime.minute:1440}")
    private int retryTimeInMinute;

    @Value("${rdsreplica.order.retrytime.minute:1440}")
    private int replicaRetryTimeInMinute;

    private int createTimeDay = 3;

    @Value("${rds.multiplePhysicalZone:AZONE-cq02+AZONE-bjyz,AZONE-gznx+AZONE-gzhxy,AZONE-nmg02+AZONE-dbl}")
    private String multiplePhysicalZone;

    private static final Logger LOGGER = LoggerFactory.getLogger(RdsNewOrderExecuteService.class);

    private static final String ORDERALREADYEXISTS_ERROR_CODE = "OrderAlreadyExists";

    @Value("${sms.order.create.success.tpl.id:Tpl_3c9014ff-0670-4aad-b362-a3b6130ee94e}")
    private String smsCreateSuccessId;

    @Value("${sms.order.create.failed.tpl.id:Tpl_2ef56822-636c-4c7d-9a0c-919925b5dd8a}")
    private String smsCreateFailedId;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private OthersService othersService;

    @Autowired
    private RdsOrderService rdsOrderService;

    @Autowired
    private InstanceDao instanceDao;

    @Autowired
    private RdsResourceManagerService rdsResourceManagerService;

    @Override
    public ExecutionResult execute(OrderClient client, ResourceClient resourceClient, Order order) {
        LOGGER.debug("executeOrder");
        ExecutionResult executionResult = new ExecutionResult();
        boolean legalOrder;
        try {
            legalOrder = checkOrder(order);
        } catch (Exception e) {
            legalOrder = false;
            LOGGER.error("checkOrder occur error.", e);
        }

        LOGGER.debug("legalOrder = {}", legalOrder);

        boolean fromShoppingCart = RDSConstant.ORDER_SOURCE_SHOPPING_CART.contains(order.getSource());
        LOGGER.debug("fromShoppingCart = {}", fromShoppingCart);
        if (fromShoppingCart && !legalOrder) {
            return executionResult;
        }

        if (isExceedCreateTime(order)) {
            setExecutionResult(executionResult, order, null, "[RDS order execute] " + RDSConstant.ORDER_TIME_OUT,
                    null, false);
            instanceDao.deleteCreateFailedInstance(order.getUuid(), order.getAccountId());
            updateOrderStatus(client, order, Boolean.FALSE, null);
            return executionResult;
        }

        // 请求后端服务生成instance
        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        List<ResourceMapping> resources = new ArrayList<ResourceMapping>();
        updateOrderRequest.setResources(resources);

        // TODO: 如果item数量大于1, 会有潜在的错误，毕竟不同的item之间不可能保证原子的操作，事实上，目前也只可能出现只有一个item的情况。
        for (Order.Item item : order.getItems()) {
            // 获取item里的配置
            String conf = item.getExtra();

            InstanceCreateRequest instanceCreateRequest;

            ObjectMapper objectMapper = new ObjectMapper();
            try {
                instanceCreateRequest = objectMapper.readValue(conf, OrderItemExtraInfo.class)
                        .getInstanceCreateRequest();
                instanceCreateRequest.getInstanceParameters().setPubliclyAccessible(false);
                LOGGER.debug("OrderItemExtraInfo:" + OrderItemExtraInfo.class);
                LOGGER.debug("instanceCreateRequest:" + instanceCreateRequest);
            } catch (IOException ex) {
                LOGGER.error("rds order error: deserialize configuration error " + ex.toString() + ex);
                // TODO:暂时屏蔽，防止一旦出现序列化失败导致重复发短信
                // sendMessage.send(false, order.getAccountId(), order.getUuid(), ServiceType.RDS);
                setExecutionResult(executionResult, order, null, "[RDS Console] Parse flavor failed", ex, null);
                return executionResult;
            }

            instanceCreateRequest.setOrderId(order.getUuid());
            instanceCreateRequest.setInstanceAmount(item.getCount());

            // RDS 控制台、OpenAPI 的请求，已经把订单、实例信息保存到数据库中了，但购物车来的订单没有把订单、实例信息保存到数据库中，
            // 这里进行入库操作。
            if (fromShoppingCart) {
                instanceService.saveInstance2Db(instanceCreateRequest, order, RDSConstant.FROM_CONSOLE);
                if (CollectionUtils.isEmpty(instanceCreateRequest.getInstanceIds())) {
                    // insertOneOrder Occur DuplicateKeyException，说明有其他线程已处理，这里直接返回
                    return executionResult;
                }
            }

            String logicalZone = instanceCreateRequest.getInstanceParameters().getAzone();
            if (logicalZone.contains(",")) {
                instanceCreateRequest.getInstanceParameters()
                        .setAzone(logicalZone.replaceAll(",", "+"));
            }
            // 判断订单从哪个环境创建，线上测试环境会往订单注入字段env
            String env = null;
            try {
                env = objectMapper.readValue(conf, OrderItemExtraInfo.class).getEnv();
            } catch (IOException e) {
                e.printStackTrace();
            }
            LOGGER.debug("executeOrderNewEnv:" + env);
            RDSClient rdsClient = clientFactory.createRdsClientByAccountId(order.getAccountId());;
            RDSClient2 rdsClient2 = clientFactory.createRdsClient2ByUserId(order.getAccountId());;
            // 如果存在env字段则请求对应环境的后端
            if (StringUtils.isNotEmpty(env)) {
                 rdsClient = clientFactory.createRdsClientByAccountId(order.getAccountId(), env);
                 rdsClient2 = clientFactory.createRdsClient2ByUserId(order.getAccountId(), env);
            }


            if (instanceCreateRequest.getInstanceParameters().getInstanceType() != null
                    && instanceCreateRequest.getInstanceParameters().getInstanceType()
                    .equalsIgnoreCase("financial")) {       // raft使用新版的接口
                LOGGER.debug("[order-executor]raft rds use new create interface");
                rdsClient = clientFactory.createRdsClientV2ByAccountId(order.getAccountId());
                rdsClient2 = clientFactory.createRdsClient2V2ByUserId(order.getAccountId());
            }

            try {
                // 克隆实例设置备份策略与名称
                if (instanceCreateRequest.getInstanceParameters().getInitialDataReference() != null
                        && StringUtils.isNotEmpty(instanceCreateRequest.getInstanceParameters()
                        .getInitialDataReference().getInstanceId())) {
                    InstanceGetResponse response = rdsClient2.instanceDescribe(instanceCreateRequest
                            .getInstanceParameters().getInitialDataReference().getInstanceId());
                    instanceCreateRequest.getInstanceParameters().setBackupPolicy(
                            response.getInstance().getBackupPolicy());
                    if (StringUtils.isEmpty(instanceCreateRequest.getInstanceParameters().getInstanceName())) {
                        instanceCreateRequest.getInstanceParameters()
                                .setInstanceName(response.getInstance().getInstanceId() + "_replica");
                    }
                }

                for (String instanceShortId : instanceCreateRequest.getInstanceIds()) {
                    ResourceMapping resourceMapping = new ResourceMapping();
                    resourceMapping.setShortId(instanceShortId);
                    resourceMapping
                            .withKey(item.getKey())
                            .withStatus(ResourceStatus.INIT);
                    resources.add(resourceMapping);
                }
                LOGGER.debug("instanceCreateRequest:" + instanceCreateRequest);
                InstanceListResponse instanceCreateResponse = rdsClient.instanceCreate(instanceCreateRequest);
                Collection<Instance> instances = instanceCreateResponse.getInstances();

                int i = 0;
                for (Instance instance : instances) {
                    resources.get(i).withId(instance.getInstanceId());
                    i++;
                }
            } catch (BceInternalResponseException ex) {
                // 如果此订单已向后端提交过，则无视此异常
                if (ORDERALREADYEXISTS_ERROR_CODE.equals(ex.getCode())) {
                    OrderInfo orderInfo = rdsClient.getOrderInfo(order.getUuid());
                    List<String> instanceIds = orderInfo.getOrder().getApplications();
                    if (instanceIds != null) {
                        for (int i = 0; i < instanceIds.size(); i++) {
                            resources.get(i).withId(instanceIds.get(i));
                        }
                    }
                } else if ((HttpStatus.BAD_REQUEST.value() == ex.getHttpStatus())
                        || (HttpStatus.NOT_FOUND.value() == ex.getHttpStatus())
                        || (HttpStatus.FORBIDDEN.value() == ex.getHttpStatus())) {
                    setExecutionResult(executionResult, order, null,
                            "[RDS Backend] Execute READY_FOR_CREATE order failed", ex, false);
                    instanceDao.deleteCreateFailedInstance(order.getUuid(), order.getAccountId());
                    updateOrderStatus(client, order, Boolean.FALSE, null);
//                    executionResult.setMessageCenterModels(getMessage(order, false));
                    return executionResult;
                } else {
                    throw ex;
                }
            }
        }

        // 至此，创建请求已经发给后端，console 层已经得到实例长ID信息，将这项信息入库
        for (ResourceMapping resource : updateOrderRequest.getResources()) {
            String instanceShortId = resource.getShortId(); // 短ID
            String instanceId = resource.getId(); // 长ID
            int affectedRows = instanceDao.updateInstanceUuid(instanceShortId, instanceId);
            LOGGER.info("instanceShortId = {}, instanceId = {}, affectedRows = {}", instanceShortId, instanceId,
                    affectedRows);
        }
        int count = 3;
        while (count-- > 0) {
            try {
                updateOrderRequest.setStatus(OrderStatus.CREATING);
                updateOrderRequest.setServiceType(order.getServiceType());
                client.update(order.getUuid(), updateOrderRequest);
                executionResult.setExecutionStatus(ExecutionStatus.CREATING);
                LOGGER.info("rds write back order status success");
                break;
            } catch (Exception ex) {
                LOGGER.warn("rds order update status error" + ex);
                try {
                    Thread.sleep(1000);
                } catch (Exception e) {
                    LOGGER.info("rds order sleep" + e);
                }
            }
        }
        return executionResult;
    }

    private boolean checkOrder(Order order) {
        List<Order.Item> orderItems = order.getItems();
        for (Order.Item orderItem : orderItems) {
            if (checkOrderItem(orderItem)) {
                continue;
            }
            return false;
        }
        return true;
    }

    private boolean checkOrderItem(Order.Item orderItem) {
        OrderItemExtraInfo orderItemExtraInfo =
                JsonUtils.fromJsonString(orderItem.getExtra(), OrderItemExtraInfo.class);
        InstanceCreateRequest instanceCreateRequest = orderItemExtraInfo.getInstanceCreateRequest();
        InstanceCreateRequest.InstanceParameters instanceParameters = instanceCreateRequest.getInstanceParameters();

        // 本订单 Item serviceType
        String serviceType = rdsOrderService
                .getServiceType(instanceParameters.getEngine(), instanceParameters.getSourceInstanceId());
        String orderServiceType = orderItem.getServiceType();
        if (!serviceType.equals(orderServiceType)) {
            LOGGER.warn("Illegal order, serviceType = {}, orderServiceType = {}", serviceType, orderServiceType);
            return false;
        }

        InstanceCreateModel.DashCreateInstance dashCreateInstance = new InstanceCreateModel.DashCreateInstance();
        dashCreateInstance.setEngine(instanceParameters.getEngine());
        dashCreateInstance.setEngineVersion(instanceParameters.getEngineVersion());
        dashCreateInstance.setAzone(instanceParameters.getAzone());
        dashCreateInstance.setNodeAmount(instanceParameters.getNodeAmount());
        dashCreateInstance.setIsSingle(instanceParameters.getIsSingle());
        dashCreateInstance.setIsEnhanced(instanceParameters.getIsEnhanced());
        dashCreateInstance.setInstanceType(instanceParameters.getInstanceType());
        dashCreateInstance.setCategory(instanceParameters.getCategory());
        dashCreateInstance.setSourceInstanceId(instanceParameters.getSourceInstanceId());
        dashCreateInstance.setInitialDataReference(instanceParameters.getInitialDataReference());
        dashCreateInstance.setCpuCount(instanceParameters.getCpuCount());
        dashCreateInstance.setAllocatedMemoryInGB(instanceParameters.getAllocatedMemoryInMB() / 1024);
        dashCreateInstance.setAllocatedStorageInGB(instanceParameters.getAllocatedStorageInGB());
        dashCreateInstance.setAzone(instanceParameters.getAzone());
        dashCreateInstance.setVpcId(instanceParameters.getVpcId());
        dashCreateInstance.setResourceType(instanceParameters.getResourceType());
        StringBuilder subnetIdSb = new StringBuilder();
        for (Map.Entry<String, String> entry : instanceParameters.getSubnetId().entrySet()) {
            if (entry.getValue().isEmpty()) {
                subnetIdSb.append(entry.getKey());
            } else {
                subnetIdSb.append(entry.getKey()).append(":").append(entry.getValue());
            }
            subnetIdSb.append(",");
        }
        if (subnetIdSb.length() > 0) {
            subnetIdSb.deleteCharAt(subnetIdSb.length() - 1);
        }
        dashCreateInstance.setSubnetId(subnetIdSb.toString());
        dashCreateInstance.setDiskIoType(instanceParameters.getDiskIoType());

        Set<FlavorItem> flavorItems = rdsOrderService.getFlavorItemsForOrder(dashCreateInstance);
        // 这些跟计费没直接关系的计费项，getFlavorItemsForOrder 中没有为其设置 scale，这里将其 scale 置为 Billing 默认值 1，
        // 不然，无法通过下面的 diffFlavorItems
        List<String> abnormalFlavorItemNames = Arrays.asList("subServiceType", "sourceInstanceId", "azone", "vpcId",
                "subnetId");
        for (FlavorItem flavorItem : flavorItems) {
            if (abnormalFlavorItemNames.contains(flavorItem.getName()) && flavorItem.getScale() == null) {
                flavorItem.setScale(1);
            }
        }

        Flavor orderFlavorItems = orderItem.getFlavor();
        return diffFlavorItems(flavorItems, orderFlavorItems);
    }

    private boolean diffFlavorItems(Set<FlavorItem> flavorItems, Flavor orderFlavorItems) {
        boolean res = true;
        Map<String, FlavorItem> flavorItemMap = new HashMap<>(flavorItems.size());
        for (FlavorItem flavorItem : flavorItems) {
            flavorItemMap.put(flavorItem.getName(), flavorItem);
        }
        Map<String, Flavor.FlavorItem> orderFlavorItemMap = new HashMap<>(orderFlavorItems.size());
        for (Flavor.FlavorItem flavorItem : orderFlavorItems) {
            orderFlavorItemMap.put(flavorItem.getName(), flavorItem);
        }
        for (Map.Entry<String, FlavorItem> entry : flavorItemMap.entrySet()) {
            String flavorItemName = entry.getKey();
            FlavorItem flavorItem = entry.getValue();
            if (!orderFlavorItemMap.containsKey(flavorItemName)) {
                res = false;
                LOGGER.warn("Illegal order, flavorItemName = {}, not in orderFlavorItems.", flavorItemName);
            }
            Flavor.FlavorItem orderFlavorItem = orderFlavorItemMap.get(flavorItemName);
            if (!diffFlavorItem(flavorItem, orderFlavorItem)) {
                res = false;
                LOGGER.warn("Illegal order, flavorItemName = {}, not equal.", flavorItemName);
                LOGGER.warn("Illegal order, flavorItemValue = {} ===> {}", flavorItem.getValue(),
                        orderFlavorItem.getValue());
                LOGGER.warn("Illegal order, flavorItemScale = {} ===> {}", flavorItem.getScale(),
                        orderFlavorItem.getScale());
            }
        }
        for (Map.Entry<String, Flavor.FlavorItem> entry : orderFlavorItemMap.entrySet()) {
            String orderFlavorItemName = entry.getKey();
            if (!flavorItemMap.containsKey(orderFlavorItemName)) {
                res = false;
                LOGGER.warn("Illegal order, orderFlavorItemName = {}, not in flavorItems.", orderFlavorItemName);
            }
        }
        return res;
    }

    private boolean diffFlavorItem(FlavorItem flavorItem, Flavor.FlavorItem orderFlavorItem) {
        if (!flavorItem.getName().equals(orderFlavorItem.getName())) {
            return false;
        }
        if (!flavorItem.getValue().equals(orderFlavorItem.getValue())) {
            return false;
        }
        if (flavorItem.getScale() != orderFlavorItem.getScale().intValue()) {
            return false;
        }
        return true;
    }

    @Override
    public ExecutionResult check(OrderClient client, ResourceClient resourceClient, Order order) {
        LOGGER.debug("checkStatusOrder");        // 请求后端服务生成instance
        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        boolean succeed = true;

        // 从order信息中解析实例创建信息，获取instanceType判断是否是需要走raft接口
        InstanceCreateRequest instanceCreateRequest;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            instanceCreateRequest = objectMapper.readValue(order.getItems().get(0).getExtra(),
                    OrderItemExtraInfo.class).getInstanceCreateRequest();
            instanceCreateRequest.getInstanceParameters().setPubliclyAccessible(false);
        } catch (IOException ex) {
            LOGGER.error("rds order error: deserialize configuration error " + ex.toString() + ex);
            // TODO:暂时屏蔽，防止一旦出现序列化失败导致重复发短信
            // sendMessage.send(false, order.getAccountId(), order.getUuid(), ServiceType.RDS);
            setExecutionResult(executionResult, order, null, "[RDS Console] Parse flavor failed", ex, null);
            return executionResult;
        }

        List<String> instanceIds = new ArrayList<>();
        Map<String, String> instanceLongId2ShortIdMaps = new HashMap<>();
        Map<String, String> instanceShortId2LongIdMaps = new HashMap<>();
        Map<String, Instance> instanceLongIdMap = new HashMap<>();
        Map<String, Resource> instanceLongIdResourceMap = new HashMap<>();
        if (isExceedRetryTime(order)) {
            succeed = false;
            setExecutionResult(executionResult, order, null, "[RDS Backend] " + RDSConstant.ORDER_TIME_OUT,
                    null, succeed);
        } else {
            // 判断订单从哪个环境创建，线上测试环境会往订单注入字段env
            String env = OrderEnvUtil.getEnvFromNewOrder(order);
            LOGGER.debug("checkOrderNewEnv:" + env);
            RDSClient rdsClientV1 = StringUtils.isNotEmpty(env) ?
                    clientFactory.createRdsClientByAccountId(order.getAccountId(), env) :
                    clientFactory.createRdsClientByAccountId(order.getAccountId());
            RDSClient2 rdsClient = StringUtils.isNotEmpty(env) ?
                    clientFactory.createRdsClient2ByUserId(order.getAccountId(), env) :
                    clientFactory.createRdsClient2ByUserId(order.getAccountId());

            if (instanceCreateRequest.getInstanceParameters().getInstanceType() != null
                    && instanceCreateRequest.getInstanceParameters().getInstanceType()
                    .equalsIgnoreCase("financial")) {  // raft使用新版的接口
                LOGGER.debug("[order-executor]raft rds use new create interface");
                rdsClient = clientFactory.createRdsClient2V2ByUserId(order.getAccountId());
            }

//            Map<String, String> instanceLongId2ShortIdMaps = new HashMap<String, String>();

            if (order.getResourceIds() == null) {
                // 这个 if 条件不会成立，只会执行 else 分支
                for (ResourceMapping resourceMapping : order.getItems().get(0).getResourceMappings()) {
                    instanceIds.add(resourceMapping.getId());
                    instanceLongId2ShortIdMaps.put(resourceMapping.getId(), resourceMapping.getShortId());
                }
            } else {
                for (String resourceId : order.getResourceIds()) {

                    Resource res = resourceClient.get(resourceId);
                    // instanceIds 存储的是长 ID
                    instanceIds.add(res.getName());
                    // instanceLongId2ShortIdMaps 长、短 ID Map
                    instanceLongId2ShortIdMaps.put(res.getName(), res.getShortId());
                    instanceShortId2LongIdMaps.put(res.getShortId(), res.getName());
                    instanceLongIdResourceMap.put(res.getName(), res);
                }
            }

            for (String instanceId : instanceIds) {
//                ResourceClient resourceClient = orderExecuteClientFactory.createResourceClient();
//                String instanceId = resourceClient.get(resourceId).getName();
//                instanceIds.add(instanceId);
                InstanceGetResponse response;
                try {
                    response = rdsClient.instanceDescribe(instanceId);
                    instanceLongIdMap.put(instanceId, response.getInstance());
                } catch (BceInternalResponseException ex) {

                    if (ex.getCode().equals("DbinstanceNotFound") || ex.getHttpStatus() == 404) {
                        // 因为后端存在主从延迟，之前DbinstanceNotFound 或 404 时候可能实例是存在的，所以暂时去掉这两种情况的失败处理，
                        // 最终还是要依赖后端优化，暂时只是打印个日志
                        // 等待下次查询
                        LOGGER.warn("rds instance:{} [New Order] found in resources but not exist "
                                + "in background service", instanceId);
                    }

                    if (ex.getCode().equals("InstanceAlreadyDeleted")) {
                        // order服务认定创建失败。这种情况应该是由于数据不一致导致的。需人工介入，事实上order服务会去删resource资源，
                        // 最终结果是一致的
                        // 此时认定创建订单已失败
                        LOGGER.warn("rds instance:{} found in resources but 'InstanceAlreadyDeleted'"
                                + " in background service", instanceId);
                        succeed = false;
                        setExecutionResult(executionResult, order, null,
                                "[RDS Backend] After call rds create interface, query rds detail failed", ex, false);
                        break;
                    }
                    // 其他错误，则不认定订单创建失败，下次再次查询
                    LOGGER.warn("rds query instance:{} state error", instanceId);
                    return executionResult;
                }
                String status = response.getInstance().getInstanceStatus();
                switch (status) {
                    case "creating":
                        executionResult.setExecutionStatus(ExecutionStatus.CREATING);
                        executionResult.setMessageCenterModels(null);
                        return executionResult;
                    case "deleted":
                    case "failed":
                        succeed = false;
                        setExecutionResult(executionResult, order, null, "[RDS Backend] Query rds detail failed, "
                                + "After call rds create interface, query rds detail status is " + status, null, false);
                        break;
                    case "available":
//                        UpdateResourceRequest updateResourceRequest = new UpdateResourceRequest();
//                        updateResourceRequest.setStatus(ResourceStatus.RUNNING);
//                        resourceClient.update(resourceId, updateResourceRequest);
                        executionResult.setExecutionStatus(ExecutionStatus.SUCCESS);
                        executionResult.setMessageCenterModels(getMessage(order, null, true));

                        // 通知后端绑定长短id，通知失败不影响
                        try {

                            LOGGER.info("ready to bing shortId & longId(backend).");
                            if (instanceLongId2ShortIdMaps.get(instanceId) != null) {
                                rdsClientV1.bindShortId(instanceId,
                                        instanceLongId2ShortIdMaps.get(instanceId));
                            } else {
                                LOGGER.error("notice rds bind short instance id error, not find shortId",
                                        instanceLongId2ShortIdMaps);
                            }

                        } catch (Exception e) {
                            LOGGER.error("notice rds bind short instance id error, execute error", e);
                        }

                        break;
                    default:
                        LOGGER.warn("unexpected rds instance status:{} ", status);
                        return executionResult;
                }

                if (!succeed) {
                    break;
                }
            }
        }

        if (succeed) {
            try {
                // 更新tags失败，只记日志，不做处理
                rdsAssignTags(instanceIds, order, instanceLongId2ShortIdMaps);
            } catch (Exception e) {
                LOGGER.error("rdsAssignTags error for " + order, e);
            }
            try {
                sendUpdateNameMsgToResourceManager(order, instanceShortId2LongIdMaps, instanceLongIdMap);
                // 更新resource失败，只记日志，不做处理
                rdsAssignResources(instanceIds, order, instanceLongId2ShortIdMaps);
            } catch (Exception e) {
                LOGGER.error("rdsAssignTags resource for " + order, e);
            }

        }

        // 更新表 t_rds_instance
        if (succeed) {
            List<InstancePO> instancePOS = instanceDao
                    .getInstanceListByOrderUuid(order.getUuid(), order.getAccountId());
            for (InstancePO instancePO : instancePOS) {
                String instanceShortId = instancePO.getInstanceId();
                String instanceId = instanceShortId2LongIdMaps.get(instanceShortId);
                packageInstancePO(instancePO, instanceLongIdMap.get(instanceId),
                        instanceLongIdResourceMap.get(instanceId));
            }
            instanceDao.batchUpdateInstanceById(instancePOS);
        } else {
            instanceDao.deleteCreateFailedInstance(order.getUuid(), order.getAccountId());
        }

        updateOrderStatus(client, order, succeed, instanceIds);
        return executionResult;
    }

    /**
     * 组装InstancePO
     *
     * @param instancePO instancePO
     * @param instance instance
     * @param resource resource
     */
    private void packageInstancePO(InstancePO instancePO, Instance instance, Resource resource) {
        instancePO.setInstanceUuid(instance.getInstanceId());
        instancePO.setInstanceName(instance.getInstanceName());
        instancePO.setInstanceStatus(instance.getInstanceStatus());
        instancePO.setUsedStorage(instance.getUsedStorageInMB() / 1024.00);
        instancePO.setInstanceCreateTime(new Timestamp(instance.getInstanceCreateTime().getTime()));
        if (resource.getExpireTime() != null) {
            instancePO.setInstanceExpireTime(new Timestamp(resource.getExpireTime().getTime()));
        }
        instancePO.getEndpoint().setAddress(instance.getEndpoint().getAddress());
        instancePO.getEndpoint().setPort(instance.getEndpoint().getPort());
        instancePO.getEndpoint().setInetIp(instance.getEndpoint().getInetIp());
        instancePO.getEndpoint().setVnetIp(instance.getEndpoint().getVnetIp());
        instancePO.setReplicationType(instance.getReplicationType());
        instancePO.getBackupPolicy().setBackupFreeSpace(instance.getBackupPolicy().getFreeSpaceInGB());
        instancePO.setResourceUuid(resource.getUuid());
    }

    private void updateOrderStatus(OrderClient client, Order order, boolean succeed, List<String> instanceIds) {
        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setServiceType(order.getServiceType());
        if (succeed) {
            updateOrderRequest.setStatus(OrderStatus.CREATED);
            try {
                setAutoRenewRules(order, instanceIds);
            } catch (Exception ex) {
                LOGGER.warn("RDS autorenew ERROR : " + ex.getMessage());
            }
        } else {
            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
        }

        client.update(order.getUuid(), updateOrderRequest);
    }

    private void setAutoRenewRules(Order order, List<String> instanceIds) {
        for (Order.Item orderItem : order.getItems()) {
            AutoRenewCreateRequest autoRenewCreateRequest =
                    getAutoRenewCreateRequestFromOrderItem(order, orderItem);
            if (autoRenewCreateRequest != null) {
                autoRenewCreateRequest.setServiceIds(instanceIds);
                AutoRenewClient autoRenewClient =
                        clientFactory.createAutoRenewClient(order.getAccountId());
                if (autoRenewCreateRequest.getRenewTime() > 0
                        && CollectionUtils.isNotEmpty(autoRenewCreateRequest.getServiceIds())) {
                    autoRenewClient.createAutoRenewRule(autoRenewCreateRequest);
                }
            }
        }
    }

    private AutoRenewCreateRequest getAutoRenewCreateRequestFromOrderItem(Order order, Order.Item orderItem) {
        AutoRenewCreateRequest autoRenewCreateRequest = null;
        if (StringUtils.isEmpty(orderItem.getExtra())) {
            return null;
        }
        if (ServiceType.RDS.getName().equalsIgnoreCase(orderItem.getServiceType())
                || ServiceType.RDS_REPLICA.getName().equalsIgnoreCase(orderItem.getServiceType())) {
            ObjectMapper objectMapper = new ObjectMapper();
            OrderItemExtraInfo orderItemExtraInfo;
            try {
                orderItemExtraInfo = objectMapper.readValue(orderItem.getExtra(), OrderItemExtraInfo.class);
            } catch (IOException ex) {
                LOGGER.error("rds order error: deserialize configuration error " + ex.toString() + ex);
//                saveOrderExceptionToTrace(order, "[RDS OrderExecute] Parse orderItem extra failed", ex);
                return null;
            }

            String autoRenewTimeUnit = orderItemExtraInfo.getAutoRenewTimeUnit();
            int autoRenewTime = orderItemExtraInfo.getAutoRenewTime();
            if (StringUtils.isNotEmpty(autoRenewTimeUnit) && autoRenewTime > 0) {
                autoRenewCreateRequest = new AutoRenewCreateRequest();
                autoRenewCreateRequest.setRegion(orderItem.getRegion());
                autoRenewCreateRequest.setAccountId(order.getAccountId());
                autoRenewCreateRequest.setRenewTime(Integer.valueOf(autoRenewTime));
                autoRenewCreateRequest.setRenewTimeUnit(autoRenewTimeUnit);
                autoRenewCreateRequest.setServiceType(orderItem.getServiceType());
            }
        }
        return autoRenewCreateRequest;
    }

    private boolean isExceedRetryTime(Order order) {
        boolean result = false;

        Calendar retryEndTime = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
//        Date purchaseTime = ProductPayType.PRE_PAY.toString().equalsIgnoreCase(order.getProductType())
//                ? order.getPurchaseTime() : order.getCreateTime();
//        retryEndTime.setTime(purchaseTime);
        Date updateTime = order.getUpdateTime();
        retryEndTime.setTime(updateTime);
        retryEndTime.add(Calendar.MINUTE, ServiceType.RDS.toString().equalsIgnoreCase(order.getServiceType())
                ? retryTimeInMinute : replicaRetryTimeInMinute);

        Calendar now = Calendar.getInstance(TimeZone.getTimeZone("UTC"));

        if (retryEndTime.before(now)) {
            result = true;
        }

        return result;
    }

    private boolean isExceedCreateTime(Order order) {
        boolean result = false;

        Calendar retryEndTime = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        Date updateTime = order.getUpdateTime();
        retryEndTime.setTime(updateTime);
        retryEndTime.add(Calendar.DATE, createTimeDay);

        Calendar now = Calendar.getInstance(TimeZone.getTimeZone("UTC"));

        if (retryEndTime.before(now)) {
            result = true;
        }

        return result;
    }

    @Override
    protected String getMessageTpl(boolean succeed) {
        return succeed ? smsCreateSuccessId : smsCreateFailedId;
    }

    public void rdsAssignTags(List<String> instanceIds, Order order,
                              Map<String, String> instanceLongId2ShortIdMaps) {

        // 只处理一次
        int i = 0;

        for (Order.Item item : order.getItems()) {

            if ( i > 0) {
                LOGGER.info("[assign tags] great than 0, stop.");
                break;
            }

            // 获取item里的配置
            String conf = item.getExtra();

            LOGGER.info("[assign tags] conf : " + conf);

            OrderItemExtraInfo info;
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                info = objectMapper.readValue(conf, OrderItemExtraInfo.class);
            } catch (IOException ex) {
                LOGGER.error("rds assign tags error: deserialize configuration error " + ex.toString() + ex);
                return;
            }

            LOGGER.info("[assign tags] mapper " + info);

            InstanceCreateRequest createRequest = info.getInstanceCreateRequest();

            String currentRegion = UserSettingsService.getRegion();

            List<Tag> tags = info.getTags();

            Map<String, List<Tag>> instanceIdTagsMap = new HashMap<>();
            if (StringUtils.isNotEmpty(createRequest.getInstanceParameters().getSourceInstanceId())
                    && (ServiceType.RDS_PROXY.getName().equals(item.getServiceType())
                    || ServiceType.RDS_REPLICA.getName().equals(item.getServiceType()))) {
                // sourceInstanceId 不为空，说明目前创建的实例是只读实例或代理实例；
                // 只读实例、代理实例，同步其主实例标签；
                tags = othersService.queryTagsByInstanceId(currentRegion, order.getAccountId(),
                        createRequest.getInstanceParameters().getSourceInstanceId());
            }
            if (CollectionUtils.isNotEmpty(tags)) {
                for (String instanceId : instanceIds) {
                    instanceIdTagsMap.put(instanceId, tags);
                }
            }

            // 设置tags
            othersService.createAndAssignTag(currentRegion, item.getServiceType(), order.getAccountId(),
                    instanceIdTagsMap, instanceLongId2ShortIdMaps);
        }
    }

    public void sendUpdateNameMsgToResourceManager(Order order, Map<String, String> instanceShortId2LongIdMaps,
                                                   Map<String, Instance> instanceLongIdMap) {
        for (Order.Item item : order.getItems()) {
            String serviceType = item.getServiceType();
            String accountId = order.getAccountId();
            String userId = order.getUserId();
            OrderItemExtraInfo orderItemExtraInfo = JsonUtils.fromJsonString(item.getExtra(), OrderItemExtraInfo.class);
            for (String instanceShortId : orderItemExtraInfo.getInstanceCreateRequest().getInstanceIds()) {
                String id = instanceShortId;
                String uuid = instanceShortId2LongIdMaps.get(id);
                String name = instanceLongIdMap.get(uuid).getInstanceName();
                rdsResourceManagerService.sendUpdateNameMsg(serviceType, accountId, userId, id, uuid, name);
            }
        }
    }

    public void rdsAssignResources(List<String> instanceIds, Order order,
                                   Map<String, String> instanceLongId2ShortIdMaps) {

        // 只处理一次
        int i = 0;

        for (Order.Item item : order.getItems()) {

            if ( i > 0) {
                LOGGER.info("[assign resource] great than 0, stop.");
                break;
            }

            // 获取item里的配置
            String conf = item.getExtra();

            LOGGER.info("[assign resource] conf : " + conf);

            OrderItemExtraInfo info;
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                info = objectMapper.readValue(conf, OrderItemExtraInfo.class);
            } catch (IOException ex) {
                LOGGER.error("rds assign resource error: deserialize configuration error " + ex.toString() + ex);
                return;
            }

            LOGGER.info("[assign resource] mapper " + info);

            if (info.getResourceGroupId() == null || info.getResourceGroupId() == "") {
                LOGGER.info("rds assign resource, no resource assign ");
                continue;
            }

//            InstanceCreateRequest createRequest = info.getInstanceCreateRequest();
            String resourceGroupId = info.getResourceGroupId();
            String currentRegion = UserSettingsService.getRegion();

            List<GroupResInfo> bindResources = new ArrayList<>();

            for (String eachInstanceId : instanceIds) {
                GroupResInfo assignResource = new GroupResInfo();
                assignResource.setResourceId(instanceLongId2ShortIdMaps.get(eachInstanceId));
                assignResource.setAccountId(order.getAccountId());
                assignResource.setUserId(order.getUserId());
                assignResource.setGroupId(resourceGroupId);
                assignResource.setResourceRegion(currentRegion);
                assignResource.setResourceType(item.getServiceType() == null ? ServiceType.RDS.name() :
                        item.getServiceType());
                assignResource.setCreateTime(new Timestamp(System.currentTimeMillis()));
                bindResources.add(assignResource);
            }

            ResourceBindRequest resourceBindRequest = new ResourceBindRequest();
            resourceBindRequest.setBindings(bindResources);
            // 设置resource
            ResManagerClient resManagerClient = clientFactory.resourceGroupClient(order.getAccountId());

            resManagerClient.bindResource(resourceBindRequest, true);
        }
    }
}
