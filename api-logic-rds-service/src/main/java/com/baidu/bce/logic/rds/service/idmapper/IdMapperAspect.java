package com.baidu.bce.logic.rds.service.idmapper;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * Created by luping03 on 2017/12/28.
 */
@Aspect
@Component
@Order(Integer.MAX_VALUE - 50)
public class IdMapperAspect {

    private static final Logger LOGGER = LoggerFactory.getLogger(IdMapperAspect.class);

    @Autowired
    IdMapperService idMapperService;

    @Pointcut("execution(* com.baidu.bce.logic.rds.controller..*.*(..)) "
            + "and @annotation(org.springframework.web.bind.annotation.RequestMapping)")
//            + "and !execution(* com.baidu.bce.logic.rds.controller.IdMapperController.*(..))")
    public void controllerMethodPointcut() {

    }

    /**
     * 对于参数，如果想做长短id转化，必须在参数上添加IdMapper注解，无论字符串还是对象；
     * 对象中的属性，list和map需要指明type和category，map只处理value，不处理key
     * List元素的refValue是自身
     *
     * @param proceedingJoinPoint proceedingJoinPoint
     * @return Object
     * @throws Throwable
     */
    @Around("controllerMethodPointcut()")
    public Object idMapperInterceptor(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        // LOGGER.debug("idMapperInterceptor start");
        StopWatch sw = new StopWatch("handle controller method");
        sw.start("aspect 1. handle parameters");
        Object result = null;
        Object[] originalArgs = proceedingJoinPoint.getArgs();
        Object[] args = null;
        // 1. 处理参数
        // LOGGER.debug("1. handle parameters");
        try {
            args = handleMethodParameters(proceedingJoinPoint);
//            if (ArrayUtils.isNotEmpty(args)) {
//                for (int i = 0; i < args.length; i++) {
//                    IdMapperAnnotationUtil.handleIdMapperAnnotation(args[i]);
//                }
//            }

        } catch (Throwable throwable) {
            LOGGER.error("###IdMapperAspect error!", throwable);
            args = originalArgs;
        }
        sw.stop();
        sw.start("aspect 2. execute method");
        // 2. 执行方法
        result = proceedingJoinPoint.proceed(args);

        // 3. 处理返回结果
        sw.stop();
        sw.start("aspect 3. handle result");
        if (result != null) {
//            IdMapperAnnotationUtil.handleIdMapperAnnotation(result);
        }
        sw.stop();
        LOGGER.debug(sw.prettyPrint());

        return result;
    }

    // example to get method's parameter annotation
    // 传入参数转换长id，
    // 传入参数可能是长id，短id，或者空，长id和空不予处理，短id转化，
    private Object[] handleMethodParameters(ProceedingJoinPoint thisJoinPoint) throws Throwable {
        Object[] args = thisJoinPoint.getArgs();
        Object[] result = new Object[args.length];
        MethodSignature methodSignature = (MethodSignature) thisJoinPoint.getSignature();
        Method method = methodSignature.getMethod();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();
        // assert args.length == parameterAnnotations.length;
        for (int argIndex = 0; argIndex < args.length; argIndex++) {
            for (Annotation annotation : parameterAnnotations[argIndex]) {
                if (!(annotation instanceof IdMapper)) {
                    continue;
                }

                if (args[argIndex] instanceof String) {
                    // 有idmapper的string，但是值是空字符串，不需要转化长短ID
                    String id = (String) args[argIndex];
                    if (StringUtils.isBlank(id)) {
                        continue;
                    }
                    String longId = idMapperService.getInstanceUuid(id);
                    result[argIndex] = longId;
                    // LOGGER.debug("result[argIndex] is {}", longId);
                } else {
                    // 对象类型，检查内部结构
                    handleIdMapperAnnotation(args[argIndex]);
                }
            }
            if (result[argIndex] == null) {
                result[argIndex] = args[argIndex];
            }
        }

        return result;
    }

//    private IdMapperService getIdMapperService() {
//        return BeanFactory.getBean(IdMapperService.class);
//    }
    public void handleIdMapperAnnotation(Object arg)  throws Throwable {
        LOGGER.debug("handleIdMapperAnnotation. {}", arg);
        Field[] arr = arg.getClass().getDeclaredFields();
        int len = arr.length;

        for ( int i = 0; i < len; ++i ) {
            Field field = arr[i];
            field.setAccessible(true);
            String fieldName = field.getName();
            Object fieldValue = field.get(arg);
            Class fieldClazz = field.getType();
            LOGGER.debug("handleIdMapperAnnotation fieldName={}, fieldValue={}, fieldClazz={}",
                    new Object[]{fieldName, fieldValue, fieldClazz});
            Annotation annotation = field.getAnnotation(IdMapper.class);
            LOGGER.debug("handleIdMapperAnnotation field={}, annotation={}", field, annotation);
            if ( annotation instanceof IdMapper ) {
                fieldClazz = fieldValue.getClass();
                if (isPrimitiveType(fieldClazz)) {
                    // 有idmapper的string，但是值是空字符串，不需要转化长短ID
                    String id = fieldValue.toString();
                    LOGGER.debug("handleIdMapperAnnotation id", id);
                    if (StringUtils.isBlank(id)) {
                        continue;
                    }
                    String longId = idMapperService.getInstanceUuid(id);
                    LOGGER.debug("handleIdMapperAnnotation longId", longId);
                    arr[i].set(arg, longId);
                    LOGGER.debug("handleIdMapperAnnotation arr$[i$] is {}", arr[i].get(arg));
                    // LOGGER.debug("result[argIndex] is {}", longId);
                } else {
                    handleIdMapperAnnotation(arr[i]);
                }
            }
        }
    }
    private boolean isPrimitiveType(Class<?> paramClazz) {
        return paramClazz.isPrimitive() || paramClazz.getName().startsWith("java.lang");
    }
}
