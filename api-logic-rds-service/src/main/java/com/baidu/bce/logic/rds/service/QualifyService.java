package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.qualify.QualifyClientV2;
import com.baidu.bce.internalsdk.qualify.model.RealNameInfoResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class QualifyService {
    private static final Logger LOGGER = LoggerFactory.getLogger(QualifyService.class);

    @Autowired
    private LogicRdsClientFactory logicRdsClientFactory;

    public RealNameInfoResponse getQualifyRealname() {
        QualifyClientV2 qualifyClient = logicRdsClientFactory.createQualifyClient();
        return qualifyClient.getRealNameWithWhiteList(LogicUserService.getAccountId());
    }
}
