package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.rollback.CheckTimeResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.PreCheckTimeRequest;
import com.baidu.bce.internalsdk.rds.model.rollback.RollbackListResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlRollbackResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlRollbacksRequest;
import com.baidu.bce.internalsdk.rds.model.rollback.SqlTaskDetailResponse;
import com.baidu.bce.internalsdk.rds.model.rollback.TaskParamResponse;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RdsSqlRollbackService {

    @Autowired
    private LogicRdsClientFactory clientFactory;

    public SqlRollbackResponse rollbackTask(String instanceId, SqlRollbacksRequest request) {
        RDSClient2 rdsClient2 = clientFactory.createRdsClient2();
        return rdsClient2.rollbackTask(instanceId, request);
    }

    public CheckTimeResponse checkTime(String instanceId, PreCheckTimeRequest request) {
        RDSClient2 rdsClient2 = clientFactory.createRdsClient2();
        return rdsClient2.checkTime(instanceId, request);
    }

    public RollbackListResponse taskList(String instanceId, Integer marker, Integer maxKeys, String from) {
//        // 对于来自控制台的请求，需要对 marker 字段统一做减一处理
//        if (!RDSConstant.FROM_API.equals(from)) {
//            if (marker.equals(0)) {
//                marker = 0;
//            } else {
//                marker -= 1;
//            }
//        }
        RDSClient2 rdsClient2 = clientFactory.createRdsClient2();
        // totalCount == totalKeys 方便前端处理
        RollbackListResponse rollbackListResponse = rdsClient2.taskList(instanceId, marker, maxKeys);
        if (rollbackListResponse != null && rollbackListResponse.getPagination() != null
                && rollbackListResponse.getPagination().getTotalKeys() != null) {
            rollbackListResponse.setTotalCount(rollbackListResponse.getPagination().getTotalKeys());
        }
        return rollbackListResponse;
    }

    public SqlTaskDetailResponse detail(String instanceId, String taskID) {
        RDSClient2 rdsClient2 = clientFactory.createRdsClient2();
        return rdsClient2.detail(instanceId, taskID);
    }

    public TaskParamResponse getTaskParameters(String instanceId, String taskID) {
        RDSClient2 rdsClient2 = clientFactory.createRdsClient2();
        return rdsClient2.getTaskParameters(instanceId, taskID);
    }
}
