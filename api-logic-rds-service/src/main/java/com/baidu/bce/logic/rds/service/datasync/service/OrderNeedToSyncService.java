package com.baidu.bce.logic.rds.service.datasync.service;

import com.baidu.bce.logic.rds.dao.model.OrderNeedToSyncPO;
import com.baidu.bce.logic.rds.dao.mybatis.OrderNeedToSyncMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by luping03 on 18/1/8.
 */
@Service
public class OrderNeedToSyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(NeedToSyncService.class);

    @Autowired
    private OrderNeedToSyncMapper orderSyncMapper;

    /**
     * 查出一条待同步的数据
     *
     * @return
     */
    public List<OrderNeedToSyncPO> findOneSyncOrder(Date outLockDate) {
        return orderSyncMapper.findOneSyncOrder(outLockDate);
    }

    /**
     * 尝试锁定一行记录, 只有lock_time = oldLockDate, lock_id为空时才可以锁定
     *
     * @param id
     * @param lockDate
     * @param lockId
     * @return
     */
    public int lockOneLine(Long id, Date lockDate, String lockId, Date oldLockDate) {
        return orderSyncMapper.lockOneLine(id, lockDate, lockId, oldLockDate);
    }

    /**
     * 重新抢夺锁定一行记录, 只有lock_time锁定时间过长
     *
     * @param id
     * @param lockDate
     * @param outLockDate
     * @param lockId
     * @return
     */
    public int seizeLockOneLine(Long id, Date outLockDate, Date lockDate, String lockId) {
        return orderSyncMapper.seizeLockOneLine(id, lockDate, outLockDate, lockId);
    }

    /**
     * 释放锁，但不删除记录
     *
     * @param record
     * @return
     */
    public int unLockOneLine(OrderNeedToSyncPO record) {
        return orderSyncMapper.unLockOneLineByResourceUUid(record.getOrderUuid());
    }

    /**
     * 更新订单状态
     *
     * @param orderNeedToSyncPO
     * @return
     */
    public void updateOrderStatus(OrderNeedToSyncPO orderNeedToSyncPO) {
        orderSyncMapper.updateOrderStatus(orderNeedToSyncPO);
    }

    /**
     * 更新订单状态
     *
     * @param orderNeedToSyncPO
     * @return
     */
    public void insertOneOrder(OrderNeedToSyncPO orderNeedToSyncPO) {
        orderSyncMapper.insertOneOrder(orderNeedToSyncPO);
    }
}
