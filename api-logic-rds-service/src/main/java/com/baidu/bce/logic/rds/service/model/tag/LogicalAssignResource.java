package com.baidu.bce.logic.rds.service.model.tag;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by luping03 on 17/7/7.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class LogicalAssignResource {
    /**
     * 资源长id
     */
    @IdPermission
    private String instanceId;

    /**
     * 资源类型
     */
    private String serviceType;

    private List<Tag> tags;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    @Override
    public String toString() {
        return "LogicalAssignResource{"
                + "instanceId='" + instanceId + '\''
//                + ", instanceName='" + instanceName + '\''
                + ", serviceType='" + serviceType + '\''
                + ", tags=" + tags
                + '}';
    }
}
