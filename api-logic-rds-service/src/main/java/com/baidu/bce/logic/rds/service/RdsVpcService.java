package com.baidu.bce.logic.rds.service;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.request.ListSubnetRequest;
import com.baidu.bce.externalsdk.logical.network.vpc.model.SimpleVpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.VpcIdsRequest;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 用于封装中心云区域（例如：北京、保定、广州等区域）的 VPC、子网等逻辑。
 *  注意：
 *      1. 只可用于中心云，不可用于边缘云；
 *
 * <AUTHOR>
 * @since 12/5/22
 */
@Service
public class RdsVpcService {

    @Autowired
    private LogicRdsClientFactory clientFactory;

    /**
     * 批量查询 VPC 信息
     *
     * @param vpcUuids 批量个数不限
     * @return simpleVpcVos
     */
    public List<SimpleVpcVo> getSimpleVpcVosByVpcUuids(Collection<String> vpcUuids) {
        if (vpcUuids == null) {
            return null;
        }
        List<SimpleVpcVo> simpleVpcVos = new ArrayList<>();

        int batchSize = 10;
        VpcIdsRequest vpcIdsRequest = new VpcIdsRequest();
        vpcIdsRequest.setVpcIds(new ArrayList<String>(batchSize));
        for (String vpcUuid : vpcUuids) {
            vpcIdsRequest.getVpcIds().add(vpcUuid);
            if (vpcIdsRequest.getVpcIds().size() == batchSize) {
                simpleVpcVos.addAll(clientFactory.createVpcClient().get(vpcIdsRequest).values());
                vpcIdsRequest.getVpcIds().clear();
            }
        }
        if (vpcIdsRequest.getVpcIds().size() > 0) {
            simpleVpcVos.addAll(clientFactory.createVpcClient().get(vpcIdsRequest).values());
        }
        return simpleVpcVos;
    }

    /**
     * 批量查询子网信息
     *
     * @param subnetUuids 批量个数不限
     * @return subnetVos
     */
    public List<SubnetVo> getSubnetVosBySubnetUuids(Collection<String> subnetUuids) {
        if (subnetUuids == null) {
            return null;
        }
        List<SubnetVo> subnetVos = new ArrayList<>();

        int batchSize = 10;
        ListSubnetRequest listSubnetRequest = new ListSubnetRequest();
        listSubnetRequest.setSubnetIds(new ArrayList<String>(batchSize));
        for (String subnetUuid : subnetUuids) {
            listSubnetRequest.getSubnetIds().add(subnetUuid);
            if (listSubnetRequest.getSubnetIds().size() == batchSize) {
                subnetVos.addAll(clientFactory.createSubnetClient().listSubnet(listSubnetRequest));
                listSubnetRequest.getSubnetIds().clear();
            }
        }
        if (listSubnetRequest.getSubnetIds().size() > 0) {
            subnetVos.addAll(clientFactory.createSubnetClient().listSubnet(listSubnetRequest));
        }
        return subnetVos;
    }
}
