package com.baidu.bce.logic.rds.service;


import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.plat.resource.manager.sdk.ResManagerClient;
import com.baidu.bce.plat.resource.manager.sdk.SyncResClient;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupResInfo;
import com.baidu.bce.plat.resource.manager.sdk.model.Resource;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceBindRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.SyncResMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 封装与资源管理服务交互相关逻辑。
 * 资源管理服务接入文档：<a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tQvrZcAEPS/dOnzKCPWUX/ipZE7pS8sG_FN_"></a>
 */
@Service
public class RdsResourceManagerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RdsResourceManagerService.class);

    /**
     * 新增、更新
     */
    public static final String SYNC_TYPE_UPDATE = "update";
    /**
     * 全量对账
     */
    public static final String SYNC_TYPE_RECONCIE = "reconcile";
    /**
     * 删除
     */
    public static final String SYNC_TYPE_DELETE = "delete";

    private static final Long ACQUIRE_SYNC_LOCK_REQUEST_DURATION = 60000L;


    @Value("${console.instance.detail.page.url.temp:/rds/?#/rds/detail~instanceId=%s}")
    private String consoleInstanceDetailPageUrlTemp;


    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private LogicRdsClientFactory clientFactory;


    /**
     * 1. 新实例创建成功时，向资源管理服务发送 UPDATE 消息
     * 2. 主实例、只读实例、代理实例更新名字时，向资源管理服务发送 UPDATE 消息
     *
     * @param serviceType 产品线类型，取值：
     * @param accountId 账户ID
     * @param userId 用户ID
     * @param id 实例短ID
     * @param uuid 实例长ID
     * @param name 实例名称
     */
    public void sendUpdateNameMsg(String serviceType, String accountId, String userId, String id, String uuid,
                                  String name) {
        Resource resource = new Resource();
        resource.setId(id);
        resource.setUuid(uuid);
        resource.setName(name);
        send(SYNC_TYPE_UPDATE, serviceType, accountId, userId, Collections.singletonList(resource));
        LOGGER.debug("sendUpdateNameMsg, serviceType = {}, accountId = {}, id = {}, uuid = {}, name = {}",
                serviceType, accountId, id, uuid, name);
    }

    /**
     * 主实例、只读实例、代理实例删除时，向资源管理服务发送 DELETE 消息（实例释放进入回收站时，不需要发送该消息）
     *
     * @param serviceType 产品线类型，取值：
     * @param accountId 账户ID
     * @param userId 用户ID
     * @param id 实例短ID
     * @param uuid 实例长ID
     */
    public void sendDeleteMsg(String serviceType, String accountId, String userId, String id, String uuid) {
        Resource resource = new Resource();
        resource.setId(id);
        resource.setUuid(uuid);
        send(SYNC_TYPE_DELETE, serviceType, accountId, userId, Collections.singletonList(resource));
        LOGGER.debug("sendDeleteMsg, serviceType = {}, accountId = {}, id = {}, uuid = {}",
                serviceType, accountId, id, uuid);
    }


    /**
     * 无锁发送同步资源信息的消息。
     *
     * @param syncType 同步类型
     * @param serviceType 产品线类型，取值：
     * @param accountId 账户ID
     * @param userId 用户ID
     * @param resources 待同步的资源，调用该方法前，需要设置好属性：id（资源短ID）,uuid（资源长ID）, name（资源名称）
     */
    public void send(String syncType, String serviceType, String accountId, String userId, List<Resource> resources) {
        int batchSize = 100;
        String region = regionConfiguration.getCurrentRegion();

        // SyncResClient 跟 accountId 没关系
        SyncResClient syncResClient = clientFactory.getSyncResClient();

        SyncResMessage syncResMessage = new SyncResMessage();
        syncResMessage.setSyncType(syncType);
        syncResMessage.setResources(new ArrayList<Resource>(batchSize));
        for (Resource resource : resources) {
            // accountId, userId, type, region, url 为公共属性，这里统一设置，无需上层设置
            resource.setAccountId(accountId);
            resource.setUserId(userId);
            resource.setType(serviceType);
            resource.setRegion(region);
            // https://console.bce.baidu.com/rds/?#/rds/detail~instanceId=rdsmelbfl27n5xr
            resource.setUrl(String.format(consoleInstanceDetailPageUrlTemp, resource.getUuid()));

            syncResMessage.getResources().add(resource);
            if (syncResMessage.getResources().size() == batchSize) {
                syncResClient.send(serviceType, syncResMessage);

                syncResMessage = new SyncResMessage();
                syncResMessage.setSyncType(syncType);
                syncResMessage.setResources(new ArrayList<Resource>(batchSize));
            }
        }

        if (!syncResMessage.getResources().isEmpty()) {
            syncResClient.send(serviceType, syncResMessage);
        }
    }


    /**
     * 资源添加到资源管理资源组
     *
     * @param resourceType 产品线类型，取值：
     * @param accountId 账户ID
     * @param userId 用户ID
     * @param groupId 资源管理资源组ID
     * @param resourceIds 实例资源ID
     */
    public void bindResource(String resourceType, String accountId, String userId, String groupId,
                             List<String> resourceIds) {
        ResourceBindRequest resourceBindRequest = new ResourceBindRequest();
        resourceBindRequest.setBindings(new ArrayList<GroupResInfo>(resourceIds.size()));
        for (String resourceId : resourceIds) {
            GroupResInfo groupResInfo = new GroupResInfo();
            groupResInfo.setResourceId(resourceId);
            groupResInfo.setResourceType(resourceType);
            groupResInfo.setResourceRegion(regionConfiguration.getCurrentRegion());
            groupResInfo.setGroupId(groupId);
            groupResInfo.setAccountId(accountId);
            groupResInfo.setUserId(userId);
            groupResInfo.setCreateTime(new Timestamp(System.currentTimeMillis()));

            resourceBindRequest.getBindings().add(groupResInfo);
        }

        ResManagerClient resManagerClient = clientFactory.getResManagerClient(accountId);
        resManagerClient.bindResource(resourceBindRequest, true);
    }
}
