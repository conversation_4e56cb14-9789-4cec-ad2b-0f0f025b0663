package com.baidu.bce.logic.rds.service.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OpenApiInstanceMarkerListResponse {

    private String marker;

    private Boolean isTruncated = false;

    private String nextMarker;

    private Integer maxKeys; // 每页包含的最大数量

    private List<InstanceAbstract> instances;

    public String getMarker() {
        return marker;
    }

    public void setMarker(String marker) {
        this.marker = marker;
    }

    public Boolean getIsTruncated() {
        return isTruncated;
    }

    public void setIsTruncated(Boolean isTruncated) {
        this.isTruncated = isTruncated;
    }

    public String getNextMarker() {
        return nextMarker;
    }

    public void setNextMarker(String nextMarker) {
        this.nextMarker = nextMarker;
    }

    public Integer getMaxKeys() {
        return maxKeys;
    }

    public void setMaxKeys(Integer maxKeys) {
        this.maxKeys = maxKeys;
    }

    public List<InstanceAbstract> getInstances() {
        return instances;
    }

    public void setInstances(List<InstanceAbstract> instances) {
        this.instances = instances;
    }
}
