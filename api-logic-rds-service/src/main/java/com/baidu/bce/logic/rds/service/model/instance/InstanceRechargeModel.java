package com.baidu.bce.logic.rds.service.model.instance;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/7/15.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class InstanceRechargeModel {
    @NotNull
    private String instanceId;
    @Min(1)
    private Integer duration;

    private String serviceType;

    // resource id
    private String uuid;


    @Override
    public String toString() {
        return "DashboardInstanceRechargeModel{"
                + "instanceId='" + instanceId + '\''
                + ", duration=" + duration
                + '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
