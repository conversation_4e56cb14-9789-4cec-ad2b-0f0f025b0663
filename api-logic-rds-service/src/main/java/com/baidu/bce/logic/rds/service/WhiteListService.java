package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.FailinjectResponse;
import com.baidu.bce.internalsdk.rds.model.security.*;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * Created by luping03 on 17/11/3.
 */
@Service
public class WhiteListService {

    public static final String ip = "(^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(/(\\d|[1-2]\\d|3[0-2]))?$)|(^%$)";
    public static final Pattern IpPattern = Pattern.compile(ip);

    @Autowired
    LogicRdsClientFactory rdsClientFactory;

    @Autowired
    InstanceService instanceService;

    public SecurityIp getWhiteList(String instanceId) {
        instanceService.checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);
        return rdsClientFactory.createRdsClientByInstanceId(instanceId).queryWhiteList(instanceId);
    }

    public void setWhiteList(String instanceId, SecurityIpPutRequest request, String eTag) {
        instanceService.checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);
        StringBuilder stringBuilder = new StringBuilder();
        for (String ip : request.getSecurityIps()) {
            if (!IpPattern.matcher(ip).find()) {
                stringBuilder.append(ip).append(" ");
            }
        }
        if (!stringBuilder.toString().equals("")) {
            throw new RDSExceptions.ParamValidationException(stringBuilder.toString());
        }
        // rdsClientFactory.createRdsClient().updateWhiteList(instanceId, request.getSecurityIps(), eTag);
        rdsClientFactory.createRdsClientByInstanceId(instanceId)
                .updateWhiteList(instanceId, request.getSecurityIps(), eTag);
    }

    public SslInfoResponse getSslState(String instanceId) {
        return rdsClientFactory.createRdsClient().querySslInfo(instanceId);
    }

    public SslInfoResponse updateSslAccess(String instanceId, SslAccessibleRequest sslAccessibleRequest) {
        return rdsClientFactory.createRdsClient().updateSslAccessible(instanceId, sslAccessibleRequest);
    }

    public SslGetCaResponse queryCa() {
        return rdsClientFactory.createRdsClient().queryCa();
    }

    public FailinjectResponse updateProxyAccountIp(UpdateProxyIpMsg requestBody) {
        return rdsClientFactory.createRdsClient().updateProxyAccountIp(requestBody);
    }

    public UpdateProxyIpMsg getProxyAccountIp(String instanceId, String accountName) {
        return rdsClientFactory.createRdsClient().getProxyAccountIp(instanceId, accountName);
    }
}
