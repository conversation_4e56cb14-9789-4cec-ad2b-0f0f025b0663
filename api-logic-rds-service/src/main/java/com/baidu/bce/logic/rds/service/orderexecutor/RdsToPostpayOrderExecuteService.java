package com.baidu.bce.logic.rds.service.orderexecutor;

import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import com.baidu.bce.order.executor.sdk.model.MessageCenterModel;
import com.baidu.bce.order.executor.sdk.model.ReceiverType;
import com.baidubce.util.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by luping03 on 17/6/29.
 */
@Configuration
@Service
public class RdsToPostpayOrderExecuteService extends AbstractRdsExecutorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RdsToPostpayOrderExecuteService.class);

    private static final int RETRY_NUM = 3;
    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${sms.rds.toPostpay.success.tpl.id:smsTpl:b4bb7329-9ba6-4db9-9d54-5aef700681e0}")
    private String smsToPostpaySuccessId;

    @Value("${sms.rds.toPostpay.nosufficientbalance.tpl.id:smsTpl:24476704-9e27-4bda-b3d2-fbfc1e016cd4}")
    private String smsToPostpayNoSufficientBalanceId;

    @Value("${sms.order.create.failed.tpl.id:Tpl_2ef56822-636c-4c7d-9a0c-919925b5dd8a}")
    private String smsCreateFailedId;

    @Override
    public ExecutionResult execute(OrderClient client, ResourceClient resourceClient, Order order) {
        LOGGER.info("RdsToPostpayOrderExecuteService executeOrder [begin]"
                        + " [orderId:{},serviceType:{},orderStatus:{}, orderType:{}]",
                order.getUuid(), order.getServiceType(), order.getStatus().name(), order.getType());
        ExecutionResult executionResult = new ExecutionResult();
        Instance instance = getInstance(order, resourceClient);
        // 账户余额检查
        if (updateOrderStatus(client, order, OrderStatus.CREATED)) {
            // 更新为 created 成功, 发送短信
            setExecutionResult(executionResult, order, instance, null, null, true);
        } else {
            // 订单置为失败
            updateOrderStatus(client, order, OrderStatus.CREATE_FAILED);
            setExecutionResult(executionResult, order, instance, "[Billing] update Order Status failed.",
                    null, null);
            executionResult.setExecutionStatus(ExecutionStatus.FAILURE);
            executionResult.setMessageCenterModels(getOrderMessage(order, instance, false));
        }
        return executionResult;
    }

    private String genSMSContentString(Order order, Instance instance) {
        Map<String, String> content = new HashMap<>();
        content.put("instanceName", instance.getInstanceName());
        content.put("instanceId", instance.getInstanceId());
        content.put("region", getRegionDesc(instance.getRegion()));
        content.put("logicalZone", getLogicalZoneDesc(instance.getAzone(), getRegionDesc(instance.getRegion())));
        return JsonUtils.toJsonString(content);
    }

    @Override
    protected List<MessageCenterModel> getMessage(Order order, Instance instance, boolean succeed) {
        String[] receivers = {order.getAccountId(), order.getUserId()};
        MessageCenterModel smsMessage = new MessageCenterModel(getMessageTpl(succeed),
                genSMSContentString(order, instance), Joiner.on(',').join(receivers), ReceiverType.UserId);

        return Collections.singletonList(smsMessage);
    }

    protected List<MessageCenterModel> getOrderMessage(Order order, Instance instance, boolean succeed) {
        ServiceType serviceType = ServiceType.valueOf(order.getServiceType());
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("serviceType", serviceType.getFullName());
        contentMap.put("orderId", order.getUuid());
        String[] receivers = {order.getAccountId(), order.getUserId()};
        MessageCenterModel smsMessage = new MessageCenterModel(smsCreateFailedId,
                JsonUtils.toJsonString(contentMap), Joiner.on(',').join(receivers), ReceiverType.UserId);

        return Collections.singletonList(smsMessage);
    }

    /**
     * 获取region的中文描述
     *
     * @param region
     *
     * @return
     */
    public String getRegionDesc(String region) {
        String[] regions = RDSConstant.REGION_PAIRS.split(";");
        for (String regionTmp : regions) {
            String[] regionTmpArr = regionTmp.split("-");
            if (region.equalsIgnoreCase(regionTmpArr[0])) {
                return regionTmpArr[1];
            }
        }
        return region;
    }

    private String getLogicalZoneDesc(String azone, String region) {
        return region + "-" + azone.replaceAll("zone", "");
    }

    private Instance getInstance(Order order, ResourceClient resourceClient) {
        RDSClient2 rdsClient = clientFactory.createRdsClient2ByUserId(order.getAccountId());
        if (order.getItems().get(0).getExtra().equalsIgnoreCase("financial")) {
            rdsClient = clientFactory.createRdsClient2V2ByUserId(order.getAccountId());
        }
        Resource resource = resourceClient.get(order.getResourceIds().get(0));
        if (!resource.getName().isEmpty() && resource.getName().length() <= 12) {
            rdsClient = clientFactory.createRdsClient2V2ByUserId(order.getAccountId());
        }
        Instance instance = rdsClient.instanceDescribe(resource.getName()).getInstance();
        if (StringUtils.isNotEmpty(resource.getShortId())) {
            instance.setInstanceId(resource.getShortId());
        }
        return instance;
    }

    private boolean updateOrderStatus(OrderClient client, Order order, OrderStatus status) {
        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setStatus(status);
        updateOrderRequest.setServiceType(order.getServiceType());
        client.update(order.getUuid(), updateOrderRequest);
        return true;
    }

    @Override
    protected String getMessageTpl(boolean succeed) {
        return succeed ? smsToPostpaySuccessId : smsToPostpayNoSufficientBalanceId;
    }

    @Override
    public ExecutionResult check(OrderClient orderClient, ResourceClient resourceClient, Order order) {
        return null;
    }
}
