/*
 * Copyright (C) 2015 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.service.model.instance;

import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.MachinePO;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceGroupsDetailFull;

import java.util.ArrayList;
import java.util.List;

public class InstanceAbstract extends Instance {

    private String location;

    private boolean hasSlave = false;

    private int expireDate;

    private boolean hasProxy = false;

    private String vpcName;
    // 资源的唯一标示
    private String resourceUuid;

    private List<Tag> tags = new ArrayList<>();  // 标签

    private String task; // 相关任务，如自动续费（目前只有这个）

    private String orderStatus; //  相关联的计费变更订单的类型to_prepay to_postpay
    /**
     * vpc网段及子网掩码
     */
    private String vpcCidr;

    private List<String> zoneNames = new ArrayList<>(); // 用于api展示

    private List<MachinePO> dccHosts;

    private String category;

    // 实例名称，只给iam的实例列表接口使用，其他接口不要用
    private String name;

    private ResourceGroupsDetailFull resourceGroup;

    private String resourceGroupId;

    private String resourceGroupName;

    private boolean isChargeProxy = true;

    @Override
    public String toString() {
        return "InstanceAbstract{"
                  + "location='" + location + '\''
                  + '}';
    }

    public InstanceAbstract() {
    }

    public InstanceAbstract(Instance instance, String location) {
        super(instance);
        this.location = location;
//        if ("dcc".equalsIgnoreCase(this.getMachineType())) {
//            this.withDccHosts(dccHostConvert(instance.getDccHostIds()));
//        }
    }

    public InstanceAbstract(InstancePO instancePO) {
        this.withInstanceId(instancePO.getInstanceUuid())
                .withInstanceShortId(instancePO.getInstanceId())
                .withInstanceName(instancePO.getInstanceName())
                .withEngine(instancePO.getEngine())
                .withEngineVersion(instancePO.getEngineVersion())
                .withEndpoint(new Endpoint(instancePO.getEndpoint().getPort(), instancePO.getEndpoint().getAddress(),
                        instancePO.getEndpoint().getVnetIp(), instancePO.getEndpoint().getInetIp()))
                .withAllocatedMemoryInMB((int) (instancePO.getMemoryCapacity() * 1024))
                .withAllocatedMemoryInGB(instancePO.getMemoryCapacity())
                .withAllocatedStorageInGB(instancePO.getVolumeCapacity())
                .withUsedStorageInMB((int) (instancePO.getUsedStorage() * 1024))
                .withUsedStorageInGB(instancePO.getUsedStorage())
                .withCpuCount(instancePO.getCpuCount())
                .withInstanceType(instancePO.getInstanceType())
                .withSourceInstanceId(instancePO.getSourceInstanceId())
                .withInstanceStatus(instancePO.getInstanceStatus())
                .withEipStatus(instancePO.getEipStatus())
                .withBackupPolicy(new SnapshotPolicy(instancePO.getBackupPolicy().getBackupDays(),
                        instancePO.getBackupPolicy().getBackupTime(),
                        instancePO.getBackupPolicy().getPersistent(),
                        instancePO.getBackupPolicy().getExpireInDays(),
                        instancePO.getBackupPolicy().getBackupFreeSpace(),
                        instancePO.getBackupPolicy().getLogBackupRetainDays(),
                        instancePO.getBackupPolicy().getDataBackupType()))
                .withPubliclyAccessible(instancePO.isPubliclyAccessible())
                .withInstanceCreateTime(instancePO.getInstanceCreateTime())
                .withInstanceExpireTime(instancePO.getInstanceExpireTime())
                .withTopoly(new Topology(instancePO.getTopology().getMaster(),
                        instancePO.getTopology().getReadReplica(),
                        instancePO.getTopology().getRdsproxy()))
                .withProductType(instancePO.getProductType())
                .withAzone(instancePO.getZoneNames())
                .withSuperUserFlag(instancePO.getSuperUserFlag())
                .withReplicationType(instancePO.getReplicationType())
                .withApplicationType(instancePO.getApplicationType())
                .withNodeAmount(instancePO.getNodeAmount())
                .withMachineType(instancePO.getMachineType())
                .withTotalStorageInGB(instancePO.getTotalVolumeCapacity());
        this.withDccHosts(instancePO.getDccHosts());
    }

    public boolean getIsChargeProxy() {
        return isChargeProxy;
    }

    public void setIsChargeProxy(boolean chageProxy) {
        isChargeProxy = chageProxy;
    }

    public String getResourceGroupId() {
        return resourceGroupId;
    }

    public void setResourceGroupId(String resourceGroupId) {
        this.resourceGroupId = resourceGroupId;
    }

    public String getResourceGroupName() {
        return resourceGroupName;
    }

    public void setResourceGroupName(String resourceGroupName) {
        this.resourceGroupName = resourceGroupName;
    }

    public List<MachinePO> getDccHosts() {
        return dccHosts;
    }

    public Instance withDccHosts(List<MachinePO> dccHosts) {
        this.dccHosts = dccHosts;
        return this;
    }

    public List<String> getZoneNames() {
        return zoneNames;
    }

    public void setZoneNames(List<String> zoneNames) {
        this.zoneNames = zoneNames;
    }

    public String getTask() {
        return task;
    }

    public void setTask(String task) {
        this.task = task;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public String getResourceUuid() {
        return resourceUuid;
    }

    public void setResourceUuid(String resourceUuid) {
        this.resourceUuid = resourceUuid;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public boolean isHasSlave() {
        return hasSlave;
    }

    public void setHasSlave(boolean hasSlave) {
        this.hasSlave = hasSlave;
    }

    public int getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(int expireDate) {
        this.expireDate = expireDate;
    }

    public boolean isHasProxy() {
        return hasProxy;
    }

    public void setHasProxy(boolean hasProxy) {
        this.hasProxy = hasProxy;
    }

    public String getVpcName() {
        return vpcName;
    }

    public void setVpcName(String vpcName) {
        this.vpcName = vpcName;
    }

    public String getVpcCidr() {
        return vpcCidr;
    }

    public void setVpcCidr(String vpcCidr) {
        this.vpcCidr = vpcCidr;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ResourceGroupsDetailFull getResourceGroup() {
        return resourceGroup;
    }

    public void setResourceGroup(ResourceGroupsDetailFull resourceGroup) {
        this.resourceGroup = resourceGroup;
    }
}
