package com.baidu.bce.logic.rds.service;

import com.alibaba.fastjson.JSON;
import com.baidu.bce.common.network.common.permission.PermissionServiceForRds;
import com.baidu.bce.console.home.service.util.HomeServiceRequestContext;
import com.baidu.bce.externalsdk.logical.network.eni.EniExternalClient;
import com.baidu.bce.externalsdk.logical.network.eni.model.PrivateIp;
import com.baidu.bce.externalsdk.logical.network.eni.model.request.PrivateIpCheckRequest;
import com.baidu.bce.externalsdk.logical.network.subnet.ExternalSubnetClient;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.request.ListSubnetRequest;
import com.baidu.bce.externalsdk.logical.network.vpc.ExternalVpcClient;
import com.baidu.bce.externalsdk.logical.network.vpc.model.SimpleVpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.VpcIdsRequest;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.OrderClientV2;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.ResourceClientV2;
import com.baidu.bce.internalsdk.order.model.AdvancedOrderFilter;
import com.baidu.bce.internalsdk.order.model.Flavor;
import com.baidu.bce.internalsdk.order.model.GetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.ManualOpResponse;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.OrderType;
import com.baidu.bce.internalsdk.order.model.ProductPayType;
import com.baidu.bce.internalsdk.order.model.RefundRequest;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ResourceKey;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.internalsdk.order.model.UpdateResourceRequest;
import com.baidu.bce.internalsdk.rds.BccClient;
import com.baidu.bce.internalsdk.rds.BceKmsClient;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.RDSClient3;
import com.baidu.bce.internalsdk.rds.RDSLogicClient;
import com.baidu.bce.internalsdk.rds.model.BasicCategoryRequest;
import com.baidu.bce.internalsdk.rds.model.EffectiveTimeRequest;
import com.baidu.bce.internalsdk.rds.model.InstanceRegion;
import com.baidu.bce.internalsdk.rds.model.MaintainDurationRequest;
import com.baidu.bce.internalsdk.rds.model.MaintaintimeTasks;
import com.baidu.bce.internalsdk.rds.model.RetainCategoryRequest;
import com.baidu.bce.internalsdk.rds.model.SplitCopyRegionPolicyRequest;
import com.baidu.bce.internalsdk.rds.model.StatusResponse;
import com.baidu.bce.internalsdk.rds.model.SwitchMasterBackupRequest;
import com.baidu.bce.internalsdk.rds.model.TaskRequest;
import com.baidu.bce.internalsdk.rds.model.UpdateLoaclPolicyRequest;
import com.baidu.bce.internalsdk.rds.model.UpdateTaskRequest;
import com.baidu.bce.internalsdk.rds.model.UpdateEncryptPolicyReq;
import com.baidu.bce.internalsdk.rds.model.ViewPageResponse;
import com.baidu.bce.internalsdk.rds.model.MaintaintimeTasksResponse;

import com.baidu.bce.internalsdk.rds.model.bcc.BccInstanceDetail;
import com.baidu.bce.internalsdk.rds.model.blb.GetLbdcClusterResponse;
import com.baidu.bce.internalsdk.rds.model.blb.LbdcCluster;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeSubnet;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeVpc;
import com.baidu.bce.internalsdk.rds.model.group.GroupInfo;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupDetailResponse;
import com.baidu.bce.internalsdk.rds.model.instance.AzChangeParams;
import com.baidu.bce.internalsdk.rds.model.instance.AzoneInfo;
import com.baidu.bce.internalsdk.rds.model.instance.BccConnectList;
import com.baidu.bce.internalsdk.rds.model.instance.BccInnerConnectDetail;
import com.baidu.bce.internalsdk.rds.model.instance.BccInstanceListRequest;
import com.baidu.bce.internalsdk.rds.model.instance.BcmGroupResponses;
import com.baidu.bce.internalsdk.rds.model.instance.ClusterStatusResponce;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceAzone;
import com.baidu.bce.internalsdk.rds.model.instance.CreateParams;
import com.baidu.bce.internalsdk.rds.model.instance.HotUpgradeResponse;
import com.baidu.bce.internalsdk.rds.model.instance.HotupgradeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceChangeDiskTypeResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCheckPortResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCreateRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceListResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceMoreDetailRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceOpenTdeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePnetIpResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePrecheckParameterRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstancePrecheckParameterResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceQuotChangeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceQuotTimeDetailResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceReplicaDelayMaster;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceRestoreRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceTdeStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateAddressRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateNameRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdatePortRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdatePublicAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateReplicaOnLineRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateReplicationTypeRequest;
import com.baidu.bce.internalsdk.rds.model.instance.OrderItemExtraInfo;
import com.baidu.bce.internalsdk.rds.model.instance.OrderStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.PrecheckResourceCreateRequest;
import com.baidu.bce.internalsdk.rds.model.instance.PrecheckResourceCreateResponse;
import com.baidu.bce.internalsdk.rds.model.instance.ProbeInstanceResponse;
import com.baidu.bce.internalsdk.rds.model.instance.ProxyTopoInfo;
import com.baidu.bce.internalsdk.rds.model.instance.PublicNetworkConnDetail;
import com.baidu.bce.internalsdk.rds.model.instance.PublicNetworkConnectMessage;
import com.baidu.bce.internalsdk.rds.model.instance.PublicNetworkRequest;
import com.baidu.bce.internalsdk.rds.model.instance.PutFlowRequest;
import com.baidu.bce.internalsdk.rds.model.instance.RecoveryToSourceInstanceRequest;
import com.baidu.bce.internalsdk.rds.model.instance.ResizeParams;
import com.baidu.bce.internalsdk.rds.model.instance.SlaInstanceResponse;
import com.baidu.bce.internalsdk.rds.model.instance.UserKmsListResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.BackupUsageResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.DataBackupRetainStrategy;
import com.baidu.bce.internalsdk.rds.model.security.V2ListRequest;
import com.baidu.bce.internalsdk.rds.model.security.V2SecurityIpResponse;
import com.baidu.bce.internalsdk.rds.model.security.V2SecurityIps;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotGetResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.baidu.bce.internalsdk.rds.util.PatternString;
import com.baidu.bce.logic.core.constants.Payment;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.InnerConnectCheckPO;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.MachinePO;
import com.baidu.bce.logic.rds.dao.model.OrderNeedToSyncPO;
import com.baidu.bce.logic.rds.dao.model.PublicNetworkConnectCheckPO;
import com.baidu.bce.logic.rds.dao.model.SubnetPO;
import com.baidu.bce.logic.rds.dao.mybatis.ConnectCheckMapper;
import com.baidu.bce.logic.rds.service.constant.EngineType;
import com.baidu.bce.logic.rds.service.constant.InstanceTableColums;
import com.baidu.bce.logic.rds.service.constant.InstanceType;
import com.baidu.bce.logic.rds.service.constant.RdsInstanceStatus;
import com.baidu.bce.logic.rds.service.constant.TagsChangeType;
import com.baidu.bce.logic.rds.service.datasync.service.OrderNeedToSyncService;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.ListRequest;
import com.baidu.bce.logic.rds.service.model.RdsListRequest;
import com.baidu.bce.logic.rds.service.model.WhiteAccountResult;
import com.baidu.bce.internalsdk.rds.model.instance.GlobalInstanceResponses;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import com.baidu.bce.logic.rds.service.model.instance.LogicInstanceCreateResponse;
import com.baidu.bce.logic.rds.service.model.order.RdsCreateOrderRequestVo;
import com.baidu.bce.logic.rds.service.model.otherservice.ZoneDetailList;
import com.baidu.bce.logic.rds.service.model.pricing.AutoRenew;
import com.baidu.bce.logic.rds.service.model.pricing.OrderIdsRequest;
import com.baidu.bce.logic.rds.service.model.pricing.Price;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.baidu.bce.logic.rds.service.model.tag.AssignTagRequest;
import com.baidu.bce.logic.rds.service.model.tag.LogicalAssignResource;
import com.baidu.bce.logic.rds.service.permission.LocalMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.permission.RdsToMasterShortIdConverter;
import com.baidu.bce.logic.rds.service.resource.ResourceGroupService;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.CheckIp;
import com.baidu.bce.logic.rds.service.util.IDGenerator;
import com.baidu.bce.logic.rds.service.util.InstanceForApiUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.ParamComparator;
import com.baidu.bce.logic.rds.service.util.ParamComparatorByIp;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.logic.rds.service.util.TagOfInstanceAbstractComparator;
import com.baidu.bce.logic.rds.service.util.ThreadPool;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.plat.resource.manager.sdk.SyncLockClient;
import com.baidu.bce.plat.resource.manager.sdk.SyncResClient;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupDetailRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupDetailResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceBrief;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceGroupsDetailFull;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.sdk.renew.AutoRenewClient;
import com.baidu.bce.sdk.renew.model.AutoRenewCreateRequest;
import com.baidu.bce.sdk.renew.model.AutoRenewRule;
import com.baidu.bce.user.settings.sdk.model.FeatureAclRequest;
import com.baidubce.services.kms.KmsClient;
import com.baidubce.services.kms.model.ListKeysRequest;
import com.baidubce.services.kms.model.ListKeysResponse;
import com.baidubce.util.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.util.SubnetUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;

import static com.baidu.bce.logic.rds.service.util.RDSConstant.RDS_CATEGORYS;

/**
 * Created by luping03 on 17/11/2.
 */
@Service
public class InstanceService {
    private static final Logger logger = LoggerFactory.getLogger(InstanceService.class);

    public static final long ONE_DAY_MILLISECOND = 1000 * 60 * 60 * 24;

    @Value("${instance.release.success.tpl:smsTpl:498b8c8b-b877-4276-a7b8-971eeede56f0}")
    String instanceReleaseSuccessSmsTpl;

    @Value("${instance.release.fail.tpl:smsTpl:62f2e58c-49ae-4402-91f0-d1d1c2ea336e}")
    String instanceReleaseFailSmsTpl;

    @Value("${masterInstance.release.success.tpl:smsTpl:6323e041-2438-43fe-b59b-9992e6dde6d9}")
    String masterInstanceReleaseSuccessSmsTpl;

    @Value("${rds.special.physicalzone:}")
    private String specialPhysicalZone;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private RdsOrderService rdsOrderService;

    @Autowired
    private OthersService othersService;

    @Autowired
    private TagLogicalService rdsTagLogicalService;

    @Autowired
    private EdgeService edgeService;

    @Autowired
    private RdsVpcWrapperService rdsVpcWrapperService;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    OrderNeedToSyncService orderNeedToSyncService;

    @Autowired
    QuotaValidatorFactory quotaValidatorFactory;

    @Autowired
    IDGenerator idGenerator;

    @Autowired
    InstanceDao instanceDao;

    @Autowired
    InstanceForApiUtils instanceForApiUtils;

    @Autowired
    private IdMapperService idMapperService;

    @Autowired
    private PermissionServiceForRds rermissionServiceForRds;

    @Autowired
    private RdsToMasterShortIdConverter rdsToMasterShortIdConverter;

    @Value("#{'${rds.financial.region:fsh}'.split(',')}")
    private String[] financialRegion;

    @Autowired
    ResourceGroupService resourceGroupService;

    @Autowired
    private RdsAsyncService rdsAsyncService;

    @Autowired
    private PricingService pricingService;

    @Autowired
    private RdsBillingService rdsBillingService;

    @Autowired
    private SwitchStrategyService strategyService;

    @Autowired
    private ConnectCheckMapper innerMapper;

    public void updateBackupPolicy(String instanceId, final SnapshotPolicy policy) {
        checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_REBOOT);

        Instance instance = clientFactory.createRdsClient2ByInstanceId(instanceId)
                .instanceDescribe(instanceId).getInstance();
        // 三节点增强版支持日志备份10年
        if (StringUtils.isNotEmpty(instance.getApplicationType())
                && instance.getApplicationType().equalsIgnoreCase("enhanced")) {
            if (policy.getPersistent()
                    && (policy.getExpireInDays() < 1
                    || policy.getExpireInDays() > 3660 )) {
                throw new RDSBusinessExceptions.ParamValidationException();
            }
        } else {
            if (policy.getPersistent()
                    && (policy.getExpireInDays() < 1
                    || policy.getExpireInDays() > 730 )) {
                throw new RDSBusinessExceptions.ParamValidationException();
            }
        }

        // 更新备份策略时，若使用快照备份，需限制下必须为云盘类型的实例
        if (StringUtils.isNotEmpty(policy.getDataBackupType())
                && "snapshot".equalsIgnoreCase(policy.getDataBackupType())) {
            if (StringUtils.isNotEmpty(instance.getDiskType()) && "ssd".equalsIgnoreCase(instance.getDiskType())) {
                throw new RDSExceptions.BackupException();
            }
        }
        List<DataBackupRetainStrategy> retainStrategys = new ArrayList<>();
        // 阶梯备份时，保存每个阶梯的当前时间
        long preTime = 0;
        // 首先映射备份保留时长字段，即expireInDays
        DataBackupRetainStrategy item = new DataBackupRetainStrategy();
        item.setStartSeconds(preTime);
        item.setEndSeconds(preTime - RDSConstant.SECONDS_OF_ONE_DAY * policy.getExpireInDays());
        preTime = item.getEndSeconds();
        retainStrategys.add(item);
        logger.debug("retainStrategys.get(0).getStartSeconds() : {}, retainStrategys.get(0).getEndSeconds : {}",
                retainStrategys.get(0).getStartSeconds(), retainStrategys.get(0).getEndSeconds());
        // 阶梯备份时，参数校验与转化
       if (policy.getRetentionPeriod() != null && StringUtils.isNotEmpty(policy.getTimeUnit())) {
           logger.debug("policy.retentionPeriod is {}, policy.timeUnit is {}", policy.getRetentionPeriod(),
                   policy.getTimeUnit());
           // RDSConstant.TIME_UNITS.stream().anyMatch(item -> item.equalsIgnoreCase(policy.getTimeUnit()))
           if (RDSConstant.TIME_UNITS.contains(policy.getTimeUnit().toLowerCase())) {
               // 阶梯备份参数有问题时，默认按周处理
               long timeUnit = RDSConstant.TIME_UNIT_MONTH.equalsIgnoreCase(policy.getTimeUnit()) ? 30 : 7;

               for (int i = 0; i < policy.getRetentionPeriod(); i++) {
                   DataBackupRetainStrategy req = new DataBackupRetainStrategy();
                   req.setStartSeconds(preTime);
                   req.setEndSeconds(preTime - RDSConstant.SECONDS_OF_ONE_DAY * timeUnit);
                   // 更新当前时间
                   preTime = req.getEndSeconds();
                   retainStrategys.add(req);
                   logger.debug("retainStrategys.get(i).getStartSeconds() : {}, retainStrategys.get(i).getEndSeconds : {}",
                           retainStrategys.get(i + 1).getStartSeconds(), retainStrategys.get(i + 1).getEndSeconds());
               }
           }
       }
        policy.setDataBackupRetainStrategys(retainStrategys);

        // 若开启跨地域备份，则需数据处理 dataBackupCopyRetainDays -> dataBackupCopyStoragesRegions
        if (policy.getDataBackupCopyEnable()) {
            logger.info("handle update across region backup policy, dataBackupCopyRetainDays is : {}",
                    policy.getDataBackupCopyRetainDays());
            List<DataBackupRetainStrategy> retainCopyStrategys = new ArrayList<>();
            // 阶梯备份时，保存每个阶梯的当前时间
            long preCopyTime = 0;
            // 首先映射备份保留时长字段，即expireInDays
            DataBackupRetainStrategy copyRegionItem = new DataBackupRetainStrategy();
            copyRegionItem.setStartSeconds(preCopyTime);
            copyRegionItem.setEndSeconds(preCopyTime - RDSConstant.SECONDS_OF_ONE_DAY * policy.getDataBackupCopyRetainDays());
            preCopyTime = copyRegionItem.getEndSeconds();
            retainCopyStrategys.add(copyRegionItem);
            policy.setDataBackupCopyRetainStrategys(retainCopyStrategys);
        }


        clientFactory.createRdsClient2().instanceModifyBackupPolicy(instanceId, policy);

        Map<String, Object> map = new HashMap<>();
        map.put(InstanceTableColums.backupDays, policy.getBackupDays());
        map.put(InstanceTableColums.backupTime, policy.getBackupTime());
        map.put(InstanceTableColums.persistent, policy.getPersistent());
        map.put(InstanceTableColums.expireInDays, policy.getPersistent() ? policy.getExpireInDays() : 0);
        instanceDao.updateInstanceByInstanceUuid(map, instanceId, clientFactory.getAccountId());
    }

    public BackupUsageResponse getBackupUsage(String instanceId) {
        return clientFactory.createRdsClient2().getBackupUsage(instanceId);
    }


    public void restore(String instanceId, InstanceRestoreRequest request) {
        clientFactory.createRdsClient().instanceRestoreSnapshot(instanceId, request);
    }

    public InstanceExtension detail(String instanceId, String from) {
        InstanceExtension instanceExtension = null;
        String oldInstanceId = instanceId;

        InstancePO instancePO;
        if (BasisUtils.isShortId(instanceId)) {
            instancePO = instanceDao.queryAllInstanceByInstanceId(instanceId, clientFactory.getAccountId());
            if (instancePO == null) {
                throw new RDSBusinessExceptions.InstanceNotExist();
            }
            // 实例创建失败后，实例详情接口返回实例，状态为failed
            if (instancePO.getInstanceStatus().equalsIgnoreCase(RdsInstanceStatus.FAILED.getValue())) {
                instanceExtension = new InstanceExtension(instancePO);
                return instanceExtension;
            } else if (instancePO.getInstanceStatus().equalsIgnoreCase("deleted")) {
                throw new RDSBusinessExceptions.InstanceNotExist();
            }
            if (instancePO.getInstanceStatus().equalsIgnoreCase(RdsInstanceStatus.CREATING.getValue())) {
                instanceExtension = new InstanceExtension(instancePO);
//                    logger.info("instancePO.getInstanceStatus():" , instancePO.getInstanceStatus());
            } else {
                instanceExtension = getInstanceExtension(instancePO.getInstanceUuid());
                instanceExtension.setInstanceShortId(instancePO.getInstanceId());
            }
        } else {
            instanceExtension = getInstanceExtension(instanceId);
            instanceExtension.setInstanceShortId(instanceDao.queryInstanceId(instanceExtension.getInstanceId()));
        }
        instanceExtension.setRegion(regionConfiguration.getCurrentRegion());
        instanceExtension.setTimestamp(new Date(System.currentTimeMillis()));
        bindOrderStatusToInstanceExtension(instanceExtension);
        bindResourceGroupToInstanceDetail(instanceExtension);

        // TODO 历史数据，物理az转逻辑az
        setZoneType(instanceExtension);
        setVpcDetailForInstanceExtension(instanceExtension);
        setSubnetDetailForInstanceExtension2(instanceExtension);
        setRogroupVpcAndSubnetInstanceExtension(instanceExtension);
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            try {
                unitPrice(instanceExtension);
            }catch (Exception e){
                logger.error("show unitPrice error", e);
            }
            instanceForApiUtils.standardInstanceExtension(oldInstanceId, instanceExtension);

            // 设置系列信息，这部分有遗留问题，需要做特殊处理，Category为空的情况下，使用applicationType填充
            if (StringUtils.isBlank(instanceExtension.getCategory())) {
                instanceExtension.setCategory(instanceExtension.getApplicationType());
            }

        }

        attachEdgeRegionInfo(Collections.singletonList(instanceExtension));
        // 阶梯备份策略、跨地域备份策略时兼容处理备份策略字段字段
        attachBackupPolicy(instanceExtension);

        Map<String, List<Tag>> tagMap = rdsTagLogicalService.queryTagsByResourceUuids(
                Collections.singletonList(instanceExtension.getInstanceId()));
        if (tagMap != null && tagMap.containsKey(instanceExtension.getInstanceId())) {
            instanceExtension.setTags(tagMap.get(instanceExtension.getInstanceId()));
        }
        return instanceExtension;
    }

    private void setZoneType(InstanceExtension instanceExtension) {
        logger.debug("set zone type");
        if (instanceExtension == null) {
            logger.debug("pzone to zone type is null");
            return;
        }
        List<String> specialZones = Arrays.asList(specialPhysicalZone.split(","));
        if (!instanceExtension.getPhysicalZone().isEmpty()) {
            Set<Map.Entry<String, String>> entries = instanceExtension.getPhysicalZone().entrySet();
            for (Map.Entry<String, String> entry : entries) {
                if (specialZones.contains(entry.getValue())) {
                    entry.setValue("lcc");
                    instanceExtension.setZoneType("lcc");
                    logger.debug("zone type is lcc");
                } else {
                    entry.setValue("normal");
                    instanceExtension.setZoneType("normal");
                    logger.debug("zone type is normal");
                }
            }
        }

    }

    /**
     *
     * 管控不维护 expireInDays 字段，console 从阶梯备份中读取并维护
     * 此处 console 需处理retentionPeriod timeUnit expireInDays
     * @param instanceExtension
     */
    private void attachBackupPolicy(InstanceExtension instanceExtension) {
        List<DataBackupRetainStrategy> dataBackupRetainStrategys
                = instanceExtension.getBackupPolicy().getDataBackupRetainStrategys();
        if (CollectionUtils.isNotEmpty(dataBackupRetainStrategys)) {
            logger.debug("dataBackupRetainStrategys.size() : {}", dataBackupRetainStrategys.size());
            // 取出约定好的第一个元素用来填充 expireInDays
            DataBackupRetainStrategy expireItem = dataBackupRetainStrategys.get(0);
            long durTime = expireItem.getStartSeconds() - expireItem.getEndSeconds();
            int expireInDays = (int) (durTime / RDSConstant.SECONDS_OF_ONE_DAY);
            logger.debug("backup policy detail, first startSecond is : {0}, endSecond is : {1}, expireInDays is : {2}",
                    expireItem.getStartSeconds(), expireItem.getEndSeconds(), expireInDays);
            instanceExtension.getBackupPolicy().setExpireInDays(expireInDays);
            if (dataBackupRetainStrategys.size() >= 2) {
                // 填充备份周期与份数
                instanceExtension.getBackupPolicy().setRetentionPeriod(dataBackupRetainStrategys.size() - 1);
                DataBackupRetainStrategy backupRetainStrategy =
                        dataBackupRetainStrategys.get(dataBackupRetainStrategys.size() - 1);
                int days = (int) ((backupRetainStrategy.getStartSeconds() - backupRetainStrategy.getEndSeconds())
                        / RDSConstant.SECONDS_OF_ONE_DAY);
                String timeUnit = "";
                switch (days) {
                    case 30 :
                        timeUnit = RDSConstant.TIME_UNIT_MONTH;
                        break;
                    case 7 :
                        timeUnit = RDSConstant.TIME_UNIT_WEEK;
                        break;
                    default:
                        logger.error("retain strategy time unit error, last is : {}", days);
                        break;
                }
                instanceExtension.getBackupPolicy().setTimeUnit(timeUnit);
                // 对 fe 隐藏无需感知的字段
                instanceExtension.getBackupPolicy().setDataBackupRetainStrategys(new ArrayList<DataBackupRetainStrategy>());
            }
        } else {
            logger.error("rds backup policy detail retain strategy is null.");
        }

        // 处理跨地域备份策略字段
        if (instanceExtension.getBackupPolicy().getDataBackupCopyEnable()) {
            // 若开启跨地域备份，则处理数据
            if (CollectionUtils.isNotEmpty(instanceExtension.getBackupPolicy().getDataBackupCopyRetainStrategys())) {
                logger.debug("dataBackupCopyRetainStrategys.size() : {}",
                        instanceExtension.getBackupPolicy().getDataBackupCopyRetainStrategys().size());
                // 取出约定好的第一个元素用来填充 dataBackupCopyRetainDays
                DataBackupRetainStrategy expireItem =
                        instanceExtension.getBackupPolicy().getDataBackupCopyRetainStrategys().get(0);
                long durTime = expireItem.getStartSeconds() - expireItem.getEndSeconds();
                int expireCopyInDays = (int) (durTime / RDSConstant.SECONDS_OF_ONE_DAY);
                instanceExtension.getBackupPolicy().setDataBackupCopyRetainDays(expireCopyInDays);
                logger.debug("backup policy detail, first startSecond is : {0}, endSecond is : {1}, expireInDays is : {2}",
                        expireItem.getStartSeconds(), expireItem.getEndSeconds(), expireCopyInDays);
            } else {
                logger.error("rds backup across region policy detail retain strategy is null.");
            }
        }

    }

    private void attachEdgeRegionInfo(Collection<? extends Instance> instances) {
        if (!rdsVpcWrapperService.isEdgeRegion()) {
            return;
        }
        if (CollectionUtils.isEmpty(instances)) {
            return;
        }
        // regionId <-> regionName
        Map<String, String> regionIdRegionNameMap = edgeService.getRegionIdRegionNameMap();
        for (Instance instance : instances) {
            if (StringUtils.isEmpty(instance.getEdgeRegion())) {
                continue;
            }
            instance.setEdgeRegionName(regionIdRegionNameMap.get(instance.getEdgeRegion()));
        }
        // vpcUuid <-> vpcId
        Set<String> vpcUuids = new HashSet<>();
        for (Instance instance : instances) {
            if (StringUtils.isEmpty(instance.getEdgeVpcId())) {
                continue;
            }
            vpcUuids.add(instance.getEdgeVpcId());
        }
        if (!vpcUuids.isEmpty()) {
            Map<String, EdgeVpc> vpcUuidMap = edgeService.getVpcUuidMap(vpcUuids);
            for (Instance instance : instances) {
                if (StringUtils.isEmpty(instance.getEdgeVpcId())) {
                    continue;
                }
                if (!vpcUuidMap.containsKey(instance.getEdgeVpcId())) {
                    logger.warn("vpcUuid = {}, edgeVpc is null.", instance.getEdgeVpcId());
                    continue;
                }
                instance.setEdgeVpcId(vpcUuidMap.get(instance.getEdgeVpcId()).getVpcId());
            }
        }
        // subnetUuid <-> subnetId
        Set<String> subnetUuids = new HashSet<>();
        for (Instance instance : instances) {
            if (StringUtils.isEmpty(instance.getEdgeSubnetId())) {
                continue;
            }
            subnetUuids.add(instance.getEdgeSubnetId());
        }
        if (!subnetUuids.isEmpty()) {
            Map<String, EdgeSubnet> subnetUuidMap = edgeService.getSubnetUuidMap(subnetUuids);
            for (Instance instance : instances) {
                if (StringUtils.isEmpty(instance.getEdgeSubnetId())) {
                    continue;
                }
                if (!subnetUuidMap.containsKey(instance.getEdgeSubnetId())) {
                    logger.warn("subnetUuid = {}, edgeSubnet is null.", instance.getEdgeSubnetId());
                    continue;
                }
                instance.setEdgeSubnetId(subnetUuidMap.get(instance.getEdgeSubnetId()).getSubnetId());
            }
        }
    }

    /**
     * openapi 批量查询实例详情
     * @param moreDetailRequest
     * @param from
     * @return
     */
    public List<InstanceExtension> moreDetail(InstanceMoreDetailRequest moreDetailRequest, String from) {
        List<InstanceExtension> instanceExtensionList = new ArrayList<>();

        try {
            List<InstancePO> instancePOList = null;
            if (moreDetailRequest != null && !CollectionUtils.isEmpty(moreDetailRequest.getIds())) {
                instancePOList = instanceDao.queryInstanceByInstanceIds(moreDetailRequest.getIds(),
                        clientFactory.getAccountId());
            }
            if (instancePOList == null || instancePOList.size() <= 0) {
                throw new RDSExceptions.ResourceNotExistException();
            }

            for (InstancePO instancePO : instancePOList) {
                InstanceExtension instanceExtension = new InstanceExtension();

                if (instancePO.getInstanceStatus().equalsIgnoreCase(RdsInstanceStatus.CREATING.getValue())) {
                    instanceExtension = new InstanceExtension(instancePO);
//                    logger.info("instancePO.getInstanceStatus():" , instancePO.getInstanceStatus());
                } else {
                    instanceExtension = getInstanceExtension(instancePO.getInstanceUuid());
                    instanceExtension.setInstanceShortId(instancePO.getInstanceId());
                }

                instanceExtension.setRegion(regionConfiguration.getCurrentRegion());
                instanceExtension.setTimestamp(new Date(System.currentTimeMillis()));
                bindOrderStatusToInstanceExtension(instanceExtension);
                // TODO 历史数据，物理az转逻辑az
                setVpcDetailForInstanceExtension(instanceExtension);
                setSubnetDetailForInstanceExtension2(instanceExtension);


                instanceForApiUtils.standardInstanceExtension(null, instanceExtension);

                // 设置系列信息，这部分有遗留问题，需要做特殊处理，Category为空的情况下，使用applicationType填充
                if (StringUtils.isBlank(instanceExtension.getCategory())) {
                    instanceExtension.setCategory(instanceExtension.getApplicationType());
                }

                instanceExtensionList.add(instanceExtension);
            }
        } catch (BceInternalResponseException ex) {
            LogicRdsExceptionHandler.handle(ex);
        }

        return instanceExtensionList;
    }



    public LogicPageResultResponse<InstanceAbstract> listInstanceWithPageByMultiKey(RdsListRequest listRequest,
                                                                                    String machineType,
                                                                                    Boolean onlyFilterMaster) {
        String order = (listRequest.getOrders() == null || listRequest.getOrders().size() == 0)
                ? null : listRequest.getOrders().get(0).getOrder();
        String orderBy = (listRequest.getOrders() == null || listRequest.getOrders().size() == 0)
                ? null : listRequest.getOrders().get(0).getOrderBy();

        List<InstanceAbstract> instances;
        if ("DCC".equalsIgnoreCase(machineType)) {
            instances = listInstanceOnDcc(
                    listRequest.getDaysToExpiration(), order, orderBy, listRequest.getFilterMap(),
                    listRequest.getServiceType());
        } else {
            instances = listInstanceByExpiredDate(
                    listRequest.getDaysToExpiration(), order, orderBy, listRequest.getFilterMap(),
                    listRequest.getServiceType(), RDSConstant.FROM_CONSOLE);
        }

        instances = (List<InstanceAbstract>)instanceForApiUtils.standardInstanceAbstractList(
                instances, RDSConstant.FROM_CONSOLE, listRequest.getFilterMap(), onlyFilterMaster);
        instances = reorderInstanceList(instances, order, orderBy);
        // 查询绑定资源信息
        instances = bindResourceToInstance(instances);
        // 资源筛选
        instances = resourceGroupService.filterResourceGroup(
                instances, listRequest.getFilterMap(),
                listRequest.getPageNo(), listRequest.getPageSize());
        resolvePost(instances);
        Map<String, Object> masterInstanceIdMap = new HashMap<>();
        for (InstanceAbstract instance : instances) {
            if (instance.getSourceInstanceId() == null || "".equals(instance.getSourceInstanceId())) {
                masterInstanceIdMap.put(instance.getInstanceId(), instance);
            }
        }

        LogicPageResultResponse<InstanceAbstract> response = listForPage(
                instances, order, orderBy, listRequest.getPageNo(), listRequest.getPageSize(), masterInstanceIdMap);
//        logger.info("listForPage:" + response.getResult().size());

//        for (InstanceAbstract abs : response.getResult()) {
//            logger.info("InstanceAbstracts(listForPage): instanceId:{},instanceUUid:{} ", abs.getInstanceShortId(), abs.getInstanceId());
//
//        }
//        response.setResult(instanceForApiUtils.standardInstanceAbstractList(
//                response.getResult(), RDSConstant.FROM_CONSOLE, listRequest.getFilterMap()));
        attachEdgeRegionInfo(response.getResult());
        // 查询并绑定自动续费到实例（task）
        bindAutoRenew(response.getResult());
        return response;
    }

    public LogicPageResultResponse<InstanceAbstract> listforbcm(RdsListRequest listRequest,
                                                                String machineType,
                                                                Boolean onlyFilterMaster,
                                                                String instanceType) {
        String order = (listRequest.getOrders() == null || listRequest.getOrders().size() == 0)
                ? null : listRequest.getOrders().get(0).getOrder();
        String orderBy = (listRequest.getOrders() == null || listRequest.getOrders().size() == 0)
                ? null : listRequest.getOrders().get(0).getOrderBy();

        List<InstanceAbstract> instances;
        if ("DCC".equalsIgnoreCase(machineType)) {
            instances = listInstanceOnDcc(
                    listRequest.getDaysToExpiration(), order, orderBy, listRequest.getFilterMap(),
                    listRequest.getServiceType());
        } else {
            instances = listInstanceByExpiredDate(
                    listRequest.getDaysToExpiration(), order, orderBy, listRequest.getFilterMap(),
                    listRequest.getServiceType(), RDSConstant.FROM_CONSOLE);
        }

        instances = (List<InstanceAbstract>) instanceForApiUtils.standardInstanceAbstractList(
                instances, RDSConstant.FROM_CONSOLE, listRequest.getFilterMap(), onlyFilterMaster);
        // 查询绑定资源信息
        instances = bindResourceToInstance(instances);
        // 资源筛选
        instances = resourceGroupService.filterResourceGroup(
                instances, listRequest.getFilterMap(),
                listRequest.getPageNo(), listRequest.getPageSize());

        instances = reorderInstanceList(instances, order, orderBy);

        resolvePost(instances);
        Map<String, Object> masterInstanceIdMap = new HashMap<>();
        for (InstanceAbstract instance : instances) {
            if (instance.getSourceInstanceId() == null || "".equals(instance.getSourceInstanceId())) {
                masterInstanceIdMap.put(instance.getInstanceId(), instance);
            }
        }
        List<InstanceAbstract> instancesFilter = new ArrayList<>();
        LogicPageResultResponse<InstanceAbstract> response = null;
        if (StringUtils.isNotBlank(instanceType)) {
            if ("mysqlmaster".equals(instanceType)) {
                for (InstanceAbstract instance : instances) {
                    if ("MySQL".equals(instance.getEngine()) && "master".equals(instance.getInstanceType())) {
                        instancesFilter.add(instance);
                    }
                }
            }else if ("mysqlrdsproxy".equals(instanceType)) {
                for (InstanceAbstract instance : instances) {
                    if ("MySQL".equals(instance.getEngine()) && "rdsproxy".equals(instance.getInstanceType())) {
                        instancesFilter.add(instance);
                    }
                }
            }else if ("mysqlreadreplica".equals(instanceType)) {
                for (InstanceAbstract instance : instances) {
                    if ("MySQL".equals(instance.getEngine()) && "readReplica".equals(instance.getInstanceType())) {
                        instancesFilter.add(instance);
                    }
                }
            }else if ("postgresql".equals(instanceType)) {
                for (InstanceAbstract instance : instances) {
                    if ("postgresql".equals(instance.getEngine())) {
                        instancesFilter.add(instance);
                    }
                }
            }else if ("sqlserver".equals(instanceType)) {
                for (InstanceAbstract instance : instances) {
                    if ("sqlserver".equals(instance.getEngine())) {
                        instancesFilter.add(instance);
                    }
                }
            }
            response = listForPageBybcm(
                    instancesFilter, order, orderBy, listRequest.getPageNo(), listRequest.getPageSize()
                    , masterInstanceIdMap);

        } else {
            response = listForPageBybcm(
                    instances, order, orderBy, listRequest.getPageNo(), listRequest.getPageSize(), masterInstanceIdMap);
        }


        return response;
    }

    public LogicPageResultResponse<InstanceAbstract> listInstanceWithPageByApi(RdsListRequest listRequest) {
        String order = (listRequest.getOrders() == null || listRequest.getOrders().size() == 0)
                ? null : listRequest.getOrders().get(0).getOrder();
        String orderBy = (listRequest.getOrders() == null || listRequest.getOrders().size() == 0)
                ? null : listRequest.getOrders().get(0).getOrderBy();

        List<InstanceAbstract> instances = listInstanceByExpiredDate(
                    listRequest.getDaysToExpiration(), order, orderBy, listRequest.getFilterMap(),
                    listRequest.getServiceType(), null);
        addInstanceShortId(instances);
        instances = (List<InstanceAbstract>)instanceForApiUtils.standardInstanceAbstractList(
                instances, RDSConstant.FROM_API, listRequest.getFilterMap(), Boolean.TRUE);
        instances = reorderInstanceList(instances, order, orderBy);
        // 填充 category 双机版为null
//        resolvePost(instances);

        LogicPageResultResponse<InstanceAbstract> response = listForPage(
                instances, order, orderBy, listRequest.getPageNo(), listRequest.getPageSize(), null);
        return response;
    }



    // 查询过保机器
    public LogicPageResultResponse<InstanceAbstract> listQuotInstanceWithPageByMultiKey(RdsListRequest listRequest) {

        String order = (listRequest.getOrders() == null || listRequest.getOrders().size() == 0)
                ? null : listRequest.getOrders().get(0).getOrder();
        String orderBy = (listRequest.getOrders() == null || listRequest.getOrders().size() == 0)
                ? null : listRequest.getOrders().get(0).getOrderBy();


        RDSClient rdsClient = clientFactory.createRdsClient();

        Map<String, String> filterMap = listRequest.getFilterMap();

        if (filterMap != null
                && filterMap.get("instanceType") != null  // instanceType为financila时表示raft版本创建raft版本的client
                && filterMap.get("instanceType").equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2);
            // filterMap.remove("injstanceType");   // 使用完该字段后销毁，以免影响之后真正的条件筛选
        }

        // step 1 查询后端获取实例
        InstanceListResponse instancesSource = rdsClient.instanceList("quot");

        // 获取资源列表
        Resources resources = this.getResourceList();

        List<InstanceAbstract> instances = null;

        // 在list重构前，临时用这种方式拼凑数据
        // 下面几个接口都是复用的list接口，尽量不要因为过保功能修改这些接口
        // step 2 绑定标签信息到实例
        instances = bindTagsToInstance(instancesSource.getInstances());

        // step 3 填装实例附加信息1，如资源信息，并做部分过滤
        instances = getInstanceAbstracts(listRequest.getDaysToExpiration() * ONE_DAY_MILLISECOND,
                resources, instances, filterMap, order, orderBy,
                listRequest.getServiceType(), Boolean.FALSE, null);

        // step 3.1 去掉创建中的实例，为了复用原方法做的折中选择
        instances = removeCreatingInstance(instances);

        // step 4 填装实例附加信息2，如短ID信息，并做filterMap过滤
        instances = (List<InstanceAbstract>)instanceForApiUtils.standardInstanceAbstractList(
                instances, RDSConstant.FROM_CONSOLE, listRequest.getFilterMap(), Boolean.FALSE);

        // step 5 重排序，主要是把代理、只读等实例与主实例对齐并进行分级
//        instances = reorderInstanceList(instances);

        // 排序后的一些处理
        resolvePost(instances);

        // step 6 封装成page
        LogicPageResultResponse<InstanceAbstract> response = listForPageNormal(
                instances, order, orderBy, listRequest.getPageNo(), listRequest.getPageSize());

        return response;
    }


    // 去掉实例中的创建中实例
    private List<InstanceAbstract> removeCreatingInstance(List<InstanceAbstract> instances) {

        List<InstanceAbstract> result = new ArrayList<InstanceAbstract>();

        for (InstanceAbstract each : instances) {
            if (ObjectUtils.notEqual(each.getInstanceStatus(), "creating")
                    && ObjectUtils.notEqual(each.getInstanceStatus(), "notExist")) {
                result.add(each);
            }
        }
        return result;
    }


    // 排序后的一些处理
    private void resolvePost(List<InstanceAbstract> instances) {
        for (InstanceAbstract eachInstance : instances) {
            // 填充 category，目前是根据application type判断
            // 创建中和已创建的实例，由于取数据的源头不一样，所以返回参数不尽相同，需要区别对待
            if (ObjectUtils.equals(RDSConstant.APPLICATION_TYPE_SINGLE, eachInstance.getApplicationType())
                    || ObjectUtils.equals(RDSConstant.CATEGORY_SINGLETON, eachInstance.getApplicationType())) {
                eachInstance.setCategory(RDSConstant.CATEGORY_SINGLETON);
            }
        }
    }

    public LogicPageResultResponse<InstanceAbstract> listInstanceWithPageForPolicy(RdsListRequest listRequest) {
        String order = (listRequest.getOrders() == null || listRequest.getOrders().size() == 0)
                ? null : listRequest.getOrders().get(0).getOrder();
        String orderBy = (listRequest.getOrders() == null || listRequest.getOrders().size() == 0)
                ? null : listRequest.getOrders().get(0).getOrderBy();

        listRequest.setServiceType(ServiceType.RDS.toString()); // 只需要主实例
        List<InstanceAbstract> instances;
        // 普通实例
        instances = listInstanceByExpiredDate(
                listRequest.getDaysToExpiration(), order, orderBy, listRequest.getFilterMap(),
                listRequest.getServiceType(), true, null);
        logger.info("listForPage 1:" + instances.size());
        // on dcc实例
        instances.addAll(listInstanceOnDcc(
                listRequest.getDaysToExpiration(), order, orderBy, listRequest.getFilterMap(),
                listRequest.getServiceType()));
        logger.info("listForPage 2:" + instances.size());
        instances = (List<InstanceAbstract>)instanceForApiUtils.standardInstanceAbstractList(
                instances, RDSConstant.FROM_CONSOLE, listRequest.getFilterMap(), Boolean.TRUE);

        // raft实例
        if (ArrayUtils.contains(financialRegion, regionConfiguration.getCurrentRegion())) {
            List<InstanceAbstract> instancesRaft;
            if (listRequest.getFilterMap() == null) {
                listRequest.setFilterMap(new HashMap<String, String>());
            }
            listRequest.getFilterMap().put("instanceType", RDSConstant.INSTANCE_TYPE_RAFT);
            instancesRaft = listInstanceByExpiredDate(
                    listRequest.getDaysToExpiration(), order, orderBy, listRequest.getFilterMap(),
                    listRequest.getServiceType(), true, null);
            logger.info("listForPage 3:" + instancesRaft.size());

//        listRequest.getFilterMap().remove("instanceType");
            instancesRaft = (List<InstanceAbstract>)instanceForApiUtils.standardInstanceAbstractList(
                    instancesRaft, RDSConstant.FROM_CONSOLE, listRequest.getFilterMap(), Boolean.TRUE);
//        instances = reorderInstanceList(instances);
            logger.info("listForPage 4:" + instancesRaft.size());

            instances.addAll(instancesRaft);
        }

        logger.info("listForPage 5:" + instances.size());
        LogicPageResultResponse<InstanceAbstract> response = listForPage(
                instances, order, orderBy, listRequest.getPageNo(), listRequest.getPageSize(), null);
        return response;
    }

    public LogicMarkerResultResponse<InstanceAbstract> listInstanceWithMarkerByMultiKey(String marker, int maxKeys,
        String vnetIp) {
        List<InstanceAbstract> instances = listInstanceByExpiredDate(
                -1, null, null, null, null, null);

        addInstanceShortId(instances);
        instances = reorderInstanceList(instances, null, null);

        instances = (List<InstanceAbstract>)instanceForApiUtils
                .standardInstanceAbstractList(instances, RDSConstant.FROM_API, null, Boolean.TRUE);
        // 查询绑定资源信息
        instances = bindResourceToInstance(instances);
        // 根据 vnetIp 精准匹配过滤 支持多个值 默认为空 与原逻辑保持一致
        instances = filterByVnetIp(vnetIp, instances);

        LogicMarkerResultResponse<InstanceAbstract> response = listForPageByMarker(instances, marker, maxKeys);
        return response;
    }

    public LogicMarkerResultResponse<InstanceAbstract> listV2InstanceWithMarkerByMultiKey(String marker, int maxKeys,
                                                                                        String vnetIp) {
        List<InstanceAbstract> instances = listInstanceByExpiredDate(
                -1, null, null, null, null, RDSConstant.FROM_API_V2);

        addInstanceShortId(instances);
        instances = reorderInstanceList(instances, null, null);

        instances = (List<InstanceAbstract>) instanceForApiUtils
                .standardInstanceAbstractList(instances, RDSConstant.FROM_API, null, Boolean.TRUE);
        // 查询绑定资源信息
        instances = bindResourceToInstance(instances);
        // 根据 vnetIp 精准匹配过滤 支持多个值 默认为空 与原逻辑保持一致
        instances = filterByVnetIp(vnetIp, instances);

        LogicMarkerResultResponse<InstanceAbstract> response = listForPageByMarker(instances, marker, maxKeys);
        return response;
    }

    /**
     * 专用于实例列表支持通过 vetIp 筛选数据
     * @param vnetIp
     * @param instances
     * @return
     */
    public List<InstanceAbstract> filterByVnetIp(String vnetIp, List<InstanceAbstract> instances) {
        List<InstanceAbstract> res = new LinkedList<>();
        if (StringUtils.isNotEmpty(vnetIp)) {
            if (vnetIp.contains(",")) {
                String[] vnetIpList = vnetIp.split(",");
                for (String ip : vnetIpList) {
                    for (InstanceAbstract each : instances) {
                        if (StringUtils.isNotEmpty(each.getEndpoint().getVnetIp())
                                && each.getEndpoint().getVnetIp().contains(ip)) {
                            res.add(each);
                        }
                    }
                }
            } else {
                for (InstanceAbstract each : instances) {
                    if (StringUtils.isNotEmpty(each.getEndpoint().getVnetIp())
                            && each.getEndpoint().getVnetIp().contains(vnetIp)) {
                        res.add(each);
                    }
                }
            }
        } else {
            // 为空时，返回原值即可
            return instances;
        }
        return res;
    }

    public Collection<Instance> proxyManageList(String proxyId) {
        Instance instanceProxy = clientFactory.createRdsClient2().instanceDescribe(proxyId).getInstance();
        String masterId = instanceProxy.getTopology().getMaster().get(0);
        InstanceListResponse instancesList = clientFactory.createRdsClient().instanceList(null);
        List<Instance> instanceList
                = instanceListForProxyManagement((List<Instance>) instancesList.getInstances(), masterId, proxyId);
        return instanceList;
    }

    private List<Instance> instanceListForProxyManagement(List<Instance> instances, String masterId, String proxyId) {
        List<Instance> resultList = new ArrayList<>();
        List<String> longIds = new ArrayList<>();
        Instance master = null;
        List<Instance> readOnly = new ArrayList<Instance>();
        List<Instance> proxy = new ArrayList<Instance>();
        List<Instance> other = new ArrayList<Instance>();
        for (Instance instance : instances) {
            if (instance.getInstanceId().equalsIgnoreCase(masterId)
                    || (StringUtils.isNotEmpty(instance.getSourceInstanceId())
                    && instance.getSourceInstanceId().equalsIgnoreCase(masterId))
                    && !instance.getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_PROXY)
                    && !"failed".equalsIgnoreCase(instance.getInstanceStatus())) {

                // 临时的一个排序
                if (instance.getInstanceType().equals(RDSConstant.INSTANCE_TYPE_MASTER)) {
                    master = instance;
                } else if (instance.getInstanceType().equals(RDSConstant.INSTANCE_TYPE_REPLICA)) {
                    readOnly.add(instance);
                } else if (instance.getInstanceType().equals(RDSConstant.INSTANCE_TYPE_PROXY)) {
                    proxy.add(instance);
                } else {
                    other.add(instance);
                }

                longIds.add(instance.getInstanceId());
            }
        }

        // 临时的一个排序，master->readonly->proxy
        if (master != null) {
            resultList.add(master);
        }
        resultList.addAll(readOnly);
        resultList.addAll(proxy);
        resultList.addAll(other);

        Map<String, String> idMap = idMapperService.idMapByLongId(longIds);
        for (Instance instance : instances) {
            String shortId = idMap.get(instance.getInstanceId());
            if (StringUtils.isEmpty(shortId)) {
                logger.warn("api list can not find shortId,instanceUuid:{}", instance.getInstanceId());
                continue;
            }
            instance.setInstanceShortId(shortId);
        }
        return resultList;
    }

    private List<InstanceAbstract> addInstanceShortId(List<InstanceAbstract> instances) {
        List<InstanceAbstract> resultList = new ArrayList<>();
        List<String> longIds = new ArrayList<>();
        logger.info("[addInstanceShortId] instance size {}", instances.size());
        for (InstanceAbstract instance : instances) {
            logger.info("[addInstanceShortId] instance getInstanceId {}", instance.getInstanceId());
            if ((StringUtils.isNotEmpty(instance.getInstanceId()))
                    && !"failed".equalsIgnoreCase(instance.getInstanceStatus())) {
                resultList.add(instance);

                longIds.add(instance.getInstanceId());
            }
        }
        logger.info("[addInstanceShortId] instance longIds size {}", longIds.size());
        Map<String, String> idMap = idMapperService.idMapByLongId(longIds);
        for (InstanceAbstract instance : instances) {
            String shortId = idMap.get(instance.getInstanceId());
            if (StringUtils.isEmpty(shortId)) {
                logger.warn("api list can not find shortId,instanceUuid:{}", instance.getInstanceId());
                continue;
            }
            instance.setInstanceShortId(shortId);
        }
        return resultList;
    }
    public void setRogroupVpcAndSubnetInstanceExtension(InstanceExtension instance) {
        if (CollectionUtils.isEmpty(instance.getRoGroupList())) {
            return;
        }

        Set<String> vpcUuids = new HashSet<>();
        Set<String> subnetUuids = new HashSet<>();
        for (Instance.RoGroup roGroup : instance.getRoGroupList()) {
            if (StringUtils.isEmpty(roGroup.getVpcId())) {
                continue;
            }
            vpcUuids.add(roGroup.getVpcId());
            if (StringUtils.isEmpty(roGroup.getSubnetId())) {
                continue;
            }
            subnetUuids.add(roGroup.getSubnetId());
        }
        Map<String, SimpleVpcVo> vpcUuidSimpleVpcVoMap = rdsVpcWrapperService.getVpcUuidSimpleVpcVoMap(vpcUuids);
        Map<String, SubnetVo> subnetUuidSubnetVoMap = rdsVpcWrapperService.getSubnetUuidSubnetVoMap(subnetUuids);
        for (Instance.RoGroup roGroup : instance.getRoGroupList()) {
            if (StringUtils.isEmpty(roGroup.getVpcId())) {
                continue;
            }
            if (vpcUuidSimpleVpcVoMap.containsKey(roGroup.getVpcId())) {
                roGroup.setVpcVo(vpcUuidSimpleVpcVoMap.get(roGroup.getVpcId()));
            }

            if (StringUtils.isEmpty(roGroup.getSubnetId())) {
                continue;
            }
            if (subnetUuidSubnetVoMap.containsKey(roGroup.getSubnetId())) {
                roGroup.setSubnetVo(subnetUuidSubnetVoMap.get(roGroup.getSubnetId()));
            }
        }
    }


    public void setVpcDetailForInstanceExtension(InstanceExtension instance) {
        // 注意：不论是普通区域，还是边缘计算区域，后端返回的 vpcId 字段，是 VPC 实例长 ID，
        //  从面向用户侧的 API 来说，这种 ID 更准确的名称应该是 vpcUuid。
        if (StringUtils.isNotEmpty(instance.getEdgeVpcId())) {
            // 存在 edgeVpcId 说明是边缘计算区域实例，那么忽略 vpcId 字段
            EdgeVpc edgeVpc = edgeService.getVpcByVpcUuid(instance.getEdgeVpcId());
            instance.setVpcName(edgeVpc.getName());
            instance.setVpcCidr(edgeVpc.getCidr());
            instance.setVpcShortId(edgeVpc.getVpcId());
            return;
        }
        if (StringUtils.isNotEmpty(instance.getVpcId())) {
            ExternalVpcClient vpcClient = clientFactory.createVpcClient();
            VpcIdsRequest vpcIdsRequest = new VpcIdsRequest();
            vpcIdsRequest.setVpcIds(Collections.singletonList(instance.getVpcId()));
            SimpleVpcVo vpcVo = vpcClient.get(vpcIdsRequest).get(instance.getVpcId());
            logger.debug("vpc name is:" + vpcVo.getName());
            instance.setVpcName(vpcVo.getName());
            instance.setVpcCidr(vpcVo.getCidr());
            instance.setVpcShortId(vpcVo.getShortId());
        } else {
            instance.setVpcName("");
            instance.setVpcCidr("");
            instance.setVpcShortId("");
        }
    }

    private void handleForInstanceRoleInfo(boolean isEdgeInstance, String regionId, String vpcUuid,
                                           Instance.RoleInfo roleInfo) {
        if (roleInfo == null || StringUtils.isEmpty(roleInfo.getSubnetId())) {
            return;
        }
        if (isEdgeInstance) {
            EdgeSubnet edgeSubnet = edgeService.getSubnetBySubnetUuid(roleInfo.getSubnetId());
            roleInfo.setName(edgeSubnet.getName());
            roleInfo.setVpcCidr(edgeSubnet.getCidr());
            return;
        }
        String subnetId = roleInfo.getSubnetId();
        List<String> ids = new ArrayList<>();
        ListSubnetRequest subnetRequestWithZone = new ListSubnetRequest();
        ids.add(subnetId);
        subnetRequestWithZone.setSubnetIds(ids);
        List<SubnetVo> subnetsResponse = clientFactory.createSubnetClient()
                .listSubnet(subnetRequestWithZone);
        if (subnetsResponse != null && subnetsResponse.size() > 0) {
            roleInfo.setName(subnetsResponse.get(0).getName());
            roleInfo.setVpcCidr(subnetsResponse.get(0).getCidr());
            roleInfo.setShortId(subnetsResponse.get(0).getShortId());
        }
    }

    public void setSubnetDetailForInstanceExtension2(InstanceExtension instance) {
//        logger.debug("in detail, set subnet detail");
        logger.debug("subnetId is: " + instance.getSubnetId());
        ListSubnetRequest subnetRequest = new ListSubnetRequest();
        subnetRequest.setSubnetType(new Short("1")); // 1.BCC 2.BBC

        boolean isEdgeInstance = StringUtils.isNotEmpty(instance.getEdgeSubnetId());
        handleForInstanceRoleInfo(isEdgeInstance, instance.getEdgeRegion(),
                instance.getEdgeVpcId(), instance.getNodeMaster());
        handleForInstanceRoleInfo(isEdgeInstance, instance.getEdgeRegion(),
                instance.getEdgeVpcId(), instance.getNodeSlave());
        handleForInstanceRoleInfo(isEdgeInstance, instance.getEdgeRegion(),
                instance.getEdgeVpcId(), instance.getNodeReadReplica());
        handleForInstanceRoleInfo(isEdgeInstance, instance.getEdgeRegion(),
                instance.getEdgeVpcId(), instance.getNodeMasterReadReplica());
        handleForInstanceRoleInfo(isEdgeInstance, instance.getEdgeRegion(),
                instance.getEdgeVpcId(), instance.getNodeBackupReadReplica());

        if (isEdgeInstance) {
            EdgeSubnet edgeSubnet = edgeService.getSubnetBySubnetUuid(instance.getEdgeSubnetId());
            instance.getSubnets().add(new InstanceExtension.Subnet(edgeSubnet.getName(),
                    edgeSubnet.getSubnetId(), "", edgeSubnet.getCidr(), edgeSubnet.getSubnetId()));
            return;
        }

        ExternalSubnetClient subnetClient = clientFactory.createSubnetClient();
        List<SubnetVo> subnetVos = new ArrayList<>();
        if (instance.getSubnetId() != null && !instance.getSubnetId().isEmpty()) {
            List<String> ids = new ArrayList<>();
            for (String zone : instance.getSubnetId().keySet()) {
                if (StringUtils.isNotEmpty(instance.getSubnetId().get(zone))) {
                    ids.add(instance.getSubnetId().get(zone));
                } else {
                    ListSubnetRequest subnetRequestWithZone = new ListSubnetRequest();
                    subnetRequest.setSubnetType(new Short("1"));
                    subnetRequestWithZone.setZone(zone);
                    subnetVos.addAll(subnetClient.listSubnet(subnetRequestWithZone));
                }
            }
            subnetRequest.setSubnetIds(ids);
            if (!ids.isEmpty()) {
                if (instance.getNodeMaster() == null || instance.getNodeSlave() == null) {
                    subnetVos.addAll(subnetClient.listSubnet(subnetRequest));
                } else {
                    SubnetVo subnetVo = new SubnetVo();
                    subnetVo.setName(instance.getNodeMaster().getName());
                    subnetVo.setSubnetId(instance.getNodeMaster().getSubnetId());
                    subnetVo.setAz(instance.getNodeMaster().getAzone());
                    subnetVo.setCidr(instance.getNodeMaster().getVpcCidr());
                    subnetVo.setShortId(instance.getNodeMaster().getShortId());
                    subnetVos.add(subnetVo);
                    if (ids.size() > 1){
                        SubnetVo sub = new SubnetVo();
                        sub.setName(instance.getNodeSlave().getName());
                        sub.setSubnetId(instance.getNodeSlave().getSubnetId());
                        sub.setAz(instance.getNodeSlave().getAzone());
                        sub.setCidr(instance.getNodeSlave().getVpcCidr());
                        sub.setShortId(instance.getNodeSlave().getShortId());
                        subnetVos.add(sub);
                    }
                }
            }

        } else {
            if (instance.getAzone().contains(",")) {
                String[] azones = instance.getAzone().split(",");
                for (String az : azones) {
                    subnetRequest.setZone(az);
                    subnetVos.addAll(subnetClient.listSubnet(subnetRequest));
                }
            } else {
                subnetRequest.setZone(instance.getAzone());
                subnetVos.addAll(subnetClient.listSubnet(subnetRequest));
            }
        }
        for (SubnetVo subnetVo : subnetVos) {
            logger.debug("subnet name is " + subnetVo.getName());
            instance.getSubnets().add(new InstanceExtension.Subnet(subnetVo.getName(),
                    subnetVo.getSubnetId(), subnetVo.getAz(), subnetVo.getCidr(), subnetVo.getShortId()));
        }

    }

    /**
     * 绑定相关计费变更订单类型到InstanceAbstract上
     *
     * @param instance
     */
    public void bindOrderStatusToInstanceExtension(InstanceExtension instance) {
        if (instance == null) {
            return;
        }
        // 获取 TO_POSTPAY 和 TO_PREPAY 订单
        Map<String, Order> orderMap = rdsOrderService.getOrderMapByFilters(Arrays.asList(OrderStatus.DEFERRED_CREATE),
                Arrays.asList(OrderType.TO_POSTPAY, OrderType.TO_PREPAY));
        if (MapUtils.isEmpty(orderMap)) {
            return;
        }
        if (orderMap.containsKey(instance.getResourceUuid())) {
            Order tempOrder = orderMap.get(instance.getResourceUuid());
            instance.setOrderStatus(tempOrder.getType().name().toLowerCase());
        }
    }

    public InstanceExtension getInstanceExtension(String instanceId) {
        InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(instanceId)
                .instanceDescribe(instanceId);
        InstanceExtension instance = new InstanceExtension(instanceGetResponse.getInstance());
        if (!"DCC".equalsIgnoreCase(instance.getMachineType())) {
            Resource resource = getResourceByInstanceId(instanceId);
            if (null == resource) {
                logger.warn("rds instanceId: {} not in resources", instanceId);
                throw new RDSBusinessExceptions.ResourceNotExistException();
            }
            instance.setOrderId(resource.getOrderId());
            instance.setInstanceExpireTime(resource.getExpireTime());
            instance.setResourceUuid(resource.getUuid());
            instance.setProductType(resource.getProductType());
            boolean chargeInfo = updateProxyChargeInfo(resource);
            instance.setIsChargeProxy(chargeInfo);
        } else {
            instance.withDccHosts(instanceDao.getMachineListByUuid(instance.getInstanceId()));
        }

        if (instance.getInstanceStatus().equals("available")) {
            if (!instance.getLockMode().equals("unlock")) {
                instance.setInstanceStatus(instance.getLockMode());
            }
        }
        // physical to logical
        if (instance.getAzone() == null || instance.getAzone().equals("default")) {
            instance.setAzone("zoneA");
        }
        if (instance.getAzone().contains("+")) {
            instance.setAzone(instance.getAzone().replaceAll("\\+", ","));
        }

        // 系列信息，目前只支持单机版
        if (instance.getIsSingle() != null && instance.getIsSingle()
                || instance.getApplicationType() != null && instance.getApplicationType().equals("single")) {
            instance.setCategory(RDSConstant.CATEGORY_SINGLETON);
        }

        return instance;
    }

    public Resource getResourceByInstanceId(String instanceId) {
        Resource resource = null;
        for (Resource tmp : getResourceList()) {
            if (tmp.getName().equals(instanceId)) {
                resource = tmp;
                break;
            }
        }
        return resource;
    }

    // 不区分主备实例，只是简单的拼装list
    public LogicPageResultResponse<InstanceAbstract> listForPageNormal(List<InstanceAbstract> instanceAbstracts,
                                                                       String order,
                                                                       String orderBy,
                                                                       int pageNo,
                                                                       int pageSize) {
        LogicPageResultResponse<InstanceAbstract> response = new LogicPageResultResponse<>();
        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);

        if (instanceAbstracts == null || instanceAbstracts.size() == 0) {
            return response;
        }
        if (pageNo < 1 || pageSize < 1) {
            return response;
        }

        // (2 - 1) * 10 = 10
        int pageStart = (pageNo - 1) * pageSize;
        int pageEnd = 0;
        // 13 - (1 * 10) = 3
        // 20 - (1 * 10) = 10
        int currentSize = instanceAbstracts.size() - ((pageNo - 1) * pageSize);

        // if 3 > 10
        if (currentSize > pageSize) {
            pageEnd = pageNo * pageSize;
        } else {
            pageEnd = instanceAbstracts.size();
        }

        List<InstanceAbstract> instanceForPage = instanceAbstracts.subList(pageStart, pageEnd);

        response.setTotalCount(instanceAbstracts.size());
        response.setResult(instanceForPage);
        return response;
    }

    public LogicPageResultResponse<InstanceAbstract> listForPage(List<InstanceAbstract> instanceAbstracts,
                                                                 String order,
                                                                 String orderBy,
                                                                 int pageNo,
                                                                 int pageSize,
                                                                 Map<String, Object> masterInstanceIdMap) {
        LogicPageResultResponse<InstanceAbstract> response = new LogicPageResultResponse<>();
        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);

        if (instanceAbstracts == null || instanceAbstracts.size() == 0) {
            return response;
        }
        if (pageNo < 1 || pageSize < 1) {
            return response;
        }

        int pageStart = (pageNo - 1) * pageSize;
        int pageEnd = pageNo * pageSize - 1;

        int start = 0;
        int end = 0;
        int masterNum = 0;
        boolean find = false;
        InstanceAbstract instance;
        for (int index = 0; index < instanceAbstracts.size(); index++) {
            instance = instanceAbstracts.get(index);
            if (instance.getSourceInstanceId() == null || "".equals(instance.getSourceInstanceId())
                    || (masterInstanceIdMap != null
                    && !masterInstanceIdMap.containsKey(instance.getSourceInstanceId()))) {
                masterNum++;
                if (masterNum - 1 == pageStart) {
                    start = index;
                    find = true;
                }
                if (masterNum - 1 == pageEnd + 1) {
                    end = index;
                }
            }
        }
        if (!find) {
            response.setTotalCount(masterNum);
            return response;
        } else if (end == 0) {
            end = instanceAbstracts.size();
        }
        logger.info("startIndex : " + start + " ,endIndex : " + end);
        List<InstanceAbstract> instanceForPage = instanceAbstracts.subList(start, end);

        response.setTotalCount(masterNum);
        response.setResult(instanceForPage);
        return response;
    }

    public LogicPageResultResponse<InstanceAbstract> listForPageBybcm(List<InstanceAbstract> instanceAbstracts,
                                                                 String order,
                                                                 String orderBy,
                                                                 int pageNo,
                                                                 int pageSize,
                                                                 Map<String, Object> masterInstanceIdMap) {
        LogicPageResultResponse<InstanceAbstract> response = new LogicPageResultResponse<>();
        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);

        if (instanceAbstracts == null || instanceAbstracts.size() == 0) {
            return response;
        }
        if (pageNo < 1 || pageSize < 1) {
            return response;
        }

        int pageStart = (pageNo - 1) * pageSize;
        int pageEnd = pageNo * pageSize - 1;

        int start = 0;
        int end = 0;
        int masterNum = 0;
        boolean find = false;
        InstanceAbstract instance;
        for (int index = 0; index < instanceAbstracts.size(); index++) {
            instance = instanceAbstracts.get(index);
            masterNum++;
            if (masterNum - 1 == pageStart) {
                start = index;
                find = true;
            }
            if (masterNum - 1 == pageEnd + 1) {
                end = index;
            }

        }
        if (!find) {
            response.setTotalCount(masterNum);
            return response;
        } else if (end == 0) {
            end = instanceAbstracts.size();
        }
        logger.info("startIndex : " + start + " ,endIndex : " + end);
        List<InstanceAbstract> instanceForPage = instanceAbstracts.subList(start, end);

        response.setTotalCount(masterNum);
        response.setResult(instanceForPage);
        return response;
    }

    public LogicMarkerResultResponse<InstanceAbstract> listForPageByMarker(List<InstanceAbstract> instanceAbstracts,
                                                                           String marker,
                                                                           int maxKeys) {
        LogicMarkerResultResponse<InstanceAbstract> response = new LogicMarkerResultResponse<>();
        response.setMarker(marker);
        response.setMaxKeys(maxKeys);

        if (instanceAbstracts == null || instanceAbstracts.size() == 0) {
            return response;
        }
        if (StringUtils.isEmpty(marker) || maxKeys < 1) {
            return response;
        }
        String nextMarker;
        int start = 0;
        int end = 0;
        int masterCount = 0;
        int markerIndex = 1;
        boolean find = false;
        if ("-1".equals(marker)) {
            find = true;
        }
        InstanceAbstract instance;
        for (int index = 0; index < instanceAbstracts.size(); index++) {
            instance = instanceAbstracts.get(index);
            if (StringUtils.isEmpty(instance.getSourceInstanceId())) {
                masterCount++;
                if ((!"-1".equals(marker)) && instance.getInstanceShortId().equals(marker)) {
                    start = index;
                    find = true;
                    markerIndex = masterCount;
                }
                if (masterCount == markerIndex + maxKeys) {
                    end = index;
                }
            }
        }
        if (!find) {
            response.setIsTruncated(false);
            return response;
        } else if (end == 0) {
            end = instanceAbstracts.size();
            response.setIsTruncated(false);
        } else {
            nextMarker = instanceAbstracts.get(end).getInstanceShortId();
            response.setNextMarker(nextMarker);
            response.setIsTruncated(true);
        }
        logger.info("startIndex : " + start + " ,endIndex : " + end);
        List<InstanceAbstract> instanceForPage = instanceAbstracts.subList(start, end);

        response.setResult(instanceForPage);
        return response;
    }

//    public static void main(String[] args){
//        InstanceAbstract instanceAbstract1 = new InstanceAbstract();
//        instanceAbstract1.setInstanceId("rds1");
//        InstanceAbstract instanceAbstract2 = new InstanceAbstract();
//        instanceAbstract2.setInstanceId("rds2");
//        InstanceAbstract instanceAbstract3 = new InstanceAbstract();
//        instanceAbstract3.setInstanceId("rds3");
//        List<InstanceAbstract> list = new ArrayList<>();
//        list.add(instanceAbstract1);
//        list.add(instanceAbstract2);
//        list.add(instanceAbstract3);
//
//        LogicMarkerResultResponse response = new InstanceService().listForPageByMarker(list, "rds1", 3);
//        System.out.print(256 / 1024.00);
//    }

    private void filterResourceWithoutStopped(Resources resources, Collection<Instance> instanceCollection) {
        Iterator<Resource> iterator = resources.iterator();
        while (iterator.hasNext()) {
            boolean isFind = false;
            Resource resource = iterator.next();
            for (Instance instance : instanceCollection) {
                if (resource.getName().equals(instance.getInstanceId())) {
                    // 平台侧返回的实例列表找到了当前实例
                    isFind = true;
                    if (ResourceStatus.STOPPED.equals(resource.getStatus())
                            && EngineType.MYSQL.getValue().equals(instance.getEngine())) {
                        iterator.remove();

                    }
                }
            }
            if (!isFind) {
                if (ResourceStatus.STOPPED.equals(resource.getStatus())) {
                    iterator.remove();
                }
            }


        }

    }

    public List<InstanceAbstract> listInstanceByExpiredDate(int days,
                                                            String order,
                                                            String orderBy,
                                                            Map<String, String> filterMap,
                                                            String serviceType,
                                                            String from) {
        return listInstanceByExpiredDate(days, order, orderBy, filterMap, serviceType, false, from);
    }

    public List<InstanceAbstract> listInstanceByExpiredDate(int days,
                                                            String order,
                                                            String orderBy,
                                                            Map<String, String> filterMap,
                                                            String serviceType,
                                                            boolean forPolicy,
                                                            String from) {
        List<InstanceAbstract> instances = null;
        try {
            InstanceListResponse instancesSource;
            if (RDSConstant.FROM_API_V2.equalsIgnoreCase(from)) {
                RDSClient rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_2);
                instancesSource = rdsClient.instanceList(null);
            } else {
                RDSClient3 rdsClient3 = clientFactory.createRdsClient3();
                instancesSource = rdsClient3.instanceList(null);
            }
//            if (filterMap != null
//                    && filterMap.get("instanceType") != null  // instanceType为financila时表示raft版本创建raft版本的client
//                    && filterMap.get("instanceType").equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
//                rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2);
//                // filterMap.remove("injstanceType");   // 使用完该字段后销毁，以免影响之后真正的条件筛选
//            }


            Resources resources = this.getResourceList();

            // 过滤掉资源状态为STOPPED的实例，让其只显示在回收站实例列表
            filterResourceWithoutStopped(resources, instancesSource.getInstances());

            // bind Tag info to instance
            instances = bindTagsToInstance(instancesSource.getInstances());


//            if (!clientFactory.isRoot()) {
//                instances = getInstancesBySubuser(days * ONE_DAY_MILLISECOND,
//                        resources, instances, filterMap, order, orderBy, serviceType);
//            } else {
//                instances = getInstanceAbstracts(days * ONE_DAY_MILLISECOND,
//                        resources, instances, filterMap, order, orderBy, serviceType);
//            }
            instances = getInstanceAbstracts(days * ONE_DAY_MILLISECOND,
                    resources, instances, filterMap, order, orderBy, serviceType, forPolicy, from);
            // 查询并绑定计费变更类型到实例（orderStatus）
            bindOrderStatus(instances);
            // physicalZone to logicalZone
            for (InstanceAbstract instanceAbstract : instances) {
//                instanceAbstract.setAzone(getLogicalZoneByPhysicalZone(instanceAbstract.getAzone()));
                if (instanceAbstract.getAzone() == null || instanceAbstract.getAzone().equals("default")) {
                    instanceAbstract.setAzone("zoneA");
                }
                if (instanceAbstract.getAzone().contains("+")) {
                    instanceAbstract.setAzone(instanceAbstract.getAzone().replaceAll("\\+", ","));
                }
            }
        } catch (BceInternalResponseException ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
//        List<InstanceAbstract> innns = reorderInstanceList(instances);
//        for (InstanceAbstract abs : innns) {
//            logger.info("InstanceAbstracts(reorderInstanceList): instanceId:{},instanceUUid:{}, expireDate:{} ",
//                    abs.getInstanceShortId(), abs.getInstanceId(), abs.getExpireDate());
//
//        }

        return instances;
    }

    // 实例详情绑定资源组
    private void bindResourceGroupToInstanceDetail(InstanceExtension instanceExtension) {
        ResGroupDetailRequest request = new ResGroupDetailRequest();
        List<ResourceBrief> resourceBriefs = new ArrayList<>();
        ResourceBrief resourceBrief = new ResourceBrief();
        String serviceType = getServiceTypeByInstanceType(instanceExtension.getInstanceType());
        resourceBrief.setResourceType(serviceType);
        resourceBrief.setAccountId(clientFactory.getAccountId());
        resourceBrief.setResourceRegion(regionConfiguration.getCurrentRegion());
        resourceBrief.setResourceId(instanceExtension.getInstanceShortId());
        resourceBriefs.add(resourceBrief);
        request.setResourceBriefs(resourceBriefs);
        ResGroupDetailResponse resGroupBatch
                = resourceGroupService.getResGroupBatch(request, RDSConstant.FROM_CONSOLE);
        Map<String, ResourceGroupsDetailFull> map = new HashMap<>();
        if (resGroupBatch != null && CollectionUtils.isNotEmpty(resGroupBatch.getResGroups())) {
            for (ResourceGroupsDetailFull resGroup : resGroupBatch.getResGroups()) {
                map.put(resGroup.getId(), resGroup);
            }
            instanceExtension.setResourceGroup(map.get(instanceExtension.getInstanceShortId()));
        }
    }

    // 实例列表绑定资源组
    private List<InstanceAbstract> bindResourceToInstance(List<InstanceAbstract> instances) {
        ResGroupDetailRequest request = new ResGroupDetailRequest();
        List<ResourceBrief> resourceBriefs = new ArrayList<>();
        for (InstanceAbstract instance : instances) {
            ResourceBrief resourceBrief = new ResourceBrief();
            String serviceType = getServiceTypeByInstanceType(instance.getInstanceType());
            resourceBrief.setResourceType(serviceType);
            resourceBrief.setAccountId(clientFactory.getAccountId());
            resourceBrief.setResourceRegion(regionConfiguration.getCurrentRegion());
            resourceBrief.setResourceId(instance.getInstanceShortId());
            resourceBriefs.add(resourceBrief);
        }
        request.setResourceBriefs(resourceBriefs);
        ResGroupDetailResponse resGroupBatch
                = resourceGroupService.getResGroupBatch(request, RDSConstant.FROM_CONSOLE);
        Map<String, ResourceGroupsDetailFull> map = new HashMap<>();
        if (resGroupBatch != null && CollectionUtils.isNotEmpty(resGroupBatch.getResGroups())) {
            for (ResourceGroupsDetailFull resGroup : resGroupBatch.getResGroups()) {
                map.put(resGroup.getId(), resGroup);
            }
            for (InstanceAbstract instance : instances) {
                instance.setResourceGroup(map.get(instance.getInstanceShortId()));
            }
        }
        return instances;
    }

    public List<InstanceAbstract> listInstanceOnDcc(int days,
                                                    String order,
                                                    String orderBy,
                                                    Map<String, String> filterMap,
                                                    String serviceType) {
        List<InstanceAbstract> instances = null;
        try {
            InstanceListResponse instancesSource = clientFactory.createRdsClient().instanceList("dcc");

            // bind Tag info to instance
            instances = bindTagsToInstance(instancesSource.getInstances());
            // 查询并绑定自动续费到实例（task）
            bindAutoRenew(instances);

            instances = getInstanceAbstractsOnDcc(days * ONE_DAY_MILLISECOND,
                    null, instances, filterMap, order, orderBy, serviceType);

            // 查询并绑定计费变更类型到实例（orderStatus）
            bindOrderStatus(instances);
            // physicalZone to logicalZone
            for (InstanceAbstract instanceAbstract : instances) {
//                instanceAbstract.setAzone(getLogicalZoneByPhysicalZone(instanceAbstract.getAzone()));
                if (instanceAbstract.getAzone() == null || instanceAbstract.getAzone().equals("default")) {
                    instanceAbstract.setAzone("zoneA");
                }
                if (instanceAbstract.getAzone().contains("+")) {
                    instanceAbstract.setAzone(instanceAbstract.getAzone().replaceAll("\\+", ","));
                }
            }
        } catch (BceInternalResponseException ex) {
            LogicRdsExceptionHandler.handle(ex);
        }

        return instances;
    }

    private List<InstanceAbstract> getInstanceAbstractsOnDcc(long timeLeft,
                                                             Collection<Resource> resources,
                                                             Collection<InstanceAbstract> instances,
                                                             Map<String, String> filterMap,
                                                             String order,
                                                             String orderBy,
                                                             String serviceType) {
        List<InstanceAbstract> instanceAbstracts = new ArrayList<>();

//        List<InstancePO> instancePOs = instanceDao
//                .getInstanceListByInstanceStatus("creating", clientFactory.getUserId(), "dcc");

        for (InstanceAbstract instanceAbstract : instances) {
            if ("failed".equals(instanceAbstract.getInstanceStatus())) {
                continue;
            }
            if (instanceAbstract.getInstanceStatus().equalsIgnoreCase("available")) {
                if (!instanceAbstract.getLockMode().equalsIgnoreCase("unlock")) {
                    instanceAbstract.setInstanceStatus(instanceAbstract.getLockMode());
                }
            }
            // 创建中的实例过滤掉
//            if (instanceAbstract.getInstanceStatus().equalsIgnoreCase("creating")) {
//                logger.info("rds instance abstract is creating : " + instanceAbstract.getInstanceId());
//                continue;
//            }
            instanceAbstracts.add(instanceAbstract);
            instanceAbstract.withDccHosts(instanceDao.getMachineListByUuid(instanceAbstract.getInstanceId()));
        }
//        for (InstancePO instancePO : instancePOs) {
//            logger.info("InstancePOs, instanceId:{},instanceUUid:{} ", instancePO.getInstanceId(),
//                    instancePO.getInstanceUuid());
//            if (StringUtils.isNotEmpty(instancePO.getSourceInstanceId())) {
//                instancePO.setSourceInstanceId(instanceDao.queryInstanceUuid(instancePO.getSourceInstanceId()));
//            }
//            InstanceAbstract instanceAbstract = new InstanceAbstract(instancePO);
//            instanceAbstracts.add(instanceAbstract);
//        }

        sortInstanceAbstract(instanceAbstracts, order, orderBy);
        return instanceAbstracts;
    }

    private List<InstanceAbstract> getInstanceAbstracts(long timeLeft,
                                                        Collection<Resource> resources,
                                                        Collection<InstanceAbstract> instances,
                                                        Map<String, String> filterMap,
                                                        String order,
                                                        String orderBy,
                                                        String serviceType,
                                                        boolean forPolicy,
                                                        String from) {
        List<InstanceAbstract> instanceAbstracts = new ArrayList<>();

        List<InstancePO> instancePOs = instanceDao
                .getInstanceListByInstanceStatus("creating", clientFactory.getAccountId(), null);
        Iterator<InstancePO> iter = instancePOs.iterator();
        logger.debug("before remove, instancePOs.size() = {}", instancePOs.size());
        while (iter.hasNext()) {
            InstancePO instancePO = iter.next();
            if (!instancePO.getInstanceId().equals(instancePO.getInstanceUuid())) {
                // 短ID（instanceId）和长ID（instanceUuid）两者不相等，说明订单执行器 execute 阶段已经将长 ID 入库了，
                //  进而说明后端已经收到创建请求了，那么后端 API 会返回创建中的实例，所以这里过滤掉 console 库里这种数据。
                logger.debug("remove instance({} - {}) from instancePOs",
                        instancePO.getInstanceId(), instancePO.getInstanceUuid());
                iter.remove();
            }
        }
        logger.debug("after remove, instancePOs.size() = {}", instancePOs.size());
        logger.debug("resources.size() = {}", resources.size());
        for (Resource resource : resources) {
            if (!isValidResource(resource, timeLeft, serviceType)) {
                logger.debug("resource({} - {}) isn't valid.", resource.getShortId(), resource.getName());
                continue;
            }

            boolean found = false;
            for (InstanceAbstract instanceAbstract : instances) {
                if (resource.getName().equals(instanceAbstract.getInstanceId())) {
                    found = true;

                    if ("failed".equals(instanceAbstract.getInstanceStatus())) {
                        break;
                    }
                    if (instanceAbstract.getInstanceStatus().equalsIgnoreCase("available")) {
                        if (!instanceAbstract.getLockMode().equalsIgnoreCase("unlock")) {
                            instanceAbstract.setInstanceStatus(instanceAbstract.getLockMode());
                        }
                    }
                    instanceAbstract.setInstanceExpireTime(resource.getExpireTime());
                    instanceAbstract.setProductType(resource.getProductType());
                    instanceAbstract.setResourceUuid(resource.getUuid());
                    // 针对代理节点，增加标识确认当前资源为收费版 or 免费版
                    if (StringUtils.isNotEmpty(instanceAbstract.getInstanceType())
                            && RDSConstant.INSTANCE_TYPE_PROXY.equalsIgnoreCase(instanceAbstract.getInstanceType())) {
                        boolean isCharge = updateProxyChargeInfo(resource);
                        instanceAbstract.setIsChargeProxy(isCharge);
                    }
                    updateExpireDate(resource, instanceAbstract);

                    instanceAbstracts.add(instanceAbstract);
                    break;
                }
            }
            if (found) {
                continue;
            }
            // 在resource列表中存在，但是后端服务中不存在
            logger.warn("rds instance {} (resourceId: {}) exist in resource list but cannot find in rds service list",
                    resource.getName(), resource.getId());
//            if (!forPolicy) {
//                InstanceAbstract instanceAbstract = new InstanceAbstract();
//                instanceAbstract.setInstanceStatus("notExist");
//                instanceAbstract.setInstanceId(resource.getName());
//                instanceAbstract.setInstanceName("");
//                instanceAbstract.setInstanceExpireTime(resource.getExpireTime());
//                instanceAbstract.setInstanceCreateTime(resource.getCreateTime());
//                instanceAbstract.setProductType(resource.getProductType());
//                instanceAbstract.setEngine("");
//                instanceAbstract.setEngineVersion("");
//                instanceAbstract.setInstanceType("master");
//                if (resource.getName().length() == 12) {        // resource的name为短ID时表示为raft版
//                    instanceAbstract.setInstanceType(RDSConstant.INSTANCE_TYPE_RAFT);
//                }
//                updateExpireDate(resource, instanceAbstract);
//                instanceAbstracts.add(instanceAbstract);
//            }
        }
        for (InstancePO instancePO : instancePOs) {
            if (!isValidInstance(instancePO, timeLeft, serviceType)) {
                continue;
            }
            logger.info("InstancePOs, instanceId:{},instanceUUid:{} ", instancePO.getInstanceId(),
                    instancePO.getInstanceUuid());
            if (StringUtils.isNotEmpty(instancePO.getSourceInstanceId())) {
                instancePO.setSourceInstanceId(instanceDao.queryInstanceUuid(instancePO.getSourceInstanceId()));
            }
            InstanceAbstract instanceAbstract = new InstanceAbstract(instancePO);
            updateLogicExpireDate(instanceAbstract);
//            if (filterInstance(instanceAbstract, filterMap)) {
//                continue;
//            }
            instanceAbstracts.add(instanceAbstract);
        }
//        for (InstanceAbstract abss : instanceAbstracts) {
//            logger.info("InstanceAbstracts: instanceId:{},instanceUUid:{}, expireDate:{}", abss.getInstanceShortId(),
//                    abss.getInstanceId(), abss.getExpireDate());
//        }
        if (!RDSConstant.FROM_CONSOLE.equalsIgnoreCase(from)) {
            sortInstanceAbstract(instanceAbstracts, order, orderBy);
        }
//        for (InstanceAbstract abss : instanceAbstracts) {
//            logger.info("After sort InstanceAbstracts: instanceId:{},instanceUUid:{} ",
//                    abss.getInstanceShortId(), abss.getInstanceId());
//        }
        return instanceAbstracts;
    }

    private boolean updateProxyChargeInfo(Resource resource) {
        if (CollectionUtils.isNotEmpty(resource.getFlavor())) {
            boolean freeType = false;
            boolean freeNodeAmount = false;
            List<Flavor.FlavorItem> flavor = resource.getFlavor();
            for (Flavor.FlavorItem flavorItem : flavor) {
                // 计费项中,主要依赖subServiceType\nodeAmount来判断收费情况
                if (StringUtils.isNotEmpty(flavorItem.getName())
                        && "subServiceType".equalsIgnoreCase(flavorItem.getName())
                        && StringUtils.isNotEmpty(flavorItem.getValue())
                        && (RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL
                        .equalsIgnoreCase(flavorItem.getValue()) ||
                        RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE
                                .equalsIgnoreCase(flavorItem.getValue()))) {
                    freeType = true;
                }

                if (StringUtils.isNotEmpty(flavorItem.getName())
                        && "nodeAmount".equalsIgnoreCase(flavorItem.getName())
                        && StringUtils.isNotEmpty(flavorItem.getValue())
                        && "2".equalsIgnoreCase(flavorItem.getValue())) {
                    freeNodeAmount = true;
                }
            }
            // 若满足条件，更新当前节点标识为免费版
            return !freeType || !freeNodeAmount;
        } else {
            return true;
        }
    }

    public boolean filterInstance(InstanceAbstract instanceAbstract, Map<String, String> filterMap,
                                  boolean onlyFilterMaster) {
        boolean isFiltered = false;
        if ((instanceAbstract.getSourceInstanceId() == null
                || "".equals(instanceAbstract.getSourceInstanceId())) || !onlyFilterMaster) {
            if (filterMap != null && filterMap.size() > 0) {
                Set<Map.Entry<String, String>> keywordTypeSet = filterMap.entrySet();
                for (Map.Entry<String, String> keywordTypeEntry : keywordTypeSet) {
                    // tag过滤
                    if ("tag".equalsIgnoreCase(keywordTypeEntry.getKey())) {
                        if (isFiltered = tagFilter(instanceAbstract, keywordTypeEntry)) {
                            break;
                        }
                    } else {
                        // 普通字段过滤
                        if (isFiltered = filtered(instanceAbstract, keywordTypeEntry)) {
                            break;
                        }
                    }
                }
            }
        }
        return isFiltered;
    }

    private List<InstanceAbstract> getInstancesBySubuser(long timeLeft,
                                                         Collection<Resource> resources,
                                                         Collection<InstanceAbstract> instances,
                                                         Map<String, String> filterMap,
                                                         String order,
                                                         String orderBy,
                                                         String serviceType) {
        List<InstanceAbstract> instanceAbstracts = new ArrayList<>();

        List<InstancePO> instancePOs = instanceDao
                .getInstanceListByInstanceStatus("creating", clientFactory.getAccountId(), null);

        for (InstanceAbstract instanceAbstract : instances) {
            if ("failed".equals(instanceAbstract.getInstanceStatus())) {
                continue;
            }
            boolean found = false;
            for (Resource resource : resources) {
                if (!isValidResource(resource, timeLeft, serviceType)) {
                    continue;
                }
                if (resource.getName().equals(instanceAbstract.getInstanceId())) {
                    found = true;
                    if (instanceAbstract.getInstanceStatus().equals("available")) {
                        if (!instanceAbstract.getLockMode().equals("unlock")) {
                            instanceAbstract.setInstanceStatus(instanceAbstract.getLockMode());
                        }
                    }

                    // 创建中的实例过滤掉
                    if (instanceAbstract.getInstanceStatus().equalsIgnoreCase("creating")) {
                        break;
                    }
                    instanceAbstract.setInstanceExpireTime(resource.getExpireTime());
                    instanceAbstract.setProductType(resource.getProductType());
                    instanceAbstract.setResourceUuid(resource.getUuid());
                    updateExpireDate(resource, instanceAbstract);

//                    if (filterInstance(instanceAbstract, filterMap)) {
//                        break;
//                    }
                    instanceAbstracts.add(instanceAbstract);
                    break;
                }
            }
            if (found) {
                continue;
            }
            // 在resource列表中存在，但是后端服务中不存在
            logger.warn("rds instance {} exist in instance list but cannot find in resource list",
                    instanceAbstract.getInstanceId());
        }
        for (InstancePO instancePO : instancePOs) {
            if (!isValidInstance(instancePO, timeLeft, serviceType)) {
                continue;
            }
            if (StringUtils.isNotEmpty(instancePO.getSourceInstanceId())) {
                instancePO.setSourceInstanceId(instanceDao.queryInstanceUuid(instancePO.getSourceInstanceId()));
            }
            InstanceAbstract instanceAbstract = new InstanceAbstract(instancePO);
//            if (filterInstance(instanceAbstract, filterMap)) {
//                break;
//            }
            updateLogicExpireDate(instanceAbstract);
            instanceAbstracts.add(instanceAbstract);
        }
        sortInstanceAbstract(instanceAbstracts, order, orderBy);
        return instanceAbstracts;
    }

    /**
     * 模板方法。若object中的相应字段不符合filter则返回true。否则返回false。模糊匹配，关键字为空时返回false
     *
     * @param object
     * @param filter filter.getKey()必须是object中的字段名，否则该filter失效
     * @param <T>
     * @return
     */
    private static <T>
    boolean filtered(T object, Map.Entry<String, String> filter) {
        String keyword = filter.getValue();
        String keywordType = filter.getKey();
//        logger.info("now in turn is : " + filter);
        if (StringUtils.isEmpty(keywordType)) {
            return false;
        }
        if (StringUtils.isEmpty(keyword)) {
            return false;
        }
        String upperParamType = keywordType.substring(0, 1).toUpperCase() + keywordType.substring(1);
        String valueParam;
        try {
            Method m = object.getClass().getMethod("get" + upperParamType);
            valueParam = (String) m.invoke(object);
        } catch (Exception ex) {
            logger.info("get" + upperParamType + " feature not exist in " + object);
            logger.info(ex.getMessage());
            return false;
        }

//        logger.info("keyword is '" + keyword + "' and valueParam is '" + valueParam + "'");
        if (valueParam == null || !valueParam.contains(keyword)) {
            return true;
        }
        return false;
    }

    private boolean isValidResource(Resource resource,
                                    long timeLeft,
                                    String serviceType) {
        if (StringUtils.isNotEmpty(serviceType) && !serviceType.equalsIgnoreCase(resource.getServiceType())) {
            return false;
        }
        if (timeLeft >= 0) {
            if (ResourceStatus.DESTROYED.equals(resource.getStatus()) || resource.getExpireTime() == null
                    || "postpay".equalsIgnoreCase(resource.getProductType())
                    || resource.getExpireTime().getTime() - new Date().getTime() > timeLeft) {
                return false;
            }
        } else {
            if (ResourceStatus.DESTROYED.equals(resource.getStatus())) {
                return false;
            }
        }
        return true;
    }

    private boolean isValidInstance(InstancePO instance,
                                    long timeLeft,
                                    String serviceType) {
        if (StringUtils.isNotEmpty(serviceType) && !serviceType.equalsIgnoreCase(instance.getInstanceType())) {
            return false;
        }
        if (timeLeft >= 0) {
            if (instance.getInstanceExpireTime() == null
                    || "postpay".equalsIgnoreCase(instance.getProductType())
                    || instance.getInstanceExpireTime().getTime() - new Date().getTime() > timeLeft) {
                return false;
            }
        }
        return true;
    }

    private boolean tagFilter(InstanceAbstract instance, Map.Entry<String, String> filter) {
        boolean find = false;
        if (instance == null || instance.getTags() == null) {
            return !find;
        }
        String[] tagKeyValue = filter.getValue().split("__");
        if (tagKeyValue.length < 1) {
            throw new CommonExceptions.RequestInvalidException("tag filter is invalid");
        }
        for (Tag tag : instance.getTags()) {
            logger.info("TagKey ='" + tag.getTagKey() + "',SubKeywordType = '" + tagKeyValue[0] + "'");
            String tagValue = tagKeyValue.length > 1?tagKeyValue[1]:null;
            if (tag.getTagKey().equals(tagKeyValue[0]) && (containsExceptEmpty(tag.getTagValue(), tagValue)
                    || "@@@".equals(tagValue))) {
                find = true;
                break;
            }
        }
        return !find;
    }

    private  boolean containsExceptEmpty(String value, String keyword) {
//        logger.info("value ='" + value + "',keyword = '" + keyword + "'");
        if (StringUtils.isNotEmpty(keyword) && (value.contains(keyword))
                || (StringUtils.isEmpty(value) && StringUtils.isEmpty(keyword))) {
            return true;
        }
        return false;
    }

    /**
     * 对InstanceAbstract排序
     *
     * @param instanceAbstracts
     * @param order
     * @param orderBy
     */
    private void sortInstanceAbstract(List<InstanceAbstract> instanceAbstracts,
                                      String order,
                                      String orderBy) {
        if (StringUtils.isEmpty(orderBy)) {
            return;
        }
        if ("tag".equalsIgnoreCase(orderBy)) {
            if ("desc".equalsIgnoreCase(order)) {
                Collections.sort(instanceAbstracts, new TagOfInstanceAbstractComparator(false));
            } else {
                Collections.sort(instanceAbstracts, new TagOfInstanceAbstractComparator(true));
            }
        } else {
            if ("vnetIp".equals(orderBy) || "inetIp".equals(orderBy)) {
                Collections.sort(instanceAbstracts, new ParamComparatorByIp(orderBy, order));
            } else {
                Collections.sort(instanceAbstracts, new ParamComparator<InstanceAbstract>(orderBy, order));
            }
        }
    }


    private void updateExpireDate(Resource resource, InstanceAbstract instanceAbstract) {
        if (ProductPayType.PRE_PAY.toString().equalsIgnoreCase(resource.getProductType())) {
            if (resource.getExpireTime() != null) {
                long timeLast = resource.getExpireTime().getTime() - new Date().getTime();
                instanceAbstract.setExpireDate((int) (timeLast / ONE_DAY_MILLISECOND));
            }
        }
    }

    private void updateLogicExpireDate(InstanceAbstract instanceAbstract) {
        if (Payment.isPrepay(instanceAbstract.getProductType())) {
            if (instanceAbstract.getInstanceExpireTime() != null) {
                // 8小时 = 8 × 3600000毫秒 = 28800000毫秒
                long timeLast = 28800000 + instanceAbstract.getInstanceExpireTime().getTime() - new Date().getTime();
                instanceAbstract.setExpireDate((int) (timeLast / ONE_DAY_MILLISECOND));
            }
        }
    }

    /**
     * 绑定相关计费变更订单类型到InstanceAbstract上
     *
     * @param instances
     */
    private void bindOrderStatus(List<InstanceAbstract> instances) {
        if (CollectionUtils.isEmpty(instances)) {
            return;
        }
        // 获取 TO_POSTPAY 和 TO_PREPAY 订单
        Map<String, Order> orderMap = rdsOrderService.getOrderMapByFilters(Arrays.asList(OrderStatus.DEFERRED_CREATE),
                Arrays.asList(OrderType.TO_POSTPAY, OrderType.TO_PREPAY));
        if (MapUtils.isEmpty(orderMap)) {
            return;
        }

        for (InstanceAbstract instance : instances) {
            // 添加 to_prepay to_postpay订单状态
            if (orderMap.containsKey(instance.getResourceUuid())) {
                Order tempOrder = orderMap.get(instance.getResourceUuid());
                instance.setOrderStatus(tempOrder.getType().name().toLowerCase());
            }
        }
    }

    /**
     * 绑定是否有自动续费信息到InstanceAbstract上
     *
     * @param instances
     */
    public void bindAutoRenew(Collection<InstanceAbstract> instances) {
        if (CollectionUtils.isEmpty(instances)) {
            return;
        }
        List<InstanceAbstract> instanceIds = new ArrayList<>();
        for (InstanceAbstract instanceAbstract : instances) {
            // 只有主实例支持预付费，只有预付费实例才可以开通自动续费
            if (!RDSConstant.PRODUCTTYPE_PREPAY.equals(instanceAbstract.getProductType())) {
                // 非预付费实例
                continue;
            }
            instanceIds.add(instanceAbstract);
        }
        if (instanceIds.isEmpty()) {
            return;
        }
        Map<String, AutoRenewRule> rdsAutoRenewMap = new HashMap<>();
        try {
            rdsAutoRenewMap = rdsBillingService.getAutoRenewRuleMapByInstanceIds(instanceIds);
        } catch (Exception ex) {
            // 降级
            logger.info("get tag info failed : " + ex.getMessage());
        }
        if (MapUtils.isEmpty(rdsAutoRenewMap)) {
            return;
        }
        for (InstanceAbstract instance : instances) {
            if (rdsAutoRenewMap.get(instance.getInstanceId()) != null) {
                instance.setTask(RDSConstant.AUTO_RENEW_TASK);
            }
        }
    }

    /**
     * 绑定tag信息到InstanceAbstract上
     *
     * @param instances return List<InstanceAbstract>
     */
    public List<InstanceAbstract> bindTagsToInstance(Collection<Instance> instances) {
        List<String> instanceIds = new ArrayList<>();
        List<InstanceAbstract> instanceAbstracts = new ArrayList<>();
        for (Instance instance : instances) {
            instanceIds.add(instance.getInstanceId());
            instanceAbstracts.add(new InstanceAbstract(instance, "北京"));
        }
        try {
            Map<String, List<Tag>> tagMap = rdsTagLogicalService.queryTagsByResourceUuids(instanceIds);
            for (InstanceAbstract instanceAbstract : instanceAbstracts) {
                instanceAbstract.setTags(tagMap.get(instanceAbstract.getInstanceId()));
            }
        } catch (Exception ex) {
            logger.info("get tag info failed : " + ex.getMessage());
        }
        return instanceAbstracts;
    }

    public Resources getResourceList() {
        Resources resources;
        try {
            GetResourcesRequest resourcesRequest = new GetResourcesRequest();
            resourcesRequest.setAccountId(clientFactory.getAccountId());
            resourcesRequest.setServiceType(ServiceType.RDS.toString());
            resourcesRequest.setRegion(regionConfiguration.getCurrentRegion());
            resourcesRequest.setLimit(Integer.MAX_VALUE);
            ResourceClient resourceClient = clientFactory.createResourceClient();
            resources = resourceClient.list(resourcesRequest);
            resourcesRequest.setServiceType(ServiceType.RDS_REPLICA.toString());
            resources.addAll(resourceClient.list(resourcesRequest));
            resourcesRequest.setServiceType(ServiceType.RDS_PROXY.toString());
            resources.addAll(resourceClient.list(resourcesRequest));
            return resources;
        } catch (BceInternalResponseException ex) {
            logger.warn("RDS get resource list fail! {}", ex);
            throw new RDSExceptions.ResourceNotExistException();
        } catch (Exception ex) {
            logger.warn("RDS get resource list fail! {}", ex);
            throw new RDSExceptions.ResourceServerException();
        }
    }

    /**
     * 按照跟FE的约定，对实例ID进行排序，master0, replica0-1, replica0-2, master1, replica1-1, replica1-2。。。类似顺序
     *
     * @param instances
     * @return
     */
    private List<InstanceAbstract> reorderInstanceList(List<InstanceAbstract> instances, String order, String orderBy) {
        List<InstanceAbstract> total = new ArrayList<>();
        if (instances == null || instances.size() == 0) {
            return total;
        }
        logger.warn("before total size {} : ", total.size());
        Map<String, List<InstanceAbstract>> masterIdReplicaMap = new HashMap<>();
//        Map<String, InstanceAbstract> master2proxy = new HashMap<>();
        Map<String, List<InstanceAbstract>> master2proxy = new HashMap<>();
        Map<String, List<InstanceAbstract>> newReplicaAndeProxyMap = new HashMap<>();
        List<InstanceAbstract> masterInstances = new ArrayList<>();
        for (InstanceAbstract instance : instances) {
            if (instance.getSourceInstanceId() == null || "".equals(instance.getSourceInstanceId())) {
                masterInstances.add(instance);
            } else if (instance.getInstanceType().equalsIgnoreCase("rdsproxy")) {
                if (!master2proxy.containsKey(instance.getSourceInstanceId())) {
                    master2proxy.put(instance.getSourceInstanceId(), new ArrayList<InstanceAbstract>());
                }
                master2proxy.get(instance.getSourceInstanceId()).add(instance);
            } else {
                if (!masterIdReplicaMap.containsKey(instance.getSourceInstanceId())) {
                    masterIdReplicaMap.put(instance.getSourceInstanceId(), new ArrayList<InstanceAbstract>());
                }
                logger.debug("masterInstances id is {}", instance.getInstanceId());
                masterIdReplicaMap.get(instance.getSourceInstanceId()).add(instance);
            }
        }
        logger.warn("before masterInstances size : " + masterInstances.size());

        // 保留
        List<InstanceAbstract> masterInstancesTmpForReorder = new ArrayList(masterInstances);

        // 筛选只读代理实例
        newReplicaAndeProxyMap.putAll(master2proxy);
//        newReplicaAndeProxyMap.putAll(masterIdReplicaMap);
        for (String key : masterIdReplicaMap.keySet()) {
            if (newReplicaAndeProxyMap.containsKey(key)) {
                newReplicaAndeProxyMap.get(key).addAll(masterIdReplicaMap.get(key));
            } else {
                newReplicaAndeProxyMap.put(key, masterIdReplicaMap.get(key));
            }
        }
        logger.warn("newReplicaAndeProxyMap size : " + newReplicaAndeProxyMap.size());
        for (Map.Entry<String, List<InstanceAbstract>> stringListEntry : newReplicaAndeProxyMap.entrySet()) {
            logger.debug("replicaInstance id is {}", stringListEntry.getKey());
            if (CollectionUtils.isNotEmpty(masterInstances)) {
                boolean found = false;
                for (InstanceAbstract masterInstance : masterInstances) {
                    logger.debug("replicaInstance id is .. {}", stringListEntry.getKey());
                    if (!stringListEntry.getKey().equals(masterInstance.getInstanceId())) {
                        continue;
                    } else {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    masterInstances.addAll(stringListEntry.getValue());
                    masterInstancesTmpForReorder.addAll(stringListEntry.getValue());
                }
            } else {
                masterInstances.addAll(stringListEntry.getValue());
                masterInstancesTmpForReorder.addAll(stringListEntry.getValue());
            }
        }
        sortInstanceAbstract(masterInstancesTmpForReorder, order, orderBy);
        logger.warn("after masterInstances size : " + masterInstances.size());
        // 由于鉴权不支持有序实例入参，为了保持列表有序，利用tmp实例列表来进行再排序
        List<InstanceAbstract> masterInstancesWithOrder = new ArrayList<>();
        for (InstanceAbstract eachOrder : masterInstancesTmpForReorder) {
            String shortId = eachOrder.getInstanceShortId();
            for (InstanceAbstract eachPermission :  masterInstances) {
                // found
                if (ObjectUtils.equals(shortId, eachPermission.getInstanceShortId())) {
                    masterInstancesWithOrder.add(eachPermission);
                    break;
                }
            }
        }
        masterInstances = masterInstancesWithOrder;
//        List<InstanceAbstract> readInstanceList = new ArrayList<>();
        for (InstanceAbstract master : masterInstances) {
            total.add(master);
            if (master2proxy.containsKey(master.getInstanceId())) {
//                total.add(master2proxy.get(master.getInstanceId()));
//                master.setHasProxy(true);
                for (InstanceAbstract proxy : master2proxy.get(master.getInstanceId())) {
                    logger.warn("after proxyID {} : ", proxy.getInstanceId());
                    if (proxy.getInstanceType().equals("rdsproxy")) {
                        total.add(proxy);
                    }
                }
                master2proxy.remove(master.getInstanceId());
                master.setHasProxy(true);
            }
            if (masterIdReplicaMap.containsKey(master.getInstanceId())) {
                logger.warn("masterIdReplicaMap size {} : ", masterIdReplicaMap.size());
                for (InstanceAbstract replica : masterIdReplicaMap.get(master.getInstanceId())) {
                    logger.warn("after replicaID {} : ", replica.getInstanceId());
                    total.add(replica);
                }
                masterIdReplicaMap.remove(master.getInstanceId());
                master.setHasSlave(true);
            }
        }
        logger.warn("total size {} : ", total.size());

//        for (Map.Entry<String, List<InstanceAbstract>> stringListEntry : masterIdReplicaMap.entrySet()) {
//            logger.debug("replicaInstance id is {}", stringListEntry.getKey());
//            readInstanceList.addAll(stringListEntry.getValue());
//        }
//        for (Map.Entry<String, List<InstanceAbstract>> stringListEntry : master2proxy.entrySet()) {
//            readInstanceList.addAll(stringListEntry.getValue());
//        }
//        Collections.sort(readInstanceList,
//                new ParamComparator<InstanceAbstract>("instanceCreateTime", "desc"));
        // 经过鉴权后，与原始size可能会不一样
//        if (total.size() != instances.size()) {
//            logger.warn("rds instance list contains error, ordered list size not equals origin list size.");
//        }
//        total.addAll(readInstanceList);
        return total;
    }

    // 批量鉴权
    public Collection<InstanceAbstract> batchPermission(Collection<InstanceAbstract> masterInstances) {

        // 主账号直接返回，不进行过滤
//        if (ObjectUtils.equals(clientFactory.getAccountId(),
//                clientFactory.getUserId())) {
//            logger.info("batchPermission is root user {}", clientFactory.getAccountId());
//            return masterInstances;
//        }

        try {
            List<String> resourcesIds = new ArrayList<String>();

            // 为了匹配最后的结果
            Map<String, InstanceAbstract> allInstanceMap = new HashMap<String, InstanceAbstract>();

            Map<String, String> shortIdLongIdMap = new HashMap<String, String>();

            for (InstanceAbstract eachInstance : masterInstances) {

                String resourceId = "instance/" + eachInstance.getInstanceShortId();
                logger.debug("[batchPermission] each resourceId {}", resourceId);
                resourcesIds.add(resourceId);

                allInstanceMap.put("instance/" + eachInstance.getInstanceId(), eachInstance);
                shortIdLongIdMap.put(eachInstance.getInstanceShortId(), eachInstance.getInstanceId());
                logger.debug("[batchPermission] put map {}, {}", eachInstance.getInstanceShortId(),
                        eachInstance.getInstanceId());
            }

            List<String> perms = new ArrayList<String>();
            perms.add(RDSConstant.READ);
            perms.add(RDSConstant.WRITE);
            perms.add(RDSConstant.OPERATE);
            perms.add(RDSConstant.FULL_CONTROL);

            LocalMasterShortIdConverter conver = new LocalMasterShortIdConverter();

            conver.setIdsMap(allInstanceMap);

            List<String> filterResult = rermissionServiceForRds.filterResourceList(clientFactory.getAccountId(),
                    clientFactory.getUserId(),
                    RDSConstant.SERVICE_RDS,
                    shortIdLongIdMap, perms);

            logger.info("batch result : {}", Arrays.toString(filterResult.toArray()));

            List<InstanceAbstract> filterMasterInstances = new ArrayList<>();
            for (String eachHasPermInstance : filterResult) {

                InstanceAbstract instance = allInstanceMap.get("instance/" + eachHasPermInstance);

                if (instance != null) {
                    filterMasterInstances.add(instance);
                }
            }

            return filterMasterInstances;
        } catch (Exception e) {
            logger.error("error", e);
            throw new RDSExceptions.CheckPermissionException();
        } // 鉴权结束
    }

    public InstancePO checkInstanceByStatus(String instanceId, String instanceStatusStr) {
        InstancePO instancePO = instanceDao.querySimpleInstanceById(instanceId, clientFactory.getAccountId());
        if (instancePO == null) {
            throw new RDSExceptions.ResourceNotExistException();
        }
        if (!instanceStatusStr.contains(instancePO.getInstanceStatus())) {
            throw new RDSExceptions.InvalidAction();
        }
        return instancePO;
    }

    public void checkInstanceSpecification(PriceDiffModel config) {
        if (config.getCpuCount() != null && config.getAllocatedMemoryInGB() != null) {
            if ((config.getCpuCount() == 6 && config.getAllocatedMemoryInGB() == 8)
                    ||(config.getCpuCount() == 12 && config.getAllocatedMemoryInGB() == 24)
                    ||(config.getCpuCount() == 20 && config.getAllocatedMemoryInGB() == 48)
                    ||(config.getCpuCount() == 20 && config.getAllocatedMemoryInGB() == 64)
                    ||(config.getCpuCount() == 56 && config.getAllocatedMemoryInGB() == 480)
            ) {
                throw new RDSExceptions.ParamValidationException("This specification has been sold out.");
            }
        }
    }

    public void getZoneAndSubnetUuid(PriceDiffModel config) {
        if (config.getMasterAzone() != null) {
            String logicalMasterZone = othersService.apiZoneTologicalZone(config.getMasterAzone());
            config.setMasterAzone(logicalMasterZone);
        }
        if (config.getBackupAzone() != null) {
            String logicalBackupZone = othersService.apiZoneTologicalZone(config.getBackupAzone());
            config.setBackupAzone(logicalBackupZone);
        }
        if (config.getSubnets().size() > 0) {
            for (InstanceCreateModel.SubnetMap subnetMap : config.getSubnets()) {
                String logicalZone = othersService.apiZoneTologicalZone(subnetMap.getZoneName());
                SubnetVo subnetVo = clientFactory.createSubnetClient()
                        .findSubnetWithIpUsage(subnetMap.getSubnetId());
                if (subnetVo != null && subnetVo.getAz().equals(logicalZone)) {
                    config.setSubnetId(config.getSubnetId() + logicalZone + ":" + subnetVo.getSubnetUuid() + ",");
                } else {
                    throw new RDSExceptions.ParamValidationException("subnetId match zoneName error");
                }
            }
            config.setSubnetId(config.getSubnetId().substring(0, config.getSubnetId().length() - 1));
        }
        if (config.getDiskType() != null
                && config.getDiskIoType().equalsIgnoreCase("cloud_enha")){
            if (config.getVolumeCapacity() < 50){
                throw new RDSExceptions.ParamValidationException("volumecapacity min 50.");
            }
        }
    }

    public Instance checkInstanceByStatusForBackend(String instanceUuid, String instanceStatusStr) {
        if (StringUtils.isEmpty(instanceUuid)) {
            throw new RDSBusinessExceptions.ResourceNotExistException();
        }
        if (BasisUtils.isShortId(instanceUuid) &&
                !clientFactory.getInstanceTypeByUuid(instanceUuid).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            throw new RDSBusinessExceptions.InvalidAction();
        }
        Instance instance = clientFactory.createRdsClient2ByInstanceId(instanceUuid)
                .instanceDescribe(instanceUuid).getInstance();
        if (instance == null) {
            throw new RDSBusinessExceptions.ResourceNotExistException();
        }
        if (!instanceStatusStr.contains(instance.getInstanceStatus())) {
            throw new RDSBusinessExceptions.InvalidInstanceStatus();
        }
        return instance;
    }

    // 变配前置检查接口，复用的地方较多，顾此处重写一份
    public Instance checkInstanceByStatusForBackend(String instanceUuid, String instanceStatusStr,
                                                    InstanceExtension instance) {
        /**
         * 实例在热活组中且所处的热活组内有实例处于非available状态时，不允许变配
         */
        if (StringUtils.isNotEmpty(instance.getGroupId())) {
            String groupId = instance.getGroupId();
            InstanceGroupDetailResponse instanceGroupDetailResponse
                    = clientFactory.createRdsClient().instanceGroupDetail(groupId);
            if (instanceGroupDetailResponse.getGroup().getLeader() != null
                    && StringUtils.isNotEmpty(instanceGroupDetailResponse.getGroup().getLeader().getStatus())
                    && !RDSConstant.INSTANCE_GROUP_RESIZE_AVAI_STATUS.
                    contains(instanceGroupDetailResponse.getGroup().getLeader().getStatus())) {
//                    && !"available".equalsIgnoreCase(instanceGroupDetailResponse.getGroup().getLeader().getStatus())) {
                throw new RDSBusinessExceptions.ResizeUnsupportInGroupException
                        (instanceGroupDetailResponse.getGroup().getLeader().getInstanceIdShort()
                                , instanceGroupDetailResponse.getGroup().getLeader().getStatus());
            }

            if (CollectionUtils.isNotEmpty(instanceGroupDetailResponse.getGroup().getFollowers())) {
                for (GroupInfo.GroupMember member : instanceGroupDetailResponse.getGroup().getFollowers()) {
                    if (StringUtils.isNotEmpty(member.getStatus())
                            && StringUtils.isNotEmpty(member.getInstanceIdShort())
                            && !RDSConstant.INSTANCE_GROUP_RESIZE_AVAI_STATUS.contains(member.getStatus())) {
                        throw new RDSBusinessExceptions.ResizeUnsupportInGroupException
                                (member.getInstanceIdShort(), member.getStatus());
                    }
                }
            }
        }
        /**
         * 当变配代理实例时，若主实例非 available,则不允许对代理实例发起变配
         */
        if (StringUtils.isNotEmpty(instance.getInstanceType())
                && RDSConstant.INSTANCE_TYPE_PROXY.equalsIgnoreCase(instance.getInstanceType())
                && instance.getTopology().getMasterIdMapping() != null
                && !"available".equalsIgnoreCase(instance.getTopology().getMasterIdMapping().getStatus())) {
            throw new RDSBusinessExceptions.InstanceStatusUnsatisfiedException(instanceUuid,
                    instance.getTopology().getMasterIdMapping().getAppId(),
                    instance.getTopology().getMasterIdMapping().getStatus());
        }

        if (StringUtils.isEmpty(instanceUuid)) {
            throw new RDSBusinessExceptions.ResourceNotExistException();
        }
        if (BasisUtils.isShortId(instanceUuid) &&
                !clientFactory.getInstanceTypeByUuid(instanceUuid).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            throw new RDSBusinessExceptions.InvalidAction();
        }
//        Instance instance = clientFactory.createRdsClient2ByInstanceId(instanceUuid)
//                .instanceDescribe(instanceUuid).getInstance();
        if (instance == null) {
            throw new RDSBusinessExceptions.ResourceNotExistException();
        }
        if (!instanceStatusStr.contains(instance.getInstanceStatus())) {
            throw new RDSBusinessExceptions.InvalidInstanceStatus();
        }
        // 若当前变配的是只读实例，需校验对应主实例的状态
        if (RDSConstant.INSTANCE_TYPE_REPLICA.equalsIgnoreCase(instance.getInstanceType())
                && instance.getTopology().getMasterIdMapping() != null) {
            Instance.MasterIdMapping masterIdMapping = instance.getTopology().getMasterIdMapping();
            if (!instanceStatusStr.contains(masterIdMapping.getStatus())) {
                throw new RDSBusinessExceptions.InvalidInstanceStatus();
            }
        }
        return instance;
    }

    public void updateInstanceName(String instanceId, InstanceUpdateNameRequest request) {
        Instance instance = checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_MODIFY_NAME);
        instance.setInstanceShortId(instanceDao.queryInstanceId(instanceId));
        List<Instance> deleteResourceGroups = new ArrayList<>();
        instance.setInstanceName(request.getInstanceName());
        deleteResourceGroups.add(instance);
        if (clientFactory.getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            clientFactory.createRdsClientByInstanceId(instanceId).instanceModifyNameRaft(instanceId, request);
        } else {
            clientFactory.createRdsClient().instanceModifyName(instanceId, request);
        }

        Map<String, Object> map = new HashMap<>();
        map.put(InstanceTableColums.instanceName, request.getInstanceName());
//        instanceDao.updateInstanceByInstanceUuid(map, instanceId, clientFactory.getAccountId());
        changeResource(deleteResourceGroups, "update", clientFactory.getAccountId());
    }

    public void updatePubliclyAccessible(String instanceId, InstanceUpdatePublicAccessibleRequest request) {
        checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_MODIFY_PUBLICACCESS);

        if (request.isPubliclyAccessible()) {
            FeatureAclRequest aclRequest = new FeatureAclRequest("EipBlackList",
                    null, "AccountId", clientFactory.getAccountId());
            if (clientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist()) {
                throw new RDSExceptions.NotSupportOperation();
            }
        }
        if (clientFactory.getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2)
                    .instanceModifyPubliclyAccessibleRaft(instanceId, request);
        } else {
            clientFactory.createRdsClient().instanceModifyPubliclyAccessible(instanceId, request);
        }
        Map<String, Object> map = new HashMap<>();
        map.put(InstanceTableColums.publiclyAccessible, request.isPubliclyAccessible());
        map.put(InstanceTableColums.eipStatus, RDSConstant.EIP_STATUS_CTEATING);
        instanceDao.updateInstanceByInstanceUuid(map, instanceId, clientFactory.getAccountId());
    }

    public void updateDomain(String instanceId, InstanceUpdateAddressRequest request, String from) {
        checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_MODIFY_DOMAIN);

        String baseDomain = getBaseDomain(instanceId);
        String prefixDomain = "";
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            prefixDomain = request.getEndpoint().getAddress();
        } else {
            prefixDomain = getPrefixDomain(request.getEndpoint().getAddress());
        }
        if (!Pattern.matches(PatternString.PATTERN_ADDRESS, prefixDomain)) {
            throw new RDSExceptions.ParamValidationException();
        }
        String domain = prefixDomain + baseDomain;

        request.getEndpoint().setAddress(domain);
        if (clientFactory.getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2).instanceModifyDomainRaft(instanceId, request);
        } else {
            clientFactory.createRdsClient().instanceModifyDomain(instanceId, request);
        }
        Map<String, Object> map = new HashMap<>();
        map.put(InstanceTableColums.address, domain);
        instanceDao.updateInstanceByInstanceUuid(map, instanceId, clientFactory.getAccountId());
    }

    private String getPrefixDomain(String domain) {
        int prefixDomainIndex = domain.indexOf(".");
        return domain.substring(0, prefixDomainIndex);
    }

    private String getBaseDomain(String instanceId) {
        Instance instance = clientFactory.createRdsClient2ByInstanceId(instanceId)
                .instanceDescribe(instanceId).getInstance();
        String address = instance.getEndpoint().getAddress();
        return address.substring(address.indexOf("."));
    }

    public void updateReplicationType(String instanceId, InstanceUpdateReplicationTypeRequest request) {
        request.setReplicationType(BasisUtils.lowerCaseFirstChar(request.getReplicationType()));

        checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_MODIFY_REPLICATIONTYPE);
        clientFactory.createRdsClient().instanceModifyReplicationType(instanceId, request);

        Map<String, Object> map = new HashMap<>();
        map.put(InstanceTableColums.replicationType, RDSConstant.REPLICATION_TYPE_MODIFYING);
        instanceDao.updateInstanceByInstanceUuid(map, instanceId, clientFactory.getAccountId());
    }

    public void reboot(InstanceIdRequest request) {
        checkInstanceByStatusForBackend(request.getInstanceId(), RDSConstant.INSTANCE_STATUS_REBOOT);
        if (clientFactory.getInstanceTypeByUuid(request.getInstanceId()).
                equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2).instanceRebootRaft(request.getInstanceId());
        } else {
            clientFactory.createRdsClient().instanceReboot(request);
        }
        // 去掉状态更新，防止前后端状态不一致的情况
//        Map<String, Object> map = new HashMap<>();
//        map.put(InstanceTableColums.instanceStatus, RdsInstanceStatus.REBOOTING.getValue());
//        instanceDao.updateInstanceByInstanceUuid(map, instanceId, clientFactory.getAccountId());
    }

    public void reboot(String instanceId, EffectiveTimeRequest request) {
        checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_REBOOT);
        if (clientFactory.getInstanceTypeByUuid(instanceId).
                equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2).instanceRebootRaft(instanceId);
        } else {
            clientFactory.createRdsClient().instanceReboot(instanceId, request);
        }

    }

    public InstanceReplicaDelayMaster replicaDelay(String instanceId) {
        if (BasisUtils.isShortId(instanceId)) {
            return new InstanceReplicaDelayMaster().withSecondsBehindMaster(0);
        }
        return clientFactory.createRdsClient().getReplicaDelay(instanceId);
    }

    public boolean checkMasterQuota() {
        return quotaValidatorFactory.getQuotaValidator(ServiceType.RDS).validate(null);
    }

    public boolean checkReplicaQuota(String instanceId) {
        return quotaValidatorFactory.getQuotaValidator(ServiceType.RDS_REPLICA).validate(instanceId);
    }

    public void release(String instanceId) {
        Instance instance = null;
        try {
            instance = clientFactory.createRdsClient().instanceDescribe(instanceId).getInstance();
            delete(instance);
        } catch (RDSExceptions.AvailableInstanceException ex) {
            throw ex;
        }
    }

    public boolean checkStatusForDelete(Instance instance) {
        if (instance == null) {
            return false;
        }
        if (RDSConstant.INSTANCE_STATUS_DELETE.contains(instance.getInstanceStatus())) {
//            if (RdsInstanceStatus.AVAILABLE.getValue().equalsIgnoreCase(instance.getInstanceStatus())
//                    && Payment.isPrepay(instance.getProductType())
//                    && instance.getInstanceExpireTime().after(new Date())) {
//                return false;
//            }
            return true;
        } else {
            return false;
        }
    }

    public Boolean prePayStatus (ResourceStatus status, Boolean expired) {
        if (ObjectUtils.equals(status, ResourceStatus.RUNNING) && expired) {
            return true;
        } else {
            return false;
        }
    }

//    /**
//     * 释放单个实例进回收站
//     * @param instanceId
//     * @param from
//     */
//
//    public void delete(String instanceId, String from) {
//
//        if (instanceId == null) {
//            throw new RDSExceptions.BadRequestException();
//        }
//        Map<String, Resource> resourceMap;
//
//        try {
//            resourceMap = getResourceMap().get();
//        } catch (Exception e) {
//            logger.error("get resource error : " + e);
//            throw new RDSExceptions.ResourceServerException();
//        }
//
//        RDSClient client = clientFactory.createRdsClient();
//
//        InstanceExtension instancedetail = detail(instanceId, from);
//
//        // 检查实例状态是否可以删除
//        if (!RdsInstanceStatus.deletableStatus.contains(instancedetail.getInstanceStatus())) {
//            logger.error("Not available for delete instanceId:{}, state:{}",
//                    instanceId, instancedetail.getInstanceStatus());
//            throw new RDSExceptions.AvailableInstanceException();
//        }
//
//        Resource resource = resourceMap.get(instancedetail.getInstanceId());
//        // 未查到资源信息
//        if (resource == null) {
//            logger.error("Get resource null for instanceId:{}", instanceId);
//            throw new RDSExceptions.InstanceNotExistException();
//        }
//
//        // 判断相关实例是否存在未完成的订单，如果有的话，则释放操作失败。
//        List<Order> orders = getBusyOrders();
//        if (null != resource &&
//                (isMasterInstance(instancedetail.getInstanceType()) ||
//                        isRaftInstance(instancedetail.getInstanceType()))) {
//            for (Order order : orders) {
//                if (order.getResourceIds() != null && StringUtils.isNotEmpty(resource.getUuid())) {
//                    if (order.getResourceIds().contains(resource.getUuid())) {
//                        logger.warn("There's an instance will failed to destroy, instanceId: {}",
//                                instanceId);
//                    }
//
//                }
//            }
//        }
//
//        OrderClient orderClient = null;
//        ResourceClientV2 resourceClientV2 = null;
//        if (resource != null) {
//            try {
//                // 预付费退款
//                if (ProductPayType.PRE_PAY.alias.equals(resource.getProductType())) {
//                    if (orderClient == null) {
//                        orderClient = clientFactory.createOrderClient();
//                    }
//                    RefundRequest refundRequest = new RefundRequest();
//                    refundRequest.setUuid(resource.getUuid());
//                    refundRequest.setSource("OSP");
//                    refundRequest.setRefundReason("RDS 用户释放实例");
//                    orderClient.refund(refundRequest);
//                } else {
//                    // 后付费手动停服
//                    if (resourceClientV2 == null) {
//                        resourceClientV2 = clientFactory.createResourceClientV2();
//                    }
//                    ResourceKey resourceKey = new ResourceKey();
//                    resourceKey.setServiceType(resource.getServiceType());
//                    resourceKey.setRegion(resource.getRegion());
//                    resourceKey.setResourceName(resource.getName());
//                    ManualOpResponse opResponse = resourceClientV2.manualStop(resourceKey);
//                    // 可能会失败
//                    if (!opResponse.isSuccess()) {
//                        throw new BceInternalResponseException(opResponse.getReason());
//                    }
//                }
//            } catch (BceInternalResponseException e) {
//                logger.error("Delete resource error, resource uuid:{}, instanceId:{}",
//                        resource.getUuid(), instanceId);
//                // 删除资源出错的情况下不删除后端，否则会资源泄露
//                throw new RDSExceptions.ReleaseInstanceException(e.getMessage());
//            }
//
//            try {
//                // scs后端使用showId
//                client.stopSingleInstance(instanceId);
//            } catch (BceInternalResponseException e) {
//                logger.error("Stop rds error, instanceId:{}", instanceId);
//            }
//        }
//    }

    public List<String> oldDelete(String[] instanceIds, String from) {
        List<String> delFailInstanceIds = new ArrayList<>();
        List<String> delFailInstanceShortIds = new ArrayList<>();
        String[] shortInstanceId = new String[instanceIds.length];
        try {
            HashMap<String, Instance> instanceMap = getInstanceMap();
            for (int i = 0; i < instanceIds.length; i++) {
                if (RDSConstant.FROM_API.equalsIgnoreCase(from) || containShortId(instanceIds)) {
                    shortInstanceId[i] = instanceIds[i];
                    if (instanceDao.countNotDeleted(instanceIds[i], clientFactory.getAccountId()) > 0) {
                        logger.info("Instance or its child is not available, instanceId: {}", instanceIds[i]);
                        throw new RDSExceptions.AvailableInstanceException();
                    }
                    instanceIds[i] = instanceDao.queryInstanceUuid(instanceIds[i]);
                }
                if (StringUtils.isEmpty(instanceIds[i])) {
                    throw new RDSExceptions.ResourceNotExistException();
                }
                Instance instance = instanceMap.get(instanceIds[i]);
                if (!checkStatusForDelete(instance)) {
                    logger.info("Instance is not available, instanceId: {}", instanceIds[i]);
                    throw new RDSExceptions.AvailableInstanceException();
                }
            }
            HashMap<String, Resource> resourceMap = getResourceMapKeyByInstanceId();
            List<Order> orders = getBusyOrders();

            ResourceClient resourceClient = clientFactory.createResourceClient();
            UpdateResourceRequest updateResourceRequest = new UpdateResourceRequest();
            updateResourceRequest.setStatus(ResourceStatus.DESTROYED);

            List<String> delInstanceIds = new ArrayList<>();

            // 预付费实例不能通过控制台、api删除，除非resource是STOPPED、DESTROYED、DESTROYING状态
            for (String eachInstance : instanceIds) {
                Resource eachResource = resourceMap.get(eachInstance);
                if (eachResource != null
                        && StringUtils.equals(eachResource.getProductType(), ProductPayType.PRE_PAY.alias)
                        && !ObjectUtils.equals(eachResource.getStatus(), ResourceStatus.STOPPED)
                        && !ObjectUtils.equals(eachResource.getStatus(), ResourceStatus.DESTROYED)
                        && !ObjectUtils.equals(eachResource.getStatus(), ResourceStatus.DESTROYING)
                        && !(ObjectUtils.equals(eachResource.getStatus(), ResourceStatus.RUNNING)
                        && eachResource.getExpired())) {
                    throw new RDSExceptions.PrepayInstanceDeleteException();
                }
            }
            List<Instance> deleteResourceGroups = new ArrayList<>();
            for (int i = 0; i < instanceIds.length; i++) {
                Instance instance = clientFactory.createRdsClient2ByInstanceId(instanceIds[i])
                        .instanceDescribe(instanceIds[i]).getInstance();
//                instance.setInstanceShortId(instanceDao.queryInstanceId(instanceIds[i]));
                if ("DCC".equalsIgnoreCase(instance.getMachineType())) {
                    delInstanceIds.add(instanceIds[i]);
                    continue;
                }
                Resource resource = resourceMap.get(instanceIds[i]);
                if (null != resource &&
                        (isMasterInstance(instance.getInstanceType()) || isRaftInstance(instance.getInstanceType()))) {
                    for (Order order : orders) {
                        if (order.getResourceIds() != null && StringUtils.isNotEmpty(resource.getUuid())) {
                            if (order.getResourceIds().contains(resource.getUuid())) {
                                delFailInstanceIds.add(instanceIds[i]);
                                delFailInstanceShortIds.add(shortInstanceId[i]);
                                logger.warn("There's an instance will failed to destroy, instanceId: {}",
                                        instanceIds[i]);
//                                throw new RDSExceptions.ResourceInTaskException();
//                                if (OrderStatus.READY_FOR_CREATE.equals(order.getStatus())
//                                        && "available".equals(instance.getInstanceStatus().toLowerCase())) {
//                                    delInstanceIds.add(instanceIds[i]);
//                                } else {
//                                    delFailInstanceIds.add(instanceIds[i]);
//                                    delFailInstanceShortIds.add(shortInstanceId[i]);
//                                    logger.warn("There's an instance will failed to destroy, instanceId: {}",
//                                            instanceIds[i]);
//                                }
                            }
                        }
                    }
                }

                try {
                    if (isMasterInstance(instance.getInstanceType())
                            && CollectionUtils.isNotEmpty(instance.getReadReplica())) {
                        // destroy replica resource
                        for (String replicaId : instance.getReadReplica()) {
                            Resource res = resourceMap.get(replicaId);
                            if (res == null || (res != null && retryDestroyResource(
                                    resourceClient, res, updateResourceRequest, replicaId) > 0)) {
//                                lockOneInstance(instance);
                                delInstanceIds.add(replicaId);
                                Instance readReplica = new Instance();
//                                String instanceShortId = instanceDao.queryInstanceId(replicaId);
//                                readReplica.setInstanceShortId(instanceShortId);
                                readReplica.setInstanceId(replicaId);
                                readReplica.setInstanceType(RDSConstant.INSTANCE_TYPE_REPLICA);
                                deleteResourceGroups.add(readReplica);
//                                logger.warn("instanceReadReplica delete resource destroy, " +
//                                                "instanceId: {} instanceShortId {}",
//                                        replicaId, instanceShortId);
                            }

                        }
                    }

                    if (isMasterInstance(instance.getInstanceType())
                            && CollectionUtils.isNotEmpty(instance.getTopology().getRdsproxy())) {
                        // destroy proxy resource
                        for (String proxyId : instance.getTopology().getRdsproxy()) {
                            Resource res = resourceMap.get(proxyId);
                            if (res == null || (res != null && retryDestroyResource(
                                    resourceClient, res, updateResourceRequest, proxyId) > 0)) {
//                                lockOneInstance(instance);
                                delInstanceIds.add(proxyId);
                                Instance proxy = new Instance();
//                                String instanceShortId = instanceDao.queryInstanceId(proxyId);
//                                proxy.setInstanceShortId(instanceShortId);
                                proxy.setInstanceId(proxyId);
                                proxy.setInstanceType(RDSConstant.INSTANCE_TYPE_PROXY);
                                deleteResourceGroups.add(proxy);
//                                logger.warn("instanceRdsproxy delete resource destroy, " +
//                                                "instanceId: {} instanceShortId {}",
//                                        proxyId, instanceShortId);
                            }
                        }
                    }
                    if (!delFailInstanceIds.contains(instanceIds[i])) {
                        if (resource == null || (resource != null && retryDestroyResource(
                                resourceClient, resource, updateResourceRequest, instanceIds[i]) > 0)) {
//                            lockOneInstance(instance);
                            delInstanceIds.add(instanceIds[i]);
                            Instance master = new Instance();
//                            String instanceShortId = instanceDao.queryInstanceId(instanceIds[i]);
//                            master.setInstanceShortId(instanceShortId);
                            master.setInstanceId(instanceIds[i]);
                            master.setInstanceType(instance.getInstanceType());
                            deleteResourceGroups.add(master);
//                            logger.warn("instanceMaster delete resource destroy, " +
//                                            "instanceId: {} instanceShortId {}",
//                                    instanceIds[i], instanceShortId);
                        }
                    }
                } catch (Exception ex) {
                    // 只读或代理有一个删除失败，主实例就不能删除
                    delFailInstanceIds.add(instanceIds[i]);
                    delFailInstanceShortIds.add(shortInstanceId[i]);
                }
            }
            if (delInstanceIds != null && delInstanceIds.size() > 0) {
                HashSet set = new HashSet(delInstanceIds);
                delInstanceIds.clear();
                delInstanceIds.addAll(set);
            }
            changeResource(deleteResourceGroups, "delete", clientFactory.getAccountId());
            lockInstanceBatch(delInstanceIds);
            deleteInstanceBatch(delInstanceIds);
            logger.info("batch delete instance: {}. failed delete instance: {}.", delInstanceIds, delFailInstanceIds);
        } catch (BceInternalResponseException ex) {
            if ("OrderExceptions.ResourceInTaskException".equals(ex.getCode())) {
                throw new RDSExceptions.ResourceInTaskException();
            }
            throw ex;
        }
        if (RDSConstant.FROM_API.equalsIgnoreCase(from) || shortInstanceId.length > 0) {
            return delFailInstanceShortIds;
        }
        return delFailInstanceIds;
    }

    /**
     * 释放实例进回收站
     * @param instanceIds
     *
     */
    public List<String> delete(String[] instanceIds, String from) {

        // 阻塞状态集合
        List<OrderStatus> blockedOrderStatus = Arrays.asList(OrderStatus.NEED_PURCHASE, OrderStatus.NEED_CONFIRM,
                OrderStatus.READY_FOR_CREATE, OrderStatus.CREATING, OrderStatus.DEFERRED_CREATE);
        List<String> delFailInstanceIds = new ArrayList<>();
        List<String> delFailInstanceShortIds = new ArrayList<>();
        String[] shortInstanceId = new String[instanceIds.length];
        OrderClient orderClient = clientFactory.createOrderClient();
        OrderClientV2 orderClient2 = clientFactory.createOrderClientV2();
        try {
            HashMap<String, Instance> instanceMap = getInstanceMap();
            for (int i = 0; i < instanceIds.length; i++) {
                if (RDSConstant.FROM_API.equalsIgnoreCase(from) || containShortId(instanceIds)) {
                    shortInstanceId[i] = instanceIds[i];
                    // 当前存在状态不是'available'与 'lockExpiration' 且delete=0的实例
                    if (instanceDao.countNotDeleted(instanceIds[i], clientFactory.getAccountId()) > 0) {
                        logger.info("Instance or its child is not available, instanceId: {}", instanceIds[i]);
                        throw new RDSExceptions.AvailableInstanceException();
                    }
                    instanceIds[i] = instanceDao.queryInstanceUuid(instanceIds[i]);
                }
                if (StringUtils.isEmpty(instanceIds[i])) {
                    throw new RDSExceptions.ResourceNotExistException();
                }
                Instance instance = instanceMap.get(instanceIds[i]);
                if (!checkStatusForDelete(instance)) {
                    if (instance != null) {
                        logger.info("Instance detail is : instanceId : {}, instanceStatus : {}",
                                instance.getInstanceId() , instance.getInstanceStatus());
                    }
                    logger.info("Instance is not available, instanceId: {}", instanceIds[i]);
                    throw new RDSExceptions.AvailableInstanceException();
                }
            }
            HashMap<String, Resource> resourceMap = getResourceMapKeyByInstanceId();
            List<Order> orders = getBusyOrders();

            ResourceClient resourceClient = clientFactory.createResourceClient();
            UpdateResourceRequest updateResourceRequest = new UpdateResourceRequest();
            updateResourceRequest.setStatus(ResourceStatus.DESTROYED);

            List<String> delInstanceIds = new ArrayList<>();

            // 预付费实例不能通过控制台、api删除，除非resource是STOPPED、DESTROYED、DESTROYING状态
//            for (String eachInstance : instanceIds) {
//                Resource eachResource = resourceMap.get(eachInstance);
//                if (eachResource != null
//                        && StringUtils.equals(eachResource.getProductType(), ProductPayType.PRE_PAY.alias)
//                        && !ObjectUtils.equals(eachResource.getStatus(), ResourceStatus.STOPPED)
//                        && !ObjectUtils.equals(eachResource.getStatus(), ResourceStatus.DESTROYED)
//                        && !ObjectUtils.equals(eachResource.getStatus(), ResourceStatus.DESTROYING)
//                        && !(ObjectUtils.equals(eachResource.getStatus(), ResourceStatus.RUNNING)
//                        && eachResource.getExpired())) {
//                    throw new RDSExceptions.PrepayInstanceDeleteException();
//                }
//            }
            List<Instance> deleteResourceGroups = new ArrayList<>();

            // 批量释放的情况下 加个多线程用来并发访问实例详情接口
            Map<String , Future<Instance>> instanceDetails = new HashMap<>();
            for (final String ids : instanceIds) {
                // 此处需根据当前实例是否为金融版实例 进而决定创建哪个接口
                final RDSClient2 rdsClient2 = clientFactory.createRdsClient2ByInstanceId(ids);

                Future<Instance> instances = ThreadPool.submit(new Callable<Instance>() {
                    @Override
                    public Instance call() {
                        return rdsClient2.instanceDescribe(ids).getInstance();
                    }
                });
                instanceDetails.put(ids, instances);

            }

            // 关闭线程池 任务执行结束后释放线程资源
            // ThreadPool.shutdown();

            for (int i = 0; i < instanceIds.length; i++) {
//                Instance instance = clientFactory.createRdsClient2ByInstanceId(instanceIds[i])
//                        .instanceDescribe(instanceIds[i]).getInstance();
                Future<Instance> instanceFuture = instanceDetails.get(instanceIds[i]);
                Instance instance = null;
                try {
                    instance = instanceFuture.get();
                } catch (ExecutionException | InterruptedException e) {
                    logger.error("instanceFuture.get() occur error.");
                }
                if ("DCC".equalsIgnoreCase(instance.getMachineType())) {
                    delInstanceIds.add(instanceIds[i]);
                    continue;
                }
                Resource resource = resourceMap.get(instanceIds[i]);
                if (null != resource &&
                        (isMasterInstance(instance.getInstanceType()) || isRaftInstance(instance.getInstanceType()))) {
                    for (Order order : orders) {
                        if (order.getResourceIds() != null && StringUtils.isNotEmpty(resource.getUuid())) {
                            if (order.getResourceIds().contains(resource.getUuid())) {
                                delFailInstanceIds.add(instanceIds[i]);
                                delFailInstanceShortIds.add(shortInstanceId[i]);
                                logger.warn("There's an instance will failed to destroy, instanceId: {}",
                                        instanceIds[i]);
                            }
                        }
                    }
                }
                if (null != resource &&
                        (isMasterInstance(instance.getInstanceType()) || isRaftInstance(instance.getInstanceType()))) {
                    Iterator<Order> iterator = orders.iterator();
                    while (iterator.hasNext()) {
                        Order order = iterator.next();
                        if (order.getResourceIds() != null && StringUtils.isNotEmpty(resource.getUuid())) {
                            if (!order.getResourceIds().contains(resource.getUuid())) {
                                // 此时若主实例可以释放 只读代理无论任何情况下均应该可以释放
                                if (CollectionUtils.isNotEmpty(instance.getTopology().getReadReplica())) {
                                    // 若包含只读，则取消其所有阻碍释放的订单
                                    for (String replicaId : instance.getTopology().getReadReplica()) {
                                        Resource res = resourceMap.get(replicaId);
                                        while (iterator.hasNext()) {
                                            Order orderRe = iterator.next();
                                            logger.info("orderRe.getResourceIds() is {}, res.getUuid() is {}, orderRe.getUuid() is {}",
                                                    orderRe.getResourceIds(), res.getUuid(), orderRe.getUuid());
                                            if (CollectionUtils.isNotEmpty(orderRe.getResourceIds()) &&
                                                    StringUtils.isNotEmpty(res.getUuid()) &&
                                                    StringUtils.isNotEmpty(orderRe.getUuid()) &&
                                                    blockedOrderStatus.contains(orderRe.getStatus()) &&
                                                    orderRe.getResourceIds().contains(res.getUuid())) {
                                                UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
                                                OrderStatus orderStatus = OrderStatus.CREATE_FAILED;
                                                updateOrderRequest.setStatus(orderStatus);
                                                updateOrderRequest.setServiceType(order.getServiceType());
                                                orderClient.update(orderRe.getUuid(), updateOrderRequest);
//                                                CancelOrderRequest orderRequest = new CancelOrderRequest();
//                                                orderRequest.setOrderIds(new ArrayList<String>
//                                                        (Collections.singleton(orderRe.getUuid())));
//                                                orderClient2.cancel(orderRequest);
                                                iterator.remove();
                                            }
                                        }

                                    }
                                }

                                if (CollectionUtils.isNotEmpty(instance.getTopology().getRdsproxy())) {
                                    // 若包含只读，则取消其所有阻碍释放的订单
                                    for (String proxyId : instance.getTopology().getRdsproxy()) {
                                        Resource res = resourceMap.get(proxyId);
                                        while (iterator.hasNext()) {
                                            Order orderProxy = iterator.next();
                                            if (CollectionUtils.isNotEmpty(orderProxy.getResourceIds()) &&
                                                    StringUtils.isNotEmpty(res.getUuid()) &&
                                                    StringUtils.isNotEmpty(orderProxy.getUuid()) &&
                                                    blockedOrderStatus.contains(orderProxy.getStatus())
                                                    && orderProxy.getResourceIds().contains(res.getUuid())) {
                                                UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
                                                OrderStatus orderStatus = OrderStatus.CREATE_FAILED;
                                                updateOrderRequest.setStatus(orderStatus);
                                                updateOrderRequest.setServiceType(order.getServiceType());
                                                orderClient.update(orderProxy.getUuid(), updateOrderRequest);
//                                                CancelOrderRequest orderRequest = new CancelOrderRequest();
//                                                orderRequest.setOrderIds(new ArrayList<String>(Collections.singleton(orderProxy.getUuid())));
//                                                orderClient2.cancel(orderRequest);
                                                iterator.remove();
                                            }
                                        }
                                    }
                                }


                            }
                        }
                    }
                }

                try {
                    // 1.释放主实例，需判断是否存在只读、代理
                    if (isMasterInstance(instance.getInstanceType())) {

                        // 释放主实例
                        if (!delFailInstanceIds.contains(instanceIds[i])) {

                            logger.info("release master");
                            RDSClient client = clientFactory.createRdsClient();
                            Instance master = new Instance();
                            master.setInstanceId(instanceIds[i]);
                            master.setInstanceType(instance.getInstanceType());
                            // 先判断实例是否是mysql,只有mysql可以走释放逻辑，其余的需执行删除操作。
//                            Resource res = resourceMap.get(instance.getInstanceId());
//                            logger.info("instance engine is {} ", instance.getEngine());
//                            if (!("MySQL".equalsIgnoreCase(instance.getEngine()))) {
//                                if (res == null || (res != null && retryDestroyResource(resourceClient,
//                                        res, updateResourceRequest, instance.getInstanceId()) > 0)) {
//                                    delInstanceIds.add(instance.getInstanceId());
//                                    // 目前PG 支持只读实例，需在释放PG主实例时连同释放只读实例，此处兼容 sqlserver
//                                    // 如果当前 PG、sqlserver 主实例 包含只读
//                                    if (CollectionUtils.isNotEmpty(instance.getTopology().getReadReplica())) {
//                                        // 删除只读 新增预付费计费方式 需兼容
//                                        logger.info("destroy readReplica");
//                                        for (String replicaId : instance.getTopology().getReadReplica()) {
//                                            Resource resRep = resourceMap.get(replicaId);
//                                            // 1. 后付费类型只读实例或资源为空，数据库资源直接删除，计费资源直接销毁
//                                            if (resRep == null || (resRep != null && ProductPayType.POST_PAY.alias
//                                                    .equals(resRep.getProductType()) && retryDestroyResource(
//                                                    resourceClient, resRep, updateResourceRequest, replicaId) > 0)) {
//                                                delInstanceIds.add(replicaId);
//                                                Instance readReplica = new Instance();
//                                                readReplica.setInstanceId(replicaId);
//                                                readReplica.setInstanceType(RDSConstant.INSTANCE_TYPE_REPLICA);
//                                                deleteResourceGroups.add(readReplica);
//                                            } else if (resRep != null &&
//                                                    ProductPayType.PRE_PAY.alias.equals(resRep.getProductType())) {
//                                                // 2. 预付费类型只读实例，数据库资源直接删除，计费资源需要退款
//                                                if (orderClient == null) {
//                                                    orderClient = clientFactory.createOrderClient();
//                                                }
//                                                RefundRequest refundRequest = new RefundRequest();
//                                                refundRequest.setUuid(resRep.getUuid());
//                                                refundRequest.setSource("OSP");
//                                                refundRequest.setRefundReason("RDS 用户删除只读实例");
//                                                orderClient.refund(refundRequest);
//                                                delInstanceIds.add(replicaId);
//                                                Instance readReplica = new Instance();
//                                                readReplica.setInstanceId(replicaId);
//                                                readReplica.setInstanceType(RDSConstant.INSTANCE_TYPE_REPLICA);
//                                                deleteResourceGroups.add(readReplica);
//                                            }
//                                        }
//                                    }
//                                    // 包含代理
//                                    if (CollectionUtils.isNotEmpty(instance.getTopology().getRdsproxy())) {
//                                        logger.info("destroy rdsproxy");
//                                        // 删除代理
//                                        for (String proxyId : instance.getTopology().getRdsproxy()) {
//                                            Resource resProxy = resourceMap.get(proxyId);
//                                            if (resProxy == null || (resProxy != null && retryDestroyResource(
//                                                    resourceClient, resProxy, updateResourceRequest, proxyId) > 0)) {
//                                                delInstanceIds.add(proxyId);
//                                                Instance proxy = new Instance();
//                                                proxy.setInstanceId(proxyId);
//                                                proxy.setInstanceType(RDSConstant.INSTANCE_TYPE_PROXY);
//                                                deleteResourceGroups.add(proxy);
//                                            }
//                                        }
//                                    }
//                                    continue;
//                                }
//                            }

                            // 主实例进回收站
                            List<String> failedIds = new ArrayList<>();
                            ResourceClientV2 resourceClientV2 = null;
                            AtomicBoolean isPrePay = new AtomicBoolean(false);

                            if (resource != null) {
                                try {
                                    logger.info("resource {} prepare to enter recycler: {}",
                                            resource.getName(), resource.getProductType()); // prepay
                                    // 预付费退款
                                    if (ProductPayType.PRE_PAY.alias.equals(resource.getProductType())) {
                                        if (orderClient == null) {
                                            orderClient = clientFactory.createOrderClient();
                                        }
                                        RefundRequest refundRequest = new RefundRequest();
                                        refundRequest.setUuid(resource.getUuid());
                                        refundRequest.setSource("OSP");
                                        refundRequest.setRefundReason("RDS 用户释放主实例");
                                        orderClient.refund(refundRequest);
                                        isPrePay.set(true);
                                    } else {

                                        // 后付费手动停服
                                        if (resourceClientV2 == null) {
                                            resourceClientV2 = clientFactory.createResourceClientV2();
                                        }
                                        ResourceKey resourceKey = new ResourceKey();
                                        resourceKey.setServiceType(resource.getServiceType());
                                        resourceKey.setRegion(resource.getRegion());
                                        resourceKey.setResourceName(resource.getName());
                                        // 可能会失败
                                        ManualOpResponse opResponse = resourceClientV2.manualStop(resourceKey);
                                        if (!opResponse.isSuccess()) {
                                            throw new BceInternalResponseException(opResponse.getReason());
                                        }
                                    }
                                    // 释放实例前需要锁定资源
                                    lockInstance(master);
                                    try {
                                        // 释放后端实例资源
//                                        if (ProductPayType.PRE_PAY.alias.equals(resource.getProductType())) {
//                                            client.stopPrepaySingleInstance(master.getInstanceId());
//                                        } else {
//                                            client.stopSingleInstance(master.getInstanceId());
//                                        }
                                        client.stopSingleInstance(master.getInstanceId());
                                    } catch (BceInternalResponseException e) {
                                        logger.error("Delete master instance error, resource uuid:{}, showId:{}",
                                                resource.getUuid(), master.getInstanceId());
                                        failedIds.add(master.getInstanceId());
                                        delFailInstanceIds.add(master.getInstanceId());
                                        if ("InternalFailure".equals(e.getCode())) {
                                            throw new RDSExceptions.AvailableInstanceException();
                                        }
                                    }
                                } catch (BceInternalResponseException e) {
                                    logger.error("Delete resource error, resource uuid:{}, showId:{}",
                                            resource.getUuid(), master.getInstanceId());
                                    failedIds.add(master.getInstanceId());
                                    delFailInstanceIds.add(master.getInstanceId());
                                    // 删除资源出错的情况下不删除后端，否则会资源泄露
                                    if ("OrderExceptions.InvalidResourceStatusException".equals(e.getCode())) {
                                        throw new RDSBusinessExceptions.
                                                ResourceUnsatisfiedException(master.getInstanceId());
                                    } else {
                                        throw new RDSBusinessExceptions.
                                                ReleaseInstanceException(master.getInstanceId());
                                    }
                                }
                            }
                        }

                        // 包含只读
                        if (CollectionUtils.isNotEmpty(instance.getTopology().getReadReplica())) {
                            // 删除只读
                            logger.info("destroy readReplica");
                            for (String replicaId : instance.getTopology().getReadReplica()) {
                                Resource res = resourceMap.get(replicaId);
                                // 1. 后付费类型只读实例或资源为空，数据库资源直接删除，计费资源直接销毁
                                if (res == null || (res != null && ProductPayType.POST_PAY.alias
                                        .equals(res.getProductType()) && retryDestroyResource(
                                        resourceClient, res, updateResourceRequest, replicaId) > 0)) {
                                    delInstanceIds.add(replicaId);
                                    Instance readReplica = new Instance();
                                    readReplica.setInstanceId(replicaId);
                                    readReplica.setInstanceType(RDSConstant.INSTANCE_TYPE_REPLICA);
                                    deleteResourceGroups.add(readReplica);
                                } else if (res != null &&
                                        ProductPayType.PRE_PAY.alias.equals(res.getProductType())) {
                                    // 2. 预付费类型只读实例，数据库资源直接删除，计费资源需要退款
                                    if (orderClient == null) {
                                        orderClient = clientFactory.createOrderClient();
                                    }
                                    RefundRequest refundRequest = new RefundRequest();
                                    refundRequest.setUuid(res.getUuid());
                                    refundRequest.setSource("OSP");
                                    refundRequest.setRefundReason("RDS 用户删除只读实例");
                                    orderClient.refund(refundRequest);
                                    delInstanceIds.add(replicaId);
                                    Instance readReplica = new Instance();
                                    readReplica.setInstanceId(replicaId);
                                    readReplica.setInstanceType(RDSConstant.INSTANCE_TYPE_REPLICA);
                                    deleteResourceGroups.add(readReplica);
                                }

                            }
                        }
                        // 包含代理
                        if (CollectionUtils.isNotEmpty(instance.getTopology().getRdsproxy())) {
                            logger.info("destroy rdsproxy");
                            // 删除代理
                            for (String proxyId : instance.getTopology().getRdsproxy()) {
                                Resource res = resourceMap.get(proxyId);
                                if (res == null || (res != null && retryDestroyResource(
                                        resourceClient, res, updateResourceRequest, proxyId) > 0)) {
                                    delInstanceIds.add(proxyId);
                                    Instance proxy = new Instance();
                                    proxy.setInstanceId(proxyId);
                                    proxy.setInstanceType(RDSConstant.INSTANCE_TYPE_PROXY);
                                    deleteResourceGroups.add(proxy);
                                }
                            }
                        }

                    }
                    // 2.删除只读、代理实例
                    else {
                        if (RDSConstant.INSTANCE_TYPE_REPLICA.equals(instance.getInstanceType())) {
                            logger.info("just destroy readReplica");
                            // 删除只读
                            Resource res = resourceMap.get(instance.getInstanceId());
                            // 1. 后付费类型只读实例或资源为空，数据库资源直接删除，计费资源直接销毁
                            if (res == null || (res != null && ProductPayType.POST_PAY.alias
                                    .equals(res.getProductType()) && retryDestroyResource(
                                    resourceClient, res, updateResourceRequest, instance.getInstanceId()) > 0)) {
                                delInstanceIds.add(instance.getInstanceId());
                                Instance readReplica = new Instance();
                                readReplica.setInstanceId(instance.getInstanceId());
                                readReplica.setInstanceType(RDSConstant.INSTANCE_TYPE_REPLICA);
                                deleteResourceGroups.add(readReplica);
                            } else if (res != null &&
                                    ProductPayType.PRE_PAY.alias.equals(res.getProductType())) {
                                // 2. 预付费类型只读实例，数据库资源直接删除，计费资源需要退款
                                if (orderClient == null) {
                                    orderClient = clientFactory.createOrderClient();
                                }
                                RefundRequest refundRequest = new RefundRequest();
                                refundRequest.setUuid(resource.getUuid());
                                refundRequest.setSource("OSP");
                                refundRequest.setRefundReason("RDS 用户删除只读实例");
                                orderClient.refund(refundRequest);
                                delInstanceIds.add(instance.getInstanceId());
                                Instance readReplica = new Instance();
                                readReplica.setInstanceId(instance.getInstanceId());
                                readReplica.setInstanceType(RDSConstant.INSTANCE_TYPE_REPLICA);
                                deleteResourceGroups.add(readReplica);
                            }


                        }

                        else if (RDSConstant.INSTANCE_TYPE_PROXY.equals(instance.getInstanceType())) {
                            logger.info("just destroy rdsproxy");
                            // 删除代理
                            Resource res = resourceMap.get(instance.getInstanceId());
                            if (res == null || (res != null && retryDestroyResource(
                                    resourceClient, res, updateResourceRequest, instance.getInstanceId()) > 0)) {
                                delInstanceIds.add(instance.getInstanceId());
                                Instance proxy = new Instance();
                                proxy.setInstanceId(instance.getInstanceId());
                                proxy.setInstanceType(RDSConstant.INSTANCE_TYPE_PROXY);
                                deleteResourceGroups.add(proxy);
                            }

                        }
                    }


                } catch (Exception ex) {
                    // 只读或代理有一个删除失败，主实例就不能删除
                    delFailInstanceIds.add(instanceIds[i]);
                    delFailInstanceShortIds.add(shortInstanceId[i]);
                }
            }
            if (delInstanceIds != null && delInstanceIds.size() > 0) {
                HashSet set = new HashSet(delInstanceIds);
                delInstanceIds.clear();
                delInstanceIds.addAll(set);
            }
            changeResource(deleteResourceGroups, "delete", clientFactory.getAccountId());
            lockInstanceBatch(delInstanceIds);
            deleteInstanceBatch(delInstanceIds);
            logger.info("batch delete instance: {}. failed delete instance: {}.", delInstanceIds, delFailInstanceIds);

        } catch (BceInternalResponseException ex) {
            if ("OrderExceptions.ResourceInTaskException".equals(ex.getCode())) {
                throw new RDSExceptions.ResourceInTaskException();
            }
            throw ex;
        }

//        if (RDSConstant.FROM_API.equalsIgnoreCase(from) || shortInstanceId.length > 0) {
//            return delFailInstanceShortIds;
//        }
        return delFailInstanceIds;
    }




    protected Future<Map<String, Resource>> getResourceMap() {
        GetResourcesRequest resourcesRequest = new GetResourcesRequest();
        resourcesRequest.setAccountId(clientFactory.getUserId());
        resourcesRequest.setServiceType(ServiceType.RDS.toString());
        resourcesRequest.setRegion(HomeServiceRequestContext.getRegion());
        resourcesRequest.setLimit(1000);
        ResourceClient resourceClient = clientFactory.createResourceClient();
        return rdsAsyncService.resourceList(resourceClient, resourcesRequest, BceInternalRequest.getThreadRequestId());
    }

    private boolean containShortId(String[] instanceIds) {

        if (instanceIds == null || instanceIds.length == 0) {
            return false;
        }

        for (String eachInstanceId : instanceIds) {
            if (BasisUtils.isShortId(eachInstanceId)) {
                return true;
            }
        }

        return false;
    }

    public void deleteInstanceBatch(List<String> instanceIdList) {
        StringBuilder instanceIds = new StringBuilder();
        StringBuilder pgInstanceIds = new StringBuilder();
        StringBuilder sqlInstanceIds = new StringBuilder();
        for (String instanceId : instanceIdList) {
            instanceIds.append(instanceId).append(",");
            if (instanceId.startsWith("rdsg")) {
                pgInstanceIds.append(instanceId).append(",");
            } else {
                sqlInstanceIds.append(instanceId).append(",");
            }
        }
        if (instanceIds == null || instanceIds.length() == 0) {
            // throw new RDSExceptions.CheckInstanceStatusAndOrderStatus();
            return;
        }
        String ids = instanceIds.substring(0, instanceIds.length() - 1);
        RDSClient rdsClient = clientFactory.createRdsClient();
        if (clientFactory.getInstanceTypeByUuid(instanceIdList.get(0))
                .equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) { // raft版RDS，走新的接口
            rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2);
        }
        instanceDao.deleteInstanceByInstanceUuids(instanceIdList, clientFactory.getAccountId());
        // 批量删除的类型目前都是一样的，有一个为raft该批量全是raft
        if (StringUtils.isNotEmpty(pgInstanceIds.toString())) {
            String pgids = pgInstanceIds.substring(0, pgInstanceIds.length() - 1);
            rdsClient.instanceDeleteBatch(pgids);
        }
        if (StringUtils.isNotEmpty(sqlInstanceIds.toString())) {
            String sqlids = sqlInstanceIds.substring(0, sqlInstanceIds.length() - 1);
            rdsClient.instanceDeleteBatch(sqlids);
        }

    }

    /**
     * 新增重载方法 支持资源启停使用
     * @param instanceIdList
     * @param accountId
     */
    public void deleteInstanceBatch2Audit(List<String> instanceIdList, String accountId, RDSClient rdsClient) {
        StringBuilder instanceIds = new StringBuilder();
        StringBuilder pgInstanceIds = new StringBuilder();
        StringBuilder sqlInstanceIds = new StringBuilder();
        for (String instanceId : instanceIdList) {
            instanceIds.append(instanceId).append(",");
            if (instanceId.startsWith("rdsg")) {
                pgInstanceIds.append(instanceId).append(",");
            } else {
                sqlInstanceIds.append(instanceId).append(",");
            }
        }
        if (instanceIds == null || instanceIds.length() == 0) {
            // throw new RDSExceptions.CheckInstanceStatusAndOrderStatus();
            return;
        }
        String ids = instanceIds.substring(0, instanceIds.length() - 1);
        if (clientFactory.getInstanceTypeByUuid(instanceIdList.get(0))
                .equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) { // raft版RDS，走新的接口
            rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2);
        }
        instanceDao.deleteInstanceByInstanceUuids(instanceIdList, accountId);
        // 批量删除的类型目前都是一样的，有一个为raft该批量全是raft
        if (StringUtils.isNotEmpty(pgInstanceIds.toString())) {
            String pgids = pgInstanceIds.substring(0, pgInstanceIds.length() - 1);
            rdsClient.instanceDeleteBatch(pgids);
        }
        if (StringUtils.isNotEmpty(sqlInstanceIds.toString())) {
            String sqlids = sqlInstanceIds.substring(0, sqlInstanceIds.length() - 1);
            rdsClient.instanceDeleteBatch(sqlids);
        }

    }

    public void lockInstanceBatch(List<String> instanceIdList) {
        StringBuilder instanceIds = new StringBuilder();
        for (String instanceId : instanceIdList) {
            instanceIds.append(instanceId).append(",");
        }
        if (instanceIds == null || instanceIds.length() == 0) {
            return;
        }
        String ids = instanceIds.substring(0, instanceIds.length() - 1);
        // 批量锁定的类型目前都是一样的，有一个为raft该批全是raft
        clientFactory.createRdsClient2ByInstanceId(instanceIdList.get(0))
                .instanceLockBatch(ids, "lockExpiration");

    }

    /**
     * 锁状态接口支持资源启停
     * @param instanceIdList
     * @param accountId
     */
    public void lockInstanceBatch2Audit(List<String> instanceIdList, String accountId, RDSClient2 client) {
        StringBuilder instanceIds = new StringBuilder();
        for (String instanceId : instanceIdList) {
            instanceIds.append(instanceId).append(",");
        }
        if (instanceIds == null || instanceIds.length() == 0) {
            return;
        }
        String ids = instanceIds.substring(0, instanceIds.length() - 1);
        // 批量锁定的类型目前都是一样的，有一个为raft该批全是raft
        client.instanceLockBatch(ids, "lockExpiration");

    }

    private int retryDestroyResource(ResourceClient resourceClient, Resource res,
                                     UpdateResourceRequest updateResourceRequest,
                                     String instanceId) throws BceInternalResponseException {
        // 若为预付费资源 直接退款即可
        if (StringUtils.isNotEmpty(res.getProductType())
                && ProductPayType.PRE_PAY.alias.equals(res.getProductType())) {
            OrderClient orderClient = clientFactory.createOrderClient();
            RefundRequest refundRequest = new RefundRequest();
            refundRequest.setUuid(res.getUuid());
            refundRequest.setSource("OSP");
            refundRequest.setRefundReason("RDS 用户释放实例");
            orderClient.refund(refundRequest);
            logger.info("refund resource success, resourceUuid: {}, instanceId: {}", res.getUuid(), instanceId);
            return 1;
        }
        BceInternalResponseException e = new BceInternalResponseException("");
        int i = 3;
        while (i > 0) {
            try {
                resourceClient.update(res.getUuid(), updateResourceRequest);
                logger.info("destroy resource success, resourceUuid: {}, instanceId: {}", res.getUuid(), instanceId);
                return 1;
            } catch (BceInternalResponseException ex) {
                e = ex;
                i--;
                continue;
            }
        }
        logger.warn("destroy resource failed, resourceUuid: {}, instanceId: {}", res.getUuid(), instanceId);
        throw e;
    }

    private HashMap<String, Resource> getResourceMapKeyByInstanceId() {
        HashMap<String, Resource> resourceMap = new HashMap<>();
        for (Resource tmp : getResourceList()) {
            resourceMap.put(tmp.getName(), tmp);
        }
        return resourceMap;
    }

    public List<Order> getBusyOrders() {
        AdvancedOrderFilter orderFilter = new AdvancedOrderFilter();
        List<OrderStatus> statuses = new ArrayList<>();
        statuses.add(OrderStatus.NEED_PURCHASE);
        statuses.add(OrderStatus.NEED_CONFIRM);
        statuses.add(OrderStatus.READY_FOR_CREATE);
        statuses.add(OrderStatus.CREATING);
        statuses.add(OrderStatus.DEFERRED_CREATE);
        orderFilter.setAccountId(clientFactory.getAccountId());
        orderFilter.setStatusList(statuses);
        orderFilter.setServiceType("RDS");
        List<Order> orders = (List) clientFactory.getOrderClient().advancedList(orderFilter).getOrders();

        AdvancedOrderFilter orderFilterReplica = new AdvancedOrderFilter();
        orderFilterReplica.setAccountId(clientFactory.getAccountId());
        orderFilterReplica.setStatusList(statuses);
        orderFilterReplica.setServiceType("RDS_REPLICA");
        List<Order> ordersReplica = (List) clientFactory.getOrderClient().advancedList(orderFilterReplica).getOrders();
        orders.addAll(ordersReplica);
        return orders;
    }

    public HashMap<String, Instance> getInstanceMap() {
        HashMap<String, Instance> instanceMap = new HashMap<>();
        List<Instance> instances = (ArrayList) clientFactory.createRdsClient2().instanceList().getInstances();
        List<Instance> instancesOnDcc = (ArrayList) clientFactory.createRdsClient().instanceList("dcc").getInstances();

        for (Instance instance : instances) {
            instanceMap.put(instance.getInstanceId(), instance);
        }
        for (Instance instance : instancesOnDcc) {
            instanceMap.put(instance.getInstanceId(), instance);
        }
        // 上海区需要去查询raft版的实例
        if (ArrayUtils.contains(financialRegion, regionConfiguration.getCurrentRegion())) {
            List<Instance> instanceRaft = (ArrayList) clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2)
                    .instanceList(null).getInstances();
            if (instanceRaft != null) {
                for (Instance instance : instanceRaft) {
                    instanceMap.put(instance.getInstanceId(), instance);
                }
            }
        }

        return instanceMap;
    }

    public void delete(Instance instance) {
        try {
            if (!"available".equalsIgnoreCase(instance.getInstanceStatus())) {
                throw new RDSExceptions.AvailableInstanceException();
            }
            destroyResource(instance);
            lockInstance(instance);
            deleteInstance(instance);
        } catch (BceInternalResponseException ex) {
            if ("OrderExceptions.ResourceInTaskException".equals(ex.getCode())) {
                throw new RDSExceptions.ResourceInTaskException();
            }
            throw ex;
        }
    }

    private void deleteInstance(Instance instance) {
        if (CollectionUtils.isNotEmpty(instance.getReadReplica())) {
            for (String replicaId : instance.getReadReplica()) {
                clientFactory.createRdsClient().instanceDelete(replicaId);
            }
        }

        if (isMasterInstance(instance.getInstanceType())
                && CollectionUtils.isNotEmpty(instance.getTopology().getRdsproxy())) {
            for (String proxyId : instance.getTopology().getRdsproxy()) {
                clientFactory.createRdsClient().instanceDelete(proxyId);
            }
        }

        clientFactory.createRdsClient().instanceDelete(instance.getInstanceId());
    }

    public void lockInstance(Instance instance) {
        if (CollectionUtils.isNotEmpty(instance.getTopology().getReadReplica())) {
            for (String replicaId : instance.getReadReplica()) {
                clientFactory.createRdsClient().instanceLock(replicaId, "lockExpiration");
            }
        }

        if (isMasterInstance(instance.getInstanceType())
                && CollectionUtils.isEmpty(instance.getTopology().getRdsproxy())) {
            for (String proxyId : instance.getTopology().getRdsproxy()) {
                clientFactory.createRdsClient().instanceLock(proxyId, "lockExpiration");
            }
        }

        clientFactory.createRdsClient().instanceLock(instance.getInstanceId(), "lockExpiration");
    }

    public void lockInstance4Audit(Instance instance, String accountId, RDSClient client) {
        if (CollectionUtils.isNotEmpty(instance.getTopology().getReadReplica())) {
            for (String replicaId : instance.getReadReplica()) {
                client.instanceLock(replicaId, "lockExpiration");
            }
        }

        if (isMasterInstance(instance.getInstanceType())
                && CollectionUtils.isEmpty(instance.getTopology().getRdsproxy())) {
            for (String proxyId : instance.getTopology().getRdsproxy()) {
                client.instanceLock(proxyId, "lockExpiration");
            }
        }

        client.instanceLock(instance.getInstanceId(), "lockExpiration");
    }

    private void destroyResource(Instance instance) {
        Resource resource = getResourceByInstanceId(instance.getInstanceId());
        if (null == resource) {
            throw new RDSExceptions.ResourceNotExistException();
        }

        ResourceClient resourceClient = clientFactory.createResourceClient();

        UpdateResourceRequest updateResourceRequest = new UpdateResourceRequest();
        updateResourceRequest.setStatus(ResourceStatus.DESTROYED);

        if (isMasterInstance(instance.getInstanceType())) {
            AdvancedOrderFilter orderFilter = new AdvancedOrderFilter();
            List<OrderStatus> statuses = new ArrayList<>();
            statuses.add(OrderStatus.NEED_PURCHASE);
            statuses.add(OrderStatus.NEED_CONFIRM);
            statuses.add(OrderStatus.READY_FOR_CREATE);
            statuses.add(OrderStatus.CREATING);
            orderFilter.setAccountId(clientFactory.getAccountId());
            orderFilter.setStatusList(statuses);
            orderFilter.setServiceType("RDS");
            Collection<Order> orders = clientFactory.getOrderClient().advancedList(orderFilter).getOrders();
            for (Order order : orders) {
                if (order.getResourceIds() != null && StringUtils.isNotEmpty(resource.getUuid())) {
                    if (order.getResourceIds().contains(resource.getUuid())) {
                        throw new RDSExceptions.ResourceInTaskException();
                    }
                }
            }
        }

        if (isMasterInstance(instance.getInstanceType()) && CollectionUtils.isNotEmpty(instance.getReadReplica())) {
            // destroy replica resource
            for (String replicaId : instance.getReadReplica()) {
                Resources resources = resourceClient.list(
                        new GetResourcesRequest().withName(replicaId));
                if (!resources.isEmpty()) {
                    for (Resource tmp : resources) {
                        resourceClient.update(tmp.getUuid(), updateResourceRequest);
                    }
                }
            }
        }

        if (isMasterInstance(instance.getInstanceType())
                && CollectionUtils.isNotEmpty(instance.getTopology().getRdsproxy())) {
            // destroy proxy resource
            for (String proxyId : instance.getTopology().getRdsproxy()) {
                Resources resources = resourceClient.list(
                        new GetResourcesRequest().withName(proxyId));
                if (!resources.isEmpty()) {
                    for (Resource tmp : resources) {
                        resourceClient.update(tmp.getUuid(), updateResourceRequest);
                    }
                }
            }
        }

        resourceClient.update(resource.getUuid(), updateResourceRequest);
    }

    private boolean isMasterInstance(String instanceType) {
        return "master".equalsIgnoreCase(instanceType);
    }

    private boolean isRaftInstance(String instanceType) {
        return RDSConstant.INSTANCE_TYPE_RAFT.equalsIgnoreCase(instanceType);
    }

    /**
     * 创建实例，处理边缘计算区域特殊逻辑：
     *  1. 汇总购买实例个数；
     *  2. 将边缘计算 vpcId, subnetId 由短 ID 转换为长 ID；
     *
     * @param instanceCreateModel instanceCreateModel
     */
    private void handleForEdge(InstanceCreateModel instanceCreateModel) {
        if (instanceCreateModel.getInstance().getEdgeRegion() != null
                && !instanceCreateModel.getInstance().getEdgeRegion().isEmpty()) {
            int number = 0;
            for (Map.Entry<String, Integer> entry : instanceCreateModel.getInstance().getEdgeRegion().entrySet()) {
                number += entry.getValue();
            }
            instanceCreateModel.setNumber(number);
        }
        if (instanceCreateModel.getInstance().getEdgeVpcId() != null) {
            List<String> edgeVpcIds = new ArrayList<>();
            for (Map.Entry<String, String> entry : instanceCreateModel.getInstance().getEdgeVpcId().entrySet()) {
                String edgeRegionId = entry.getKey();
                String edgeVpcId = entry.getValue();
                edgeVpcIds.add(edgeVpcId);
            }
            Map<String, EdgeVpc> vpcIdMap = edgeService.getVpcIdMap(edgeVpcIds);
            for (Map.Entry<String, String> entry : instanceCreateModel.getInstance().getEdgeVpcId().entrySet()) {
                String edgeVpcId = entry.getValue();
                entry.setValue(vpcIdMap.get(edgeVpcId).getVpcUuid());
            }

            if (instanceCreateModel.getInstance().getEdgeSubnetId() != null) {
                Map<String, EdgeSubnet> subnetIdMap = new HashMap<>();
                for (EdgeVpc vpc : vpcIdMap.values()) {
                    if (vpc.getSubnets() == null) {
                        continue;
                    }
                    for (EdgeSubnet subnet : vpc.getSubnets()) {
                        subnetIdMap.put(subnet.getSubnetId(), subnet);
                    }
                }

                for (Map.Entry<String, String> entry : instanceCreateModel.getInstance().getEdgeSubnetId().entrySet()) {
                    String edgeRegionId = entry.getKey();
                    String edgeSubnetId = entry.getValue();
                    entry.setValue(subnetIdMap.get(edgeSubnetId).getSubnetUuid());
                }
            }
        }

    }

    public LogicInstanceCreateResponse createInstances(
            BaseCreateOrderRequestVo<InstanceCreateModel> request, String from) throws Exception {
        InstanceCreateModel instanceCreateModel = request.getItems().get(0).getConfig();
        handleForEdge(instanceCreateModel);
        // 参数补全、参数合法性检查
        checkAndSupplyInstanceCreateModel(instanceCreateModel, from);
        // 生成短ID，并赋值到request的InstanceCreateModel中
        List<String> instanceShortIds = generateInstanceShortIds(
                instanceCreateModel.getInstance().getInstanceType(),instanceCreateModel.getNumber());
        instanceCreateModel.setInstanceShortIds(instanceShortIds);
        // 创建订单
        OrderUuidResult orderUuidResult = rdsOrderService.createNewOrder(request, from);
        // 保存数据到表：t_rds_instance、t_rds_subnet、t_rds_ordersync
        List<String> instanceIds = saveInstance2Db(instanceCreateModel, orderUuidResult.getOrderId(), from);
        LogicInstanceCreateResponse response = new LogicInstanceCreateResponse();
        response.setOrderId(orderUuidResult.getOrderId());
        response.setInstanceIds(instanceIds);
        return response;
    }

    public LogicInstanceCreateResponse createInstancesOnDcc(
            BaseCreateOrderRequestVo<InstanceCreateModel> request, String from) throws Exception {
        InstanceCreateModel config = request.getItems().get(0).getConfig();
        // 参数补全、参数合法性检查
        checkAndSupplyInstanceCreateModel(config, from);
        InstanceCreateRequest instanceCreateRequest
                = config.convertToOrderItemExtraInfo(othersService.zoneMap()).getInstanceCreateRequest();
        List<Instance> instances
                = (List)clientFactory.createRdsClient().instanceCreate(instanceCreateRequest).getInstances();
        List<String> instanceIds = saveInstanceOnDcc2Db(config, instances.get(0).getInstanceId(), from);

        LogicInstanceCreateResponse response = new LogicInstanceCreateResponse();
        response.setOrderId("");
        response.setInstanceIds(instanceIds);

        return response;
    }

    private List<String> generateInstanceShortIds(String instanceType, int number) {
        List<String> shortIds = new ArrayList<>();
        for (int i = 0; i < number; i++) {
            shortIds.add(idGenerator.createExternalId(RDSConstant.API_RDS_PREFIX));
        }
        return shortIds;
    }

    public void checkAndSupplyInstanceCreateModel(InstanceCreateModel instanceCreateModel, String from)
            throws Exception {

        // 通用型实例只允许 MySQL双机本地盘
        if (StringUtils.isNotEmpty(instanceCreateModel.getInstance().getResourceType()) &&
                RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instanceCreateModel.getInstance().getResourceType())) {
//            // 当前通用型实例仅支持部分地域
//            String currentRegion = regionConfiguration.getCurrentRegion();
//            if (StringUtils.isNotEmpty(currentRegion) &&
//                    !RDSConstant.GENERAL_INSTANCE_SUPPORT_REGION.contains(currentRegion)) {
//                throw new RDSBusinessExceptions.UnsupportRegionException(currentRegion);
//            }
//            if ((StringUtils.isNotEmpty(instanceCreateModel.getInstance().getDiskIoType())
//                && !RDSConstant.DISK_OF_NORMAL_IO.equalsIgnoreCase(instanceCreateModel.getInstance().getDiskIoType()))
//                || (StringUtils.isNotEmpty(instanceCreateModel.getInstance().getCategory()) &&
//                    RDSConstant.CATEGORY_SINGLETON.equalsIgnoreCase(instanceCreateModel.getInstance().getCategory()) &&
//                    StringUtils.isNotEmpty(instanceCreateModel.getInstance().getEngine()) &&
//                    RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instanceCreateModel.getInstance().getEngine()))){
//                throw new RDSBusinessExceptions.GeneralInstanceNotSupportException();
//            }
            if (StringUtils.isNotEmpty(instanceCreateModel.getInstance().getEngine()) &&
                    (RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(instanceCreateModel.getInstance().getEngine()) ||
                RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(instanceCreateModel.getInstance().getEngine()))) {
                throw new RDSBusinessExceptions.GeneralInstanceNotSupportException();
            }

//            // 只读、代理 单双机均可 支持本地盘
//            if (StringUtils.isNotEmpty(instanceCreateModel.getInstance().getSourceInstanceId()) &&
//                StringUtils.isNotEmpty(instanceCreateModel.getInstance().getDiskIoType()) &&
//                !RDSConstant.DISK_OF_NORMAL_IO.equalsIgnoreCase(instanceCreateModel.getInstance().getDiskIoType())) {
//                throw new RDSBusinessExceptions.GeneralInstanceNotSupportException();
//            }

        }



        if (RDSConstant.FROM_API.equalsIgnoreCase(from)
                && StringUtils.isNotBlank(instanceCreateModel.getInstance().getSourceInstanceId())) {
            // 拿到主实例详情
            String sourceInstanceId = instanceCreateModel.getInstance().getSourceInstanceId();
            String instanceUuid = idMapperService.getInstanceUuid(sourceInstanceId);
            InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(instanceUuid)
                    .instanceDescribe(instanceUuid);
            InstanceExtension sourceDetail = new InstanceExtension(instanceGetResponse.getInstance());
            // 避免对代理实例进行此项校验
            if (instanceCreateModel.getInstance().getNodeAmount() == null) {
                // 新建的只读实例磁盘大小不能小于主实例磁盘大小
                if (sourceDetail.getAllocatedStorageInGB() > instanceCreateModel.getInstance().getAllocatedStorageInGB()) {
                    throw new RDSBusinessExceptions.ResizeMasterReplicaDiskException();
                }
            }
            // 限制下 主实例类型且非 sqlserver
            if (!RDSConstant.INSTANCE_TYPE_MASTER.equals(sourceDetail.getInstanceType())
                    || RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(sourceDetail.getEngine())) {
                throw new RDSBusinessExceptions.InstanceShrinkageEngineException();
            }

//            // 若主实例为通用型实例，则不允许建立只读实例与代理实例
//            if (StringUtils.isNotEmpty(sourceDetail.getResourceType())
//                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(sourceDetail.getResourceType())) {
//                throw new RDSBusinessExceptions.GeneralInstanceNotSupportException();
//            }
        }

        if (!RDSConstant.FROM_API.equalsIgnoreCase(from)
                && StringUtils.isBlank(instanceCreateModel.getAutoRenewTimeUnit())) {
            instanceCreateModel.setAutoRenewTimeUnit("month");
        }
        if (ObjectUtils.equals("cloud_nor", instanceCreateModel.getInstance().
                getDiskIoType())) {
            instanceCreateModel.getInstance().setDiskType("cds");
            instanceCreateModel.getInstance().setCdsType("cloud_hp1");
            // 高性能云磁盘
        } else if (ObjectUtils.equals("cloud_high", instanceCreateModel.getInstance().getDiskIoType())) {
            instanceCreateModel.getInstance().setDiskType("cds");
            instanceCreateModel.getInstance().setCdsType("hp1");
        } else if (ObjectUtils.equals("cloud_enha", instanceCreateModel.getInstance().getDiskIoType())){
            instanceCreateModel.getInstance().setDiskType("cds");
            instanceCreateModel.getInstance().setCdsType("enhanced_ssd_pl1");
        } else if (ObjectUtils.equals("normal_io", instanceCreateModel.getInstance().getDiskIoType())){
            instanceCreateModel.getInstance().setDiskType("ssd");
            instanceCreateModel.getInstance().setCdsType("");
        }

        if (instanceCreateModel.getInstance().getDiskType() != null
                && instanceCreateModel.getInstance().getDiskType()
                .equalsIgnoreCase("cds")
                && instanceCreateModel.getInstance().getCdsType()
                .equalsIgnoreCase("enhanced_ssd_pl1")){
            if (instanceCreateModel.getInstance().getAllocatedStorageInGB() < 50){
                throw new RDSExceptions.ParamValidationException("volumecapacity min 50.");
            }

            // 暂时去掉单机版云盘的限制
//            if (RDSConstant.CATEGORY_SINGLETON.equalsIgnoreCase(
//                            instanceCreateModel.getInstance().getCategory()) &&
//                    RDSConstant.RDS_CDS_ENGINE_VERSIONS.contains(
//                            instanceCreateModel.getInstance().getEngineVersion())){
//                throw new RDSExceptions.ParamValidationException("ESSD not supported.");
//            }
        }

        InstanceCreateModel.DashCreateInstance instance = instanceCreateModel.getInstance();

        // 只读实例支持本地盘ssd和增强型ESSD cloud_enha
        if (InstanceType.INSTANCE_TYPE_REPLICA.getValue().equalsIgnoreCase(instance.getInstanceType())) {
            if (ObjectUtils.equals("cloud_enha", instanceCreateModel.getInstance().getDiskIoType())){
                // 只读实例 mysql55、56不支持云盘
                if (RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instance.getEngine()) &&
                    RDSConstant.RDS_SSD_ENGINE_MYSQL_VERSIONS.contains(instance.getEngineVersion())) {
                    throw new RDSExceptions.ParamValidationException("MySql version is not match!");
                }


                instanceCreateModel.getInstance().setDiskType("cds");
                instanceCreateModel.getInstance().setCdsType("enhanced_ssd_pl1");
            } else if (ObjectUtils.equals("normal_io", instanceCreateModel.getInstance().getDiskIoType())){
                instanceCreateModel.getInstance().setDiskType("ssd");
                instanceCreateModel.getInstance().setCdsType("");
            } else if (instanceCreateModel.getInstance().getDiskIoType().isEmpty()) {
                instance.setDiskIoType("normal_io");
                instance.setDiskType("ssd");
                instance.setCdsType("");
            }
            else {
                throw new RDSExceptions.ParamValidationException("diskIoType must be normal_io or cloud_enha");
            }
        }

        // 主实例过滤不同数据库对应的磁盘类型
        // 主实例
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            if (!InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getEngine())
                    && !InstanceType.INSTANCE_TYPE_REPLICA.getValue().equalsIgnoreCase(instance.getEngine())
                    && StringUtils.isBlank(instance.getSourceInstanceId())) {
                // MYSQL
                if (RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instance.getEngine())){
                    // 单机
                    if (RDSConstant.CATEGORY_SINGLETON.equalsIgnoreCase(instance.getCategory())
                            || RDSConstant.CATEGORY_BASIC.equalsIgnoreCase(instance.getCategory())) {
                        if (!ObjectUtils.equals("cloud_high", instance.getDiskIoType())) {
                            throw new RDSExceptions.ParamValidationException("diskIoType must be cloud_high" );
                        }
                        // 双机
                    } else if ((RDSConstant.CATEGORY_STANDARD.equalsIgnoreCase(instance.getCategory())
                            || StringUtils.isBlank(instance.getCategory()))
                            && RDSConstant.RDS_SSD_ENGINE_MYSQL_VERSIONS.contains(instance.getEngineVersion())) {
                        if (!ObjectUtils.equals("normal_io", instance.getDiskIoType())) {
                            throw new RDSExceptions.ParamValidationException("diskIoType must be normal_io" );
                        }
                    } else if ((RDSConstant.CATEGORY_STANDARD.equalsIgnoreCase(instance.getCategory())
                            || StringUtils.isBlank(instance.getCategory()))
                            && RDSConstant.RDS_CDS_ENGINE_MYSQL_VERSIONS.contains(instance.getEngineVersion())) {
                        if (!ObjectUtils.equals("normal_io", instance.getDiskIoType())
                                && !ObjectUtils.equals("cloud_enha", instance.getDiskIoType())) {
                            throw new RDSExceptions.ParamValidationException(
                                    "diskIoType must be normal_io or cloud_enha");
                        }
                    }
                    // sqlserver
                } else if (RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(instance.getEngine())) {
                    // 单机
                    if (RDSConstant.CATEGORY_SINGLETON.equalsIgnoreCase(instance.getCategory())
                            || RDSConstant.CATEGORY_BASIC.equalsIgnoreCase(instance.getCategory())){
                        if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012SP3.equals(instance.getEngineVersion())) {
                            instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012);
                            instance.setCategory("");
                        } else if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016
                                .equals(instance.getEngineVersion())) {
                            instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2016);
                            instance.setCategory("");
                            instance.setIsSingle(true);
                        } else if (RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2017
                                .equals(instance.getEngineVersion())) {
                            instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2017);
                            instance.setCategory("");
                            instance.setIsSingle(true);
                        } else {
                            throw new RDSExceptions.ParamValidationException();
                        }
                    }
                    if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012.equals(instance.getEngineVersion())) {
                        if (!ObjectUtils.equals("cloud_enha", instance.getDiskIoType())) {
                            throw new RDSExceptions.ParamValidationException(
                                    "diskIoType must be cloud_enha" );
                        }
                    } else if (RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2016.equals(instance.getEngineVersion())
                            || RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2017.equals(instance.getEngineVersion())) {
                        if (!ObjectUtils.equals("cloud_nor", instance.getDiskIoType())
                                && !ObjectUtils.equals("cloud_high", instance.getDiskIoType())
                                && !ObjectUtils.equals("cloud_enha", instance.getDiskIoType())) {
                            throw new RDSExceptions.ParamValidationException(
                                    "diskIoType must be cloud_nor or cloud_high or cloud_enha");
                        }
                        // 双机
                    } else if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION.equals(instance.getEngineVersion())
                            || RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012SP3.equals(instance.getEngineVersion())
                            || RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016.equals(instance.getEngineVersion())
                            || RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2017.equals(instance.getEngineVersion())) {
                        if (!ObjectUtils.equals("cloud_enha", instance.getDiskIoType())) {
                            throw new RDSExceptions.ParamValidationException(
                                    "diskIoType must be cloud_enha" );
                        }
                    }
                    // sqlserver最小为2c
                    if (instance.getCpuCount() < 2) {
                        throw new RDSExceptions.ParamValidationException( "CPU min 2c" );
                    }
                } else if (RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(instance.getEngine())) {
                    if (RDSConstant.CATEGORY_SINGLETON.equalsIgnoreCase(instance.getCategory())
                            || RDSConstant.CATEGORY_BASIC.equalsIgnoreCase(instance.getCategory())) {
                        if (!ObjectUtils.equals("cloud_nor", instance.getDiskIoType())
                                && !ObjectUtils.equals("cloud_high", instance.getDiskIoType())) {
                            throw new RDSExceptions.ParamValidationException(
                                    "diskIoType must be cloud_nor or cloud_high");
                        }
                    } else if (RDSConstant.CATEGORY_STANDARD.equalsIgnoreCase(instance.getCategory())
                            || StringUtils.isBlank(instance.getCategory())) {
                        if (!ObjectUtils.equals("normal_io", instance.getDiskIoType())
                                && !ObjectUtils.equals("cloud_enha", instance.getDiskIoType())) {
                            throw new RDSExceptions.ParamValidationException(
                                    "diskIoType must be normal_io or cloud_enha" );
                        }
                    }
                }
            }
        }

        // 针对来自控制台的新购请求，需保证单机版磁盘类型必须为 cloud_high
        if (RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instance.getEngine()) &&
                (RDSConstant.CATEGORY_SINGLETON.equalsIgnoreCase(instance.getCategory())
                || RDSConstant.CATEGORY_BASIC.equalsIgnoreCase(instance.getCategory()))) {
            if (!RDSConstant.MYSQL_SINGLE_CDS_AVAILABLE_TYPE.contains(instance.getDiskIoType())) {
                logger.info("the diskIoType must be cloud_high while creating single instance.");
                instance.setDiskIoType("cloud_high");
                instance.setDiskType("cds");
                instance.setCdsType("hp1");
            }
        }

        // 字符集验证
        if (instance != null && StringUtils.isNotEmpty(instance.getCharacterSetName())
                && !RDSConstant.INSTANCE_CHARACTER_TYPE.contains(instance.getCharacterSetName().toLowerCase())) {
            throw new RDSExceptions.ParamValidationException("not a valid characterSetName");
        }

         // sqlserver 不允许克隆实例
//        if (instance.getInitialDataReference() != null && instance.getInitialDataReference().getInstanceId() != null){
//            if (instance.getEngine().equalsIgnoreCase("sqlserver")){
//                throw new RDSExceptions.InstanceTypeError();
//            }
//        }


        if (!RDSConstant.FROM_API.equalsIgnoreCase(from)) {

            // 如果没有设置系列，则手动设置（为了兼容旧代码）
            if (StringUtils.isBlank(instance.getCategory())) {
                instance.setCategory(RDSConstant.CATEGORY_STANDARD);
                if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012.equalsIgnoreCase(instance.getEngineVersion())
                        || RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2016
                        .equalsIgnoreCase(instance.getEngineVersion())
                        || (instance.getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER)
                        && (instance.getIsSingle() != null && instance.getIsSingle()))) {
                    instance.setCategory(RDSConstant.CATEGORY_BASIC);
                }
                if (instance.getIsEnhanced() != null && instance.getIsEnhanced()) { // 三节点增强版
                    instance.setCategory(RDSConstant.CATEGORY_ENHANCED);
                }
                if (StringUtils.isNotEmpty(instance.getInstanceType())
                        && instance.getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) { // raft金融版
                    instance.setCategory(RDSConstant.CATEGORY_FINANCIAL);
                }
                // Mysql单机版
                if (StringUtils.isNotEmpty(instance.getInstanceType())
                        && instance.getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RDS_SINGLE)) {
                    instance.setCategory(RDSConstant.CATEGORY_SINGLETON);
                }
            }
            return;
        }

        if (StringUtils.isEmpty(instance.getCategory())) {
            instance.setCategory(RDSConstant.CATEGORY_STANDARD);
        } else if(!RDS_CATEGORYS.contains(instance.getCategory())){
            throw new RDSExceptions.ParamValidationException("not a valid category");
        }
        // 单机版sqlserver2012校验
//        if (RDSConstant.CATEGORY_BASIC.equals(instance.getCategory())) {
//            if (!RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012SP3.equals(instance.getEngineVersion())
//                    || !RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(instance.getEngine())) {
//                throw new RDSExceptions.ParamValidationException("only SqlServer 2012sp3 support Basic category");
//            } else {
//                instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012);
//            }
            // sqlServer单机版校验（2012、2016）
//            if ((!RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012SP3.equals(instance.getEngineVersion())
//                    || !RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(instance.getEngine()))
//                    && !RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016
//                    .equalsIgnoreCase(instance.getEngineVersion())) {
//                throw new RDSExceptions.ParamValidationException("only SqlServer 2012sp3 " +
//                        "and SqlServer 2016 support Basic category");
//            } else if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012SP3.equals(instance.getEngineVersion())
//                    && RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(instance.getEngine())) {
//                instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012);
//            }

//        }

        if (InstanceType.INSTANCE_TYPE_REPLICA.getValue().equalsIgnoreCase(instance.getEngine())
                || InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getEngine())) {
            if (StringUtils.isNotEmpty(instance.getSourceInstanceId())) {
                // 此处改用管控接口返回的实例状态，TODO
                // 短 ID 转长 ID
                String sourceInsUuid = idMapperService.getInstanceUuid(instance.getSourceInstanceId());
                InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(sourceInsUuid)
                        .instanceDescribe(sourceInsUuid);
                InstanceExtension instancePO = new InstanceExtension(instanceGetResponse.getInstance());
//                InstancePO instancePO = instanceDao
//                        .queryInstanceByInstanceId(instance.getSourceInstanceId(), clientFactory.getAccountId());
                if (instancePO == null) {
                    throw new RDSExceptions.ResourceNotExistException();
                }
                if (!InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getEngine())) {
                    if (!RDSConstant.INSTANCE_STATUS_CREATE_REPLICA.contains(instancePO.getInstanceStatus())) {
                        throw new RDSExceptions.NotSupportOperation();
                    }
                    if (instanceDao
                            .countCreatingReplica(instance.getSourceInstanceId(), clientFactory.getAccountId()) > 0) {
                        throw new RDSExceptions.CreateReplicaInstanceException();
                    }
                    if (StringUtils.isNotEmpty(instancePO.getEngine())) { // 历史数据为空
                        instance.setEngine(instancePO.getEngine());
                    }
                    instance.setEngineVersion(instancePO.getEngineVersion());
                } else {
                    if (!RDSConstant.INSTANCE_STATUS_CREATE_PROXY.contains(instancePO.getInstanceStatus())) {
                        throw new RDSExceptions.NotSupportOperation();
                    }
//                    if (instanceDao
//                            .countProxy(instance.getSourceInstanceId(), clientFactory.getAccountId()) > 0) {
//                        throw new RDSExceptions.ExceedProxyQuotaException();
//                    }
                }
                // 此处设置为长 ID
                // String sourceInsUuid = instancePO.getInstanceUuid();
                instance.setSourceInstanceId(sourceInsUuid);

                if (StringUtils.isNotBlank(sourceInsUuid)){
                    InstanceGetResponse sourceInstance =
                            clientFactory.createRdsClient2ByInstanceId(sourceInsUuid).instanceDescribe(sourceInsUuid);
                    // 未设置专属BLB集群ID且主实例是BLB专属集群时,需要设置专属BLB集群ID
                    if (StringUtils.isBlank(instance.getBgwGroupId())) {
                        if (sourceInstance != null && sourceInstance.getInstance() != null) {
                            String bgwGroupId = sourceInstance.getInstance().getBgwGroupId();
                            if (StringUtils.isNotBlank(bgwGroupId)) {
                                instance.setBgwGroupId(bgwGroupId);
                                instance.setBgwGroupExclusive(true);
                            }
                        }
                    }
                }
            } else {
                throw new RDSExceptions.ParamValidationException("sourceInstanceId can not be null");
            }
        }

        if (!InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getEngine())
                && instance.getCpuCount() == 0) {
            throw new RDSExceptions.ParamValidationException("cpuCount can not be null");
        }
        // 同步模式校验
        if (StringUtils.isNotEmpty(instance.getReplicationType())) {
            if (!(RDSConstant.REPLICATION_TYPE_ASYNC.equalsIgnoreCase(instance.getReplicationType())
                    || RDSConstant.REPLICATION_TYPE_SEMISYNC.equalsIgnoreCase(instance.getReplicationType()))) {
                throw new RDSExceptions.ParamValidationException("replicationType is illegal");
            }
        }

        // zone vpc subnet校验&补充
        VpcVo defaultVpc = clientFactory.createVpcClient().getAndCreateDefaultVpc();
        if (StringUtils.isEmpty(instance.getVpcId())) {
            instance.setVpcId(defaultVpc.getVpcId());
        } else { // 转换成长vpcID
            Map<String, SimpleVpcVo> simpleVpcVoMap = clientFactory
                    .createVpcClient().get(new VpcIdsRequest(Collections.singletonList(instance.getVpcId())));
            String vpcUuid = null;
            if (simpleVpcVoMap == null || (vpcUuid = getVpcUuid(simpleVpcVoMap, instance.getVpcId())) == null) {
                throw new RDSExceptions.ParamValidationException("vpcId is not exist");
            }
            instance.setVpcId(vpcUuid);
        }

        if (instance.getVpcId().equals(defaultVpc.getVpcId())) {
            // 设置默认可用区
            ZoneDetailList zoneList = null;
            if (instance.getZoneNames() == null || instance.getZoneNames().size() == 0) {
                if(instance.getSubnets() == null || instance.getSubnets().size() == 0){
                    zoneList = othersService.zoneList(null);
                    for (ZoneDetailList.ZoneDetail zone : zoneList.getZones()) {
                        if (zone.isAvailable()) {
                            logger.info("zone.getZoneNames().get(0):" + zone.getZoneNames().get(0));
//                        instance.setAzone(zone.getZoneNames().get(0));
                            instance.setZoneNames(Collections.singletonList("cn-"
                                    + regionConfiguration.getCurrentRegion()
                                    + "-" + zone.getZoneNames().get(0).substring(4).toLowerCase()));
                            logger.info("instance.getAzone():" + instance.getAzone());
                            break;
                        }
                    }
                }else{
                    List<String> zoneNames=new ArrayList<>();
                    for(InstanceCreateModel.SubnetMap e: instance.getSubnets()){
                        zoneNames.add(e.getZoneName());
                    }
                    instance.setZoneNames(zoneNames);
                }


                if (instance.getZoneNames().size() <= 0) {
                    throw new RDSExceptions.NoDefaultZoneException();
                }
//                if (StringUtils.isEmpty(instance.getAzone())) {
//                    throw new RDSExceptions.NoDefaultZoneException();
//                }
            }
//            } else {
//                for (String apiZone : instance.getZoneNames()) {
//                    instance.setAzone(instance.getAzone() + othersService.apiZoneTologicalZone(apiZone) + ",");
//                }
//                instance.setAzone(instance.getAzone().substring(0, instance.getAzone().length() - 1));
//            }
            // 设置默认子网
            supplyZoneAndSubnetInfo(instance, true, zoneList);
        } else {
            if (instance.getSubnets() == null || instance.getSubnets().size() == 0) {
                throw new RDSExceptions.MissingSubnetException();
            } else {
                if(instance.getZoneNames() == null || instance.getZoneNames().size() == 0){
                    List<String> zoneNames=new ArrayList<>();
                    for(InstanceCreateModel.SubnetMap e: instance.getSubnets()){
                        zoneNames.add(e.getZoneName());
                    }
                    instance.setZoneNames(zoneNames);
                }
                supplyZoneAndSubnetInfo(instance, false, null);
            }
        }
        // 验证ip属不属于子网内
        boolean flag = false;
        SubnetVo subnetVo = new SubnetVo();
        String subnet = instanceCreateModel.getInstance().getSubnetId();
        if (StringUtils.isNotBlank(instanceCreateModel.getInstance().getOvip())) {
            if (instanceCreateModel.getInstance().getOvip().matches
                    ("([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])" +
                            "(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}")) {
                ExternalSubnetClient subnetClient = clientFactory.createSubnetClientByUserId();
                if (subnet.contains(",")){
                    subnetVo=subnetClient.findSubnetWithIpUsage(
                            subnet.substring(subnet.indexOf(":") + 1 , subnet.indexOf(","))
                    );
                    if (CheckIp.isInRange(instanceCreateModel
                            .getInstance().getOvip(), subnetVo.getCidr())) {
                        flag = true;
                    }
                }else{
                    subnetVo=subnetClient.findSubnetWithIpUsage(
                            subnet.substring(subnet.indexOf(":") + 1)
                    );
                    flag = CheckIp.isInRange(instanceCreateModel
                            .getInstance().getOvip(), subnetVo.getCidr());
                }
                if (flag) {
                    EniExternalClient externalClient = clientFactory.createEniExternalClient();
                    PrivateIpCheckRequest ipRequest = new PrivateIpCheckRequest();
                    List<PrivateIp> ips = new ArrayList<>();
                    PrivateIp ip = new PrivateIp();
                    ip.setPrivateIp(instanceCreateModel.getInstance().getOvip());
                    ips.add(ip);
                    ipRequest.setSubnetId(subnetVo.getSubnetId());
                    ipRequest.setVpcId(instanceCreateModel.getInstance().getVpcId());
                    ipRequest.setPrivateIps(ips);
                    if (!externalClient.validatedIpCheck(ipRequest).isIpValid()) {
                        throw new RDSExceptions.ParamValidationException("ip not available");
                    }
                }else{
                    throw new RDSExceptions.ParamValidationException("not belong to subnet");
                }
            }else{
                throw new RDSExceptions.ParamValidationException("ip format error");
            }
        }
        if (instance.getInitialDataReference() != null
                && instance.getInitialDataReference().getInstanceId() != null) {
            String instanceId = instance.getInitialDataReference().getInstanceId();
            if (BasisUtils.isShortId(instanceId)) {
                instanceId = findInsntaceUUidByShortId(instanceId);
            }
            RDSClient rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_3);
            GlobalInstanceResponses globalInstanceResponses = rdsClient.overviewList();
            List<GlobalInstanceResponses.GlobalInstanceResponse> instances = globalInstanceResponses.getInstances();
            GlobalInstanceResponses.GlobalInstanceResponse globalInstance = null;
            for (GlobalInstanceResponses.GlobalInstanceResponse ins : instances) {
                if (instanceId.equalsIgnoreCase(ins.getInstanceId())) {
                    logger.info("clone instance, find instance id by global msg, instance id is : {}", instanceId);
                    globalInstance = ins;
                }
            }
            if (globalInstance == null) {
                throw new RDSBusinessExceptions.ResourceNotExistException();
            }
            if (StringUtils.isNotEmpty(instance.getEngineVersion())
                    && !instance.getEngineVersion().equalsIgnoreCase(globalInstance.getDbVersion())) {
                throw new RDSExceptions.InitialDataReferenceEngineVersion();
            }
//                InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(instanceId)
//                        .instanceDescribe(instanceId);
//                if (!instance.getEngineVersion()
//                        .equalsIgnoreCase(instanceGetResponse.getInstance().getEngineVersion())){
//                    throw new RDSExceptions.InitialDataReferenceEngineVersion();
//                }
        }


        checkExclusiveBLB(instance.getBgwGroupId());
         // -20C48GB
         // -20C64GB
         // - 56C480GB
         // -6C8GB
         // -12C24GB
         // 置售罄状态
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            if ((instance.getCpuCount() == 6 && instance.getAllocatedMemoryInGB() == 8)
                    ||(instance.getCpuCount() == 12 && instance.getAllocatedMemoryInGB() == 24)
                    ||(instance.getCpuCount() == 20 && instance.getAllocatedMemoryInGB() == 48)
                    ||(instance.getCpuCount() == 20 && instance.getAllocatedMemoryInGB() == 64)
                    ||(instance.getCpuCount() == 56 && instance.getAllocatedMemoryInGB() == 480)
            ) {
                throw new RDSExceptions.ParamValidationException("This specification has been sold out.");
            }
        }

        // sqlserver2017 无论单双机 版本均为2017
        if (RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2017
                .equals(instance.getEngineVersion())) {
            instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2017);
        }

    }

    public void checkExclusiveBLB(String bgwGroupId) {
        if (StringUtils.isNotBlank(bgwGroupId)) {
            GetLbdcClusterResponse blbLbdc = othersService.getBlbLbdc(null);
            if (blbLbdc == null || CollectionUtils.isEmpty(blbLbdc.getClusterList())) {
                throw new RDSExceptions.ParamValidationException(
                        String.format("专属集群%s不存在或已被删除", bgwGroupId)
                );
            }
            boolean exist = false;
            for (LbdcCluster lbdcCluster : blbLbdc.getClusterList()) {
                if (bgwGroupId.equals(lbdcCluster.getId())) {
                    exist = true;
                    break;
                }
            }
            if (!exist) {
                throw new RDSExceptions.ParamValidationException(
                        String.format("专属集群%s不存在或已被删除", bgwGroupId)
                );
            }
        }
    }

    private String getVpcUuid(Map<String, SimpleVpcVo> simpleVpcVoMap, String vpcId) {
        if (simpleVpcVoMap == null || StringUtils.isEmpty(vpcId)) {
            return null;
        }
        Set<Map.Entry<String, SimpleVpcVo>> entries = simpleVpcVoMap.entrySet();
        for (Map.Entry<String, SimpleVpcVo> entry : entries) {
            if (entry.getValue().getShortId().equalsIgnoreCase(vpcId)) {
                return entry.getValue().getVpcId();
            }
        }
        return null;
    }

    public void supplyZoneAndSubnetInfo(InstanceCreateModel.DashCreateInstance instance,
                                        boolean allowDefaultSubnet, ZoneDetailList zoneDetailList) throws Exception {
        Map<String, String> zoneAndSubnet = new HashMap<>();
        Map<String, SubnetVo> subnetMapKeyBySubnetId = getSubnetMapKeyBySubnetId(instance.getVpcId());
        for (InstanceCreateModel.SubnetMap subnetMap : instance.getSubnets()) {
            String logicalZone = othersService.apiZoneTologicalZone(subnetMap.getZoneName());
            SubnetVo subnetVo = subnetMapKeyBySubnetId.get(subnetMap.getSubnetId());
            if (subnetVo != null && subnetVo.getAz().equals(logicalZone)) {
                if (zoneAndSubnet.get(logicalZone) != null) {
                    throw new RDSExceptions.ParamValidationException("one zoneName only need one subnetId");
                }
                zoneAndSubnet.put(logicalZone, subnetVo.getSubnetUuid());
            } else {
                throw new RDSExceptions.ParamValidationException("subnetId match zoneName error");
            }
        }
        for (String zone : instance.getZoneNames()) {
            String logicalZone = othersService.apiZoneTologicalZone(zone);
            instance.setAzone(instance.getAzone() + logicalZone + ",");
        }
        instance.setAzone(instance.getAzone().substring(0, instance.getAzone().length() - 1));

        for (String zone : instance.getZoneNames()) {
            String logicalZone = othersService.apiZoneTologicalZone(zone);
            String subnetId = zoneAndSubnet.get(logicalZone);
            if (StringUtils.isEmpty(subnetId)) {
                if (allowDefaultSubnet) {
                    if (zoneDetailList == null) {
                        zoneDetailList = othersService.zoneList(null);
                    }
                    for (ZoneDetailList.ZoneDetail zoneDetail : zoneDetailList.getZones()) {
                        if (logicalZone.equals(zoneDetail.getZoneNameStr())) {
                            subnetId = zoneDetail.getDefaultSubnetId();
                            break;
                        }
                    }
                } else {
                    throw new RDSExceptions.MissingSubnetException();
                }
            }
            instance.setSubnetId(instance.getSubnetId() + logicalZone + ":" + subnetId + ",");
        }
        instance.setSubnetId(instance.getSubnetId().substring(0, instance.getSubnetId().length() - 1));
    }

    public Map<String, SubnetVo> getSubnetMapKeyBySubnetId(String vpcId) {
        List<SubnetVo> subnetVos = clientFactory.createSubnetClient().findByVpcIds(Collections.singletonList(vpcId));
        Map<String, SubnetVo> subnetMap = new HashMap<>();
        for (SubnetVo subnetVo : subnetVos) {
            subnetMap.put(subnetVo.getShortId(), subnetVo);
        }
        return subnetMap;
    }

    public List<String> saveInstance2Db(InstanceCreateModel config, String orderUuid, String from) {
        List<String> instanceIds = new ArrayList<>();
        List<InstancePO> instancePOs = new ArrayList<>();
        InstanceCreateModel.DashCreateInstance instance;
        for (int i = 0; i < config.getNumber(); i++) {
            // shortID生成放到前一步，这里直接获取
            String shortId = config.getInstanceShortIds().get(i);
            instance = config.getInstance();
            InstancePO instancePO = new InstancePO();
            instancePO.setInstanceId(shortId);
            instancePO.setInstanceUuid(shortId);
            instancePO.setUserId(clientFactory.getAccountId());
            if (instance.getSourceInstanceId() != null) {
                InstancePO master
                        = instanceDao.queryInstanceByInstanceUuid(instance.getSourceInstanceId(), clientFactory.getAccountId());
                if (InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getEngine())) {
                    instancePO.setEngine(master.getEngine());
                } else {
                    instancePO.setEngine(instance.getEngine());
                    instancePO.setEngineVersion(instance.getEngineVersion());
                }
                instancePO.setSourceInstanceId(master.getInstanceId());
            } else {
                instancePO.setEngine(instance.getEngine());
                instancePO.setEngineVersion(instance.getEngineVersion());
            }

            instancePO.setApplicationType(instance.getCategory());
            Calendar calendar = Calendar.getInstance();
            instancePO.setInstanceCreateTime(new Timestamp(calendar.getTimeInMillis()));
            String productType = config.getProductType();
            if (Payment.isPostpay(productType)) {
                instancePO.setInstanceStatus(RdsInstanceStatus.CREATING.getValue());
                instancePO.setProductType(Payment.POSTPAY.getValue());
            } else {
                if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
                    instancePO.setInstanceStatus(RdsInstanceStatus.CREATING.getValue());
                } else {
                    instancePO.setInstanceStatus(RdsInstanceStatus.UNKNOWN.getValue());
                }
                instancePO.setProductType(Payment.PREPAY.getValue());

                calendar.add(Calendar.MONTH, config.getDuration());
                instancePO.setInstanceExpireTime(new Timestamp(calendar.getTimeInMillis()));
            }
            if (instance.getAllocatedMemoryInGB() != null) {
                instancePO.setMemoryCapacity(instance.getAllocatedMemoryInGB());
            }
            if (instance.getAllocatedStorageInGB() != null) {
                instancePO.setVolumeCapacity(instance.getAllocatedStorageInGB());
            }
            instancePO.setCpuCount(instance.getCpuCount());
            if (instance.getNodeAmount() != null) {
                instancePO.setNodeAmount(instance.getNodeAmount());
            }
            instancePO.setPubliclyAccessible(false);
            instancePO.setEipStatus(RDSConstant.EIP_STATUS_CLOSED);

            String aZone = instance.getAzone();
            if (StringUtils.isEmpty(instance.getReplicationType())) {
                instancePO.setReplicationType(RDSConstant.REPLICATION_TYPE_ASYNC);
            } else {
                instancePO.setReplicationType(BasisUtils.lowerCaseFirstChar(instance.getReplicationType()));
            }

            com.baidu.bce.logic.rds.dao.model.SnapshotPolicy backupPolicy = instancePO.getBackupPolicy();
            backupPolicy.setBackupTime(instance.getBackupPolicy().getBackupTime());
            backupPolicy.setBackupDays(instance.getBackupPolicy().getBackupDays());
            backupPolicy.setPersistent(instance.getBackupPolicy().getPersistent());
            backupPolicy.setExpireInDays(instance.getBackupPolicy().getExpireInDays());

            instancePO.setInstanceType(instance.getInstanceType());
            if (StringUtils.isEmpty(instance.getInstanceType())) {
                instancePO.setInstanceType(getInstanceType(instance.getEngine(), instance.getSourceInstanceId()));
            }

            instancePO.setInstanceName(instance.getInstanceName());
            instancePO.getEndpoint().setPort(BasisUtils.getDefaultPort(instancePO.getEngine()));
            instancePO.setZoneNames(aZone);
            instancePO.setVpcUuid(instance.getVpcId() == null ? "" : instance.getVpcId());
            instancePO.setSuperUserFlag(RDSConstant.SUPER_USER_FLAG_COMMON);
            instancePO.setOrderUuid(orderUuid);
            instancePO.setSource(from);

            List<SubnetPO> subnetPOs = instancePO.getSubnetIds();
            if (!StringUtils.isEmpty(instance.getSubnetId())) {
                String[] subnetItems = instance.getSubnetId().split(",");
                for (String item : subnetItems) {
                    if (!item.trim().isEmpty()) {
                        String[] part = item.trim().split(":");
                        if (part.length == 2) {
                            subnetPOs.add(new SubnetPO(part[0], part[1], shortId));
                        } else if (part.length == 1) {
                            throw new RDSExceptions.ResourceServerException();
                        }
                    }
                }
            }

            instancePOs.add(instancePO);
            instanceIds.add(shortId);
        }
        instanceDao.batchInsertInstances(instancePOs);

        // 插入订单至待同步订单表
        OrderNeedToSyncPO orderNeedToSyncPO = new OrderNeedToSyncPO();
        orderNeedToSyncPO.setUserId(clientFactory.getAccountId());
        orderNeedToSyncPO.setOrderUuid(orderUuid);
        orderNeedToSyncPO.setUpdatedTime(new Timestamp(Calendar.getInstance().getTimeInMillis()));
        if (Payment.isPostpay(config.getProductType()) || RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            orderNeedToSyncPO.setOrderStatus("creating");
        } else {
            orderNeedToSyncPO.setOrderStatus("unknown");
        }
        orderNeedToSyncPO.setOrderType(OrderType.NEW.name());
        orderNeedToSyncService.insertOneOrder(orderNeedToSyncPO);
        return instanceIds;
    }

    public void saveInstance2Db(InstanceCreateRequest instanceCreateRequest, Order order, String from) {
        // 插入订单至待同步订单表
        OrderNeedToSyncPO orderNeedToSyncPO = new OrderNeedToSyncPO();
        orderNeedToSyncPO.setUserId(order.getAccountId());
        orderNeedToSyncPO.setOrderUuid(order.getUuid());
        orderNeedToSyncPO.setUpdatedTime(new Timestamp(Calendar.getInstance().getTimeInMillis()));
        if (Payment.isPostpay(order.getProductType()) || RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            orderNeedToSyncPO.setOrderStatus("creating");
        } else {
            orderNeedToSyncPO.setOrderStatus("unknown");
        }
        orderNeedToSyncPO.setOrderType(OrderType.NEW.name());
        try {
            orderNeedToSyncService.insertOneOrder(orderNeedToSyncPO);
        } catch (DuplicateKeyException e) {
            logger.debug("Occur DuplicateKeyException");
            List<String> instanceShortIds = instanceDao.getShortIdsByOrderId(order.getUuid());
            logger.debug("instanceShortIds = {}", JsonUtils.toJsonString(instanceShortIds));
            // 由于主从延迟，这里可能存在主库写入，从库读不到的情况
            if (CollectionUtils.isNotEmpty(instanceShortIds)) {
                logger.debug("setInstanceIds");
                instanceCreateRequest.setInstanceIds(instanceShortIds);
            }
            return;
        }

        // 生成实例短 ID
        List<String> instanceShortIds = generateInstanceShortIds(
                instanceCreateRequest.getInstanceParameters().getInstanceType(),
                instanceCreateRequest.getInstanceAmount());
        instanceCreateRequest.setInstanceIds(instanceShortIds);

        List<String> instanceIds = new ArrayList<>();
        List<InstancePO> instancePOs = new ArrayList<>();
        for (int i = 0; i < instanceCreateRequest.getInstanceAmount(); i++) {
            // shortID生成放到前一步，这里直接获取
            String shortId = instanceCreateRequest.getInstanceIds().get(i);
            InstancePO instancePO = new InstancePO();
            instancePO.setInstanceId(shortId);
            instancePO.setInstanceUuid(shortId);
            instancePO.setUserId(order.getAccountId());
            InstanceCreateRequest.InstanceParameters instance = instanceCreateRequest.getInstanceParameters();
            if (instance.getSourceInstanceId() != null) {
                InstancePO master
                        = instanceDao.queryInstanceByInstanceUuid(instance.getSourceInstanceId(), order.getAccountId());
                if (InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getEngine())) {
                    instancePO.setEngine(master.getEngine());
                } else {
                    instancePO.setEngine(instance.getEngine());
                    instancePO.setEngineVersion(instance.getEngineVersion());
                }
                instancePO.setSourceInstanceId(master.getInstanceId());
            } else {
                instancePO.setEngine(instance.getEngine());
                instancePO.setEngineVersion(instance.getEngineVersion());
            }

            instancePO.setApplicationType(instance.getCategory());
            Calendar calendar = Calendar.getInstance();
            instancePO.setInstanceCreateTime(new Timestamp(calendar.getTimeInMillis()));
            String productType = order.getProductType();
            if (Payment.isPostpay(productType)) {
                instancePO.setInstanceStatus(RdsInstanceStatus.CREATING.getValue());
                instancePO.setProductType(Payment.POSTPAY.getValue());
            } else {
                if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
                    instancePO.setInstanceStatus(RdsInstanceStatus.CREATING.getValue());
                } else {
                    instancePO.setInstanceStatus(RdsInstanceStatus.UNKNOWN.getValue());
                }
                instancePO.setProductType(Payment.PREPAY.getValue());

                instancePO.setInstanceExpireTime(new Timestamp(instance.getInstanceExpireTime().getTime()));
            }
            if (instance.getAllocatedMemoryInMB() != null) {
                instancePO.setMemoryCapacity(instance.getAllocatedMemoryInMB() / 1024);
            }
            if (instance.getAllocatedStorageInGB() != null) {
                instancePO.setVolumeCapacity(instance.getAllocatedStorageInGB());
            }
            instancePO.setCpuCount(instance.getCpuCount());
            if (instance.getNodeAmount() != null) {
                instancePO.setNodeAmount(instance.getNodeAmount());
            }
            instancePO.setPubliclyAccessible(false);
            instancePO.setEipStatus(RDSConstant.EIP_STATUS_CLOSED);

            String aZone = instance.getAzone();
            if (StringUtils.isEmpty(instance.getReplicationType())) {
                instancePO.setReplicationType(RDSConstant.REPLICATION_TYPE_ASYNC);
            } else {
                instancePO.setReplicationType(BasisUtils.lowerCaseFirstChar(instance.getReplicationType()));
            }

            com.baidu.bce.logic.rds.dao.model.SnapshotPolicy backupPolicy = instancePO.getBackupPolicy();
            backupPolicy.setBackupTime(instance.getBackupPolicy().getBackupTime());
            backupPolicy.setBackupDays(instance.getBackupPolicy().getBackupDays());
            backupPolicy.setPersistent(instance.getBackupPolicy().getPersistent());
            backupPolicy.setExpireInDays(instance.getBackupPolicy().getExpireInDays());

            instancePO.setInstanceType(instance.getInstanceType());
            if (StringUtils.isEmpty(instance.getInstanceType())) {
                instancePO.setInstanceType(getInstanceType(instance.getEngine(), instance.getSourceInstanceId()));
            }

            instancePO.setInstanceName(instance.getInstanceName());
            instancePO.getEndpoint().setPort(BasisUtils.getDefaultPort(instancePO.getEngine()));
            instancePO.setZoneNames(aZone);
            instancePO.setVpcUuid(instance.getVpcId() == null ? "" : instance.getVpcId());
            instancePO.setSuperUserFlag(RDSConstant.SUPER_USER_FLAG_COMMON);
            instancePO.setOrderUuid(order.getUuid());
            instancePO.setSource(from);

            List<SubnetPO> subnetPOs = instancePO.getSubnetIds();
            if (instance.getSubnetId() != null) {
                for (Map.Entry<String, String> entry : instance.getSubnetId().entrySet()) {
                    subnetPOs.add(new SubnetPO(entry.getKey(), entry.getValue(), shortId));
                }
            }

            instancePOs.add(instancePO);
            instanceIds.add(shortId);
        }
        instanceDao.batchInsertInstances(instancePOs);
    }

    private String getInstanceType(String engine, String sourceInstanceId) {
        if (StringUtils.isEmpty(sourceInstanceId)) {
            return InstanceType.INSTANCE_TYPE_MASTER.getValue();
        } else if (engine.equals(RDSConstant.RDS_ENGINE_PROXY)) {
            return InstanceType.INSTANCE_TYPE_PROXY.getValue();
        } else {
            return InstanceType.INSTANCE_TYPE_REPLICA.getValue();
        }
    }

    public void onOrOfflineReplica(String proxyInstanceId, InstanceUpdateReplicaOnLineRequest request) {
        request.setRdsproxyId(proxyInstanceId);
        clientFactory.createRdsClient().onOrOfflineReplica(request);
    }

    public void putFlow(String proxyInstanceId, PutFlowRequest request) {

        // 流量配比只能是1-5
        if (StringUtils.isBlank(request.getWeight())) {
            throw new RDSExceptions.InvalidWeightException("params 'weight' required.");
        }

        Integer weight =  Integer.valueOf(request.getWeight());
        if (weight < 1 || weight > 5) {
            throw new RDSExceptions.InvalidWeightException("params 'weight' must be between 1 and 5.");
        }

        request.setRdsproxyId(proxyInstanceId);
        clientFactory.createRdsClient().putFlow(request);
    }


    private List<String> saveInstanceOnDcc2Db(InstanceCreateModel config, String instanceUuid, String from) {
        List<String> instanceIds = new ArrayList<>();
        List<InstancePO> instancePOs = new ArrayList<>();
        InstanceCreateModel.DashCreateInstance instance;
        for (int i = 0; i < config.getNumber(); i++) {
            String shortId = idGenerator.createExternalId(RDSConstant.API_RDS_PREFIX);
            instance = config.getInstance();
            InstancePO instancePO = new InstancePO();
            instancePO.setInstanceId(shortId);
            instancePO.setInstanceUuid(instanceUuid);
            instancePO.setUserId(clientFactory.getAccountId());

            instancePO.setEngine(instance.getEngine());
            instancePO.setEngineVersion(instance.getEngineVersion());

            instancePO.setApplicationType(instance.getCategory());
            Calendar calendar = Calendar.getInstance();
            instancePO.setInstanceCreateTime(new Timestamp(calendar.getTimeInMillis()));
            instancePO.setInstanceStatus(RdsInstanceStatus.CREATING.getValue());
            if (instance.getAllocatedMemoryInGB() != null) {
                instancePO.setMemoryCapacity(instance.getAllocatedMemoryInGB());
            }
            if (instance.getAllocatedStorageInGB() != null) {
                instancePO.setTotalVolumeCapacity(instance.getAllocatedStorageInGB());
            }
            instancePO.setCpuCount(instance.getCpuCount());
//            if (instance.getNodeAmount() != null) {
//                instancePO.setNodeAmount(instance.getNodeAmount());
//            }
            instancePO.setPubliclyAccessible(false);
            instancePO.setEipStatus(RDSConstant.EIP_STATUS_CLOSED);

            String aZone = instance.getAzone();
            if (StringUtils.isEmpty(instance.getReplicationType())) {
                instancePO.setReplicationType(RDSConstant.REPLICATION_TYPE_ASYNC);
            } else {
                instancePO.setReplicationType(BasisUtils.lowerCaseFirstChar(instance.getReplicationType()));
            }

            com.baidu.bce.logic.rds.dao.model.SnapshotPolicy backupPolicy = instancePO.getBackupPolicy();
            backupPolicy.setBackupTime(instance.getBackupPolicy().getBackupTime());
            backupPolicy.setBackupDays(instance.getBackupPolicy().getBackupDays());
            backupPolicy.setPersistent(instance.getBackupPolicy().getPersistent());
            backupPolicy.setExpireInDays(instance.getBackupPolicy().getExpireInDays());

            instancePO.setInstanceType(getInstanceType(instance.getEngine(), instance.getSourceInstanceId()));

            instancePO.setInstanceName(instance.getInstanceName());
            instancePO.getEndpoint().setPort(BasisUtils.getDefaultPort(instancePO.getEngine()));
            instancePO.setZoneNames(aZone);
            instancePO.setVpcUuid(instance.getVpcId());
            instancePO.setSuperUserFlag(RDSConstant.SUPER_USER_FLAG_COMMON);
            instancePO.setOrderUuid("");
            instancePO.setSource(from);

            List<SubnetPO> subnetPOs = instancePO.getSubnetIds();
            if (!StringUtils.isEmpty(instance.getSubnetId())) {
                String[] subnetItems = instance.getSubnetId().split(",");
                for (String item : subnetItems) {
                    if (!item.trim().isEmpty()) {
                        String[] part = item.trim().split(":");
                        if (part.length == 2) {
                            subnetPOs.add(new SubnetPO(part[0], part[1], shortId));
                        } else if (part.length == 1) {
                            throw new RDSExceptions.ResourceServerException();
                        }
                    }
                }
            }

            if (instance.getDccHosts() != null) {
                for (MachinePO machinePO : instance.getDccHosts()) {
                    machinePO.setInstanceId(shortId);
                }
            }
            instancePO.setDccHosts(instance.getDccHosts());
            instancePO.setMachineType(instance.getMachineType());
            instancePOs.add(instancePO);
            instanceIds.add(shortId);
        }
        instanceDao.batchInsertInstances(instancePOs);
        return instanceIds;
    }

    public OrderUuidResult resizeInstance(String instanceId,
                                          RdsCreateOrderRequestVo<PriceDiffModel> request,
                                          String from) {
        PriceDiffModel config = request.getItems().get(0).getConfig();
        if (EdgeService.isSubnetId(config.getEdgeSubnetId())) {
            // ConsoleAPI, OpenAPI 均提供 subnetId 参数，而非 subnetUuid 参数，但后端 API 需要 subnetUuid，Console 层做统一转换。
            EdgeSubnet edgeSubnet = edgeService.getSubnet(config.getEdgeSubnetId());
            config.setEdgeSubnetId(edgeSubnet.getSubnetUuid());
        }
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
//            checkInstanceSpecification(config);
            getZoneAndSubnetUuid(config);
        }
        InstanceExtension instance = getInstanceExtension(instanceId);
        checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_RESIZE, instance);


        // 限制mysql5.5 5.6转云盘 白名单开放
        if (RDSConstant.RDS_ENGINE_MYSQL.contains(instance.getEngine())) {
            if (RDSConstant.RDS_SSD_ENGINE_MYSQL_VERSIONS.contains(instance.getEngineVersion())) {
                if (ObjectUtils.equals("normal_io", instance.getDiskIoType())) {
                    List<String> cloudDisk = Arrays.asList("cloud_high", "cloud_enha", "cloud_nor");
                    String diskIoType = request.getItems().get(0).getConfig().getDiskIoType();
                    if (StringUtils.isNotBlank(diskIoType) && cloudDisk.contains(diskIoType)) {
                        WhiteAccountResult whiteAccount = othersService.isWhiteAccount(null);
                        if (!whiteAccount.getMysqlSsdToEssd()) {
                            throw new RDSExceptions.NotSupportLocalDiskConvertDisk();
                        } else {
                            logger.debug("Interface white list of mysqlSsdToEssd");
                        }
                    } else {
                        logger.error("The diskIoType parameter is empty");
                    }
                }
            }
        }

        // 限制通用型实例 云盘转本地盘
        if (StringUtils.isNotEmpty(instance.getResourceType())
                && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType())
                && StringUtils.isNotEmpty(instance.getDiskIoType())
                && !RDSConstant.DISK_OF_NORMAL_IO.equalsIgnoreCase(instance.getDiskIoType())
                && StringUtils.isNotEmpty(config.getDiskIoType())
                && RDSConstant.DISK_OF_NORMAL_IO.equalsIgnoreCase(config.getDiskIoType())) {
            logger.info("This instance {} not support cloud disk to local disk", instanceId);
            throw new RDSBusinessExceptions.GeneralInstanceNotSupportException();
        }


        if (Arrays.asList(OrderType.TO_POSTPAY, OrderType.TO_PREPAY).contains(instance.getInstanceStatus())) {
            throw new RDSBusinessExceptions.HasChangeBillingException();
        }
        if (InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getInstanceType())) {
            if (config.getNodeAmount() == null) {
                throw new RDSBusinessExceptions.ParamValidationException();
            }
        } else {
            if (config.getAllocatedMemoryInGB() == null && config.getAllocatedMemoryInMB() == null) {
                if (!config.getOldFlavor()) {
                    config.setAllocatedMemoryInGB((int)instance.getAllocatedMemoryInGB());
                } else {
                    config.setAllocatedMemoryInMB(instance.getAllocatedMemoryInMB());
                }
            }
            if (config.getAllocatedStorageInGB() == null) {
                config.setAllocatedStorageInGB(instance.getAllocatedStorageInGB());
            }
            if (config.getCpuCount() == null) {
                config.setCpuCount(instance.getCpuCount());
            }
            // 这里略显多余
            if (config.getDiskIoType() == null) {
                config.setDiskIoType(instance.getDiskIoType());
            }
        }
        config.setInstanceId(instanceId);
        if (ObjectUtils.equals("cloud_nor", request.getItems().get(0).getConfig().getDiskIoType())) {
            request.getItems().get(0).getConfig().setDiskType("cds");
            request.getItems().get(0).getConfig().setCdsType("premium_ssd");
            // 高性能云磁盘
        } else if (ObjectUtils.equals("cloud_high", request.getItems().get(0).getConfig().getDiskIoType())) {
            request.getItems().get(0).getConfig().setDiskType("cds");
            request.getItems().get(0).getConfig().setCdsType("ssd");
        } else if (ObjectUtils.equals("cloud_enha", request.getItems().get(0).getConfig().getDiskIoType())){
            request.getItems().get(0).getConfig().setDiskType("cds");
            request.getItems().get(0).getConfig().setCdsType("enhanced_ssd_pl1");
        } else if (ObjectUtils.equals("normal_io", request.getItems().get(0).getConfig().getDiskIoType())){
            request.getItems().get(0).getConfig().setDiskType("ssd");
            request.getItems().get(0).getConfig().setCdsType("");
        }

        // 只读实例支持本地盘ssd和增强型ESSD cloud_enha
        if (InstanceType.INSTANCE_TYPE_REPLICA.getValue().equalsIgnoreCase(instance.getEngine())) {
            if (ObjectUtils.equals("cloud_enha", request.getItems().get(0).getConfig().getDiskIoType())){
                request.getItems().get(0).getConfig().setDiskType("cds");
                request.getItems().get(0).getConfig().setCdsType("enhanced_ssd_pl1");
            } else if (ObjectUtils.equals("normal_io", request.getItems().get(0).getConfig().getDiskIoType())){
                request.getItems().get(0).getConfig().setDiskType("ssd");
                request.getItems().get(0).getConfig().setCdsType("");
            } else {
                throw new RDSExceptions.ParamValidationException("diskIoType must be normal_io or cloud_enha");
            }
        }

        OrderUuidResult result = rdsOrderService.createResizeOrder(request, instance);
        Map<String, Object> map = new HashMap<>();
//        if (Payment.isPostpay(instance.getProductType()) || RDSConstant.FROM_API.equalsIgnoreCase(from)) {
//            map.put(InstanceTableColums.instanceStatus, RdsInstanceStatus.MODIFYING.getValue());
//            map.put(InstanceTableColums.orderStatus, RdsInstanceStatus.MODIFYING.getValue());
//        }
        map.put(InstanceTableColums.orderUuid, result.getOrderId());
        instanceDao.updateInstanceByInstanceUuid(map, config.getInstanceId(), clientFactory.getAccountId());

        // 插入订单至待同步订单表
        OrderNeedToSyncPO orderNeedToSyncPO = new OrderNeedToSyncPO();
        orderNeedToSyncPO.setUserId(clientFactory.getAccountId());
        orderNeedToSyncPO.setOrderUuid(result.getOrderId());
        orderNeedToSyncPO.setUpdatedTime(new Timestamp(Calendar.getInstance().getTimeInMillis()));
        if (Payment.isPostpay(instance.getProductType()) || RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            orderNeedToSyncPO.setOrderStatus("creating");
        } else {
            orderNeedToSyncPO.setOrderStatus("unknown");
        }
        orderNeedToSyncPO.setOrderType(OrderType.RESIZE.name());
        orderNeedToSyncService.insertOneOrder(orderNeedToSyncPO);
        return result;
    }


    public List<Order> resizeBatchInstance(String instanceId,
                                           RdsCreateOrderRequestVo<PriceDiffModel> request,
                                           String from) {
        // 实例长短 ID 转换（只读）
        for (int i = 0 ; i < request.getItems().size(); i++) {
            PriceDiffModel config = request.getItems().get(i).getConfig();
            // 批量变配时，接口传入的只读实例 ID 为短 ID ，此处做长短 ID 转换
            if (StringUtils.isNotEmpty(config.getInstanceId())) {
                config.setInstanceId(idMapperService.getInstanceUuid(config.getInstanceId()));
                request.getItems().get(i).setConfig(config);
            } else {
                logger.error("batch resize instance id is empty.");
                throw new RDSExceptions.ParamValidationException();
            }
        }

        List<InstanceExtension> instanceList = new ArrayList<>();
        Map<String, Future<Instance>> extensionMap = new HashMap<>();
        // 并发方式读取实例详情
        for (int i = 0; i < request.getItems().size(); i++) {
            final PriceDiffModel config = request.getItems().get(i).getConfig();
            final RDSClient2 rdsClient2 = clientFactory.createRdsClient2ByInstanceId(config.getInstanceId());
            Future<Instance> instance = ThreadPool.submit(new Callable<Instance>() {
                @Override
                public Instance call() throws Exception {
                    return rdsClient2.instanceDescribe(config.getInstanceId()).getInstance();
                }
            });

            extensionMap.put(config.getInstanceId(), instance);
        }

        for (int i = 0 ; i < request.getItems().size(); i++) {
            PriceDiffModel config = request.getItems().get(i).getConfig();
            if (EdgeService.isSubnetId(config.getEdgeSubnetId())) {
                // ConsoleAPI, OpenAPI 均提供 subnetId 参数，而非 subnetUuid 参数，但后端 API 需要 subnetUuid，Console 层做统一转换。
                EdgeSubnet edgeSubnet = edgeService.getSubnet(config.getEdgeSubnetId());
                config.setEdgeSubnetId(edgeSubnet.getSubnetUuid());
            }
            if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
//                checkInstanceSpecification(config);
                getZoneAndSubnetUuid(config);
            }
            Future<Instance> instanceFuture = extensionMap.get(config.getInstanceId());
            Instance instance1 = null;
            try {
                instance1 = instanceFuture.get();
            } catch (ExecutionException | InterruptedException e) {
                logger.error("instanceFuture.get() occur error.");
            }
            InstanceExtension instance = new InstanceExtension(instance1);
            if (!"DCC".equalsIgnoreCase(instance.getMachineType())) {
                Resource resource = getResourceByInstanceId(config.getInstanceId());
                logger.warn("config.getInstanceId() is {}, resource is {}", config.getInstanceId(), resource);
                if (null == resource) {
                    logger.warn("rds instanceId: {} not in resources", instanceId);
                    throw new RDSBusinessExceptions.ResourceNotExistException();
                }
                instance.setOrderId(resource.getOrderId());
                instance.setInstanceExpireTime(resource.getExpireTime());
                instance.setResourceUuid(resource.getUuid());
                instance.setProductType(resource.getProductType());
            } else {
                instance.withDccHosts(instanceDao.getMachineListByUuid(instance.getInstanceId()));
            }

            if (instance.getInstanceStatus().equals("available")) {
                if (!instance.getLockMode().equals("unlock")) {
                    instance.setInstanceStatus(instance.getLockMode());
                }
            }
            // physical to logical
            if (instance.getAzone() == null || instance.getAzone().equals("default")) {
                instance.setAzone("zoneA");
            }
            if (instance.getAzone().contains("+")) {
                instance.setAzone(instance.getAzone().replaceAll("\\+", ","));
            }

            // 系列信息，目前只支持单机版
            if (instance.getIsSingle() != null && instance.getIsSingle()
                    || instance.getApplicationType() != null && instance.getApplicationType().equals("single")) {
                instance.setCategory(RDSConstant.CATEGORY_SINGLETON);
            }
            logger.debug("instanceExtension : {}", JsonUtils.toJsonString(instance));
            // 限制批量变配的引擎类型
            // 此处限制前置是为了在不满足条件的情况下减少耗时
            if (! RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instance.getEngine())) {
                throw new RDSBusinessExceptions.InstanceShrinkageEngineException();
            }

            // 若当前对只读实例发起批量变配，直接拦截
            if (i == 0 && RDSConstant.INSTANCE_TYPE_REPLICA.equalsIgnoreCase(instance.getInstanceType())) {
                throw new RDSBusinessExceptions.ParamValidationException();
            }

            // 顺序存放
            instanceList.add(instance);
            checkInstanceByStatusForBackend(config.getInstanceId(), RDSConstant.INSTANCE_STATUS_RESIZE, instance);


            // 限制mysql5.5 5.6转云盘 白名单开放
            if (RDSConstant.RDS_ENGINE_MYSQL.contains(instance.getEngine())) {
                if (RDSConstant.RDS_SSD_ENGINE_MYSQL_VERSIONS.contains(instance.getEngineVersion())) {
                    if (ObjectUtils.equals("normal_io", instance.getDiskIoType())) {
                        List<String> cloudDisk = Arrays.asList("cloud_high", "cloud_enha", "cloud_nor");
                        String diskIoType = request.getItems().get(0).getConfig().getDiskIoType();
                        if (StringUtils.isNotBlank(diskIoType) && cloudDisk.contains(diskIoType)) {
                            WhiteAccountResult whiteAccount = othersService.isWhiteAccount(null);
                            if (!whiteAccount.getMysqlSsdToEssd()) {
                                throw new RDSExceptions.NotSupportLocalDiskConvertDisk();
                            } else {
                                logger.debug("Interface white list of mysqlSsdToEssd");
                            }
                        } else {
                            logger.error("The diskIoType parameter is empty");
                        }
                    }
                }
            }


            if (Arrays.asList(OrderType.TO_POSTPAY, OrderType.TO_PREPAY).contains(instance.getInstanceStatus())) {
                throw new RDSBusinessExceptions.HasChangeBillingException();
            }
            if (InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getInstanceType())) {
                if (config.getNodeAmount() == null) {
                    throw new RDSBusinessExceptions.ParamValidationException();
                }
            } else {
                if (config.getAllocatedMemoryInGB() == null && config.getAllocatedMemoryInMB() == null) {
                    if (!config.getOldFlavor()) {
                        config.setAllocatedMemoryInGB((int) instance.getAllocatedMemoryInGB());
                    } else {
                        config.setAllocatedMemoryInMB(instance.getAllocatedMemoryInMB());
                    }
                }
                if (config.getAllocatedStorageInGB() == null) {
                    config.setAllocatedStorageInGB(instance.getAllocatedStorageInGB());
                }
                if (config.getCpuCount() == null) {
                    config.setCpuCount(instance.getCpuCount());
                }
                // 这里略显多余
                if (config.getDiskIoType() == null) {
                    config.setDiskIoType(instance.getDiskIoType());
                }
            }
            // 新接口此值为必填项，不需在此重新赋值
            // config.setInstanceId(instanceId);
            if (ObjectUtils.equals("cloud_nor", request.getItems().get(i).getConfig().getDiskIoType())) {
                request.getItems().get(i).getConfig().setDiskType("cds");
                request.getItems().get(i).getConfig().setCdsType("premium_ssd");
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", request.getItems().get(i).getConfig().getDiskIoType())) {
                request.getItems().get(i).getConfig().setDiskType("cds");
                request.getItems().get(i).getConfig().setCdsType("ssd");
            } else if (ObjectUtils.equals("cloud_enha", request.getItems().get(i).getConfig().getDiskIoType())){
                request.getItems().get(i).getConfig().setDiskType("cds");
                request.getItems().get(i).getConfig().setCdsType("enhanced_ssd_pl1");
            } else if (ObjectUtils.equals("normal_io", request.getItems().get(i).getConfig().getDiskIoType())){
                request.getItems().get(i).getConfig().setDiskType("ssd");
                request.getItems().get(i).getConfig().setCdsType("");
            }

            // 只读实例支持本地盘ssd和增强型ESSD cloud_enha
            if (InstanceType.INSTANCE_TYPE_REPLICA.getValue().equalsIgnoreCase(instance.getEngine())) {
                if (ObjectUtils.equals("cloud_enha", request.getItems().get(i).getConfig().getDiskIoType())){
                    request.getItems().get(i).getConfig().setDiskType("cds");
                    request.getItems().get(i).getConfig().setCdsType("enhanced_ssd_pl1");
                } else if (ObjectUtils.equals("normal_io", request.getItems().get(i).getConfig().getDiskIoType())){
                    request.getItems().get(i).getConfig().setDiskType("ssd");
                    request.getItems().get(i).getConfig().setCdsType("");
                } else {
                    throw new RDSExceptions.ParamValidationException("diskIoType must be normal_io or cloud_enha");
                }
            }
        }

        // 批量变配时主实例磁盘大小要小于等于只读实例磁盘大小 & 校验是否存在多主变配的情况
        checkDiskAndParam(request, instanceList);

        // 批量变配订单
        List<Order> result = rdsOrderService.createBatchResizeOrder(request, instanceList, from);

        // 根据订单项中的资源 id 反查实例 id，然后同步至订单表
        syncOrders(result, instanceList, from);
        return result;

    }

    /**
     * 批量变配时校验主实例与只读实例磁盘大小关系 & 参数是否存在多主情况 O(n)
     * @param request
     */
    private void checkDiskAndParam(RdsCreateOrderRequestVo<PriceDiffModel> request,
                                   List<InstanceExtension> instanceList) {

        // 先拿到主实例
        InstanceExtension masterInstance = instanceList.get(0);
        // 1.先校验当前传参情况 保证主实例与只读实例存储大小限制
       if (StringUtils.isEmpty(masterInstance.getSourceInstanceId())) {
           // 当前第一个实例为主实例，符合预期
           PriceDiffModel masterConfig = request.getItems().get(0).getConfig();
           // 校验请求参数的磁盘限制
           for (int i = 1; i < request.getItems().size(); i++) {
               PriceDiffModel replicaConfig = request.getItems().get(i).getConfig();
               if (masterConfig.getVolumeCapacity() > replicaConfig.getVolumeCapacity()) {
                   // 若主实例磁盘大于只读实例磁盘 ，报错
                   throw new RDSBusinessExceptions.ResizeMasterReplicaDiskException();
               }
           }
       } else {
           logger.error("instanceList.get(0) is not master, detail is : {}", JsonUtils.toJsonString(masterInstance));
       }

        // 2.校验传参中的只读实例是否为当前主实例的只读实例
        List<String> replicas = masterInstance.getTopology().getReadReplica();
        for (int i = 1 ; i < request.getItems().size(); i++) {
            if (!replicas.contains(request.getItems().get(i).getConfig().getInstanceId())) {
                throw new RDSBusinessExceptions.ParameterTopoErrorException(
                        idMapperService.getInstanceId(request.getItems().get(i).getConfig().getInstanceId()));
            }
        }

        // 3.当批量变配的操作没有将所有只读实例均进行变配时，再校验当前主实例下挂载的所有只读实例大小限制关系
        int readNum = masterInstance.getTopology().getReadReplicaIdMapping().size() + 1;
        if (request.getItems().size() != readNum) {
            if (masterInstance.getTopology().getReadReplica().size() != 0
                    && RDSConstant.INSTANCE_TYPE_MASTER.equalsIgnoreCase(masterInstance.getInstanceType())) {
                Integer volumeCapacity = request.getItems().get(0).getConfig().getVolumeCapacity();
                List<Instance.ReadReplicaIdMapping> readReplicaIdMapping =
                        masterInstance.getTopology().getReadReplicaIdMapping();

                // 将当前发起变配的只读实例踢出
                Map<String, Instance.ReadReplicaIdMapping> replicaInstances = new HashMap<>();
                List<Instance.ReadReplicaIdMapping> replicaIdMappingList = new ArrayList<>();
                for (Instance.ReadReplicaIdMapping readReplicaIdMapping1 : readReplicaIdMapping) {
                    replicaInstances.put(readReplicaIdMapping1.getAppId(), readReplicaIdMapping1);
                }
                // 将当前发起变配的只读实例踢出
                for (int i = 1 ; i < request.getItems().size(); i++) {
                    if (!replicaInstances.containsKey(request.getItems().get(i).getConfig().getInstanceId())) {
                        replicaIdMappingList.add
                                (replicaInstances.get(request.getItems().get(i).getConfig().getInstanceId()));
                    }
                }
                // 将当前发起变配的只读实例踢出
//                List<Instance.ReadReplicaIdMapping> replicaIdMappingList = new ArrayList<>();
//                for (int i = 1; i < request.getItems().size(); i++) {
//                    for (Instance.ReadReplicaIdMapping mapping : readReplicaIdMapping) {
//                        if (!request.getItems().get(i).getConfig().getInstanceId().
//                                equalsIgnoreCase(mapping.getAppId())) {
//                            replicaIdMappingList.add(mapping);
//                        }
//                    }
//                }
                logger.debug("replicaidMappingList is {}", replicaIdMappingList);

                for (Instance.ReadReplicaIdMapping mapping : replicaIdMappingList) {
                    if (volumeCapacity > mapping.getAllocatedStorageInGB()) {
                        logger.debug("volumeCapacity is {}, mapping.getAllocatedStorageInGB is {}",
                                volumeCapacity, mapping.getAllocatedStorageInGB());
                        throw new RDSBusinessExceptions.ResizeMasterReplicaDiskException();
                    }
                }
            }
        }

        // 校验参数是否存在多主情况
        for (int i = 1; i < instanceList.size(); i ++) {
            InstanceExtension instanceExtension = instanceList.get(i);
            if (StringUtils.isEmpty(instanceExtension.getSourceInstanceId())) {
                // 证明当前不是实例 不符合预期
                throw new RDSBusinessExceptions.MultiMasterResizeException();
            }
        }

        // 存储降配时，目标存储size必须大于等于使用量*1.2；
        if (request.getItems().get(0).getConfig().getVolumeCapacity() < instanceList.get(0).getAllocatedStorageInGB()) {
            for (int i = 0 ;i < request.getItems().size(); i++) {
                double preVolumnCapacity = instanceList.get(i).getUsedStorageInGB();
                double usedStoraged = preVolumnCapacity * 1.2;
                int targetVolumnCapacity = request.getItems().get(i).getConfig().getVolumeCapacity();
                if (targetVolumnCapacity < usedStoraged) {
                    throw new RDSBusinessExceptions.InstanceShrinkageSizeException();
                }
            }
        }

    }

    /**
     * 批量操作时同步订单信息，注此时也会将批量订单同步至现有的订单同步表，TODO
     * @param result
     * @param instanceList
     * @param from
     */
    private void syncOrders(List<Order> result, List<InstanceExtension> instanceList, String from) {

        Map<Resource , Order> instanceOrderMaps = new HashMap<>();
        ResourceClient resourceClient = clientFactory.createResourceClient();
        // 维护一个订单与实例的对应关系
        for (Order order : result) {
            // 每个订单的订单项里可能会有多个资源ID，若想在此反查实例 ID 的话，理论上拿任意一个资源 ID 均可
            List<String> resourceIds = order.getItems().get(0).getResourceIds();
            if (CollectionUtils.isNotEmpty(resourceIds)) {
                Resource resource = resourceClient.get(resourceIds.get(0));
                instanceOrderMaps.put(resource, order);
            }
        }
        for (Map.Entry<Resource, Order> entry : instanceOrderMaps.entrySet()) {
            Resource resource = entry.getKey();
            Map<String, Object> map = new HashMap<>();
//            if (Payment.isPostpay(resource.getProductType()) || RDSConstant.FROM_API.equalsIgnoreCase(from)) {
//                map.put(InstanceTableColums.instanceStatus, RdsInstanceStatus.MODIFYING.getValue());
//                map.put(InstanceTableColums.orderStatus, RdsInstanceStatus.MODIFYING.getValue());
//            }
            map.put(InstanceTableColums.orderUuid, entry.getValue().getUuid());
            instanceDao.updateInstanceByInstanceUuid(map, resource.getName(), clientFactory.getAccountId());

            // 插入订单至待同步订单表
            OrderNeedToSyncPO orderNeedToSyncPO = new OrderNeedToSyncPO();
            orderNeedToSyncPO.setUserId(clientFactory.getAccountId());
            orderNeedToSyncPO.setOrderUuid(entry.getValue().getUuid());
            orderNeedToSyncPO.setUpdatedTime(new Timestamp(Calendar.getInstance().getTimeInMillis()));
            if (Payment.isPostpay(resource.getProductType()) || RDSConstant.FROM_API.equalsIgnoreCase(from)) {
                orderNeedToSyncPO.setOrderStatus("creating");
            } else {
                orderNeedToSyncPO.setOrderStatus("unknown");
            }
            orderNeedToSyncPO.setOrderType(OrderType.RESIZE.name());
            orderNeedToSyncService.insertOneOrder(orderNeedToSyncPO);
        }
    }

    // 实例过保切换时间范围查询
    public InstanceQuotTimeDetailResponse quotFindRepairTime(String instanceId, String type, String from) {
        RDSClient rdsClient = clientFactory.createRdsClient();
        return rdsClient.quotFindRepairTime(instanceId, type);
    }

    // 实例过保切换时间范围查询
    public void quotChangeRepairTime(InstanceQuotChangeRequest request) {
        RDSClient rdsClient = clientFactory.createRdsClient();
        rdsClient.quotChangeRepairTime(request);

    }

    // 发起主备切换
    public void switchMasterBackup(String instanceId, SwitchMasterBackupRequest request, String from) {

        // 对于来自 openAPI 的请求 需要判断当前请求是立即操作还是时间窗口操作
        // 如果是立即操作的话，需要进行前置检查。如果前置检查通过即可进行可靠性优先的主备切换
        // 如果前置检查失败，则切换成可用性策略进行强制主备切换
//        if (RDSConstant.FROM_API.equals(from) && StringUtils.isNotEmpty(request.getEffectiveTime()) &&
//            RDSConstant.IMMEDIATE.equals(request.getEffectiveTime())) {
//
//            // 此时需要进行任务的前置检查
//            int countCheck = 6;
//            SwitchPrecheckResponse precheckResponse = strategyService.strategyService(instanceId);
//            TaskStatusResponses taskStatus = strategyService.taskStatus(precheckResponse.getTaskId());
//
//            // 每次最多请求六次管控前置检查接口，每次间隔一秒钟
//            while (RDSConstant.RUNNING.equals(taskStatus.getStatus()) && countCheck > 0) {
//                taskStatus = strategyService.taskStatus(precheckResponse.getTaskId());
//                countCheck--;
//                try {
//                    // 检查操作等待一秒钟
//                    Thread.sleep(1000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                    logger.error("precheck sleep 1 second failed.");
//                }
//            }
//
//            if (RDSConstant.FINISHED.equals(taskStatus.getStatus())) {
//                CheckStatusResponse topoCheck = taskStatus.getData().getTopoCheck();
//                CheckStatusResponse delayCheck = taskStatus.getData().getDelayCheck();
//                CheckStatusResponse trxCheck = taskStatus.getData().getTrxCheck();
//                // 如果拓扑检查失败的话，不可以进行强制切换，接口直接提示报错即可
//                if (topoCheck.getCode() == 400 || topoCheck.getCode() == 500) {
//                    logger.error("topo check failed.");
//                    throw new RDSExceptions.TopoCheckException("topo check failed.");
//                }
//                // 三项检查都通过时，可以进行正常的主备切换。否则就需要进行强制切换
//                if (topoCheck.getCode() == 200 && delayCheck.getCode() == 200 && trxCheck.getCode() == 200) {
//                    clientFactory.createRdsClient().switchMasterBackup(instanceId, request);
//                } else {
//                    request.setExchangeType(1);
//                    clientFactory.createRdsClient().switchMasterBackup(instanceId, request);
//                }
//            } else {
//                // 15s 内前置检查若还没有完成，默认进行强制切换。此情况适用于管控前置检查接口超时情况
//                request.setExchangeType(1);
//                clientFactory.createRdsClient().switchMasterBackup(instanceId, request);
//            }
//
//        } else {
//            // 控制台操作直接进行主备切换
//            clientFactory.createRdsClient().switchMasterBackup(instanceId, request);
//        }

        clientFactory.createRdsClient().switchMasterBackup(instanceId, request);

    }


    public void changeTagsToInstance(String instanceId, List<Tag> tags, TagsChangeType changeType, Boolean relationTag) {

        // 判断实例是否存在
        InstancePO instancePO = instanceDao.queryInstanceByInstanceUuid(instanceId, clientFactory.getAccountId());
        if (instancePO == null) {
            throw new RDSExceptions.ResourceNotExistException();
        }

        // 默认false
        if (relationTag == null) {
            relationTag = Boolean.FALSE;
        }

        if (StringUtils.isBlank(instanceId)) {
            logger.error("InstanceId is empty. Can't bind tags");
        }

        List<String> resourceUuids = new ArrayList<>();

        resourceUuids.add(instanceId);

        Map<String, List<Tag>> tagMap = rdsTagLogicalService.queryTagsByResourceUuids(resourceUuids);

        // 取出旧tag
        List<Tag> oldTags = tagMap.get(instanceId) == null ? new ArrayList<Tag>() : tagMap.get(instanceId);

        List<Tag> result = new ArrayList<Tag>();

        if (ObjectUtils.equals(changeType, TagsChangeType.UNBIND)) {

            // 此时不需要再解绑
            if (oldTags.size() == 0) {
                logger.info("InstanceId tags is empty. Can't unbind tags");
                return;
            }

            Map<String ,Tag> removeTags = new HashMap<String ,Tag>();
            // 首先准备一个map，用来remove
            for (Tag eachRemoveTag : tags) {
                removeTags.put(eachRemoveTag.getTagKey(), eachRemoveTag);
            }

            // remove操作，使用新建list的方式
            for (Tag eachOldTag : oldTags) {
                // map 不存在的才往result里加
                if (removeTags.get(eachOldTag.getTagKey()) == null) {
                    result.add(eachOldTag);
                }
            }
        } else if (ObjectUtils.equals(changeType, TagsChangeType.BIND)) {
            // 存在绑定标签
            if (oldTags.size() > 0) {
                List<Tag> mergeTags = new ArrayList<Tag>();

                mergeTags.addAll(oldTags);
                mergeTags.addAll(tags);

                Map<String ,Tag> tagsMap = new HashMap<String, Tag>();

                for (Tag each : mergeTags) {
                    // 对于相同的key，后面的Tag会覆盖前面的，达到更新的目的
                    tagsMap.put(each.getTagKey(), each);
                }
                result = new ArrayList<Tag>(tagsMap.values());
            } else {
                // 初始情况，直接等于tags
                result = tags;
            }
        }

        AssignTagRequest request = new AssignTagRequest();
        request.setRelationTag(relationTag);

        List<LogicalAssignResource> resources = new ArrayList<LogicalAssignResource>();
        LogicalAssignResource resource = new LogicalAssignResource();
        resource.setServiceType(getServiceTypeByInstanceType(instancePO.getInstanceType()));
        resource.setInstanceId(instanceId);
        resource.setTags(result);
        resources.add(resource);
        request.setResources(resources);
        othersService.assignTags(request);
    }

    public String getServiceTypeByInstanceType(String instanceType) {

        if (instanceType == null) {
            return ServiceType.RDS.getName();
        }

        if (ObjectUtils.equals("readreplica", instanceType.toLowerCase())) {
            return ServiceType.RDS_REPLICA.getName();
        } else if (ObjectUtils.equals("rdsproxy", instanceType.toLowerCase())) {
            return ServiceType.RDS_PROXY.getName();
        } else {
            return ServiceType.RDS.getName();
        }

    }

    public InstancePnetIpResponse getPnetIp(String instanceId) {
        return clientFactory.createRdsClient().pnetIp(instanceId);
    }


    public void openTde(String instanceId, InstanceOpenTdeRequest request) {
        clientFactory.createRdsClient().openTde(instanceId, request);
    }

    public InstanceTdeStatusResponse checkTde(String instanceId) {
        return clientFactory.createRdsClient().checkTde(instanceId);
    }

    public StatusResponse checkFdisk(String instanceId){
        return clientFactory.createRdsClient().checkFdisk(instanceId);
    }

    public void azoneMigration(String instanceId, AzoneInfo request) {
        precheckResourceAzChange(instanceId, request);
        clientFactory.createRdsClient().azoneMigration(instanceId, request);
    }

    public UserKmsListResponse getKmsList() {

        KmsClient client = clientFactory.createKmsClient();

        // TODO
        UserKmsListResponse res = new UserKmsListResponse();

        ListKeysRequest req = new ListKeysRequest();
        req.setLimit(100);
        try {
            ListKeysResponse listKeysResponse = client.listKeys(req);

            List<ListKeysResponse.Key> keys = listKeysResponse.getKeys();

            List kmsIds = new ArrayList();
            for (ListKeysResponse.Key eachKey : keys) {
                kmsIds.add(eachKey.getKeyId());
            }
            res.setKmsIds(kmsIds);
            return res;
        } catch (Exception e) {
            logger.error("query kms error. ", e);
            List kmsIds = new ArrayList();
            kmsIds.add("92ce97be-21b5-87a1-af45-823f9621f802");
            res.setKmsIds(kmsIds);
            return res;
        }
    }

    public UserKmsListResponse getKmsListV2() {

        KmsClient client = clientFactory.createKmsClientV2();

        // TODO
        UserKmsListResponse res = new UserKmsListResponse();

        ListKeysRequest req = new ListKeysRequest();
        req.setLimit(100);
        try {
            ListKeysResponse listKeysResponse = client.listKeys(req);

            List<ListKeysResponse.Key> keys = listKeysResponse.getKeys();

            List kmsIds = new ArrayList();
            for (ListKeysResponse.Key eachKey : keys) {
                kmsIds.add(eachKey.getKeyId());
            }
            res.setKmsIds(kmsIds);
            return res;
        } catch (Exception e) {
            logger.error("query kms error. ", e);
            List kmsIds = new ArrayList();
            kmsIds.add("92ce97be-21b5-87a1-af45-823f9621f802");
            res.setKmsIds(kmsIds);
            return res;
        }
    }

    public UserKmsListResponse getKmsListV3() {

        KmsClient client = clientFactory.createKmsClientV3();

        // TODO
        UserKmsListResponse res = new UserKmsListResponse();

        ListKeysRequest req = new ListKeysRequest();
        req.setLimit(100);
        try {
            ListKeysResponse listKeysResponse = client.listKeys(req);

            List<ListKeysResponse.Key> keys = listKeysResponse.getKeys();

            List kmsIds = new ArrayList();
            for (ListKeysResponse.Key eachKey : keys) {
                kmsIds.add(eachKey.getKeyId());
            }
            res.setKmsIds(kmsIds);
            return res;
        } catch (Exception e) {
            logger.error("query kms error. ", e);
            List kmsIds = new ArrayList();
            kmsIds.add("92ce97be-21b5-87a1-af45-823f9621f802");
            res.setKmsIds(kmsIds);
            return res;
        }
    }

    public UserKmsListResponse getBceKmsList() {

        BceKmsClient client = clientFactory.createBceKmsClient();

        // TODO
        UserKmsListResponse res = new UserKmsListResponse();

        ListKeysRequest req = new ListKeysRequest();
        req.setLimit(100);

        logger.error("query bce kms. ");
        client.keyList();
        return res;

    }

    public void recoveryToSourceInstanceBySnapshot(String instanceId,
                                                   RecoveryToSourceInstanceRequest request, String from) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = findInsntaceUUidByShortId(instanceId);
        }
        // 前置拦截：不可选取逻辑备份集进行库表恢复
        SnapshotGetResponse snapshotGetResponse = clientFactory.createRdsClient2().
                snapshotGet(instanceId, request.getSnapshotId(), 43200);
        if (StringUtils.isNotEmpty(snapshotGetResponse.getSnapshot().getDataBackupType()) &&
                RDSConstant.BACK_UP_TYPE_LOGICAL.equalsIgnoreCase
                        (snapshotGetResponse.getSnapshot().getDataBackupType())) {
            throw new RDSBusinessExceptions.BackupParameterException();
        }
        clientFactory.createRdsClient().recoveryToSourceInstanceBySnapshot(instanceId, request);
    }

    public void recoveryToSourceInstanceByTime(String instanceId,
                                               RecoveryToSourceInstanceRequest request, String from) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = findInsntaceUUidByShortId(instanceId);
        }
        clientFactory.createRdsClient().recoveryToSourceInstanceByTime(instanceId, request);
    }


    public List<InstanceAbstract> listGlobalInstanceByExpiredDate(int days,
                                                                  String order,
                                                                  String orderBy,
                                                                  Map<String, String> filterMap,
                                                                  String serviceType,
                                                                  boolean forPolicy, String region) {
        List<InstanceAbstract> instances = null;
        try {
            RDSClient rdsClient = clientFactory.createRdsClient();
            InstanceListResponse instancesSource = rdsClient.instanceList(null);

            if ("fsh".equals(regionConfiguration.getCurrentRegion())) {
                RDSClient rdsClientFsh = clientFactory.createRdsClient("RDS-fshv2");
                InstanceListResponse instanceListFsh = rdsClientFsh.instanceList(null);
                if (instanceListFsh != null && !"".equals(instanceListFsh)) {
                    instancesSource.getInstances().addAll(instanceListFsh.getInstances());
                }
            }

            if ("fwh".equals(regionConfiguration.getCurrentRegion())) {
                RDSClient rdsClientFsh = clientFactory.createRdsClient("RDS-fwhv2");
                InstanceListResponse instanceListFwh = rdsClientFsh.instanceList(null);
                if (instanceListFwh != null && !"".equals(instanceListFwh)) {
                    instancesSource.getInstances().addAll(instanceListFwh.getInstances());
                }
            }
            Resources resources = this.getGlobalResourceList(region);

            // bind Tag info to instance
            instances = bindTagsToInstance(instancesSource.getInstances());

//            if (!clientFactory.isRoot()) {
//                instances = getInstancesBySubuser(days * ONE_DAY_MILLISECOND,
//                        resources, instances, filterMap, order, orderBy, serviceType);
//            } else {
//                instances = getInstanceAbstracts(days * ONE_DAY_MILLISECOND,
//                        resources, instances, filterMap, order, orderBy, serviceType);
//            }
            instances = getInstanceAbstracts(days * ONE_DAY_MILLISECOND,
                    resources, instances, filterMap, order, orderBy, serviceType, forPolicy, null);
            // 查询并绑定计费变更类型到实例（orderStatus）
            bindOrderStatus(instances);
            // physicalZone to logicalZone
            for (InstanceAbstract instanceAbstract : instances) {
//                instanceAbstract.setAzone(getLogicalZoneByPhysicalZone(instanceAbstract.getAzone()));
                if (instanceAbstract.getAzone() == null || instanceAbstract.getAzone().equals("default")) {
                    instanceAbstract.setAzone("zoneA");
                }
                if (instanceAbstract.getAzone().contains("+")) {
                    instanceAbstract.setAzone(instanceAbstract.getAzone().replaceAll("\\+", ","));
                }
            }

        } catch (BceInternalResponseException ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
//        List<InstanceAbstract> innns = reorderInstanceList(instances);
//        for (InstanceAbstract abs : innns) {
//            logger.info("InstanceAbstracts(reorderInstanceList): instanceId:{},instanceUUid:{}, expireDate:{} ",
//                    abs.getInstanceShortId(), abs.getInstanceId(), abs.getExpireDate());
//
//        }
        // 查询并绑定自动续费到实例（task）
        bindAutoRenew(instances);

        return instances;
    }

    public Resources getGlobalResourceList(String region) {
        Resources resources;
        try {
            GetResourcesRequest resourcesRequest = new GetResourcesRequest();
            resourcesRequest.setAccountId(clientFactory.getAccountId());
            resourcesRequest.setServiceType(ServiceType.RDS.toString());
            resourcesRequest.setRegion(null);
            resourcesRequest.setLimit(Integer.MAX_VALUE);
            ResourceClient resourceClient = clientFactory.createResourceClient();
            resources = resourceClient.list(resourcesRequest);
            resourcesRequest.setServiceType(ServiceType.RDS_REPLICA.toString());
            resources.addAll(resourceClient.list(resourcesRequest));
            resourcesRequest.setServiceType(ServiceType.RDS_PROXY.toString());
            resources.addAll(resourceClient.list(resourcesRequest));
            return resources;
        } catch (BceInternalResponseException ex) {
            logger.warn("RDS get resource list fail! {}", ex);
            throw new RDSExceptions.ResourceNotExistException();
        } catch (Exception ex) {
            logger.warn("RDS get resource list fail! {}", ex);
            throw new RDSExceptions.ResourceServerException();
        }
    }


    public List<Order> getOrders() {
        AdvancedOrderFilter orderFilter = new AdvancedOrderFilter();
        List<OrderStatus> statuses = new ArrayList<>();
        statuses.add(OrderStatus.EXPIRED);
        statuses.add(OrderStatus.CREATED);
        orderFilter.setAccountId(clientFactory.getAccountId());
        orderFilter.setStatusList(statuses);
        orderFilter.setServiceType("RDS");
        return (List) clientFactory.getOrderClient().advancedList(orderFilter).getOrders();
    }


    /**
     * 概览页
     * @return
     */
    public ViewPageResponse showOverviewPageAll(String region) {
        ViewPageResponse viewPageResponse = new ViewPageResponse();

        try {
            int instanceRunningCount = 0;
            int instanceWillExpireCount = 0;
            int instanceExpiredCount = 0;
            int instanceMysqlCount = 0;
            int instanceSqlServerCount = 0;
            int instancePGCount = 0;
            int postPayCount = 0;
            int prePayCount = 0;
            int totalCount = 0;

            for (String serviceName : RDSConstant.SERVICE_NAME_LIST) {
                try {
                    RDSLogicClient rdsClient = clientFactory.createRdsLogicClient(serviceName);
                    ViewPageResponse eachViewPageResponse = rdsClient.showOverviewPage();
                    if (eachViewPageResponse != null && !"".equals(eachViewPageResponse)) {
                        instanceRunningCount += eachViewPageResponse.getInstanceRunningCount();
                        instanceWillExpireCount += eachViewPageResponse.getInstanceWillExpireCount();
                        instanceExpiredCount += eachViewPageResponse.getInstanceExpiredCount();
                        instanceMysqlCount += eachViewPageResponse.getInstanceMysqlCount();
                        instanceSqlServerCount += eachViewPageResponse.getInstanceSqlServerCount();
                        instancePGCount += eachViewPageResponse.getInstancePGCount();
                        postPayCount += eachViewPageResponse.getPostPayCount();
                        totalCount += eachViewPageResponse.getInstanceTotalCount();
                        prePayCount += eachViewPageResponse.getPrePayCount();
                        List<InstanceRegion> instanceRegionList = eachViewPageResponse.getInstanceRegionList();
                        viewPageResponse.getInstanceRegionList().addAll(instanceRegionList);
                    }
                }catch (Exception e){
                    logger.error("show overviewPage info error", e);
                }
            }

            viewPageResponse.setInstanceRunningCount(instanceRunningCount);
            viewPageResponse.setInstanceExpiredCount(instanceExpiredCount);
            viewPageResponse.setInstanceWillExpireCount(instanceWillExpireCount);
            viewPageResponse.setInstanceMysqlCount(instanceMysqlCount);
            viewPageResponse.setInstanceSqlServerCount(instanceSqlServerCount);
            viewPageResponse.setInstancePGCount(instancePGCount);
            viewPageResponse.setPrePayCount(prePayCount);
            viewPageResponse.setPostPayCount(postPayCount);
            viewPageResponse.setInstanceTotalCount(totalCount);

        } catch (BceInternalResponseException ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return viewPageResponse;
    }



    public Collection<InstanceAbstract> standardshowOverview(Collection<InstanceAbstract> result,
                                                                     String from, Map<String, String> filterMap,
                                                                     boolean onlyFilterMaster) {
        List<String> longIds = new ArrayList<>();
        for (InstanceAbstract instance : result) {
            longIds.add(instance.getInstanceId());
            if (StringUtils.isNotEmpty(instance.getSourceInstanceId())) {
                longIds.add(instance.getSourceInstanceId());
            }
        }
        Map<String, String> idMap = idMapperService.idMapByLongId(longIds);
        Collection<InstanceAbstract> newInstanceList = new ArrayList<>();
        for (InstanceAbstract instance : result) {
            String shortId = idMap.get(instance.getInstanceId());
            if (StringUtils.isEmpty(shortId)) {
//                throw new RDSExceptions.ResourceServerException();
                logger.warn("api list can not find shortId,instanceUuid:{}", instance.getInstanceId());
                continue;
            }
            instance.setInstanceShortId(shortId);
//            if (instanceService.filterInstance(instance, filterMap, onlyFilterMaster)) {
//                continue;
//            }
            if (filterMap == null || filterMap.get("instanceType") == null
                    || !filterMap.get("instanceType").equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
                // 不含有instanceType = financial过滤，则不展示creating状态的raft 实例
                if (instance.getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
                    continue;
                }
            }
            newInstanceList.add(instance);
        }
        return newInstanceList;
    }


    /**
     * 概览页
     * @return
     */
    public ViewPageResponse showOverviewPage(String region) {
        List<InstanceAbstract> instances;
        instances = listGlobalInstanceByExpiredDate(
                -1, null, null, null, null, false, region);

        Map<String, String> filterMap = new HashMap<>();
        filterMap.put("instanceType", "financial");
        instances = (List<InstanceAbstract>) standardshowOverview(instances, RDSConstant.FROM_CONSOLE,
                filterMap, Boolean.TRUE);

        ViewPageResponse viewPageResponse = new ViewPageResponse();

        try {

            int instanceRunningCount = 0;
            int instanceWillExpireCount = 0;
            int instanceExpiredCount = 0;
            int instanceMysqlCount = 0;
            int instanceSqlServerCount = 0;
            int instancePGCount = 0;
            int postPayCount = 0;
            int prePayCount = 0;
            int instanceCreating = 0;

            Map<String, InstanceRegion> instanceRegionMap = new HashMap<>();

            for (InstanceAbstract instance : instances) {
                if (!ObjectUtils.equals("creating", instance.getInstanceStatus().toLowerCase())) {
                    if (instanceRegionMap.get(instance.getRegion()) == null
                            || "".equals(instanceRegionMap.get(instance.getRegion()))){
                        logger.debug("instanceRegion", instance.getRegion());
                        InstanceRegion instanceRegion = new InstanceRegion();
                        instanceRegion.setInstanceTotalCount(1);
                        instanceRegion.setRegion(instance.getRegion());
                        formatInstanceRegion(instance, instanceRegion);
                        instanceRegionMap.put(instance.getRegion(), instanceRegion);
                    } else {
                        logger.debug("instanceRegion", instance.getRegion());
                        InstanceRegion instanceRegion = instanceRegionMap.get(instance.getRegion());
                        int instanceRegionTotalCount = instanceRegion.getInstanceTotalCount();
                        instanceRegionTotalCount++;
                        instanceRegion.setInstanceTotalCount(instanceRegionTotalCount);
                        formatInstanceRegion(instance, instanceRegion);
                        instanceRegionMap.put(instance.getRegion(), instanceRegion);
                    }

                    // 只有预付费才有到期概念
                    if (ObjectUtils.equals("prepay", instance.getProductType().toLowerCase())) {
                        // 即将到期，为0表示今天到期
                        if (instance.getExpireDate() < 0) {
                            instanceExpiredCount++;
                        } else if (0 <= instance.getExpireDate() && instance.getExpireDate() < 7) {
                            instanceWillExpireCount++;
                        }
                        prePayCount++;
                    } else if (ObjectUtils.equals("postpay", instance.getProductType().toLowerCase())) {
                        postPayCount++;
                    }

                    // 引擎类型
                    String engineType = instance.getEngine().toLowerCase();
                    if (ObjectUtils.equals("mysql", engineType)) {
                        instanceMysqlCount++;
                    } else if (ObjectUtils.equals("sqlserver", engineType)) {
                        instanceSqlServerCount++;
                    } else if (ObjectUtils.equals("postgresql", engineType)) {
                        instancePGCount++;
                    }

                }

                if (ObjectUtils.equals("available", instance.getInstanceStatus().toLowerCase())) {
                    instanceRunningCount++;
                }
                if (ObjectUtils.equals("creating", instance.getInstanceStatus().toLowerCase())) {
                    instanceCreating++;
                }
            }
            // List<GroupInfo> groupInfoList = grouplistResponse.getGroups();
//            Set<String> regionIdList = map.keySet();
//            for (String  id : regionIdList) {
//                respopnse.addStatisticInfo(RegionCountEnum.getRegionCount(id),
//                        String.valueOf(map.get(id)), RegionCountEnum.getRegion(id));
//            }
            List<InstanceRegion> instanceRegionList = new ArrayList(instanceRegionMap.values());
            viewPageResponse.setInstanceRunningCount(instanceRunningCount);
            viewPageResponse.setInstanceExpiredCount(instanceExpiredCount);
            viewPageResponse.setInstanceWillExpireCount(instanceWillExpireCount);
            viewPageResponse.setInstanceMysqlCount(instanceMysqlCount);
            viewPageResponse.setInstanceSqlServerCount(instanceSqlServerCount);
            viewPageResponse.setInstancePGCount(instancePGCount);
            viewPageResponse.setPrePayCount(prePayCount);
            viewPageResponse.setPostPayCount(postPayCount);
            viewPageResponse.setInstanceRegionList(instanceRegionList);
            viewPageResponse.setInstanceTotalCount(instances.size() - instanceCreating);

        } catch (Exception e) {
            logger.error("show overviewPage info error, use default info", e);
            LogicRdsExceptionHandler.handle(e);
        }
        logger.debug("viewPageResponse", viewPageResponse);
        return viewPageResponse;
    }


    public String findInsntaceUUidByShortId(String shortId) {
        return findInstanceIdByInstanceShortId(shortId);
    }

    private String findInstanceIdByInstanceShortId(String shortId) {
        InstancePO instancePO = instanceDao.queryInstanceByInstanceId(shortId, clientFactory.getAccountId());
        if (instancePO == null) {
            throw new RDSExceptions.ResourceNotExistException();
        }
        if (instancePO.getInstanceStatus().equalsIgnoreCase(RdsInstanceStatus.CREATING.getValue())) {
            throw new RDSExceptions.ResourceNotCreatedException();
        }

        return instancePO.getInstanceUuid();
    }

    public String findShortIdByInstanceUUid(String uuid) {
        String shortId = idMapperService.getInstanceId(uuid);
        if (StringUtils.isEmpty(shortId)) {
            throw new RDSExceptions.ResourceNotExistException();
        }

        return shortId;
    }


    private void formatInstanceRegion(InstanceAbstract instance, InstanceRegion instanceRegion) {
        if (ObjectUtils.equals("available", instance.getInstanceStatus().toLowerCase())) {
            int instanceRunningCount = instanceRegion.getInstanceRunningCount();
            instanceRunningCount++;
            instanceRegion.setInstanceRunningCount(instanceRunningCount);
        } else {
            logger.info("instanceStatus is not exist {}", instance.getInstanceStatus());
        }
        // 只有预付费才有到期概念
        if (ObjectUtils.equals("prepay", instance.getProductType().toLowerCase())) {
            // 即将到期，为0表示今天到期
            if (instance.getExpireDate() < 0) {
                int instanceExpiredCount = instanceRegion.getInstanceExpiredCount();
                instanceExpiredCount++;
                instanceRegion.setInstanceExpiredCount(instanceExpiredCount);
            } else if (0 <= instance.getExpireDate() && instance.getExpireDate() < 7) {
                int instanceWillExpireCount = instanceRegion.getInstanceWillExpireCount();
                instanceWillExpireCount++;
                instanceRegion.setInstanceWillExpireCount(instanceWillExpireCount);
            }
        }

        // 引擎类型
        String engineType = instance.getEngine().toLowerCase();
        if (ObjectUtils.equals("mysql", engineType)) {
            int instanceMysqlCount = instanceRegion.getInstanceMysqlCount();
            instanceMysqlCount++;
            instanceRegion.setInstanceMysqlCount(instanceMysqlCount);
        } else if (ObjectUtils.equals("sqlserver", engineType)) {
            int instanceSqlServerCount = instanceRegion.getInstanceSqlServerCount();
            instanceSqlServerCount++;
            instanceRegion.setInstanceSqlServerCount(instanceSqlServerCount);
        } else if (ObjectUtils.equals("postgresql", engineType)) {
            int instancePGCount = instanceRegion.getInstancePGCount();
            instancePGCount++;
            instanceRegion.setInstancePGCount(instancePGCount);
        }
    }


    public void setAutoRenewRules(List<Order> orderList, List<String> instanceIds, AutoRenew autoRenew) {

        if (StringUtils.equals(autoRenew.getAutoRenewTimeUnit(), "month")) {
            if (autoRenew.getAutoRenewTime() < 0 || autoRenew.getAutoRenewTime() > 9) {
                logger.error("auto renew params error 'autoRenewTime' : {}", autoRenew.getAutoRenewTime());
                throw new RDSExceptions.AutoRenewParamsValidationException();
            }
        } else if (StringUtils.equals(autoRenew.getAutoRenewTimeUnit(), "year")) {
            if (autoRenew.getAutoRenewTime() < 0 || autoRenew.getAutoRenewTime() > 3) {
                logger.error("auto renew params error 'autoRenewTime' : {}", autoRenew.getAutoRenewTime());
                throw new RDSExceptions.AutoRenewParamsValidationException();
            }
        } else {
            logger.error("auto renew params error 'autoRenewTimeUnit' : {}", autoRenew.getAutoRenewTimeUnit());
            throw new RDSExceptions.AutoRenewParamsValidationException();
        }
        List<AutoRenewCreateRequest> autoRenewCreateRequestList = new ArrayList<>();
        if (orderList != null && orderList.size() > 0) {
            for (Order order : orderList) {
                for (Order.Item orderItem : order.getItems()) {
                    AutoRenewCreateRequest autoRenewCreateRequestFromOrderItem
                            = getAutoRenewCreateRequestFromOrderItem(order, orderItem, autoRenew);
                    autoRenewCreateRequestList.add(autoRenewCreateRequestFromOrderItem);
                }
            }
        }
        if (autoRenewCreateRequestList != null && autoRenewCreateRequestList.size() > 0) {
            for (AutoRenewCreateRequest autoRenewCreateRequest : autoRenewCreateRequestList) {
                for (AutoRenewCreateRequest renewCreateRequest : autoRenewCreateRequestList) {
                    if (!autoRenewCreateRequest.getUuid().equals(renewCreateRequest.getUuid())) {
                        if (!autoRenewCreateRequest.getRegion().equals(renewCreateRequest.getRegion())
                                || !autoRenewCreateRequest.getAccountId().equals(renewCreateRequest.getAccountId())) {
                            throw new RDSExceptions.RegionOrCreaterException();
                        }
                    }
                }
            }
        }
        AutoRenewCreateRequest autoRenewCreateRequest = new AutoRenewCreateRequest();

        autoRenewCreateRequest.setRegion(autoRenewCreateRequestList.get(0).getRegion());
        autoRenewCreateRequest.setAccountId(orderList.get(0).getAccountId());
        autoRenewCreateRequest.setRenewTime(Integer.valueOf(autoRenew.getAutoRenewTime()));
        autoRenewCreateRequest.setRenewTimeUnit(autoRenew.getAutoRenewTimeUnit());
        autoRenewCreateRequest.setServiceType(autoRenewCreateRequestList.get(0).getServiceType());
        autoRenewCreateRequest.setServiceIds(instanceIds);
        AutoRenewClient autoRenewClient =
                        clientFactory.createAutoRenewClient(orderList.get(0).getAccountId());
        autoRenewClient.createAutoRenewRule(autoRenewCreateRequest);
//        for (Order.Item orderItem : order.getItems()) {
//            AutoRenewCreateRequest autoRenewCreateRequestFromOrderItem
//                    = getAutoRenewCreateRequestFromOrderItem(order, orderItem, autoRenew);
//            if (autoRenewCreateRequest != null) {
//                autoRenewCreateRequest.setServiceIds(instanceIds);
//                AutoRenewClient autoRenewClient =
//                        clientFactory.createAutoRenewClient(order.getAccountId());
//                if (autoRenewCreateRequest.getRenewTime() > 0
//                        && CollectionUtils.isNotEmpty(autoRenewCreateRequest.getServiceIds())) {
//                    autoRenewClient.createAutoRenewRule(autoRenewCreateRequest);
//                }
//            }
//        }

    }

    private AutoRenewCreateRequest getAutoRenewCreateRequestFromOrderItem(Order order, Order.Item orderItem,
                                                                          AutoRenew autoRenew) {
        AutoRenewCreateRequest autoRenewCreateRequest = new AutoRenewCreateRequest();
        if (StringUtils.isEmpty(orderItem.getExtra())) {
            return null;
        }
        if (ServiceType.RDS.getName().equalsIgnoreCase(orderItem.getServiceType())
                || ServiceType.RDS_REPLICA.getName().equalsIgnoreCase(orderItem.getServiceType())) {
            ObjectMapper objectMapper = new ObjectMapper();
            OrderItemExtraInfo orderItemExtraInfo;
            try {
                orderItemExtraInfo = objectMapper.readValue(orderItem.getExtra(), OrderItemExtraInfo.class);
            } catch (IOException ex) {
                logger.error("rds order error: deserialize configuration error " + ex.toString() + ex);
//                saveOrderExceptionToTrace(order, "[RDS OrderExecute] Parse orderItem extra failed", ex);
                return null;
            }
//            int autoRenewTime = orderItemExtraInfo.getAutoRenewTime();
//            String autoRenewTimeUnit = orderItemExtraInfo.getAutoRenewTimeUnit();
//            if (StringUtils.isNotEmpty(autoRenewTimeUnit) || autoRenewTime > 0) {
//                throw new RDSExceptions.AutoRenewParamException();
//            }
            autoRenewCreateRequest.setUuid(order.getUuid());
            autoRenewCreateRequest.setAccountId(order.getAccountId());
            autoRenewCreateRequest.setRegion(orderItem.getRegion());
            autoRenewCreateRequest.setServiceType(orderItem.getServiceType());
        }
        return autoRenewCreateRequest;
    }

    public OrderIdsRequest getOrderId(List<String> instanceIds, String from) {
        OrderIdsRequest orderIdsRequest = new OrderIdsRequest();
        List<InstancePO> instancePOList = null;
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            instancePOList = instanceDao.queryInstanceByInstanceIds(instanceIds, clientFactory.getAccountId());
            if (instancePOList == null || instancePOList.size() <= 0) {
                throw new RDSExceptions.ResourceNotExistException();
            }
        }
        List<String> instanceUUIds = new ArrayList<>();
        List<String> orderIds = new ArrayList<>();
        if (instancePOList != null && instancePOList.size() > 0) {
            for (InstancePO instancePO : instancePOList) {
                logger.debug("instanceId is {}", instancePO.getInstanceId());

                if (instancePO.getInstanceStatus().equalsIgnoreCase(RdsInstanceStatus.CREATING.getValue())) {
                    throw new RDSExceptions.AutoRenewStatusValidationException();
                }
                InstanceGetResponse instanceGetResponse = clientFactory
                        .createRdsClient2ByInstanceId(instancePO.getInstanceUuid())
                        .instanceDescribe(instancePO.getInstanceUuid());

                InstanceExtension instance = new InstanceExtension(instanceGetResponse.getInstance());
                Resource resource = getResourceByInstanceId(instancePO.getInstanceUuid());
                if (null == resource) {
                    logger.warn("rds instanceId: {} not in resources", instancePO.getInstanceUuid());
                    throw new RDSExceptions.ResourceNotExistException();
                }
                instance.setProductType(resource.getProductType());

                if (instance.getInstanceStatus().equalsIgnoreCase(RdsInstanceStatus.CREATING.getValue())) {
                    throw new RDSExceptions.ResourceNotCreatedException();
                }

                if (Payment.POSTPAY.getValue().equals(instance.getProductType())
                        || OrderType.TO_POSTPAY.equals(instance.getInstanceStatus())
                        || OrderType.TO_PREPAY.equals(instance.getInstanceStatus())) {
                    throw new RDSExceptions.AvailableInstanceException();
                }
                instanceUUIds.add(instance.getInstanceId());
                orderIds.add(resource.getOrderId());

            }
            orderIdsRequest.setInstanceIds(instanceUUIds);
            orderIdsRequest.setOrderIds(orderIds);
        }
        return orderIdsRequest;
    }

    public void syncInsertGroup(List<com.baidu.bce.plat.resource.manager.sdk.model.Resource> resourceList,
                                String syncType, String serviceType) {
        SyncResClient syncResClient = clientFactory.createSyncResClient();
        SyncLockClient lock = clientFactory.createSyncLockClient();
        String region = regionConfiguration.getCurrentRegion();
        if (CollectionUtils.isNotEmpty(resourceList)) {
            logger.debug("get instance resourece resourceList is {}", resourceList.size());
            if (resourceList.size() <= RDSConstant.SYNC_RES_MAX_SIZE) {
                rdsAsyncService.syncResource(syncResClient, lock, syncType, serviceType, resourceList,
                        BceInternalRequest.getThreadRequestId(), region);
            } else {
                int size = resourceList.size();
                int index = 1000;
                for (int i = 0; i < size; i += 1000) {
                    if (i + 1000 > size) {
                        index = size - i;
                    }
                    final List<com.baidu.bce.plat.resource.manager.sdk.model.Resource> newResourceList
                            = resourceList.subList(i, i + index);
                    rdsAsyncService.syncResource(syncResClient, lock, syncType, serviceType, newResourceList,
                            BceInternalRequest.getThreadRequestId(), region);
                }
            }
        }
    }

    public void changeResource(List<Instance> instanceList, String syncType, String accountId) {

        List<com.baidu.bce.plat.resource.manager.sdk.model.Resource> resourceMasterList = new ArrayList<>();
        List<com.baidu.bce.plat.resource.manager.sdk.model.Resource> resourceProxyList = new ArrayList<>();
        List<com.baidu.bce.plat.resource.manager.sdk.model.Resource> resourceReadReplicaList = new ArrayList<>();
        for (Instance instance : instanceList) {
            com.baidu.bce.plat.resource.manager.sdk.model.Resource resource
                    = new com.baidu.bce.plat.resource.manager.sdk.model.Resource();
            if (StringUtils.isNotBlank(accountId)) {
                resource.setAccountId(accountId);
                resource.setUserId(accountId);
            } else {
                resource.setAccountId(instance.getUserId());
                resource.setUserId(instance.getUserId());
            }
            resource.setId(instance.getInstanceShortId());
            resource.setUuid(instance.getInstanceId());
            resource.setRegion(regionConfiguration.getCurrentRegion());
            resource.setName(instance.getInstanceName());
            String serviceType = getServiceTypeByInstanceType(instance.getInstanceType());
            resource.setType(serviceType);
            if (ServiceType.RDS.name().equalsIgnoreCase(serviceType)) {
                resourceMasterList.add(resource);
            } else if (ServiceType.RDS_REPLICA.name().equalsIgnoreCase(serviceType)) {
                resourceReadReplicaList.add(resource);
            } else if (ServiceType.RDS_PROXY.name().equalsIgnoreCase(serviceType)) {
                resourceProxyList.add(resource);
            }
        }
        if (CollectionUtils.isNotEmpty(resourceMasterList)) {
            syncInsertGroup(resourceMasterList, syncType, ServiceType.RDS.name());
        }
        if (CollectionUtils.isNotEmpty(resourceProxyList)) {
            syncInsertGroup(resourceProxyList, syncType, ServiceType.RDS_PROXY.name());
        }
        if (CollectionUtils.isNotEmpty(resourceReadReplicaList)) {
            syncInsertGroup(resourceReadReplicaList, syncType, ServiceType.RDS_REPLICA.name());
        }
    }

    public void updateInstancePort(String instanceId, InstanceUpdatePortRequest request) {
        clientFactory.createRdsClient().instanceModifyPort(instanceId, request);
    }

    public InstanceCheckPortResponse instanceEnrtyCheck(String instanceId) {
        RDSClient rdsClient = clientFactory.createRdsClient();
        return rdsClient.instanceEnrtyCheck(instanceId);
    }

    private void unitPrice(InstanceExtension instanceExtension){
        if (instanceExtension.getOrderId() == null) {
            throw new RDSExceptions.InvalidOrderException();
        }
        Order order = clientFactory.getOrderClient().get(instanceExtension.getOrderId());
        if (order == null || CollectionUtils.isEmpty(order.getItems())) {
            throw new RDSExceptions.InvalidOrderException();
        }
        InstanceCreateModel request = new InstanceCreateModel();
        InstanceCreateModel.DashCreateInstance instance = new InstanceCreateModel.DashCreateInstance();
        instance.setAllocatedMemoryInGB((int) instanceExtension.getAllocatedMemoryInGB());
        instance.setAllocatedStorageInGB(instanceExtension.getAllocatedStorageInGB());
        instance.setAzone(instanceExtension.getAzone());
        instance.setCategory(instanceExtension.getCategory());
        instance.setCpuCount(instanceExtension.getCpuCount());
        instance.setDiskIoType(instanceExtension.getDiskIoType());
        if (instanceExtension.getEngine().equalsIgnoreCase("MySQL")){
            if (instanceExtension.getInstanceType().equalsIgnoreCase("master")){
                instance.setEngine(instanceExtension.getEngine());
            } else {
                instance.setEngine(instanceExtension.getInstanceType());
            }
        }else {
            instance.setEngine(instanceExtension.getEngine().toLowerCase());
        }

        instance.setEngineVersion(instanceExtension.getEngineVersion());
        instance.setSourceInstanceId(instanceExtension.getSourceInstanceId());
        instance.setNodeAmount(instanceExtension.getNodeAmount());
        request.setProductType(instanceExtension.getProductType());
        request.setNumber(1);
        request.setInstance(instance);
        List<Order.Item> items = order.getItems();
        Order.Item item = items.get(0);
        BigDecimal time = item.getTime();
        String timeUnit = item.getTimeUnit();
        if ("year".equalsIgnoreCase(timeUnit)) {
            // 年的情况下将时间转换成月
            time = time.multiply(new BigDecimal(12));
        }else if ("day".equalsIgnoreCase(timeUnit)) {
            time = time.divide(new BigDecimal(31), 10, BigDecimal.ROUND_UP).add(new BigDecimal(1));
        }
        if ("postpay".equals(instanceExtension.getProductType())) {
            request.setDuration(null);
        }else {
            request.setDuration(time.intValue());
        }
        Price price = pricingService.getPrice(request, null);
        NumberFormat numberFormat = NumberFormat.getInstance();
        if ("postpay".equals(instanceExtension.getProductType())) {
            int oneDayMinute = 24 * 60;
            BigDecimal result = price.getPrice().multiply(new BigDecimal(oneDayMinute));
            numberFormat.setGroupingUsed(false);
            numberFormat.setMaximumFractionDigits(10);
            String unitPrice = numberFormat.format(result) + "元/天";
            instanceExtension.setUnitPrice(unitPrice);
        }else {
            BigDecimal result = price.getPrice().divide(new BigDecimal(time.intValue()), 10, BigDecimal.ROUND_UP);
            numberFormat.setGroupingUsed(false);
            numberFormat.setMaximumFractionDigits(10);
            instanceExtension.setUnitPrice(numberFormat.format(result) + "元/月");
        }
    }

    public HotUpgradeResponse checkHotUpgrade(String instanceId, HotupgradeRequest request) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = findInsntaceUUidByShortId(instanceId);
        }
        if (instanceId.startsWith("rdsg")) {
            return clientFactory.createRdsClient().checkHotUpgrade(instanceId, request);
        } else {
            return clientFactory.createRdsClient2().checkHotUpgrade(instanceId, request);
        }
    }

    public void updateMaintaintime(String instanceId, MaintainDurationRequest request) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = findInsntaceUUidByShortId(instanceId);
        }
        clientFactory.createRdsClient().updateMaintaintime(instanceId, request);
    }

    public MaintaintimeTasks getMaintaintimeTasks(TaskRequest request) {
        // 此接口给后端的是短ID
//        String instanceId = request.getInstanceId();
//        if(StringUtils.isNotEmpty(instanceId)) {
//            if (BasisUtils.isShortId(instanceId)) {
//                instanceId = findInsntaceUUidByShortId(instanceId);
//            }
//            request.setInstanceId(instanceId);
//        }
        return clientFactory.createRdsClient().getMaintaintimeTasks(request);
    }

    public MaintaintimeTasksResponse getMaintaintimeTasksResponse(TaskRequest request) {
        MaintaintimeTasks tasks = getMaintaintimeTasks(request);
        return convertToMaintainTimesResponse(tasks);
    }
    // 将返回结果中的shortId转换成instanceId,appName转换为instanceName 去掉appId appName
    private MaintaintimeTasksResponse convertToMaintainTimesResponse(MaintaintimeTasks tasks) {
        String tasksStr = JSON.toJSONString(tasks);
        MaintaintimeTasksResponse maintaintimeTasksResponse = JSON.parseObject(tasksStr, MaintaintimeTasksResponse.class);
        return maintaintimeTasksResponse;
    }
    public void cancelMaintaintimeTask(String taskId) {
        clientFactory.createRdsClient().cancelMaintaintimeTask(taskId);
    }

    public void suspend(String instanceId) {
        clientFactory.createRdsClient().suspend(instanceId);
    }

    public void start(String instanceId) {
            clientFactory.createRdsClient().start(instanceId);
    }

    public InstanceChangeDiskTypeResponse changeDiskType(String instanceId, String targetDiskType) {
        return clientFactory.createRdsClient2().changeDiskType(instanceId, targetDiskType);
    }

    public InstancePrecheckParameterResponse precheckParameter(String instanceId,
                                                               InstancePrecheckParameterRequest request) {
        return clientFactory.createRdsClient2().precheckParameter(instanceId, request);
    }

    public OrderStatusResponse getOrderStatus(String orderId) {
        Order order = clientFactory.getOrderClient().get(orderId);
        OrderStatusResponse response = new OrderStatusResponse();
        response.setOrderId(orderId);
        response.setStatus(order.getStatus());
        return response;
    }

    public void packTags(List<InstanceAbstract> instances) {

        if (CollectionUtils.isEmpty(instances)) {
            return;
        }

        List<String> instanceIds = new ArrayList<>();
        for (InstanceAbstract instance : instances) {
            instanceIds.add(instance.getInstanceId());
        }

        Map<String , List<Tag>> tagsMap = rdsTagLogicalService.queryTagsByResourceUuids(instanceIds);

        for (InstanceAbstract instance : instances) {
            if (tagsMap.containsKey(instance.getInstanceId())) {
                instance.setTags(tagsMap.get(instance.getInstanceId()));
            }
        }
    }

    /**
     * 根据实例名称、实例id 过滤实例
     * @param instance
     * @param filters
     * @return
     */
    public boolean isFiltered(InstanceAbstract instance, List<ListRequest.Filter> filters) {

        if (CollectionUtils.isEmpty(filters)) {
            return false;
        }

        for (ListRequest.Filter filter : filters) {
            if (StringUtils.isEmpty(filter.getKeyword()) || StringUtils.isEmpty(filter.getKeywordType())){
                continue;
            }

            if (filter.getKeywordType().equalsIgnoreCase(RDSConstant.FILTER_BY_INSTANCE_ID)) {
                if (!instance.getInstanceId().equalsIgnoreCase(filter.getKeyword())) {
                    logger.info("instance filtered by: " + filter.getKeywordType() + ", " + filter.getKeyword());
                    return true;
                }
            }
            if (filter.getKeywordType().equalsIgnoreCase(RDSConstant.FILTER_BY_INSTANCE_NAME)) {
                if (!instance.getInstanceName().equalsIgnoreCase(filter.getKeyword())) {
                    logger.info("instance filtered by: " + filter.getKeywordType() + ", " + filter.getKeyword());
                    return true;
                }
            }
            // 根据实例短id进行过滤
            if (filter.getKeywordType().equalsIgnoreCase(RDSConstant.FILTER_BY_INSTANCE_SHORT_ID)) {
                if (!instance.getInstanceShortId().equalsIgnoreCase(filter.getKeyword())) {
                    logger.info("instance filtered by: " + filter.getKeywordType() + ", " + filter.getKeyword());
                    return true;
                }
            }

        }
        return false;
    }


    public void precheckResourceCreate(InstanceCreateModel request) {
        if (regionConfiguration.getCurrentRegion().equals(RDSConstant.REGION_EDGE)) {
            return;
        }
        InstanceCreateModel.DashCreateInstance config = request.getInstance();
        PrecheckResourceCreateRequest req = new PrecheckResourceCreateRequest();
        CreateParams createParams = new CreateParams();
        req.setAction("create");
        req.setUserId(clientFactory.getAccountId());
        createParams.setDbType(config.getEngine());
        List<String> subnetList = new ArrayList<>();
        if (!StringUtils.isEmpty(config.getSubnetId())) {
            String[] subnetItems = config.getSubnetId().split(",");
            for (String item : subnetItems) {
                if (!item.trim().isEmpty()) {
                    String[] part = item.trim().split(":");
                    if (part.length == 2) {
                        subnetList.add(part[1]);
                    }
                }
            }
        }
        if (StringUtils.isEmpty(config.getSourceInstanceId())) {
            createParams.setType("master");
        } else if (config.getEngine().equals(RDSConstant.RDS_ENGINE_PROXY)) {
            createParams.setType("rdsproxy");
            createParams.setApplicationType("normal");
            createParams.setRdsproxySubnetIds(subnetList);
        } else {
            createParams.setType("readReplica");
            createParams.setSlaveSubnetId(subnetList.get(0));
        }
        if (RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(config.getEngine())
                || RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(config.getEngine())
                || RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(config.getEngine())) {
            if (RDSConstant.CATEGORY_STANDARD.equalsIgnoreCase(config.getCategory())
                    || StringUtils.isBlank(config.getCategory())) {
                createParams.setApplicationType("normal");
            } else {
                createParams.setApplicationType("single");
            }
        }

//        if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012.equalsIgnoreCase(config.getEngineVersion())
//                || RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2016.equalsIgnoreCase(config.getEngineVersion())) {
//            createParams.setApplicationType("single");
//        } else if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION.equals(config.getEngineVersion())
//                || RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012SP3.equals(config.getEngineVersion())
//                || RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016.equals(config.getEngineVersion())){
//            createParams.setApplicationType("normal");
//        }
        createParams.setInstanceCount(request.getNumber());
        if ((StringUtils.isEmpty(config.getSourceInstanceId())
                && RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(config.getEngine()))
                || RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(config.getEngine())
                || RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(config.getEngine())) {
            createParams.setMasterSubnetId(subnetList.get(0));
            if (RDSConstant.CATEGORY_STANDARD.equalsIgnoreCase(config.getCategory())
                    || StringUtils.isBlank(config.getCategory())) {
                if (subnetList.size() >= 2) {
                    createParams.setBackupSubnetId(subnetList.get(1));
                } else {
                    createParams.setBackupSubnetId(subnetList.get(0));
                }
            }
        }
//        if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012.equals(config.getEngineVersion())
//                || RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2016.equals(config.getEngineVersion())) {
//            createParams.setMasterSubnetId(subnetList.get(0));
//        }
//        if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION.equals(config.getEngineVersion())
//                || RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012SP3.equals(config.getEngineVersion())
//                || RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016.equals(config.getEngineVersion())) {
//            createParams.setMasterSubnetId(subnetList.get(0));
//            if (subnetList.size() >= 2) {
//                createParams.setBackupSubnetId(subnetList.get(1));
//            } else {
//                createParams.setBackupSubnetId(subnetList.get(0));
//            }
//        }
        if (config.getNodeAmount() != null) {
            createParams.setNodeAmount(config.getNodeAmount());
        }
        req.setCreateParams(createParams);
        PrecheckResourceCreateResponse response = clientFactory.createRdsClient2().precheckResourceCreate(req);
        if (response.getPass() == 0) {
//            throw new RDSExceptions.PrecheckResourceException();
            throw new RDSBusinessExceptions.SubnetPrecheckResourceException();
        }
    }
    public void precheckResourceResize(PriceDiffModel config, InstanceExtension instanceExtension) {
        // 若请求来自边缘地域或者明确指定了不需要前置检查，直接返回 TODO
        if (regionConfiguration.getCurrentRegion().equals(RDSConstant.REGION_EDGE)
                || !config.getNeedPrecheck()) {
            return;
        }
        List<String> subnetList = new ArrayList<>();
        if (StringUtils.isEmpty(config.getSubnetId())) {
            // 需要根据当前详情真的逻辑可用区 来判断需要对哪些子网进行前置检查
            String[] logicalZone = instanceExtension.getAzone().split(",");
            Map<String, String> avaiZone = new HashMap<>();
            for (String zone : logicalZone) {
                if (instanceExtension.getSubnetId().containsKey(zone)) {
                    String id = instanceExtension.getSubnetId().get(zone);
                    avaiZone.put(zone, id);
                }
            }
            config.setSubnetId(BasisUtils.mapConvertString(avaiZone));
        }

        if (!StringUtils.isEmpty(config.getSubnetId())) {
            String[] subnetItems = config.getSubnetId().split(",");
            for (String item : subnetItems) {
                if (!item.trim().isEmpty()) {
                    String[] part = item.trim().split(":");
                    if (part.length == 2) {
                        subnetList.add(part[1]);
                    }
                }
            }
        }
        PrecheckResourceCreateRequest req = new PrecheckResourceCreateRequest();
        ResizeParams resizeParams = new ResizeParams();
        req.setAction("resize");
        req.setUserId(clientFactory.getAccountId());
        resizeParams.setAppId(config.getInstanceId());
        // 单机转双机 TODO
        if (instanceExtension.getInstanceType().equalsIgnoreCase("master")) {
            resizeParams.setMasterSubnetId(subnetList.get(0));
            if (instanceExtension.getApplicationType().equalsIgnoreCase("normal")) {
                if (subnetList.size() >= 2) {
                    resizeParams.setBackupSubnetId(subnetList.get(1));
                } else {
                    resizeParams.setBackupSubnetId(subnetList.get(0));
                }
            }
        }
        if (instanceExtension.getInstanceType().equalsIgnoreCase("readReplica")) {
            resizeParams.setSlaveSubnetId(instanceExtension.getNodeReadReplica().getSubnetId());
        }
        if (instanceExtension.getInstanceType().equalsIgnoreCase("rdsproxy")) {
            resizeParams.setRdsproxySubnetIds(instanceExtension.getNodeProxy().getSubnetId());
        }

        if (config.getNodeAmount() != null) {
            resizeParams.setNodeAmount(config.getNodeAmount());
        }

        if (config.getCpuCount() != null) {
            resizeParams.setCpuCount(config.getCpuCount());
        }

        if (config.getAllocatedMemoryInGB() != null) {
            resizeParams.setAllocatedMemoryInMB(config.getAllocatedMemoryInGB() * 1024);
        }

        if (config.getAllocatedStorageInGB() != null) {
            resizeParams.setAllocatedStorageInGB(config.getAllocatedStorageInGB());
        }

        if (config.getForceHotUpgrade() != null) {
            resizeParams.setForceHotResize(config.getForceHotUpgrade());
        }
        req.setResizeParams(resizeParams);
        PrecheckResourceCreateResponse response = clientFactory.createRdsClient2().precheckResourceCreate(req);
        if (response.getPass() == 0) {
            throw new RDSBusinessExceptions.PrecheckResourceException();
        }
    }
    public void precheckResourceAzChange(String instanceId, AzoneInfo request) {
        PrecheckResourceCreateRequest req = new PrecheckResourceCreateRequest();
        AzChangeParams azChangeParams = new AzChangeParams();
        req.setAction("azChange");
        req.setUserId(clientFactory.getAccountId());
        azChangeParams.setAppId(instanceId);
        Map<String, String> subnetId = request.getInstanceParameters().getSubnetId();
        azChangeParams.setMasterSubnetId(subnetId.get(request.getInstanceParameters().getLogicalZoneMaster()));
        azChangeParams.setBackupSubnetId(subnetId.get(request.getInstanceParameters().getLogicalZoneBackup()));
        req.setAzChangeParams(azChangeParams);
        PrecheckResourceCreateResponse response = clientFactory.createRdsClient2().precheckResourceCreate(req);
        if (response.getPass() == 0) {
//            throw new RDSExceptions.PrecheckResourceException();
            throw new RDSBusinessExceptions.SubnetPrecheckResourceException();
        }
    }

    public PrecheckResourceCreateResponse precheckResource(PrecheckResourceCreateRequest request) {
        request.setUserId(clientFactory.getAccountId());
        return clientFactory.createRdsClient2().precheckResourceCreate(request);
    }

    public String getZoneUuid(List<String> zoneNames) {
        String azone = "";
        for (String zone : zoneNames) {
            String logicalZone = othersService.apiZoneTologicalZone(zone);
            azone = azone + logicalZone + "+";
        }
        azone = azone.substring(0, azone.length() - 1);
        return azone;
    }

    public Map<String, String> getSubnetUuid(List<InstanceAzone.SubnetMap> subnets) {
        Map<String, String> subnetId = new HashMap<>();
        for (InstanceAzone.SubnetMap subnetMap : subnets) {
            String logicalZone = othersService.apiZoneTologicalZone(subnetMap.getZoneName());
            SubnetVo subnetVo = clientFactory.createSubnetClient()
                    .findSubnetWithIpUsage(subnetMap.getSubnetId());
            if (subnetVo != null && subnetVo.getAz().equals(logicalZone)) {
                subnetId.put(logicalZone, subnetVo.getSubnetUuid());
            } else {
                throw new RDSExceptions.ParamValidationException("subnetId match zoneName error");
            }
        }
        return subnetId;
    }

    public ClusterStatusResponce clusterStatus(String instanceId) {
        // 校验引擎类型 实例类型
        InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(instanceId)
                .instanceDescribe(instanceId);
        if (!RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instanceGetResponse.getInstance().getEngine())) {
            throw new RDSBusinessExceptions.InstanceShrinkageEngineException();
        }
        if (RDSConstant.APPLICATION_TYPE_SINGLE.equalsIgnoreCase
                (instanceGetResponse.getInstance().getApplicationType())) {
            throw new RDSBusinessExceptions.ApplicationTypeException();
        }
        if (RDSConstant.INSTANCE_TYPE_REPLICA.equalsIgnoreCase(instanceGetResponse.getInstance().getInstanceType())
                && RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(instanceGetResponse.getInstance().getEngine())) {
            throw new RDSBusinessExceptions.InstanceShrinkageEngineException();
        }
        RDSClient2 rdsClient2 = clientFactory.createRdsClient2();
        return rdsClient2.clusterStatus(instanceId);
    }

    public ProbeInstanceResponse probeInstance(String instanceId) {
        return clientFactory.createRdsClient2().probeInstance(instanceId);

    }

    public BcmGroupResponses bcmGroupList(String serviceName,
                                          String region, Integer pageNo, Integer pageSize) {
        String userId = LogicUserService.getUserId();
        return clientFactory.createBcmClient().bcmGroupList(userId, serviceName, region, pageNo, pageSize);
    }

    public SlaInstanceResponse availableInstance(String instanceId) {
        // 当前代理实例不支持该操作
        InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(instanceId)
                .instanceDescribe(instanceId);
        if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getInstanceType())
                && RDSConstant.INSTANCE_TYPE_PROXY.equalsIgnoreCase
                (instanceGetResponse.getInstance().getInstanceType())) {
            throw new RDSBusinessExceptions.
                    InstanceTypeNotSupportException(instanceGetResponse.getInstance().getInstanceType());
        }
        return clientFactory.createRdsClient2().availableInstance(instanceId);
    }

    public void insertBccInstance(BccInstanceListRequest request) {
        // 校验请求参数中的bccId 与 instanceId 是否符合预期
        BccClient bccClient = clientFactory.createBccClient();
        // 取出RDS实例详情
        InstanceGetResponse instanceGetResponse = clientFactory.
                createRdsClient2ByInstanceId(request.getBccInstanceList().get(0).getInstanceId())
                .instanceDescribe(request.getBccInstanceList().get(0).getInstanceId());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date currentDate = new Date();
        // 格式化当前时间为指定格式的字符串
        String formattedDate = sdf.format(currentDate);
        if (!request.getBccInstanceList().isEmpty()) {
            for (BccInstanceListRequest.BccListRequest req : request.getBccInstanceList()) {
                BccInstanceDetail bccInstanceDetail = bccClient.bccDetail(req.getBccInstanceId());
                if (StringUtils.isNotEmpty(bccInstanceDetail.getInstance().getStatus())
                        && !"Running".equalsIgnoreCase(bccInstanceDetail.getInstance().getStatus())) {
                    throw new RDSBusinessExceptions.InstanceStatusErrorException();
                } else {
                    logger.debug("bcc interface test ok.");
                    InnerConnectCheckPO innerPO = new InnerConnectCheckPO();
                    innerPO.setRdsInstanceId(req.getInstanceId());
                    innerPO.setRdsInstanceStatus(instanceGetResponse.getInstance().getInstanceStatus());
                    innerPO.setRdsVpcId(instanceGetResponse.getInstance().getVpcId());
                    innerPO.setBccInstanceId(bccInstanceDetail.getInstance().getId());
                    innerPO.setBccInstanceName(bccInstanceDetail.getInstance().getName());
                    innerPO.setBccInstanceStatus(bccInstanceDetail.getInstance().getStatus());
                    innerPO.setBccVnetIp(bccInstanceDetail.getInstance().getInternalIp());
                    innerPO.setBccVpcId(req.getVpcId());
                    innerPO.setBccVpcName(req.getVpcName());
                    innerPO.setBccVpcCidr(req.getVpcCidr());
                    innerPO.setConnectId(UUID.randomUUID().toString().substring(0, 9));
                    innerPO.setConnectStatus("create");
                    innerPO.setCreateTime(Timestamp.valueOf(formattedDate));
                    innerPO.setUpdateTime(Timestamp.valueOf(formattedDate));
                    innerPO.setUserId(LogicUserService.getUserId());
                    innerPO.setBccInstanceUuid(req.getBccInstanceUuid());
                    // 数据入库
                    innerMapper.insertInnerConnect(innerPO);
                }
            }
        }
    }

    public BccConnectList innerCheckList(String instanceId, Integer pageNo, Integer pageSize) {
        // 根据 instanceId 查询内网连接列表
        List<InnerConnectCheckPO> innerConnectList = innerMapper.getInnerConnectList(instanceId, pageNo, pageSize);

        BccConnectList bccConnectList = new BccConnectList();
        List<BccConnectList.BccConnectMessage> bccList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (innerConnectList != null) {
            for(InnerConnectCheckPO checkPO : innerConnectList) {
                BccConnectList.BccConnectMessage connectMessage = new BccConnectList.BccConnectMessage();
                connectMessage.setBccInstanceId(checkPO.getBccInstanceId());
                connectMessage.setBccInstanceName(checkPO.getBccInstanceName());
                connectMessage.setBccVpcName(checkPO.getBccVpcName());
                connectMessage.setBccVpcCidr(checkPO.getBccVpcCidr());
                connectMessage.setBccVnetIp(checkPO.getBccVnetIp());
                connectMessage.setUpdateTime(sdf.format(checkPO.getUpdateTime()));
                connectMessage.setConnectId(checkPO.getConnectId());
                connectMessage.setConnectStatus(checkPO.getConnectStatus());
                connectMessage.setBccInstanceUuid(checkPO.getBccInstanceUuid());
                // 添加数据
                bccList.add(connectMessage);
            }
        } else {
            logger.error("InnerConnectList is empty.");
        }
        bccConnectList.setBccConnectList(bccList);
        return bccConnectList;
    }

    public BccInnerConnectDetail innerCheckDetail(String connectId) {
        BccInnerConnectDetail innerConnectDetail = new BccInnerConnectDetail();
        InnerConnectCheckPO innerDetail = innerMapper.getInnerDetail(connectId);
        BccClient bccClient = clientFactory.createBccClient();
        InstanceGetResponse instanceGetResponse = clientFactory.
                createRdsClient2ByInstanceId(innerDetail.getRdsInstanceId())
                .instanceDescribe(innerDetail.getRdsInstanceId());
        BccInstanceDetail bccInstanceDetail;
        try {
            bccInstanceDetail = bccClient.bccDetail(innerDetail.getBccInstanceId());
            if (StringUtils.isNotEmpty(bccInstanceDetail.getInstance().getStatus())) {
                innerConnectDetail.setBccInstanceStatus(bccInstanceDetail.getInstance().getStatus());
            }
        } catch (BceInternalResponseException e) {
            logger.error("bcc instance detail occurs error.");
            innerConnectDetail.setBccInstanceStatus("error");
        }
        // ORM映射
        if (StringUtils.isNotEmpty(innerDetail.getConnectStatus())) {
            innerConnectDetail.setConnectStatus(innerDetail.getConnectStatus());
        }
        innerConnectDetail.setRdsInstanceStatus(instanceGetResponse.getInstance().getInstanceStatus());

        innerConnectDetail.setIdenticalVpc(innerDetail.getIdenticalVpc());
        innerConnectDetail.setBccInWhiteList(innerDetail.getBccInWhiteList());

        return innerConnectDetail;
    }

    public void checkInnerConnect(String connectId) {
        // 进行内网连接检测 TODO
        String connectStatus = "checking";
        String identicalVpc = "checking";
        String bccInWhiteList = "checking";
        innerMapper.updateInnerConnectStatus(connectId, connectStatus, identicalVpc, bccInWhiteList);
        // 触发异步更新任务
        doCheckInnerConnect(connectId);
    }

    public void deleteInnerConnect(String connectId) {
        innerMapper.deleteConnect(connectId);
    }

    public void insertOuterIp(PublicNetworkRequest request) {
        // 取出RDS实例详情
        InstanceGetResponse instanceGetResponse = clientFactory.
                createRdsClient2ByInstanceId(request.getInstanceId())
                .instanceDescribe(request.getInstanceId());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date currentDate = new Date();
        // 格式化当前时间为指定格式的字符串
        String formattedDate = sdf.format(currentDate);
        if (CollectionUtils.isNotEmpty(request.getInetIp())) {
            for (String ip : request.getInetIp()) {
                PublicNetworkConnectCheckPO networkConnectCheckPO = new PublicNetworkConnectCheckPO();
                networkConnectCheckPO.setConnectId(UUID.randomUUID().toString().substring(0, 9));
                networkConnectCheckPO.setConnectStatus("create");
                networkConnectCheckPO.setRdsInstanceId(instanceGetResponse.getInstance().getInstanceId());
                networkConnectCheckPO.setRdsInstanceStatus(instanceGetResponse.getInstance().getInstanceStatus());
                networkConnectCheckPO.setInetIp(ip);
                networkConnectCheckPO.setBccInWhiteList("fail");
                networkConnectCheckPO.setInetStatus("fail");
                networkConnectCheckPO.setCreateTime(Timestamp.valueOf(formattedDate));
                networkConnectCheckPO.setUpdateTime(Timestamp.valueOf(formattedDate));
                networkConnectCheckPO.setUserId(LogicUserService.getUserId());
                innerMapper.insertPublicIp(networkConnectCheckPO);
            }
        }
    }

    public PublicNetworkConnectMessage outerCheckList(String instanceId, Integer pageNo, Integer pageSize) {
        PublicNetworkConnectMessage result = new PublicNetworkConnectMessage();
        List<PublicNetworkConnectMessage.InetConnectList> inetConnectLists = new ArrayList<>();

        List<PublicNetworkConnectCheckPO> outList = innerMapper.getOuterConnectList(instanceId, pageNo, pageSize);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (PublicNetworkConnectCheckPO outer : outList) {
            PublicNetworkConnectMessage.InetConnectList subDetail = new PublicNetworkConnectMessage.InetConnectList();
            subDetail.setConnectId(outer.getConnectId());
            subDetail.setConnectStatus(outer.getConnectStatus());
            subDetail.setInetIp(outer.getInetIp());
            subDetail.setUpdateTime(sdf.format(outer.getUpdateTime()));
            inetConnectLists.add(subDetail);
        }
        result.setInetConnectList(inetConnectLists);
        return result;


    }

    public PublicNetworkConnDetail outerCheckDetail(String connectId) {
        PublicNetworkConnectCheckPO mapperOuterDetail = innerMapper.getOuterDetail(connectId);
        InstanceGetResponse instanceGetResponse = clientFactory.
                createRdsClient2ByInstanceId(mapperOuterDetail.getRdsInstanceId())
                .instanceDescribe(mapperOuterDetail.getRdsInstanceId());
        PublicNetworkConnDetail result = new PublicNetworkConnDetail();
        // ORM
        if (mapperOuterDetail != null ) {
            result.setBccInWhiteList(mapperOuterDetail.getBccInWhiteList());
            result.setInetStatus(mapperOuterDetail.getInetStatus());
        }
        result.setRdsInstanceStatus(instanceGetResponse.getInstance().getInstanceStatus());
        return result;
    }

    public void checkOuterConnect(String connectId) {
        String connectStatus = "checking";
        String inetStatus = "checking";
        String bccInWhiteList = "checking";
        // 进行外网连接检测 TODO
        innerMapper.updateOuterConnectStatus(connectId, connectStatus, inetStatus, bccInWhiteList);
        // 触发外网连接检查
        doCheckOuterConnect(connectId);
    }

    public void deleteOuterConnect(String connectId) {
        innerMapper.deleteOuterConnect(connectId);
    }

    @Async
    private void doCheckInnerConnect(String connectId) {
        // 进行内网连接检测
        // 对比两个实例的 vpc 是否一致
        String connectStatus = "checking";
        String identicalVpc = "checking";
        String bccInWhiteList = "checking";
        boolean isVpc = false;
        boolean isWhite = false;
        InnerConnectCheckPO innerDetail = innerMapper.getInnerDetail(connectId);
        BccClient bccClient = clientFactory.createBccClient();
        try {
            BccInstanceDetail bccInstanceDetail = bccClient.bccDetail(innerDetail.getBccInstanceId());
            // 校验 bcc 是否还存活
            if (StringUtils.isNotEmpty(bccInstanceDetail.getInstance().getStatus())
                    && !"Running".equalsIgnoreCase(bccInstanceDetail.getInstance().getStatus())) {
                connectStatus = "fail";
                identicalVpc = "fail";
                bccInWhiteList = "fail";
                logger.error("bcc instance id: " + bccInstanceDetail.getInstance().getId() + ", status error : "
                        + bccInstanceDetail.getInstance().getStatus());
                innerMapper.updateInnerConnectStatus(connectId, connectStatus, identicalVpc, bccInWhiteList);
                return;
            }
        } catch (BceInternalResponseException e) {
            logger.error("get bcc instance detail occurs error.");
            connectStatus = "fail";
            identicalVpc = "fail";
            bccInWhiteList = "fail";
            innerMapper.updateInnerConnectStatus(connectId, connectStatus, identicalVpc, bccInWhiteList);
            return;
        }

        if (StringUtils.isNotEmpty(innerDetail.getBccVpcId())
                && StringUtils.isNotEmpty(innerDetail.getRdsVpcId())
                && innerDetail.getRdsVpcId().equalsIgnoreCase(innerDetail.getBccVpcId())) {
            identicalVpc = "pass";
            isVpc = true;
        } else {
            identicalVpc = "fail";
        }
        String rdsInstanceId = innerDetail.getRdsInstanceId();
        V2ListRequest requestBody = new V2ListRequest();
        requestBody.setInstanceId(rdsInstanceId);
        V2SecurityIpResponse v2SecurityIpResponse =
                clientFactory.createRdsClient2ByInstanceId(rdsInstanceId).v2QueryWhiteList(rdsInstanceId, requestBody);

        // 将实例现有的所有 ip 白名单均存放起来
        List<String> ipLists = new ArrayList<>();
        for (V2SecurityIps securityIp : v2SecurityIpResponse.getSecurityIps()) {
            ipLists.addAll(securityIp.getSecurityIps());
        }
        // 若存在cidr模式地址，则将 cidr 模式 转为 ip 模式，方便对比
        List<String> itemLists = new ArrayList<>();
        for (String ipItem : ipLists) {
            if (ipItem.contains("/")) {
                SubnetUtils subnetUtils = new SubnetUtils(ipItem);
                itemLists.addAll(Arrays.asList(subnetUtils.getInfo().getAllAddresses()));
            }
        }
        if (CollectionUtils.isNotEmpty(itemLists)) {
            ipLists.addAll(itemLists);
        }
        if (ipLists.contains("%") ||
                (StringUtils.isNotEmpty(innerDetail.getBccVnetIp()) && ipLists.contains(innerDetail.getBccVnetIp()))) {
            bccInWhiteList = "pass";
            isWhite = true;
        } else {
            bccInWhiteList = "fail";
        }
        // 更新连接检测结果
        if (isVpc && isWhite) {
            connectStatus = "pass";
        } else {
            connectStatus = "fail";
        }
        innerMapper.updateInnerConnectStatus(connectId, connectStatus, identicalVpc, bccInWhiteList);
    }

    @Async
    private void doCheckOuterConnect(String connectId) {
        // 进行内网连接检测
        // 对比两个实例的 vpc 是否一致
        String connectStatus = "checking";
        String inetStatus = "checking";
        String bccInWhiteList = "checking";
        boolean isVpc = false;
        boolean isWhite = false;
        PublicNetworkConnectCheckPO outerDetail = innerMapper.getOuterDetail(connectId);

        String rdsInstanceId = outerDetail.getRdsInstanceId();
        // 实例是否开启公网
        InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(rdsInstanceId)
                .instanceDescribe(rdsInstanceId);
        String eipStatus = instanceGetResponse.getInstance().getEipStatus();
        if (StringUtils.isNotEmpty(eipStatus) && !"closed".equalsIgnoreCase(eipStatus)) {
            inetStatus = "pass";
            isVpc = true;
        } else {
            inetStatus = "fail";
        }
        V2ListRequest requestBody = new V2ListRequest();
        requestBody.setInstanceId(rdsInstanceId);
        V2SecurityIpResponse v2SecurityIpResponse =
                clientFactory.createRdsClient2ByInstanceId(rdsInstanceId).v2QueryWhiteList(rdsInstanceId, requestBody);

        // 将实例现有的所有 ip 白名单均存放起来
        List<String> ipLists = new ArrayList<>();
        for (V2SecurityIps securityIp : v2SecurityIpResponse.getSecurityIps()) {
            ipLists.addAll(securityIp.getSecurityIps());
        }
        // 若存在cidr模式地址，则将 cidr 模式 转为 ip 模式，方便对比
        List<String> itemLists = new ArrayList<>();
        for (String ipItem : ipLists) {
            if (ipItem.contains("/")) {
                SubnetUtils subnetUtils = new SubnetUtils(ipItem);
                itemLists.addAll(Arrays.asList(subnetUtils.getInfo().getAllAddresses()));
            }
        }
        if (CollectionUtils.isNotEmpty(itemLists)) {
            ipLists.addAll(itemLists);
        }
        if (ipLists.contains("%") ||
                (StringUtils.isNotEmpty(outerDetail.getInetIp()) && ipLists.contains(outerDetail.getInetIp()))) {
            bccInWhiteList = "pass";
            isWhite = true;
        } else {
            bccInWhiteList = "fail";
        }

        if (isVpc && isWhite) {
            connectStatus = "pass";
        } else {
            connectStatus = "fail";
        }
        innerMapper.updateOuterConnectStatus(connectId, connectStatus, inetStatus, bccInWhiteList);
    }



    public void updateBasicBackupPolicy(BasicCategoryRequest request) {

        clientFactory.createRdsClient2().updateBasicBackupPolicy(request);
    }

    public void updateRetainBackupPolicy(RetainCategoryRequest request) {

        List<DataBackupRetainStrategy> retainStrategys = new ArrayList<>();
        // 阶梯备份时，保存每个阶梯的当前时间
        long preTime = 0;
        // 首先映射备份保留时长字段，即expireInDays
        DataBackupRetainStrategy item = new DataBackupRetainStrategy();
        item.setStartSeconds(preTime);
        item.setEndSeconds(preTime - RDSConstant.SECONDS_OF_ONE_DAY * request.getBackupPolicy().getExpireInDays());
        preTime = item.getEndSeconds();
        retainStrategys.add(item);
        logger.debug("retainStrategys.get(0).getStartSeconds() : {}, retainStrategys.get(0).getEndSeconds : {}",
                retainStrategys.get(0).getStartSeconds(), retainStrategys.get(0).getEndSeconds());
        // 阶梯备份时，参数校验与转化
        if (request.getBackupPolicy().getRetentionPeriod() != null
                && StringUtils.isNotEmpty(request.getBackupPolicy().getTimeUnit())) {
            logger.debug("policy.retentionPeriod is {}, policy.timeUnit is {}",
                    request.getBackupPolicy().getRetentionPeriod(), request.getBackupPolicy().getTimeUnit());
            // RDSConstant.TIME_UNITS.stream().anyMatch(item -> item.equalsIgnoreCase(policy.getTimeUnit()))
            if (RDSConstant.TIME_UNITS.contains(request.getBackupPolicy().getTimeUnit().toLowerCase())) {
                // 阶梯备份参数有问题时，默认按周处理
                long timeUnit = RDSConstant.TIME_UNIT_MONTH.
                        equalsIgnoreCase(request.getBackupPolicy().getTimeUnit()) ? 30 : 7;
                for (int i = 0; i < request.getBackupPolicy().getRetentionPeriod(); i++) {
                    DataBackupRetainStrategy req = new DataBackupRetainStrategy();
                    req.setStartSeconds(preTime);
                    req.setEndSeconds(preTime - RDSConstant.SECONDS_OF_ONE_DAY * timeUnit);
                    // 更新当前时间
                    preTime = req.getEndSeconds();
                    retainStrategys.add(req);
                    logger.debug("retainStrategys.get(i).getStartSeconds() : {}, retainStrategys.get(i).getEndSeconds : {}",
                            retainStrategys.get(i + 1).getStartSeconds(), retainStrategys.get(i + 1).getEndSeconds());
                }
            }
        }
        request.getBackupPolicy().setDataBackupRetainStrategys(retainStrategys);

        clientFactory.createRdsClient2().updateRetainBackupPolicy(request);

    }

    public void updateCopyRegionBackupPolicy(SplitCopyRegionPolicyRequest request) {
        // 若开启跨地域备份，则需数据处理 dataBackupCopyRetainDays -> dataBackupCopyStoragesRegions
        if (request.getBackupPolicy().getDataBackupCopyEnable()) {
            logger.info("handle update across region backup policy, dataBackupCopyRetainDays is : {}",
                    request.getBackupPolicy().getDataBackupCopyRetainDays());
            List<DataBackupRetainStrategy> retainCopyStrategys = new ArrayList<>();
            // 阶梯备份时，保存每个阶梯的当前时间
            long preCopyTime = 0;
            DataBackupRetainStrategy copyRegionItem = new DataBackupRetainStrategy();
            copyRegionItem.setStartSeconds(preCopyTime);
            copyRegionItem.setEndSeconds(preCopyTime - RDSConstant.SECONDS_OF_ONE_DAY
                    * request.getBackupPolicy().getDataBackupCopyRetainDays());
            preCopyTime = copyRegionItem.getEndSeconds();
            retainCopyStrategys.add(copyRegionItem);
            request.getBackupPolicy().setDataBackupCopyRetainStrategys(retainCopyStrategys);
        }

        clientFactory.createRdsClient2().updateCopyRegionBackupPolicy(request);
    }

    public void updateEncryptPolicy(UpdateEncryptPolicyReq request) {
        clientFactory.createRdsClient2().updateEncryptPolicy(request);
    }
    /**
     * 此方法用于返回概览页所需实例列表
     * @return
     */
    public GlobalInstanceResponses overviewList() {
        GlobalInstanceResponses globalInstanceResponses = new GlobalInstanceResponses();
        GlobalInstanceResponses result = new GlobalInstanceResponses();
        RDSClient rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_3);
        globalInstanceResponses = rdsClient.overviewList();
        if (globalInstanceResponses == null || globalInstanceResponses.getInstances() == null) {
            return globalInstanceResponses;
        }
        // 此处需根据实例信息查询资源信息
        Iterator<GlobalInstanceResponses.GlobalInstanceResponse> iterator
                = globalInstanceResponses.getInstances().iterator();
        Resources resourceList = getResourceList();
        Map<String, Integer> totalCount = new HashMap<>();
        Map<String, Integer> availableCount = new HashMap<>();
        Map<String, Integer> expiringSoonCount = new HashMap<>();
        Map<String, Integer> expiredCount = new HashMap<>();
        while (iterator.hasNext()) {
            GlobalInstanceResponses.GlobalInstanceResponse res = iterator.next();
            if (StringUtils.isNotEmpty(res.getInstanceStatus())
                    && RdsInstanceStatus.CREATING.getValue().equalsIgnoreCase(res.getInstanceStatus())) {
                // 概览页过滤掉创建中的实例
                continue;
            }
            // 对齐实例列表状态展示
            if (res.getInstanceStatus().equalsIgnoreCase("available")) {
                if (!res.getLockMode().equalsIgnoreCase("unlock")) {
                    res.setInstanceStatus(res.getLockMode());
                }
            }
            // jdk8 可以直接使用getOrDefault() 方法
//            totalCount.put(res.getRegion(), totalCount.getOrDefault(res.getRegion(), 0) + 1);
            // 填充全部实例数
            Integer count = totalCount.get(res.getRegion());
            if (count == null) {
                totalCount.put(res.getRegion(), 1);
            } else {
                totalCount.put(res.getRegion(), count + 1);
            }
            // 填充运行中实例数
            if (StringUtils.isNotEmpty(res.getInstanceStatus()) &&
                    RDSConstant.STATUS_AVAILABLE.equalsIgnoreCase(res.getInstanceStatus())) {
                Integer countAvai = availableCount.get(res.getRegion());
                if (countAvai == null) {
                    availableCount.put(res.getRegion(), 1);
                } else {
                    availableCount.put(res.getRegion(), countAvai + 1);
                }
            }
            // 填充已到期实例数
            if (StringUtils.isNotEmpty(res.getInstanceStatus()) &&
                    RDSConstant.RECYCLE_STATUS.contains(res.getInstanceStatus())) {
                Integer countExpired = expiredCount.get(res.getRegion());
                if (countExpired == null) {
                    expiredCount.put(res.getRegion(), 1);
                } else {
                    expiredCount.put(res.getRegion(), countExpired + 1);
                }
                continue;
            }
            Resource resource = null;
            for (Resource tmp : resourceList) {
                if (tmp.getName().equals(res.getInstanceId())) {
                    resource = tmp;
                    break;
                }
            }
            if (resource != null && "prepay".equalsIgnoreCase(resource.getProductType())
                    && resource.getExpireTime() != null) {
                res.setInstanceExpireTime(resource.getExpireTime());
                if (resource.getExpireTime() != null) {
                    // 8小时 = 8 × 3600000毫秒 = 28800000毫秒
                    long timeLast = 28800000 + resource.getExpireTime().getTime() - new Date().getTime();
                    res.setExpireDate((int) (timeLast / ONE_DAY_MILLISECOND));
                }
                if (res.getExpireDate() <= 7) {
                    Integer countExpiring = expiringSoonCount.get(res.getRegion());
                    if (countExpiring == null) {
                        expiringSoonCount.put(res.getRegion(), 1);
                    } else {
                        expiringSoonCount.put(res.getRegion(), countExpiring + 1);
                    }
                }
            }
        }
        globalInstanceResponses.setTotalCount(totalCount);
        globalInstanceResponses.setAvailableCount(availableCount);
        globalInstanceResponses.setExpiringSoonCoune(expiringSoonCount);
        globalInstanceResponses.setExpiredCount(expiredCount);
        return globalInstanceResponses;
    }

    public void updateMaintaintimeTasks(String taskId, UpdateTaskRequest request) {
        clientFactory.createRdsClient().updateMaintaintimeTasks(taskId, request);
    }

    public void updateLocalBinlogPolicy(String instanceId, UpdateLoaclPolicyRequest request) {
        clientFactory.createRdsClient2().updateLocalBinlogPolicy(instanceId, request);
    }

    public ProxyTopoInfo proxyTopoInfo(String instanceId) {
        return clientFactory.createRdsClient().proxyTopoInfo(instanceId);
    }
}
