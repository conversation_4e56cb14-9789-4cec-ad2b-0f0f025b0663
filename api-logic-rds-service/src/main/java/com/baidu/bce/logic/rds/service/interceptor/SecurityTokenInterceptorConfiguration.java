package com.baidu.bce.logic.rds.service.interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * 为了修复安全漏洞：http://wiki.baidu.com/pages/viewpage.action?pageId=1416450397
 */
@Configuration
public class SecurityTokenInterceptorConfiguration extends WebMvcConfigurerAdapter {

    @Value("${bce.logic.authentication.paths:/**}")
    private String tokenCheckPaths;

    @Value("${bce.logic.not.authentication.paths:}")
    private String tokenCheckPathsExcluded;

    @Autowired
    private SecurityTokenInterceptor securityTokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(securityTokenInterceptor)
                .addPathPatterns(tokenCheckPaths.split(";"))
                .excludePathPatterns(tokenCheckPathsExcluded.split(";"));
    }
}
