package com.baidu.bce.logic.rds.service;

import com.baidu.bce.externalsdk.logical.network.eni.EniExternalClient;
import com.baidu.bce.externalsdk.logical.network.eni.model.PrivateIp;
import com.baidu.bce.externalsdk.logical.network.eni.model.request.PrivateIpCheckRequest;
import com.baidu.bce.externalsdk.logical.network.subnet.ExternalSubnetClient;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.internalsdk.order.PricingClientV3;
import com.baidu.bce.internalsdk.order.PricingUtils;
import com.baidu.bce.internalsdk.order.model.*;
import com.baidu.bce.internalsdk.rds.AllPayPromotionClient;
import com.baidu.bce.logic.core.constants.Payment;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.service.model.instance.AllPayPriceResult;
import com.baidu.bce.logic.rds.service.model.instance.AllPayPriceResultResponse;
import com.baidu.bce.logic.rds.service.model.instance.BatchPriceAllPayRequest;
import com.baidu.bce.logic.rds.service.model.instance.BatchPriceRequest;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import com.baidu.bce.logic.rds.service.model.instance.PriceAllPayRequest;
import com.baidu.bce.logic.rds.service.util.CheckIp;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.model.pricing.Price;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.pricing.service.PricingQueryService;
import com.baidu.bce.pricing.service.model.common.FlavorItem;
import com.baidu.bce.pricing.service.model.common.Time;
import com.baidu.bce.pricing.service.model.common.Usage;
import com.baidu.bce.pricing.service.model.query.PricingQueryDiscountResponse;
import com.baidu.bce.pricing.service.model.query.RegularPricingQueryRequest;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Period;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Created by luping03 on 17/11/7.
 */
@Service
public class PricingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PricingService.class);

    @Autowired
    private LogicRdsClientFactory rdsClientFactory;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private InstanceService instanceService;

    public Price getPrice(InstanceCreateModel request, String from) {
//        String mutiEngine = request.getInstance().getEngineVersion()
//                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012) ? request.getInstance().getEngine()
//                + "_singleton" : request.getInstance().getEngine();
        if (request.getInstance().getEdgeRegion() != null && !request.getInstance().getEdgeRegion().isEmpty()) {
            request.setNumber(0);
            for (Map.Entry<String, Integer> entry : request.getInstance().getEdgeRegion().entrySet()) {
                request.setNumber(request.getNumber() + entry.getValue());
            }
        }
        LOGGER.info("get_price : reqeust {}", request);
        // 验证ip属不属于子网内
        boolean flag = false;
        String subnet = "";
        SubnetVo subnetVo = new SubnetVo();
        if (StringUtils.isNotBlank(request.getInstance().getSubnetId())){
            subnet = request.getInstance().getSubnetId();
        }
        if (StringUtils.isNotBlank(request.getInstance().getOvip())) {
            if (request.getInstance().getOvip().matches
                    ("([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])" +
                            "(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}")) {
                ExternalSubnetClient subnetClient = rdsClientFactory.createSubnetClientByUserId();

                if (subnet.contains(",")){
                    subnetVo=subnetClient.findSubnetWithIpUsage(
                            subnet.substring(subnet.indexOf(":") + 1 , subnet.indexOf(","))
                    );
                    if (CheckIp.isInRange(request
                            .getInstance().getOvip(), subnetVo.getCidr())) {
                        flag = true;
                    }
                }else{
                    subnetVo=subnetClient.findSubnetWithIpUsage(
                            subnet.substring(subnet.indexOf(":") + 1)
                    );
                    flag = CheckIp.isInRange(request
                            .getInstance().getOvip(), subnetVo.getCidr());
                }

                if (flag) {
                    EniExternalClient externalClient = rdsClientFactory.createEniExternalClient();
                    PrivateIpCheckRequest ipRequest = new PrivateIpCheckRequest();
                    List<PrivateIp> ips = new ArrayList<>();
                    PrivateIp ip = new PrivateIp();
                    ip.setPrivateIp(request.getInstance().getOvip());
                    ips.add(ip);
                    ipRequest.setSubnetId(subnetVo.getSubnetId());
                    ipRequest.setVpcId(request.getInstance().getVpcId());
                    ipRequest.setPrivateIps(ips);
                    if (!externalClient.validatedIpCheck(ipRequest).isIpValid()) {
                        //throw new RDSExceptions.ParamValidationException("ip not available");
                        throw new RDSExceptions.ParamValidationException("ip not available");
                    }
                }else{
                    throw new RDSExceptions.ParamValidationException("not belong to subnet");
                }
            }else{
                throw new RDSExceptions.ParamValidationException("ip format error");
            }
        }
        /*boolean flag = false;
        List<SubnetVo> subnetVo = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getInstance().getOvip())) {
            if (request.getInstance().getOvip().matches
                    ("([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])" +
                            "(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}")) {
                ExternalSubnetClient subnetClient = rdsClientFactory.createSubnetClientByUserId();

                if (request.getInstance().getAzone().contains(",")){
                    subnetVo = subnetClient
                            .findAllByAzAndVpc("zoneA"
                                    , request.getInstance().getVpcId());
                    if (CheckIp.isInRange(request
                            .getInstance().getOvip(), subnetVo.get(0).getCidr())) {
                        flag = true;
                    }
                    for (int i=0;i<=subnetVo.size();i++){
                        subnetVo.get(i).getCidr();
                    }
                }else{
                    subnetVo = subnetClient
                            .findAllByAzAndVpc(request.getInstance().getAzone()
                                    , request.getInstance().getVpcId());
                    flag = CheckIp.isInRange(request
                            .getInstance().getOvip(), subnetVo.get(0).getCidr());
                }

                if (flag) {
                    EniExternalClient externalClient = rdsClientFactory.createEniExternalClient();
                    PrivateIpCheckRequest ipRequest = new PrivateIpCheckRequest();
                    List<PrivateIp> ips = new ArrayList<>();
                    PrivateIp ip = new PrivateIp();
                    ip.setPrivateIp(request.getInstance().getOvip());
                    ips.add(ip);
                    ipRequest.setSubnetId(subnetVo.get(0).getSubnetId());
                    ipRequest.setVpcId(request.getInstance().getVpcId());
                    ipRequest.setPrivateIps(ips);
                    if (!externalClient.validatedIpCheck(ipRequest).isIpValid()) {
                        //throw new RDSExceptions.ParamValidationException("ip not available");
                        throw new RDSExceptions.ParamValidationException("ip not available");
                    }
                }else{
                    throw new RDSExceptions.ParamValidationException("not belong to subnet");
                }
            }else{
                throw new RDSExceptions.ParamValidationException("ip format error");
            }
        }*/
        // 如果为通用型实例询价，逻辑单独处理
        String mutiEngine = "";
        String subServiceTypeFlavorItemValue = "";
        if (StringUtils.isNotEmpty(request.getInstance().getResourceType()) &&
                RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(request.getInstance().getResourceType()) &&
                StringUtils.isNotEmpty(request.getInstance().getEngine()) &&
                RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(request.getInstance().getEngine())) {
            if ((StringUtils.isNotEmpty(request.getInstance().getCategory())  // 单机版的情况
                    && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON))) {
                // 此处单机版价格暂无，默认与原单机版保持一致，TODO
                subServiceTypeFlavorItemValue = request.getInstance().getEngine() + "_singleton";
                mutiEngine = request.getInstance().getEngine() + "_singleton";
            } else {
                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                mutiEngine = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
            }
        } else {
            if (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_SQLSERVER)
                    && StringUtils.isNotEmpty(request.getInstance().getCategory())  // 单机版的情况
                    && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // 处理sqlserver单机版的情况
                mutiEngine = request.getInstance().getEngine() + "_singleton";
            } else if ((request.getInstance().getIsEnhanced() != null
                    && request.getInstance().getIsEnhanced()) // 处理MySQL三节点增强版的情况
                    || (StringUtils.isNotEmpty(request.getInstance().getInstanceType())  // raft版的情况
                    && request.getInstance().getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT))) {
                mutiEngine = request.getInstance().getEngine().toLowerCase() + "_finance";
            } else if (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)
                    && StringUtils.isNotEmpty(request.getInstance().getCategory())  // 单机版的情况
                    && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
                // 这里没用RDSConstant.INSTANCE_TYPE_RDS_SINGLE
                mutiEngine = request.getInstance().getEngine() + "_singleton";
            } else if (request.getInstance().getEngine().equalsIgnoreCase((RDSConstant.RDS_ENGINE_PG))
                    && StringUtils.isNotEmpty(request.getInstance().getCategory()) // pg单机版情况
                    && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
                mutiEngine = request.getInstance().getEngine() + "_singleton";
            } else {
                mutiEngine = request.getInstance().getEngine();
            }
        }
        // 完全适配控制台逻辑
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            String engine = request.getInstance().getEngine().toLowerCase();

            switch (engine) {
                case RDSConstant.RDS_ENGINE_MYSQL_LOWER_CASE:
                    subServiceTypeFlavorItemValue = "MySQL";
                    break;
                case RDSConstant.RDS_ENGINE_PG:
                    subServiceTypeFlavorItemValue = "postgresql";
                    break;
                case RDSConstant.RDS_ENGINE_SQLSERVER:
                   subServiceTypeFlavorItemValue = "sqlserver";
                   break;
                case RDSConstant.INSTANCE_TYPE_REPLICA_LOWER_CASE:
                    if (StringUtils.isNotEmpty(request.getInstance().getCategory())
                            && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
                        //后付费 单机版 独占型与通用型价格一致
                        if (StringUtils.isNotEmpty(request.getProductType())
                                && Payment.isPostpay(request.getProductType())) {
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                        } else {
                            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                            request.getInstance().getResourceType())) {
                                // 预付费通用单机
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                            } else {
                                // 预付费独占单机 默认
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                            }
                        }
                    } else {
                        //后付费 双机版
                        if (StringUtils.isNotEmpty(request.getProductType())
                                && Payment.isPostpay(request.getProductType())) {
                            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                    request.getInstance().getResourceType())) {
                                // 通用型
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                            } else {
                                // 独占型 默认
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                            }
                        } else {
                            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                    request.getInstance().getResourceType())) {
                                // 预付费 通用
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                            } else {
                                // 预付费 独占 默认
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                            }
                        }
                    }
                    break;
                case RDSConstant.RDS_ENGINE_PROXY:
                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(request.getInstance().getResourceType())) {
                        // 通用型代理
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL;
                    } else {
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    }
                    break;
                default:
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    break;
            }
        }
        else {
            if (StringUtils.isEmpty(request.getInstance().getSourceInstanceId())) {
                // 当前为主实例，逻辑不变
                subServiceTypeFlavorItemValue = mutiEngine;
            } else if (StringUtils.isNotEmpty(request.getInstance().getReplicaType()) &&
                RDSConstant.REPLICA_TYPE_BASIC.equalsIgnoreCase(request.getInstance().getReplicaType())) {
                // 单机版只读
                if (StringUtils.isNotEmpty(request.getProductType())
                        && Payment.isPostpay(request.getProductType())) {
                    //后付费 单机版 独占型与通用型价格一致
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                } else {
                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                            request.getInstance().getResourceType())) {
                        // 预付费通用单机
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    } else {
                        // 预付费独占单机 默认
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    }
                }
            } else if (StringUtils.isNotEmpty(request.getInstance().getReplicaType()) &&
                    RDSConstant.REPLICA_TYPE_HA.equalsIgnoreCase(request.getInstance().getReplicaType())) {
                // 双机版只读
                //后付费 双机版
                if (StringUtils.isNotEmpty(request.getProductType())
                        && Payment.isPostpay(request.getProductType())) {
                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                            request.getInstance().getResourceType())) {
                        // 通用型
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                    } else {
                        // 独占型 默认
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                    }
                } else {
                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                            request.getInstance().getResourceType())) {
                        // 预付费 通用
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                    } else {
                        // 预付费 独占 默认
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                    }
                }
            } else {
                LOGGER.error("readReplica get price error, request.getInstance().getReplicaType() : {}",
                        request.getInstance().getReplicaType());
                subServiceTypeFlavorItemValue = "default";
            }
        }

        PricingQueryService pricingQueryService = rdsClientFactory.createNewPricingQueryClient();
        List<FlavorItem> flavorElements = new ArrayList<>();
        // 实例配置询价
        if (RDSConstant.RDS_ENGINE_PROXY.equalsIgnoreCase(request.getInstance().getEngine())) {
            FlavorItem nodeAmountChargeItem = new FlavorItem();
            nodeAmountChargeItem.setName("nodeAmount");
            nodeAmountChargeItem.setValue(request.getInstance().getNodeAmount().toString());
            nodeAmountChargeItem.setScale(new BigDecimal(1));
            flavorElements.add(nodeAmountChargeItem);
            // 代理实例支持通用型
            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(request.getInstance().getResourceType())) {
                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL;
                FlavorItem subServiceTypeChargeItem = new FlavorItem();
                subServiceTypeChargeItem.setName("subServiceType");
                subServiceTypeChargeItem.setValue(subServiceTypeFlavorItemValue);
                flavorElements.add(subServiceTypeChargeItem);
            }
        } else {
            if (ObjectUtils.equals("high_io", request.getInstance().getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
                // 通用ssd云磁盘新增计费项
            } else if (ObjectUtils.equals("cloud_nor", request.getInstance().getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", request.getInstance().getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
            } else if (ObjectUtils.equals("cloud_enha", request.getInstance().getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
            } else {
                flavorElements.add(new FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
            }

            if (request.getInstance().getOldFlavor()) {
                FlavorItem memoryChargeItem = new FlavorItem();
                memoryChargeItem.setName("memory");
                memoryChargeItem.setValue(request.getInstance().getAllocatedMemoryInMB() + "m");
                memoryChargeItem.setScale(new BigDecimal(1));
                flavorElements.add(memoryChargeItem);
            } else {
                FlavorItem cpuChargeItem = new FlavorItem();
                cpuChargeItem.setName("cpu");
                cpuChargeItem.setValue(request.getInstance().getCpuCount() + "");
                cpuChargeItem.setScale(new BigDecimal(1));
                flavorElements.add(cpuChargeItem);

                FlavorItem memoryChargeItem = new FlavorItem();
                memoryChargeItem.setName("memory");
                memoryChargeItem.setValue(request.getInstance().getAllocatedMemoryInGB() + "g");
                memoryChargeItem.setScale(new BigDecimal(1));
                flavorElements.add(memoryChargeItem);
            }
            FlavorItem subServiceTypeChargeItem = new FlavorItem();
            subServiceTypeChargeItem.setName("subServiceType");
            subServiceTypeChargeItem.setValue(subServiceTypeFlavorItemValue);
            flavorElements.add(subServiceTypeChargeItem);
        }
        com.baidu.bce.pricing.service.model.common.Flavor flavor
                = new com.baidu.bce.pricing.service.model.common.Flavor ();
        flavor.setFlavorItems(flavorElements);
        // 针对来自api 的询价请求进行特殊处理，注: 接口请求没有 sourceInstanceId
        // 鉴于之前对不同类型的引擎定义 大小写风格不统一，此处需考虑忽略大小写
        ServiceType serviceType;
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            if (StringUtils.isNotEmpty(request.getInstance().getEngine()) &&
                    ((request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)) ||
                    (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_SQLSERVER)) ||
                    (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PG)))) {
                serviceType = ServiceType.RDS;
            } else if (StringUtils.isNotEmpty(request.getInstance().getEngine()) &&
                    request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
                serviceType = ServiceType.RDS_PROXY;
                request.setProductType(ProductPayType.POST_PAY.toString());
            } else {
                serviceType = ServiceType.RDS_REPLICA;
                request.setProductType(ProductPayType.POST_PAY.toString());
            }
        } else {
            if (StringUtils.isEmpty(request.getInstance().getSourceInstanceId())) {
                serviceType = ServiceType.RDS;
            } else {
                if (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
                    serviceType = ServiceType.RDS_PROXY;
                    request.setProductType(ProductPayType.POST_PAY.toString());
                } else {
                    serviceType = ServiceType.RDS_REPLICA;
                }
            }
        }
        // 此处逻辑永远也不会进去 后续可以删掉 TODO
        if (StringUtils.isNotEmpty(request.getInstance().getInstanceId())) {
            Resource resource = getResourceByInstanceId(request.getInstance().getInstanceId());
            serviceType = ServiceType.valueOf(resource.getServiceType());
            request.setProductType(resource.getProductType());
        }

        Map<String, BigDecimal> price = getInstancePrice(request, pricingQueryService, flavor, serviceType, from);

        // 创建只读、代理实例时不需要对备份进行询价
        String backupPrice = "0";
        if (serviceType == ServiceType.RDS) {
            // 备份收费询价
            List<FlavorItem> backupItemList = new ArrayList<>();
            RegularPricingQueryRequest backupPriceRequest = new RegularPricingQueryRequest();
            backupPriceRequest.setAccountId(rdsClientFactory.getAccountId());
            backupPriceRequest.setRegion(regionConfiguration.getCurrentRegion());
            backupPriceRequest.setServiceType(serviceType.name());
            backupPriceRequest.setScene(RDSConstant.SCENE_NEW);
            backupPriceRequest.setUsage(new Usage().withAmount("1Gi"));
            backupPriceRequest.setCount(1);
            Period period = new Period("PT1M");
            Time time = new Time();
            time.setStartTime(DateTime.now());
            time.setPeriod(period);
            backupPriceRequest.setTime(time);
            com.baidu.bce.pricing.service.model.common.Flavor flavorBackup
                    = new com.baidu.bce.pricing.service.model.common.Flavor ();
            flavorBackup.setFlavorItems(backupItemList);
            backupPriceRequest.setFlavor(flavorBackup);
            if (StringUtils.isNotEmpty(request.getInstance().getDiskIoType()) &&
                    "normal_io".equals(request.getInstance().getDiskIoType())) {
                // 本地盘类型实例的备份询价
                backupPriceRequest.setChargeItemName("Backup");
                backupPriceRequest.setQueryTime(DateTime.now());
                backupItemList.add(new FlavorItem().withName("Backup").withValue(String.valueOf(1)));

            } else {
                // 云盘类型实例的备份询价
                backupPriceRequest.setChargeItemName("BackupToSnapshot");
                backupPriceRequest.setQueryTime(DateTime.now());
                backupItemList.add(new FlavorItem().withName("BackupToSnapshot").withValue(String.valueOf(1)));

            }

            backupPrice = pricingQueryService.getRegularPrice(backupPriceRequest).
                    getPrice().stripTrailingZeros().toPlainString();
            LOGGER.info("backup price : {}", backupPrice);

        }

        // 公网流量询价
        RegularPricingQueryRequest regularPricingRequest = new RegularPricingQueryRequest();
        regularPricingRequest.setAccountId(rdsClientFactory.getAccountId());
        regularPricingRequest.setRegion(regionConfiguration.getCurrentRegion());
        regularPricingRequest.setServiceType(serviceType.name());
        regularPricingRequest.setChargeItemName("WebOutBytes");
        regularPricingRequest.setScene(RDSConstant.SCENE_NEW);
        regularPricingRequest.setQueryTime(DateTime.now());
        Usage usage = new Usage();
        usage.setUsageAmount(String.valueOf(1024 * 1024 * 1024));
        regularPricingRequest.setUsage(usage);
        regularPricingRequest.setCount(request.getNumber());
        List<FlavorItem> chargeItemList = new ArrayList<>();
        chargeItemList.add(new FlavorItem().withName("WebOutBytes").withValue(String.valueOf(1024 * 1024 * 1024)));
        regularPricingRequest
                .setFlavor(new com.baidu.bce.pricing.service.model.common.Flavor().withFlavorItems(chargeItemList));
        BigDecimal traffic = pricingQueryService.getRegularPrice(regularPricingRequest).getPrice().stripTrailingZeros();
        return new Price().withPrice(price.get("price")).withCatalogPrice(price.get("catalogPrice"))
                .withDiscount(price.get("discount")).withRealCatalogPrice(price.get("realCatalogPrice"))
                .withTrafficInGB(traffic).withBackupInGB(backupPrice);
    }

    /**
     * 此v2服务专用于控制台，保证 API 用户使用方式不变，升级的询价接口满足
     *      1. 有权限看优惠的账户&用户，能看到优惠
     *      2. 没权限看优惠的账户&用户，智能看到目录价
     * @param request
     * @param from
     * @return
     */
    public Price getPriceV2(InstanceCreateModel request, String from) {

        if (request.getInstance().getEdgeRegion() != null && !request.getInstance().getEdgeRegion().isEmpty()) {
            request.setNumber(0);
            for (Map.Entry<String, Integer> entry : request.getInstance().getEdgeRegion().entrySet()) {
                request.setNumber(request.getNumber() + entry.getValue());
            }
        }
        LOGGER.info("get_price : reqeust {}", request);
        // 验证ip属不属于子网内
        boolean flag = false;
        String subnet = "";
        SubnetVo subnetVo = new SubnetVo();
        if (StringUtils.isNotBlank(request.getInstance().getSubnetId())){
            subnet = request.getInstance().getSubnetId();
        }
        if (StringUtils.isNotBlank(request.getInstance().getOvip())) {
            if (request.getInstance().getOvip().matches
                    ("([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])" +
                            "(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}")) {
                ExternalSubnetClient subnetClient = rdsClientFactory.createSubnetClientByUserId();

                if (subnet.contains(",")){
                    subnetVo=subnetClient.findSubnetWithIpUsage(
                            subnet.substring(subnet.indexOf(":") + 1 , subnet.indexOf(","))
                    );
                    if (CheckIp.isInRange(request
                            .getInstance().getOvip(), subnetVo.getCidr())) {
                        flag = true;
                    }
                }else{
                    subnetVo=subnetClient.findSubnetWithIpUsage(
                            subnet.substring(subnet.indexOf(":") + 1)
                    );
                    flag = CheckIp.isInRange(request
                            .getInstance().getOvip(), subnetVo.getCidr());
                }

                if (flag) {
                    EniExternalClient externalClient = rdsClientFactory.createEniExternalClient();
                    PrivateIpCheckRequest ipRequest = new PrivateIpCheckRequest();
                    List<PrivateIp> ips = new ArrayList<>();
                    PrivateIp ip = new PrivateIp();
                    ip.setPrivateIp(request.getInstance().getOvip());
                    ips.add(ip);
                    ipRequest.setSubnetId(subnetVo.getSubnetId());
                    ipRequest.setVpcId(request.getInstance().getVpcId());
                    ipRequest.setPrivateIps(ips);
                    if (!externalClient.validatedIpCheck(ipRequest).isIpValid()) {
                        //throw new RDSExceptions.ParamValidationException("ip not available");
                        throw new RDSExceptions.ParamValidationException("ip not available");
                    }
                }else{
                    throw new RDSExceptions.ParamValidationException("not belong to subnet");
                }
            }else{
                throw new RDSExceptions.ParamValidationException("ip format error");
            }
        }
        // 如果为通用型实例询价，逻辑单独处理
        String mutiEngine = "";
        String subServiceTypeFlavorItemValue = "";
        if (StringUtils.isNotEmpty(request.getInstance().getResourceType()) &&
                RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(request.getInstance().getResourceType()) &&
                StringUtils.isNotEmpty(request.getInstance().getEngine()) &&
                RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(request.getInstance().getEngine())) {
            if ((StringUtils.isNotEmpty(request.getInstance().getCategory())  // 单机版的情况
                    && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON))) {
                // 此处单机版价格暂无，默认与原单机版保持一致，TODO
                subServiceTypeFlavorItemValue = "MySQL_singleton";
                mutiEngine = "MySQL_singleton";
            } else {
                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                mutiEngine = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
            }
        } else {
            if (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_SQLSERVER)
                    && StringUtils.isNotEmpty(request.getInstance().getCategory())  // 单机版的情况
                    && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // 处理sqlserver单机版的情况
                mutiEngine = request.getInstance().getEngine().toLowerCase() + "_singleton";
                subServiceTypeFlavorItemValue = mutiEngine;
            } else if ((request.getInstance().getIsEnhanced() != null
                    && request.getInstance().getIsEnhanced()) // 处理MySQL三节点增强版的情况
                    || (StringUtils.isNotEmpty(request.getInstance().getInstanceType())  // raft版的情况
                    && request.getInstance().getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT))) {
                mutiEngine = request.getInstance().getEngine().toLowerCase() + "_finance";
                subServiceTypeFlavorItemValue = mutiEngine;
            } else if (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)
                    && StringUtils.isNotEmpty(request.getInstance().getCategory())  // 单机版的情况
                    && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
                // 这里没用RDSConstant.INSTANCE_TYPE_RDS_SINGLE
                mutiEngine = "MySQL_singleton";
                subServiceTypeFlavorItemValue = mutiEngine;
            } else if (request.getInstance().getEngine().equalsIgnoreCase((RDSConstant.RDS_ENGINE_PG))
                    && StringUtils.isNotEmpty(request.getInstance().getCategory()) // pg单机版情况
                    && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
                mutiEngine = request.getInstance().getEngine().toLowerCase() + "_singleton";
                subServiceTypeFlavorItemValue = mutiEngine;
            } else {
                // 独享 双机
//                mutiEngine = request.getInstance().getEngine();
                String engine = request.getInstance().getEngine().toLowerCase();
                switch (engine) {
                    case RDSConstant.RDS_ENGINE_MYSQL_LOWER_CASE:
                        mutiEngine = "MySQL";
                        break;
                    case RDSConstant.RDS_ENGINE_PG:
                        mutiEngine = "postgresql";
                        break;
                    case RDSConstant.RDS_ENGINE_SQLSERVER:
                        mutiEngine = "sqlserver";
                        break;
                    default:
                        mutiEngine = "MySQL";
                        break;
                }
                subServiceTypeFlavorItemValue = mutiEngine;
            }
        }
//         完全适配控制台逻辑
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            String engine = request.getInstance().getEngine().toLowerCase();
            switch (engine) {
                case RDSConstant.INSTANCE_TYPE_REPLICA_LOWER_CASE:
                    if (StringUtils.isNotEmpty(request.getInstance().getCategory())
                            && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
                        //后付费 单机版 独占型与通用型价格一致
                        if (StringUtils.isNotEmpty(request.getProductType())
                                && Payment.isPostpay(request.getProductType())) {
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                        } else {
                            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                    request.getInstance().getResourceType())) {
                                // 预付费通用单机
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                            } else {
                                // 预付费独占单机 默认
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                            }
                        }
                    } else {
                        //后付费 双机版
                        if (StringUtils.isNotEmpty(request.getProductType())
                                && Payment.isPostpay(request.getProductType())) {
                            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                    request.getInstance().getResourceType())) {
                                // 通用型
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                            } else {
                                // 独占型 默认
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                            }
                        } else {
                            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                    request.getInstance().getResourceType())) {
                                // 预付费 通用
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                            } else {
                                // 预付费 独占 默认
                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                            }
                        }
                    }
                    break;
                case RDSConstant.RDS_ENGINE_PROXY:
                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(request.getInstance().getResourceType())) {
                        // 通用型代理
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL;
                    } else {
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    }
                    break;
//                default:
//                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
//                    break;
            }
        }
        else {
            if (StringUtils.isEmpty(request.getInstance().getSourceInstanceId())) {
                // 当前为主实例，逻辑不变
                subServiceTypeFlavorItemValue = mutiEngine;
            } else if (StringUtils.isNotEmpty(request.getInstance().getReplicaType()) &&
                    RDSConstant.REPLICA_TYPE_BASIC.equalsIgnoreCase(request.getInstance().getReplicaType())) {
                // 单机版只读
                if (StringUtils.isNotEmpty(request.getProductType())
                        && Payment.isPostpay(request.getProductType())) {
                    //后付费 单机版 独占型与通用型价格一致
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                } else {
                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                            request.getInstance().getResourceType())) {
                        // 预付费通用单机
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    } else {
                        // 预付费独占单机 默认
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    }
                }
            } else if (StringUtils.isNotEmpty(request.getInstance().getReplicaType()) &&
                    RDSConstant.REPLICA_TYPE_HA.equalsIgnoreCase(request.getInstance().getReplicaType())) {
                // 双机版只读
                //后付费 双机版
                if (StringUtils.isNotEmpty(request.getProductType())
                        && Payment.isPostpay(request.getProductType())) {
                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                            request.getInstance().getResourceType())) {
                        // 通用型
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                    } else {
                        // 独占型 默认
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                    }
                } else {
                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                            request.getInstance().getResourceType())) {
                        // 预付费 通用
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                    } else {
                        // 预付费 独占 默认
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                    }
                }
            } else {
                LOGGER.error("readReplica get price error, request.getInstance().getReplicaType() : {}",
                        request.getInstance().getReplicaType());
                subServiceTypeFlavorItemValue = "default";
            }
        }
//        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
//            String engine = request.getInstance().getEngine().toLowerCase();
//            switch (engine) {
//                case RDSConstant.RDS_ENGINE_MYSQL_LOWER_CASE:
//                    subServiceTypeFlavorItemValue = "MySQL";
//                    break;
//                case RDSConstant.RDS_ENGINE_PG:
//                    subServiceTypeFlavorItemValue = "postgresql";
//                    break;
//                case RDSConstant.RDS_ENGINE_SQLSERVER:
//                    subServiceTypeFlavorItemValue = "sqlserver";
//                    break;
//                case RDSConstant.INSTANCE_TYPE_REPLICA_LOWER_CASE:
//                    if (StringUtils.isNotEmpty(request.getInstance().getCategory())
//                            && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
//                        //后付费 单机版 独占型与通用型价格一致
//                        if (StringUtils.isNotEmpty(request.getProductType())
//                                && Payment.isPostpay(request.getProductType())) {
//                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
//                        } else {
//                            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
//                                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
//                                    request.getInstance().getResourceType())) {
//                                // 预付费通用单机
//                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
//                            } else {
//                                // 预付费独占单机 默认
//                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
//                            }
//                        }
//                    } else {
//                        //后付费 双机版
//                        if (StringUtils.isNotEmpty(request.getProductType())
//                                && Payment.isPostpay(request.getProductType())) {
//                            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
//                                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
//                                    request.getInstance().getResourceType())) {
//                                // 通用型
//                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
//                            } else {
//                                // 独占型 默认
//                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
//                            }
//                        } else {
//                            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
//                                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
//                                    request.getInstance().getResourceType())) {
//                                // 预付费 通用
//                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
//                            } else {
//                                // 预付费 独占 默认
//                                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
//                            }
//                        }
//                    }
//                    break;
//                case RDSConstant.RDS_ENGINE_PROXY:
//                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
//                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(request.getInstance().getResourceType())) {
//                        // 通用型代理
//                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL;
//                    } else {
//                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
//                    }
//                    break;
//                default:
//                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
//                    break;
//            }
//        }
//        else {
//            if (StringUtils.isEmpty(request.getInstance().getSourceInstanceId())) {
//                // 当前为主实例，逻辑不变
//                subServiceTypeFlavorItemValue = mutiEngine;
//            } else if (StringUtils.isNotEmpty(request.getInstance().getReplicaType()) &&
//                    RDSConstant.REPLICA_TYPE_BASIC.equalsIgnoreCase(request.getInstance().getReplicaType())) {
//                // 单机版只读
//                if (StringUtils.isNotEmpty(request.getProductType())
//                        && Payment.isPostpay(request.getProductType())) {
//                    //后付费 单机版 独占型与通用型价格一致
//                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
//                } else {
//                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
//                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
//                            request.getInstance().getResourceType())) {
//                        // 预付费通用单机
//                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
//                    } else {
//                        // 预付费独占单机 默认
//                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
//                    }
//                }
//            } else if (StringUtils.isNotEmpty(request.getInstance().getReplicaType()) &&
//                    RDSConstant.REPLICA_TYPE_HA.equalsIgnoreCase(request.getInstance().getReplicaType())) {
//                // 双机版只读
//                //后付费 双机版
//                if (StringUtils.isNotEmpty(request.getProductType())
//                        && Payment.isPostpay(request.getProductType())) {
//                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
//                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
//                            request.getInstance().getResourceType())) {
//                        // 通用型
//                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
//                    } else {
//                        // 独占型 默认
//                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
//                    }
//                } else {
//                    if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
//                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
//                            request.getInstance().getResourceType())) {
//                        // 预付费 通用
//                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
//                    } else {
//                        // 预付费 独占 默认
//                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
//                    }
//                }
//            } else {
//                LOGGER.error("readReplica get price error, request.getInstance().getReplicaType() : {}",
//                        request.getInstance().getReplicaType());
//                subServiceTypeFlavorItemValue = "default";
//            }
//        }
        PricingQueryService pricingQueryService = rdsClientFactory.createNewPricingQueryClient();
        AllPayPromotionClient allPayPromotionClient = rdsClientFactory.createAllPayPromotionClient();
        List<FlavorItem> flavorElements = new ArrayList<>();
        // 实例配置询价
        if (RDSConstant.RDS_ENGINE_PROXY.equalsIgnoreCase(request.getInstance().getEngine())) {
            FlavorItem nodeAmountChargeItem = new FlavorItem();
            nodeAmountChargeItem.setName("nodeAmount");
            nodeAmountChargeItem.setValue(request.getInstance().getNodeAmount().toString());
            nodeAmountChargeItem.setScale(new BigDecimal(1));
            flavorElements.add(nodeAmountChargeItem);
            // 代理实例支持通用型
            if (StringUtils.isNotEmpty(request.getInstance().getResourceType())
                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(request.getInstance().getResourceType())) {
                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL;
                FlavorItem subServiceTypeChargeItem = new FlavorItem();
                subServiceTypeChargeItem.setName("subServiceType");
                subServiceTypeChargeItem.setValue(subServiceTypeFlavorItemValue);
                flavorElements.add(subServiceTypeChargeItem);
            }
        } else {
            if (ObjectUtils.equals("high_io", request.getInstance().getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
                // 通用ssd云磁盘新增计费项
            } else if (ObjectUtils.equals("cloud_nor", request.getInstance().getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", request.getInstance().getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
            } else if (ObjectUtils.equals("cloud_enha", request.getInstance().getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
            } else {
                flavorElements.add(new FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
            }

            if (request.getInstance().getOldFlavor()) {
                FlavorItem memoryChargeItem = new FlavorItem();
                memoryChargeItem.setName("memory");
                memoryChargeItem.setValue(request.getInstance().getAllocatedMemoryInMB() + "m");
                memoryChargeItem.setScale(new BigDecimal(1));
                flavorElements.add(memoryChargeItem);
            } else {
                FlavorItem cpuChargeItem = new FlavorItem();
                cpuChargeItem.setName("cpu");
                cpuChargeItem.setValue(request.getInstance().getCpuCount() + "");
                cpuChargeItem.setScale(new BigDecimal(1));
                flavorElements.add(cpuChargeItem);

                FlavorItem memoryChargeItem = new FlavorItem();
                memoryChargeItem.setName("memory");
                memoryChargeItem.setValue(request.getInstance().getAllocatedMemoryInGB() + "g");
                memoryChargeItem.setScale(new BigDecimal(1));
                flavorElements.add(memoryChargeItem);
            }
            FlavorItem subServiceTypeChargeItem = new FlavorItem();
            subServiceTypeChargeItem.setName("subServiceType");
            subServiceTypeChargeItem.setValue(subServiceTypeFlavorItemValue);
            flavorElements.add(subServiceTypeChargeItem);
        }
        com.baidu.bce.pricing.service.model.common.Flavor flavor
                = new com.baidu.bce.pricing.service.model.common.Flavor ();
        flavor.setFlavorItems(flavorElements);
        // 针对来自api 的询价请求进行特殊处理，注: 接口请求没有 sourceInstanceId
        // 鉴于之前对不同类型的引擎定义 大小写风格不统一，此处需考虑忽略大小写
        ServiceType serviceType;
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            if (StringUtils.isNotEmpty(request.getInstance().getEngine()) &&
                    ((request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)) ||
                            (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_SQLSERVER)) ||
                            (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PG)))) {
                serviceType = ServiceType.RDS;
            } else if (StringUtils.isNotEmpty(request.getInstance().getEngine()) &&
                    request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
                serviceType = ServiceType.RDS_PROXY;
                request.setProductType(ProductPayType.POST_PAY.toString());
            } else {
                serviceType = ServiceType.RDS_REPLICA;
//                request.setProductType(ProductPayType.POST_PAY.toString());
            }
        } else {
            if (StringUtils.isEmpty(request.getInstance().getSourceInstanceId())) {
                serviceType = ServiceType.RDS;
            } else {
                if (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
                    serviceType = ServiceType.RDS_PROXY;
                    request.setProductType(ProductPayType.POST_PAY.toString());
                } else {
                    serviceType = ServiceType.RDS_REPLICA;
                }
            }
        }
        // 此处逻辑永远也不会进去 后续可以删掉 TODO
        if (StringUtils.isNotEmpty(request.getInstance().getInstanceId())) {
            Resource resource = getResourceByInstanceId(request.getInstance().getInstanceId());
            serviceType = ServiceType.valueOf(resource.getServiceType());
            request.setProductType(resource.getProductType());
        }

        Map<String, BigDecimal> price = getInstancePriceV2(request, allPayPromotionClient, flavor, serviceType, from);

        // 创建只读、代理实例时不需要对备份进行询价
        String backupPrice = "0";
        if (serviceType == ServiceType.RDS) {
            // 备份收费询价
            List<FlavorItem> backupItemList = new ArrayList<>();
            RegularPricingQueryRequest backupPriceRequest = new RegularPricingQueryRequest();
            backupPriceRequest.setAccountId(rdsClientFactory.getAccountId());
            backupPriceRequest.setRegion(regionConfiguration.getCurrentRegion());
            backupPriceRequest.setServiceType(serviceType.name());
            backupPriceRequest.setScene(RDSConstant.SCENE_NEW);
            backupPriceRequest.setUsage(new Usage().withAmount("1Gi"));
            backupPriceRequest.setCount(1);
            Period period = new Period("PT1M");
            Time time = new Time();
            time.setStartTime(DateTime.now());
            time.setPeriod(period);
            backupPriceRequest.setTime(time);
            com.baidu.bce.pricing.service.model.common.Flavor flavorBackup
                    = new com.baidu.bce.pricing.service.model.common.Flavor ();
            flavorBackup.setFlavorItems(backupItemList);
            backupPriceRequest.setFlavor(flavorBackup);
            if (StringUtils.isNotEmpty(request.getInstance().getDiskIoType()) &&
                    "normal_io".equals(request.getInstance().getDiskIoType())) {
                // 本地盘类型实例的备份询价
                backupPriceRequest.setChargeItemName("Backup");
                backupPriceRequest.setQueryTime(DateTime.now());
                backupItemList.add(new FlavorItem().withName("Backup").withValue(String.valueOf(1)));

            } else {
                // 云盘类型实例的备份询价
                backupPriceRequest.setChargeItemName("BackupToSnapshot");
                backupPriceRequest.setQueryTime(DateTime.now());
                backupItemList.add(new FlavorItem().withName("BackupToSnapshot").withValue(String.valueOf(1)));

            }

            backupPrice = pricingQueryService.getRegularPrice(backupPriceRequest).
                    getPrice().stripTrailingZeros().toPlainString();
            LOGGER.info("backup price : {}", backupPrice);

        }

        // 公网流量询价
        RegularPricingQueryRequest regularPricingRequest = new RegularPricingQueryRequest();
        regularPricingRequest.setAccountId(rdsClientFactory.getAccountId());
        regularPricingRequest.setRegion(regionConfiguration.getCurrentRegion());
        regularPricingRequest.setServiceType(serviceType.name());
        regularPricingRequest.setChargeItemName("WebOutBytes");
        regularPricingRequest.setScene(RDSConstant.SCENE_NEW);
        regularPricingRequest.setQueryTime(DateTime.now());
        Usage usage = new Usage();
        usage.setUsageAmount(String.valueOf(1024 * 1024 * 1024));
        regularPricingRequest.setUsage(usage);
        regularPricingRequest.setCount(request.getNumber());
        List<FlavorItem> chargeItemList = new ArrayList<>();
        chargeItemList.add(new FlavorItem().withName("WebOutBytes").withValue(String.valueOf(1024 * 1024 * 1024)));
        regularPricingRequest
                .setFlavor(new com.baidu.bce.pricing.service.model.common.Flavor().withFlavorItems(chargeItemList));
        BigDecimal traffic = pricingQueryService.getRegularPrice(regularPricingRequest).getPrice().stripTrailingZeros();
        return new Price().withPrice(price.get("price")).withCatalogPrice(price.get("catalogPrice"))
                .withDiscount(price.get("discount"))
                .withDiscountRate(price.get("discountRate"))
                .withTrafficInGB(traffic).withBackupInGB(backupPrice);
    }

//    public Price getPrice(InstanceCreateModel request) {
// //        String mutiEngine = request.getInstance().getEngineVersion()
// //                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012) ? request.getInstance().getEngine()
// //                + "_singleton" : request.getInstance().getEngine();
//        LOGGER.info("get_price : reqeust {}", request);
//
//        String mutiEngine = "";
//        if (request.getInstance().getEngineVersion()
//                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012) ||
//                request.getInstance().getEngineVersion()
//                        .equals(RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2016)
//                || (request.getInstance().getEngineVersion().equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016)
//                && ("cloud_nor".equals(request.getInstance().getDiskIoType())
//                || "cloud_high".equals(request.getInstance().getDiskIoType())))) { // 处理sqlserver单机版的情况
//            mutiEngine = request.getInstance().getEngine() + "_singleton";
//        } else if ((request.getInstance().getIsEnhanced() != null
//                && request.getInstance().getIsEnhanced()) // 处理MySQL三节点增强版的情况
//                || (StringUtils.isNotEmpty(request.getInstance().getInstanceType())  // raft版的情况
//                && request.getInstance().getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT))) {
//            mutiEngine = request.getInstance().getEngine().toLowerCase() + "_finance";
//        } else if (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)
//                && StringUtils.isNotEmpty(request.getInstance().getCategory())  // 单机版的情况
//                && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
//            // 这里没用RDSConstant.INSTANCE_TYPE_RDS_SINGLE
//            mutiEngine = request.getInstance().getEngine() + "_singleton";
//        } else if (request.getInstance().getEngine().equalsIgnoreCase((RDSConstant.RDS_ENGINE_PG))
//                && StringUtils.isNotEmpty(request.getInstance().getCategory()) // pg单机版情况
//                && request.getInstance().getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
//            mutiEngine = request.getInstance().getEngine() + "_singleton";
//        } else {
//            mutiEngine = request.getInstance().getEngine();
//        }
//        String subServiceTypeFlavorItemValue = StringUtils.isEmpty(request.getInstance().getSourceInstanceId())
//                ? mutiEngine : "default";
//
//        PricingQueryClient priceClient = rdsClientFactory.createNewPricingQueryClient();
//        List<ChargeItem> flavorElements = new ArrayList<>();
//        if (RDSConstant.RDS_ENGINE_PROXY.equalsIgnoreCase(request.getInstance().getEngine())) {
//            ChargeItem nodeAmountChargeItem = new ChargeItem();
//            nodeAmountChargeItem.setName("nodeAmount");
//            nodeAmountChargeItem.setValue(request.getInstance().getNodeAmount().toString());
//            nodeAmountChargeItem.setScale(new BigDecimal(1));
//            flavorElements.add(nodeAmountChargeItem);
//        } else {
//            if (ObjectUtils.equals("high_io", request.getInstance().getDiskIoType())) {
//                flavorElements.add(new ChargeItem().withName("NVMedisk").withValue("1g")
//                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
//                // 通用ssd云磁盘新增计费项
//            } else if (ObjectUtils.equals("cloud_nor", request.getInstance().getDiskIoType())) {
//                flavorElements.add(new ChargeItem().withName("premium_ssd").withValue("1g")
//                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
//                // 高性能云磁盘
//            } else if (ObjectUtils.equals("cloud_high", request.getInstance().getDiskIoType())) {
//                flavorElements.add(new ChargeItem().withName("ssd").withValue("1g")
//                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
//            } else {
//                flavorElements.add(new ChargeItem().withName("disk").withValue("1g")
//                        .withScale(new BigDecimal(request.getInstance().getAllocatedStorageInGB())));
//            }
//
//            if (request.getInstance().getOldFlavor()) {
//                ChargeItem memoryChargeItem = new ChargeItem();
//                memoryChargeItem.setName("memory");
//                memoryChargeItem.setValue(request.getInstance().getAllocatedMemoryInMB() + "m");
//                memoryChargeItem.setScale(new BigDecimal(1));
//                flavorElements.add(memoryChargeItem);
//            } else {
//                ChargeItem cpuChargeItem = new ChargeItem();
//                cpuChargeItem.setName("cpu");
//                cpuChargeItem.setValue(request.getInstance().getCpuCount() + "");
//                cpuChargeItem.setScale(new BigDecimal(1));
//                flavorElements.add(cpuChargeItem);
//
//                ChargeItem memoryChargeItem = new ChargeItem();
//                memoryChargeItem.setName("memory");
//                memoryChargeItem.setValue(request.getInstance().getAllocatedMemoryInGB() + "g");
//                memoryChargeItem.setScale(new BigDecimal(1));
//                flavorElements.add(memoryChargeItem);
//            }
//            ChargeItem subServiceTypeChargeItem = new ChargeItem();
//            subServiceTypeChargeItem.setName("subServiceType");
//            subServiceTypeChargeItem.setValue(subServiceTypeFlavorItemValue);
//            flavorElements.add(subServiceTypeChargeItem);
//        }
//        Flavor flavor = new Flavor();
//        flavor.setFlavorElements(flavorElements);
//
//        ServiceType serviceType;
//        if (StringUtils.isEmpty(request.getInstance().getSourceInstanceId())) {
//            serviceType = ServiceType.RDS;
//        } else {
//            if (request.getInstance().getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
//                serviceType = ServiceType.RDS_PROXY;
//                request.setProductType(ProductPayType.POST_PAY.toString());
//            } else {
//                serviceType = ServiceType.RDS_REPLICA;
//                request.setProductType(ProductPayType.POST_PAY.toString());
//            }
//        }
//
//        if (StringUtils.isNotEmpty(request.getInstance().getInstanceId())) {
//            Resource resource = getResourceByInstanceId(request.getInstance().getInstanceId());
//            serviceType = ServiceType.valueOf(resource.getServiceType());
//            request.setProductType(resource.getProductType());
//        }
//
//        BigDecimal price = getInstancePrice(request, priceClient, flavor, serviceType);
//
//        CatalogCpcPricingQueryRequest queryRequest = new CatalogCpcPricingQueryRequest();
//        queryRequest.setAccountId(rdsClientFactory.getAccountId());
//        queryRequest.setRegion(regionConfiguration.getCurrentRegion());
//        queryRequest.setServiceType(serviceType.name());
//        queryRequest.setProductType(ProductType.ON_DEMAND);
//        queryRequest.setScene(RDSConstant.SCENE_NEW);
//        List<ChargeItem> chargeItemList = new ArrayList<>();
//        chargeItemList.add(new ChargeItem().withName("WebOutBytes").withValue(String.valueOf(1024 * 1024 * 1024)));
//        queryRequest.setFlavor(new Flavor().withFlavorElements(chargeItemList));
//        BigDecimal traffic = priceClient.getCpcPrice(queryRequest).getPrice().stripTrailingZeros();
//
//        return new Price().withPrice(price).withTrafficInGB(traffic);
//    }

    private Map<String, BigDecimal> getInstancePrice(InstanceCreateModel request, PricingQueryService priceClient,
                                              com.baidu.bce.pricing.service.model.common.Flavor flavor,
                                              ServiceType serviceType, String from) {
        Map<String, BigDecimal> prices = new HashMap<>();
        if (Payment.isPrepay(request.getProductType())) {
            RegularPricingQueryRequest regularPricingRequest = new RegularPricingQueryRequest();
            regularPricingRequest.setAccountId(rdsClientFactory.getAccountId());
            regularPricingRequest.setServiceType(serviceType.name());
            regularPricingRequest.setSubServiceType(StringUtils.isEmpty(request.getInstance().getSourceInstanceId())
                    ? request.getInstance().getEngine() : "default");
            regularPricingRequest.setScene(RDSConstant.SCENE_NEW);
            regularPricingRequest.setCount(request.getNumber());
            Time time = new Time();
            time.setPeriod(PricingUtils.toPeriod(request.getDuration(), RDSConstant.RDS_PREPAY_TIME_UNIT));
            time.setStartTime(DateTime.now());
            regularPricingRequest.setTime(time);
            regularPricingRequest.setQueryTime(DateTime.now());
            regularPricingRequest.setChargeItemName("Cpt2");
            regularPricingRequest.setRegion(regionConfiguration.getCurrentRegion());
            regularPricingRequest.setFlavor(flavor);
            PricingQueryDiscountResponse regularPrice =
                    priceClient.getRegularPriceWithDiscount(regularPricingRequest);
            // 去除末尾多余的0
            prices.put("price", regularPrice.getPrice().stripTrailingZeros());
            prices.put("catalogPrice", regularPrice.getCatalogPrice().stripTrailingZeros());
            prices.put("discount", regularPrice.getDiscount().stripTrailingZeros());
            prices.put("realCatalogPrice", regularPrice.getRealCatalogPrice().stripTrailingZeros());

        } else if (Payment.isPostpay(request.getProductType())) {
            RegularPricingQueryRequest regularPricingRequest = new RegularPricingQueryRequest();
            regularPricingRequest.setAccountId(rdsClientFactory.getAccountId());
            regularPricingRequest.setServiceType(serviceType.name());
            if (RDSConstant.FROM_API.equalsIgnoreCase(from) && serviceType != ServiceType.RDS) {
                regularPricingRequest.setSubServiceType("default");
            } else {
                regularPricingRequest.setSubServiceType(StringUtils.isEmpty(request.getInstance().getSourceInstanceId())
                        ? request.getInstance().getEngine() : "default");
            }
            regularPricingRequest.setScene(RDSConstant.SCENE_NEW);
            regularPricingRequest.setCount(request.getNumber());
            Time time = new Time();
            time.setStartTime(DateTime.now());
            time.setPeriod(PricingUtils.toPeriod(1, RDSConstant.RDS_POSTPAY_TIME_UNIT));
            regularPricingRequest.setTime(time);
            regularPricingRequest.setQueryTime(DateTime.now());
            regularPricingRequest.setChargeItemName("RunningTimeMinutes");
            regularPricingRequest.setRegion(regionConfiguration.getCurrentRegion());
            regularPricingRequest.setFlavor(flavor);
            PricingQueryDiscountResponse regularPrice =
                    priceClient.getRegularPriceWithDiscount(regularPricingRequest);
            prices.put("price", regularPrice.getPrice().stripTrailingZeros());
            prices.put("catalogPrice", regularPrice.getCatalogPrice().stripTrailingZeros());
            prices.put("discount", regularPrice.getDiscount().stripTrailingZeros());
            prices.put("realCatalogPrice", regularPrice.getRealCatalogPrice().stripTrailingZeros());

        }
        return prices;
    }

    private Map<String, BigDecimal> getInstancePriceV2(InstanceCreateModel request, AllPayPromotionClient priceClient,
                                                       com.baidu.bce.pricing.service.model.common.Flavor flavor,
                                                       ServiceType serviceType, String from) {
        Map<String, BigDecimal> prices = new HashMap<>();
        if (Payment.isPrepay(request.getProductType())) {
            BatchPriceAllPayRequest payRequest = new BatchPriceAllPayRequest();
            List<PriceAllPayRequest> requests = new ArrayList<>();
            PriceAllPayRequest payRequest1 = new PriceAllPayRequest();
            payRequest1.setUuid(UUID.randomUUID().toString());
            payRequest1.setAccountId(rdsClientFactory.getAccountId());
            payRequest1.setServiceType(serviceType.name());
            payRequest1.setRegion(regionConfiguration.getCurrentRegion());
            payRequest1.setDuration(request.getDuration());
            payRequest1.setTimeUnit(RDSConstant.RDS_PREPAY_TIME_UNIT);
            payRequest1.setOrderType(RDSConstant.SCENE_NEW);
            if (request.getNumber() != null) {
                payRequest1.setCount(request.getNumber());
            } else {
                payRequest1.setCount(1);
            }
            payRequest1.setFlavor(flavor);
            payRequest1.setProductType(Payment.PREPAY.getValue());
            payRequest1.setSubProductType("Cpt2");
            payRequest1.setQueryTime(DateTime.now());
            requests.add(payRequest1);
            payRequest.setRequests(requests);
            AllPayPriceResultResponse priceResultResponse = priceClient.getPriceAllPayBatch(payRequest);
            if (priceResultResponse.getResponses() != null && !priceResultResponse.getResponses().isEmpty()) {
                AllPayPriceResult allPayPriceResult = priceResultResponse.getResponses().get(0);
                // 去除末尾多余的0
                prices.put("price", allPayPriceResult.getPrice().stripTrailingZeros());
                prices.put("catalogPrice", allPayPriceResult.getCatalogPrice().stripTrailingZeros());
                prices.put("discount", allPayPriceResult.getDiscountRate().stripTrailingZeros());
                prices.put("discountRate", allPayPriceResult.getDiscountRate().stripTrailingZeros());
            }
        } else if (Payment.isPostpay(request.getProductType())) {
            BatchPriceAllPayRequest payRequest = new BatchPriceAllPayRequest();
            List<PriceAllPayRequest> requests = new ArrayList<>();
            PriceAllPayRequest payRequest1 = new PriceAllPayRequest();
            payRequest1.setUuid(UUID.randomUUID().toString());
            payRequest1.setAccountId(rdsClientFactory.getAccountId());
            payRequest1.setServiceType(serviceType.name());
            payRequest1.setRegion(regionConfiguration.getCurrentRegion());
            payRequest1.setDuration(1);
            if (StringUtils.isNotEmpty(request.getTimeUnit())
                    && RDSConstant.QUERY_PRICE_TIME_UNIT.contains(request.getTimeUnit().toUpperCase())) {
                payRequest1.setTimeUnit(request.getTimeUnit());
            } else {
                payRequest1.setTimeUnit(RDSConstant.RDS_POSTPAY_TIME_UNIT);
            }
//            payRequest1.setTimeUnit(RDSConstant.RDS_POSTPAY_TIME_UNIT);
            payRequest1.setOrderType(RDSConstant.SCENE_NEW);
            if (request.getNumber() != null) {
                payRequest1.setCount(request.getNumber());
            } else {
                payRequest1.setCount(1);
            }
            payRequest1.setFlavor(flavor);
            payRequest1.setProductType(Payment.POSTPAY.getValue());
            payRequest1.setChargeItemName("RunningTimeMinutes");
            payRequest1.setQueryTime(DateTime.now());
            requests.add(payRequest1);
            payRequest.setRequests(requests);
            AllPayPriceResultResponse priceResultResponse = priceClient.getPriceAllPayBatch(payRequest);
            if (priceResultResponse.getResponses() != null && !priceResultResponse.getResponses().isEmpty()) {
                AllPayPriceResult allPayPriceResult = priceResultResponse.getResponses().get(0);
                // 去除末尾多余的0
                prices.put("price", allPayPriceResult.getPrice().stripTrailingZeros());
                prices.put("catalogPrice", allPayPriceResult.getCatalogPrice().stripTrailingZeros());
                prices.put("discount", allPayPriceResult.getDiscountRate().stripTrailingZeros());
                prices.put("discountRate", allPayPriceResult.getDiscountRate().stripTrailingZeros());
            }
        }
        return prices;
    }

    public Resource getResourceByInstanceId(String instanceId) {
        Resources resources = instanceService.getResourceList();
        for (Resource resource : resources) {
            if (resource.getName().equals(instanceId)) {
                return resource;
            }
        }
        throw new RDSExceptions.ResourceNotExistException();
    }

    public Price getPriceDiff(PriceDiffModel request) {
        return this.composeResizeRequest(request);
//        double priceDifference = rdsClientFactory.createOrderClient()
//                .marginDilatationPrice(resizeRequest).doubleValue();
//        return new Price().withPrice(new BigDecimal(priceDifference));
    }

    private Price composeResizeRequest(PriceDiffModel request) {
        InstanceExtension instance = instanceService.detail(request.getInstanceId(), null);
        Resource resource = this.getResourceByInstanceId(request.getInstanceId());
        // 单机转双机的询价需单独处理
        if (StringUtils.isNotEmpty(request.getCategory())
                && RDSConstant.APPLICATION_TYPE_NORMAL.equalsIgnoreCase(request.getCategory())
                && RDSConstant.APPLICATION_TYPE_SINGLE.equalsIgnoreCase(instance.getApplicationType())) {
            return composeSingletonToNormalResizeRequest(request, instance, resource);
        }
        Price price = new Price();
//        String mutiEngine = instance.getEngineVersion()
//                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012) ? instance.getEngine()
//                + "_singleton" : instance.getEngine();
        // 支持新增的通用型实例询价
        String mutiEngine = "";
        String subServiceTypeFlavorItemValue = "";
        if (StringUtils.isNotEmpty(instance.getResourceType()) &&
                RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType()) &&
                StringUtils.isNotEmpty(instance.getEngine()) &&
                        RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instance.getEngine())) {
            if (StringUtils.isNotEmpty(instance.getApplicationType()) &&
                    RDSConstant.APPLICATION_TYPE_NORMAL.equalsIgnoreCase(instance.getApplicationType())) {
                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                mutiEngine = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
            } else {
                // 此处单机版价格暂无，默认与原单机版保持一致，TODO
                subServiceTypeFlavorItemValue = instance.getEngine() + "_singleton";
                mutiEngine = instance.getEngine() + "_singleton";
            }

        } else {
            if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER)
                    && instance.getApplicationType().equals(RDSConstant.APPLICATION_TYPE_SINGLE)) { // 处理sqlserver 2012单机版
                mutiEngine = instance.getEngine() + "_singleton";
            } else if (request.getIsEnhanced() != null && request.getIsEnhanced()) { // 处理mysql三节点增强版
                // mutiEngine = "rds_finance";
                mutiEngine = instance.getEngine().toLowerCase() + "_finance";
            } else if (instance.getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)
                    && StringUtils.isNotEmpty(instance.getCategory())
                    && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // 处理单机版
                mutiEngine = instance.getEngine() + "_singleton";
            } else if (instance.getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PG)
                    && StringUtils.isNotEmpty(instance.getCategory())
                    && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // pg单机版
                mutiEngine = instance.getEngine() + "_singleton";
            } else {
                mutiEngine = instance.getEngine();
            }
        }
        if (StringUtils.isEmpty(instance.getSourceInstanceId())) {
            // 当前为主实例，逻辑不变
            subServiceTypeFlavorItemValue = mutiEngine;
        } else if (StringUtils.isNotEmpty(instance.getReplicaType()) &&
                RDSConstant.REPLICA_TYPE_BASIC.equalsIgnoreCase(instance.getReplicaType())) {
            // 单机版只读
            if (StringUtils.isNotEmpty(instance.getProductType())
                    && Payment.isPostpay(instance.getProductType())) {
                //后付费 单机版 独占型与通用型价格一致
                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
            } else {
                if (StringUtils.isNotEmpty(instance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                        instance.getResourceType())) {
                    // 预付费通用单机
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                } else {
                    // 预付费独占单机 默认
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                }
            }
        } else if (StringUtils.isNotEmpty(instance.getReplicaType()) &&
                RDSConstant.REPLICA_TYPE_HA.equalsIgnoreCase(instance.getReplicaType())) {
            // 双机版只读
            //后付费 双机版
            if (StringUtils.isNotEmpty(instance.getProductType())
                    && Payment.isPostpay(instance.getProductType())) {
                if (StringUtils.isNotEmpty(instance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                        instance.getResourceType())) {
                    // 通用型
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                } else {
                    // 独占型 默认
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                }
            } else {
                if (StringUtils.isNotEmpty(instance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                        instance.getResourceType())) {
                    // 预付费 通用
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                } else {
                    // 预付费 独占 默认
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                }
            }
        } else {
            LOGGER.error("readReplica get price error, request.getInstance().getReplicaType() : {}",
                    instance.getReplicaType());
            subServiceTypeFlavorItemValue = "default";
        }

        if (request.getDiskIoType() == null) {
            request.setDiskIoType(instance.getDiskIoType());
        }

        ServiceType serviceType;
        if (StringUtils.isEmpty(instance.getSourceInstanceId())) {
            serviceType = ServiceType.RDS;
        } else {
            if (instance.getInstanceType().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
                serviceType = ServiceType.RDS_PROXY;

            } else {
                serviceType = ServiceType.RDS_REPLICA;
            }
        }

        // 此处旧逻辑有问题，当变配时磁盘类型发生变化，询价参数取的是当前实例的而非用户传值，更正
        // 兼容逻辑，为防止用户不传diskIoType（磁盘类型不变），可取资源中的参数
        String diskIoType = StringUtils.isNotEmpty(request.getDiskIoType()) ?
                request.getDiskIoType() : instance.getDiskIoType();

        if (ProductPayType.PRE_PAY.toString().equalsIgnoreCase(resource.getProductType())) {
            ResizeRequest resizeRequest = new ResizeRequest();
            resizeRequest.setUuid(resource.getUuid());
            if (request.getOldFlavor()) {
                resizeRequest.setFlavor(new HashSet<>(new com.baidu.bce.internalsdk.order.model.Flavor()
                        .addFlavorItem(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                                .withName("memory")
                                .withValue(request.getAllocatedMemoryInMB() + "m")
                                .withScale(new BigDecimal(1)))
                        .addFlavorItem(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                                .withName("subServiceType")
                                .withValue(subServiceTypeFlavorItemValue))));

                Set<com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
                if (ObjectUtils.equals("high_io", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("NVMedisk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 新增计费项
                } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("premium_ssd").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 高性能云磁盘
                } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem().withName("ssd")
                            .withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("enhanced_ssd_pl1").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("disk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                }
            } else {
                resizeRequest.setFlavor(new HashSet<>(new com.baidu.bce.internalsdk.order.model.Flavor()
                        .addFlavorItem(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                                .withName("memory")
                                .withValue(request.getAllocatedMemoryInGB() + "g")
                                .withScale(new BigDecimal(1)))
                        .addFlavorItem(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                                .withName("cpu")
                                .withValue(request.getCpuCount() + "")
                                .withScale(new BigDecimal(1)))
                        .addFlavorItem(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                                .withName("subServiceType")
                                .withValue(subServiceTypeFlavorItemValue))));

                Set<com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
                if (ObjectUtils.equals("high_io", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("NVMedisk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 新增计费项
                } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem().withName("premium_ssd")
                            .withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 高性能云磁盘
                } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem().withName("ssd")
                            .withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                    // 增强型云磁盘
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("enhanced_ssd_pl1").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("disk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                }
            }
            if (ProductPayType.PRE_PAY.toString().equalsIgnoreCase(resource.getProductType())) {
                resizeRequest.setResizeType(0);
            }
            BigDecimal priceDifference = rdsClientFactory.createOrderClient()
                    .marginDilatationPrice(resizeRequest);
            price.withPrice(priceDifference);
        } else if (ProductPayType.POST_PAY.toString().equalsIgnoreCase(resource.getProductType())) {
            com.baidu.bce.pricing.service.model.common.Flavor pricingFlavor =
                    new com.baidu.bce.pricing.service.model.common.Flavor();
            List<FlavorItem> flavorElements = new ArrayList<>();
            if (instance.getInstanceType().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
                FlavorItem nodeAmountChargeItem = new FlavorItem();
                nodeAmountChargeItem.setName("nodeAmount");
                nodeAmountChargeItem.setValue(request.getNodeAmount().toString());
                nodeAmountChargeItem.setScale(new BigDecimal(1));
                flavorElements.add(nodeAmountChargeItem);
                // 支持通用型代理
                if (StringUtils.isNotEmpty(instance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType())) {
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL;
                    FlavorItem subServiceTypeChargeItem = new FlavorItem();
                    subServiceTypeChargeItem.setName("subServiceType");
                    subServiceTypeChargeItem.setValue(subServiceTypeFlavorItemValue);
                    flavorElements.add(subServiceTypeChargeItem);
                }
            } else {
                if (request.getOldFlavor()) {
                    FlavorItem memoryChargeItem = new FlavorItem();
                    memoryChargeItem.setName("memory");
                    memoryChargeItem.setValue(request.getAllocatedMemoryInMB() + "m");
                    memoryChargeItem.setScale(new BigDecimal(1));
                    flavorElements.add(memoryChargeItem);
                } else {
                    FlavorItem cpuChargeItem = new FlavorItem();
                    cpuChargeItem.setName("cpu");
                    cpuChargeItem.setValue(request.getCpuCount() + "");
                    cpuChargeItem.setScale(new BigDecimal(1));
                    flavorElements.add(cpuChargeItem);

                    FlavorItem memoryChargeItem = new FlavorItem();
                    memoryChargeItem.setName("memory");
                    memoryChargeItem.setValue(request.getAllocatedMemoryInGB() + "g");
                    memoryChargeItem.setScale(new BigDecimal(1));
                    flavorElements.add(memoryChargeItem);
                }

                FlavorItem subServiceTypeChargeItem = new FlavorItem();
                subServiceTypeChargeItem.setName("subServiceType");
                subServiceTypeChargeItem.setValue(subServiceTypeFlavorItemValue);
                flavorElements.add(subServiceTypeChargeItem);

                 if (ObjectUtils.equals("high_io", diskIoType)) {
                    flavorElements.add(new FlavorItem().withName("NVMedisk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 新增计费项
                } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                    flavorElements.add(new FlavorItem().withName("premium_ssd")
                            .withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 高性能云磁盘
                } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                    flavorElements.add(new FlavorItem().withName("ssd")
                            .withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                 } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                     flavorElements.add(new FlavorItem()
                             .withName("enhanced_ssd_pl1").withValue("1g")
                             .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else {
                    flavorElements.add(new FlavorItem().withName("disk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                }
            }

            pricingFlavor.setFlavorItems(flavorElements);

            PricingClientV3 pricingClientV3 = rdsClientFactory.createPricingClientV3();
            BigDecimal currentPrice = pricingClientV3.getCpt1Price(
                    regionConfiguration.getCurrentRegion(),
                    serviceType.name(),
                    RDSConstant.RDS_POSTPAY_TIME_UNIT,
                    1,
                    1,
                    DateTime.now(),
                    pricingFlavor,
                    null,
                    LogicUserService.getAccountId());

            BigDecimal trafficPrice = pricingClientV3.getCpcPrice(
                    regionConfiguration.getCurrentRegion(),
                    serviceType.name(),
                    DateTime.now(),
                    "WebOutBytes",
                    BigDecimal.valueOf(1024 * 1024 * 1024),
                    LogicUserService.getAccountId()
            ).stripTrailingZeros();

             price.withPrice(currentPrice).withTrafficInGB(trafficPrice);
        }

        // 无论是后付费还是预付费实例 ，均需展示备份价格
        // 变配只读、代理实例时不需要对备份进行询价
        String backupPrice = "0";
        PricingQueryService pricingQueryService = rdsClientFactory.createNewPricingQueryClient();
        if (serviceType == ServiceType.RDS) {
            // 备份收费询价
            List<FlavorItem> backupItemList = new ArrayList<>();
            RegularPricingQueryRequest backupPriceRequest = new RegularPricingQueryRequest();
            backupPriceRequest.setAccountId(rdsClientFactory.getAccountId());
            backupPriceRequest.setRegion(regionConfiguration.getCurrentRegion());
            backupPriceRequest.setServiceType(serviceType.name());
            backupPriceRequest.setScene(RDSConstant.SCENE_NEW);
            backupPriceRequest.setUsage(new Usage().withAmount("1Gi"));
            backupPriceRequest.setCount(1);
            Period period = new Period("PT1M");
            Time time = new Time();
            time.setStartTime(DateTime.now());
            time.setPeriod(period);
            backupPriceRequest.setTime(time);
            com.baidu.bce.pricing.service.model.common.Flavor flavorBackup
                    = new com.baidu.bce.pricing.service.model.common.Flavor ();
            flavorBackup.setFlavorItems(backupItemList);
            backupPriceRequest.setFlavor(flavorBackup);
            if (StringUtils.isNotEmpty(request.getDiskIoType()) &&
                    "normal_io".equals(request.getDiskIoType())) {
                // 本地盘类型实例的备份询价
                backupPriceRequest.setChargeItemName("Backup");
                backupPriceRequest.setQueryTime(DateTime.now());
                backupItemList.add(new FlavorItem().withName("Backup").withValue(String.valueOf(1)));

            } else {
                // 云盘类型实例的备份询价
                backupPriceRequest.setChargeItemName("BackupToSnapshot");
                backupPriceRequest.setQueryTime(DateTime.now());
                backupItemList.add(new FlavorItem().withName("BackupToSnapshot").withValue(String.valueOf(1)));

            }

            backupPrice = pricingQueryService.getRegularPrice(backupPriceRequest).
                    getPrice().stripTrailingZeros().toPlainString();
            LOGGER.info("backup price : {}", backupPrice);
        }
        price.withBackupInGB(backupPrice);
        return price;
    }

    /**
     * 单机转双机的场景，询价操作单独处理
     * @param request
     * @param instance
     * @param resource
     * @return
     */
    private Price composeSingletonToNormalResizeRequest(PriceDiffModel request, InstanceExtension instance,
                                                        Resource resource) {
        Price price = new Price();
        // 逻辑至此可保证必为双机版实例询价，只读实例暂不考虑 TODO
        String subServiceTypeFlavorItemValue = instance.getEngine();
        if (StringUtils.isNotEmpty(instance.getResourceType())
                && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType())) {
            // 通用型单机转双机
            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
        } else {
            subServiceTypeFlavorItemValue = instance.getEngine();
        }
        if (request.getDiskIoType() == null) {
            request.setDiskIoType(instance.getDiskIoType());
        }
        ServiceType serviceType;
        if (StringUtils.isEmpty(instance.getSourceInstanceId())) {
            serviceType = ServiceType.RDS;
        } else {
            if (instance.getInstanceType().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
                serviceType = ServiceType.RDS_PROXY;

            } else {
                serviceType = ServiceType.RDS_REPLICA;
            }
        }
        // 此处旧逻辑有问题，当变配时磁盘类型发生变化，询价参数取的是当前实例的而非用户传值，更正
        // 兼容逻辑，为防止用户不传diskIoType（磁盘类型不变），可取资源中的参数
        String diskIoType = StringUtils.isNotEmpty(request.getDiskIoType()) ?
                request.getDiskIoType() : instance.getDiskIoType();

        if (ProductPayType.PRE_PAY.toString().equalsIgnoreCase(resource.getProductType())) {
            ResizeRequest resizeRequest = new ResizeRequest();
            resizeRequest.setUuid(resource.getUuid());
            if (request.getOldFlavor()) {
                resizeRequest.setFlavor(new HashSet<>(new com.baidu.bce.internalsdk.order.model.Flavor()
                        .addFlavorItem(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                                .withName("memory")
                                .withValue(request.getAllocatedMemoryInMB() + "m")
                                .withScale(new BigDecimal(1)))
                        .addFlavorItem(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                                .withName("subServiceType")
                                .withValue(subServiceTypeFlavorItemValue))));

                Set<com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
                if (ObjectUtils.equals("high_io", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("NVMedisk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 新增计费项
                } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("premium_ssd").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 高性能云磁盘
                } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem().withName("ssd")
                            .withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("enhanced_ssd_pl1").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("disk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                }
            } else {
                resizeRequest.setFlavor(new HashSet<>(new com.baidu.bce.internalsdk.order.model.Flavor()
                        .addFlavorItem(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                                .withName("memory")
                                .withValue(request.getAllocatedMemoryInGB() + "g")
                                .withScale(new BigDecimal(1)))
                        .addFlavorItem(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                                .withName("cpu")
                                .withValue(request.getCpuCount() + "")
                                .withScale(new BigDecimal(1)))
                        .addFlavorItem(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                                .withName("subServiceType")
                                .withValue(subServiceTypeFlavorItemValue))));

                Set<com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
                if (ObjectUtils.equals("high_io", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("NVMedisk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 新增计费项
                } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem().withName("premium_ssd")
                            .withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 高性能云磁盘
                } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem().withName("ssd")
                            .withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("enhanced_ssd_pl1").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else {
                    flavors.add(new com.baidu.bce.internalsdk.order.model.Flavor.FlavorItem()
                            .withName("disk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                }
            }
            if (ProductPayType.PRE_PAY.toString().equalsIgnoreCase(resource.getProductType())) {
                resizeRequest.setResizeType(0);
            }
            BigDecimal priceDifference = rdsClientFactory.createOrderClient()
                    .marginDilatationPrice(resizeRequest);
            price.withPrice(priceDifference);
        } else if (ProductPayType.POST_PAY.toString().equalsIgnoreCase(resource.getProductType())) {
            com.baidu.bce.pricing.service.model.common.Flavor pricingFlavor =
                    new com.baidu.bce.pricing.service.model.common.Flavor();
            List<FlavorItem> flavorElements = new ArrayList<>();
            if (instance.getInstanceType().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
                FlavorItem nodeAmountChargeItem = new FlavorItem();
                nodeAmountChargeItem.setName("nodeAmount");
                nodeAmountChargeItem.setValue(request.getNodeAmount().toString());
                nodeAmountChargeItem.setScale(new BigDecimal(1));
                flavorElements.add(nodeAmountChargeItem);
            } else {
                if (request.getOldFlavor()) {
                    FlavorItem memoryChargeItem = new FlavorItem();
                    memoryChargeItem.setName("memory");
                    memoryChargeItem.setValue(request.getAllocatedMemoryInMB() + "m");
                    memoryChargeItem.setScale(new BigDecimal(1));
                    flavorElements.add(memoryChargeItem);
                } else {
                    FlavorItem cpuChargeItem = new FlavorItem();
                    cpuChargeItem.setName("cpu");
                    cpuChargeItem.setValue(request.getCpuCount() + "");
                    cpuChargeItem.setScale(new BigDecimal(1));
                    flavorElements.add(cpuChargeItem);

                    FlavorItem memoryChargeItem = new FlavorItem();
                    memoryChargeItem.setName("memory");
                    memoryChargeItem.setValue(request.getAllocatedMemoryInGB() + "g");
                    memoryChargeItem.setScale(new BigDecimal(1));
                    flavorElements.add(memoryChargeItem);
                }

                FlavorItem subServiceTypeChargeItem = new FlavorItem();
                subServiceTypeChargeItem.setName("subServiceType");
                subServiceTypeChargeItem.setValue(subServiceTypeFlavorItemValue);
                flavorElements.add(subServiceTypeChargeItem);

                if (ObjectUtils.equals("high_io", diskIoType)) {
                    flavorElements.add(new FlavorItem().withName("NVMedisk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 新增计费项
                } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                    flavorElements.add(new FlavorItem().withName("premium_ssd")
                            .withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                    // 高性能云磁盘
                } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                    flavorElements.add(new FlavorItem().withName("ssd")
                            .withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                    flavorElements.add(new FlavorItem()
                            .withName("enhanced_ssd_pl1").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                } else {
                    flavorElements.add(new FlavorItem().withName("disk").withValue("1g")
                            .withScale(new BigDecimal(request.getAllocatedStorageInGB())));
                }
            }

            pricingFlavor.setFlavorItems(flavorElements);

            PricingClientV3 pricingClientV3 = rdsClientFactory.createPricingClientV3();
            BigDecimal currentPrice = pricingClientV3.getCpt1Price(
                    regionConfiguration.getCurrentRegion(),
                    serviceType.name(),
                    RDSConstant.RDS_POSTPAY_TIME_UNIT,
                    1,
                    1,
                    DateTime.now(),
                    pricingFlavor,
                    null,
                    LogicUserService.getAccountId());

            BigDecimal trafficPrice = pricingClientV3.getCpcPrice(
                    regionConfiguration.getCurrentRegion(),
                    serviceType.name(),
                    DateTime.now(),
                    "WebOutBytes",
                    BigDecimal.valueOf(1024 * 1024 * 1024),
                    LogicUserService.getAccountId()
            ).stripTrailingZeros();

            price.withPrice(currentPrice).withTrafficInGB(trafficPrice);
        }

        // 无论是后付费还是预付费实例 ，均需展示备份价格
        // 变配只读、代理实例时不需要对备份进行询价
        String backupPrice = "0";
        PricingQueryService pricingQueryService = rdsClientFactory.createNewPricingQueryClient();
        if (serviceType == ServiceType.RDS) {
            // 备份收费询价
            List<FlavorItem> backupItemList = new ArrayList<>();
            RegularPricingQueryRequest backupPriceRequest = new RegularPricingQueryRequest();
            backupPriceRequest.setAccountId(rdsClientFactory.getAccountId());
            backupPriceRequest.setRegion(regionConfiguration.getCurrentRegion());
            backupPriceRequest.setServiceType(serviceType.name());
            backupPriceRequest.setScene(RDSConstant.SCENE_NEW);
            backupPriceRequest.setUsage(new Usage().withAmount("1Gi"));
            backupPriceRequest.setCount(1);
            Period period = new Period("PT1M");
            Time time = new Time();
            time.setStartTime(DateTime.now());
            time.setPeriod(period);
            backupPriceRequest.setTime(time);
            com.baidu.bce.pricing.service.model.common.Flavor flavorBackup
                    = new com.baidu.bce.pricing.service.model.common.Flavor ();
            flavorBackup.setFlavorItems(backupItemList);
            backupPriceRequest.setFlavor(flavorBackup);
            if (StringUtils.isNotEmpty(request.getDiskIoType()) &&
                    "normal_io".equals(request.getDiskIoType())) {
                // 本地盘类型实例的备份询价
                backupPriceRequest.setChargeItemName("Backup");
                backupPriceRequest.setQueryTime(DateTime.now());
                backupItemList.add(new FlavorItem().withName("Backup").withValue(String.valueOf(1)));

            } else {
                // 云盘类型实例的备份询价
                backupPriceRequest.setChargeItemName("BackupToSnapshot");
                backupPriceRequest.setQueryTime(DateTime.now());
                backupItemList.add(new FlavorItem().withName("BackupToSnapshot").withValue(String.valueOf(1)));

            }

            backupPrice = pricingQueryService.getRegularPrice(backupPriceRequest).
                    getPrice().stripTrailingZeros().toPlainString();
            LOGGER.info("backup price : {}", backupPrice);
        }
        price.withBackupInGB(backupPrice);
        return price;
    }

    public List<Price> getBatchPrice(BatchPriceRequest request, String from) {
        List<Price> result = new ArrayList<>();
        LOGGER.info("get_price : reqeust {}", request);
        for (int i = 0; i < request.getInstances().size(); i++) {
            // 校验询价类型 单机 or 双机 or 增强型
            String mutiEngine = checkInstanceType(request.getInstances().get(i), from);

            // 确认商品类型，兼容 consoleAPI openAPI 两套询价逻辑
            String subServiceTypeFlavorItemValue = "";
            if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
                subServiceTypeFlavorItemValue = checkSubServiceType(request, request.getInstances().get(i), from);
            } else {
                if (StringUtils.isEmpty(request.getInstances().get(i).getSourceInstanceId())) {
                    subServiceTypeFlavorItemValue = mutiEngine;
                } else if (StringUtils.isNotEmpty(request.getInstances().get(i).getReplicaType()) &&
                        RDSConstant.REPLICA_TYPE_BASIC.equalsIgnoreCase(
                                request.getInstances().get(i).getReplicaType())) {
                    // 单机版只读
                    if (StringUtils.isNotEmpty(request.getProductType())
                            && Payment.isPostpay(request.getProductType())) {
                        //后付费 单机版 独占型与通用型价格一致
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    } else {
                        if (StringUtils.isNotEmpty(request.getInstances().get(i).getResourceType())
                                && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                request.getInstances().get(i).getResourceType())) {
                            // 预付费通用单机
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                        } else {
                            // 预付费独占单机 默认
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                        }
                    }
                } else if (StringUtils.isNotEmpty(request.getInstances().get(i).getReplicaType()) &&
                        RDSConstant.REPLICA_TYPE_HA.equalsIgnoreCase(request.getInstances().get(i).getReplicaType())) {
                    // 双机版只读
                    //后付费 双机版
                    if (StringUtils.isNotEmpty(request.getProductType())
                            && Payment.isPostpay(request.getProductType())) {
                        if (StringUtils.isNotEmpty(request.getInstances().get(i).getResourceType())
                                && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                request.getInstances().get(i).getResourceType())) {
                            // 通用型
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                        } else {
                            // 独占型 默认
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                        }
                    } else {
                        if (StringUtils.isNotEmpty(request.getInstances().get(i).getResourceType())
                                && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                request.getInstances().get(i).getResourceType())) {
                            // 预付费 通用
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                        } else {
                            // 预付费 独占 默认
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                        }
                    }
                }
            }

            PricingQueryService pricingQueryService = rdsClientFactory.createNewPricingQueryClient();
            List<FlavorItem> flavorElements = new ArrayList<>();
            // 实例配置询价 此处包含 CPU MEM
            checkInstanceConfigInfo(flavorElements, request.getInstances().get(i), subServiceTypeFlavorItemValue);
            com.baidu.bce.pricing.service.model.common.Flavor flavor
                    = new com.baidu.bce.pricing.service.model.common.Flavor ();
            flavor.setFlavorItems(flavorElements);
            // 针对来自api 的询价请求进行特殊处理，注: 接口请求没有 sourceInstanceId
            // 鉴于之前对不同类型的引擎定义 大小写风格不统一，此处需考虑忽略大小写
            ServiceType serviceType;
            if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
                if (StringUtils.isNotEmpty(request.getInstances().get(i).getEngine()) &&
                        ((request.getInstances().get(i).getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)) ||
                                (request.getInstances().get(i).getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_SQLSERVER)) ||
                                (request.getInstances().get(i).getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PG)))) {
                    serviceType = ServiceType.RDS;
                } else if (StringUtils.isNotEmpty(request.getInstances().get(i).getEngine()) &&
                        request.getInstances().get(i).getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
                    serviceType = ServiceType.RDS_PROXY;
                    request.setProductType(ProductPayType.POST_PAY.toString());
                } else {
                    serviceType = ServiceType.RDS_REPLICA;
                    request.setProductType(ProductPayType.POST_PAY.toString());
                }
            } else {
                if (StringUtils.isEmpty(request.getInstances().get(i).getSourceInstanceId())) {
                    serviceType = ServiceType.RDS;
                } else {
                    if (request.getInstances().get(i).getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PROXY)) {
                        serviceType = ServiceType.RDS_PROXY;
                        request.setProductType(ProductPayType.POST_PAY.toString());
                    } else {
                        serviceType = ServiceType.RDS_REPLICA;
                    }
                }
            }

            // 批量询价接口 此处需使用外层数据 故暂不封装
            Map<String, BigDecimal> prices = new HashMap<>();
            if (Payment.isPrepay(request.getProductType())) {
                RegularPricingQueryRequest regularPricingRequest = new RegularPricingQueryRequest();
                regularPricingRequest.setAccountId(rdsClientFactory.getAccountId());
                regularPricingRequest.setServiceType(serviceType.name());
                regularPricingRequest.setSubServiceType(
                        StringUtils.isEmpty(request.getInstances().get(i).getSourceInstanceId())
                        ? request.getInstances().get(i).getEngine() : "default");
                regularPricingRequest.setScene(RDSConstant.SCENE_NEW);
                regularPricingRequest.setCount(request.getNumber());
                Time time = new Time();
                time.setPeriod(PricingUtils.toPeriod(request.getDuration(), RDSConstant.RDS_PREPAY_TIME_UNIT));
                time.setStartTime(DateTime.now());
                regularPricingRequest.setTime(time);
                regularPricingRequest.setQueryTime(DateTime.now());
                regularPricingRequest.setChargeItemName("Cpt2");
                regularPricingRequest.setRegion(regionConfiguration.getCurrentRegion());
                regularPricingRequest.setFlavor(flavor);
                com.baidu.bce.pricing.service.model.query.PricingQueryResponse regularPrice
                        = pricingQueryService.getRegularPrice(regularPricingRequest);
                // 去除末尾多余的0
                prices.put("price", new BigDecimal(regularPrice.getPrice().stripTrailingZeros().toPlainString()));
                prices.put("catalogPrice",
                        new BigDecimal(regularPrice.getCatalogPrice().stripTrailingZeros().toPlainString()));
            } else if (Payment.isPostpay(request.getProductType())) {
                RegularPricingQueryRequest regularPricingRequest = new RegularPricingQueryRequest();
                regularPricingRequest.setAccountId(rdsClientFactory.getAccountId());
                regularPricingRequest.setServiceType(serviceType.name());
                if (RDSConstant.FROM_API.equalsIgnoreCase(from) && serviceType != ServiceType.RDS) {
                    regularPricingRequest.setSubServiceType("default");
                } else {
                    regularPricingRequest.setSubServiceType(StringUtils.isEmpty(
                            request.getInstances().get(i).getSourceInstanceId())
                            ? request.getInstances().get(i).getEngine() : "default");
                }
                regularPricingRequest.setScene(RDSConstant.SCENE_NEW);
                regularPricingRequest.setCount(request.getNumber());
                Time time = new Time();
                time.setStartTime(DateTime.now());
                time.setPeriod(PricingUtils.toPeriod(1, RDSConstant.RDS_POSTPAY_TIME_UNIT));
                regularPricingRequest.setTime(time);
                regularPricingRequest.setQueryTime(DateTime.now());
                regularPricingRequest.setChargeItemName("RunningTimeMinutes");
                regularPricingRequest.setRegion(regionConfiguration.getCurrentRegion());
                regularPricingRequest.setFlavor(flavor);
                com.baidu.bce.pricing.service.model.query.PricingQueryResponse regularPrice
                        = pricingQueryService.getRegularPrice(regularPricingRequest);
                prices.put("price", new BigDecimal(regularPrice.getPrice().stripTrailingZeros().toPlainString()));
                prices.put("catalogPrice",
                        new BigDecimal(regularPrice.getCatalogPrice().stripTrailingZeros().toPlainString()));

            }

            result.add(new Price().withPrice(prices.get("price")).withCatalogPrice(prices.get("catalogPrice")));
        }
        return result;
    }

    private void checkInstanceConfigInfo(List<FlavorItem> flavorElements,
                                         BatchPriceRequest.BatchCreateInstance batchCreateInstance,
                                         String subServiceTypeFlavorItemValue) {

        if (RDSConstant.RDS_ENGINE_PROXY.equalsIgnoreCase(batchCreateInstance.getEngine())) {
            // 代理实例询价
            FlavorItem nodeAmountChargeItem = new FlavorItem();
            nodeAmountChargeItem.setName("nodeAmount");
            nodeAmountChargeItem.setValue(batchCreateInstance.getNodeAmount().toString());
            nodeAmountChargeItem.setScale(new BigDecimal(1));
            flavorElements.add(nodeAmountChargeItem);
            // 支持通用型代理
            if (StringUtils.isNotEmpty(batchCreateInstance.getResourceType())
                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(batchCreateInstance.getResourceType())) {
                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL;
            }
        } else {
            // 此处只查询计算资源价格，不涉及存储
            if (ObjectUtils.equals("high_io", batchCreateInstance.getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(0)));
                // 通用ssd云磁盘新增计费项
            } else if (ObjectUtils.equals("cloud_nor", batchCreateInstance.getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(0)));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", batchCreateInstance.getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(0)));
            } else if (ObjectUtils.equals("cloud_enha", batchCreateInstance.getDiskIoType())) {
                flavorElements.add(new FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(0)));
            } else {
                flavorElements.add(new FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(0)));
            }
            FlavorItem cpuChargeItem = new FlavorItem();
            cpuChargeItem.setName("cpu");
            cpuChargeItem.setValue(batchCreateInstance.getCpuCount() + "");
            cpuChargeItem.setScale(new BigDecimal(1));
            flavorElements.add(cpuChargeItem);
            FlavorItem memoryChargeItem = new FlavorItem();
            memoryChargeItem.setName("memory");
            memoryChargeItem.setValue(batchCreateInstance.getAllocatedMemoryInGB() + "g");
            memoryChargeItem.setScale(new BigDecimal(1));
            flavorElements.add(memoryChargeItem);
            FlavorItem subServiceTypeChargeItem = new FlavorItem();
            subServiceTypeChargeItem.setName("subServiceType");
            subServiceTypeChargeItem.setValue(subServiceTypeFlavorItemValue);
            flavorElements.add(subServiceTypeChargeItem);
        }
    }

    /**
     * 校验商品类型
     * @param batchCreateInstance
     * @param from
     * @return
     */
    private String checkSubServiceType(BatchPriceRequest request,
                                       BatchPriceRequest.BatchCreateInstance batchCreateInstance, String from) {
        String subServiceTypeFlavorItemValue = "";
        String engine = batchCreateInstance.getEngine().toLowerCase();
        switch (engine) {
            case RDSConstant.RDS_ENGINE_MYSQL_LOWER_CASE:
                subServiceTypeFlavorItemValue = "MySQL";
                break;
            case RDSConstant.RDS_ENGINE_PG:
                subServiceTypeFlavorItemValue = "postgresql";
                break;
            case RDSConstant.RDS_ENGINE_SQLSERVER:
                subServiceTypeFlavorItemValue = "sqlserver";
                break;
            case RDSConstant.RDS_ENGINE_PROXY:
                if (StringUtils.isNotEmpty(batchCreateInstance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(batchCreateInstance.getResourceType())) {
                    // 通用型代理
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL;
                } else {
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                }
                break;
            case RDSConstant.INSTANCE_TYPE_REPLICA_LOWER_CASE:
                if (StringUtils.isNotEmpty(batchCreateInstance.getCategory())
                        && batchCreateInstance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
                    //后付费 单机版 独占型与通用型价格一致
                    if (StringUtils.isNotEmpty(request.getProductType())
                            && Payment.isPostpay(request.getProductType())) {
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    } else {
                        if (StringUtils.isNotEmpty(batchCreateInstance.getResourceType())
                                && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                batchCreateInstance.getResourceType())) {
                            // 预付费通用单机
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                        } else {
                            // 预付费独占单机 默认
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                        }
                    }
                } else {
                    //后付费 双机版
                    if (StringUtils.isNotEmpty(request.getProductType())
                            && Payment.isPostpay(request.getProductType())) {
                        if (StringUtils.isNotEmpty(batchCreateInstance.getResourceType())
                                && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                batchCreateInstance.getResourceType())) {
                            // 通用型
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                        } else {
                            // 独占型 默认
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                        }
                    } else {
                        if (StringUtils.isNotEmpty(batchCreateInstance.getResourceType())
                                && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                                batchCreateInstance.getResourceType())) {
                            // 预付费 通用
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                        } else {
                            // 预付费 独占 默认
                            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                        }
                    }
                }
                break;
            default:
                subServiceTypeFlavorItemValue = "default";
                break;
        }

        return subServiceTypeFlavorItemValue;
    }


    /**
     *  用于校验当前询价的实例类型 单机 双机 增强版
     * @param batchCreateInstance
     * @param from
     * @return
     */
    private String checkInstanceType(BatchPriceRequest.BatchCreateInstance batchCreateInstance, String from) {

        String mutiEngine = "";
        if (StringUtils.isNotEmpty(batchCreateInstance.getResourceType())
                && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(batchCreateInstance.getResourceType())
                && StringUtils.isNotEmpty(batchCreateInstance.getEngine()) &&
                RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(batchCreateInstance.getEngine())) {
            if ((StringUtils.isNotEmpty(batchCreateInstance.getCategory())  // 单机版的情况
                    && batchCreateInstance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON))) {
                // 此处单机版价格暂无，默认与原单机版保持一致，TODO
                mutiEngine = batchCreateInstance.getEngine() + "_singleton";
            } else {
                mutiEngine = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
            }
            return mutiEngine;
        }
        if (batchCreateInstance.getEngine().equalsIgnoreCase((RDSConstant.RDS_ENGINE_SQLSERVER))
                && StringUtils.isNotEmpty(batchCreateInstance.getCategory()) // pg单机版情况
                && batchCreateInstance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // 处理sqlserver单机版的情况
            mutiEngine = batchCreateInstance.getEngine() + "_singleton";
        } else if ((batchCreateInstance.getIsEnhanced() != null
                && batchCreateInstance.getIsEnhanced()) // 处理MySQL三节点增强版的情况
                || (StringUtils.isNotEmpty(batchCreateInstance.getInstanceType())  // raft版的情况
                && batchCreateInstance.getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT))) {
            mutiEngine = batchCreateInstance.getEngine().toLowerCase() + "_finance";
        } else if (batchCreateInstance.getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)
                && StringUtils.isNotEmpty(batchCreateInstance.getCategory())  // 单机版的情况
                && batchCreateInstance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
            // 这里没用RDSConstant.INSTANCE_TYPE_RDS_SINGLE
            mutiEngine = batchCreateInstance.getEngine() + "_singleton";
        } else if (batchCreateInstance.getEngine().equalsIgnoreCase((RDSConstant.RDS_ENGINE_PG))
                && StringUtils.isNotEmpty(batchCreateInstance.getCategory()) // pg单机版情况
                && batchCreateInstance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) {
            mutiEngine = batchCreateInstance.getEngine() + "_singleton";
        } else {
            mutiEngine = batchCreateInstance.getEngine();
        }

        return mutiEngine;

    }
}
