package com.baidu.bce.logic.rds.service.orderexecutor;


import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


@Service
public class RdsRenewOrderExecuteService extends AbstractRdsExecutorService {


    @Value("${sms.order.create.success.tpl.id:Tpl_3c9014ff-0670-4aad-b362-a3b6130ee94e}")
    private String smsCreateSuccessId;

    @Value("${sms.order.create.failed.tpl.id:Tpl_2ef56822-636c-4c7d-9a0c-919925b5dd8a}")
    private String smsCreateFailedId;

    @Autowired
    InstanceDao instanceDao;

    @Autowired
    private LogicRdsClientFactory clientFactory;



    @Override
    public ExecutionResult execute(OrderClient orderClient, ResourceClient resourceClient, Order order) {
        ExecutionResult executionResult = new ExecutionResult();

        // 账户余额检查
        updateOrderStatus(orderClient, order, OrderStatus.CREATED);

        executionResult.setExecutionStatus(ExecutionStatus.SUCCESS);
        // 更新为 created 成功, 发送短信
        // executionResult.setMessageCenterModels(getSuccessMessage(order));

        return executionResult;
    }

    @Override
    public ExecutionResult check(OrderClient orderClient, ResourceClient resourceClient, Order order) {
        ExecutionResult executionResult = new ExecutionResult();
        executionResult.setExecutionStatus(ExecutionStatus.SUCCESS);
        return executionResult;
    }

    private boolean updateOrderStatus(OrderClient client, Order order, OrderStatus status) {
        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setStatus(status);
        updateOrderRequest.setServiceType(order.getServiceType());
        client.update(order.getUuid(), updateOrderRequest);
        return true;
    }


    @Override
    protected String getMessageTpl(boolean succeed) {
        return succeed ? smsCreateSuccessId : smsCreateFailedId;
    }
}
