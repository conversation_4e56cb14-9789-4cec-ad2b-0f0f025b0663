package com.baidu.bce.logic.rds.service.util;

import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logical.tag.sdk.common.TagComparator;
import com.baidu.bce.logical.tag.sdk.model.Tag;

import java.util.List;

/**
 * Created by luping03 on 17/7/6.
 */
public class TagOfInstanceAbstractComparator extends TagComparator<InstanceAbstract> {
    public TagOfInstanceAbstractComparator(boolean isAscend) {
        super(isAscend);
    }

    @Override
    public List<Tag> getO1Tags(InstanceAbstract o1) {
        return o1.getTags();
    }

    @Override
    public List<Tag> getO2Tags(InstanceAbstract o2) {
        return o2.getTags();
    }
}
