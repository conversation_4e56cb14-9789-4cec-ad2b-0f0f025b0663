package com.baidu.bce.logic.rds.service.idmapper;

import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.IdMapPO;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by luping03 on 17/12/29.
 */
@Component
public class IdMapperService {

    @Autowired
    InstanceDao instanceDao;

    public Map<String, String> idMapByLongId(List<String> longIds) {
        if (longIds == null || longIds.size() == 0) {
            return new HashMap<>();
        }
        List<IdMapPO> idMapPOS = instanceDao.idMapList(longIds);
        Map<String, String> idMap = new HashMap<>();
        for (IdMapPO idMapPO : idMapPOS) {
            idMap.put(idMapPO.getInstanceUuid(), idMapPO.getInstanceId());
        }
        return idMap;
    }

    public String getInstanceId(String instanceId) {
        if (StringUtils.isEmpty(instanceId)) {
            return null;
        }
        if (BasisUtils.isShortId(instanceId)) {
            return instanceId;
        }
        return instanceDao.queryInstanceId(instanceId);
    }

    public String getInstanceUuid(String instanceId) {
        if (StringUtils.isEmpty(instanceId)) {
            return null;
        }
        if (BasisUtils.isLongId(instanceId)) {
            return instanceId;
        }
        return instanceDao.queryInstanceUuid(instanceId);
    }
}
