package com.baidu.bce.logic.rds.service.util;

import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateFlavorRequest;
import com.baidu.bce.internalsdk.rds.model.instance.OrderItemExtraInfo;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/8/4 11:25
 * 用于查询当前订单是否来源于线上测试环境
 */
public class OrderEnvUtil {
    public static String getEnvFromNewOrder(Order order) {
        String env = null;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            env = objectMapper.readValue(order.getItems().get(0).getExtra(), OrderItemExtraInfo.class).getEnv();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return env;
    }
    public static String getEnvFromResizeOrder(Order order) {
        String env = null;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            env = objectMapper.readValue(order.getItems().get(0).getExtra(),
                    InstanceUpdateFlavorRequest.class).getEnv();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return env;
    }
}
