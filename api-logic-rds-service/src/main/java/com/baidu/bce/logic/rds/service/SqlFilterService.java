package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.model.SqlFilterAllowedResponse;
import com.baidu.bce.internalsdk.rds.model.SqlFilterList;
import com.baidu.bce.internalsdk.rds.model.SqlFilterRequest;
import com.baidu.bce.internalsdk.rds.model.SqlFilterResponse;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SqlFilterService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SqlFilterService.class);

    @Autowired
    LogicRdsClientFactory rdsClientFactory;

    @Autowired
    private InstanceService instanceService;

    public SqlFilterList sqlFilterList(String instanceId) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return rdsClientFactory.createRdsClient().sqlFilterList(instanceId);
    }

    public SqlFilterResponse sqlFilterDetail(String instanceId, String sqlFilterId) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return rdsClientFactory.createRdsClient().sqlFilterDetail(instanceId, sqlFilterId);
    }

    public void addSqlFilter(String instanceId, SqlFilterRequest request) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        rdsClientFactory.createRdsClient().addSqlFilter(instanceId, request);
    }

    public void updateSqlFilter(String instanceId, String sqlFilterId, SqlFilterRequest request) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        rdsClientFactory.createRdsClient().updateSqlFilter(instanceId, sqlFilterId, request);
    }

    public void actionSqlFilter(String instanceId, String sqlFilterId, SqlFilterRequest request) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        rdsClientFactory.createRdsClient().actionSqlFilter(instanceId, sqlFilterId, request);
    }

    public void deleteSqlFilter(String instanceId, String sqlFilterId) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        rdsClientFactory.createRdsClient().deleteSqlFilter(instanceId, sqlFilterId);
    }

    public SqlFilterAllowedResponse allowedSqlFilter(String instanceId) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return rdsClientFactory.createRdsClient().allowedSqlFilter(instanceId);
    }
}
