package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.CheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.AccountPrivilege;
import com.baidu.bce.internalsdk.rds.model.database.Database;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCheckExistRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseChecksizeResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseCreateRequest;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseGetResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListV2Response;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.database.GetTableSizeRequest;
import com.baidu.bce.internalsdk.rds.model.database.GetTableSizeResponse;
import com.baidu.bce.internalsdk.rds.model.database.ListDatabasesRequest;
import com.baidu.bce.internalsdk.rds.model.database.ListDatabasesResponse;
import com.baidu.bce.internalsdk.rds.model.database.ListTableRequest;
import com.baidu.bce.internalsdk.rds.model.database.ListTableResponse;
import com.baidu.bce.internalsdk.rds.model.database.TableLevelListDatabaseResponse;
import com.baidu.bce.internalsdk.rds.model.database.TableListResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseOwnerRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.logic.rds.service.model.IsExistResponse;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * Created by luping03 on 17/10/11.
 */
@Service
public class DatabaseService {

    @Autowired
    LogicRdsClientFactory logicRdsClientFactory;

    @Autowired
    AccountService accountService;

    public DatabaseListResponse list(String instanceId, String from) {
        DatabaseListResponse response =
                logicRdsClientFactory.createRdsClientByInstanceId(instanceId).databaseList(instanceId);
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            standardForApi(response.getDatabases());
        }
        return response;
    }

    // 查询单个数据库的详细信息
    public DatabaseGetResponse detail(String instanceId, String dbName) {
        return logicRdsClientFactory.createRdsClientByInstanceId(instanceId).databaseDescribe(instanceId, dbName);
    }

    public void create(String instanceId, Database database, String from) {
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            checkCreateDatabase(database);
        }
        DatabaseCreateRequest request = new DatabaseCreateRequest(database);
        RDSClient client = logicRdsClientFactory.createRdsClientByInstanceId(instanceId);
        client.databaseCreate(instanceId, request);

        // pg engine 需要跳过逻辑
        InstanceGetResponse instanceGetResponse = logicRdsClientFactory.createRdsClient2ByInstanceId(instanceId)
                .instanceDescribe(instanceId);
        if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getEngine())
                && RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(instanceGetResponse.getInstance().getEngine())) {
            return;
        }
        for (AccountPrivilege accountPrivilege : database.getAccountPrivileges()) {
            //pull account privileges
            Account account = client.accountDescribe(instanceId, accountPrivilege.getAccountName()).getAccount();
            Account.DatabasePrivilege databasePrivilege = new Account.DatabasePrivilege();
            databasePrivilege.setDbName(database.getDbName());
            databasePrivilege.setAuthType(accountPrivilege.getAuthType());
            account.getDatabasePrivileges().add(databasePrivilege);
            //update account privileges
            if (logicRdsClientFactory.getInstanceTypeByUuid(instanceId)
                    .equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) { // 处理raft版
                client.accountModifyPrivilegeRaft(instanceId,
                        accountPrivilege.getAccountName(), account.getDatabasePrivileges(), account.getETag());
            } else {
                client.accountModifyPrivilege(instanceId,
                        accountPrivilege.getAccountName(), account.getDatabasePrivileges(), account.getETag());
            }
        }
    }

    public void updateRemark(String instanceId, String dbName, DatabaseUpdateRemarkRequest remarkRequest) {
        if (logicRdsClientFactory.getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            logicRdsClientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2)
                    .databaseModifyRemarkRaft(instanceId, dbName, remarkRequest);
        } else {
            logicRdsClientFactory.createRdsClient()
                    .databaseModifyRemark(instanceId, dbName, remarkRequest);
        }
    }

    public void delete(String instanceId, String dbName) {

        logicRdsClientFactory.createRdsClientByInstanceId(instanceId).databaseDelete(instanceId, dbName);


    }

    public IsExistResponse databaseCheck(String instanceId, String dbName) {
        IsExistResponse response = new IsExistResponse();
        CheckExistResponse checkResponse = null;
        if (logicRdsClientFactory.getInstanceTypeByUuid(instanceId)
                .equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) { // raft
            checkResponse = logicRdsClientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2)
                    .dataBaseCheckRaft(instanceId, dbName);
        } else {
            checkResponse = logicRdsClientFactory.createRdsClient()
                    .dataBaseCheck(instanceId, dbName);
        }
        if (checkResponse.getIsExist() == 0) {
            response.setExist(Boolean.FALSE);
        } else {
            response.setExist(Boolean.TRUE);
        }
        return response;
    }

    public DatabaseChecksizeResponse checksize(String instanceId) {
        return logicRdsClientFactory.createRdsClientByInstanceId(instanceId)
                .checksize(instanceId);
    }

    public DatabaseListV2Response listdatabases(String instanceId) {
        return logicRdsClientFactory.createRdsClientByInstanceId(instanceId)
                .listdatabases(instanceId);
    }

    public ListDatabasesResponse listDatabasesv2(ListDatabasesRequest request) {
        return logicRdsClientFactory.createRdsClient2()
                .listDatabasesV2(request);
    }
    public TableLevelListDatabaseResponse tableLevelListdatabasesV2(ListTableRequest request) {
        return logicRdsClientFactory.createRdsClient2()
                .tableLevelListdatabasesV2(request);
    }

    public TableListResponse listtables(String instanceId, String pattern, String dbName) {
        return logicRdsClientFactory.createRdsClientByInstanceId(instanceId)
                .listtables(instanceId, pattern, dbName);
    }

    public ListTableResponse listTablesV2(ListTableRequest request) {
        return logicRdsClientFactory.createRdsClient2()
                .listTableV2(request);
    }

    public GetTableSizeResponse getTableSize(GetTableSizeRequest request) {
        return logicRdsClientFactory.createRdsClient2()
                .getTableSize(request);
    }
    public DatabaseCheckExistResponse checkdbexist(String instanceId, DatabaseCheckExistRequest request) {
        return logicRdsClientFactory.createRdsClientByInstanceId(instanceId)
                .checkdbexist(instanceId, request);
    }

    public void databaseModifyOwner(String instanceId, String dbName,
                                                          DatabaseOwnerRequest request) {
        logicRdsClientFactory.createRdsClientByInstanceId(instanceId)
                .databaseModifyOwner(instanceId, dbName, request);
    }

    public void checkCreateDatabase(Database database) {
        // AccountPrivileges 参数校验
        if (database.getAccountPrivileges() != null && database.getAccountPrivileges().size() != 0) {
            for (AccountPrivilege privilege : database.getAccountPrivileges()) {
                privilege.setAuthType(BasisUtils.lowerCaseFirstChar(privilege.getAuthType()));
            }
        }
    }

    private void standardForApi(Collection<Database> databases) {
        if (databases == null || databases.size() == 0) {
            return;
        }
        for (Database database : databases) {
            standardForApi(database);
        }
    }

    private void standardForApi(Database database) {
        if (database != null) {
            for (AccountPrivilege privilege : database.getAccountPrivileges()) {
                privilege.setAuthType(BasisUtils.upperCaseFirstChar(privilege.getAuthType()));
            }
        }
    }
}
