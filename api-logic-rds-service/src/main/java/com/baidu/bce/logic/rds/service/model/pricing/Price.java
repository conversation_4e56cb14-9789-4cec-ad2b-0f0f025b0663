package com.baidu.bce.logic.rds.service.model.pricing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * Created by luping03 on 17/11/7.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Price {
    private BigDecimal price;
    private BigDecimal catalogPrice;
    private BigDecimal trafficInGB = new BigDecimal(0);
    private BigDecimal discount;
    private BigDecimal realCatalogPrice;
    private String backupInGB = "0";
    @Min(0)
    @Max(100)
    private BigDecimal discountRate = new BigDecimal(100);

    @Override
    public String toString() {
        return "Price{" +
                "price=" + price +
                ", trafficInGB=" + trafficInGB +
                ", backupInGB=" + backupInGB +
                '}';
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public Price withDiscountRate(final BigDecimal discountRate) {
        this.discountRate = discountRate;
        return this;
    }

    public Price withTrafficInGB(final BigDecimal trafficInGB) {
        this.trafficInGB = trafficInGB;
        return this;
    }


    public Price withPrice(final BigDecimal price) {
        this.price = price;
        return this;
    }

    public Price withCatalogPrice(final BigDecimal catalogPrice) {
        this.catalogPrice = catalogPrice;
        return this;
    }

    public Price withBackupInGB(final String backupInGB) {
        this.backupInGB = backupInGB;
        return this;
    }

    public Price withDiscount(final BigDecimal discount) {
        this.discount = discount;
        return this;
    }


    public Price withRealCatalogPrice(final BigDecimal realCatalogPrice) {
        this.realCatalogPrice = realCatalogPrice;
        return this;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public BigDecimal getRealCatalogPrice() {
        return realCatalogPrice;
    }

    public void setRealCatalogPrice(BigDecimal realCatalogPrice) {
        this.realCatalogPrice = realCatalogPrice;
    }

    public BigDecimal getTrafficInGB() {
        return trafficInGB;
    }

    public void setTrafficInGB(BigDecimal trafficInGB) {
        this.trafficInGB = trafficInGB;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getCatalogPrice() {
        return catalogPrice;
    }

    public void setCatalogPrice(BigDecimal catalogPrice) {
        this.catalogPrice = catalogPrice;
    }

    public String getBackupInGB() {
        return backupInGB;
    }

    public void setBackupInGB(String backupInGB) {
        this.backupInGB = backupInGB;
    }
}
