package com.baidu.bce.logic.rds.service;

import com.baidu.bce.externalsdk.logical.network.subnet.model.ListSubnetVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.SimpleVpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.VpcIdsRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.CreateRoGroupRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.CreateRoGroupResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupDetailResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupLeaveRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupListResponse;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateEnableDelayOffRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateIsBalanceReloadRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdatePubliclyAccessibleRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.RoGroupUpdateWeightRequest;
import com.baidu.bce.internalsdk.rds.model.rogroup.UpdateRoGroupPropertyRequest;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.user.settings.sdk.model.FeatureAclRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class RoGroupService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RoGroupService.class);

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    InstanceDao instanceDao;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private RdsVpcWrapperService rdsVpcWrapperService;

    @Autowired
    private EdgeService edgeService;

    private String vpcId2VpcUuid(String vpcId) {
        if (rdsVpcWrapperService.isEdgeRegion()) {
            // 边缘计算区域，不论是 ConsoleAPI 还是 OpenAPI，接收的都是短 ID，而非长 ID
            return edgeService.getVpc(vpcId).getVpcUuid();
        }
        // 非边缘计算区域，通常逻辑是：ConsoleAPI 接收的是长 ID，OpenAPI 接收的是短 ID，后续更一致的做法是在 OpenAPI Controller 中
        //  做完转换逻辑，Service 中使用统一的长 ID 格式。
        Map<String, SimpleVpcVo> simpleVpcVoMap = clientFactory
                .createVpcClient().get(new VpcIdsRequest(Collections.singletonList(vpcId)));
        for (Map.Entry<String, SimpleVpcVo> entry : simpleVpcVoMap.entrySet()) {
            if (entry.getValue().getShortId().equals(vpcId)) {
                return entry.getValue().getVpcId();
            }
        }
        return vpcId;
    }

    private String subnetId2SubnetUuid(String vpcUuid, String subnetId) {
        if (rdsVpcWrapperService.isEdgeRegion()) {
            // 边缘计算区域，不论是 ConsoleAPI 还是 OpenAPI，接收的都是短 ID，而非长 ID
            return edgeService.getSubnet(subnetId).getSubnetUuid();
        }
        // 非边缘计算区域，通常逻辑是：ConsoleAPI 接收的是长 ID，OpenAPI 接收的是短 ID，后续更一致的做法是在 OpenAPI Controller 中
        //  做完转换逻辑，Service 中使用统一的长 ID 格式。
        // subnetId 短ID 转 长ID
        List<ListSubnetVo> subnetVos = clientFactory.createSubnetClient().getSubnetsByVpcId(vpcUuid);
        for (ListSubnetVo subnetVo : subnetVos) {
            if (subnetVo.getShortId().equals(subnetId)) {
                return subnetVo.getSubnetId();
            }
        }
        return subnetId;
    }

    public CreateRoGroupResponse roGroupCreate(CreateRoGroupRequest request) {
        request.setSourceAppId(shortId(request.getSourceAppId()));

        String vpcId = request.getVpcId();
        String subnetId = request.getSubnetId();

        request.setVpcId(vpcId2VpcUuid(vpcId));
        request.setSubnetId(subnetId2SubnetUuid(request.getVpcId(), subnetId));

        CreateRoGroupResponse createRoGroupResponse = clientFactory.createRdsClient().roGroupCreate(request);
        createRoGroupResponse.setVpcId(vpcId);
        createRoGroupResponse.setSubnetId(subnetId);

        return createRoGroupResponse;
    }

    public RoGroupDetailResponse roGroupDetail(String sourceAppId, String roGroupId) {
        sourceAppId = shortId(sourceAppId);
        return clientFactory.createRdsClient().roGroupDetail(sourceAppId, roGroupId);
    }

    public RoGroupListResponse roGroupList(String instanceId) {
        instanceId = shortId(instanceId);
        RoGroupListResponse roGroupList = clientFactory.createRdsClient().roGroupList(instanceId);
        setRogroupVpcAndSubnetInstanceExtension(roGroupList);
        return roGroupList;
    }
    public String shortId(String sourceAppId){
        if (BasisUtils.isShortId(sourceAppId)) {
            sourceAppId = instanceService.findInsntaceUUidByShortId(sourceAppId);
        }
        return sourceAppId;
    }
    public void setRogroupVpcAndSubnetInstanceExtension(RoGroupListResponse roGroupList) {
        if (CollectionUtils.isEmpty(roGroupList.getRoGroupList())) {
            return;
        }

        for (RoGroupDetailResponse roGroup : roGroupList.getRoGroupList()) {
            roGroup.setInstanceShortId(instanceDao.queryInstanceId(roGroup.getSourceAppId()));
        }

        Set<String> vpcUuids = new HashSet<>();
        Set<String> subnetUuids = new HashSet<>();
        for (RoGroupDetailResponse roGroup : roGroupList.getRoGroupList()) {
            if (StringUtils.isEmpty(roGroup.getVpcId())) {
                continue;
            }
            vpcUuids.add(roGroup.getVpcId());
            if (StringUtils.isEmpty(roGroup.getSubnetId())) {
                continue;
            }
            subnetUuids.add(roGroup.getSubnetId());
        }
        Map<String, SimpleVpcVo> vpcUuidSimpleVpcVoMap = rdsVpcWrapperService.getVpcUuidSimpleVpcVoMap(vpcUuids);
        Map<String, SubnetVo> subnetUuidSubnetVoMap = rdsVpcWrapperService.getSubnetUuidSubnetVoMap(subnetUuids);
        for (RoGroupDetailResponse roGroup : roGroupList.getRoGroupList()) {
            if (StringUtils.isEmpty(roGroup.getVpcId())) {
                continue;
            }
            if (vpcUuidSimpleVpcVoMap.containsKey(roGroup.getVpcId())) {
                roGroup.setVpcVo(vpcUuidSimpleVpcVoMap.get(roGroup.getVpcId()));
            }

            if (StringUtils.isEmpty(roGroup.getSubnetId())) {
                continue;
            }
            if (subnetUuidSubnetVoMap.containsKey(roGroup.getSubnetId())) {
                roGroup.setSubnetVo(subnetUuidSubnetVoMap.get(roGroup.getSubnetId()));
            }
        }
    }

    public void deleteRoGroup(String instanceId, String roGroupId) {
        instanceId = shortId(instanceId);
        clientFactory.createRdsClient().deleteRoGroup(instanceId, roGroupId);
    }

    public void roGroupUpdateName(String sourceAppId, String roGroupId, RoGroupUpdateRequest request) {
        sourceAppId = shortId(sourceAppId);
        clientFactory.createRdsClient().roGroupUpdateName(sourceAppId, roGroupId, request);
    }

    public void roGroupUpdateEndpoint(String sourceAppId, String roGroupId, RoGroupUpdateRequest request) {
        sourceAppId = shortId(sourceAppId);
        clientFactory.createRdsClient().roGroupUpdateEndpoint(sourceAppId, roGroupId, request);
    }

    public void roGroupUpdatePubliclyAccessible(String sourceAppId, String roGroupId
            , RoGroupUpdatePubliclyAccessibleRequest request) {
        sourceAppId = shortId(sourceAppId);
        if (request.isPubliclyAccessible()) {
            FeatureAclRequest aclRequest = new FeatureAclRequest("EipBlackList",
                    null, "AccountId", clientFactory.getAccountId());
            if (clientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist()) {
                throw new RDSExceptions.NotSupportOperation();
            }
        }
        clientFactory.createRdsClient().roGroupUpdatePubliclyAccessible(sourceAppId, roGroupId, request);
    }

    public void roGroupUpdateRoGroupProperty(String sourceAppId, String roGroupId
            , UpdateRoGroupPropertyRequest request) {
        sourceAppId = shortId(sourceAppId);
        if (request.getReadReplicaList() != null && !request.getReadReplicaList().isEmpty()) {
            for (int i = 0; i < request.getReadReplicaList().size(); i++) {
                request.getReadReplicaList().get(i).setAppId(
                        shortId(request.getReadReplicaList().get(i).getAppId())
                );
            }
        }
        clientFactory.createRdsClient().roGroupUpdateRoGroupProperty(sourceAppId, roGroupId, request);
    }

    public void roGroupUpdateEnableDelayOff(String sourceAppId, String roGroupId
            , RoGroupUpdateEnableDelayOffRequest request) {
        sourceAppId = shortId(sourceAppId);
        clientFactory.createRdsClient().roGroupUpdateEnableDelayOff(sourceAppId, roGroupId, request);
    }

    public void roGroupUpdateIsBalanceReload(String sourceAppId, String roGroupId
            , RoGroupUpdateIsBalanceReloadRequest request) {
        sourceAppId = shortId(sourceAppId);
        clientFactory.createRdsClient().roGroupUpdateIsBalanceReload(sourceAppId, roGroupId, request);
    }

    public void roGroupUpdateLeastAppAmount(String sourceAppId, String roGroupId, RoGroupUpdateRequest request) {
        sourceAppId = shortId(sourceAppId);
        clientFactory.createRdsClient().roGroupUpdateLeastAppAmount(sourceAppId, roGroupId, request);
    }

    public void roGroupUpdateDelayThreshold(String sourceAppId, String roGroupId, RoGroupUpdateRequest request) {
        sourceAppId = shortId(sourceAppId);
        clientFactory.createRdsClient().roGroupUpdateDelayThreshold(sourceAppId, roGroupId, request);
    }

    public void roGroupUpdateReload(String sourceAppId, String roGroupId) {
        sourceAppId = shortId(sourceAppId);
        clientFactory.createRdsClient().roGroupUpdateReload(sourceAppId, roGroupId);
    }

    public void roGroupUpdateWeight(String sourceAppId, String roGroupId, RoGroupUpdateWeightRequest request) {
        sourceAppId = shortId(sourceAppId);
        clientFactory.createRdsClient().roGroupUpdateWeight(sourceAppId, roGroupId, request);
    }

    public void roGroupUpdateJoin(String sourceAppId, String roGroupId, RoGroupUpdateWeightRequest request) {
        sourceAppId = shortId(sourceAppId);
        if (request.getReadReplicaList() != null && !request.getReadReplicaList().isEmpty()) {
            for (int i = 0; i < request.getReadReplicaList().size() ; i++) {
                request.getReadReplicaList().get(i).setAppId(
                        shortId(request.getReadReplicaList().get(i).getAppId())
                );
            }
        }
        clientFactory.createRdsClient().roGroupUpdateJoin(sourceAppId, roGroupId, request);
    }

    public void roGroupLeave(String sourceAppId, String roGroupId, RoGroupLeaveRequest request) {
        sourceAppId = shortId(sourceAppId);
        if (request.getReadReplicaList() != null && !request.getReadReplicaList().isEmpty()) {
            for (int i = 0; i < request.getReadReplicaList().size() ; i++) {
                request.getReadReplicaList().set(i , shortId( request.getReadReplicaList().get( i )));
            }
        }
        clientFactory.createRdsClient().roGroupLeave(sourceAppId, roGroupId, request);
    }
}
