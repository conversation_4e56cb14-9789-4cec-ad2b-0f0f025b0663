package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.model.migration.*;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.model.tag.AssignTagRequest;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.Set;
import java.util.TreeSet;

/**
 * Created by luping03 on 17/10/14.
 */
@Service
public class MigrationService {
    @Autowired
    LogicRdsClientFactory rdsClientFactory;

    public MigrationsResponse list(String instanceId, String startTime, String endTime) {
        return rdsClientFactory.createRdsMigrationClient().getMigrationList(instanceId, startTime, endTime);
    }

    public MigrationDbList dblist(String instanceId, DatabaseConfig databaseConfig) {
        return rdsClientFactory.createRdsMigrationClient().getMigrationDbList(instanceId, databaseConfig);
    }

    public MigrationId preCheck(String instanceId, MigrationConfig migrationConfig) {
        return rdsClientFactory.createRdsMigrationClient().migrationCheck(instanceId, migrationConfig);
    }

    public MigrationStatus detail(String instanceId, String migrationId) {
        return rdsClientFactory.createRdsMigrationClient().getMigrationStatus(instanceId, migrationId);
    }

    public Migration task(String instanceId) {
        Migration result = null;
        Set<String> status = new TreeSet<>(Arrays.asList("dumping", "importing", "replicating"));
        for (Migration migration : rdsClientFactory.createRdsMigrationClient()
                .getMigrationList(instanceId, null, null).getMigrations()) {
            if (status.contains(migration.getMigrationStatus())) {
                if (result != null) {
                    throw new RDSExceptions.MoreThanOneMigrationTask();
                }
                result = migration;
            }
        }
        return result;
    }

    public void create(String instanceId, String migrationId) {
        rdsClientFactory.createRdsMigrationClient().createMigrationTask(instanceId, migrationId);
    }

    public void stop(String instanceId, String migrationId) {
        rdsClientFactory.createRdsMigrationClient().stopMigrationTask(instanceId, migrationId);
    }

    public void cancel(String instanceId, String migrationId) {
        rdsClientFactory.createRdsMigrationClient().cancelMigrationTask(instanceId, migrationId);
    }
}
