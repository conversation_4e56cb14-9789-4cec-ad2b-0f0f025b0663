package com.baidu.bce.logic.rds.service.datasync;

import com.baidu.bce.internalsdk.trail.util.StringUtil;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.dao.model.NeedToSyncPO;
import com.baidu.bce.logic.rds.service.datasync.service.NeedToSyncService;
import com.baidu.bce.logic.rds.service.datasync.service.NonOrderDataSyncService;
import endpoint.EndpointManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 非订单类(异步操作)数据同步的执行器
 * <p>
 * Created by luping03 on 2017/12/29.
 */
@EnableScheduling
@Configuration
@Profile("default")
public class NonOrderDataSyncScheduler extends AbstractAutoScheduler {

    private static final Logger LOGGER = LoggerFactory.getLogger(NonOrderDataSyncScheduler.class);

    @Autowired
    NeedToSyncService needToSyncService;

    @Autowired
    NonOrderDataSyncService nonOrderDataSyncService;

    @Autowired
    RegionConfiguration regionConfiguration;

    @Value("${nonOrder.dataSync.scheduler.switch:true}")
    boolean nonOrderDataSyncSwitch;

    /**
     * 1.遍历每一条记录并尝试锁定, lock_time<t0 & lockId为空，或lock_time超过锁定上限 & lockId不为空
     * 2.进行处理每条记录
     * 3.释放锁或删除记录
     */
    @Override
    public void doFixedDelayJob() {
        if (!nonOrderDataSyncSwitch) {
            LOGGER.debug("NonOrderDataSync switch down.");
            return;
        }
        LOGGER.debug("NonOrderDataSync Begin.");
        EndpointManager.setThreadRegion(regionConfiguration.getCurrentRegion());

        Date lockDate = new Date();
        Date outLockDate = getOutLockDate(lockDate);
        List<NeedToSyncPO> needToSyncList = needToSyncService.findSyncData(outLockDate);

        if (CollectionUtils.isEmpty(needToSyncList)) {
            return;
        }
        int completeDataCount = 0;
        List<String> completeData = new ArrayList<>();
        for (NeedToSyncPO needToSync : needToSyncList) {

            String instanceUuid = needToSync.getInstanceUuid();
            String lockId = UUID.randomUUID().toString();
            lockDate = new Date();

            try {
                int result;
                if (StringUtil.isBlank(needToSync.getLockId())) {
                    result = needToSyncService.lockOneLine(
                            needToSync.getId(), lockDate, lockId, needToSync.getLockTime());
                    if (result > 0) {
                        LOGGER.debug("#lock One NeedToSync uuid:{},recordDate:{},lockId:{},result:{}",
                                instanceUuid, needToSync.getT0(), lockId, result);
                    }
                } else {
                    outLockDate = getOutLockDate(lockDate);
                    result = needToSyncService.seizeLockOneLine(needToSync.getId(), outLockDate, lockDate, lockId);
                    if (result > 0) {
                        LOGGER.debug("#seizeLock One NeedToSync uuid:{},recordDate:{},lockDate:{},outLockDate:{},"
                                        + "lockId:{},result:{}", instanceUuid, needToSync.getT0(),
                                lockDate, outLockDate, lockId, result);
                    }
                }
                if (result <= 0) {
                    continue;
                }
                needToSync.setLockTime(new Timestamp(lockDate.getTime()));
                needToSync.setLockId(lockId);
                result = continueJobAfterLock(needToSync);
                completeDataCount += result;
                unLockRecord(result, needToSync, instanceUuid, completeData);
            } catch (Exception e) {
                LOGGER.error("doFixedDelayJob:ERROR uuid:{},recordDate:{},lockDate:{},"
                        + "lockId:{}, ERROR:{}", instanceUuid, needToSync.getT0(), lockDate, lockId, e);
            }
            LOGGER.debug("NonOrderDataSync#SYNC_NON_ORDER_OVER result:{}, syncId:{}", completeDataCount,
                    completeData);
        }
        LOGGER.debug("NonOrderDataSync Begin.");
    }

    /**
     * 释放锁
     *
     * @param needToSync
     * @param resourceUUid
     */
    private void unLockRecord(int result, NeedToSyncPO needToSync, String resourceUUid, List<String> completeData) {
        if (result == 1) {
            needToSyncService.unLockOneLine(needToSync);
            completeData.add(resourceUUid);
        }
    }

    /**
     * 获得锁之后，该做具体的数据同步了
     * <p>
     * 结果:释放锁
     *
     * @param needToSync
     * @return
     */
    private int continueJobAfterLock(NeedToSyncPO needToSync) {

        return nonOrderDataSyncService.syncData(needToSync);
    }

    @Override
    public String currentThreadName() {
        return "NonOrderDataSyncScheduler";
    }
}
