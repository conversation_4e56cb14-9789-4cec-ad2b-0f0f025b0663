package com.baidu.bce.logic.rds.service.util;

import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPool {

    private static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(10, 300,
            3, TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(100),
            new ThreadFactoryBuilder().setDaemon(true).setNameFormat("LogicRDSServiceThreadPool-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    public static <T> Future<T> submit(final Callable<T> task) {
        final String requestId = BceInternalRequest.getThreadRequestId();
        return EXECUTOR.submit(new Callable<T>() {
            @Override
            public T call() throws Exception {
                BceInternalRequest.setThreadRequestId(requestId);
                try {
                    return task.call();
                } finally {
                    BceInternalRequest.removeThreadRequestId();
                }
            }
        });
    }

    public static void shutdown() {
        EXECUTOR.shutdown();
    }
}
