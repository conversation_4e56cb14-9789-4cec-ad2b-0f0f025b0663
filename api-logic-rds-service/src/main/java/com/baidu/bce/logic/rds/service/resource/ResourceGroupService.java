package com.baidu.bce.logic.rds.service.resource;

import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceListResponse;
import com.baidu.bce.internalsdk.rds.model.resource.BindResource;
import com.baidu.bce.internalsdk.rds.model.resource.BindResourceRequest;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.dao.mybatis.InstanceMapper;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.RdsAsyncService;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.resource.manager.sdk.ResManagerClient;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupResInfo;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupTreeResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.HistoryGroupResRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.HistoryGroupResResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupDetailRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupDetailResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupListResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceBindRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceGroupsInfo;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceGroupsPageInfo;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupTree;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Collections;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Created by luping03 on 17/10/11.
 */
@Service
public class ResourceGroupService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResourceGroupService.class);

    @Autowired
    LogicRdsClientFactory logicRdsClientFactory;

    @Autowired
    InstanceService instanceService;

    @Autowired
    RdsAsyncService rdsAsyncService;

    @Autowired
    InstanceMapper instanceMapper;

    @Autowired
    RegionConfiguration regionConfiguration;

    @Autowired
    private IdMapperService idMapperService;

    @Value("${rds.res.manager.sync.enabled:true}")
    private boolean resManagerSyncEnabled;

    public GroupTreeResponse getGroup(String name, String from) {
         // GroupTreeResponse group = logicRdsClientFactory.resourceGroupClient()
                 // .getGroup(name);
        ResManagerClient resManagerClient = logicRdsClientFactory.resourceGroupClient();
        Future<GroupTreeResponse> groupTreeFuture = rdsAsyncService.getResourceGroup(
                resManagerClient,
                name,
                BceInternalRequest.getThreadRequestId()
        );
        GroupTreeResponse treeResponse = null;
        // 获取资源组列表, 资源管理服务不稳定经常超时，改为异步获取,设置5s超时
        try {
            treeResponse = groupTreeFuture.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.error("get res group list error. skip", e);
            groupTreeFuture.cancel(true);
            return new GroupTreeResponse(Collections.<GroupTree>emptyList());
        }
        return treeResponse;
    }

    public String createGroup(GroupRequest request, String from) {
        String response = logicRdsClientFactory.resourceGroupClient()
                .createGroup(request);
        return response;
    }

    public void changeBindResource(BindResourceRequest request, boolean force, String changeType, String from) {
        List<GroupResInfo> bindResources = new ArrayList<>();
        String resourceGroupId = request.getResourceGroupId();
        if (request != null && !CollectionUtils.isEmpty(request.getBindings())) {
            for (BindResource binding : request.getBindings()) {
                String serviceType = instanceService.getServiceTypeByInstanceType(binding.getResourceType());
                GroupResInfo res = new GroupResInfo();
                res.setGroupId(resourceGroupId);
                res.setAccountId(logicRdsClientFactory.getAccountId());
                res.setUserId(logicRdsClientFactory.getUserId());
                res.setResourceType(serviceType);
                res.setResourceRegion(regionConfiguration.getCurrentRegion());
//                instanceService.findInsntaceUUidByShortId()
                res.setResourceId(BasisUtils.isShortId(binding.getResourceId()) ?
                        instanceMapper.queryInstanceUuid(binding.getResourceId()) : binding.getResourceId());
                res.setCreateTime(new Timestamp(System.currentTimeMillis()));
                bindResources.add(res);
            }
        }
        ResourceBindRequest resourceBindRequest = new ResourceBindRequest();
        resourceBindRequest.setBindings(bindResources);
        if (RDSConstant.RESOURCE_BIND.equalsIgnoreCase(changeType)) {
            logicRdsClientFactory.resourceGroupClient().bindResource(resourceBindRequest, force);
        } else if (RDSConstant.RESOURCE_UNBIND.equalsIgnoreCase(changeType)) {
            logicRdsClientFactory.resourceGroupClient().unbindResource(resourceBindRequest);
        }
    }

    public String updateGroup(GroupRequest request, boolean force, String from) {
        String response = logicRdsClientFactory.resourceGroupClient()
                .updateGroup(request);
        return response;

    }

    public void deleteGroup(String groupId) {
        logicRdsClientFactory.resourceGroupClient().deleteGroup(groupId);
    }


    public ResourceGroupsPageInfo getGroupResWithTag(String groupId, String id, String types,
                                                     String regions, String name, boolean isCurrent,
                                                     String tags, int pageNo, int pageSize, String from) {
        ResourceGroupsPageInfo response
                = logicRdsClientFactory.resourceGroupClient().getGroupResWithTag(groupId, id, types,
                regions, name, isCurrent, tags, pageNo, pageSize);
        return response;
    }

    public ResourceGroupsPageInfo getResourcePageWithTag(String id, String types, String regions, String name,
                                                         String tags, int pageNo, int pageSize, String from) {

        ResourceGroupsPageInfo response
                = logicRdsClientFactory.resourceGroupClient().getResourcePageWithTag(id, types, regions, name,
                tags, pageNo, pageSize);
        return response;
    }

    public ResourceGroupsPageInfo getResNoPageWithTag(String id, String types, String regions,
                                                      String name, String tags, String from) {
        ResourceGroupsPageInfo response
                = logicRdsClientFactory.resourceGroupClient().getResNoPageWithTag(id, types, regions, name, tags);
        return response;
    }

    public ResourceGroupsPageInfo getResNoPageWithoutTag(String id, String types, String regions,
                                                      String name, String from) {
        ResourceGroupsPageInfo response
                = logicRdsClientFactory.resourceGroupClient().getResNoPageWithoutTag(id, types, regions, name);
        return response;
    }

    public ResourceGroupsPageInfo getResourcePageWithoutTag(String id, String types, String regions,
                                                         String name, int pageNo, int pageSize, String from) {
        ResourceGroupsPageInfo response
                = logicRdsClientFactory.resourceGroupClient().getResourcePageWithoutTag(id, types, regions,
                name, pageNo, pageSize);
        return response;
    }

    public ResGroupDetailResponse getResGroupBatch(ResGroupDetailRequest resGroupDetailRequest, String from) {
        ResGroupDetailResponse response = new ResGroupDetailResponse();
        try {
             // response = logicRdsClientFactory.resourceGroupClient().getResGroupBatch(resGroupDetailRequest);
            // 获取已绑定的资源组, 资源管理服务不稳定经常超时，改为异步获取,设置1s超时
            ResManagerClient resManagerClient = logicRdsClientFactory.resourceGroupClient();
            Future<ResGroupDetailResponse> resGroupListFuture = rdsAsyncService.getResourceGroups(
                    resManagerClient,
                    resGroupDetailRequest,
                    BceInternalRequest.getThreadRequestId()
            );
            try {
                response = resGroupListFuture.get(1, TimeUnit.SECONDS);
            } catch (Exception e) {
                LOGGER.error("pack res group error. skip", e);
                resGroupListFuture.cancel(true);
            }
        } catch (Exception e) {
            LOGGER.debug("get res group batch error.", e);
        }

        return response;
    }

    public ResGroupListResponse getGroupResList(ResGroupDetailRequest resGroupDetailRequest, String from) {
        ResGroupListResponse response
                = logicRdsClientFactory.resourceGroupClient().queryGroupResList(resGroupDetailRequest);
        return response;
    }


    public HistoryGroupResResponse getHistoryResGroupBatchByRes(ResGroupDetailRequest resGroupDetailRequest,
                                                                String from) {
        HistoryGroupResResponse response
                = logicRdsClientFactory.resourceGroupClient().getHistoryResGroupBatchByRes(resGroupDetailRequest);
        return response;
    }

    public HistoryGroupResResponse getHistoryResGroupBatchByGroup(HistoryGroupResRequest request,
                                                                  String from) {
        HistoryGroupResResponse response
                = logicRdsClientFactory.resourceGroupClient().getHistoryResGroupBatchByGroup(request);
        return response;
    }


    public List<InstanceAbstract> filterResourceGroup(List<InstanceAbstract> instances, Map<String, String> filterMap,
                                                      int pageNo, int pageSize) {
        List<ResourceGroupsInfo> newGroupsInfoList = new ArrayList<>();
        ResManagerClient resManagerClient = logicRdsClientFactory.resourceGroupClient();
        if (filterMap != null && filterMap.get("resourceGroupId") != null) {
            String resGroup = filterMap.get("resourceGroupId");
//            List<ResourceBrief> resourceBriefs = new ArrayList<>();
//            for (InstanceAbstract instance : instances) {
//                ResourceBrief resourceBrief = new ResourceBrief();
//                String serviceType = getServiceTypeByInstanceType(instance.getInstanceType());
//                resourceBrief.setResourceType(serviceType);
//                resourceBrief.setAccountId(clientFactory.getAccountId());
//                resourceBrief.setResourceRegion(regionConfiguration.getCurrentRegion());
//                resourceBrief.setResourceId(instance.getInstanceShortId());
//                resourceBriefs.add(resourceBrief);
//            }
//            request.setResourceBriefs(resourceBriefs);
//            ResGroupDetailResponse resGroupBatch
//                    = resourceGroupService.getResGroupBatch(request, RDSConstant.FROM_CONSOLE);
            List<ResourceGroupsInfo> infos
                    = resManagerClient.getGroupResWithoutTag(resGroup, null, ServiceType.RDS.name(),
                    regionConfiguration.getCurrentRegion(), null, true, pageNo, pageSize).getInfos();
            List<ResourceGroupsInfo> pxoxyInfos
                    = resManagerClient.getGroupResWithoutTag(resGroup, null, ServiceType.RDS_PROXY.name(),
                    regionConfiguration.getCurrentRegion(), null, true, pageNo, pageSize).getInfos();
            List<ResourceGroupsInfo> replicaInfos
                    = resManagerClient.getGroupResWithoutTag(resGroup, null, ServiceType.RDS_REPLICA.name(),
                    regionConfiguration.getCurrentRegion(), null, true, pageNo, pageSize).getInfos();
            newGroupsInfoList.addAll(infos);
            newGroupsInfoList.addAll(pxoxyInfos);
            newGroupsInfoList.addAll(replicaInfos);
            List<InstanceAbstract> newInstances = new ArrayList<>();
            Map<String, Boolean> resourceIds = new HashMap<>();
            if (CollectionUtils.isEmpty(newGroupsInfoList)) {
                LOGGER.debug("newGroupsInfoList is empty");
                return newInstances;
            } else {
                for (ResourceGroupsInfo resourceGroupsInfo : newGroupsInfoList) {
                    // 获取长 ID
                    resourceIds.put(resourceGroupsInfo.getUuid(), true);
                }
                if (CollectionUtils.isEmpty(instances)) {
                    return newInstances;
                }
                for (InstanceAbstract instance : instances) {
                    if (resourceIds.get(instance.getInstanceId()) != null
                            && resourceIds.get(instance.getInstanceId())) {
                        newInstances.add(instance);
                    }
                }
            }
            return newInstances;
        } else {
            return instances;
        }
    }

    private Collection<Instance> addInstanceShortId(Collection<Instance> instances) {
        List<Instance> resultList = new ArrayList<>();
        List<String> longIds = new ArrayList<>();
        LOGGER.info("[resource sync addInstanceShortId] instance size {}", instances.size());
        for (Instance instance : instances) {
            LOGGER.info("[resource sync addInstanceShortId] instance getInstanceId {}", instance.getInstanceId());
            if ((StringUtils.isNotEmpty(instance.getInstanceId()))
                    && !"failed".equalsIgnoreCase(instance.getInstanceStatus())) {
                resultList.add(instance);

                longIds.add(instance.getInstanceId());
            }
        }
        LOGGER.info("[resource sync addInstanceShortId] instance longIds size {}", longIds.size());
        Map<String, String> idMap = idMapperService.idMapByLongId(longIds);
        Iterator<Instance> iterator = resultList.iterator();
        while (iterator.hasNext()) {
            Instance instance = iterator.next();
            String shortId = idMap.get(instance.getInstanceId());
            if (StringUtils.isEmpty(shortId)) {
                LOGGER.warn("resource sync instance list can not find shortId,instanceUuid:{}",
                        instance.getInstanceId());
                iterator.remove();
                continue;
            }
            LOGGER.warn("resource sync instance list can find shortId,instanceShortId:{}",
                    shortId);
            instance.setInstanceShortId(shortId);
        }
        return resultList;
    }

    private Collection<Instance> filterInstance(Collection<Instance> instances) {
        List<Instance> resultList = new ArrayList<>();
        LOGGER.info("[resource sync addInstanceShortId] instance size {}", instances.size());
        for (Instance instance : instances) {
            LOGGER.info("[resource sync addInstanceShortId] instance getInstanceId {}", instance.getInstanceId());
            if ((StringUtils.isNotEmpty(instance.getInstanceId()))
                    && !"failed".equalsIgnoreCase(instance.getInstanceStatus())
                    && !"creating".equalsIgnoreCase(instance.getInstanceStatus())) {
                resultList.add(instance);
            }
        }
        return resultList;
    }

    /**
     * 定期同步资源
     */
    @Scheduled(initialDelay = 1000, fixedRateString = "${rds.res.manager.sync.rate:*********}")
     void syncResource() {
        if (!resManagerSyncEnabled) {
            LOGGER.debug("RDS resource manager sync disabled.");
            return;
        }
        try {
            LOGGER.debug("Start sync resource to ResManager");
            RDSClient rdsClient = logicRdsClientFactory.createRdsClientForConsole();
            List<String> accountIdList = instanceMapper.listAllAccountId();
            if (CollectionUtils.isEmpty(accountIdList)) {
                return;
            }
            int size = accountIdList.size();
            int count = 110;
            for (int i = 0; i < size; i += 110) {
                if (i + 110 > size) {
                    count = size - i;
                }
                List<String> list = accountIdList.subList(i, count + i);
                String userIds = StringUtils.join(list, ",");
                InstanceListResponse instanceListResponse = rdsClient.listInstanceResource(userIds);
                if (instanceListResponse == null || CollectionUtils.isEmpty(instanceListResponse.getInstances())) {
                    LOGGER.debug("List instance , no instance");
                    return;
                }
                Collection<Instance> instances = instanceListResponse.getInstances();
                Collection<Instance> filterInstances = filterInstance(instances);
                filterInstances = addInstanceShortId(instances);
                instanceService.changeResource((List) filterInstances, "reconcile", null);
                LOGGER.debug("Update whitelist success, accounts size:{}", instances.size());
            }
        } catch (Exception e) {
            LOGGER.error("Update whitelist error", e);
        }
    }
//
//    private Resource getResource(String region,
//                                 String instanceId, String name,
//                                 String accountId, String userId) {
//        Resource resource = new Resource();
//        resource.setAccountId(accountId);
//        resource.setUserId(userId == null ? accountId : userId);
//        resource.setUuid(instanceId);
//        resource.setRegion(region);
//        resource.setType(ServiceType.RDS.getName());
//        resource.setUrl(RDSConstant.FE_DETAIL_URL + instanceId);
//        if (name != null) {
//            resource.setName(name);
//        }
//        return resource;
//    }
//
//
//    /**
//     * 同步更新或删除的资源信息
//     * @param syncType
//     * @param instanceId
//     * @param accountId
//     * @param serviceType
//     */
//
//    public void syncResource(String syncType, String instanceId, String accountId, String serviceType) {
//        if (org.apache.commons.lang3.StringUtils.isEmpty(instanceId)) {
//            LOGGER.info("Skip sync {} Resource: instanceId is empty.", syncType);
//            return;
//        }
//        LOGGER.debug("Sync {} Resource: resourceId {}, instanceId {}", syncType, instanceId);
//
//        String currentRegion = regionConfiguration.getCurrentRegion();
//        Resource resource = getResource(currentRegion, instanceId, null, accountId, null);
//        SyncResClient syncResClient = logicRdsClientFactory.createSyncResClient();
//        SyncLockClient lock = logicRdsClientFactory.createSyncLockClient();
//        rdsAsyncService.syncResource(
//                syncResClient,
//                lock,
//                syncType,
//                serviceType,
//                Collections.singletonList(resource),
//                BceInternalRequest.getThreadRequestId(),
//                regionConfiguration.getCurrentRegion()
//        );
//    }


}

