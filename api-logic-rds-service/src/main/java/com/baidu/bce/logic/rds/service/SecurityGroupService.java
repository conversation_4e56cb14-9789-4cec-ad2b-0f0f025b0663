package com.baidu.bce.logic.rds.service;


import com.baidu.bce.internalsdk.rds.RDSClient3;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdsRequest;
import com.baidu.bce.internalsdk.rds.model.security.BlbIdsResponses;
import com.baidu.bce.internalsdk.rds.model.security.OpenApiSecurityGroupResponse;
import com.baidu.bce.internalsdk.rds.model.security.PageSecurityResponse;
import com.baidu.bce.internalsdk.rds.model.security.OpenApiSecurityGroup;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroup;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupBindRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupListResp;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupPage;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupPageRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupResponse;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupRule;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupUpdate;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessException;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;

@Service
public class SecurityGroupService {

    @Autowired
    private LogicRdsClientFactory clientFactory;

    public OpenApiSecurityGroupResponse listSecurityGroup(String instanceId) {
        OpenApiSecurityGroupResponse result = new OpenApiSecurityGroupResponse();
        InstanceIdsRequest instanceIdsRequest = new InstanceIdsRequest();
        List<String> list = Arrays.asList(instanceId);
        SecurityGroupResponse groupResponse;
        instanceIdsRequest.setAppIds(list);
        BlbIdsResponses blbIds = getBlbIds(instanceIdsRequest);
        if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
            String blbId = blbIds.getAppBLBs().get(0).getBlbId();
            String lbType = "blb";
            SecurityGroupListResp response
                    = clientFactory.createNewVpcClient().listSecurityGroup(blbId, lbType);
            List<OpenApiSecurityGroup> groups = new ArrayList<>();
            if (response != null) {
                groupResponse = response.getResult();
                if (groupResponse != null && CollectionUtils.isNotEmpty(groupResponse.getSecurityGroups())) {
                    for (SecurityGroup securityGroup : groupResponse.getSecurityGroups()) {
                        OpenApiSecurityGroup group = new OpenApiSecurityGroup();
                        List<SecurityGroupRule> outbound = new ArrayList<>();
                        List<SecurityGroupRule> inbound = new ArrayList<>();
                        for (SecurityGroupRule rule : securityGroup.getRules()) {
                            if ("egress".equals(rule.getDirection())) {
                                rule.setDirection(BasisUtils.upperCaseFirstChar(rule.getDirection()));
                                outbound.add(rule);
                            } else if ("ingress".equals(rule.getDirection())) {
                                rule.setDirection(BasisUtils.upperCaseFirstChar(rule.getDirection()));
                                inbound.add(rule);
                            }
                        }
                        group.setInbound(inbound);
                        group.setOutbound(outbound);
                        group.setProjectId(instanceId);
                        group.setSecurityGroupId(securityGroup.getSecurityGroupId());
                        group.setSecurityGroupName(securityGroup.getName());
                        group.setSecurityGroupRemark(securityGroup.getDesc());
                        group.setVpcId(securityGroup.getVpcId());
                        group.setVpcName(securityGroup.getVpcName());
                        group.setSecurityGroupUuid(securityGroup.getId());
                        groups.add(group);
                    }
                    result.setActiveRules(groupResponse.getActiveRules());
                }
            }
            result.setGroups(groups);
        }
        return result;
    }


    public OpenApiSecurityGroupResponse listSecurityGroup(String instanceId, String accountId) {
        OpenApiSecurityGroupResponse result = new OpenApiSecurityGroupResponse();
        InstanceIdsRequest instanceIdsRequest = new InstanceIdsRequest();
        List<String> list = Arrays.asList(instanceId);
        SecurityGroupResponse groupResponse;
        instanceIdsRequest.setAppIds(list);
        BlbIdsResponses blbIds = getBlbIds(instanceIdsRequest, accountId);
        if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
            String blbId = blbIds.getAppBLBs().get(0).getBlbId();
            String lbType = "blb";
            SecurityGroupListResp response
                    = clientFactory.createNewVpcClient(accountId).listSecurityGroup(blbId, lbType);
            List<OpenApiSecurityGroup> groups = new ArrayList<>();
            if (response != null) {
                groupResponse = response.getResult();
                if (groupResponse != null && CollectionUtils.isNotEmpty(groupResponse.getSecurityGroups())) {
                    for (SecurityGroup securityGroup : groupResponse.getSecurityGroups()) {
                        OpenApiSecurityGroup group = new OpenApiSecurityGroup();
                        List<SecurityGroupRule> outbound = new ArrayList<>();
                        List<SecurityGroupRule> inbound = new ArrayList<>();
                        for (SecurityGroupRule rule : securityGroup.getRules()) {
                            if ("egress".equals(rule.getDirection())) {
                                rule.setDirection(BasisUtils.upperCaseFirstChar(rule.getDirection()));
                                outbound.add(rule);
                            } else if ("ingress".equals(rule.getDirection())) {
                                rule.setDirection(BasisUtils.upperCaseFirstChar(rule.getDirection()));
                                inbound.add(rule);
                            }
                        }
                        group.setInbound(inbound);
                        group.setOutbound(outbound);
                        group.setProjectId(instanceId);
                        group.setSecurityGroupId(securityGroup.getSecurityGroupId());
                        group.setSecurityGroupName(securityGroup.getName());
                        group.setSecurityGroupRemark(securityGroup.getDesc());
                        group.setVpcId(securityGroup.getVpcId());
                        group.setVpcName(securityGroup.getVpcName());
                        group.setSecurityGroupUuid(securityGroup.getId());
                        groups.add(group);
                    }
                    result.setActiveRules(groupResponse.getActiveRules());
                }
            }
            result.setGroups(groups);
        }
        return result;
    }


    public PageSecurityResponse listSecurityGroupByVpc(SecurityGroupPageRequest groupPageRequest) {
        PageSecurityResponse pageResponse = new PageSecurityResponse();
        pageResponse
                = clientFactory.createNewVpcClient().listSecurityGroupByVpc(groupPageRequest);
        return pageResponse;
    }

    public void bindSecurityGroup(SecurityGroupBindRequest bindRequest, String from) {
//        if (bindRequest == null) {
//            throw new ScsExceptions.ParamValidationException();
//        }
//        if (CollectionUtils.isEmpty(bindRequest.getInstanceIds())) {
//            throw new ScsExceptions.ParamValidationException("instance ids cannot be empty");
//        }
//
//        List<String> failedIds = new ArrayList<>();
//        for (String instanceUuid : bindRequest.getInstanceIds()) {
//            if (!isValidStatus(instanceUuid)) {
//                failedIds.add(instanceUuid);
//            }
//        }
//        if (CollectionUtils.isNotEmpty(failedIds)) {
//            throw new ScsExceptions.BindSecurityGroupException(failedIds);
//        }
//
//        ScsInstanceListResponse scsInstances = getBlbByInstance(bindRequest.getInstanceIds());
//
//        if (scsInstances == null || !CollectionUtils.isNotEmpty(scsInstances.getScs())) {
//            throw new ScsExceptions.NotExistException(
//                    StringUtils.join(bindRequest.getInstanceIds(), ",")
//            );
//        }
//        formatScsInstanceListResponse(scsInstances);
//        List<ScsInstanceWithBLB> instances = scsInstances.getScs();
//        checkIfSameVpc(instances);
//        checkIfSameSubInstanceType(instances);
//        List<String> blbIdList = new ArrayList<>();
//        String scsLbType = "";
//        for (ScsInstanceWithBLB instance : instances) {
//            blbIdList.add(instance.getLbId());
//            scsLbType = instance.getScsLbType();
//        }
//        if (CollectionUtils.isNotEmpty(blbIdList) && blbIdList.size() > 10) {
//            throw new ScsExceptions.ParamValidationException("The size of instanceIds cannot exceed 10");
//        }
//        // 前端传入的是 securityGroupIds
//        if (CollectionUtils.isEmpty(bindRequest.getSecurityGroupUuids())) {
//            bindRequest.setSecurityGroupUuids(bindRequest.getSecurityGroupIds());
//        }
//        if (CollectionUtils.isNotEmpty(bindRequest.getSecurityGroupUuids())
//                && bindRequest.getSecurityGroupUuids().size() > 10) {
//            throw new ScsExceptions.ParamValidationException("The size of securityGroups cannot exceed 10");
//        }
//        if (ScsConstants.FROM_API.equals(from)) {
//            List<String> securityGroupUuids = null;
//            GetCacheClusterDetailsResponse instance
//                    = cacheClusterService.getCacheCluster(bindRequest.getInstanceIds().get(0));
//            if (instance != null) {
//                // 这里需要转为长id
//                securityGroupUuids = convertShortIdToLongId(bindRequest.getSecurityGroupIds(),
//                        instance.getVpcId());
//            }
//            bindRequest.setSecurityGroupUuids(securityGroupUuids);
//        }
//        bindRequest.setInstanceUuids(blbIdList);
//        bindRequest.setSecurityGroupIds(null);
//        bindRequest.setInstanceIds(null);
//        bindRequest.setSubInstanceType(scsLbType);
        clientFactory.createNewVpcClient().bindSecurityGroup(bindRequest);
    }

    public void unbindSecurityGroup(SecurityGroupUpdate updateRequest, String from) {
        if (updateRequest == null || StringUtils.isBlank(updateRequest.getInstanceId())) {
            throw new RDSBusinessExceptions.InstanceNotFoundException();
        }
        InstanceIdsRequest instanceIdsRequest = new InstanceIdsRequest();
        List<String> list = Arrays.asList(updateRequest.getInstanceId());
        instanceIdsRequest.setAppIds(list);
        BlbIdsResponses blbIds = getBlbIds(instanceIdsRequest);
        // 通用型实例不支持解绑安全组
        if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
            for (BlbIdsResponses.BlbIdResponse appBLB : blbIds.getAppBLBs()) {
                if (StringUtils.isNotEmpty(appBLB.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(appBLB.getResourceType())) {
                    throw new RDSBusinessExceptions.GeneralInstanceNotSupportException();
                }
            }
        }

        // 针对 openAPI的请求，需做长短 ID 转换
        if (StringUtils.isNotEmpty(from) && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            List<String> securityGroupUuids = null;
//            InstanceGetResponse instanceGetResponse = clientFactory.
//                    createRdsClient2ByInstanceId(updateRequest.getInstanceId())
//                    .instanceDescribe(updateRequest.getInstanceId());
//            if (instanceGetResponse != null) {
//                securityGroupUuids = convertShortIdToLongId(updateRequest.getSecurityGroupIds(),
//                        instanceGetResponse.getInstance().getVpcId());
//            }
            if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
                securityGroupUuids = convertShortIdToLongId(updateRequest.getSecurityGroupIds(),
                        blbIds.getAppBLBs().get(0).getVpcId());
            }
            updateRequest.setSecurityGroupUuids(securityGroupUuids);
        }

        if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
            String blbId = blbIds.getAppBLBs().get(0).getBlbId();
            String lbType = "blb";
            // 前端传入的也是 securityGroupIds
            if (CollectionUtils.isEmpty(updateRequest.getSecurityGroupUuids())) {
                updateRequest.setSecurityGroupUuids(updateRequest.getSecurityGroupIds());
            }
            if (CollectionUtils.isNotEmpty(updateRequest.getSecurityGroupUuids())
                    && updateRequest.getSecurityGroupUuids().size() > 10) {
                throw new RDSExceptions.ParamValidationException("The size of securityGroups cannot exceed 10");
            }
            updateRequest.setSecurityGroupIds(null);
            updateRequest.setInstanceId(null);
            updateRequest.setInstanceUuid(blbId);
            updateRequest.setSubInstanceType(lbType);
            clientFactory.createNewVpcClient().unbindSecurityGroup(updateRequest);
        }
    }

    public void unbindSecurityGroup(SecurityGroupUpdate updateRequest, String accountId, String from) {
        if (updateRequest == null || StringUtils.isBlank(updateRequest.getInstanceId())) {
            throw new RDSBusinessExceptions.InstanceNotFoundException();
        }
        InstanceIdsRequest instanceIdsRequest = new InstanceIdsRequest();
        List<String> list = Arrays.asList(updateRequest.getInstanceId());
        instanceIdsRequest.setAppIds(list);
        BlbIdsResponses blbIds = getBlbIds(instanceIdsRequest, accountId);
        // 通用型实例不支持解绑安全组
        if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
            for (BlbIdsResponses.BlbIdResponse appBLB : blbIds.getAppBLBs()) {
                if (StringUtils.isNotEmpty(appBLB.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(appBLB.getResourceType())) {
                    throw new RDSBusinessExceptions.GeneralInstanceNotSupportException();
                }
            }
        }

        // 针对 openAPI的请求，需做长短 ID 转换
        if (StringUtils.isNotEmpty(from) && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            List<String> securityGroupUuids = null;
//            InstanceGetResponse instanceGetResponse = clientFactory.
//                    createRdsClient2ByInstanceId(updateRequest.getInstanceId())
//                    .instanceDescribe(updateRequest.getInstanceId());
//            if (instanceGetResponse != null) {
//                securityGroupUuids = convertShortIdToLongId(updateRequest.getSecurityGroupIds(),
//                        instanceGetResponse.getInstance().getVpcId());
//            }
            if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
                securityGroupUuids = convertShortIdToLongId(updateRequest.getSecurityGroupIds(),
                        blbIds.getAppBLBs().get(0).getVpcId());
            }
            updateRequest.setSecurityGroupUuids(securityGroupUuids);
        }

        if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
            String blbId = blbIds.getAppBLBs().get(0).getBlbId();
            String lbType = "blb";
            // 前端传入的也是 securityGroupIds
            if (CollectionUtils.isEmpty(updateRequest.getSecurityGroupUuids())) {
                updateRequest.setSecurityGroupUuids(updateRequest.getSecurityGroupIds());
            }
            if (CollectionUtils.isNotEmpty(updateRequest.getSecurityGroupUuids())
                    && updateRequest.getSecurityGroupUuids().size() > 10) {
                throw new RDSExceptions.ParamValidationException("The size of securityGroups cannot exceed 10");
            }
            updateRequest.setSecurityGroupIds(null);
            updateRequest.setInstanceId(null);
            updateRequest.setInstanceUuid(blbId);
            updateRequest.setSubInstanceType(lbType);
            clientFactory.createNewVpcClient(accountId).unbindSecurityGroup(updateRequest);
        }
    }

    public BlbIdsResponses getBlbIds(InstanceIdsRequest request) {
        RDSClient3 rdsClient3 = clientFactory.createRdsClient3();
        return rdsClient3.getBlbIds(request);
    }

    public BlbIdsResponses getBlbIds(InstanceIdsRequest request, String accountId) {
        RDSClient3 rdsClient3 = clientFactory.createRdsClient3(accountId);
        return rdsClient3.getBlbIds(request);
    }

    public List<SecurityGroupPage> listSecurityGroupByVpcFromApi(String vpcId) {
        SecurityGroupPageRequest groupPageRequest = new SecurityGroupPageRequest();
        groupPageRequest.setVpcId(vpcId);
        PageSecurityResponse securityGroupPagePage = null;
        if (!StringUtils.isEmpty(vpcId)) {
            securityGroupPagePage = clientFactory.createNewVpcClient().listSecurityGroupByVpc(groupPageRequest);
        }
        List<SecurityGroupPage> result = new ArrayList<>();
        if (securityGroupPagePage != null && securityGroupPagePage.getResult() != null) {
            List<LinkedHashMap> mapList = securityGroupPagePage.getResult();
            for (LinkedHashMap map : mapList) {
                SecurityGroupPage securityGroupPage = new SecurityGroupPage();
                try {
                    BeanUtils.populate(securityGroupPage, map);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                securityGroupPage.setId(null);
                result.add(securityGroupPage);
            }
        }
        return result;
    }

    private List<String> convertShortIdToLongId(List<String> securityGroupShortIds, String vpcId) {
        List<String> securityGroupLongIds = new ArrayList<>();
        List<SecurityGroupPage> groupPageList = listSecurityGroupByVpcFromApi(vpcId);
        for (SecurityGroupPage securityGroupPage : groupPageList) {
            for (String securityGroupId : securityGroupShortIds) {
                if (securityGroupPage.getSecurityGroupId().equals(securityGroupId)) {
                    securityGroupLongIds.add(securityGroupPage.getUuid());
                }
            }
        }
        return securityGroupLongIds;
    }

    public void updateSecurityGroup(SecurityGroupUpdate securityGroupUpdate, String from) {
        if (securityGroupUpdate == null || StringUtils.isBlank(securityGroupUpdate.getInstanceId())) {
            throw new RDSBusinessExceptions.InstanceNotFoundException();
        }
        InstanceIdsRequest instanceIdsRequest = new InstanceIdsRequest();
        List<String> list = Arrays.asList(securityGroupUpdate.getInstanceId());
        instanceIdsRequest.setAppIds(list);
        BlbIdsResponses blbIds = getBlbIds(instanceIdsRequest);
        // 通用型实例不支持绑定安全组
        if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
            for (BlbIdsResponses.BlbIdResponse appBLB : blbIds.getAppBLBs()) {
                if (StringUtils.isNotEmpty(appBLB.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(appBLB.getResourceType())) {
                    throw new RDSBusinessExceptions.GeneralInstanceNotSupportException();
                }
            }
        }
        // 针对 openAPI的请求，需做长短 ID 转换
        if (StringUtils.isNotEmpty(from) && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            List<String> securityGroupUuids = null;
//            InstanceGetResponse instanceGetResponse = clientFactory.
//                    createRdsClient2ByInstanceId(securityGroupUpdate.getInstanceId())
//                    .instanceDescribe(securityGroupUpdate.getInstanceId());
//            if (instanceGetResponse != null) {
//                securityGroupUuids = convertShortIdToLongId(securityGroupUpdate.getSecurityGroupIds(),
//                        instanceGetResponse.getInstance().getVpcId());
//            }
            if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
                securityGroupUuids = convertShortIdToLongId(securityGroupUpdate.getSecurityGroupIds(),
                        blbIds.getAppBLBs().get(0).getVpcId());
            }
            securityGroupUpdate.setSecurityGroupUuids(securityGroupUuids);
        }
        if (blbIds != null && !blbIds.getAppBLBs().isEmpty()) {
            String blbId = blbIds.getAppBLBs().get(0).getBlbId();
            String lbType = "blb";
            // 前端传入的也是 securityGroupIds
            if (CollectionUtils.isEmpty(securityGroupUpdate.getSecurityGroupUuids())) {
                securityGroupUpdate.setSecurityGroupUuids(securityGroupUpdate.getSecurityGroupIds());
            }
            if (CollectionUtils.isNotEmpty(securityGroupUpdate.getSecurityGroupUuids())
                    && securityGroupUpdate.getSecurityGroupUuids().size() > 10) {
                throw new RDSExceptions.ParamValidationException("The size of securityGroups cannot exceed 10");
            }
            securityGroupUpdate.setSecurityGroupIds(null);
            securityGroupUpdate.setInstanceId(null);
            securityGroupUpdate.setInstanceUuid(blbId);
            securityGroupUpdate.setSubInstanceType(lbType);
            clientFactory.createNewVpcClient().updateSecurityGroup(securityGroupUpdate);
        }
    }
}
