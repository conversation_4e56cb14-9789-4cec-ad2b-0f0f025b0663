/*
 * Copyright (C) 2015 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCreateRequest;
import com.baidu.bce.internalsdk.rds.model.instance.OrderItemExtraInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * Created by x<PERSON><PERSON><PERSON><PERSON> on 2015/5/5.
 */
@Service
public class ReplicaQuotaValidator extends QuotaValidator {

    private static final Logger logger = LoggerFactory.getLogger(ReplicaQuotaValidator.class);

    private ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public ServiceType getServiceType() {
        return ServiceType.RDS_REPLICA;
    }

    @Override
    public RdsQuotaService.QuotaType getQuotaType() {
        return RdsQuotaService.QuotaType.replica;
    }

    @Override
    public Integer getActiveInstanceCount(String sourceInstanceId) {
        Integer count = 0;
        if (StringUtils.isEmpty(sourceInstanceId)) {
            return getActiveResourceList().size();
        }
        for (Resource resource : getActiveResourceList()) {
            String extra = resource.getExtra();
            try {
                if (StringUtils.isNotEmpty(extra)) {
                    try {
                        InstanceCreateRequest request = objectMapper.readValue(extra, OrderItemExtraInfo.class)
                                .getInstanceCreateRequest();
                        if (sourceInstanceId.equals(request.getInstanceParameters().getSourceInstanceId())) {
                            count++;
                        }
                    } catch (NullPointerException e) {
                        InstanceCreateRequest request = objectMapper.readValue(extra, InstanceCreateRequest.class);
                        if (sourceInstanceId.equals(request.getInstanceParameters().getSourceInstanceId())) {
                            count++;
                        }
                    }
                }
            } catch (IOException e) {
                logger.error("[validReplicaQuota] Parse extra[{}] fail.", extra, e);
            }
        }
        return count;
    }
}
