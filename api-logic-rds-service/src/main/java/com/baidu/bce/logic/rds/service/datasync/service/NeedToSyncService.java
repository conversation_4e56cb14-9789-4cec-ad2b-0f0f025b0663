package com.baidu.bce.logic.rds.service.datasync.service;

import com.baidu.bce.logic.rds.dao.model.NeedToSyncPO;
import com.baidu.bce.logic.rds.dao.mybatis.NeedToSyncMapper;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * 数据同步表t_rds_datasync的服务操作类
 * <p>
 * Created by luping03 on 2017/12/29.
 */
@Service
public class NeedToSyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(NeedToSyncService.class);

    @Autowired
    private NeedToSyncMapper needToSyncMapper;

    /**
     * 查出所有数据
     *
     * @return
     */
    public List<NeedToSyncPO> findAllNeedToSync(Timestamp lastActionTime) {
        return needToSyncMapper.findAll(lastActionTime);
    }

    /**
     * 查出所有数据
     *
     * @return
     */
    public List<NeedToSyncPO> findSyncData(Date outLockDate) {
        return needToSyncMapper.findSyncData(outLockDate);
    }

    /**
     * 尝试锁定一行记录, 只有lock_time = oldLockDate, lock_id为空时才可以锁定
     *
     * @param id
     * @param lockDate
     * @param lockId
     * @return
     */
    public int lockOneLine(Long id, Date lockDate, String lockId, Date oldLockDate) {
        return needToSyncMapper.lockOneLine(id, lockDate, lockId, oldLockDate);
    }

//    /**
//     * 尝试锁定一行记录, 只有lock_time为空或默认值，lock_id为空时才可以锁定
//     *
//     * @param id
//     * @param lockDate
//     * @param lockId
//     * @return
//     */
//    public int lockOneLine(Long id, Date lockDate, String lockId) {
//        return needToSyncMapper.lockOneLine(id, lockDate, lockId);
//    }

    /**
     * 重新抢夺锁定一行记录, 只有lock_time锁定时间过长
     *
     * @param id
     * @param lockDate
     * @param outLockDate
     * @param lockId
     * @return
     */
    public int seizeLockOneLine(Long id, Date outLockDate, Date lockDate, String lockId) {
        return needToSyncMapper.seizeLockOneLine(id, lockDate, outLockDate, lockId);
    }

    /**
     * 释放锁，但不删除记录
     *
     * @param record
     * @return
     */
    public int unLockOneLine(NeedToSyncPO record) {
        return needToSyncMapper.unLockOneLineByResourceUUid(record.getInstanceUuid());
    }

    /**
     * test
     *
     * @return
     */
    public String show() {

        return "hello world!";
    }

    /**
     * test
     *
     * @param id
     * @return
     */
    public NeedToSyncPO showDao(Long id) {

        List<NeedToSyncPO> syncs = Lists.newArrayList();

        if (CollectionUtils.isEmpty(syncs)) {
            return null;
        }
        return syncs.get(0);
    }
}

