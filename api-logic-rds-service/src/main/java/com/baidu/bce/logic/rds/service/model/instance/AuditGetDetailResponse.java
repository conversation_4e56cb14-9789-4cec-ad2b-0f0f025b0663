package com.baidu.bce.logic.rds.service.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AuditGetDetailResponse {

    private String eipStatus;
    private List<String> readReplica;
    private List<String> rdsProxy;

    public String getEipStatus() {
        return eipStatus;
    }

    public void setEipStatus(String eipStatus) {
        this.eipStatus = eipStatus;
    }

    public List<String> getReadReplica() {
        return readReplica;
    }

    public void setReadReplica(List<String> readReplica) {
        this.readReplica = readReplica;
    }

    public List<String> getRdsProxy() {
        return rdsProxy;
    }

    public void setRdsProxy(List<String> rdsProxy) {
        this.rdsProxy = rdsProxy;
    }
}
