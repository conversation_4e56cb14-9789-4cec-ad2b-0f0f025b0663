package com.baidu.bce.logic.rds.service.group;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.CheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.AccountGetResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountListResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountPrivilege;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePrivilegesRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.database.Database;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseGetResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseListResponse;
import com.baidu.bce.internalsdk.rds.model.database.DatabaseUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.group.BatchJoinRequest;
import com.baidu.bce.internalsdk.rds.model.group.CreateGroupRequest;
import com.baidu.bce.internalsdk.rds.model.group.FollowerIdRequest;
import com.baidu.bce.internalsdk.rds.model.group.GroupForceChangeLeaderRequest;
import com.baidu.bce.internalsdk.rds.model.group.GroupForceChangeLeaderResponse;
import com.baidu.bce.internalsdk.rds.model.group.GroupInfo;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupDetailResponse;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupListResponse;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceListResponse;
import com.baidu.bce.logic.core.request.OrderModel;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.constant.RdsInstanceStatus;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.group.GroupListRequest;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.ParamComparator;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by luping03 on 17/11/3.
 */
@Service
public class InstanceGroupService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceGroupService.class);


    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private IdMapperService idMapperService;

    @Autowired
    InstanceDao instanceDao;

    @Autowired
    private OthersService othersService;

    public LogicPageResultResponse<GroupInfo> list(GroupListRequest listRequest) {

        InstanceGroupListResponse listResponse = clientFactory.createRdsClient().instanceGroupList();

        // 过滤
        listResponse.setGroups(filterGroupInfo(listResponse.getGroups(), listRequest.getFilterMap()));

        // 排序
        orderGroupInfo(listResponse.getGroups(), listRequest.getOrders());

        LogicPageResultResponse<GroupInfo> page =  listForPage(listResponse.getGroups(), null,
                null, listRequest.getPageNo(), listRequest.getPageSize());

        return page;
    }

    private void orderGroupInfo(List<GroupInfo> groups, List<OrderModel> orders) {
        String order = (orders == null || orders.size() == 0)
                ? null : orders.get(0).getOrder();
        String orderBy = (orders == null || orders.size() == 0)
                ? null : orders.get(0).getOrderBy();

        Collections.sort(groups, new ParamComparator<GroupInfo>(orderBy, order));

    }


    public LogicPageResultResponse<GroupInfo> listForPage(List<GroupInfo> groupInfos,
                                                                 String order,
                                                                 String orderBy,
                                                                 int pageNo,
                                                                 int pageSize) {
        LogicPageResultResponse<GroupInfo> response = new LogicPageResultResponse<>();
        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);

        if (groupInfos == null || groupInfos.size() == 0) {
            return response;
        }
        if (pageNo < 1 || pageSize < 1) {
            return response;
        }

        int pageStart = (pageNo - 1) * pageSize;
        int pageEnd = pageNo * pageSize;

        int start = pageStart;
        int end = Math.min(pageEnd, groupInfos.size());

        LOGGER.info("group page start {}, end {}", start, end);

        List<GroupInfo> instanceForPage = groupInfos.subList(start, end);

        response.setTotalCount(groupInfos.size());
        response.setResult(instanceForPage);
        return response;
    }

    public InstanceGroupDetailResponse detail(String groupId) {
        InstanceGroupDetailResponse listResponse = clientFactory.createRdsClient().instanceGroupDetail(groupId);
        return listResponse;
    }

    public void checkGtid(String instanceId) {
        clientFactory.createRdsClient().instanceGroupCheckGtid(instanceId);
    }

    public void checkPing(String sourceId, String targetId) {
        clientFactory.createRdsClient().instanceGroupCheckPing(sourceId, targetId);
    }

    public void checkData(String instanceId) {
        clientFactory.createRdsClient().instanceGroupCheckData(instanceId);
    }

    public void createGroup(CreateGroupRequest request) {
        clientFactory.createRdsClient().instanceGroupCreate(request);
    }

    public void joinGroup(FollowerIdRequest request) {
        clientFactory.createRdsClient().instanceGroupJoin(request);
    }

    public void changeName(String groupId, String name) {
        clientFactory.createRdsClient().instanceGroupChangeName(groupId, name);
    }

    public void delete(String groupId) {
        clientFactory.createRdsClient().instanceGroupDelete(groupId);
    }

    public void changeLeader(String groupId, String newLeader) {
        clientFactory.createRdsClient().instanceGroupChangeLeader(groupId, newLeader);
    }

    public void signOut(String groupId, String instanceId) {
        clientFactory.createRdsClient().instanceGroupSignOut(groupId, instanceId);
    }

    public AccountListResponse accountList(String groupId) {
        return clientFactory.createRdsClient().instanceGroupAccountList(groupId);
    }

    public AccountGetResponse accountDetail(String groupId, String accountName) {
        return clientFactory.createRdsClient().instanceGroupAccountDetail(groupId, accountName);
    }

    public CheckExistResponse accountCheckName(String groupId, String accountName) {
        return clientFactory.createRdsClient().instanceGroupAccountCheckName(groupId, accountName);
    }

    public void accountCreate(String groupId, Account request, String ak) {
        clientFactory.createRdsClient().instanceGroupAccountCreate(groupId, request);
    }

    public void accountUpdatePW(String groupId, String accountName, AccountUpdatePasswordRequest pwRequest) {
        clientFactory.createRdsClient()
                .instanceGroupAccountModifyPassword(groupId, accountName, pwRequest.getEncryptedPassword());
    }

    public void accountUpdateRemark(String groupId, String accountName, AccountUpdateRemarkRequest remarkRequest) {
        clientFactory.createRdsClient()
                .instanceGroupAccountModifyRemark(groupId, accountName, remarkRequest.getRemark());
    }

    public void accountUpdatePrivileges(String groupId, String accountName, AccountUpdatePrivilegesRequest request) {
        clientFactory.createRdsClient()
                .instanceGroupAccountModifyPrivilege(groupId, accountName, request.getDatabasePrivileges());
    }

    public void accountDelete(String groupId, String accountName) {
        clientFactory.createRdsClient()
                .instanceGroupAccountDelete(groupId, accountName);
    }

    public DatabaseListResponse databaseList(String groupId) {
        return clientFactory.createRdsClient().instanceGroupDatabaseList(groupId);
    }

    public DatabaseGetResponse databaseDetail(String groupId, String dbName) {
        return clientFactory.createRdsClient().instanceGroupDatabaseDescribe(groupId, dbName);
    }

    public CheckExistResponse databaseCheck(String groupId, String dbName) {
        return clientFactory.createRdsClient().instanceGroupDatabaseCheck(groupId, dbName);
    }

    public void databaseCreate(String groupId, Database database) {

        RDSClient client = clientFactory.createRdsClient();

        client.instanceGroupDatabaseCreate(groupId, database);

        for (AccountPrivilege accountPrivilege : database.getAccountPrivileges()) {
            // pull account privileges
            Account account = client.instanceGroupAccountDescribe(
                    groupId, accountPrivilege.getAccountName()).getAccount();
            Account.DatabasePrivilege databasePrivilege = new Account.DatabasePrivilege();
            databasePrivilege.setDbName(database.getDbName());
            databasePrivilege.setAuthType(accountPrivilege.getAuthType());
            account.getDatabasePrivileges().add(databasePrivilege);
            // update account privileges
            client.instanceGroupAccountModifyPrivilege(groupId,
                    accountPrivilege.getAccountName(), account.getDatabasePrivileges());
        }

    }

    public void databaseUpdateRemark(String groupId, String dbName, DatabaseUpdateRemarkRequest remarkRequest) {
        clientFactory.createRdsClient().instanceGroupDatabaseModifyRemark(groupId, dbName, remarkRequest);

    }

    public void databaseDelete(String groupId, String dbName) {
        clientFactory.createRdsClient().instanceGroupDatabaseDelete(groupId, dbName);
    }


    public List<GroupInfo> filterGroupInfo(List<GroupInfo> src, Map<String, String> filterMap) {

        List<GroupInfo> result = new ArrayList<GroupInfo>();

        for (GroupInfo eachGroup : src) {

            if (filterMap != null && filterMap.get("groupName") != null
                    && !ObjectUtils.equals(filterMap.get("groupName"), "")) {
                String filterGroupName = filterMap.get("groupName");
                if (!eachGroup.getName().toLowerCase().contains(filterGroupName)) {
                    continue;
                }

            }

            if (filterMap != null && filterMap.get("groupId") != null
                    && !ObjectUtils.equals(filterMap.get("groupId"), "")) {

                String filterGroupId = filterMap.get("groupId");
                // 过滤vpcId
                if (eachGroup.getGroupId() == null || !eachGroup.getGroupId().equals(filterGroupId)) {
                    continue;
                }
            }

            if (filterMap != null && filterMap.get("instanceStatus") != null
                    && !ObjectUtils.equals(filterMap.get("instanceStatus"), "")) {

                String filterStatus = filterMap.get("instanceStatus");

                if (eachGroup.getLeader() != null
                        && !ObjectUtils.equals(eachGroup.getLeader().getStatus(), filterStatus)) {
                    continue;
                }

            }

            if (filterMap != null && filterMap.get("resourceType") != null
                    && !ObjectUtils.equals(filterMap.get("resourceType"), "")) {

                String filterStatus = filterMap.get("resourceType");

                if (eachGroup.getLeader() != null
                        && !ObjectUtils.equals(eachGroup.getLeader().getResourceType(), filterStatus)) {
                    continue;
                }

            }

            if (filterMap != null && filterMap.get("engine") != null
                    && !ObjectUtils.equals(filterMap.get("engine"), "")) {

                String filterStatus = filterMap.get("engine");

                if (eachGroup.getLeader() != null
                        && !ObjectUtils.equals(eachGroup.getDbType(), filterStatus)) {
                    continue;
                }

            }

            result.add(eachGroup);

        }
        return result;
    }

    public GroupForceChangeLeaderResponse forceChangeLeader(String groupId,
                                                            GroupForceChangeLeaderRequest changeLeaderRequest) {
        GroupForceChangeLeaderResponse response = null;
        if (BasisUtils.isShortId(changeLeaderRequest.getLeaderId())) {
            InstancePO instancePO = instanceDao.queryInstanceByInstanceId(changeLeaderRequest.getLeaderId(),
                    clientFactory.getAccountId());
            if (instancePO == null) {
                throw new RDSExceptions.ResourceNotExistException();
            }
            if (instancePO.getInstanceStatus().equalsIgnoreCase(RdsInstanceStatus.CREATING.getValue())) {
                throw new RDSExceptions.NotSupportOperation();
//                    logger.info("instancePO.getInstanceStatus():" , instancePO.getInstanceStatus());
            }
            if (instancePO != null) {
                changeLeaderRequest.setLeaderId(instancePO.getInstanceUuid());
                response = clientFactory.createRdsClient().instanceGroupForceChangeLeader(groupId, changeLeaderRequest);
            }
        } else {
            response = clientFactory.createRdsClient().instanceGroupForceChangeLeader(groupId, changeLeaderRequest);
        }
        return response;
    }

    public void batchjoin(BatchJoinRequest request) {
        clientFactory.createRdsClient().batchjoin(request);
    }

    public List<Instance> availableList(String azone, String engine) {
        RDSClient rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_2);
        InstanceListResponse instancesSource = rdsClient.instanceList(null);

        /**
         * 限制条件
         * MySQL 5.6/5.7/8.0 双机主实例
         * 实例status为available
         * 实例lock_mode为unlock
         * 实例不属于任何热活组，即关联的groupId为空
         * 实例gtidType为1
         * 实例复制方式为异步复制 async
         * 实例未开启tde
         * 通用型实例不允许加入热活组 目前对内部用户解开限制
         */

        boolean isInternalUser = othersService.checkInternalUser();
        List<Instance> instances = (List<Instance>) instancesSource.getInstances();
        List<Instance> result = new ArrayList<>();
        switch (engine.toLowerCase()) {
            case "mysql":
                getGroupAvailableListForMysql(isInternalUser, instances, result, azone);
                break;
            case "postgresql":
                getGroupAvailableListForPostgresql(instances, result, azone);
                break;
            default:
                getGroupAvailableListForMysql(isInternalUser, instances, result, azone);
                break;
        }
        return result;
    }

    public void getGroupAvailableListForMysql(boolean isInternalUser, List<Instance> instances,
                                              List<Instance> result, String azone) {
        if (isInternalUser) {
            for (Instance instance : instances) {
                if (RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instance.getEngine())
                        && RDSConstant.RDS_GROUP_AVAILABLE_ENGINE_VERSION.contains(instance.getEngineVersion())
                        && RDSConstant.INSTANCE_TYPE_MASTER.equalsIgnoreCase(instance.getInstanceType())
                        && RDSConstant.APPLICATION_TYPE_NORMAL.equalsIgnoreCase(instance.getApplicationType())
                        && RDSConstant.INSTANCE_STATUS_CREATE_REPLICA.equalsIgnoreCase(instance.getInstanceStatus())
                        && RDSConstant.LOCK_MODE_UNLOCK.equalsIgnoreCase(instance.getLockMode())
                        && StringUtils.isEmpty(instance.getGroupId())
                        && instance.getGtidType() == 1
                        && RDSConstant.REPLICATION_TYPE_ASYNC.equalsIgnoreCase(instance.getReplicationType())
                        && !RDSConstant.TDE_STATUS_OPEN.equalsIgnoreCase(instance.getTdeStatus())) {
                    // 如果需要根据可用区筛选，需单独判断
                    if (StringUtils.isNotEmpty(azone)) {
                        if (azone.equalsIgnoreCase(instance.getAzone())) {
                            result.add(instance);
                        }
                    } else {
                        result.add(instance);
                    }
                }
            }

            // 绑定短 ID
            List<String> longIds = new ArrayList<>();
            for (Instance instance : result) {
                longIds.add(instance.getInstanceId());
                if (StringUtils.isNotEmpty(instance.getSourceInstanceId())) {
                    longIds.add(instance.getSourceInstanceId());
                }
            }
            Map<String, String> idMap = idMapperService.idMapByLongId(longIds);
            for (Instance instance : result) {
                String shortId = idMap.get(instance.getInstanceId());
                if (StringUtils.isEmpty(shortId)) {
//                throw new RDSExceptions.ResourceServerException();
                    LOGGER.warn("available list does not find shortId,instanceUuid:{}", instance.getInstanceId());
                    continue;
                }
                instance.setInstanceShortId(shortId);
            }
        } else {
            for (Instance instance : instances) {
                if (RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instance.getEngine())
                        && RDSConstant.RDS_GROUP_AVAILABLE_ENGINE_VERSION.contains(instance.getEngineVersion())
                        && RDSConstant.INSTANCE_TYPE_MASTER.equalsIgnoreCase(instance.getInstanceType())
                        && RDSConstant.APPLICATION_TYPE_NORMAL.equalsIgnoreCase(instance.getApplicationType())
                        && RDSConstant.INSTANCE_STATUS_CREATE_REPLICA.equalsIgnoreCase(instance.getInstanceStatus())
                        && RDSConstant.LOCK_MODE_UNLOCK.equalsIgnoreCase(instance.getLockMode())
                        && StringUtils.isEmpty(instance.getGroupId())
                        && instance.getGtidType() == 1
                        && RDSConstant.REPLICATION_TYPE_ASYNC.equalsIgnoreCase(instance.getReplicationType())
                        && !RDSConstant.TDE_STATUS_OPEN.equalsIgnoreCase(instance.getTdeStatus())
                        && !RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType())) {
                    // 如果需要根据可用区筛选，需单独判断
                    if (StringUtils.isNotEmpty(azone)) {
                        if (azone.equalsIgnoreCase(instance.getAzone())) {
                            result.add(instance);
                        }
                    } else {
                        result.add(instance);
                    }
                }
            }

            // 绑定短 ID
            List<String> longIds = new ArrayList<>();
            for (Instance instance : result) {
                longIds.add(instance.getInstanceId());
                if (StringUtils.isNotEmpty(instance.getSourceInstanceId())) {
                    longIds.add(instance.getSourceInstanceId());
                }
            }
            Map<String, String> idMap = idMapperService.idMapByLongId(longIds);
            for (Instance instance : result) {
                String shortId = idMap.get(instance.getInstanceId());
                if (StringUtils.isEmpty(shortId)) {
//                throw new RDSExceptions.ResourceServerException();
                    LOGGER.warn("available list does not find shortId,instanceUuid:{}", instance.getInstanceId());
                    continue;
                }
                instance.setInstanceShortId(shortId);
            }
        }
    }

    public void getGroupAvailableListForPostgresql(List<Instance> instances,
                                                   List<Instance> result, String azone) {
        for (Instance instance : instances) {
            if (RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(instance.getEngine())
                    && RDSConstant.RDS_PG_GROUP_AVAILABLE_ENGINE_VERSION.contains(instance.getEngineVersion())
                    && RDSConstant.INSTANCE_TYPE_MASTER.equalsIgnoreCase(instance.getInstanceType())
                    && RDSConstant.APPLICATION_TYPE_NORMAL.equalsIgnoreCase(instance.getApplicationType())
                    && RDSConstant.INSTANCE_STATUS_CREATE_REPLICA.equalsIgnoreCase(instance.getInstanceStatus())
                    && RDSConstant.LOCK_MODE_UNLOCK.equalsIgnoreCase(instance.getLockMode())
                    && StringUtils.isEmpty(instance.getGroupId())) {
                // 如果需要根据可用区筛选，需单独判断
                if (StringUtils.isNotEmpty(azone)) {
                    if (azone.equalsIgnoreCase(instance.getAzone())) {
                        result.add(instance);
                    }
                } else {
                    result.add(instance);
                }
            }
        }

        // 绑定短 ID
        List<String> longIds = new ArrayList<>();
        for (Instance instance : result) {
            longIds.add(instance.getInstanceId());
            if (StringUtils.isNotEmpty(instance.getSourceInstanceId())) {
                longIds.add(instance.getSourceInstanceId());
            }
        }
        Map<String, String> idMap = idMapperService.idMapByLongId(longIds);
        for (Instance instance : result) {
            String shortId = idMap.get(instance.getInstanceId());
            if (StringUtils.isEmpty(shortId)) {
//                throw new RDSExceptions.ResourceServerException();
                LOGGER.warn("available list does not find shortId,instanceUuid:{}", instance.getInstanceId());
                continue;
            }
            instance.setInstanceShortId(shortId);
        }
    }
}
