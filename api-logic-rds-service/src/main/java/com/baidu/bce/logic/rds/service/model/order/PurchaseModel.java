package com.baidu.bce.logic.rds.service.model.order;

import com.baidu.bce.internalsdk.rds.model.instance.InstanceCreateRequest;
import com.baidu.bce.internalsdk.rds.model.instance.OrderItemExtraInfo;

import javax.validation.constraints.NotNull;

/**
 * Created by luping03 on 17/11/7.
 */
public class PurchaseModel extends InstanceCreateRequest {

    private Integer duration;

    @NotNull
    private String productType;

    private int autoRenewTime;

    private String autoRenewTimeUnit;

    public int getAutoRenewTime() {
        return autoRenewTime;
    }

    public void setAutoRenewTime(int autoRenewTime) {
        this.autoRenewTime = autoRenewTime;
    }

    public String getAutoRenewTimeUnit() {
        return autoRenewTimeUnit;
    }

    public void setAutoRenewTimeUnit(String autoRenewTimeUnit) {
        this.autoRenewTimeUnit = autoRenewTimeUnit;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public OrderItemExtraInfo convertToOrderItemExtraInfo() {
        OrderItemExtraInfo extraInfo = new OrderItemExtraInfo();
        InstanceCreateRequest instanceCreateRequest = new InstanceCreateRequest();
        instanceCreateRequest.setInstanceAmount(this.getInstanceAmount());
        instanceCreateRequest.setInstanceParameters(this.getInstanceParameters());
        extraInfo.setInstanceCreateRequest(instanceCreateRequest);
        extraInfo.setAutoRenewTime(this.autoRenewTime);
        extraInfo.setAutoRenewTimeUnit(this.autoRenewTimeUnit);
        return extraInfo;
    }

}
