package com.baidu.bce.logic.rds.service.model.otherservice;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by luping03 on 17/12/20.
 */
public class ZoneDetailList {
    private List<ZoneDetail> zones = new ArrayList<>();

    public List<ZoneDetail> getZones() {
        return zones;
    }

    public void setZones(List<ZoneDetail> zones) {
        this.zones = zones;
    }

    public static class ZoneDetail {
        private List<String> zoneNames =  new ArrayList<>();

        private List<String> apiZoneNames =  new ArrayList<>();

        private boolean available = true;

        private String zoneNameStr;

        private String defaultSubnetId;

        private int maxCpuCount;

        private int maxMemory;

        private int maxStorage;

        private boolean supportNvmeDisk = Boolean.FALSE;

        private String zoneType = "normal";

        public String getZoneType() {
            return zoneType;
        }

        public void setZoneType(String zoneType) {
            this.zoneType = zoneType;
        }

        public int getMaxCpuCount() {
            return maxCpuCount;
        }

        public void setMaxCpuCount(int maxCpuCount) {
            this.maxCpuCount = maxCpuCount;
        }

        public int getMaxMemory() {
            return maxMemory;
        }

        public void setMaxMemory(int maxMemory) {
            this.maxMemory = maxMemory;
        }

        public int getMaxStorage() {
            return maxStorage;
        }

        public void setMaxStorage(int maxStorage) {
            this.maxStorage = maxStorage;
        }

        public List<String> getApiZoneNames() {
            return apiZoneNames;
        }

        public void setApiZoneNames(List<String> apiZoneNames) {
            this.apiZoneNames = apiZoneNames;
        }

        public String getZoneNameStr() {
            return zoneNameStr;
        }

        public void setZoneNameStr(String zoneNameStr) {
            this.zoneNameStr = zoneNameStr;
        }

        public String getDefaultSubnetId() {
            return defaultSubnetId;
        }

        public void setDefaultSubnetId(String defaultSubnetId) {
            this.defaultSubnetId = defaultSubnetId;
        }

        public List<String> getZoneNames() {
            return zoneNames;
        }

        public void setZoneNames(List<String> zoneNames) {
            this.zoneNames = zoneNames;
        }

        public boolean isAvailable() {
            return available;
        }

        public void setAvailable(boolean available) {
            this.available = available;
        }

        public boolean isSupportNvmeDisk() {
            return supportNvmeDisk;
        }

        public void setSupportNvmeDisk(boolean supportNvmeDisk) {
            this.supportNvmeDisk = supportNvmeDisk;
        }
    }
}
