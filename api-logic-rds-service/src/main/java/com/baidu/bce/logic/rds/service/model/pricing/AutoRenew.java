package com.baidu.bce.logic.rds.service.model.pricing;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

public class AutoRenew {

    @NotNull
    @Valid
    private String autoRenewTimeUnit;

    @NotNull
    @Valid
    private int autoRenewTime;

    @NotNull
    @Valid
    private List<String> instanceIds;

    public AutoRenew() {
    }

    public String getAutoRenewTimeUnit() {
        return autoRenewTimeUnit;
    }

    public void setAutoRenewTimeUnit(String autoRenewTimeUnit) {
        this.autoRenewTimeUnit = autoRenewTimeUnit;
    }

    public int getAutoRenewTime() {
        return autoRenewTime;
    }

    public void setAutoRenewTime(int autoRenewTime) {
        this.autoRenewTime = autoRenewTime;
    }

    public List<String> getInstanceIds() {
        return instanceIds;
    }

    public void setInstanceIds(List<String> instanceIds) {
        this.instanceIds = instanceIds;
    }
}
