package com.baidu.bce.logic.rds.service;

import com.baidu.bce.console.settings.util.ClientFactory;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.CheckExistResponse;
import com.baidu.bce.internalsdk.rds.model.account.*;
import com.baidu.bce.internalsdk.rds.model.iam.IamDecryptRequest;
import com.baidu.bce.internalsdk.rds.model.iam.IamDecryptResponse;
import com.baidu.bce.internalsdk.rds.model.iam.IamEncryptRequest;
import com.baidu.bce.internalsdk.rds.model.iam.IamEncryptResponse;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.logic.rds.service.constant.AccountType;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.model.IsExistResponse;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * Created by luping03 on 17/10/11.
 */
@Service
public class AccountService {

    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(AccountService.class);

    @Autowired
    LogicRdsClientFactory logicRdsClientFactory;

    @Autowired
    InstanceService instanceService;
    @Autowired
    private ClientFactory clientFactory;

    public AccountListResponse list(String instanceId, String from) {
        instanceService.checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);
        AccountListResponse response =
                logicRdsClientFactory.createRdsClientByInstanceId(instanceId).accountList(instanceId);
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            standardForApi(response.getAccounts());
        }
        return response;
    }

    private void standardForApi(Collection<Account> accounts) {
        if (accounts == null || accounts.size() == 0) {
            return;
        }
        for (Account account : accounts) {
            standardForApi(account);
        }
    }

    private void standardForApi(Account account) {
        if (account != null) {
            account.setSuperUserFlag(BasisUtils.upperCaseFirstChar(account.getSuperUserFlag()));
            account.setAccountStatus(BasisUtils.upperCaseFirstChar(account.getAccountStatus()));
            account.setType(AccountType.getApiValue(account.getType()));
            if (account.getDatabasePrivileges() == null || account.getDatabasePrivileges().size() == 0) {
                return;
            }
            for (Account.DatabasePrivilege privilege : account.getDatabasePrivileges()) {
                privilege.setAuthType(BasisUtils.upperCaseFirstChar(privilege.getAuthType()));
            }
        }
    }

    public void create(String instanceId, Account account, String from, String ak) {
        Instance instance = instanceService
                .checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);

        String pw = account.getPassword();
        if (from.equalsIgnoreCase(RDSConstant.FROM_API)) {
            pw = decryptPasswordFromIam(account.getPassword(), ak);
            account.setPassword(pw);
        }
//        if (!Pattern.matches(PatternString.PATTERN_ACCOUNT_PW, pw)) {
//            LOGGER.error("create account password invaild {}", pw);
//            throw new RDSExceptions.ParamValidationException();
//        }

        checkCreateAccount(instance, account);

        RDSClient rdsClient = logicRdsClientFactory.createRdsClientByInstanceId(instanceId);
        rdsClient.accountCreate(instanceId, account);
    }

    private String decryptPasswordFromIam(String encryptPassword, String ak) {
        String password = null;
        try {
            IamDecryptRequest decryptRequest =
                    new IamDecryptRequest().withCipherHex(encryptPassword).withAccesskeyId(ak);
            IamDecryptResponse decryptResponse = logicRdsClientFactory.createIamApiClient().decrypt(decryptRequest);
            password = decryptResponse.getRawHex();

            byte[] bytes = BasisUtils.parseHexStr2Byte(decryptResponse.getRawHex());

            if (ArrayUtils.isNotEmpty(bytes)) {
                password = new String(bytes);
            }

            LOGGER.info("decrypt string : source {},  pw {}", encryptPassword, password);
        } catch (Exception e) {
            LOGGER.error("decrypt password from iam error = {}", e);
            throw new RDSExceptions.AdminPassDecryptionException();
        }

        if (StringUtils.isEmpty(password)) {
            throw new RDSExceptions.AdminPassDecryptionException();
        }

        return password;
    }

    public void checkCreateAccount(Instance instance, Account account) {
        // SuperUserFlag 参数校验
        if (StringUtils.isEmpty(account.getSuperUserFlag())) {
            account.setSuperUserFlag(RDSConstant.SUPER_USER_FLAG_COMMON);
        } else {
            account.setSuperUserFlag(BasisUtils.lowerCaseFirstChar(account.getSuperUserFlag()));
            if (!account.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_SUPER)
                    && !account.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_COMMON)) {
                throw new RDSExceptions.ParamValidationException();
            }
        }
        // Type 参数校验
        if (StringUtils.isEmpty(account.getType())) {
            account.setType(AccountType.MASTER.getValue());
        } else {
            account.type(AccountType.getValue(account.getType()));
            if (!account.getType().equals(AccountType.MASTER.getValue())
                    && !account.getType().equals(AccountType.PROXY.getValue())) {
                throw new RDSExceptions.ParamValidationException();
            }
        }
        // DatabasePrivileges 参数校验
        if (account.getDatabasePrivileges() != null && account.getDatabasePrivileges().size() != 0) {
            for (Account.DatabasePrivilege privilege : account.getDatabasePrivileges()) {
                privilege.setAuthType(BasisUtils.lowerCaseFirstChar(privilege.getAuthType()));
            }
        }

        if (RDSConstant.INSTANCE_TYPE_MASTER.equalsIgnoreCase(instance.getInstanceType())
                || RDSConstant.INSTANCE_TYPE_RAFT.equalsIgnoreCase(instance.getInstanceType())) {
            RDSClient rdsClient = logicRdsClientFactory.createRdsClientByInstanceId(instance.getInstanceId());
            Collection<Account> accounts = rdsClient.accountList(instance.getInstanceId()).getAccounts();
            // super账号只允许创建一个
            if (account.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_SUPER)) {
                for (Account ac : accounts) {
                    if (ac.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_SUPER)) {
//                        throw new RDSExceptions.SuperUserExistException();
                        throw new RDSBusinessExceptions.RDSSuperUserExistException();
                    }
                }
            }
            // 若实例的数据库引擎为SQLServer，则只允许创建Common账号
            if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER)
                    && !RDSConstant.SUPER_USER_FLAG_COMMON.equalsIgnoreCase(account.getSuperUserFlag())) {
                throw new RDSExceptions.InvalidAction();
            }
        } else {
            throw new RDSExceptions.InvalidAction();
        }
    }

    public AccountGetResponse detail(String instanceId, String accountName, String from) {
        instanceService.checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);
        AccountGetResponse response = logicRdsClientFactory
                .createRdsClientByInstanceId(instanceId).accountDescribe(instanceId, accountName);
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            standardForApi(response.getAccount());
        }
        return response;
    }

    public void updateRemark(String instanceId, String accountName, AccountUpdateRemarkRequest remarkRequest) {
        if (logicRdsClientFactory.getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            // raft版操作
            logicRdsClientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2)
                    .accountModifyRemarkRaft(instanceId, accountName, remarkRequest.getRemark());
        } else {
            logicRdsClientFactory.createRdsClient()
                    .accountModifyRemark(instanceId, accountName, remarkRequest.getRemark());
        }

    }

    public void updatePW(String instanceId, String accountName, AccountUpdatePasswordRequest pwRequest,
                         String from, String ak) {
        if (logicRdsClientFactory.getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            // raft版操作
            logicRdsClientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2)
                    .accountModifyPasswordRaft(instanceId, accountName, pwRequest.getPassword(), pwRequest.getEncryptedPassword());
        } else {
            String pw = pwRequest.getPassword();
            if (from.equalsIgnoreCase(RDSConstant.FROM_API)) {
                pw = decryptPasswordFromIam(pwRequest.getPassword(), ak);
                pwRequest.setPassword(pw);
            }
            logicRdsClientFactory.createRdsClient()
                    .accountModifyPassword(instanceId, accountName, pwRequest.getPassword(), pwRequest.getEncryptedPassword());
        }
    }

    public void updateLock(String instanceId, String accountName, String lockMode) {
        logicRdsClientFactory.createRdsClient()
                    .accountModifyLock(instanceId, accountName, lockMode);

    }

    public void updatePrivileges(String instanceId, String accountName,
                                 AccountUpdatePrivilegesRequest request, String ETag, String from) {
        instanceService.checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);

        if (RDSConstant.FROM_API.equalsIgnoreCase(from)
                && request.getDatabasePrivileges() != null && request.getDatabasePrivileges().size() != 0) {
            for (Account.DatabasePrivilege privilege : request.getDatabasePrivileges()) {
                privilege.setAuthType(BasisUtils.lowerCaseFirstChar(privilege.getAuthType()));
            }
        }

        if (logicRdsClientFactory.getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            // raft版操作
            logicRdsClientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2)
                    .accountModifyPrivilegeRaft(instanceId, accountName, request.getDatabasePrivileges(), ETag);
        } else {
            logicRdsClientFactory.createRdsClient()
                    .accountModifyPrivilege(instanceId, accountName, request.getDatabasePrivileges(), ETag);
        }
    }

    public void deleteAccount(String instanceId, String accountName, String from) {

        if (ObjectUtils.equals(from, "api")
                && BasisUtils.isShortId(instanceId)) {

            String instanceUUId = instanceService.findInsntaceUUidByShortId(instanceId);

            LOGGER.info("deleteAccount findInsntaceUUidByShortId instanceId : {}, instanceUUId : {}",
                    instanceId, instanceUUId);

            // 替换成uuid
            instanceId = instanceUUId;

            logicRdsClientFactory.createRdsClient(RDSConstant.SERVICE_NAME).accountDelete(instanceId, accountName);
        } else {
            logicRdsClientFactory.createRdsClientByInstanceId(instanceId).accountDelete(instanceId, accountName);
        }
    }

    public IsExistResponse accountCheck(String instanceId, String accountName) {
        IsExistResponse response = new IsExistResponse();
        CheckExistResponse checkResponse = null;
        if (logicRdsClientFactory.getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            // raft版操作
            checkResponse = logicRdsClientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2)
                    .accountCheckRaft(instanceId, accountName);
        } else {
            checkResponse = logicRdsClientFactory.createRdsClient()
                    .accountCheck(instanceId, accountName);
        }
        if (checkResponse.getIsExist() == 0) {
            response.setExist(Boolean.FALSE);
        } else {
            response.setExist(Boolean.TRUE);
        }
        return response;
    }

    /**
     *
     * @param pw 密码明文的十六进制
     * @param ak
     * @return 密码密文的十六进制
     */
    public String encrypt(String pw, String ak) {
        String password = null;
        try {
            IamEncryptRequest encryptRequest =
                    new IamEncryptRequest().withRawHex(pw).withUserId(logicRdsClientFactory.getUserId());
            IamEncryptResponse encryptResponse = logicRdsClientFactory.createIamApiClient().encrypt(encryptRequest);
//            byte[] bytes = BasisUtils.parseHexStr2Byte(encryptResponse.getCipherHex());
//            if (ArrayUtils.isNotEmpty(bytes)) {
//                password = new String(bytes);
//            }
            password = encryptResponse.getCipherHex();
        } catch (Exception e) {
            LOGGER.error("decrypt password from iam error = {}", e);
            throw new RDSExceptions.AdminPassDecryptionException();
        }

        if (StringUtils.isEmpty(password)) {
            throw new RDSExceptions.AdminPassDecryptionException();
        }

        return password;
    }

    public BnsListResponse accountBnsList(String instanceId, String accountName) {
       return logicRdsClientFactory.createRdsClient().accountBnsList(instanceId, accountName);
    }

    public UpdateBnsResponse accountBns(UpdateBnsRequest request) {
        return logicRdsClientFactory.createRdsClient().accountBns(request);
    }
}
