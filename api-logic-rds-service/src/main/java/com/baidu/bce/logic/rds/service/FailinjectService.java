package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.FailinjectResponse;
import com.baidu.bce.internalsdk.rds.model.FailinjectRequest;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class FailinjectService {
    @Autowired
    IdMapperService idMapperService;

    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(FailinjectService.class);

    @Autowired
    LogicRdsClientFactory logicRdsClientFactory;

    public FailinjectRequest getFailinjectWhitelist() {
        FailinjectRequest response =
                logicRdsClientFactory.createRdsClient().getFailinjectWhitelist();
        if (null != response.getAppList() && response.getAppList().size() > 0) {
            List shortIds = getInstanceIdList(response.getAppList());
            response.setAppList(shortIds);
        }
        return response;
    }

    public void putFailinjectWhitelist(FailinjectRequest request) {
        if (null != request.getAppList() && request.getAppList().size() > 0) {
            List longIds = getInstanceUuidList(request.getAppList());
            request.setAppList(longIds);
        }
        logicRdsClientFactory.createRdsClient().putFailinjectWhitelist(request);

    }

    public void removeFailinjectWhitelist(FailinjectRequest request) {
        if (null != request.getAppList() && request.getAppList().size() > 0) {
            List longIds = getInstanceUuidList(request.getAppList());
            request.setAppList(longIds);
        }
        logicRdsClientFactory.createRdsClient().removeFailinjectWhitelist(request);

    }

    public FailinjectResponse postFailinject(String appId) {
        return logicRdsClientFactory.createRdsClient().postFailinject(appId);

    }

    public List<String> getInstanceUuidList(List<String> appList) {
        List<String> newAppList = new ArrayList<>();
        for (String instanceId : appList) {
            if ("*".equals(instanceId)) {
                newAppList.add(instanceId);
                continue;
            }
            String longId = idMapperService.getInstanceUuid(instanceId);
            newAppList.add(longId);
        }
        return  newAppList;
    }
    public List<String> getInstanceIdList(List<String> appList) {
        List<String> newAppList = new ArrayList<>();
        for (String instanceId : appList) {
            if ("*".equals(instanceId)) {
                newAppList.add(instanceId);
                continue;
            }
            String shortId = idMapperService.getInstanceId(instanceId);
            newAppList.add(shortId);
        }
        return  newAppList;
    }
}
