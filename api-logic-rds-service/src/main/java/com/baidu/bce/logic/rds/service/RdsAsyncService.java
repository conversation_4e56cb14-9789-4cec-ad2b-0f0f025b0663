/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.GetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.logic.rds.dao.mybatis.InstanceMapper;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.resource.manager.sdk.ResManagerClient;
import com.baidu.bce.plat.resource.manager.sdk.SyncLockClient;
import com.baidu.bce.plat.resource.manager.sdk.SyncResClient;
import com.baidu.bce.plat.resource.manager.sdk.model.AcquireSyncLockRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.GroupTreeResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ReleaseSyncLockRequest;
import com.baidu.bce.plat.resource.manager.sdk.model.Resource;
import com.baidu.bce.plat.resource.manager.sdk.model.SyncResMessage;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupDetailResponse;
import com.baidu.bce.plat.resource.manager.sdk.model.ResGroupDetailRequest;
import com.baidu.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

@SuppressWarnings("WeakerAccess")
@Service
public class RdsAsyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RdsAsyncService.class);

    @Value("${rds.console.instance_detail.url_prefix:https://console.bce.baidu.com/rds/#/rds/detail~instanceId=}")
    private String rdsConsoleInstanceDetailUrlPrefix;

    @Autowired
    private InstanceMapper instanceMapper;

    private void setThreadEnv(String requestId) {
        BceInternalRequest.setThreadRequestId(requestId);
        BceInternalRequest.setLogThreadRequestId(false);
    }

    @Async
    public Future<Map<String, com.baidu.bce.internalsdk.order.model.Resource>>
    resourceList(ResourceClient resourceClient, GetResourcesRequest resourcesRequest, String requestId) {
        setThreadEnv(requestId);
        Map<String, com.baidu.bce.internalsdk.order.model.Resource> resourceHashMap = new HashMap<>();
        Resources resources = resourceClient.list(resourcesRequest);
        if (resources != null) {
            for (com.baidu.bce.internalsdk.order.model.Resource resource : resources) {
                if (!resource.getStatus().equals(ResourceStatus.DESTROYED)) {
                    resourceHashMap.put(resource.getName(), resource);
                }
            }
        }
        return new AsyncResult<>(resourceHashMap);
    }


    @Async
    public void syncResource(SyncResClient syncResClient, SyncLockClient lock, String syncType, String serviceType,
                             List<com.baidu.bce.plat.resource.manager.sdk.model.Resource> resources, String reqId,
                             String region) {
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }
        AcquireSyncLockRequest lockRequest = new AcquireSyncLockRequest();
        lockRequest.setServiceType(serviceType);
        lockRequest.setRegion(region);
        lockRequest.setDuration(RDSConstant.SYNC_RES_DURATION);
        setThreadEnv(reqId);
        try {
            int count = 0;
            while (count < RDSConstant.MAX_RETRY_TIMES) {
                count++;
                boolean locked = lock.acquireLock(lockRequest);
                if (!locked) {
                    Thread.sleep(count * lockRequest.getDuration());
                    continue;
                }
                SyncResMessage message = new SyncResMessage();
                message.setResources(resources);
                message.setSyncType(syncType);
                LOGGER.info("[{}] Sync {} Resources size {}.", reqId, syncType, resources.size());
                LOGGER.info("Sync message detail => {}.", new Gson().toJson(message));
                // 代码中多处都会向资源管理同步消息，但设置的 id uuid 等字段有些混乱，这里统一矫正
                adjustMessage(message);
                LOGGER.info("[After adjustMessage] Sync message detail => {}.", new Gson().toJson(message));
                syncResClient.send(serviceType, message);
                break;
            }
        }  catch (InterruptedException e) {
            LOGGER.error("Acquire sync Resource lock error.", e);
        } finally {
            ReleaseSyncLockRequest unlockReq = new ReleaseSyncLockRequest();
            unlockReq.setRegion(lockRequest.getRegion());
            unlockReq.setServiceType(serviceType);
            lock.releaseLock(unlockReq);
        }
    }

    /**
     * 调整同步到资源管理的数据，尽力矫正
     *
     * @param syncResMessage 同步消息
     */
    private void adjustMessage(SyncResMessage syncResMessage) {
        for (Resource resource : syncResMessage.getResources()) {
            if (BasisUtils.isShortId(resource.getId()) && BasisUtils.isLongId(resource.getUuid())) {
                // resourceId, resourceUUid 均不需要矫正
                resource.setUrl(rdsConsoleInstanceDetailUrlPrefix + resource.getUuid());
                continue;
            }

            String instanceId = null;
            String instanceUuid = null;
            if (BasisUtils.isShortId(resource.getId())) {
                instanceId = resource.getId();
            }
            if (instanceId == null && BasisUtils.isShortId(resource.getUuid())) {
                instanceId = resource.getUuid();
            }
            if (BasisUtils.isLongId(resource.getId())) {
                instanceUuid = resource.getId();
            }
            if (instanceUuid == null && BasisUtils.isLongId(resource.getUuid())) {
                instanceUuid = resource.getUuid();
            }

            if (instanceId == null && instanceUuid != null) {
                instanceId = instanceMapper.queryInstanceId(instanceUuid);
            }
            if (instanceUuid == null && instanceId != null) {
                instanceUuid = instanceMapper.queryInstanceUuid(instanceId);
            }

            if (BasisUtils.isShortId(instanceId)) {
                resource.setId(instanceId);
            }
            if (BasisUtils.isLongId(instanceUuid)) {
                resource.setUuid(instanceUuid);
            }
            if (BasisUtils.isLongId(resource.getUuid())) {
                resource.setUrl(rdsConsoleInstanceDetailUrlPrefix + resource.getUuid());
            }
        }
    }

    @Async
    public Future<GroupTreeResponse> getResourceGroup(ResManagerClient resManagerClient,
                                                          String name,
                                                          String requestId) {
        setThreadEnv(requestId);
        GroupTreeResponse group = resManagerClient.getGroup(name);
        return new AsyncResult<>(group);
    }

    @Async
    public Future<ResGroupDetailResponse> getResourceGroups(ResManagerClient resManagerClient,
                                                            ResGroupDetailRequest resRequest,
                                                            String requestId) {
        setThreadEnv(requestId);
        ResGroupDetailResponse resGroupListResp;
        try {
            resGroupListResp = resManagerClient.getResGroupBatch(resRequest);
        } catch (Exception e) {
            // 降级
            LOGGER.error("[async] pack res group error. skip", e);
            return new AsyncResult<>(null);
        }
        return new AsyncResult<>(resGroupListResp);
    }

}

