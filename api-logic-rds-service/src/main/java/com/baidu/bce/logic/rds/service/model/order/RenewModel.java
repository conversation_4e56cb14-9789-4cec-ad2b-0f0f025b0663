package com.baidu.bce.logic.rds.service.model.order;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Created by luping03 on 17/11/9.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RenewModel {
    @NotNull
    @IdPermission
    private String instanceId;
    @Min(1)
    private Integer duration;

    // resource id
    private String uuid;

    @Override
    public String toString() {
        return "DashboardInstanceRechargeModel{"
                + "instanceId='" + instanceId + '\''
                + ", duration=" + duration
                + '}';
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
