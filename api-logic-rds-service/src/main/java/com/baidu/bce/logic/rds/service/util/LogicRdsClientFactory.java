package com.baidu.bce.logic.rds.service.util;

import com.baidu.bce.apiservice.client.ApiServiceClient;
import com.baidu.bce.billing.resourcemanager.client.ResourceClientFactory;
import com.baidu.bce.billing.resourcemanager.service.ChargeResourceService;
import com.baidu.bce.billing.resourcemanager.service.ResourceQueryService;
import com.baidu.bce.externalsdk.logical.network.subnet.ExternalSubnetClient;
import com.baidu.bce.externalsdk.logical.network.eni.EniExternalClient;
import com.baidu.bce.externalsdk.logical.network.vpc.ExternalVpcClient;
import com.baidu.bce.finance.sdk.facade.FinanceFactoryFacade;
import com.baidu.bce.finance.sdk.finance.AccountClient;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.AccessKey;
import com.baidu.bce.internalsdk.iam.model.Token;
import com.baidu.bce.internalsdk.order.OrderBatchClient;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.OrderClientV2;
import com.baidu.bce.internalsdk.order.PricingClientV3;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.ResourceClientV2;
import com.baidu.bce.internalsdk.rds.AssumeRoleRequest;
import com.baidu.bce.internalsdk.rds.BLBClient;
import com.baidu.bce.internalsdk.qualify.QualifyClientV2;
import com.baidu.bce.internalsdk.rds.AllPayPromotionClient;
import com.baidu.bce.internalsdk.rds.BccClient;
import com.baidu.bce.internalsdk.rds.BccHanClient;
import com.baidu.bce.internalsdk.rds.BceKmsClient;
import com.baidu.bce.internalsdk.rds.BcmClient;
import com.baidu.bce.internalsdk.rds.DbscClient;
import com.baidu.bce.internalsdk.rds.EdgeClient;
import com.baidu.bce.internalsdk.rds.IamApiClient;
import com.baidu.bce.internalsdk.rds.MsgGroupClient;
import com.baidu.bce.internalsdk.rds.RDSAuditClient;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.RDSClient3;
import com.baidu.bce.internalsdk.rds.RDSLogicClient;
import com.baidu.bce.internalsdk.rds.SlowqueryClient;
import com.baidu.bce.internalsdk.rds.SmartDbaClient;
import com.baidu.bce.internalsdk.sms.SMSClient;
import com.baidu.bce.internalsdk.sts.StsClient;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.internalsdk.zone.ZoneClient;
import com.baidu.bce.logic.core.authentication.IamLogicService;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.service.VpcClient;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.plat.resource.manager.sdk.ResManagerClient;
import com.baidu.bce.plat.resource.manager.sdk.SyncLockClient;
import com.baidu.bce.plat.resource.manager.sdk.SyncResClient;
import com.baidu.bce.plat.servicecatalog.ServiceCatalogOrderClient;
import com.baidu.bce.pricing.service.PricingQueryService;
import com.baidu.bce.pricing.service.client.PricingClientFactory;
import com.baidu.bce.sdk.renew.AutoRenewClient;
import com.baidu.bce.service.bus.sdk.BusClient;
import com.baidu.bce.user.settings.sdk.UserSettingsClient;
import com.baidubce.Protocol;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.auth.DefaultBceSessionCredentials;
import com.baidubce.services.kms.KmsClient;
import com.baidubce.services.kms.KmsClientConfiguration;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Created by luping03 on 17/10/11.
 */
@Component
public class LogicRdsClientFactory implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogicRdsClientFactory.class);

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Value("${iam.sts.rolename:}")
    private String roleName = "";

    @Value("${iam.console.username}")
    protected String consoleUsernameInIAM;

    @Override
    public void afterPropertiesSet() throws Exception {
        if (StringUtils.isBlank(roleName)) {
            roleName = "BceServiceRole_" + consoleUsernameInIAM.toUpperCase();
        }
    }

    private LoadingCache<AssumeRoleRequest, StsCredential> stsCredentialCache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(50, TimeUnit.MINUTES)
            .build(new CacheLoader<AssumeRoleRequest, StsCredential>() {
                @Override
                public StsCredential load(AssumeRoleRequest request) {
                    AccessKey accessKey = iamLogicService.getConsoleAccessKey();
                    String endpoint = EndpointManager.getInstance().getRegion(regionConfiguration.getCurrentRegion())
                            .getEndpoint(com.baidu.bce.internalsdk.rds.StsClient.SERVICE_NAME);
                    com.baidu.bce.internalsdk.rds.StsClient stsClient =
                            new com.baidu.bce.internalsdk.rds.StsClient(endpoint,
                                    accessKey.getAccess(), accessKey.getSecret());

                    request.setRoleName(roleName);
                    // 临时accesskey的有效期，不能超过2小时
                    // 3600 秒，比 50 分钟多 10 分钟
                    request.setDurationSeconds(3600);
                    return stsClient.assumeRole(request);
                }
            });

    @Autowired
    private IamLogicService iamLogicService;

    @Value("${iam.console.username}")
    private String consoleUserName;

    @Value("${iam.console.password}")
    private String consolePassword;

    @Autowired
    private ResourceClientFactory resourceClientFactory;

    @Autowired
    private InstanceDao instanceDao;

    @Value("${msggroup.access_token:}")
    private String msgGroupAccessToken;


    public RDSClient createRdsClient() {
        StsCredential stsCredential = getAccountStsAccessKey(); // getUserStsAccessKey();
        RDSClient rdsClient = new RDSClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public EdgeClient createEdgeClient() {
        StsCredential stsCredential = getAccountStsAccessKey();
        return new EdgeClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
    }

    public RDSClient createRdsClientByAccountId(String accountId) {
        com.baidu.bce.internalsdk.sts.model.StsCredential stsCredential = iamLogicService
                .getUserStsAccessKeyWithToken(accountId);
        RDSClient rdsClient = new RDSClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public RDSClient createRdsClientByAccountId(String accountId, String service) {
        com.baidu.bce.internalsdk.sts.model.StsCredential stsCredential = iamLogicService
                .getUserStsAccessKeyWithToken(accountId);
        RDSClient rdsClient = new RDSClient(EndpointManager.getEndpoint(service), stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public RDSClient createRdsClientV2ByAccountId(String accountId) {
        com.baidu.bce.internalsdk.sts.model.StsCredential stsCredential = iamLogicService
                .getUserStsAccessKeyWithToken(accountId);
        return new RDSClient(EndpointManager.getEndpoint(RDSConstant.SERVICE_NAME_V2), stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
    }


    public RDSClient createRdsClient(String serviceName) {
        StsCredential stsCredential = getAccountStsAccessKey();
        RDSClient rdsClient = new RDSClient(EndpointManager.getEndpoint(serviceName),
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public RDSClient createRdsClientByInstanceId(String instanceId) { // 通过instanceId进行判断获取使用新版还是老版接口

        if (getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) { // raft版RDS，走新的接口
            return createRdsClient(RDSConstant.SERVICE_NAME_V2);
        }
        return createRdsClient(RDSConstant.SERVICE_NAME);
    }
    public RDSClient3 createRdsClient3() {
        StsCredential stsCredential = getAccountStsAccessKey();
        RDSClient3 rdsClient = new RDSClient3(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public RDSClient3 createRdsClient3(String accountId) {
        StsCredential stsCredential = getUserStsAccessKey(accountId);
        RDSClient3 rdsClient = new RDSClient3(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public RDSAuditClient createRdsAuditClient() {
        StsCredential stsCredential = getAccountStsAccessKey();
        RDSAuditClient rdsClient = new RDSAuditClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public RDSAuditClient createRdsAuditClientByAccountId(String accountId) {
        //  此处由于管控存在启停账号白名单限制，故先使用 billing 的账号签名
        String billingAccountId = "74570a7ba1ac49729a9e4766891f9318";
        com.baidu.bce.internalsdk.sts.model.StsCredential stsCredential = iamLogicService
                .getUserStsAccessKeyWithToken(billingAccountId);
        return new RDSAuditClient(EndpointManager.getEndpoint("RDSAUDIT"), stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
    }

    public RDSClient2 createRdsClient2() {
        StsCredential stsCredential = getAccountStsAccessKey();
        RDSClient2 rdsClient = new RDSClient2(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public RDSClient2 createRdsClient2(String serviceName) {
        StsCredential stsCredential = getAccountStsAccessKey();
        RDSClient2 rdsClient = new RDSClient2(EndpointManager.getEndpoint(serviceName),
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }
    public RDSClient2 createRdsClient2ByInstanceId(String instanceId) {
        if (getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            // raft版操作
            return createRdsClient2(RDSConstant.SERVICE_NAME_V2);
        }

        return createRdsClient2(RDSConstant.SERVICE_NAME_2);
    }

    public RDSClient2 createRdsClient2ByUserId(String userId) {
        StsCredential stsCredential = getUserStsAccessKey(userId);
        RDSClient2 rdsClient = new RDSClient2(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public RDSClient2 createRdsClient2ByUserId(String userId, String service) {
        StsCredential stsCredential = getUserStsAccessKey(userId);
        RDSClient2 rdsClient = new RDSClient2(EndpointManager.getEndpoint(service + "-V2"),
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public RDSClient2 createRdsClient2V2ByUserId(String userId) {
        StsCredential stsCredential = getUserStsAccessKey(userId);
        RDSClient2 rdsClient = new RDSClient2(EndpointManager.getEndpoint(RDSConstant.SERVICE_NAME_V2),
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    // 数据同步时使用，raft版
    public RDSClient2 createRdsClient2ByUserId2(String userId) {
        StsCredential stsCredential = getUserStsAccessKey(userId);
        RDSClient2 rdsClient = new RDSClient2(EndpointManager.getEndpoint(RDSConstant.SERVICE_NAME_V2),
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public RDSClient createRdsMigrationClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        return new RDSClient(EndpointManager.getEndpoint("RDS.migration"),
                stsCredetial.getAccessKeyId(), stsCredetial.getSecretAccessKey(), stsCredetial.getSessionToken());
    }

    public ResourceClient createResourceClient() {
        StsCredential stsCredential = getAccountStsAccessKey();
        ResourceClient resourceClient = new ResourceClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        resourceClient.setSecurityToken(stsCredential.getSessionToken());
        return resourceClient;
    }

    public ResourceQueryService createResourceQueryService() {
        StsCredential stsCredential = getAccountStsAccessKey();
        String resourceEndpoint = "http://resource-manager.bce-billing.baidu-int.com:8671";
        ResourceQueryService resourceQueryService = resourceClientFactory.createResourceQueryService(resourceEndpoint,
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(), stsCredential.getSessionToken());
        return resourceQueryService;
    }

    public ChargeResourceService createChargeResourceService() {
        StsCredential stsCredential = getAccountStsAccessKey();
        String resourceEndpoint = "http://resource-manager.bce-billing.baidu-int.com:8671";
        ChargeResourceService chargeResourceService = resourceClientFactory.createChargeResourceService(resourceEndpoint,
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(), stsCredential.getSessionToken());
        return chargeResourceService;
    }

    public ResourceClient getResourceClient() {
        AccessKey consoleAccessKey = iamLogicService.getConsoleAccessKey();
        ResourceClient resourceClient = new ResourceClient(
                consoleAccessKey.getAccess(), consoleAccessKey.getSecret());
        return resourceClient;
    }

    public ResourceClient createResourceClientByUserId(String userId) {
        StsCredential stsCredential = getUserStsAccessKey(userId);
        ResourceClient resourceClient = new ResourceClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        resourceClient.setSecurityToken(stsCredential.getSessionToken());
        return resourceClient;
    }

    public IAMClient createIamClient() {
        IAMClient iamClient = new IAMClient();
        Token consoleToken = iamClient.getConsoleToken(consoleUserName, consolePassword);
        iamClient.setxAuthToken(consoleToken.getId());
        return iamClient;
    }

    /**
     * 创建V2 resource Client
     *
     * userId=null: 是服务号访问
     *
     * @param userId
     * @return
     */
    public ResourceClientV2 createResourceClientV2(String userId) {
        if (StringUtils.isEmpty(userId)) {
            AccessKey consoleAccessKey = iamLogicService.getConsoleAccessKey();
            return getResourceClientV2(consoleAccessKey.getAccess(), consoleAccessKey.getSecret());
        } else {
            StsCredential stsCredential = getUserStsAccessKey(userId);
            ResourceClientV2 resourceClient = getResourceClientV2(stsCredential.getAccessKeyId(),
                    stsCredential.getSecretAccessKey());
            resourceClient.setSecurityToken(stsCredential.getSessionToken());
            return resourceClient;
        }
    }

    public ResourceClientV2 createResourceClientV2() {
        StsCredential stsCredential = getUserStsAccessKey();
        ResourceClientV2 resourceClient =
                new ResourceClientV2(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        resourceClient.setSecurityToken(stsCredential.getSessionToken());
        return resourceClient;
    }

    /**
     * resource client v2
     *
     * @param ak
     * @param sk
     * @return
     */
    private ResourceClientV2 getResourceClientV2(String ak, String sk) {

        return new ResourceClientV2(ak, sk) {
            @Override
            protected BceInternalRequest createOrderRequest() {
                BceInternalRequest resq =  super.createAuthorizedRequest();
                return resq.requestId(UUID.randomUUID().toString());
            }
        };
    }

    public LogicalTagClient createLogicalTagClient() {
        StsCredential stsCredential = getAccountStsAccessKey();
        LogicalTagClient logicalTagClient = new LogicalTagClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        logicalTagClient.setSecurityToken(stsCredential.getSessionToken());
        return logicalTagClient;
    }

    public LogicalTagClient createLogicalTagClient(String accountId) {
        com.baidu.bce.internalsdk.sts.model.StsCredential stsCredential = getUserStsAccessKey(accountId);
        return new LogicalTagClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
    }

    public AutoRenewClient createAutoRenewClient() {
        StsCredential stsCredential = getAccountStsAccessKey();
        return new AutoRenewClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
    }

    public AutoRenewClient createAutoRenewClient(String userId) {
        com.baidu.bce.internalsdk.sts.model.StsCredential stsCredential = getUserStsAccessKey(userId);
        return new AutoRenewClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
    }

    public BccHanClient createBccHanClient() {
        StsCredential stsCredential = getAccountStsAccessKey();
        return new BccHanClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
    }

    public ZoneClient createZoneClient() {
        StsCredential stsCredential = getAccountStsAccessKey();
        ZoneClient zoneClient = new ZoneClient(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        zoneClient.setSecurityToken(stsCredential.getSessionToken());
        return zoneClient;
    }

    public ExternalVpcClient createVpcClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        ExternalVpcClient client = new ExternalVpcClient(stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    public ExternalVpcClient createVpcClientByUserId() {
        StsCredential stsCredetial = getUserStsAccessKey(getUserId());
        ExternalVpcClient client = new ExternalVpcClient(stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    public ExternalSubnetClient createSubnetClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        ExternalSubnetClient client = new ExternalSubnetClient(stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    public ExternalSubnetClient createSubnetClientByUserId() {
        StsCredential stsCredetial = getUserStsAccessKey(getUserId());
        ExternalSubnetClient client = new ExternalSubnetClient(stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    public ApiServiceClient createApiServiceClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        ApiServiceClient client = new ApiServiceClient(stsCredetial.getAccessKeyId(), stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    public OrderClient getOrderClient() {
        AccessKey consoleAccessKey = iamLogicService.getConsoleAccessKey();
        return new OrderClient(consoleAccessKey.getAccess(), consoleAccessKey.getSecret());
    }

    public OrderClient createOrderClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        OrderClient client = new OrderClient(stsCredetial.getAccessKeyId(), stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }
    public EniExternalClient createEniExternalClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        EniExternalClient client = new EniExternalClient(stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    // 支持 to_postpay 批量接口
    public OrderClientV2 createOrderClientV2() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        OrderClientV2 client = new OrderClientV2(stsCredetial.getAccessKeyId(), stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    public ServiceCatalogOrderClient createServiceCatalogOrderClient () {
        StsCredential stsCredetial = getUserStsAccessKey();
        ServiceCatalogOrderClient client = new ServiceCatalogOrderClient(stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey(), getUserId());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    public OrderBatchClient createOrderBatchClient () {
        StsCredential stsCredetial = getUserStsAccessKey();
        OrderBatchClient client = new OrderBatchClient(stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    public UserSettingsClient createUserSettingsClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        UserSettingsClient client =  new UserSettingsClient(stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    public SMSClient getSMSClient() {
        AccessKey consoleAccessKey = iamLogicService.getConsoleAccessKey();
        return new SMSClient(consoleAccessKey.getAccess(), consoleAccessKey.getSecret());
    }

//    public PricingQueryClient createPricingQueryClient() {
//        StsCredential stsCredetial = getAccountStsAccessKey();
//        PricingClientFactory factory = new PricingClientFactory(EndpointManager.getEndpoint("Price"),
//                stsCredetial.getAccessKeyId(), stsCredetial.getSecretAccessKey(), stsCredetial.getSessionToken());
//        return factory.createPricingQueryClient();
//    }

    /**
     * 接口文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/HXFtYvbMQj/4Gmld5Stw3/Zj408vpRQlU_sB
     * @return
     */
    public AllPayPromotionClient createAllPayPromotionClient() {
//        StsCredential stsCredential = getUserStsAccessKey();
        AccessKey consoleAccessKey = iamLogicService.getConsoleAccessKey();
        // 此接口固定使用billing侧aksk
        AllPayPromotionClient priceClient = new AllPayPromotionClient(EndpointManager.getEndpoint("Promotion"),
                consoleAccessKey.getAccess(), consoleAccessKey.getSecret());
        return priceClient;
    }
    public PricingQueryService createNewPricingQueryClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        PricingQueryService pricingQueryService
                = new PricingClientFactory().createClient(EndpointManager.getEndpoint("Price"),
                stsCredetial.getAccessKeyId(), stsCredetial.getSecretAccessKey(), stsCredetial.getSessionToken());
        return pricingQueryService;
    }

    public PricingClientV3 createPricingClientV3() {
        StsCredential stsCredential = getUserStsAccessKey();
        PricingClientV3 priceClient = new PricingClientV3(stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey());
        priceClient.setSecurityToken(stsCredential.getSessionToken());
        return priceClient;
    }

    public StsCredential getUserStsAccessKey() {
        return iamLogicService.getUserStsAccessKey(LogicUserService.getUserId());
    }

    public StsCredential getUserStsAccessKey(String userId) {
        return iamLogicService.getUserStsAccessKeyWithToken(userId);
    }

    public StsCredential getAccountStsAccessKey() {
        return iamLogicService.getUserStsAccessKey(LogicUserService.getAccountId());
    }

    public String getAccountId() {
        return LogicUserService.getAccountId();
    }

    /**
     * 当前请求用户的 userId
     *
     * @return
     */
    public String getUserId() {
        return LogicUserService.getUserId();
    }

    /**
     * @return 判断当前用户是否是子帐号（协作者／普通子帐号）
     */
    public boolean isSubuserLogin() {
        return !LogicUserService.getAccountId().equals(LogicUserService.getUserId());
    }

    /**
     * @return 判断当前用户是否是主账号
     */
    public boolean isRoot() {
        return LogicUserService.getSubjectToken().isRoot();
    }
    /**
     * 当前请求用户的手机号
     *
     * @return
     */
    public String getUserMobile() {
        return LogicUserService.getUserMobile();
    }

    public String getInstanceTypeByUuid(String instanceId) {
        InstancePO instancePO = instanceDao.queryInstanceByInstanceUuid(instanceId, getAccountId());
        if (instancePO == null && BasisUtils.isLongId(instanceId)) {
            return "!" + RDSConstant.INSTANCE_TYPE_RAFT;
            // throw new RDSExceptions.ResourceNotExistException();
        }
        if (instancePO == null) {
            throw new RDSExceptions.ResourceNotExistException();
        }
        return instancePO.getInstanceType();
    }

    public IamApiClient createIamApiClient() {
        AccessKey accessKey = iamLogicService.getConsoleAccessKey();
        return new IamApiClient(accessKey.getAccess(), accessKey.getSecret());
    }

    public SlowqueryClient createSlowqueryClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        // TODO
        return new SlowqueryClient(EndpointManager.getEndpoint("SlowQuery"), stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey(), stsCredetial.getSessionToken());
    }

    public BceKmsClient createBceKmsClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        // TODO
        return new BceKmsClient("https://nmg02-bce-test2.nmg02:8091", stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey(), stsCredetial.getSessionToken());
    }

    public KmsClient createKmsClient() {
        StsCredential consoleAccessKey = getAccountStsAccessKey();

        KmsClientConfiguration config = new KmsClientConfiguration();
        config.setCredentials(new DefaultBceCredentials(
                "37ad71da54a44b45b710c54785881216",
                "214230ab624a42d6ab908a28dd192259"));
        config.setEndpoint("nmg02-bce-test2.nmg02:8091");
//        config.setEndpoint("kms.sanbox.baidu.com");
        config.setProtocol(Protocol.HTTPS);

        KmsClient client = new KmsClient(config);
        return client;
    }

    public KmsClient createKmsClientV2() {
        StsCredential consoleAccessKey = getAccountStsAccessKey();

        KmsClientConfiguration config = new KmsClientConfiguration();
        config.setCredentials(new DefaultBceCredentials(
                consoleAccessKey.getAccessKeyId(),
                consoleAccessKey.getSecretAccessKey()));
        config.setEndpoint("nmg02-bce-test2.nmg02:8091");
//        config.setEndpoint("kms.sanbox.baidu.com");
        config.setProtocol(Protocol.HTTPS);

        KmsClient client = new KmsClient(config);
        return client;
    }

    public KmsClient createKmsClientV3() {
        StsCredential consoleAccessKey = getAccountStsAccessKey();

        KmsClientConfiguration config = new KmsClientConfiguration();
        config.setCredentials(new DefaultBceSessionCredentials(
                consoleAccessKey.getAccessKeyId(),
                consoleAccessKey.getSecretAccessKey(),
                consoleAccessKey.getSessionToken()));
        config.setEndpoint("nmg02-bce-test2.nmg02:8091");
//        config.setEndpoint("kms.sanbox.baidu.com");
        config.setProtocol(Protocol.HTTPS);

        KmsClient client = new KmsClient(config);
        return client;
    }

//    private StsCredential getStsCredential(String authHeader) {
//        StsCredential stsCredential = new StsCredential();
//
//        AccessKey accessKey = iamLogicService.getConsoleAccessKey();
//        StsClient stsClient = new StsClient(accessKey.getAccess(), accessKey.getSecret());
//
//        Token token = UserService.getSubjectToken();
//        stsCredential = stsClient.createAuthorizedRequest()
//                .path("/credential")
//                .setIsHostWithPort(true)
//                .queryParam("assumeRole", "")
//                .queryParam("accountId", UserService.getSubjectToken().getAccountId())
//                .queryParam("roleName", "BceServiceRole_console_rds")
//                .post(StsCredential.class);
//
//        return stsCredential;
//    }

    public RDSLogicClient createRdsLogicClient(String serviceName) {
        StsCredential stsCredential = getAccountStsAccessKey();
        RDSLogicClient rdsClient = new RDSLogicClient(EndpointManager.getEndpoint(serviceName),
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        rdsClient.setSecurityToken(stsCredential.getSessionToken());
        return rdsClient;
    }

    public ResManagerClient resourceGroupClient() {
        StsCredential stsCredential = getAccountStsAccessKey();
        ResManagerClient resManagerClient = new ResManagerClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        resManagerClient.setSecurityToken(stsCredential.getSessionToken());
        return resManagerClient;
    }

    public ResManagerClient resourceGroupClient(String accountId) {
        com.baidu.bce.internalsdk.sts.model.StsCredential stsCredential = getUserStsAccessKey(accountId);
        ResManagerClient resManagerClient = new ResManagerClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        resManagerClient.setSecurityToken(stsCredential.getSessionToken());
        return resManagerClient;
    }

    public SyncLockClient createSyncLockClient() {
        AccessKey accessKey = iamLogicService.getConsoleAccessKey();
        return new SyncLockClient(
                accessKey.getAccess(), accessKey.getSecret()
        );
    }


    public SyncLockClient createSyncLockClient(String accountId) {
        StsCredential stsCredential = getUserStsAccessKey(accountId);
        SyncLockClient syncLockClient = new SyncLockClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        syncLockClient.setSecurityToken(stsCredential.getSessionToken());
        return syncLockClient;
    }

    public SyncResClient createSyncResClient() {
        return new SyncResClient();
    }

    public RDSClient createRdsClientForConsole() {
        AccessKey consoleAccessKey = iamLogicService.getConsoleAccessKey();
        IAMClient iamClient = createIamClient();
        return new RDSClient(
                iamClient.getxAuthToken(), consoleAccessKey.getAccess(), consoleAccessKey.getSecret(),
        null, null);
    }


    public BLBClient createBlbClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        return new BLBClient(
                stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey(),
                stsCredetial.getSessionToken()
        );
    }

    public BccClient createBccClient() {
        StsCredential stsCredetial = getAccountStsAccessKey();
        return new BccClient(
                stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey(),
                stsCredetial.getSessionToken()
        );
    }

    public BcmClient createBcmClient() {
        StsCredential stsCredential = getAccountStsAccessKey();
        return new BcmClient(
                stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken()
        );
    }

    public SmartDbaClient createSmartDbaClient() {
        StsCredential stsCredential = getAccountStsAccessKey();
        return new SmartDbaClient(stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey(), stsCredential.getSessionToken());
    }

    public DbscClient createUserDbscClient() {
        AssumeRoleRequest assumeRoleRequest = new AssumeRoleRequest();
        assumeRoleRequest.setAccountId(LogicUserService.getAccountId());
        assumeRoleRequest.setUserId(LogicUserService.getUserId());
        LOGGER.debug("accountId = {}, userId = {}", LogicUserService.getAccountId(), LogicUserService.getUserId());
        try {
            StsCredential stsCredential = stsCredentialCache.get(assumeRoleRequest);
            return new DbscClient(LogicUserService.getAccountId(), LogicUserService.getUserId(),
                    stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                    stsCredential.getSessionToken());
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    public BusClient createBusClientForConsole() {
        AccessKey accessKey = iamLogicService.getConsoleAccessKey();
        return new BusClient(accessKey.getAccess(), accessKey.getSecret());
    }

    public AccountClient createAccountClient(String accountId) {
        com.baidu.bce.internalsdk.sts.model.StsCredential stsCredential = getUserStsAccessKey(accountId);
        return new FinanceFactoryFacade(stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken()).createAccountClient();
    }

    public QualifyClientV2 createQualifyClient() {
        StsCredential stsCredential = getUserStsAccessKey();
        return new QualifyClientV2(
                stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
    }

    public ResourceClient createResourceClientWithAccountId(String accountId) {
        com.baidu.bce.internalsdk.sts.model.StsCredential stsCredential = getUserStsAccessKey(accountId);
        ResourceClient resourceClient = new ResourceClient(
                stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey());
        resourceClient.setSecurityToken(stsCredential.getSessionToken());
        return resourceClient;
    }

    public MsgGroupClient createMsgGroupClient() {
        return new MsgGroupClient(msgGroupAccessToken);
    }

    public ResManagerClient getResManagerClient(String accountId) {
        com.baidu.bce.internalsdk.sts.model.StsCredential stsCredential = getUserStsAccessKey(accountId);
        ResManagerClient resManagerClient = new ResManagerClient(stsCredential.getAccessKeyId(),
                stsCredential.getSecretAccessKey());
        resManagerClient.setSecurityToken(stsCredential.getSessionToken());
        return resManagerClient;
    }

    public SyncResClient getSyncResClient() {
        return new SyncResClient();
    }

    public SyncLockClient getSyncLockClient() {
        AccessKey accessKey = iamLogicService.getConsoleAccessKey();
        return new SyncLockClient(accessKey.getAccess(), accessKey.getSecret());
    }

    public VpcClient createNewVpcClient() {
        StsCredential stsCredetial = getUserStsAccessKey();
        VpcClient client = new VpcClient(stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }

    public VpcClient createNewVpcClient(String accountId) {
        StsCredential stsCredetial = getUserStsAccessKey(accountId);
        VpcClient client = new VpcClient(stsCredetial.getAccessKeyId(),
                stsCredetial.getSecretAccessKey());
        client.setSecurityToken(stsCredetial.getSessionToken());
        return client;
    }
}
