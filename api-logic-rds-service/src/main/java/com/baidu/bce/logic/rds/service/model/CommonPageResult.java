package com.baidu.bce.logic.rds.service.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel("通用列表")
public class CommonPageResult<T> implements Serializable {
    @ApiModelProperty(value = "总数", required = true)
    private Integer totalCount;
    @ApiModelProperty(value = "页号", required = true)
    private Integer pageNo;
    @ApiModelProperty(value = "本页数量", required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "排序字段", required = true)
    private String orderBy;
    @ApiModelProperty(value = "升序:asc, 降序：desc", required = true)
    private String order;
    @ApiModelProperty(value = "列表结果", required = true)
    private List<T> result = new ArrayList<>();

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public List<T> getResult() {
        return result;
    }

    public void setResult(List<T> result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "CommonPageResult{"
                + "totalCount=" + totalCount
                + ", pageNo=" + pageNo
                + ", pageSize=" + pageSize
                + ", orderBy='" + orderBy + '\''
                + ", order='" + order + '\''
                + ", result=" + result
                + " }";
    }
}
