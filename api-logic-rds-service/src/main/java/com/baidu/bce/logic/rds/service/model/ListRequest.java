package com.baidu.bce.logic.rds.service.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ListRequest implements Serializable {

    private String orderBy;
    private String order;
    private int pageSize;
    private int pageNo;
    private String keywordType;
    private String keyword;
    private String instanceIds;
    /**
     * 过滤字段list
     */
    private List<Filter> filters;

    public ListRequest() {
    }

    /**
     * ************************auto generated******************************
     */
    @Override
    public String toString() {
        return "ListRequest{"
                + "orderBy='" + orderBy + '\''
                + ", order='" + order + '\''
                + ", pageSize=" + pageSize
                + ", pageNo=" + pageNo
                + ", keywordType='" + keywordType + '\''
                + ", keyword='" + keyword + '\''
                + ", instanceIds='" + instanceIds + '\''
                + " }";
    }

    public String getKeywordType() {
        return keywordType;
    }

    public void setKeywordType(String keywordType) {
        this.keywordType = keywordType;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public List<Filter> getFilters() {
        return filters;
    }

    public void setFilters(List<Filter> filters) {
        this.filters = filters;
    }

    public String getInstanceIds() {
        return instanceIds;
    }

    public void setInstanceIds(String instanceIds) {
        this.instanceIds = instanceIds;
    }

    public static class Filter {
        private String keyword;
        private String keywordType;
        private String subKeywordType;

        public String getKeyword() {
            return keyword;
        }

        public void setKeyword(String keyword) {
            this.keyword = keyword;
        }

        public String getKeywordType() {
            return keywordType;
        }

        public void setKeywordType(String keywordType) {
            this.keywordType = keywordType;
        }

        public String getSubKeywordType() {
            return subKeywordType;
        }

        public void setSubKeywordType(String subKeywordType) {
            this.subKeywordType = subKeywordType;
        }
    }

}
