/*
 * Copyright (C) 2015 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.service.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RDSConstant {
//    "creating,available,rebooting,backuping,restoring," +
//            "readReplicating,initing,lockExpiration,lockDiskQuota,migrating,modifying,masterModifying," +
//                    "readReplicateModifying"
    public static final String INSTANCE_STATUS_CREATE_REPLICA = "available";
    public static final String INSTANCE_STATUS_CREATE_PROXY = "available,rebooting,backuping,restoring,"
            + "readReplicating,initing,lockDiskQuota,migrating,modifying,masterModifying,"
            + "readReplicateModifying";

    public static final String INSTANCE_STATUS_OLD_SYNC = "available,rebooting,backuping,restoring,"
            + "readReplicating,initing,migrating,modifying,masterModifying,readReplicateModifying";

    public static final String INSTANCE_STATUS_SYNC = "available,rebooting,backuping,restoring,"
            + "readReplicating,initing,migrating,modifying,masterModifying,readReplicateModifying";

    public static final String INSTANCE_STATUS_DELETE = "available,lockExpiration,suspended";

    public static final String INSTANCE_STATUS_UNUSING = "creating,isolated,isolating";

    public static final String INSTANCE_STATUS_MODIFY_NAME = "available,lockDiskQuota";

    public static final String INSTANCE_STATUS_MODIFY_PUBLICACCESS = "available,lockDiskQuota";

    public static final String INSTANCE_STATUS_MODIFY_DOMAIN = "available,lockDiskQuota";

    public static final String INSTANCE_STATUS_REBOOT = "available,lockDiskQuota";

    public static final String INSTANCE_STATUS_RESIZE = "available,lockDiskQuota,maintaining";

    public static final String INSTANCE_STATUS_WHITELIST = "available,rebooting,backuping,restoring,"
            + "readReplicating,lockExpiration,lockDiskQuota,migrating,modifying,masterModifying,"
            + "readReplicateModifying,tdeOpening,azoneChanging,tableRestoring,rebootable,switchable,switching," +
            "suspending,suspended,starting,readOnly,readOnlyModifing";

    public static final String INSTANCE_STATUS_MODIFY_REPLICATIONTYPE = "available,backuping,renaming";

    public static final String ILLEGAL_REQUEST_MESSAGE = "Illegal request, authentication failed.";

    public static final String BACK_UP_TYPE_LOGICAL = "logical";
    public static final String BACK_UP_TYPE_PHYSICAL = "physical";

    // mysql engine and version
    public static final String RDS_ENGINE_MYSQL = "MySQL";
    public static final String RDS_ENGINE_MYSQL_LOWER_CASE = "mysql";
    public static final String RDS_ENGINE_MYSQL_VERSION = "5.6";
    public static final List<String> RDS_ENGINE_MYSQL_CAN_CREATE_PROXY = Arrays.asList("5.5", "5.6", "5.7", "8.0");
    public static final List<String> RDS_ENGINE_MYSQL_CAN_CREATE_REPLICA = Arrays.asList("5.5", "5.6", "5.7", "8.0");
    public static final String RDS_ENGINE_MYSQL_VERSION_NEED_WHITELIST = "5.5";
    public static final List<String> RDS_ENGINE_MYSQL_VERSIONS = Arrays.asList("5.5", RDS_ENGINE_MYSQL_VERSION,
            "5.7", "8.0");
    public static final List<String> RDS_FINANCE_ENGINE_MYSQL_VERSIONS = Arrays.asList(RDS_ENGINE_MYSQL_VERSION, "5.7");

    public static final List<String> RDS_GROUP_AVAILABLE_ENGINE_VERSION = Arrays.asList("5.6", "5.7", "8.0");
    public static final List<String> RDS_PG_GROUP_AVAILABLE_ENGINE_VERSION =
            Arrays.asList("10", "11", "12", "13", "14", "15", "16", "17");

    public static final List<String> RDS_CDS_ENGINE_MYSQL_VERSIONS = Arrays.asList("8.0", "5.7");

    public static final List<String> RDS_SSD_ENGINE_MYSQL_VERSIONS = Arrays.asList("5.5", "5.6");

    // sqlserver engine and version
    public static final String RDS_ENGINE_SQLSERVER = "sqlserver";
    public static final String RDS_ENGINE_SQLSERVER_VERSION = "2008r2";
    public static final String RDS_ENGINE_SQLSERVER_VERSION2012 = "2012"; // 单机版
    public static final String RDS_ENGINE_SQLSERVER_VERSION2012SP3 = "2012sp3"; // 双机版
    public static final String RDS_ENGINE_SQLSERVER_VERSION2016 = "2016sp1";
    public static final String RDS_ENGINE_SQLSERVER_SINGLEVERSION2016 = "2016"; // 2016单机版
    public static final String RDS_ENGINE_SQLSERVER_VERSION2017 = "2017";
    public static final String RDS_ENGINE_SQLSERVER_SINGLEVERSION2017 = "2017sp1";
    public static final String RDS_ENGINE_SQLSERVER_VERSION2019  = "2019";
    public static final List<String> RDS_ENGINE_SQLSERVER_VERSIONS = Arrays.asList(RDS_ENGINE_SQLSERVER_VERSION,
            RDS_ENGINE_SQLSERVER_VERSION2012, RDS_ENGINE_SQLSERVER_VERSION2012SP3, RDS_ENGINE_SQLSERVER_VERSION2016,
            RDS_ENGINE_SQLSERVER_SINGLEVERSION2016,
            RDS_ENGINE_SQLSERVER_VERSION2017, RDS_ENGINE_SQLSERVER_SINGLEVERSION2017, RDS_ENGINE_SQLSERVER_VERSION2019);

    public static final List<String> RDS_CDS_ENGINE_VERSIONS = Arrays.asList("5.5", "5.6", "10");

    // pg engine and version
    public static final String RDS_ENGINE_PG = "postgresql";
    public static final List<String> RDS_ENGINE_PG_VERSION =
            Arrays.asList("9.4", "9.5", "10", "11", "12", "13", "14", "15", "16", "17");

    // rdsproxy
    public static final String RDS_ENGINE_PROXY = "rdsproxy";
    public static final List<Integer> RDS_PROXY_NODEAMOUNT = Arrays.asList(2, 4, 6, 8, 16);

    // mysql memory and storage
    public static final List<Integer> RDS_MEMORY_RANGE_MYSQL =
            Arrays.asList(256, 512, 1024, 2048, 4096, 8192, 16384, 24576, 32768, 49152, 65536);
    public static final Integer RDS_STORAGE_MYSQL_MIN = 5;
    public static final Integer RDS_STORAGE_MYSQL_MAX = 1000;
    // mysql memory and storage 三节点增强版
    public static final List<Integer> RDS_FINANCE_MEMORY_RANGE_MYSQL =
            Arrays.asList(8192, 16384, 24576, 32768, 49152, 65536);
    public static final Integer RDS_FINANCE_STORAGE_MYSQL_MIN = 100;
    public static final Integer RDS_FINANCE_STORAGE_MYSQL_MAX = 1000;
    // mysql memory and storage 三节点增强版 沙盒用
    public static final List<Integer> RDS_FINANCE_MEMORY_RANGE_MYSQL_SANDBOX =
            Arrays.asList(256, 512, 1024, 2048, 4096, 8192, 16384, 24576, 32768, 49152, 65536);
    public static final Integer RDS_FINANCE_STORAGE_MYSQL_MIN_SANDBOX = 5;
    public static final Integer RDS_FINANCE_STORAGE_MYSQL_MAX_SANDBOX = 1000;

    // pg memory and storage
    public static final List<Integer> RDS_MEMORY_RANGE_PG =
            Arrays.asList(512, 1024, 2048, 4096, 8192, 16384, 24576, 32768, 49152, 65536);
    public static final Integer RDS_STORAGE_PG_MIN = 5;
    public static final Integer RDS_STORAGE_PG_MAX = 1000;

    // sqlserver memory and storage
    public static final List<Integer> RDS_MEMORY_RANGE_SQLSERVER =
            Arrays.asList(1024, 2048, 4096, 8192, 12288, 16384, 24576, 32768, 49152, 65536);
    public static final Integer RDS_STORAGE_SQLSERVER_MIN = 10;
    public static final Integer RDS_STORAGE_SQLSERVER_MAX = 1000;
    public static final List<String> QUERY_PRICE_TIME_UNIT = Arrays.asList("MINUTE", "HOUR", "DAY", "MONTH", "YEAR");

    public static final List<Integer> RDS_DURATION_RANGE =
            Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 24, 36);

    public static final Map<Integer, Integer> MEMORY_CPU_MAP = new HashMap<Integer, Integer>() {
        {
            put(256, 1); put(512, 1); put(1024, 2); put(2048, 2); put(4096, 4); put(8192, 6);
            put(12288, 6); put(16384, 8); put(24576, 12); put(32768, 16); put(49152, 20); put(65536, 20);
        }
    };

    public static final Double MIN_BALANCE = Double.valueOf(100);

    public static final String SCENE_NEW = "NEW"; // 新建

    public static final String AUTO_RENEW_TASK = "auto_renew";


    public static final String PRODUCTTYPE_PREPAY = "prepay";
    public static final String PRODUCTTYPE_POSTPAY = "postpay";

    public static final String RDS_POSTPAY_TIME_UNIT = "minute";
    public static final String RDS_PREPAY_TIME_UNIT = "month";


    // instanceType 实例类型
    public static final String INSTANCE_TYPE_MASTER = "master";
    public static final String INSTANCE_TYPE_REPLICA = "readReplica";
    public static final String INSTANCE_TYPE_REPLICA_LOWER_CASE = "readreplica";
    public static final String INSTANCE_TYPE_PROXY = "rdsproxy";
    public static final String API_RDS_PREFIX = "rds";
    public static final String INSTANCE_TYPE_RAFT = "financial";
    public static final String INSTANCE_TYPE_RDS_SINGLE = "singleton";

    public static final String REPLICA_TYPE_BASIC = "basic";
    public static final String REPLICA_TYPE_HA = "ha";

    // eipstatus 外网状态
    public static final String EIP_STATUS_CLOSED = "closed";
    public static final String EIP_STATUS_CTEATING = "creating";
    public static final String STATUS_AVAILABLE = "available";
    public static final String RECYCLE_STATUS = "isolated,isolating";

    public static final List<String> RDS_TO_BCM_UNAVIALABLE_STATUS =
            Arrays.asList("isolated", "deleting", "deleted", "failed");
    public static final List<String> RDS_STATUS_CAN_START =
            Arrays.asList("isolated", "deleting", "failed", "isolating", "deleted");

    // ReplicationType 同步模式
    public static final String REPLICATION_TYPE_ASYNC = "async";
    public static final String REPLICATION_TYPE_SEMISYNC = "semi_sync";
    public static final String REPLICATION_TYPE_MODIFYING = "modifying";

    // SuperUserFlag 标示是否有super账号
    public static final String SUPER_USER_FLAG_SUPER = "super";
    public static final String SUPER_USER_FLAG_COMMON = "common";

    public static final String AUTH_TYPE_R = "readOnly";
    public static final String AUTH_TYPE_RW = "readWrite";

    // 请求来源
    public static final String FROM_API = "api";
    public static final String FROM_CONSOLE = "console";
    public static final String FROM_API_V2 = "API_V2";
    public static final String FROM_INTERNAL = "internal";

    // category系列
    public static final String CATEGORY_BASIC = "Basic";
    public static final String CATEGORY_STANDARD = "Standard";
    public static final String CATEGORY_ENHANCED = "Enhanced";
    public static final String CATEGORY_FINANCIAL = "Financial";
    // 单机版SINGLETON
    public static final String CATEGORY_SINGLETON = "Singleton";
    public static final List<String> RDS_CATEGORYS =
            Arrays.asList(CATEGORY_BASIC, CATEGORY_STANDARD, CATEGORY_ENHANCED, CATEGORY_FINANCIAL, CATEGORY_SINGLETON);

    // 实例锁定状态
    public static final String LOCK_MODE_UNLOCK = "unlock";
    public static final String LOCK_MODE_LOCKEXPIRATION = "lockExpiration";
    public static final String LOCK_MODE_LOCKDISKQUOTA = "lockDiskQuota";

    public static final String EXCLUSIVE_INSTANCE = "exclusive";
    public static final String SHARED_INSTANCE = "shared";


    public static final String DISK_OF_NORMAL_IO = "normal_io";
    public static final String DISK_OF_CLOUD_NOR = "cloud_nor";
    public static final String DISK_OF_CLOUD_HIGH = "cloud_high";
    public static final String DISK_OF_CLOUD_ENHA = "cloud_enha";

    public static final String IMMEDIATE = "immediate";
    public static final String TIME_WINDOW = "timewindow";


    public static final String RUNNING = "running";
    public static final String FINISHED = "finished";

    // endpoint的serviceName
    public static final String SERVICE_NAME = "RDS";
    public static final String SERVICE_NAME_2 = "RDS2";
    public static final String SERVICE_NAME_3 = "RDS3";
    public static final String SERVICE_NAME_V2 = "RDSV2";   // 新版的RDS接口，暂时用于Raft
    public static final String X_BCE_ACCESS_KEY = "X-Bce-Accesskey";
    public static final List<String> SERVICE_NAME_LIST = Arrays.asList("RDS-bj", "RDS-bjfsg", "RDS-fsh",
            "RDS-fwh", "RDS-gz", "RDS-hb-fsg", "RDS-hkg", "RDS-sin", "RDS-su", "RDS-bd");


    // 权限
    public static final String READ = "READ";
    public static final String WRITE = "WRITE";
    public static final String OPERATE = "OPERATE";
    public static final String FULL_CONTROL = "FULL_CONTROL";
    public static final String CREATE = "CREATE";
    public static final String SERVICE_RDS = "bce:rds";
    public static final String ID_PREFIX = "instance";


    public static final String INSTANCE_NOT_SATISFIABLE = "InstanceNotSatisfiable";
    public static final String DBINSTANCE_STATE_CHANGE = "DbinstanceStateChange";
    public static final String MINOR_VERSION_MAPPING_NOT_FOUND = "MinorVersionMappingNotFound";
    public static final String MISSING_PARAMETER = "MissingParameter";
    public static final String INVALID_PARAMETER_VALUE = "InvalidParameterValue";
    public static final String RDSINSTANCE_NOT_FOUND = "RDSInstanceNotFound";
    public static final String ACCESS_DENIED = "AccessDenied";
    public static final String INSTANCE_STATUS_ERROR = "InstanceStatusError";
    public static final String INVALID_ACTION = "InvalidAction";
    public static final String RDSGROUP_MINOR_VERSION_CHECK_FAILED = "RDSGroupMinorVersionCheckFailed";
    public static final String PERMISSION_DENY = "PermissionDeny";

    public static final String BILLING_ITEMS_OF_GENERAL_NORMAL = "MySQLGeneral";
    public static final String BILLING_ITEMS_OF_DEFAULT_SINGLE = "default";
    public static final String BILLING_ITEMS_OF_MYSQL = "MySQL";
    public static final String BILLING_ITEMS_OF_MYSQL_DEFAULT = "MySQLDefault";
    public static final String BILLING_ITEMS_OF_PROXY_GENERAL = "ProxyGeneral";

    // 应用类型
    // 单机版
    public static final String APPLICATION_TYPE_SINGLE = "single";
    public static final String APPLICATION_TYPE_NORMAL = "normal";


    // 实例字符
    public static final List<String> INSTANCE_CHARACTER_TYPE = Arrays.asList("utf8mb4", "utf8", "latin1", "gbk");

    public static final String RESOURCE_UNBIND = "UNBIND";
    public static final String RESOURCE_BIND = "BIND";

    public static final int SYNC_RES_MAX_SIZE = 1000;
    public static final Long SYNC_RES_DURATION = 60000L;
    public static final int MAX_RETRY_TIMES = 3;

    public static final String ORDER_TIME_OUT = "Create Order Timeout";

    public static final String REGION_PAIRS = "bj-北京;gz-广州;hk-香港;hk02-香港2区;su-苏州;fsh-上海金融云";

    public static final String REGION_EDGE = "edge";

    public static final String FILTER_BY_INSTANCE_ID = "instanceId";
    public static final String FILTER_BY_INSTANCE_SHORT_ID = "instanceShortId";
    public static final String FILTER_BY_INSTANCE_NAME = "instanceName";
    public static final String SYNC_RES_MANAGER_DELETE = "delete";

    public static class I18nConstants {
        public static final String LOCALE_QUERY_PARAM_NAME = "locale";
        public static final String LOCALE_PARAM_SEPARATOR = "-";
    }

    public static final String CONSOLE_API_PREFIX = "/api/rds";
    public static final String OPEN_API_PREFIX = "/v1/instance";

    public static final String TDE_STATUS_OPEN = "open";
    public static final String TDE_STATUS_CLOSE = "close";

    public static final List<String> CONSOLE_RDS_PROXY_APIS = new ArrayList<>();
    static {
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/instance/autoExpansion");
        // SmartDBA 相关，参考 wiki：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/pklgqwcAHE/9bMCJVxHtlKdYK
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/instance");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/topo/list");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/putslowsqlflow");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/delslowsqlflow");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/getslowsqlflow");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/slowsql/table/trend");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/slowsql/stats/digest");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/slowsql/table");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/slowsql/table/column");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/slowsql/table/index");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/slowsql/explain");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/slowsql/stats/duration");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/slowsql/stats/source");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/slowsql/list");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/disk/list");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/db/list");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/tb/list");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/conn/list");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/conn/stati");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/sqlflow");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/smartDba/slowsqlid");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/sqlfilter/allowed");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/sqlfilter/create");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/sqlfilter/delete");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/sqlfilter/update");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/sqlfilter/action");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/sqlfilter/list");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/sqlfilter/detail");
        // 只读组管理
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/rogroup/create");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/rogroup/updateRoGroupProperty");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/rogroup/updateEndpoint");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/rogroup/updatePubliclyAccessible");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/rogroup/join");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/rogroup/updateRoGroupProperty");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/rogroup/updateReload");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/rogroup/leave");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/rogroup/list");
        // 安全管理 - 白名单
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/whitelist2/create");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/whitelist2/delete");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/whitelist2/set");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/whitelist/get");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/whitelist2/get");
        // 安全管理 - DB 防火墙
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/dbfirewall/update_state");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/dbfirewall/detail");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/dbfirewall/sqlinject/list");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/dbfirewall/sqlinject/detail");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/dbfirewall/sqlwhite/list");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/dbfirewall/sqlwhite/create");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/dbfirewall/sqlwhite/delete");
        // 安全管理 - SSL
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/ssl/get");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/ssl/set");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/ssl/get/ca");
        // 安全管理 - TDE
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/instance/checkTde");
        CONSOLE_RDS_PROXY_APIS.add("/api/rds/instance/openTde");
    }

    /**
     * 订单来源：购物车、组合购
     */
    public static final List<String> ORDER_SOURCE_SHOPPING_CART = Arrays.asList("SHOPPING_CART", "MERGE_PURCHASE");

    public static final long SECONDS_OF_ONE_DAY = 86400;
    public static final String TIME_UNIT_WEEK = "week";
    public static final String TIME_UNIT_MONTH = "month";
    public static final List<String> TIME_UNITS = Arrays.asList("week", "month");


    public static final List<String> GENERAL_INSTANCE_SUPPORT_REGION = Arrays.asList("bj", "bd", "gz", "su", "nj");

    public static final List<String> MYSQL_SINGLE_CDS_AVAILABLE_TYPE = Arrays.asList("cloud_high", "cloud_enha");

    public static final List<String> INSTANCE_GROUP_RESIZE_AVAI_STATUS = Arrays.asList("available", "maintaining");

    public static final String SPECIAL_PHYSICAL_ZONE_LCC = "lcc";
}
