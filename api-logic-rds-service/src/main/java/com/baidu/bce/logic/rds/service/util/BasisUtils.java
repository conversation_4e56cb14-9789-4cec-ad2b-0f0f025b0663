package com.baidu.bce.logic.rds.service.util;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.rds.service.exception.BackendExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;

/**
 * Created by luping03 on 17/12/28.
 */
public class BasisUtils {
    // 首字母大写
    public static String upperCaseFirstChar(String name) {
        if (StringUtils.isEmpty(name)) {
            return name;
        }
        char[] cs = name.toCharArray();
        if (cs[0] > 64 && cs[0] < 91) {
            return String.valueOf(cs);
        }
        cs[0] -= 32;
        return String.valueOf(cs);
    }

    // 首字母小写
    public static String lowerCaseFirstChar(String name) {
        if (StringUtils.isEmpty(name)) {
            return name;
        }
        char[] cs = name.toCharArray();
        if (cs[0] > 96 && cs[0] < 123) {
            return String.valueOf(cs);
        }
        cs[0] += 32;
        return String.valueOf(cs);
    }

    public static boolean isLongId(String id) {
        return (id != null) && (id.length() > 12);
    }

    public static boolean isShortId(String id) {
        return (id != null) && (id.length() > 0) && (id.length() <= 12);
    }

    public static String createInstanceName(String engine, String engineVersion, String instanceType, Boolean isClone) {
        if (isClone) {
            return  engine.toLowerCase() + engineVersion.replace(".", "")
                    .replaceAll("r2", "").replaceAll("sp3", "")
                    .replaceAll("sp1", "") + "_replica";
        }
        switch (instanceType) {
            case "master" :
            case "readReplica" :
                return  engine.toLowerCase() + engineVersion.replace(".", "")
                        .replaceAll("r2", "").replaceAll("sp3", "")
                        .replaceAll("sp1", "");
            case "rdsproxy" :
                return "rdsproxy";
            default:
                return "";
        }
    }

    public static int getDefaultPort(String engine) {
        switch (engine) {
            case "MySQL" :
                return 3306;
            case "sqlserver" :
                return 1433;
            case "postgresql" :
                return 5432;
            default:
                return 0;
        }
    }

    public static String parseByte2HexStr(byte[] buf) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }

    /**
     * 转换单位，
     * @param source 字符串
     * @return 字符串
     */
    public static String transforByte2MByte(String source) {

        if (!NumberUtils.isNumber(source)) {
            throw new IllegalArgumentException("source not a number : " + source);
        }

        // Byte
        BigDecimal sourceInBytes = new BigDecimal(source);
        BigDecimal seed1024 = new BigDecimal(1024);
        // 转换为MB（带精度）
        BigDecimal sourceInMB = sourceInBytes.divide(seed1024).divide(seed1024);
        // MB去精度，四舍五入
        long sourceInMBLong = sourceInMB.setScale(0, BigDecimal.ROUND_HALF_DOWN).longValue();
        return String.valueOf(sourceInMBLong);
    }
    /**
     *
     * Map转String
     * 1、获取map的key的set集合 转换成String数组
     * 2、循环String数组 拼接字符串 每个key value 一组间用 顿号隔开
     * @param map
     * @return
     */
    public static String mapConvertString(Map<String, String> map) {
        Set<String> keySet = map.keySet();
        // 将set集合转换为数组
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keyArray.length; i++) {
            // 参数值为空，则不参与签名 这个方法trim()是去空格
            if ((String.valueOf(map.get(keyArray[i]))).trim().length() > 0) {
                sb.append(keyArray[i]).append(":").append(String.valueOf(map.get(keyArray[i])).trim());
            }
            if (i != keyArray.length - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    public static Field getDeclaredFieldByRecursively(Class<?> klass, String fieldName) {
        if (klass == null) {
            return null;
        }
        try {
            return klass.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            return getDeclaredFieldByRecursively(klass.getSuperclass(), fieldName);
        }
    }

    public static void logicRdsExceptions(BceInternalResponseException e) {

        String code = "";

        if (StringUtils.isNotEmpty(e.getCode())) {
            code = e.getCode();
        }
        switch (code) {
            case RDSConstant.INSTANCE_NOT_SATISFIABLE:
                throw new RDSBusinessExceptions.InstanceNotSatisfiableException();
            case RDSConstant.DBINSTANCE_STATE_CHANGE:
                throw new RDSBusinessExceptions.InvalidInstanceStatus();
            case RDSConstant.MINOR_VERSION_MAPPING_NOT_FOUND:
                throw new RDSBusinessExceptions.MinorVersionMissException();
            case RDSConstant.RDSINSTANCE_NOT_FOUND:
                throw new RDSBusinessExceptions.InstanceNotFoundException();
            case RDSConstant.ACCESS_DENIED:
                throw new BackendExceptions.AccessDenied(e);
            case RDSConstant.INVALID_ACTION:
                throw new RDSBusinessExceptions.InvalidAction();
            case RDSConstant.INSTANCE_STATUS_ERROR:
                throw new RDSBusinessExceptions.InstanceNotSatisfiableException();
            case RDSConstant.MISSING_PARAMETER:
                throw new RDSBusinessExceptions.MissingParameterException();
            case RDSConstant.INVALID_PARAMETER_VALUE:
                throw new RDSBusinessExceptions.InvalidParameterException();
            case RDSConstant.RDSGROUP_MINOR_VERSION_CHECK_FAILED:
                throw new RDSBusinessExceptions.RdsGroupMinorVersionCheckException();
            case RDSConstant.PERMISSION_DENY:
                throw new RDSBusinessExceptions.RdsPermissionDenyException();
            default:
                throw new RDSBusinessExceptions.InternalServerErrorException();
        }
    }

    /**
     * 通过反射获取当前实体类中所有属性，校验 key 是否存在
     * @param object
     * @return
     */
   public static boolean checkKeyExists(Object object) {
       Class<?> clazz = object.getClass();
       Field[] fields = clazz.getDeclaredFields();

       for (Field field : fields) {
           field.setAccessible(true);
           try {
               Object value = field.get(object);
               if (value == null) {
                   return false;
               }
           } catch (IllegalAccessException e) {
               e.printStackTrace();
           }
       }

       return true;
   }

}
