package com.baidu.bce.logic.rds.service.util;

import com.baidu.bce.logic.core.utils.UUIDUtil;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

/**
 * Created by luping03 on 17/12/19.
 */
@Component
public class IDGenerator {
    @Autowired
    private InstanceDao instanceDao;

    public String createExternalId(String idPrefix) {
        int retryCount = 0;
        while (retryCount < 10) {
            try {
                String shortId = UUIDUtil.generateShortUuid();
                String id = instanceDao.queryInstanceId(idPrefix + "-" + shortId);
                if (StringUtils.isEmpty(id)) {
                    return idPrefix + "-" + shortId;
                }
            } catch (DuplicateKeyException e) {
                retryCount++;
            }
        }
        throw new DuplicateKeyException("Duplicate external_id in database.");
    }
}
