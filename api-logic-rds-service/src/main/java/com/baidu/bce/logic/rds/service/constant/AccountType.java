package com.baidu.bce.logic.rds.service.constant;

import org.apache.commons.lang.StringUtils;

/**
 * Created by luping03 on 17/12/28.
 */
public enum AccountType {
    MASTER("OnlyMaster", "onlyMaster"),
    PROXY("RdsProxy", "rdsproxy");

    private String apiValue;

    private String value;

    AccountType(String apiValue, String value) {
        this.apiValue = apiValue;
        this.value = value;
    }

    public String getApiValue() {
        return apiValue;
    }

    public static String getApiValue(String value) {
        if (StringUtils.isNotEmpty(value)) {
            AccountType[] arr = values();
            int len = arr.length;

            for (int i = 0; i < len; ++i) {
                AccountType engineType = arr[i];
                if (engineType.getApiValue().equalsIgnoreCase(value)) {
                    return engineType.getApiValue();
                }
            }
        }
        return value;
    }

    public void setApiValue(String apiValue) {
        this.apiValue = apiValue;
    }

    public String getValue() {
        return value;
    }

    public static String getValue(String value) {
        if (StringUtils.isNotEmpty(value)) {
            AccountType[] arr = values();
            int len = arr.length;

            for (int i = 0; i < len; ++i) {
                AccountType engineType = arr[i];
                if (engineType.getValue().equalsIgnoreCase(value)) {
                    return engineType.getValue();
                }
            }
        }
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}

