package com.baidu.bce.logic.rds.service.model.otherservice;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by luping03 on 17/6/8.
 */
public class AutoRenewDetail {
    private String uuid;
    private String shortId;
    private String serviceId;
    private String resourceUuid;
    private String name;
    private String region;
    private String serviceType;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Date expiredTime;
    private String renewTimeUnit;
    private int renewTime;
    private String status;
    // 订单状态，to_postpay, to_prepay,""
    private String orderStatus;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getShortId() {
        return shortId;
    }

    public void setShortId(String shortId) {
        this.shortId = shortId;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getResourceUuid() {
        return resourceUuid;
    }

    public void setResourceUuid(String resourceUuid) {
        this.resourceUuid = resourceUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    public Date getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(Date expiredTime) {
        this.expiredTime = expiredTime;
    }

    public String getRenewTimeUnit() {
        return renewTimeUnit;
    }

    public void setRenewTimeUnit(String renewTimeUnit) {
        this.renewTimeUnit = renewTimeUnit;
    }

    public int getRenewTime() {
        return renewTime;
    }

    public void setRenewTime(int renewTime) {
        this.renewTime = renewTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    @Override
    public String toString() {
        return "AutoRenewDetail{"
                + "uuid='" + uuid + '\''
                + ", shortId='" + shortId + '\''
                + ", serviceId='" + serviceId + '\''
                + ", resourceUuid='" + resourceUuid + '\''
                + ", name='" + name + '\''
                + ", status='" + status + '\''
                + ", orderStatus='" + orderStatus + '\''
                + ", region='" + region + '\''
                + ", serviceType='" + serviceType + '\''
                + ", expiredTime=" + expiredTime
                + ", renewTimeUnit='" + renewTimeUnit + '\''
                + ", renewTime=" + renewTime
                + '}';
    }
}
