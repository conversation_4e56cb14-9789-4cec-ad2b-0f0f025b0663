package com.baidu.bce.logic.rds.service.orderexecutor;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Flavor;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateFlavorRequest;
import com.baidu.bce.internalsdk.rds.model.instance.OrderInfo;
import com.baidu.bce.internalsdk.rds.model.instance.ResizeDiskRelatedInstance;
import com.baidu.bce.internalsdk.rds.model.instance.SingletonToNormalRequest;
import com.baidu.bce.logic.rds.dao.model.BatchOrderSyncPO;
import com.baidu.bce.logic.rds.dao.mybatis.BatchOrderSyncMapper;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.OrderEnvUtil;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;


/**
 * Created by hejianbin on 2014/12/25-11:56.
 */
@Service
public class RdsResizeOrderExecuteService extends AbstractRdsExecutorService {
    Logger log = LoggerFactory.getLogger(getClass());

    @Value("${rds.resizeorder.retrytime.minute:10080}")
    private int retryTimeInMinute;

    @Value("${sms.order.resize.success.tpl.id:33a3ad5c-092a-5b26-9675-d3e538c3b0e2}")
    private String smsResizeSuccessId;

    @Value("${sms.order.resize.failed.tpl.id:d0cd6c30-8b43-50a2-80b4-495e13c03cb1}")
    private String smsResizeFailedId;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private BatchOrderSyncMapper batchOrderSyncMapper;

    @Override
    public ExecutionResult execute(OrderClient client, ResourceClient resourceClient, Order order) {
        ExecutionResult executionResult = new ExecutionResult();
        // 1.先判断订单是否是批量创建的
        String batchInstances = getBatchFlagFromResieOrder(order);
        if (StringUtils.isNotEmpty(batchInstances)) {
            // 此时走到批量变配的逻辑
            log.debug("execute resize batch order...");
            return batchExecute(client, resourceClient, order);
        } else {
            // 维持原来执行变配的逻辑
            log.debug("execute resize single order...");
            if (order.getResourceIds().size() != 1) {
                log.error("unexpected resource size in order:" + order.getUuid());
            }
            String env = OrderEnvUtil.getEnvFromResizeOrder(order);
            log.debug("executeOrderResizeEnv:" + env);
            RDSClient rdsClient = StringUtils.isNotEmpty(env) ?
                    clientFactory.createRdsClientByAccountId(order.getAccountId(), env) :
                    clientFactory.createRdsClientByAccountId(order.getAccountId());
            RDSClient2 rdsClient2 = StringUtils.isNotEmpty(env) ?
                    clientFactory.createRdsClient2ByUserId(order.getAccountId(), env) :
                    clientFactory.createRdsClient2ByUserId(order.getAccountId());

            String instanceId = resourceClient.get(order.getResourceIds().get(0)).getName();
            InstanceGetResponse instanceGetResponse = rdsClient2.instanceDescribe(instanceId);

            InstanceExtension instance = new InstanceExtension(instanceGetResponse.getInstance());
            if (instance.getAzone() == null || instance.getAzone().equals("default")) {
                instance.setAzone("zoneA");
            }
            if (instance.getAzone().contains("+")) {
                instance.setAzone(instance.getAzone().replaceAll("\\+", ","));
            }

            log.debug(instance.toString());
            for (Order.Item item : order.getItems()) {
//            RDSClient rdsClient = clientFactory.createRdsClientByAccountId(order.getAccountId());
                InstanceUpdateFlavorRequest instanceUpdateFlavorRequest = new InstanceUpdateFlavorRequest();
                InstanceUpdateFlavorRequest.InstanceParameters instanceParameters =
                        new InstanceUpdateFlavorRequest.InstanceParameters();
                InstanceUpdateFlavorRequest.InstanceParameters instanceParametersExtra;
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    instanceParametersExtra = objectMapper.readValue(item.getExtra(), InstanceUpdateFlavorRequest.class)
                            .getInstanceParameters();
                    log.debug("instanceParametersExtra:" + instanceParametersExtra.toString());
                } catch (IOException ex) {
                    log.error("rds order error: deserialize configuration error " + ex.toString() + ex);
                    // TODO:暂时屏蔽，防止一旦出现序列化失败导致重复发短信
                    // sendMessage.send(false, order.getAccountId(), order.getUuid(), ServiceType.RDS);
                    setExecutionResult(executionResult, order, null, "[RDS Console] Parse flavor failed", ex, null);
                    return executionResult;
                }
                for (Flavor.FlavorItem flavorItem : item.getFlavor()) {
                    switch (flavorItem.getName()) {
                        case "memory":
                            if ("g".equals(flavorItem.getValue().substring(flavorItem.getValue().length() - 1))) {
                                instanceParameters.setAllocatedMemoryInMB(
                                        Integer.parseInt(flavorItem.getValue().substring(
                                                0, flavorItem.getValue().length() - 1)) * 1024);
                            } else {
                                instanceParameters.setAllocatedMemoryInMB(
                                        Integer.parseInt(flavorItem.getValue().substring(
                                                0, flavorItem.getValue().length() - 1)));
                            }
                            break;
                        case "disk":
                            instanceParameters.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                            break;
                        case "premium_ssd":
                            instanceParameters.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                            break;
                        case "ssd":
                            instanceParameters.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                            break;
                        case "enhanced_ssd_pl1":
                            instanceParameters.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                            break;
                        case "subServiceType":
                            instanceParameters.setEngine(flavorItem.getValue());
                            break;
                        case "nodeAmount":
                            instanceParameters.setNodeAmount(Integer.parseInt(flavorItem.getValue()));
                            break;
                        case "cpu" :
                            instanceParameters.setCpuCount(Integer.parseInt(flavorItem.getValue()));
                            break;
                        default:
                            break;
                    }
                }
                if (instanceParameters.getEngine() != null ) {
                    if (instanceParameters.getEngine().equalsIgnoreCase("default")) {
                        instanceParameters.setEngine("MySQL");
                    }
                    if (instanceParameters.getEngine().contains("_singleton")) {
                        int index = instanceParameters.getEngine().indexOf("_singleton");
                        instanceParameters.setEngine(instanceParameters.getEngine().substring(0, index));
                    }
                    if (instanceParameters.getEngine().contains("_finance")) {
                        instanceParameters.setEngine("MySQL");
                        instanceParameters.setIsEnhanced(true);
                    }
                    instanceParameters.setInstanceClass("rds-default");
                }
                if (instanceParametersExtra.getMasterAzone() != null){
                    instanceParameters.setMasterAzone(instanceParametersExtra.getMasterAzone());
                }

                if (instanceParametersExtra.getBackupAzone() != null){
                    instanceParameters.setBackupAzone(instanceParametersExtra.getBackupAzone());
                }

                if (instanceParametersExtra.getForceHotUpgrade() != null){
                    instanceParameters.setForceHotUpgrade(instanceParametersExtra.getForceHotUpgrade());
                }

                if (instanceParametersExtra.getSubnetId() != null && !instanceParametersExtra.getSubnetId().isEmpty()){
                    instanceParameters.setSubnetId(instanceParametersExtra.getSubnetId());
                }

                if (instanceParametersExtra.getDiskIoType() != null){
                    instanceParameters.setDiskIoType(instanceParametersExtra.getDiskIoType());
                }
                if (instanceParametersExtra.getDiskType() != null){
                    instanceParameters.setDiskType(instanceParametersExtra.getDiskType());
                }
                if (instanceParametersExtra.getCdsType() != null){
                    instanceParameters.setCdsType(instanceParametersExtra.getCdsType());
                }

                if (instanceParametersExtra.getEffectiveTime() != null){
                    instanceUpdateFlavorRequest.setEffectiveTime(instanceParametersExtra.getEffectiveTime());
                }

//                if ((instance.getEngineVersion().equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016)
//                        || instance.getEngine().equals(RDSConstant.RDS_ENGINE_PG))
//                        && (instance.getApplicationType() != null && "single".equals(instance.getApplicationType()))) {
//                    instanceUpdateFlavorRequest.setOrderId(order.getUuid());
//                    instanceParameters.setAzone(instance.getAzone());
//                    instanceParameters.setVpcId(instance.getVpcId());
//                    instanceParameters.setSubnetId(instance.getSubnetId());
//
//                }
                instanceParameters.setEdgeSubnetId(instanceParametersExtra.getEdgeSubnetId());
                instanceUpdateFlavorRequest.setInstanceParameters(instanceParameters);

                try {
                    // 此处需区分是正常的变配操作还是单机转双机操作
                    if (StringUtils.isNotEmpty(instance.getApplicationType())
                            && RDSConstant.APPLICATION_TYPE_SINGLE.equalsIgnoreCase(instance.getApplicationType())
                            && StringUtils.isNotEmpty(instanceParametersExtra.getCategory())
                            && RDSConstant.APPLICATION_TYPE_NORMAL.
                            equalsIgnoreCase(instanceParametersExtra.getCategory())) {
                        SingletonToNormalRequest request = new SingletonToNormalRequest();
                        // 由于实体类结构体不完全一致，故此处重新处理
                        request.setOrderId(order.getUuid());
                        request.setCpuCount(instanceUpdateFlavorRequest.getInstanceParameters().getCpuCount());
                        request.setAllocatedMemoryInMB(instanceUpdateFlavorRequest.getInstanceParameters().getAllocatedMemoryInMB());
                        request.setAllocatedStorageInGB(instanceUpdateFlavorRequest.getInstanceParameters().getAllocatedStorageInGB());
                        // 拼接 aZone 信息
                        StringBuilder aZone = new StringBuilder();
                        if (StringUtils.isNotEmpty(instanceUpdateFlavorRequest.getInstanceParameters()
                                .getMasterAzone())) {
                            aZone.append(instanceUpdateFlavorRequest.getInstanceParameters().getMasterAzone());
                        }
                        if (StringUtils.isNotEmpty(instanceUpdateFlavorRequest.getInstanceParameters()
                                .getBackupAzone())
                            && StringUtils.isNotEmpty(instanceUpdateFlavorRequest.getInstanceParameters()
                                .getMasterAzone())
                            && !instanceUpdateFlavorRequest.getInstanceParameters().getMasterAzone()
                                .equalsIgnoreCase(instanceUpdateFlavorRequest.getInstanceParameters()
                                        .getBackupAzone())) {
                            aZone.append("+").append(instanceUpdateFlavorRequest.getInstanceParameters()
                                    .getBackupAzone());
                        }
                        request.setAzone(String.valueOf(aZone));
                        request.setCdsType(instanceUpdateFlavorRequest.getInstanceParameters().getCdsType());
                        request.setDiskType(instanceUpdateFlavorRequest.getInstanceParameters().getDiskType());
                        request.setDiskIoType(instanceUpdateFlavorRequest.getInstanceParameters().getDiskIoType());
                        request.setSubnetId(instanceUpdateFlavorRequest.getInstanceParameters().getSubnetId());
                        request.setEffectiveTime(instanceUpdateFlavorRequest.getEffectiveTime());
                        rdsClient.instanceSingletonToNormal(instanceId, request);
                    } else {
                        // 原变配逻辑
                        rdsClient.instanceUpdateFlavor(instanceId, order.getUuid(), instanceUpdateFlavorRequest);
                    }
                } catch (BceInternalResponseException ex) {
                    // 如果此订单已向后端提交过，则无视此异常
                    if (!"OrderAlreadyExists".equals(ex.getCode())) {
                        setExecutionResult(executionResult, order, null,
                                "[RDS Backend] Execute READY_FOR_CREATE order failed", ex, null);
                    }
                    return executionResult;
                }

                // 更新订单状态
                UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
                OrderStatus orderStatus = OrderStatus.CREATING;
                updateOrderRequest.setStatus(orderStatus);
                updateOrderRequest.setServiceType(order.getServiceType());
                client.update(order.getUuid(), updateOrderRequest);
                executionResult.setExecutionStatus(ExecutionStatus.CREATING);
            }

        }
        return executionResult;
    }


    @Override
    public ExecutionResult check(OrderClient client, ResourceClient resourceClient, Order order) {
        log.debug("checkStatusOrder");        // 请求后端服务生成instance
        ExecutionResult executionResult = new ExecutionResult(ExecutionStatus.CREATING);
        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setServiceType(order.getServiceType());

        String env = OrderEnvUtil.getEnvFromResizeOrder(order);
        log.debug("checkOrderResizeEnv:" + env);
        RDSClient rdsClient = StringUtils.isNotEmpty(env) ?
                clientFactory.createRdsClientByAccountId(order.getAccountId(), env) :
                clientFactory.createRdsClientByAccountId(order.getAccountId());
        RDSClient2 rdsClient2 = StringUtils.isNotEmpty(env) ?
                clientFactory.createRdsClient2ByUserId(order.getAccountId(), env) :
                clientFactory.createRdsClient2ByUserId(order.getAccountId());

        boolean succeed = true;
        if (isExceedRetryTime(order)) {
            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
            updateOrderStatus(client, order, updateOrderRequest, false);
            setExecutionResult(executionResult, order, null, "[RDS Backend] " + RDSConstant.ORDER_TIME_OUT,
                    null, false);
            return executionResult;
        }

        outer:
        for (String resourceId : order.getResourceIds()) {
            String instanceId = resourceClient.get(resourceId).getName();
            InstanceGetResponse response;
            try {
                response = rdsClient2.instanceDescribe(instanceId);
                String status = response.getInstance().getInstanceStatus();
                switch (status) {
                    case "modifying":
                        return executionResult;
                    case "available":
                        String orderStatus;
                        OrderInfo orderInfo = rdsClient.getOrderInfo(order.getUuid());
                        if (orderInfo.getOrder() != null) {
                            orderStatus = orderInfo.getOrder().getStatus();
                        } else {
                            orderStatus = orderInfo.getStatus();
                        }
                        switch (orderStatus) {
                            case "success":
                                succeed = true;
                                break outer;
                            case "failed":
                                succeed = false;
                                break outer;
                            default:
                                return executionResult;
                        }
                    default:
                        log.warn("unexpected rds instance status:{} ", status);
                        return executionResult;
                }
            } catch (BceInternalResponseException ex) {
                setExecutionResult(executionResult, order, null,
                        "[RDS Backend] After called resize interface, query rds detail is failed", ex, null);
                if (ex.getCode().equals("DbinstanceNotFound") || ex.getHttpStatus() == 404) {
                    // order服务认定创建失败。这种情况应该是由于数据不一致导致的。需人工介入，事实上order服务会去删resource资源，最终结果是一致的
                    // 此时认定创建订单已失败
                    log.warn("rds instance:{} found in resources but not exist in background service", instanceId);
                    succeed = false;
                    break;
                }
                // 其他错误，则不认定订单创建失败，下次再次查询
                log.warn("rds query instance:{} state error", instanceId);
                return executionResult;
            }
        }

        if (succeed) {
            updateOrderRequest.setStatus(OrderStatus.CREATED);
            executionResult.setExecutionStatus(ExecutionStatus.SUCCESS);
        } else {
            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
            executionResult.setExecutionStatus(ExecutionStatus.FAILURE);
        }

        updateOrderStatus(client, order, updateOrderRequest, succeed);
        executionResult.setMessageCenterModels(getMessage(order, null, succeed));
        return executionResult;
    }


    /**
     * 此方法用于处理批量订单变配的执行逻辑
     * @param client
     * @param resourceClient
     * @param order
     * @return ExecutionResult
     */
    private ExecutionResult batchExecute(OrderClient client, ResourceClient resourceClient, Order order) {
        ExecutionResult executionResult = new ExecutionResult();
        // 开始处理批量变配的订单
        String batchInstanceIds = getBatchFlagFromResieOrder(order);
        int countByExtra = 1;
        if (StringUtils.isNotEmpty(batchInstanceIds)) {
            countByExtra = batchInstanceIds.split(",").length;
        }

        if (order.getResourceIds().size() != 1) {
            log.error("unexpected resource size in order:" + order.getUuid());
        }
        String env = OrderEnvUtil.getEnvFromResizeOrder(order);
        log.debug("executeOrderResizeEnv:" + env);
        RDSClient rdsClient = StringUtils.isNotEmpty(env) ?
                clientFactory.createRdsClientByAccountId(order.getAccountId(), env) :
                clientFactory.createRdsClientByAccountId(order.getAccountId());
        RDSClient2 rdsClient2 = StringUtils.isNotEmpty(env) ?
                clientFactory.createRdsClient2ByUserId(order.getAccountId(), env) :
                clientFactory.createRdsClient2ByUserId(order.getAccountId());

        // 订单信息ORM
        BatchOrderSyncPO batchOrderSyncPO = new BatchOrderSyncPO();
        // 根据资源反查实例 ID
        String instanceId = resourceClient.get(order.getResourceIds().get(0)).getName();
        batchOrderSyncPO.setInstanceId(instanceId);
        // 类型强转失败，借助构造函数
        Date createTime = order.getCreateTime();
        // UTC时间转为正常的时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
        Timestamp timestamp = new Timestamp(createTime.getTime());
        String formatTime = sdf.format(timestamp);
        batchOrderSyncPO.setCreateTime(Timestamp.valueOf(formatTime));
        batchOrderSyncPO.setExtraOrders(batchInstanceIds);
        batchOrderSyncPO.setUserId(order.getUserId());
        batchOrderSyncPO.setOrderUuid(order.getUuid());
        batchOrderSyncPO.setOrderStatus(order.getStatus().name());
        batchOrderSyncPO.setOrderType(order.getProductType());

        // 将当前批量变配的订单都取出来
        String ordersByInstanceIds =
                batchOrderSyncMapper.selectBatOrdersByInstanceIds(batchInstanceIds, order.getUuid());
        List<String> orderIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(ordersByInstanceIds)) {
            String[] split = ordersByInstanceIds.split(",");
            for (String s : split) {
                orderIds.add(s);
            }
        }

        // 更新订单流程
         batchOrderSyncMapper.updateOrderStatus(batchOrderSyncPO);
        int orderCount = 0;

        // 并发情况下，此时需立即查询库中指定订单标识的订单数量是否已满足
        orderCount = batchOrderSyncMapper.selectUnExecuteOrderCountByExtra(batchInstanceIds, ordersByInstanceIds);



        log.debug("countByExtra from order : {}, orderCount from db {}.", countByExtra, orderCount);
        // 此时若库中查出的指定标识信息订单数目 等于 传进来的订单标识项个数，则可进行批量订单的创建

        if (orderCount == countByExtra) {
                // 此时可以将可以进行批量创建的订单、并未执行创建逻辑的全部取出
            String batOrdersByOrderId = batchOrderSyncMapper.selectBatOrdersByOrderId(order.getUuid());
            List<BatchOrderSyncPO> orderSyncPOList =
                    batchOrderSyncMapper.selectSyncOrder(batchInstanceIds, batOrdersByOrderId);
                // 设计一个有序数组用来存储 能够区分 主、只读实例
                List<BatchOrderSyncPO> reOrderSyncList = new ArrayList<>(orderSyncPOList.size());
                // 设计一个数组将实例详情也存下 节省接口调用 此时存储也是顺序的
                List<InstanceGetResponse> instanceGetResponses = new ArrayList<>();
                // 此时还需注意一点，需要将主实例的订单和只读实例的订单分开。默认 batchInstanceIds中第一个实例即为主实例
                // 先将主实例放进 reorder 数组
                reOrderListOfResize(orderSyncPOList, reOrderSyncList, instanceGetResponses, rdsClient2);


                log.debug("reOrderSyncList.size() {}, instanceGetResponses.size() {}.", reOrderSyncList.size(),
                        instanceGetResponses.size());
                // 需组装调用管控接口的请求体，此处默认应该是从主实例开始处理
                InstanceUpdateFlavorRequest instanceUpdateFlavorRequest = new InstanceUpdateFlavorRequest();
                InstanceUpdateFlavorRequest.InstanceParameters instanceParameters =
                        new InstanceUpdateFlavorRequest.InstanceParameters();
                List<ResizeDiskRelatedInstance> resizeDiskRelatedInstance = new ArrayList<>();

                // 1. 此处先将主实例的参数组合好
                InstanceExtension instance = new InstanceExtension(instanceGetResponses.get(0).getInstance());
                if (instance.getAzone() == null || instance.getAzone().equals("default")) {
                    instance.setAzone("zoneA");
                }
                if (instance.getAzone().contains("+")) {
                    instance.setAzone(instance.getAzone().replaceAll("\\+", ","));
                }
                log.debug(instance.toString());
                // 此时需要通过订单 ID 查询订单
                BatchOrderSyncPO orderPO = reOrderSyncList.get(0);
                Order orderDetail = client.get(orderPO.getOrderUuid());
                // 此处的循环本质就是一次，为了向后兼容
                for (Order.Item item : orderDetail.getItems()) {
                    InstanceUpdateFlavorRequest.InstanceParameters instanceParametersExtra;
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        instanceParametersExtra = objectMapper.readValue(item.getExtra(), InstanceUpdateFlavorRequest.class)
                                .getInstanceParameters();
                        log.debug("instanceParametersExtra: {}, size: {}" + instanceParametersExtra.toString(), 0);
                    } catch (IOException ex) {
                        log.error("rds order error: deserialize configuration error " + ex.toString() + ex);
                        // TODO:暂时屏蔽，防止一旦出现序列化失败导致重复发短信
                        // sendMessage.send(false, order.getAccountId(), order.getUuid(), ServiceType.RDS);
                        setExecutionResult(executionResult, orderDetail,
                                null, "[RDS Console] Parse flavor failed", ex, null);
                        return executionResult;
                    }
                    for (Flavor.FlavorItem flavorItem : item.getFlavor()) {
                        switch (flavorItem.getName()) {
                            case "memory":
                                if ("g".equals(flavorItem.getValue().substring(flavorItem.getValue().length() - 1))) {
                                    instanceParameters.setAllocatedMemoryInMB(
                                            Integer.parseInt(flavorItem.getValue().substring(
                                                    0, flavorItem.getValue().length() - 1)) * 1024);
                                } else {
                                    instanceParameters.setAllocatedMemoryInMB(
                                            Integer.parseInt(flavorItem.getValue().substring(
                                                    0, flavorItem.getValue().length() - 1)));
                                }
                                break;
                            case "disk":
                                instanceParameters.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                                break;
                            case "premium_ssd":
                                instanceParameters.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                                break;
                            case "ssd":
                                instanceParameters.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                                break;
                            case "enhanced_ssd_pl1":
                                instanceParameters.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                                break;
                            case "subServiceType":
                                instanceParameters.setEngine(flavorItem.getValue());
                                break;
                            case "nodeAmount":
                                instanceParameters.setNodeAmount(Integer.parseInt(flavorItem.getValue()));
                                break;
                            case "cpu" :
                                instanceParameters.setCpuCount(Integer.parseInt(flavorItem.getValue()));
                                break;
                            default:
                                break;
                        }
                    }
                    if (instanceParameters.getEngine() != null ) {
                        if (instanceParameters.getEngine().equalsIgnoreCase("default")) {
                            instanceParameters.setEngine("MySQL");
                        }
                        if (instanceParameters.getEngine().contains("_singleton")) {
                            int index = instanceParameters.getEngine().indexOf("_singleton");
                            instanceParameters.setEngine(instanceParameters.getEngine().substring(0, index));
                        }
                        if (instanceParameters.getEngine().contains("_finance")) {
                            instanceParameters.setEngine("MySQL");
                            instanceParameters.setIsEnhanced(true);
                        }
                        instanceParameters.setInstanceClass("rds-default");
                    }
                    if (instanceParametersExtra.getMasterAzone() != null){
                        instanceParameters.setMasterAzone(instanceParametersExtra.getMasterAzone());
                    }

                    if (instanceParametersExtra.getBackupAzone() != null){
                        instanceParameters.setBackupAzone(instanceParametersExtra.getBackupAzone());
                    }

                    if (instanceParametersExtra.getForceHotUpgrade() != null){
                        instanceParameters.setForceHotUpgrade(instanceParametersExtra.getForceHotUpgrade());
                    }

                    if (instanceParametersExtra.getSubnetId() != null && !instanceParametersExtra.getSubnetId().isEmpty()){
                        instanceParameters.setSubnetId(instanceParametersExtra.getSubnetId());
                    }

                    if (instanceParametersExtra.getDiskIoType() != null){
                        instanceParameters.setDiskIoType(instanceParametersExtra.getDiskIoType());
                    }
                    if (instanceParametersExtra.getDiskType() != null){
                        instanceParameters.setDiskType(instanceParametersExtra.getDiskType());
                    }
                    if (instanceParametersExtra.getCdsType() != null){
                        instanceParameters.setCdsType(instanceParametersExtra.getCdsType());
                    }

                    if (instanceParametersExtra.getEffectiveTime() != null){
                        instanceUpdateFlavorRequest.setEffectiveTime(instanceParametersExtra.getEffectiveTime());
                    }

                    if ((instance.getEngineVersion().equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016)
                            || instance.getEngine().equals(RDSConstant.RDS_ENGINE_PG))
                            && (instance.getApplicationType() != null && "single".equals(instance.getApplicationType()))) {
                        instanceUpdateFlavorRequest.setOrderId(order.getUuid());
                        instanceParameters.setAzone(instance.getAzone());
                        instanceParameters.setVpcId(instance.getVpcId());
                        instanceParameters.setSubnetId(instance.getSubnetId());

                    }
                    instanceParameters.setEdgeSubnetId(instanceParametersExtra.getEdgeSubnetId());
                    instanceUpdateFlavorRequest.setInstanceParameters(instanceParameters);
                }

                // 2.再将只读实例的参数组合好
                for (int i = 1; i < reOrderSyncList.size(); i++) {
                    ResizeDiskRelatedInstance resizeInstance = new ResizeDiskRelatedInstance();
                    InstanceExtension instance1 = new InstanceExtension(instanceGetResponses.get(i).getInstance());
                    if (instance1.getAzone() == null || instance1.getAzone().equals("default")) {
                        instance1.setAzone("zoneA");
                    }
                    if (instance1.getAzone().contains("+")) {
                        instance1.setAzone(instance1.getAzone().replaceAll("\\+", ","));
                    }
                    log.debug(instance1.toString());
                    // 此时需要通过订单 ID 查询订单
                    BatchOrderSyncPO orderPO1 = reOrderSyncList.get(i);
                    Order orderDetails = client.get(orderPO1.getOrderUuid());
                    // 为请求参数设置订单 ID
                    resizeInstance.setOrderId(orderPO1.getOrderUuid());
                    // 此处的循环本质就是一次，为了向后兼容
                    for (Order.Item item : orderDetails.getItems()) {
//            RDSClient rdsClient = clientFactory.createRdsClientByAccountId(order.getAccountId());

                        InstanceUpdateFlavorRequest.InstanceParameters instanceParametersExtra;
                        ObjectMapper objectMapper = new ObjectMapper();
                        try {
                            instanceParametersExtra = objectMapper.readValue(item.getExtra(), InstanceUpdateFlavorRequest.class)
                                    .getInstanceParameters();
                            log.debug("instanceParametersExtra: {}, size: {}" + instanceParametersExtra.toString(), 0);
                        } catch (IOException ex) {
                            log.error("rds order error: deserialize configuration error " + ex.toString() + ex);
                            // TODO:暂时屏蔽，防止一旦出现序列化失败导致重复发短信
                            // sendMessage.send(false, order.getAccountId(), order.getUuid(), ServiceType.RDS);
                            setExecutionResult(executionResult, orderDetails, null, "[RDS Console] Parse flavor failed", ex, null);
                            return executionResult;
                        }
                        for (Flavor.FlavorItem flavorItem : item.getFlavor()) {
                            switch (flavorItem.getName()) {
                                case "memory":
                                    if ("g".equals(flavorItem.getValue().substring(flavorItem.getValue().length() - 1))) {
                                        resizeInstance.setAllocatedMemoryInMB(
                                                Integer.parseInt(flavorItem.getValue().substring(
                                                        0, flavorItem.getValue().length() - 1)) * 1024);
                                    } else {
                                        resizeInstance.setAllocatedMemoryInMB(
                                                Integer.parseInt(flavorItem.getValue().substring(
                                                        0, flavorItem.getValue().length() - 1)));
                                    }
                                    break;
                                case "disk":
                                    resizeInstance.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                                    break;
                                case "premium_ssd":
                                    resizeInstance.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                                    break;
                                case "ssd":
                                    resizeInstance.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                                    break;
                                case "enhanced_ssd_pl1":
                                    resizeInstance.setAllocatedStorageInGB(flavorItem.getScale().intValue());
                                    break;
                                case "cpu" :
                                    resizeInstance.setCpuCount(Integer.parseInt(flavorItem.getValue()));
                                    break;
                                default:
                                    break;
                            }
                        }

                        if (instanceParametersExtra.getDiskIoType() != null){
                            resizeInstance.setDiskIoType(instanceParametersExtra.getDiskIoType());
                        }
                        if (instanceParametersExtra.getDiskType() != null){
                            resizeInstance.setDiskType(instanceParametersExtra.getDiskType());
                        }
                        if (instanceParametersExtra.getCdsType() != null){
                            resizeInstance.setCdsType(instanceParametersExtra.getCdsType());
                        }
                    }

                    resizeInstance.setInstanceId(orderPO1.getInstanceId());

                    // 每个只读实例都需要填充下
                    resizeDiskRelatedInstance.add(resizeInstance);
                }
                // 填充只读实例至请求体中
                instanceParameters.setResizeDiskRelatedInstanceList(resizeDiskRelatedInstance);
                instanceUpdateFlavorRequest.setInstanceParameters(instanceParameters);
                log.debug("batch resize request: {}", instanceUpdateFlavorRequest.toString());

                // 发生批量变配的请求
                try {
                    // 此时拿主实例的订单 ID 作为 clientToken
                    // 此处需要新增校验，保证当前变配发起的请求是主实例
                    String masterInstanceId = checkMasterInstanceId(instanceId, instanceGetResponses);
                    rdsClient.instanceUpdateFlavor(masterInstanceId, reOrderSyncList.get(0).getOrderUuid(),
                            instanceUpdateFlavorRequest);
                } catch (BceInternalResponseException ex) {
                    // 如果此订单已向后端提交过，则无视此异常
                    if (!"OrderAlreadyExists".equals(ex.getCode())) {
                        setExecutionResult(executionResult, orderDetail, null,
                                "[RDS Backend] Execute READY_FOR_CREATE order failed", ex, null);
                    }
                    return executionResult;
                }

                // 批量更新订单状态
                for (int i = 0;i < reOrderSyncList.size(); i++) {
                    BatchOrderSyncPO syncPO = reOrderSyncList.get(i);
                    Order order1 = client.get(syncPO.getOrderUuid());
                    UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
                    OrderStatus orderStatus = OrderStatus.CREATING;
                    syncPO.setOrderStatus(OrderStatus.CREATING.name());
                    updateOrderRequest.setStatus(orderStatus);
                    updateOrderRequest.setServiceType(order1.getServiceType());
                    client.update(order1.getUuid(), updateOrderRequest);
                    executionResult.setExecutionStatus(ExecutionStatus.CREATING);
                    // 更新执行中的订单
                    batchOrderSyncMapper.updateCreatingStatus(syncPO);

                }
            }
        return executionResult;
    }

    /**
     * 此接口用于保证当前发起批量变配的一定是主实例，不依赖于资源侧
     * @param instanceId
     * @param instanceGetResponses
     * @return
     */
    private String checkMasterInstanceId(String instanceId, List<InstanceGetResponse> instanceGetResponses) {
        String masterInstanceId = instanceId;

        for (InstanceGetResponse instanceGetResponse : instanceGetResponses) {
            // 如果当前回调的正好是主实例订单，则返回
            if (instanceGetResponse.getInstance().getInstanceId().equalsIgnoreCase(instanceId)
                && StringUtils.isEmpty(instanceGetResponse.getInstance().getSourceInstanceId())) {
                 return instanceId;
            }
            if (StringUtils.isEmpty(instanceGetResponse.getInstance().getSourceInstanceId())) {
                masterInstanceId = instanceGetResponse.getInstance().getInstanceId();
            }
        }
        return masterInstanceId;
    }

    /**
     * 重组批量处理的列表，保证 主、只读 ...的顺序
     * @param orderSyncPOList
     * @param reOrderSyncList
     * @param instanceGetResponses
     * @Param rdsClient2
     */
    private void reOrderListOfResize(List<BatchOrderSyncPO> orderSyncPOList, List<BatchOrderSyncPO> reOrderSyncList,
                                     List<InstanceGetResponse> instanceGetResponses, RDSClient2 rdsClient2) {
        for (BatchOrderSyncPO orderSyncPO : orderSyncPOList) {
            InstanceGetResponse instanceGetResponse = rdsClient2.instanceDescribe(orderSyncPO.getInstanceId());
            if (StringUtils.isEmpty(instanceGetResponse.getInstance().getSourceInstanceId())) {
                // 确定为主实例
                instanceGetResponses.add(instanceGetResponse);
                reOrderSyncList.add(orderSyncPO);
                break;
            }
        }
        // 再将剩余的只读实例放进 reorder 数组
        for (BatchOrderSyncPO orderSyncPO : orderSyncPOList) {
            InstanceGetResponse instanceGetResponse = rdsClient2.instanceDescribe(orderSyncPO.getInstanceId());
            if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getSourceInstanceId())) {
                // 确定为只读实例
                instanceGetResponses.add(instanceGetResponse);
                reOrderSyncList.add(orderSyncPO);
            }
        }
    }


    /**
     * 此方法用于取出订单项中的批量标识
     * @param order
     * @return
     */
    private String getBatchFlagFromResieOrder(Order order) {
        String batchInstanceIds = "";
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            batchInstanceIds = objectMapper.readValue(order.getItems().get(0).getExtra(),
                    InstanceUpdateFlavorRequest.class).getBatchInstances();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return batchInstanceIds;
    }

    private void updateOrderStatus(OrderClient client, Order order, UpdateOrderRequest updateOrderRequest,
                                   boolean succeed) {
        client.update(order.getUuid(), updateOrderRequest);
    }

    private boolean isExceedRetryTime(Order order) {
        boolean result = false;

        Calendar retryEndTime = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
//        Date purchaseTime = ProductPayType.PRE_PAY.toString().equalsIgnoreCase(order.getProductType())
//                ? order.getPurchaseTime() : order.getCreateTime();
//        retryEndTime.setTime(purchaseTime);
        Date updateTime = order.getUpdateTime();
        retryEndTime.setTime(updateTime);
        retryEndTime.add(Calendar.MINUTE, retryTimeInMinute);

        Calendar now = Calendar.getInstance(TimeZone.getTimeZone("UTC"));

        if (retryEndTime.before(now)) {
            result = true;
        }

        log.debug("[rds] resize now is [{}], retrytime is [{}], result is {}.",
                now.getTime(), retryEndTime.getTime(), result);

        return result;
    }

    @Override
    protected String getMessageTpl(boolean succeed) {
        return succeed ? smsResizeSuccessId : smsResizeFailedId;
    }
}
