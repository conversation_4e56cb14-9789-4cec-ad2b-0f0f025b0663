package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.model.instance.ExtensionHistoryResp;
import com.baidu.bce.internalsdk.rds.model.instance.ExtensionListRequest;
import com.baidu.bce.internalsdk.rds.model.instance.ExtenstionListResponse;
import com.baidu.bce.internalsdk.rds.model.instance.OperateExtensionRequest;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ExtensionService {
    @Autowired
    private LogicRdsClientFactory clientFactory;

    public ExtenstionListResponse list(ExtensionListRequest request) {
        // 若 dbName 为空，则默认查询所有数据库的
        // 若 paegeNo 或 pageSize 为空，则默认查询第一页，每页100条数据
        if (request.getPageNo() == null || "".equals(request.getPageNo()) || "0".equals(request.getPageNo())) {
            request.setPageNo("1");
        }
        if (request.getPageSize() == null || "".equals(request.getPageSize())) {
            request.setPageSize("100");
        }
        return clientFactory.createRdsClient3().listExtensions(request);
    }

    public void operateExtension(OperateExtensionRequest request) {
        clientFactory.createRdsClient3().operateExtension(request);
    }

    public ExtensionHistoryResp historyList(String instanceId) {
        return clientFactory.createRdsClient3().historyList(instanceId);
    }
}
