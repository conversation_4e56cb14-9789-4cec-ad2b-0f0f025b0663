package com.baidu.bce.logic.rds.service.datasync.service;

import com.baidu.bce.logic.rds.dao.mybatis.ActionTimeMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;

/**
 * 用来记录 每次同步数据时需要的时间戳
 */
@Service
public class ActionTimeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActionTimeService.class);

    @Autowired
    ActionTimeMapper actionTimeMapper;

    public void addActionTime(Timestamp actionTime, String resourceType) {
        actionTimeMapper.addActionTime(actionTime, resourceType);
    }

    /**
     * 获取 同步记录表中的
     *
     * @return
     */
    public Timestamp getLastActionTime(String resourceType) {
        return actionTimeMapper.queryActionTime(resourceType);
    }

    /**
     * 更新指定资源：上次同步的最后一条记录的时间
     *
     * @param lastActionTime
     *
     * @return
     */
    public boolean updateLastActionTime(Timestamp lastActionTime, String resourceType) {
        actionTimeMapper.updateActionTimeByResourceType(lastActionTime, resourceType);
        return true;
    }
}
