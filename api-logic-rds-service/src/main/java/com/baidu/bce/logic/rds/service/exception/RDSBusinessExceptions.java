package com.baidu.bce.logic.rds.service.exception;

import org.springframework.http.HttpStatus;

/**
 * api-logic-gaiadb 模块业务逻辑异常。
 * 异常 code、message 涉及可参考：
 *  1. 公有云 OpenAPI 设计：http://gollum.baidu.com/BceApiSpec#9.7-%E9%94%99%E8%AF%AF
 *  2. RDS OpenAPI 错误返回结果：https://cloud.baidu.com/doc/RDS/s/Ejwvz0uoq
 *  3. BCC OpenAPI 错误返回：https://cloud.baidu.com/doc/BCC/s/Ojwvyo6nc
 */
public class RDSBusinessExceptions {

    /**
     * 通用异常
     */
    public static final class InternalServerErrorException extends RDSBusinessException {
        public InternalServerErrorException() {
            super("Internal service occurs error.", HttpStatus.INTERNAL_SERVER_ERROR.value(), "ServiceInternalError");
        }
    }

    /**
     * api-logic-rds 包装的 Billing 相关服务异常
     */
    public static final class ResourceNotExistException extends RDSBusinessException {
        public ResourceNotExistException() {
            super("Resource do not exist.", HttpStatus.NOT_FOUND.value(), "ResourceNotExist");
        }
    }

    public static final class InstanceNotFoundException extends RDSBusinessException {
        public InstanceNotFoundException() {
            super("Instance not found", HttpStatus.NOT_FOUND.value(), "InstanceNotFound");
        }
    }

    public static final class InstanceShrinkageEngineException extends RDSBusinessException {
        public InstanceShrinkageEngineException() {
            super("Shrinkage failed", HttpStatus.BAD_REQUEST.value(), "ShrinkageFailed");
        }
    }

    public static final class ShrinkageComputeAndStorageException extends RDSBusinessException {
        public ShrinkageComputeAndStorageException() {
            super("Shrinkage failed", HttpStatus.BAD_REQUEST.value(), "ShrinkageComputeAndStorageException");
        }
    }



    public static final class InstanceShrinkageSizeException extends RDSBusinessException {
        public InstanceShrinkageSizeException() {
            super("Shrinkage failed", HttpStatus.BAD_REQUEST.value(), "ShrinkageFailed");
        }
    }

    public static final class DiskSizeInvalidException extends RDSBusinessException {
        public DiskSizeInvalidException(String instanceId) {
            super(String.format("The instance ('%s') disk size is invalid.", instanceId),
                    HttpStatus.BAD_REQUEST.value(), "HasUnderwayOrder", new Object[]{instanceId});
        }
    }


    public static final class InstanceShrinkageUpperReplicaException extends RDSBusinessException {
        public InstanceShrinkageUpperReplicaException() {
            super("Shrinkage failed", HttpStatus.BAD_REQUEST.value(), "ShrinkageFailed");
        }
    }

    public static final class BackupParameterException extends RDSBusinessException {
        public BackupParameterException() {
            super("logical backup failed", HttpStatus.BAD_REQUEST.value(), "LogicalBackupFailed");
        }
    }

    public static final class GeneralInstanceNotSupportException extends RDSBusinessException {
        public GeneralInstanceNotSupportException() {
            super("Not support", HttpStatus.BAD_REQUEST.value(), "NotSupport");
        }
    }

    public static final class UnsupportRegionException extends RDSBusinessException {
        public UnsupportRegionException(String region) {
            super(String.format("The current region ('%s') is not support this operation.", region),
                    HttpStatus.BAD_REQUEST.value(), "HasUnderwayOrder", new Object[]{region});
        }
    }


    public static final class InstanceShrinkageLowerMasterException extends RDSBusinessException {
        public InstanceShrinkageLowerMasterException() {
            super("Shrinkage failed", HttpStatus.BAD_REQUEST.value(), "ShrinkageFailed");
        }
    }

    public static final class InstanceTypeNotSupportException extends RDSBusinessException {
        public InstanceTypeNotSupportException(String instanceType) {
            super(String.format("The instance type ('%s') is not support this operation", instanceType),
                    HttpStatus.BAD_REQUEST.value(), "InstanceTypeValidError", new Object[]{instanceType});
        }
    }

    public static final class MissingRequiredKeyException extends RDSBusinessException {
        public MissingRequiredKeyException(String param) {
            super(String.format("The required properties are missing in the entity class parameters ('%s').", param),
                    HttpStatus.BAD_REQUEST.value(), "MissingRequiredKey", new Object[]{param});
        }
    }

    public static final class InstanceNotBelongRecycleException extends RDSBusinessException {
        public InstanceNotBelongRecycleException(String instanceId) {
            super(String.format("The current instance ('%s') does not belong to recycle list.", instanceId),
                    HttpStatus.BAD_REQUEST.value(), "NotBelongToRecycleError", new Object[]{instanceId});
        }
    }

    public static final class ResizeMasterReplicaDiskException extends RDSBusinessException {
        public ResizeMasterReplicaDiskException() {
            super("Resize disk failed", HttpStatus.BAD_REQUEST.value(), "ResizeDiskFailed");
        }
    }

    public static final class NotSupportDiskTypeException extends RDSBusinessException {
        public NotSupportDiskTypeException(String diskIoType) {
            super(String.format("Single to normal diskIoType（'%s'）disabled.", diskIoType),
                    HttpStatus.BAD_REQUEST.value(), "DiskIoTypeDisabled",new Object[]{diskIoType});
        }
    }

    public static final class ParameterTopoErrorException extends RDSBusinessException {
        public ParameterTopoErrorException(String instanceId) {
            super(String.format("The instance ('%s') is not allowed this cluster.", instanceId),
                    HttpStatus.BAD_REQUEST.value(), "ParameterTopoError", new Object[]{instanceId});
        }
    }


    public static final class InstanceStatusUnsatisfiedException extends RDSBusinessException {
        public InstanceStatusUnsatisfiedException(String instanceId, String masterInstanceId, String instanceStatus) {
            super(String.format("The proxy instance ('%s') is not allowed to resize that the master instance ('%s')" +
                            " non-available status('%s')."
                    , instanceId, masterInstanceId, instanceStatus),
                    HttpStatus.BAD_REQUEST.value(), "InstanceStatusUnsatisfied"
                    , new Object[]{instanceId, masterInstanceId, instanceStatus});
        }
    }

    public static final class ResizeUnsupportInGroupException extends RDSBusinessException {
        public ResizeUnsupportInGroupException(String instanceId, String instanceStatus) {
            super(String.format("The instance in a hot activity group are not allowed to resize " +
                            "if there are instance ('%s') in the hot activity group that are non-available status('%s')."
                    , instanceId, instanceStatus),
                    HttpStatus.BAD_REQUEST.value(), "InstanceStatusUnsatisfied"
                    , new Object[]{instanceId, instanceStatus});
        }
    }

    public static final class BccInstanceStatusException extends RDSBusinessException {
        public BccInstanceStatusException(String instanceId, String instanceStatus) {
            super(String.format("The bcc instance ('%s') status is ('%s'), it is unsatisfied."
                    , instanceId, instanceStatus),
                    HttpStatus.BAD_REQUEST.value(), "InstanceStatusUnsatisfied"
                    , new Object[]{instanceId, instanceStatus});
        }
    }

    public static final class MultiMasterResizeException extends RDSBusinessException {
        public MultiMasterResizeException() {
            super("Multi master resize failed", HttpStatus.BAD_REQUEST.value(), "MultiMasterResize");
        }
    }

    public static final class ResizeMasterMemoryException extends RDSBusinessException {
        public ResizeMasterMemoryException() {
            super("Resize master memory failed", HttpStatus.BAD_REQUEST.value(), "ResizeMasterMemoryFailed");
        }
    }

    public static final class ResizeReplicaMemoryException extends RDSBusinessException {
        public ResizeReplicaMemoryException() {
            super("Resize replica memory failed", HttpStatus.BAD_REQUEST.value(), "ResizeReplicaMemoryFailed");
        }
    }

    public static final class MissingParameterException extends RDSBusinessException {
        public MissingParameterException() {
            super("Missing parameter", HttpStatus.BAD_REQUEST.value(), "MissingParameter");
        }
    }

    public static final class InstanceNotSatisfiableException extends RDSBusinessException {
        public InstanceNotSatisfiableException() {
            super("Instance not satisfiable", HttpStatus.BAD_REQUEST.value(), "InstanceNotSatisfiable");
        }
    }

    public static final class InvalidParameterException extends RDSBusinessException {
        public InvalidParameterException() {
            super("Invalid Parameter", HttpStatus.BAD_REQUEST.value(), "InvalidParameter");
        }
    }

    public static final class InstanceStatusErrorException extends RDSBusinessException {
        public InstanceStatusErrorException() {
            super("Instance Status error", HttpStatus.BAD_REQUEST.value(), "InstanceStatusError");
        }
    }

    public static final class InternalDBErrorException extends RDSBusinessException {
        public InternalDBErrorException() {
            super("Instance DB error", HttpStatus.SERVICE_UNAVAILABLE.value(), "InstanceDBError");
        }
    }


    /**
     * 变配操作相关异常
     */
    public static final class InvalidAction extends RDSBusinessException {
        public InvalidAction() {
            super("Invalid Action.", HttpStatus.FORBIDDEN.value(), "InvalidAction");
        }
    }

    public static class InvalidInstanceStatus extends RDSBusinessException {
        public InvalidInstanceStatus() {
            super("instance status is invalid", HttpStatus.FORBIDDEN.value(), "InvalidInstanceStatus");
        }
    }

    public static class MinorVersionMissException extends RDSBusinessException {
        public MinorVersionMissException() {
            super("miss minor version", HttpStatus.INTERNAL_SERVER_ERROR.value(), "MissMinorVersion");
        }
    }

    public static class RdsGroupMinorVersionCheckException extends RDSBusinessException {
        public RdsGroupMinorVersionCheckException() {
            super("group minor version check failed", HttpStatus.BAD_REQUEST.value(), "MinorVersionCehckFailed");
        }
    }

    public static class RdsPermissionDenyException extends RDSBusinessException {
        public RdsPermissionDenyException() {
            super("permission denied", HttpStatus.FORBIDDEN.value(), "PermissionDeny");
        }
    }

    public static class HasChangeBillingException extends RDSBusinessException {
        public HasChangeBillingException() {
            super("This instance is undergoing billing changes. ",
                    HttpStatus.FORBIDDEN.value(), "HasChangeBillingException");
        }
    }

    public static class ParamValidationException extends RDSBusinessException {
        public ParamValidationException() {
            super("Parameter validate error.", HttpStatus.BAD_REQUEST.value(), "ParamValidationException");
        }
    }

    public static class PrecheckResourceException extends RDSBusinessException {
        public PrecheckResourceException() {
            super("subnet ip is not enough！", HttpStatus.BAD_REQUEST.value(), "PrecheckResourceException");
        }
    }

    public static class InsufficientBalanceException extends RDSBusinessException {
        public InsufficientBalanceException() {
            super("insufficient balance", HttpStatus.INTERNAL_SERVER_ERROR.value(), "InsufficientBalance");
        }
    }

    public static class WrongFormatUrlExcption extends RDSBusinessException {
        public WrongFormatUrlExcption() {
            super("Malformed url", HttpStatus.BAD_REQUEST.value(), "WrongFormatUrlExcption");
        }
    }

    public static class RegionNotExistException extends RDSBusinessException {
        public RegionNotExistException() {
            super("The currently requested area does not exist. Please check and try again!",
                    HttpStatus.BAD_REQUEST.value(), "RegionNotExistException");
        }
    }

    public static final class InstanceNotExist extends RDSBusinessException {
        public InstanceNotExist() {
            super("Instance does not exist.", HttpStatus.NOT_FOUND.value(), "InstanceNotExist");
        }
    }

    public static final class InstanceEngineException extends RDSBusinessException {
        public InstanceEngineException() {
            super("Instance engine is not allowed", HttpStatus.BAD_REQUEST.value(), "InstanceEngineError");
        }
    }

    public static final class DeleteAutomatedBackupException extends RDSBusinessException {
        public DeleteAutomatedBackupException() {
            super("delete automated backup is not allowed", HttpStatus.BAD_REQUEST.value(), "DeleteNotAllowed");
        }
    }

    public static final class ApplicationTypeException extends RDSBusinessException {
        public ApplicationTypeException() {
            super("application type is not allowed", HttpStatus.BAD_REQUEST.value(), "ApplicationTypeInvalid");
        }
    }


    public static final class ApiDirectPayException extends RDSBusinessException {
        public ApiDirectPayException() {
            super("api direct pay failed", HttpStatus.INTERNAL_SERVER_ERROR.value(), "ApiPayFailed");
        }
    }

    public static final class AutoRenewToPostPayException extends RDSBusinessException {
        public AutoRenewToPostPayException(String instanceId) {
            super(String.format("The current instance ('%s') has been enabled for automatic renew, please cancel.",
                    instanceId), HttpStatus.BAD_REQUEST.value(), "HasUnderwayAutoRenew", new Object[]{instanceId});
        }
    }

    public static final class ProductExceedQuotaException extends RDSBusinessException {
        public ProductExceedQuotaException(String message) {
            super(String.format("The ('%s') instance you want to create exceeded the system limit.", message),
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), "ExceedQuotaException", new Object[]{message});
        }
    }

    public static class InstanceReleaseFailedException extends RDSBusinessException {
        public InstanceReleaseFailedException(String s) {
            super("Instance ('%s') delete failed.",
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), "InstanceDeleteFailedException", new Object[]{s});
        }
    }

    public static class SubnetPrecheckResourceException extends RDSBusinessException {

        public SubnetPrecheckResourceException() {
            super("subnet ip is not enough！",
                    com.baidu.bce.logic.core.constants.HttpStatus.ERROR_INPUT_INVALID, "PrecheckResourceFailed");
        }
    }

    public static class RDSSuperUserExistException extends RDSBusinessException {
        public RDSSuperUserExistException() {
            super("Super privilege account allowed to create only one.",
                    com.baidu.bce.logic.core.constants.HttpStatus.ERROR_OPERATION_DENY, "SuperAccountExist");
        }
    }

    public static final class InstanceDetailException extends RDSBusinessException {
        public InstanceDetailException(String instanceId) {
            super(String.format("The current instance details interface returns an abnormal result, " +
                            "instance id is ('%s').",
                    instanceId), HttpStatus.BAD_REQUEST.value(), "InstanceDetailAbnormal", new Object[]{instanceId});
        }
    }

    public static final class ResourceUnsatisfiedException extends RDSBusinessException {
        public ResourceUnsatisfiedException(String instanceId) {
            super(String.format("The current instance resource status is unsatisfied, " +
                            "instance id is ('%s').",
                    instanceId), HttpStatus.BAD_REQUEST.value(), "ResourceUnsatisfied", new Object[]{instanceId});
        }
    }

    public static final class ReleaseInstanceException extends RDSBusinessException {
        public ReleaseInstanceException(String instanceId) {
            super(String.format("Release instance occures error, instance id is ('%s').",
                    instanceId), HttpStatus.BAD_REQUEST.value(), "ReleaseInstanceException", new Object[]{instanceId});
        }
    }

    public static final class PrecheckGroupVersionException extends RDSBusinessException {
        public PrecheckGroupVersionException(String leaderId, String followerId) {
            super(String.format("precheck group version failed, leader id is ('%s'), follower id is ('%s')",
                            leaderId, followerId), HttpStatus.BAD_REQUEST.value(), "PrecheckGroupVersionException",
                    new Object[]{leaderId, followerId});
        }
    }
}
