/*
 * Copyright (C) 2015 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.order.model.ServiceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2015/5/5.
 */
@Service
public class QuotaValidatorFactory {

    @Autowired
    List<QuotaValidator> quotaValidators;

    public QuotaValidator getQuotaValidator(ServiceType serviceType) {
        for (QuotaValidator quotaValidator : quotaValidators) {
            if (quotaValidator.getServiceType().equals(serviceType)) {
                return quotaValidator;
            }
        }
        return null;
    }
}
