package com.baidu.bce.logic.rds.service.permission;

import com.baidu.bce.common.network.common.permission.AbstractIdConverter;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Created by luping03 on 18/6/21.
 */
public class LocalMasterShortIdConverter extends AbstractIdConverter {

    private Map<String, InstanceAbstract> idsMap = new HashMap<String, InstanceAbstract>();

    @Override
    public Set<String> convertIds(Set<String> ids) {

        Set<String> result = new HashSet<String>();

        for (String eachId : ids) {

            if (getIdsMap().get(eachId) == null) {
                throw new RDSExceptions.CoverIdException();
            }

            result.add(getIdsMap().get(eachId).getInstanceId());
        }

        return result;
    }

    public Map<String, InstanceAbstract> getIdsMap() {
        return idsMap;
    }

    public void setIdsMap(Map<String, InstanceAbstract> idsMap) {
        this.idsMap = idsMap;
    }
}
