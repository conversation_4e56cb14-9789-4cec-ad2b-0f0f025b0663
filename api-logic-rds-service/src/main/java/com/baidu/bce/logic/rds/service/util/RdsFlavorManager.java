package com.baidu.bce.logic.rds.service.util;

/**
 * Created by g<PERSON><PERSON><PERSON><PERSON> on 2016/1/26.
 */
public class RdsFlavorManager {

    // 获取套餐名称
    public static String getInstanceClass(String engine, int memoryInMb, int storageInGb) {
        // 兼容历史数据
        engine = engine == null ? "MySQL" : engine;
        engine = engine.equals("rdsproxy") ? "MySQL" : engine;
        RdsFlavor rdsFlavor = RdsFlavor.getRdsFlavor(engine, memoryInMb, storageInGb);
        if (rdsFlavor == null) {
            throw new RuntimeException("Rds flavor not exist: engine="
                    + engine + ", memoryInMb=" + memoryInMb + ", storageInGb=" + storageInGb);
        }
        return rdsFlavor.getDescription();
    }

    private enum RdsFlavor {

        // mysql
//        MYSQL_MICRO("rds-micro", "MySQL", 256, 5, 1000),
//        MYSQL_SMALL_1("rds-small-1", "MySQL", 512, 5, 1000),
//        MYSQL_SMALL_2("rds-small-2", "MySQL", 1024, 5, 1000),
//        MYSQL_SMALL_3("rds-small-3", "MySQL", 2048, 5, 1000),
//        MYSQL_MEDIUM_1("rds-medium-1", "MySQL", 4096, 5, 1000),
//        MYSQL_MEDIUM_2("rds-medium-2", "MySQL", 8192, 5, 1000),
//        MYSQL_LARGE_1("rds-large-1", "MySQL", 16384, 5, 1000),
//        MYSQL_LARGE_2("rds-large-2", "MySQL", 24576, 5, 1000),
//        MYSQL_LARGE_3("rds-large-3", "MySQL", 32768, 5, 1000),
//        MYSQL_LARGE_4("rds-large-4", "MySQL", 49152, 5, 1000),
//        MYSQL_LARGE_5("rds-large-5", "MySQL", 65536, 5, 1000),

        MYSQL_MICRO("db1.micro", "MySQL", 256, 5, 1000),
        MYSQL_SMALL("db1.small", "MySQL", 512, 5, 1000),
        MYSQL_MEDIUM("db1.medium", "MySQL", 1024, 5, 1000),
        MYSQL_LARGE("db1.large", "MySQL", 2048, 5, 1000),
        MYSQL_LARGE_x("db1.xlarge", "MySQL", 4096, 5, 1000),
        MYSQL_LARGE_2x("db2.2xlarge", "MySQL", 8192, 5, 1000),
        MYSQL_LARGE_4x("db2.4xlarge", "MySQL", 16384, 5, 1000),
        MYSQL_LARGE_6x("db2.6xlarge", "MySQL", 24576, 5, 1000),
        MYSQL_LARGE_8x("db2.8xlarge", "MySQL", 32768, 5, 1000),
        MYSQL_LARGE_10x("db2.10xlarge", "MySQL", 49152, 5, 1000),
        MYSQL_LARGE_12x("db2.12xlarge", "MySQL", 65536, 5, 1000),

        // mysql finance
        MYSQL_FINANCE_LARGE_1("db2.2xlarge", "MySQL", 8192, 100, 1000),
        MYSQL_FINANCE_LARGE_2("db2.4xlarge", "MySQL", 16384, 100, 1000),
        MYSQL_FINANCE_LARGE_3("db2.6xlarge", "MySQL", 24576, 100, 1000),
        MYSQL_FINANCE_LARGE_4("db2.8xlarge", "MySQL", 32768, 100, 1000),
        MYSQL_FINANCE_LARGE_5("db2.10xlarge", "MySQL", 49152, 100, 1000),
        MYSQL_FINANCE_LARGE_6("db2.12xlarge", "MySQL", 65536, 100, 1000),

        // sqlserver
        SQLSERVER_MICRO("rds-micro", "sqlserver", 1024, 5, 1000),
        SQLSERVER_SMALL("rds-small", "sqlserver", 2048, 5, 1000),
        SQLSERVER_MEDIUM_1("rds-medium-1", "sqlserver", 4096, 5, 1000),
        SQLSERVER_MEDIUM_2("rds-medium-2", "sqlserver", 8192, 5, 1000),
        SQLSERVER_LARGE_1("rds-large-1", "sqlserver", 12288, 5, 1000),
        SQLSERVER_LARGE_2("rds-large-2", "sqlserver", 16384, 5, 1000),
        SQLSERVER_LARGE_3("rds-large-3", "sqlserver", 24576, 5, 1000),
        SQLSERVER_LARGE_4("rds-large-4", "sqlserver", 32768, 5, 1000),
        SQLSERVER_LARGE_5("rds-large-5", "sqlserver", 49152, 5, 1000),
        SQLSERVER_LARGE_6("rds-large-6", "sqlserver", 65536, 5, 1000),

        // postgresql
        POSTGRESQL_SMALL_1("rds-small-1", "postgresql", 512, 5, 1000),
        POSTGRESQL_SMALL_2("rds-small-2", "postgresql", 1024, 5, 1000),
        POSTGRESQL_SMALL_3("rds-small-3", "postgresql", 2048, 5, 1000),
        POSTGRESQL_MEDIUM_1("rds-medium-1", "postgresql", 4096, 5, 1000),
        POSTGRESQL_MEDIUM_2("rds-medium-2", "postgresql", 8192, 5, 1000),
        POSTGRESQL_LARGE_1("rds-large-1", "postgresql", 16384, 5, 1000),
        POSTGRESQL_LARGE_2("rds-large-2", "postgresql", 24576, 5, 1000),
        POSTGRESQL_LARGE_3("rds-large-3", "postgresql", 32768, 5, 1000),
        POSTGRESQL_LARGE_4("rds-large-4", "postgresql", 49152, 5, 1000),
        POSTGRESQL_LARGE_5("rds-large-5", "postgresql", 65536, 5, 1000);

        RdsFlavor(String description, String engine, int memoryInMb, int storageMinInGb, int storageMaxInGb) {
            this.description = description;
            this.engine = engine;
            this.memoryInMb = memoryInMb;
            this.storageMinInGb = storageMinInGb;
            this.storageMaxInGb = storageMaxInGb;
        }

        private String description;
        private String engine;
        private int memoryInMb;
        private int storageMinInGb;
        private int storageMaxInGb;

        public String getDescription() {
            return description;
        }

        public String getEngine() {
            return engine;
        }

        public int getMemoryInMb() {
            return memoryInMb;
        }

        public int getStorageMinInGb() {
            return storageMinInGb;
        }

        public int getStorageMaxInGb() {
            return storageMaxInGb;
        }

        public static RdsFlavor getRdsFlavor(String engine, int memoryInMb, int storageInGb) {
            RdsFlavor targetRdsFlavor = null;
            for (RdsFlavor rdsFlavor : RdsFlavor.values()) {
                if (rdsFlavor.engine.equalsIgnoreCase(engine) && rdsFlavor.memoryInMb == memoryInMb) {
                    if (storageInGb >= rdsFlavor.storageMinInGb && storageInGb <= rdsFlavor.storageMaxInGb) {
                        targetRdsFlavor = rdsFlavor;
                    }
                }
            }
            return targetRdsFlavor;
        }
    }

}
