package com.baidu.bce.logic.rds.service.model.order;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by luping03 on 17/7/26.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CancelToPostpayOrderRequest {

    @IdPermission
    private List<String> instanceIds;

    public List<String> getInstanceIds() {
        return instanceIds;
    }

    public void setInstanceIds(List<String> instanceIds) {
        this.instanceIds = instanceIds;
    }
}

