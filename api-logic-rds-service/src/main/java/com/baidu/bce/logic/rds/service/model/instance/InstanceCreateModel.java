package com.baidu.bce.logic.rds.service.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.TagPermisson;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.ProductPayType;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCreateRequest;
import com.baidu.bce.internalsdk.rds.model.instance.OrderItemExtraInfo;
import com.baidu.bce.internalsdk.rds.model.instance.RecoveryToSourceInstanceRequest;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.dao.model.MachinePO;
import com.baidu.bce.internalsdk.rds.util.PatternString;
import com.baidu.bce.logic.rds.service.constant.InstanceType;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wordnik.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang.StringUtils;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.*;

/**
 * Created by hejianbin on 2014/6/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class InstanceCreateModel {

    @NotNull
    @Valid
    private DashCreateInstance instance;

    private Integer duration;

    @NotNull
    private String productType;

    private String timeUnit = "MINUTE";

    @NotNull
    @Range(min = 1L, max = 10L, message = "一次最多购买10个rds实例！")
    private Integer number = 1;

    private String autoRenewTimeUnit;

    private int autoRenewTime = 0;

    private List<String> instanceShortIds;  // 自己生成的实例短ID

    private Boolean isDirectPay = false;

    @Override
    public String toString() {
        return "DashboardInstanceCreateModel{"
                + "instance=" + instance
                + ", duration=" + duration
                + ", number=" + number
                + '}';
    }

    /**
     * 生成创建订单时的Extra字段，包括InstanceCreateRequest和自动续费信息
     * 订单执行器用
     * @return OrderItemExtraInfo
     */
    public OrderItemExtraInfo convertToOrderItemExtraInfo(Map<String, String> zoneMap) {
        OrderItemExtraInfo extraInfo = new OrderItemExtraInfo();
        InstanceCreateRequest instanceCreateRequest = new InstanceCreateRequest();
        extraInfo.setInstanceCreateRequest(convertTo(instanceCreateRequest, zoneMap));
        extraInfo.setAutoRenewTime(this.autoRenewTime);
        extraInfo.setAutoRenewTimeUnit(this.autoRenewTimeUnit);
        return extraInfo;
    }

    /**
     * 转化为等价的API使用的生成实例的请求
     *
     * @return InstanceCreateRequest
     */
    public InstanceCreateRequest convertTo(InstanceCreateRequest instanceCreateRequest, Map<String, String> zoneMap) {
        String instanceType = InstanceType.INSTANCE_TYPE_MASTER.getValue();
        instanceCreateRequest.withInstanceAmount(this.getNumber());
        try {
            instanceCreateRequest.withOrderId(UUID.randomUUID().toString());
            instanceCreateRequest.withChildUserId(LogicUserService.getUserId());
            instanceCreateRequest.withInstanceIds(this.getInstanceShortIds()); // raft新增传给后端instanceIds
            InstanceCreateRequest.InstanceParameters instanceParameter = instanceCreateRequest.getInstanceParameters();
            instanceParameter.setCategory(this.getInstance().getCategory());
            instanceParameter.setBackupPolicy(this.getInstance().getBackupPolicy());
            instanceParameter.withEngine(this.getInstance().getEngine())
//                    .withAllocatedMemoryInMB(this.getInstance().getAllocatedMemoryInGB() * 1024)
//                    .withAllocatedStorageInGB(this.getInstance().getAllocatedStorageInGB())
                    .withCpuCount(this.getInstance().getCpuCount())
                    .withAzone(this.getInstance().getAzone())
                    .withReplicaType(this.getInstance().getReplicaType())
                    .withResourceType(this.getInstance().getResourceType())
                    .withPubliclyAccessible(false);
            if (this.getInstance().getIsInheritMasterAuthip() != null) {
                instanceParameter.setIsInheritMasterAuthip(this.getInstance().getIsInheritMasterAuthip());
            }
            if (this.getInstance().getResourcePlatform() != null) {
                instanceParameter.setResourcePlatform(this.getInstance().getResourcePlatform());
            }
            if (this.getInstance().getSupportStorageEngine() != null) {
                instanceParameter.setSupportStorageEngine(this.getInstance().getSupportStorageEngine());
            }
            if (this.getInstance().getIsDataBackupCopy()) {
                instanceParameter.setIsDataBackupCopy(this.getInstance().getIsDataBackupCopy());
            }
            if (this.getInstance().getCdsEncryptKey() != null) {
                instanceParameter.setCdsEncryptKey(this.getInstance().getCdsEncryptKey());
            }
            if (this.getInstance().getParameterTemplateId() != null){
                instanceParameter.setParameterTemplateId(this.getInstance().getParameterTemplateId());
            }
            if (this.getInstance().getBcmGroupName() != null) {
                instanceParameter.setBcmGroupName(this.getInstance().getBcmGroupName());
            }

            if (this.getInstance().getLeaderInstanceId() != null) {
                instanceParameter.setLeaderAppId(this.getInstance().getLeaderInstanceId());
            }
            if (this.getInstance().getOvip() != null){
                instanceParameter.setOvip(this.getInstance().getOvip());
            }
            if (this.getInstance().getReplicaType() != null) {
                instanceParameter.setReplicaType(this.getInstance().getReplicaType());
            }
            if (this.getInstance().getResourceType() != null) {
                instanceParameter.setResourceType(this.getInstance().getResourceType());
            }
            if (this.getInstance().getEntryPort() != null){
                instanceParameter.setEntryPort(this.getInstance().getEntryPort());
            }
            if (this.getInstance().getDiskType() != null){
                instanceParameter.setDiskType(this.getInstance().getDiskType());
            }
            if (this.getInstance().getCdsType() != null){
                instanceParameter.setCdsType(this.getInstance().getCdsType());
            }
            if (this.getInstance().getLowerCaseTableNames() != null){
                instanceParameter.setLowerCaseTableNames(this.getInstance().getLowerCaseTableNames());
            }
            if (this.getInstance().getData() != null){
                instanceParameter.setData(this.getInstance().getData());
            }
            if (this.getInstance().getSourceInstanceId() != null
                    && !"".equals(this.getInstance().getSourceInstanceId())) {
                instanceParameter.setSourceInstanceId(this.getInstance().getSourceInstanceId());
                instanceType = InstanceType.INSTANCE_TYPE_PROXY.getValue();
            }

            if (this.getInstance().getEngineVersion() != null
                    && !"".equals(this.getInstance().getEngineVersion())) {
                instanceParameter.withEngineVersion(this.getInstance().getEngineVersion());
                if (this.getInstance().getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER) &&
                    this.getInstance().getEngineVersion().equals(RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2016)) {
                    instanceParameter.withEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016);
                }
                if (this.getInstance().getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER) &&
                        this.getInstance().getEngineVersion().equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012)) {
                    this.getInstance().setIsSingle(true);
                }
            }

            if (StringUtils.isEmpty(this.getInstance().getSourceInstanceId())
                    || !this.getInstance().getEngine().equals("rdsproxy")) {
                String instanceClass = "rds";
                instanceParameter.withInstanceClass(instanceClass);
//                        .withCharacterSetName(this.getInstance().getCharacterSetName());

                if (("mysql").equals(this.getInstance().getEngine().toLowerCase())
                        && StringUtils.isEmpty(this.getInstance().getSourceInstanceId())) {
                    instanceParameter.setCharacterSetName(this.getInstance().characterSetName);
                }


                if (this.getInstance().getAllocatedMemoryInGB() != null) {
                    instanceParameter.setAllocatedMemoryInMB(this.getInstance().getAllocatedMemoryInGB() * 1024);
                }
                if (this.getInstance().getAllocatedStorageInGB() != null) {
                    instanceParameter.setAllocatedStorageInGB(this.getInstance().getAllocatedStorageInGB());
                }

                instanceType = InstanceType.INSTANCE_TYPE_REPLICA.getValue();
            }

            if (ProductPayType.PRE_PAY.toString().equalsIgnoreCase(productType)) {
                Calendar now = Calendar.getInstance();
                now.add(Calendar.MONTH, this.getDuration());
                instanceParameter.withInstanceExpireTime(now.getTime());
            }

            instanceParameter.setVpcId(this.getInstance().getVpcId());
            Map<String, String> subnetMap = new LinkedHashMap<>();
            if (!StringUtils.isEmpty(this.getInstance().getSubnetId())) {
                String[] subnetItems = this.getInstance().getSubnetId().split(",");
                for (String item : subnetItems) {
                    if (!item.trim().isEmpty()) {
                        String[] part = item.trim().split(":");
                        if (part.length == 2) {
                            subnetMap.put(part[0], part[1]);
                        } else if (part.length == 1) {
                            subnetMap.put(part[0], "");
                        }
                    }
                }
            }
            instanceParameter.setPhysicalZone(zoneMap);
            instanceParameter.setSubnetId(subnetMap);
            instanceParameter.setInitialDataReference(this.getInstance().getInitialDataReference());
            // 金融云RDS新增字段
            instanceParameter.whitIsEnhanced(this.getInstance().getIsEnhanced());
            instanceParameter.setNodeAmount(this.getInstance().getNodeAmount());
            instanceParameter.setReplicationType(this.getInstance().getReplicationType());
            instanceParameter.setInstanceName(this.getInstance().getInstanceName());
            instanceParameter.setInstanceType(this.getInstance().getInstanceType()); // raft版新增, =financial

            instanceParameter.setIsSingle(this.getInstance().getIsSingle());
            instanceParameter.setDiskType(this.getInstance().getDiskType());
            instanceParameter.setCdsType(this.getInstance().getCdsType());
            instanceParameter.setDiskIoType(this.getInstance().getDiskIoType());

            if (StringUtils.isEmpty(this.getInstance().getInstanceName())) {
                boolean isClone = false;
                if (instance.getInitialDataReference() != null
                        && StringUtils.isNotEmpty(instance.getInitialDataReference().getInstanceId())) {
                    isClone = true;
                }
                String defaultName = BasisUtils.createInstanceName(
                        instance.getEngine(), instance.getEngineVersion(), instanceType, isClone);
                instanceParameter.setInstanceName(defaultName);
                instance.setInstanceName(defaultName);
            } else {
                instanceParameter.setInstanceName(this.getInstance().getInstanceName());
            }
            if ("dcc".equalsIgnoreCase(instance.getMachineType())) {
                for (MachinePO machinePO : this.getInstance().getDccHosts()) {
                    if ("Master".equalsIgnoreCase(machinePO.getInstanceRole())) {
                        instanceParameter.getDccHostIds().setMaster(new InstanceCreateRequest.DccHostId(
                                machinePO.getMachineInstanceId(), machinePO.getAzone()));
                    }
                    if ("Backup".equalsIgnoreCase(machinePO.getInstanceRole())) {
                        instanceParameter.getDccHostIds().setBackup(new InstanceCreateRequest.DccHostId(
                                machinePO.getMachineInstanceId(), machinePO.getAzone()));
                    }
                }
            }
            instanceParameter.setMachineType(this.getInstance().getMachineType());

            // 设置BLB专属集群参数
            instanceParameter.withBgwGroupId(this.getInstance().getBgwGroupId());
            instanceParameter.withBgwGroupExclusive(this.getInstance().isBgwGroupExclusive());

            instanceParameter.setEdgeRegion(this.getInstance().getEdgeRegion());
            instanceParameter.setEdgeVpcId(this.getInstance().getEdgeVpcId());
            instanceParameter.setEdgeSubnetId(this.getInstance().getEdgeSubnetId());
        } catch (BceInternalResponseException ex) {
            LogicRdsExceptionHandler.handle(ex);
        }
        return instanceCreateRequest;
    }

    public String getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(String timeUnit) {
        this.timeUnit = timeUnit;
    }

    public String getAutoRenewTimeUnit() {
        return autoRenewTimeUnit;
    }

    public void setAutoRenewTimeUnit(String autoRenewTimeUnit) {
        this.autoRenewTimeUnit = autoRenewTimeUnit;
    }

    public int getAutoRenewTime() {
        return autoRenewTime;
    }

    public void setAutoRenewTime(int autoRenewTime) {
        this.autoRenewTime = autoRenewTime;
    }

    public DashCreateInstance getInstance() {
        return instance;
    }

    public void setInstance(DashCreateInstance instance) {
        this.instance = instance;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public List<String> getInstanceShortIds() {
        return instanceShortIds;
    }

    public void setInstanceShortIds(List<String> instanceShortIds) {
        this.instanceShortIds = instanceShortIds;
    }

    public Boolean getIsDirectPay() {
        return this.isDirectPay;
    }

    public void setIsDirectPay(Boolean isDirectPay) {
        this.isDirectPay = isDirectPay;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DashCreateInstance {
        @Pattern(regexp = PatternString.PATTERN_INSTANCE_NAME)
        private String instanceName;
        @NotNull
        private String engine;

        private String engineVersion;

        @Min(1)
        private Integer allocatedMemoryInGB;

        @Min(1)
        private Integer allocatedMemoryInMB;

        @Min(5)
        private Integer allocatedStorageInGB;

        private String characterSetName;

        private String location;

        private String sourceInstanceId;

        private String instanceId; // 后付费变配获取价格 用

        private String azone = "";

        private List<String> zoneNames = new ArrayList<>(); // 接收openapi传过来的可用区信息

        private List<SubnetMap> subnets = new ArrayList<>(); // 接收openapi传过来的子网信息

        private String vpcId;

        private String subnetId = "";

        private InstanceCreateRequest.InitialDataReference initialDataReference;

        private List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> data;

        private Boolean isEnhanced; // 上海金融专区三节点增强版标识

        // 单机版，临时用这个标识，鉴于有些部分本次改不动了，以后这些都要去掉
        private Boolean isSingle;

        private int cpuCount;

        private Integer nodeAmount;

        private String category; // 实例系列，单机双机三节点

        private String replicationType; // 无用，后端不支持创建时设置数据同步模式

        // 这个字段语义不明，和其他
        private String instanceType;    //  上海金融专区新增，表示RDS类型，financial表示raft版, singleton 表示单机版

        @Valid
        private SnapshotPolicy backupPolicy = new SnapshotPolicy();

        private Boolean oldFlavor = false; // 是否是老套餐（上一代）

        private List<MachinePO> dccHosts;

        private String machineType;

        private Boolean relationTag;

        @TagPermisson
        private List<Tag> tags;

        private String diskIoType;

        /* 专属集群ID。如果bgwGroupExclusive为true，要将blb分配到哪个专属集群，如果不传默认分配到用户名下BLB实例最少的专属集群 */
        /* 支持版本：MySQL双机版/单机版、只读实例、代理实例、SQLServer2016单机, 不支持的版本传此参数无效 */
        private String bgwGroupId;

        /* 是否使用专属集群 */
        private boolean bgwGroupExclusive;

        private String diskType;

        private String cdsType;

        private String resourceGroupId;

        private String ovip;

        private Integer entryPort;

        private Integer lowerCaseTableNames;

        private String parameterTemplateId;

        // Map<edgeRegionId, Count> 每个节点购买的数量
        private Map<String, Integer> edgeRegion;

        private Map<String, String> edgeVpcId;

        private Map<String, String> edgeSubnetId;

        // 用于控制是否为高可用只读实例
        private String replicaType;

        private String bcmGroupName;

        private String leaderAppId;

        private String leaderInstanceId;

        private String resourceType;

        @ApiModelProperty("只读实例是否继承主实例 IP 白名单，默认不继承")
        private Boolean isInheritMasterAuthip;

        @ApiModelProperty("kms ID")
        private String cdsEncryptKey;

        private String resourcePlatform;

        private String supportStorageEngine;

        private boolean isDataBackupCopy;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("DashCreateInstance{");
            sb.append("engine='").append(engine).append('\'');
            sb.append(", engineVersion='").append(engineVersion).append('\'');
            sb.append(", allocatedMemoryInG=").append(allocatedMemoryInGB);
            sb.append(", allocatedStorageInGB=").append(allocatedStorageInGB);
            sb.append(", characterSetName='").append(characterSetName).append('\'');
            sb.append(", location='").append(location).append('\'');
            sb.append(", sourceInstanceId='").append(sourceInstanceId).append('\'');
            sb.append(", azone='").append(azone).append('\'');
            sb.append(", vpcId='").append(vpcId).append('\'');
            sb.append(", subnetId='").append(subnetId).append('\'');
            sb.append(", isEnhanced='").append(isEnhanced).append('\'');
            sb.append(", isSingle='").append(getIsSingle()).append('\'');
            sb.append(", instanceType=").append(instanceType).append('\'');
            sb.append(", category=").append(category).append('\'');
            sb.append(", relationTag=").append(getRelationTag()).append('\'');
            sb.append(", tags=").append(getTags()).append('\'');
            sb.append(", diskType=").append(diskType).append('\'');
            sb.append(", cdsType=").append(cdsType).append('\'');
            sb.append('}');
            return sb.toString();
        }

        public boolean getIsDataBackupCopy() {
            return isDataBackupCopy;
        }

        public void setIsDataBackupCopy(boolean dataBackupCopy) {
            isDataBackupCopy = dataBackupCopy;
        }

        public String getResourcePlatform() {
            return resourcePlatform;
        }

        public void setResourcePlatform(String resourcePlatform) {
            this.resourcePlatform = resourcePlatform;
        }

        public String getSupportStorageEngine() {
            return supportStorageEngine;
        }

        public void setSupportStorageEngine(String supportStorageEngine) {
            this.supportStorageEngine = supportStorageEngine;
        }


        public String getCdsEncryptKey() {
            return cdsEncryptKey;
        }

        public void setCdsEncryptKey(String cdsEncryptKey) {
            this.cdsEncryptKey = cdsEncryptKey;
        }

        public Boolean getIsInheritMasterAuthip() {
            return isInheritMasterAuthip;
        }

        public void setIsInheritMasterAuthip(Boolean inheritMasterAuthip) {
            isInheritMasterAuthip = inheritMasterAuthip;
        }

        public String getLeaderAppId() {
            return leaderInstanceId;
        }

        public void setLeaderAppId(String leaderAppId) {
            this.leaderAppId = leaderAppId;
        }

        public String getLeaderInstanceId() {
            return leaderInstanceId;
        }

        public void setLeaderInstanceId(String leaderInstanceId) {
            this.leaderInstanceId = leaderInstanceId;
        }

        public String getResourceType() {
            return resourceType;
        }

        public void setResourceType(String resourceType) {
            this.resourceType = resourceType;
        }

        public String getBcmGroupName() {
            return bcmGroupName;
        }

        public void setBcmGroupName(String bcmGroupName) {
            this.bcmGroupName = bcmGroupName;
        }

        public Map<String, Integer> getEdgeRegion() {
            return edgeRegion;
        }

        public void setEdgeRegion(Map<String, Integer> edgeRegion) {
            this.edgeRegion = edgeRegion;
        }

        public Map<String, String> getEdgeVpcId() {
            return edgeVpcId;
        }

        public void setEdgeVpcId(Map<String, String> edgeVpcId) {
            this.edgeVpcId = edgeVpcId;
        }

        public Map<String, String> getEdgeSubnetId() {
            return edgeSubnetId;
        }

        public void setEdgeSubnetId(Map<String, String> edgeSubnetId) {
            this.edgeSubnetId = edgeSubnetId;
        }

        public String getDiskType() {
            return diskType;
        }

        public void setDiskType(String diskType) {
            this.diskType = diskType;
        }

        public String getCdsType() {
            return cdsType;
        }

        public void setCdsType(String cdsType) {
            this.cdsType = cdsType;
        }

        public String getMachineType() {
            return machineType;
        }

        public void setMachineType(String machineType) {
            this.machineType = machineType;
        }

        public List<MachinePO> getDccHosts() {
            return dccHosts;
        }

        public void setDccHosts(List<MachinePO> dccHosts) {
            this.dccHosts = dccHosts;
        }

        public Integer getAllocatedMemoryInMB() {
            return allocatedMemoryInMB;
        }

        public void setAllocatedMemoryInMB(Integer allocatedMemoryInMB) {
            this.allocatedMemoryInMB = allocatedMemoryInMB;
        }

        public Boolean getOldFlavor() {
            return oldFlavor;
        }

        public void setOldFlavor(Boolean oldFlavor) {
            this.oldFlavor = oldFlavor;
        }

        public String getInstanceName() {
            return instanceName;
        }

        public void setInstanceName(String instanceName) {
            this.instanceName = instanceName;
        }

        public List<String> getZoneNames() {
            return zoneNames;
        }

        public void setZoneNames(List<String> zoneNames) {
            this.zoneNames = zoneNames;
        }

        public List<SubnetMap> getSubnets() {
            return subnets;
        }

        public void setSubnets(List<SubnetMap> subnets) {
            this.subnets = subnets;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getReplicationType() {
            return replicationType;
        }

        public void setReplicationType(String replicationType) {
            this.replicationType = replicationType;
        }

        public Boolean getEnhanced() {
            return isEnhanced;
        }

        public void setEnhanced(Boolean enhanced) {
            isEnhanced = enhanced;
        }

        public int getCpuCount() {
            return cpuCount;
        }

        public void setCpuCount(int cpuCount) {
            this.cpuCount = cpuCount;
        }

        public Integer getNodeAmount() {
            return nodeAmount;
        }

        public void setNodeAmount(Integer nodeAmount) {
            this.nodeAmount = nodeAmount;
        }

        public SnapshotPolicy getBackupPolicy() {
            return backupPolicy;
        }

        public void setBackupPolicy(SnapshotPolicy backupPolicy) {
            this.backupPolicy = backupPolicy;
        }

        public void setAllocatedStorageInGB(Integer allocatedStorageInGB) {
            this.allocatedStorageInGB = allocatedStorageInGB;
        }

        public String getSourceInstanceId() {
            return sourceInstanceId;
        }

        public void setSourceInstanceId(String sourceInstanceId) {
            this.sourceInstanceId = sourceInstanceId;
        }

        public String getCharacterSetName() {
            return characterSetName;
        }

        public void setCharacterSetName(String characterSetName) {
            this.characterSetName = characterSetName;
        }

        public String getEngine() {
            return engine;
        }

        public void setEngine(String engine) {
            this.engine = engine;
        }

        public String getEngineVersion() {
            return engineVersion;
        }

        public void setEngineVersion(String engineVersion) {
            this.engineVersion = engineVersion;
        }

        public Integer getAllocatedMemoryInGB() {
            return allocatedMemoryInGB;
        }

        public void setAllocatedMemoryInGB(Integer allocatedMemoryInGB) {
            this.allocatedMemoryInGB = allocatedMemoryInGB;
        }

        public Integer getAllocatedStorageInGB() {
            return allocatedStorageInGB;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getInstanceId() {
            return instanceId;
        }

        public void setInstanceId(String instanceId) {
            this.instanceId = instanceId;
        }

        public String getAzone() {
            return azone;
        }

        public void setAzone(String azone) {
            this.azone = azone;
        }

        public String getVpcId() {
            return vpcId;
        }

        public void setVpcId(String vpcId) {
            this.vpcId = vpcId;
        }

        public String getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(String subnetId) {
            this.subnetId = subnetId;
        }

        public InstanceCreateRequest.InitialDataReference getInitialDataReference() {
            return initialDataReference;
        }

        public void setInitialDataReference(InstanceCreateRequest.InitialDataReference initialDataReference) {
            this.initialDataReference = initialDataReference;
        }

        public Boolean getIsEnhanced() {
            return isEnhanced;
        }

        public void setIsEnhanced(Boolean enhanced) {
            isEnhanced = enhanced;
        }

        public String getInstanceType() {
            return instanceType;
        }

        public void setInstanceType(String instanceType) {
            this.instanceType = instanceType;
        }

        public Boolean getIsSingle() {
            return isSingle;
        }

        public void setIsSingle(Boolean isSingle) {
            this.isSingle = isSingle;
        }

        public String getDiskIoType() {
            return diskIoType;
        }

        public void setDiskIoType(String diskIoType) {
            this.diskIoType = diskIoType;
        }

        public Boolean getRelationTag() {
            return relationTag;
        }

        public void setRelationTag(Boolean relationTag) {
            this.relationTag = relationTag;
        }

        public List<Tag> getTags() {
            return tags;
        }

        public void setTags(List<Tag> tags) {
            this.tags = tags;
        }

        public String getResourceGroupId() {
            return resourceGroupId;
        }

        public void setResourceGroupId(String resourceGroupId) {
            this.resourceGroupId = resourceGroupId;
        }
        public String getOvip() {
            return ovip;
        }

        public void setOvip(String ovip) {
            this.ovip = ovip;
        }
        public String getBgwGroupId() {
            return bgwGroupId;
        }

        public void setBgwGroupId(String bgwGroupId) {
            this.bgwGroupId = bgwGroupId;
        }

        public boolean isBgwGroupExclusive() {
            return bgwGroupExclusive;
        }

        public void setBgwGroupExclusive(boolean bgwGroupExclusive) {
            this.bgwGroupExclusive = bgwGroupExclusive;
        }

        public Integer getEntryPort() {
            return entryPort;
        }

        public void setEntryPort(Integer entryPort) {
            this.entryPort = entryPort;
        }

        public Integer getLowerCaseTableNames() {
            return lowerCaseTableNames;
        }

        public void setLowerCaseTableNames(Integer lowerCaseTableNames) {
            this.lowerCaseTableNames = lowerCaseTableNames;
        }

        public String getParameterTemplateId() {
            return parameterTemplateId;
        }

        public void setParameterTemplateId(String parameterTemplateId) {
            this.parameterTemplateId = parameterTemplateId;
        }

        public List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> getData() {
            return data;
        }

        public void setData(List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> data) {
            this.data = data;
        }

        public String getReplicaType() {
            return replicaType;
        }

        public void setReplicaType(String replicaType) {
            this.replicaType = replicaType;
        }
    }

    public static class SubnetMap {
        private String zoneName;
        private String subnetId;

        public String getZoneName() {
            return zoneName;
        }

        public void setZoneName(String zoneName) {
            this.zoneName = zoneName;
        }

        public String getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(String subnetId) {
            this.subnetId = subnetId;
        }
    }
}
