/*
 * Copyright (C) 2015 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.order.model.ServiceType;
import org.springframework.stereotype.Service;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2015/5/5.
 */
@Service
public class MasterQuotaValidator extends QuotaValidator {

    @Override
    public ServiceType getServiceType() {
        return ServiceType.RDS;
    }

    @Override
    public RdsQuotaService.QuotaType getQuotaType() {
        return RdsQuotaService.QuotaType.master;
    }

    @Override
    public Integer getActiveInstanceCount(String sourceInstanceId) {
        return getActiveResourceList().size();
    }

}
