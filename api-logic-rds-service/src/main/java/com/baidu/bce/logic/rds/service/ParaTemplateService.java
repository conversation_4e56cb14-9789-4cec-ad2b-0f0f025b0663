package com.baidu.bce.logic.rds.service;

import com.alibaba.fastjson.JSON;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.iam.model.Token;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.config.ConfigItem;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ApplyDetail;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ApplyInstance;
import com.baidu.bce.internalsdk.rds.model.paratemplate.CreateParaTemplateResponse;
import com.baidu.bce.internalsdk.rds.model.paratemplate.DuplicateTempRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateApplyHistory;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateCompare;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateCreateRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateDetail;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateModel;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplatePage;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateUpdateRequest;
import com.baidu.bce.internalsdk.rds.model.paratemplate.ParaTemplateCompareRequest;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.service.model.argument.ConfigList;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.util.InstanceForApiUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.alibaba.fastjson.JSONObject;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;

@Service
public class ParaTemplateService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RdsAsyncService.class);

    @Autowired
    LogicRdsClientFactory rdsClientFactory;

    @Autowired
    InstanceService instanceService;

    @Autowired
    InstanceForApiUtils instanceForApiUtils;

    @Autowired
    ArgumentService argumentService;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(10, 100, 3,
            TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(100),
            new ThreadFactoryBuilder().setDaemon(true).setNameFormat("LogicRDSServiceThreadPool-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 模板列表
     * @param pageNo
     * @param pageSize
     * @return
     */
    public ParaTemplatePage<ParaTemplateModel> listParaTemplate(Integer pageNo, Integer pageSize,
                                                                String dbType, String dbVersion, String type,
                                                                String tplName, String showId) {

         RDSClient rdsClient = rdsClientFactory.createRdsClient();
         JSONObject jsonObject = rdsClient.listParaTemplate(pageNo, pageSize, dbType, dbVersion, type, tplName, showId);


         ParaTemplatePage<ParaTemplateModel> paraTemplatePage = new ParaTemplatePage();

         List<ParaTemplateModel> paraTemplateModelList = new ArrayList<>();
        if (jsonObject != null) {
            // Map<String, Object> map = JSONObject.parseObject(jsonObject.toString(), Map.class);

            paraTemplatePage.setPage((Integer) jsonObject.get("page"));
            paraTemplatePage.setPerpage((Integer) jsonObject.get("perpage"));
            paraTemplatePage.setTotal((Integer) jsonObject.get("total"));
            if (jsonObject.get("list") != null) {
                paraTemplateModelList =
                        JSON.parseArray(jsonObject.getJSONArray("list").toJSONString(), ParaTemplateModel.class);
            }
        }
        paraTemplatePage.setList(paraTemplateModelList);
        return paraTemplatePage;
    }

    /**
     * 模板应用历史
     * @param templateId
     * @param pageNo
     * @param pageSize
     * @return
     */
    public ParaTemplatePage<ParaTemplateApplyHistory> listParaTemplateApplyHistory(String templateId, Integer pageNo,
                                                                                   Integer pageSize) {
        RDSClient rdsClient = rdsClientFactory.createRdsClient();
        JSONObject jsonObject = rdsClient.listParaTemplateApplyHistory(templateId, pageNo, pageSize);

        List<ParaTemplateApplyHistory> templateModelList = new ArrayList<>();
        ParaTemplatePage<ParaTemplateApplyHistory> paraTemplatePage = new ParaTemplatePage<>();

        if (jsonObject != null) {
            paraTemplatePage.setPage((Integer) jsonObject.get("page"));
            paraTemplatePage.setPerpage((Integer) jsonObject.get("perpage"));
            paraTemplatePage.setTotal((Integer) jsonObject.get("total"));

            if (jsonObject.get("list") != null) {
                templateModelList = JSON.parseArray(jsonObject.getJSONArray("list").toJSONString(),
                                                                                    ParaTemplateApplyHistory.class);
            }
        }
        List<InstanceAbstract> instances = instanceService.listInstanceByExpiredDate(-1,
                "instanceName", "desc", null,
                null, RDSConstant.FROM_CONSOLE);
        instances = (List<InstanceAbstract>) instanceForApiUtils.standardInstanceAbstractList(instances,
                RDSConstant.FROM_CONSOLE, null, Boolean.TRUE);

        if (instances != null && instances.size() > 0) {
            for (InstanceAbstract instance : instances) {
                for (ParaTemplateApplyHistory paraTemplateApplyHistory : templateModelList) {
                    if ((!"creating".equals(instance.getInstanceStatus())
                            && instance.getInstanceId().equals(paraTemplateApplyHistory.getAppId()))) {
                        paraTemplateApplyHistory.setInstanceId(instance.getInstanceId());
                        paraTemplateApplyHistory.setEngine(instance.getEngine());
                        paraTemplateApplyHistory.setEngineVersion(instance.getEngineVersion());
                        paraTemplateApplyHistory.setInstanceName(instance.getInstanceName());
                        paraTemplateApplyHistory.setInstanceCreateTime(instance.getInstanceCreateTime());
                        paraTemplateApplyHistory.setApplicationType(instance.getApplicationType());
                        paraTemplateApplyHistory.setInstanceStatus(instance.getInstanceStatus());
                        paraTemplateApplyHistory.setAzone(instance.getAzone());
                        paraTemplateApplyHistory.setRegion(instance.getRegion());
                        paraTemplateApplyHistory.setAppIdShort(instance.getInstanceShortId());
                    }
                }
            }
        }
        paraTemplatePage.setList(templateModelList);
        return paraTemplatePage;
    }

    /**
     * 模板应用详情
     * @param applyId
     * @return
     */
    public List<ApplyDetail> getParaTemplateApplyDetail(String applyId) {

        RDSClient rdsClient = rdsClientFactory.createRdsClient();
        JSONObject jsonObject = rdsClient.getParaTemplateApplyDetail(applyId);

        List<ApplyDetail> list = new ArrayList<>();
        if (jsonObject != null) {
            List<Map> mapList = JSON.parseArray(jsonObject.toString(), Map.class);
            if (mapList != null && mapList.size() > 0) {
                for (Map<String, String> map : mapList) {
                    ApplyDetail applyDetail = new ApplyDetail();
                    applyDetail.setNewPara(map.get("new"));
                    applyDetail.setOldPara(map.get("old"));
                    applyDetail.setPara(map.get("key"));
                    list.add(applyDetail);
                }
            }
        }
        return list;
    }

    /**
     * 新增模板
     * @param paraTemplateCreateRequest
     */
    public CreateParaTemplateResponse addParaTemplate(ParaTemplateCreateRequest paraTemplateCreateRequest) {
        CreateParaTemplateResponse response = new CreateParaTemplateResponse();
        Map<String, Object> map = new HashMap<>();
        if (paraTemplateCreateRequest != null) {
            if ("mysql".equals(paraTemplateCreateRequest.getDbType())) {
                paraTemplateCreateRequest.setDbType("MySQL");
            }
            map.put("db_type", paraTemplateCreateRequest.getDbType());
            map.put("db_version", paraTemplateCreateRequest.getDbVersion());
            map.put("name", paraTemplateCreateRequest.getName());
            map.put("desc", paraTemplateCreateRequest.getDesc());
            map.put("parameters", paraTemplateCreateRequest.getParameters());
        }
        RDSClient rdsClient = rdsClientFactory.createRdsClient();
        JSONObject jsonObject = rdsClient.addParaTemplate(map);
        if (jsonObject != null) {
            CreateParaTemplateResponse.Data data = JSON.parseObject(jsonObject.getJSONObject("data").
                    getJSONObject("tplInfo").toJSONString(), CreateParaTemplateResponse.Data.class);
            response.setData(data);
        }
        return response;
    }


    /**
     * 模板详情
     * @param templateId
     * @return
     */
    public ParaTemplateModel getTemplateDetail(String templateId) {
        RDSClient rdsClient = rdsClientFactory.createRdsClient();
        JSONObject jsonObject = rdsClient.getParaTemplateDetail(templateId);

        List<ParaTemplateDetail> paraTemplateDetailList = new ArrayList<>();
        ParaTemplateModel paraTemplateModel = new ParaTemplateModel();
        if (jsonObject != null) {
            // Map<String, String> map = JSONObject.parseObject(jsonObject.toString(), Map.class);
            paraTemplateDetailList =
                    JSON.parseArray(jsonObject.getJSONArray("parameters").toJSONString(), ParaTemplateDetail.class);
            paraTemplateModel = JSON.parseObject(jsonObject.toJSONString(), ParaTemplateModel.class);
            paraTemplateModel.setPara(paraTemplateDetailList);
//            paraTemplateModel.setDesc((String) map.get("desc"));
//            paraTemplateModel.setCreateTime((String) map.get("create_time"));
//            paraTemplateModel.setCount((Integer) map.get("count"));
//            paraTemplateModel.setDbVersion((String) map.get("db_version"));
//            paraTemplateModel.setDbType((String) map.get("db_type"));
//            paraTemplateModel.setName((String) map.get("name"));
        }
        return paraTemplateModel;
    }

    /**
     * 删除模板
     * @param templateId
     */
    public void deleteTemplate(String templateId) {
        RDSClient rdsClient = rdsClientFactory.createRdsClient();
        rdsClient.deleteTemplate(templateId);
    }

    /**
     * 修改模板
     * @param templateId
     * @param updateRequestList
     */
    public void updateTemplate(String templateId, String name, List<ParaTemplateUpdateRequest> updateRequestList) {

        List<Map<String, String>> mapList = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();

        if (updateRequestList != null && updateRequestList.size() > 0) {
            for (ParaTemplateUpdateRequest updateRequest : updateRequestList) {
                Map<String, String> map = new HashMap<>();
                map.put("key", updateRequest.getKey());
                map.put("new", updateRequest.getNewValue());
                map.put("old", updateRequest.getOldValue());
                mapList.add(map);
            }
        }
        if (StringUtils.isNotEmpty(name)) {
            jsonObject.put("name", name);
        }
        jsonObject.put("parameters", mapList);
        RDSClient rdsClient = rdsClientFactory.createRdsClient();
        rdsClient.updateTemplate(templateId, jsonObject);
    }


    /**
     * 应用模板
     * @param templateId
     * @param request
     */
    public Map<String,List<ParaTemplateCompare>> applyTemplate(
            final String templateId, final ParaTemplateCompareRequest request) throws Throwable {
        final RDSClient rdsClient = rdsClientFactory.createRdsClient();
        Map<String, Future<List<ParaTemplateCompare>>> instanceIdFutureMap =
                new HashMap<>();
        final Token token = LogicUserService.getSubjectToken();
        final String region = EndpointManager.getThreadRegion();
        final ParaTemplateModel templateDetail = this.getTemplateDetail(templateId);
        final String requestId = BceInternalRequest.getThreadRequestId();
        for (final String instanceId : request.getInstanceIds()){
            Future<List<ParaTemplateCompare>> future = executor.submit(new Callable<List<ParaTemplateCompare>>() {
                @Override
                public List<ParaTemplateCompare> call() throws Exception {
                    LogicUserService.setSubjectToken(token);
                    EndpointManager.setThreadRegion(region);
                    BceInternalRequest.setThreadRequestId(requestId);
                    try {
                        rdsClient.applyTemplate(templateId, instanceId,
                                request.getEffectiveTime(), request.getSwitchover());
                        return compareParaTemplate(templateId, instanceId, templateDetail);
                    } finally {
                        LogicUserService.removeSubjectToken();
                        EndpointManager.removeThreadRegion();
                        BceInternalRequest.removeThreadRequestId();
                    }
                }
            });
            instanceIdFutureMap.put(instanceId, future);
        }
        Map<String,List<ParaTemplateCompare>> paraTemplateCompareMap = new HashMap<>();
        for (Map.Entry<String, Future<List<ParaTemplateCompare>>> entry : instanceIdFutureMap.entrySet()) {
            String instanceId = entry.getKey();
            Future<List<ParaTemplateCompare>> future = entry.getValue();
            try {
                paraTemplateCompareMap.put(instanceId, future.get());
            } catch (InterruptedException e) {
                LOGGER.error(String.format("Get ParaTemplateCompare instanceId: %s fail.", instanceId), e);
            } catch (ExecutionException e) {
                Throwable t = e.getCause();
                if (t instanceof BceInternalResponseException) {
                    throw t;
                }
                LOGGER.error("Throwable t : " , t);
            }
        }


        return paraTemplateCompareMap;
    }


    /**
     * 应用模板下拉实例列表
     * @param engine
     * @param engineVersion
     * @return
     */
    public List<ApplyInstance> listApplyInstance(String engine, String engineVersion, String instanceType) {

        List<InstanceAbstract> instances = instanceService.listInstanceByExpiredDate(-1,
                "instanceName", "desc", null,
                null, RDSConstant.FROM_CONSOLE);
        instances = (List<InstanceAbstract>) instanceForApiUtils.standardInstanceAbstractList(instances,
                RDSConstant.FROM_CONSOLE, null, Boolean.TRUE);

        List<ApplyInstance> applyInstanceList = new ArrayList<>();
        if (instances != null && instances.size() > 0) {
            for (InstanceAbstract instance : instances) {
                if (("available".equals(instance.getInstanceStatus()) && engine.equals(instance.getEngine())
                        && engineVersion.equals(instance.getEngineVersion()))) {
                    if (StringUtils.isNotEmpty(instanceType)) {
                         if (instanceType.equalsIgnoreCase(instance.getInstanceType())) {
                             // 只有当新增的入参不为空 且匹配到具体值时，才将其加入返回值中
                             ApplyInstance applyInstance = new ApplyInstance();
                             applyInstance.setShortId(instance.getInstanceShortId());
                             applyInstance.setInstanceId(instance.getInstanceId());
                             applyInstance.setEngine(instance.getEngine());
                             applyInstance.setEngineVersion(instance.getEngineVersion());
                             applyInstance.setInstanceName(instance.getInstanceName());
                             applyInstance.setInstanceCreateTime(instance.getInstanceCreateTime());
                             applyInstance.setApplicationType(instance.getApplicationType());
                             applyInstance.setInstanceStatus(instance.getInstanceStatus());
                             applyInstance.setAzone(instance.getAzone());
                             applyInstance.setRegion(instance.getRegion());
                             applyInstance.setInstanceType(instance.getInstanceType());
                             applyInstance.setSourceInstanceId(instance.getSourceInstanceId());
                             applyInstanceList.add(applyInstance);
                         } else {
                             LOGGER.debug("apply param tpl instance is not match instanceType");
                             continue;
                         }
                    } else {
                        // 维持原逻辑
                        ApplyInstance applyInstance = new ApplyInstance();
                        applyInstance.setShortId(instance.getInstanceShortId());
                        applyInstance.setInstanceId(instance.getInstanceId());
                        applyInstance.setEngine(instance.getEngine());
                        applyInstance.setEngineVersion(instance.getEngineVersion());
                        applyInstance.setInstanceName(instance.getInstanceName());
                        applyInstance.setInstanceCreateTime(instance.getInstanceCreateTime());
                        applyInstance.setApplicationType(instance.getApplicationType());
                        applyInstance.setInstanceStatus(instance.getInstanceStatus());
                        applyInstance.setAzone(instance.getAzone());
                        applyInstance.setRegion(instance.getRegion());
                        applyInstance.setInstanceType(instance.getInstanceType());
                        applyInstance.setSourceInstanceId(instance.getSourceInstanceId());
                        applyInstanceList.add(applyInstance);
                    }
                }
            }
        }
        return applyInstanceList;
    }

    public List<ParaTemplateCompare> compareParaTemplate(String templateId, String instanceId) {
        ParaTemplateModel templateDetail = this.getTemplateDetail(templateId);
        return compareParaTemplate(templateId, instanceId, templateDetail);
    }


    /**
     * 模板参数比较
     * @param templateId
     * @param instanceId
     * @return
     */
    public List<ParaTemplateCompare> compareParaTemplate(String templateId, String instanceId,
                                                         ParaTemplateModel templateDetail) {

        List<ParaTemplateCompare> paraTemplateCompareList = new ArrayList<>();

        ConfigList response = argumentService.list(instanceId, null, null);
        if (response != null && !CollectionUtils.isEmpty(response.getItems())
                && templateDetail != null && !CollectionUtils.isEmpty(templateDetail.getPara())) {
            for (ConfigItem item : response.getItems()) {
                ParaTemplateCompare paraTemplateCompare = new ParaTemplateCompare();
                BeanUtils.copyProperties(item, paraTemplateCompare);
                for (ParaTemplateDetail paraTemplateDetail : templateDetail.getPara()) {
                    if (item.getName().equals(paraTemplateDetail.getKey())) {
                        paraTemplateCompare.setPostValue(paraTemplateDetail.getValue());
                        paraTemplateCompare.setChange(false);
                        if ("set<string>".equals(item.getType())) {
                            List<String> itemValueList = Arrays.asList(item.getValue().split(","));
                            List<String> templateValueList
                                    = Arrays.asList(paraTemplateDetail.getValue().split(","));
                            Collections.sort(itemValueList);
                            Collections.sort(templateValueList);
                            if (!itemValueList.equals(templateValueList)) {
                                paraTemplateCompare.setChange(true);
                            }
                        } else if (!item.getValue().equals(paraTemplateDetail.getValue())) {
                            paraTemplateCompare.setChange(true);
                        }
                        continue;
                    }
                }
                paraTemplateCompareList.add(paraTemplateCompare);
            }
        }
        return paraTemplateCompareList;
    }

    public List<LinkedHashMap> listDatebaseParameters(String dbType, String dbVersion, String templateId) {
        RDSClient rdsClient = rdsClientFactory.createRdsClient();

        List<LinkedHashMap> linkedHashMaps = rdsClient.listDatebaseParameters(dbType, dbVersion, templateId);

        if (StringUtils.isNotEmpty(templateId)) {
            JSONObject jsonObject = rdsClient.getParaTemplateDetail(templateId);
            List<ParaTemplateDetail> paraTemplateDetailList = new ArrayList<>();
            if (jsonObject != null) {
                paraTemplateDetailList =
                        JSON.parseArray(jsonObject.getJSONArray("parameters").toJSONString(), ParaTemplateDetail.class);
            }
            Iterator<LinkedHashMap> iterator = linkedHashMaps.iterator();
            while (iterator.hasNext()) {
                LinkedHashMap hashMap = iterator.next();
                for (ParaTemplateDetail paraTemplateDetail : paraTemplateDetailList) {
                    if (hashMap.get("name").equals(paraTemplateDetail.getKey())) {
                        iterator.remove();
                        continue;
                    }
                }
            }

        }
        return linkedHashMaps;
    }

    public void duplicateTemplate(DuplicateTempRequest request) {
        RDSClient rdsClient = rdsClientFactory.createRdsClient();
        rdsClient.duplicateTemplate(request.getTemplateId(), request);
    }
}
