package com.baidu.bce.logic.rds.service.interceptor;

import com.baidu.bce.internalsdk.iam.model.SignatureValidator;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 为了修复安全漏洞：http://wiki.baidu.com/pages/viewpage.action?pageId=1416450397
 */
@Component
public class SecurityTokenInterceptor extends HandlerInterceptorAdapter {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        RdsRequestParam.setThreadSecurityToken(request.getHeader(SignatureValidator.SECURITY_TOKEN_HEADER));
        return true;
    }
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        RdsRequestParam.removeThreadSecurityToken();
    }
}