package com.baidu.bce.logic.rds.service;

import com.baidu.bce.fbi.common.exception.FbiException;
import com.baidu.bce.finance.cashier.api.CashierService;
import com.baidu.bce.finance.cashier.api.request.BceApiPayOrderRequest;
import com.baidu.bce.finance.cashier.api.response.BcePayOrderResult;
import com.baidu.bce.finance.cashier.api.type.BcePayBusinessStatus;
import com.baidu.bce.finance.cashier.client.CashierClientFactory;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.order.OrderBatchClient;
import com.baidu.bce.internalsdk.order.OrderClientV2;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.AdvancedOrderFilter;
import com.baidu.bce.internalsdk.order.model.BatchResizeRequest;
import com.baidu.bce.internalsdk.order.model.CancelOrderRequest;
import com.baidu.bce.internalsdk.order.model.Flavor;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderResizeRequest;
import com.baidu.bce.internalsdk.order.model.OrderResizeRequestItem;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.OrderType;
import com.baidu.bce.internalsdk.order.model.ProductPayType;
import com.baidu.bce.internalsdk.order.model.ResizeRequest;
import com.baidu.bce.internalsdk.order.model.ResizeType;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.order.model.TimeUnit;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceUpdateFlavorRequest;
import com.baidu.bce.internalsdk.rds.model.instance.OrderItemExtraInfo;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.internalsdk.zone.ZoneClient;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.core.constants.Payment;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.core.utils.BeanCopyUtil;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.BatchOrderSyncPO;
import com.baidu.bce.logic.rds.dao.model.FlavorPO;
import com.baidu.bce.logic.rds.dao.mybatis.BatchOrderSyncMapper;
import com.baidu.bce.logic.rds.dao.mybatis.FlavorMapper;
import com.baidu.bce.logic.rds.service.constant.EngineType;
import com.baidu.bce.logic.rds.service.constant.InstanceType;
import com.baidu.bce.logic.rds.service.constant.RdsInstanceStatus;
import com.baidu.bce.logic.rds.service.datasync.NewOrderDataSyncScheduler;
import com.baidu.bce.logic.rds.service.exception.BackendExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessException;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.exception.handler.LogicRdsExceptionHandler;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.instance.InstanceCreateModel;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import com.baidu.bce.logic.rds.service.model.order.ApiRenewModel;
import com.baidu.bce.logic.rds.service.model.order.CancelToPostpayOrderRequest;
import com.baidu.bce.logic.rds.service.model.order.RdsCreateOrderRequestVo;
import com.baidu.bce.logic.rds.service.model.order.RenewModel;
import com.baidu.bce.logic.rds.service.model.pricing.Price;
import com.baidu.bce.logic.rds.service.model.pricing.PriceDiffModel;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.LogicRdsPaymentHandle;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.logic.rds.service.util.RdsFlavorManager;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.plat.servicecatalog.model.order.BaseCreateOrderRequestVo;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.FlavorItem;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.OrdersResult;
import com.baidu.bce.plat.servicecatalog.model.order.PaymentModel;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateRenewTypeOrderItem;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateResizeTypeOrderItem;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateToPostpayTypeOrderItem;
import com.baidubce.util.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import endpoint.EndpointManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.regex.Pattern;

/**
 * Created by luping03 on 17/7/25.
 */
@Service("logicRdsOrderService")
public class RdsOrderService {

    private static final Logger logger = LoggerFactory.getLogger(RdsOrderService.class);
    public static final int RESOURCE_NUM = 100000;

    @Value("${rds.autoRenew.url:}")
    private String autoRenewUrl;

    @Autowired
    LogicRdsClientFactory rdsClientFactory;


    @Autowired
    private NewOrderDataSyncScheduler orderDataSyncScheduler;

    @Autowired
    QuotaValidatorFactory quotaValidatorFactory;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private OthersService othersService;

    @Autowired
    private IdMapperService idMapperService;

    @Value("${rds.storage.limit.zones:AZONE-bjyz,AZONE-gzhxy}")
    String storageLimitedZones;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Value("${rds.service.accountId:}")
    String rdsServiceAccountId;

    @Value("${rds.service.policyId:}")
    String rdsServicePolicyId;

    @Value("${console.rds.service.accountId:}")
    String consoleRdsServiceAccountId;

    @Value("${console.rds.service.policyId:}")
    String consoleRdsServicePolicyId;

    @Autowired
    FlavorMapper flavorMapper;

    @Autowired
    private InstanceDao instanceDao;

    @Value("#{'${rds.financial.region:fsh}'.split(',')}")
    private String[] financialRegion;

    @Autowired
    private RdsIamService rdsIamService;

    @Value("${env.service:}")
    private String env;

    @Autowired
    private PricingService pricingService;

    @Autowired
    private RecyclerService recyclerService;

    @Autowired
    private CashierClientFactory cashierClientFactory;

    @Autowired
    private BatchOrderSyncMapper batchOrderSyncMapper;


    public CashierService getCashierClient() {
        String sericeName = "Cashier";
        StsCredential stsCredetial = rdsClientFactory.getAccountStsAccessKey();
        CashierClientFactory cashierClientFactory = new CashierClientFactory();
        CashierService cashierClient = cashierClientFactory.createCashierClient(EndpointManager.getEndpoint(sericeName),
               stsCredetial.getAccessKeyId(), stsCredetial.getSecretAccessKey(), stsCredetial.getSessionToken() );
        return cashierClient;
    }

    public OrderUuidResult createToPostpayOrder(RdsCreateOrderRequestVo<RenewModel> request) {
        Map<String, String> filterMap = new HashMap<>();
        filterMap.put("productType", "prepay");
        if (rdsClientFactory.getInstanceTypeByUuid(request.getItems().get(0).getConfig().getInstanceId())
                .equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            filterMap.put("instanceType", "financial");
        }

        List<InstanceAbstract> instanceAbstracts = instanceService
                .listInstanceByExpiredDate(-1, null, null, filterMap, null, RDSConstant.FROM_CONSOLE);
        // 之前的逻辑 根本获取不到实例的自动续费信息，补充
        instanceService.bindAutoRenew(instanceAbstracts);
        Map<String, InstanceAbstract> instanceMap = new HashMap<>();
        for (InstanceAbstract instance : instanceAbstracts) {
            instanceMap.put(instance.getInstanceId(), instance);
        }

        List<CreateToPostpayTypeOrderItem> createRenewTypeOrderItems = new LinkedList();
        for (RdsCreateOrderRequestVo.Item<RenewModel> item : request.getItems()) {
            RenewModel config = item.getConfig();

            checkToPostpayStat(instanceMap.get(config.getInstanceId()));

            Resource resource = instanceService.getResourceByInstanceId(config.getInstanceId());

            if (resource == null) {
                throw new RDSExceptions.ResourceNotExistException();
            }

            CreateToPostpayTypeOrderItem createToPostpayTypeOrderItem = new CreateToPostpayTypeOrderItem();
            createToPostpayTypeOrderItem.setServiceType(resource.getServiceType());
            createToPostpayTypeOrderItem.setProductType(ProductPayType.POST_PAY.alias);
            createToPostpayTypeOrderItem.setPaymentMethod(item.getPaymentMethod());
            createToPostpayTypeOrderItem.setResourceUuid(instanceMap.get(config.getInstanceId()).getResourceUuid());
            if (instanceMap.get(config.getInstanceId()).getInstanceType()
                    .equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
                createToPostpayTypeOrderItem.setExtra(RDSConstant.INSTANCE_TYPE_RAFT);
            }

            createRenewTypeOrderItems.add(createToPostpayTypeOrderItem);
        }

        CreateOrderRequest createOrderRequest = new CreateOrderRequest();
        createOrderRequest.setOrderType(OrderType.TO_POSTPAY.name());
        createOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
        createOrderRequest.setPaymentMethod(request.getPaymentMethod());
        createOrderRequest.setItems(createRenewTypeOrderItems);

        OrdersResult ordersResult = rdsClientFactory.createServiceCatalogOrderClient()
                .batchCreateOrder(createOrderRequest);

        if (ordersResult == null || CollectionUtils.isEmpty(ordersResult.getOrders())) {
            throw new RDSExceptions.CreateToPrepostOrderExcption();
        }
        StringBuilder orderStrBuilder = new StringBuilder("");
        for (Order order : ordersResult.getOrders()) {
            orderStrBuilder.append(order.getUuid()).append(",");
        }
        orderStrBuilder.deleteCharAt(orderStrBuilder.length() - 1);
        logger.info("createToPostPayOrder,orderIds:{}", orderStrBuilder.toString());
        OrderUuidResult orderUuidResult = new OrderUuidResult(orderStrBuilder.toString());
        return orderUuidResult;
    }

    /**
     * 检查是否满足预转后的各个条件
     * 预付费、未到期、未开通自动续费、未开通计费变更(to_postpay|to_prepay)
     * @param instanceAbstract
     */
    private void checkToPostpayStat(InstanceAbstract instanceAbstract) {
        if (instanceAbstract == null) {
            throw new RDSExceptions.ResourceNotExistException();
        }
        if (new Date().after(instanceAbstract.getInstanceExpireTime())) {
            throw new RDSExceptions.InstanceExpiredException();
        }
        if (StringUtils.isNotBlank(instanceAbstract.getOrderStatus())) {
            throw new RDSExceptions.HasChangeBillingException();
        }
        if (RDSConstant.AUTO_RENEW_TASK.equalsIgnoreCase(instanceAbstract.getTask())) {
            throw new RDSBusinessExceptions.AutoRenewToPostPayException(instanceAbstract.getInstanceId());
        }
    }

    public Map<String, Order> getOrderMapByFilters(List<OrderStatus> statusList, List<OrderType> typeList) {
        Map<String, Order> orderMap = new HashMap<>();
        Collection<Order> orders = getOrdersByFilters(statusList, typeList);
        if (CollectionUtils.isEmpty(orders)) {
            return orderMap;
        }
        List<String> tempResourceIds = null;
        for (Order order : orders) {
            tempResourceIds = order.getResourceIds();
            if (CollectionUtils.isEmpty(tempResourceIds)) {
                continue;
            }
            for (String resourceId : tempResourceIds) {
                if (StringUtils.isNotBlank(resourceId)) {
                    orderMap.put(resourceId, order);
                }
            }
        }
        return orderMap;
    }

    public Collection<Order> getOrdersByFilters(List<OrderStatus> statusList, List<OrderType> typeList) {
        AdvancedOrderFilter advancedOrderFilter = new AdvancedOrderFilter();
        advancedOrderFilter.setAccountId(rdsClientFactory.getAccountId());
        advancedOrderFilter.setItemServiceType(ServiceType.RDS.toString());
        advancedOrderFilter.setStatusList(statusList);
        advancedOrderFilter.setOrderTypeList(typeList);
        advancedOrderFilter.setBegin(1);
        advancedOrderFilter.setLimit(RESOURCE_NUM);
        // 只读实例支持预付费类型。补充
        AdvancedOrderFilter advancedOrderFilterRDSReplica = new AdvancedOrderFilter();
        advancedOrderFilterRDSReplica.setAccountId(rdsClientFactory.getAccountId());
        advancedOrderFilterRDSReplica.setItemServiceType(ServiceType.RDS_REPLICA.toString());
        advancedOrderFilterRDSReplica.setStatusList(statusList);
        advancedOrderFilterRDSReplica.setOrderTypeList(typeList);
        advancedOrderFilterRDSReplica.setBegin(1);
        advancedOrderFilterRDSReplica.setLimit(RESOURCE_NUM);
        Collection<Order> orders = rdsClientFactory.getOrderClient().advancedList(advancedOrderFilter).getOrders();
        Collection<Order> ordersReplica = rdsClientFactory.getOrderClient().advancedList(advancedOrderFilterRDSReplica).getOrders();
        orders.addAll(ordersReplica);
        return orders;
    }

    public void cancelToPostpayOrder(CancelToPostpayOrderRequest request) {
        if (CollectionUtils.isEmpty(request.getInstanceIds())) {
            throw new RDSExceptions.ParamValidationException("请选择要操作的实例");
        }

        Map<String, String> filterMap = new HashMap<>();
        filterMap.put("productType", "prepay");

        // 根据instanceId查询数据库判断instanceType是否是financial，如果是filterMap中存储instanceType=financial
        if (rdsClientFactory.getInstanceTypeByUuid(request.getInstanceIds().get(0))
                .equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            filterMap.put("instanceType", "financial");
        }

        List<InstanceAbstract> instanceAbstracts = instanceService
                .listInstanceByExpiredDate(-1, null, null, filterMap, null, RDSConstant.FROM_CONSOLE);

        if (CollectionUtils.isEmpty(instanceAbstracts)) {
            throw new RDSExceptions.ResourceNotExistException();
        }
        Map<String, InstanceAbstract> instancesMap = new HashMap<>();
        for (InstanceAbstract instance : instanceAbstracts) {
            instancesMap.put(instance.getInstanceId(), instance);
        }
        // 获取 EIP TO_POSTPAY 和 TO_PREPAY 订单, 需要 orderId
        Map<String, Order> orderMap = getOrderMapByFilters(Arrays.asList(OrderStatus.DEFERRED_CREATE),
                Arrays.asList(OrderType.TO_POSTPAY));
        List<String> orderIds = new ArrayList<>();
        for (String instanceId : request.getInstanceIds()) {
            if (!instancesMap.containsKey(instanceId)) {
                throw new RDSExceptions.ResourceNotExistException();
            }
            InstanceAbstract instance = instancesMap.get(instanceId);
            if (!orderMap.containsKey(instance.getResourceUuid())) {
                throw new RDSExceptions.ResourceNotExistException();
            }
            if (!"to_postpay".equalsIgnoreCase(orderMap.get(instance.getResourceUuid()).getType().name())) {
                throw new RDSExceptions.NoChangeBillingException();
            }

            orderIds.add(orderMap.get(instance.getResourceUuid()).getUuid());
        }
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new RDSExceptions.ResourceNotExistException();
        }
        OrderClientV2 orderClientV2 = rdsClientFactory.createOrderClientV2();
        CancelOrderRequest cancelOrderRequest = new CancelOrderRequest();
        cancelOrderRequest.setOrderIds(orderIds);
        orderClientV2.cancel(cancelOrderRequest);
    }

    public OrderUuidResult createResizeOrder(RdsCreateOrderRequestVo<PriceDiffModel> request) {
        OrderUuidResult result;

        RdsCreateOrderRequestVo.Item<PriceDiffModel> item = request.getItems().get(0);
        PriceDiffModel config = item.getConfig();

        InstanceExtension instanceExtension = instanceService.getInstanceExtension(config.getInstanceId());
//        checkProductType(instanceExtension.getProductType());

        try {
            checkResizingMemoryAndStorage(config, instanceExtension);
        } catch (BceInternalResponseException ex) {
            LogicRdsExceptionHandler.handle(ex);
        }

        ResizeRequest resizeRequest = createResizeRequest(config);

        CreateResizeTypeOrderItem createResizeTypeOrderItem = new CreateResizeTypeOrderItem();
        createResizeTypeOrderItem.setServiceType(ServiceType.RDS.name());
        createResizeTypeOrderItem.setProductType(instanceExtension.getProductType());
        createResizeTypeOrderItem.setPaymentMethod(item.getPaymentMethod());

        createResizeTypeOrderItem.setExtra(resizeRequest.getExtra());
        Set<FlavorItem> flavorItems =
                BeanCopyUtil.copySetCollection(new HashSet<Object>(resizeRequest.getFlavor()));
        createResizeTypeOrderItem.setFlavor(flavorItems);
        createResizeTypeOrderItem.setResourceUuid(resizeRequest.getUuid());
        createResizeTypeOrderItem.setResizeType(resizeRequest.getResizeType());

        CreateOrderRequest createOrderRequest = new CreateOrderRequest();
        createOrderRequest.setOrderType(OrderType.RESIZE.name());
        createOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
        createOrderRequest.setPaymentMethod(request.getPaymentMethod());
        createOrderRequest.getItems().add(createResizeTypeOrderItem);

        // 增加是否直接付款
        if (Payment.isPrepay(instanceExtension.getProductType())) {
            if (config.getIsDirectPay()) {
                createOrderRequest.setIsDirectPay(config.getIsDirectPay());
            }
        }

        result = rdsClientFactory.createServiceCatalogOrderClient().createOrder(createOrderRequest);

        return result;
    }

    // 变配接口service层已被v1接口复用。此处为了兼容旧逻辑，新建立一个service服务用于v2接口的变配
    public OrderUuidResult createResizeOrder(RdsCreateOrderRequestVo<PriceDiffModel> request,
                                             InstanceExtension instanceExtension) {
        OrderUuidResult result;

        RdsCreateOrderRequestVo.Item<PriceDiffModel> item = request.getItems().get(0);
        PriceDiffModel config = item.getConfig();

        try {
            checkResizingMemoryAndStorage(config, instanceExtension);
        } catch (BceInternalResponseException ex) {
            LogicRdsExceptionHandler.handle(ex);
        }

        // 单机转双机的情况下，此处单独进行处理
        ResizeRequest resizeRequest;
        if (StringUtils.isNotEmpty(config.getCategory())
                && RDSConstant.APPLICATION_TYPE_NORMAL.equalsIgnoreCase(config.getCategory())
                && RDSConstant.APPLICATION_TYPE_SINGLE.equalsIgnoreCase(instanceExtension.getApplicationType())
                && RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instanceExtension.getEngine())) {
            // 先校验云磁盘类型 单机支持高性能云盘，双机支持增强型云盘
            checkDiskTypeOfSingleToNormal(config, instanceExtension);
            // 单机转双机的订单参数
            resizeRequest = createSingleToNormalResizeRequest(config, instanceExtension);
        } else {
            resizeRequest = createResizeRequest(config, instanceExtension);
        }

        CreateResizeTypeOrderItem createResizeTypeOrderItem = new CreateResizeTypeOrderItem();
        createResizeTypeOrderItem.setServiceType(ServiceType.RDS.name());
        createResizeTypeOrderItem.setProductType(instanceExtension.getProductType());
        createResizeTypeOrderItem.setPaymentMethod(item.getPaymentMethod());

        createResizeTypeOrderItem.setExtra(resizeRequest.getExtra());
        Set<FlavorItem> flavorItems =
                BeanCopyUtil.copySetCollection(new HashSet<Object>(resizeRequest.getFlavor()));
        createResizeTypeOrderItem.setFlavor(flavorItems);
        createResizeTypeOrderItem.setResourceUuid(resizeRequest.getUuid());
        createResizeTypeOrderItem.setResizeType(resizeRequest.getResizeType());

        CreateOrderRequest createOrderRequest = new CreateOrderRequest();
        createOrderRequest.setOrderType(OrderType.RESIZE.name());
        createOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
        createOrderRequest.setPaymentMethod(request.getPaymentMethod());
        createOrderRequest.getItems().add(createResizeTypeOrderItem);

        // 增加是否直接付款
        if (Payment.isPrepay(instanceExtension.getProductType())) {
            if (config.getIsDirectPay()) {
                createOrderRequest.setIsDirectPay(config.getIsDirectPay());
            }
        }

        result = rdsClientFactory.createServiceCatalogOrderClient().createOrder(createOrderRequest);

        return result;
    }

    /**
     * 单机转双机时，需要限制下云盘之间的转换，单机支持高性能云盘 双机支持增强型云盘、本地盘 TODO
     * @param config
     * @param instanceExtension
     */
    private void checkDiskTypeOfSingleToNormal(PriceDiffModel config, InstanceExtension instanceExtension) {
        if ((StringUtils.isEmpty(config.getDiskIoType()) && StringUtils.isNotEmpty(instanceExtension.getDiskIoType())
                && "cloud_high".equalsIgnoreCase(instanceExtension.getDiskIoType()))
                || (StringUtils.isNotEmpty(config.getDiskIoType()) && "cloud_high".
                equalsIgnoreCase(config.getDiskIoType()))) {
            throw new RDSBusinessExceptions.NotSupportDiskTypeException(instanceExtension.getDiskIoType());
        }
    }

    /**
     * 此方法用来处理单机转双机的订单参数
     * @param config 接口传参
     * @param instance 当前实例详情信息
     * @return
     */
    private ResizeRequest createSingleToNormalResizeRequest(PriceDiffModel config, InstanceExtension instance) {
        // 逻辑至此，可保证均为双机版实例，故可如此处理
        // 单机版只读转双机版只读暂不考虑 TODO
        String subServiceTypeFlavorItemValue = instance.getEngine();
        if (StringUtils.isNotEmpty(instance.getResourceType())
                && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType())) {
            // 通用型单机转双机
            subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
        } else {
            subServiceTypeFlavorItemValue = instance.getEngine();
        }
        // 此处旧逻辑有问题，当变配时磁盘类型发生变化，询价参数取的是当前实例的而非用户传值，更正
        // 兼容逻辑，为防止用户不传diskIoType（磁盘类型不变），可取资源中的参数
        String diskIoType = StringUtils.isNotEmpty(config.getDiskIoType()) ?
                config.getDiskIoType() : instance.getDiskIoType();
        ResizeRequest resizeRequest = new ResizeRequest();
        resizeRequest.setUuid(instance.getResourceUuid());
        if (config.getOldFlavor()){
            resizeRequest.setFlavor(new HashSet<>(new Flavor()
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("memory")
                            .withValue(config.getAllocatedMemoryInMB() + "m")
                            .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("subServiceType")
                            .withValue(subServiceTypeFlavorItemValue))));

            Set<Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
            if (ObjectUtils.equals("high_io", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 通用型ssd云磁盘
            } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else {
                flavors.add(new Flavor.FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            }

        } else {
            resizeRequest.setFlavor(new HashSet<>(new Flavor().addFlavorItem(new Flavor.FlavorItem()
                    .withName("cpu")
                    .withValue(config.getCpuCount() + "")
                    .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("memory")
                            .withValue(config.getAllocatedMemoryInGB() + "g")
                            .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("subServiceType")
                            .withValue(subServiceTypeFlavorItemValue))));

            Set<Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
            if (ObjectUtils.equals("high_io", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 通用型ssd云磁盘
            } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else {
                flavors.add(new Flavor.FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            }

        }
        if (ProductPayType.PRE_PAY.toString().equalsIgnoreCase(instance.getProductType())) {
            int resizeType = 0;
            Price priceResponse =
                    pricingService.getPriceDiff(config);
            if (priceResponse.getPrice().doubleValue() < 0) {
                resizeType = 1;
            }
            resizeRequest.setResizeType(resizeType);
        }
        try {
            resizeRequest.setExtra(new ObjectMapper().writeValueAsString(creatUpdateFlavorRequest(config, instance)));
        } catch (JsonProcessingException e) {
            throw new RDSExceptions.OrderPreparedException();
        }
        resizeRequest.setUserId(rdsClientFactory.getAccountId());
        return resizeRequest;
    }

    /**
     * 此方法用来创建批量变配的订单
     * @param request
     * @param instanceList
     * @return
     */
    public List<Order> createBatchResizeOrder(RdsCreateOrderRequestVo<PriceDiffModel> request,
                                              List<InstanceExtension> instanceList, String from) {

        // 将请求中的订单项取出并存储在数组中
        List<OrderResizeRequest> orderResizeRequestList = new ArrayList<>();
        for (int i = 0 ; i < request.getItems().size(); i++) {
            // 将请求中的订单项逐个取出
            RdsCreateOrderRequestVo.Item<PriceDiffModel> item = request.getItems().get(i);
            PriceDiffModel config = item.getConfig();
            try {
                // 理论上来说 都是顺序存储的
                checkBatchResizingMemoryAndStorage(config, instanceList.get(i));
            } catch (BceInternalResponseException ex) {
                LogicRdsExceptionHandler.handle(ex);
            }

            // 此处多传入的 request 是为了给批量变配的订单打标记
            ResizeRequest resizeRequest = createResizeRequest(config, instanceList.get(i), request);

            OrderResizeRequestItem createResizeTypeOrderItem = new OrderResizeRequestItem();
            createResizeTypeOrderItem.setUuid(resizeRequest.getUuid());
            // 此处需添加批量变配的订单标识
            createResizeTypeOrderItem.setExtra(resizeRequest.getExtra());
            Set<Flavor.FlavorItem> flavorItems =
                    BeanCopyUtil.copySetCollection(new HashSet<Object>(resizeRequest.getFlavor()));
            createResizeTypeOrderItem.setFlavor(new ArrayList<>(flavorItems));

            // 组合批量创建订单时的请求体
            OrderResizeRequest orderResizeRequest = new OrderResizeRequest();
            // 初始化数组大小
            orderResizeRequest.setItems(new ArrayList<OrderResizeRequestItem>(1));
            orderResizeRequest.getItems().add(createResizeTypeOrderItem);

            // 将请求体的值存储至数组中，供后续批量创建接口调用
            orderResizeRequestList.add(orderResizeRequest);
        }

        // 至此，批量创建请求中的值已全部取出
        BatchResizeRequest batchResizeRequest = new BatchResizeRequest();
        batchResizeRequest.setUserId(LogicUserService.getUserId());
        // 此处应考虑下批量订单的传值类型
        batchResizeRequest.setResizeType(ResizeType.AUTO_RESIZE);
        batchResizeRequest.setResizeList(orderResizeRequestList);
        // 设置clientToken，防止超时
        String clientToken = "combine-order-rds-" + System.nanoTime();
        batchResizeRequest.setClientToken(clientToken);
        logger.debug("batchResizeRequest is : {}", JsonUtils.toJsonString(batchResizeRequest));
        OrderBatchClient orderBatchClient = rdsClientFactory.createOrderBatchClient();
        List<Order> result = orderBatchClient.batchResizeCreateMultiOrders(batchResizeRequest);
        // 先把预付费类型订单拿出来 只读支持预付费，可能有多个预付费订单
//        Order prePayOrder = null;
        List<Order> prePayOrders = new ArrayList<>();
        StringBuilder orderIds = new StringBuilder();
        for (Order order : result) {
            if (Payment.isPrepay(order.getProductType())) {
                prePayOrders.add(order);
            }
            orderIds.append(order.getUuid()).append(",");
        }
        orderIds.deleteCharAt(orderIds.length() - 1);

        // 订单存放至批量订单表中
        List<BatchOrderSyncPO> batchOrderSyncPOList = new ArrayList<>();
        ResourceClient resourceClient = rdsClientFactory.createResourceClient();
        for (Order order : result) {
            String batchInstanceIds = getBatchFlagFromResieOrder(order);
            // 订单信息ORM
            BatchOrderSyncPO batchOrderSyncPO = new BatchOrderSyncPO();
            // 根据资源反查实例 ID
            String instanceId = resourceClient.get(order.getResourceIds().get(0)).getName();
            batchOrderSyncPO.setInstanceId(instanceId);
            // 类型强转失败，借助构造函数
            Date createTime = order.getCreateTime();
            // UTC时间转为正常的时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
            Timestamp timestamp = new Timestamp(createTime.getTime());
            String formatTime = sdf.format(timestamp);
            batchOrderSyncPO.setCreateTime(Timestamp.valueOf(formatTime));
            batchOrderSyncPO.setExtraOrders(batchInstanceIds);
            batchOrderSyncPO.setUserId(order.getUserId());
            batchOrderSyncPO.setOrderUuid(order.getUuid());
            batchOrderSyncPO.setOrderStatus(order.getStatus().name());
            batchOrderSyncPO.setOrderType(order.getProductType());
            batchOrderSyncPO.setBatchOrders(String.valueOf(orderIds));
            batchOrderSyncPOList.add(batchOrderSyncPO);
        }

        try {
            batchOrderSyncMapper.insertBatchOrders(batchOrderSyncPOList);
        }catch (DuplicateKeyException e) {
            // 由于主从延迟，这里可能存在主库写入，从库读不到的情况
            logger.error("Occur DuplicateKeyException");
        }
        // 增加是否直接付款，批量变配存在预付费实例时，需要console 通过 API 支付
        for (Order prePayOrder : prePayOrders) {
            if (prePayOrder != null && prePayOrder.getStatus().equals(OrderStatus.NEED_PURCHASE)) {
                logger.debug("need purchase order : {}", prePayOrder.getUuid());
                // 创建 API 支付客户端工具
                CashierService cashierService = getCashierClient();
                if (request.getItems().get(0).getConfig().getIsDirectPay()) {
                    // 此时需要通过 API 支付。若使用方不想通过手动传入代金券且还走代金券支付的逻辑（单个变配的逻辑） TODO
                    // 1.若用户想通过代金券，则获取其请求参数中必传的代金券 ID
                    List<Long> couponId = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(request.getItems().get(0).getConfig().getCouponId())) {
                        couponId = request.getItems().get(0).getConfig().getCouponId();
                    }
                    BceApiPayOrderRequest apiPayOrderRequest = new BceApiPayOrderRequest();
                    if (prePayOrder != null) {
                        apiPayOrderRequest.setOrderId(prePayOrder.getUuid());
                    }
                    apiPayOrderRequest.setPayUserId(LogicUserService.getAccountId());
                    if (CollectionUtils.isNotEmpty(couponId)) {
                        apiPayOrderRequest.setPayItems(new ArrayList<BceApiPayOrderRequest.ApiPayItem>(couponId.size()));
                        // 此处由于支付接口暂不支持设置多个代金券，故只取传入的第一个值，TODO
                        for (int i = 0; i < 1; i++) {
                            apiPayOrderRequest.getPayItems().add(new BceApiPayOrderRequest.ApiPayItem());
                            apiPayOrderRequest.getPayItems().get(i).setOrderItemKey(prePayOrder.getItems().get(0).getKey());
                            apiPayOrderRequest.getPayItems().get(i).setCouponId(couponId.get(i));
                        }

                    } else {
                        apiPayOrderRequest.setPayItems(new ArrayList<BceApiPayOrderRequest.ApiPayItem>());
                    }
                    try {
                        BcePayOrderResult bcePayOrderResult = cashierService.apiPayment(apiPayOrderRequest);
                        // 接口除网络服务情况下不会抛出异常，故此处需兼容处理下异常情况
                        if (BcePayBusinessStatus.FAILED.equals(bcePayOrderResult.getStatus())) {
                            logger.error("api pay order error.");
                            // 针对来自 api的请求，正常报错；来自控制台的请求，返回订单号供前端跳转
                            if (RDSConstant.FROM_CONSOLE.equalsIgnoreCase(from)) {
                                return result;
                            } else {
                                // 触发订单检查任务
                                orderDataSyncScheduler.doCancelBatchOrderJob();
                                throw new RDSBusinessExceptions.ApiDirectPayException();
                            }


                        }
                    } catch (FbiException e) {
                        e.printStackTrace();
                        throw new RDSBusinessExceptions.ApiDirectPayException();
                    }

                }
            }
        }


        return result;
    }

    /**
     * 此方法用于在订单中取出相关标识
     * @param order
     * @return
     */
    private String getBatchFlagFromResieOrder(Order order) {
        String batchInstanceIds = "";
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            batchInstanceIds = objectMapper.readValue(order.getItems().get(0).getExtra(),
                    InstanceUpdateFlavorRequest.class).getBatchInstances();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return batchInstanceIds;
    }


    private ResizeRequest createResizeRequest(PriceDiffModel config) {
        InstanceExtension instance = instanceService.detail(config.getInstanceId(), null);
        String mutiEngine = "";
//        if (instance.getEngineVersion()
//                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012)) { // 处理sqlserver 2012单机版
//            mutiEngine = instance.getEngine() + "_singleton";
        if (instance.getEngineVersion()
                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012)
                || (instance.getEngineVersion()
                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016) &&
                instance.getApplicationType().equals(RDSConstant.APPLICATION_TYPE_SINGLE))) { // sqlserver 新增2016单机版
            mutiEngine = instance.getEngine() + "_singleton";
        } else if (config.getIsEnhanced() != null && config.getIsEnhanced()) { // 处理mysql三节点增强版
            mutiEngine = instance.getEngine().toLowerCase() + "_finance";
        } else if (instance.getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)
                && StringUtils.isNotEmpty(instance.getCategory())
                && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // 单机版的情况
            mutiEngine = instance.getEngine() + "_singleton";
        } else if (instance.getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PG)
                && StringUtils.isNotEmpty(instance.getCategory())
                && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // pg单机版情况
            mutiEngine = instance.getEngine() + "_singleton";
        } else {
            mutiEngine = instance.getEngine();
        }
        String subServiceTypeFlavorItemValue = StringUtils.isEmpty(instance.getSourceInstanceId())
                ? mutiEngine : "default";

        ResizeRequest resizeRequest = new ResizeRequest();
        resizeRequest.setUuid(instance.getResourceUuid());
        if (InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getInstanceType())) {
            resizeRequest.setFlavor(new HashSet<>(new Flavor()
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("nodeAmount")
                            .withValue(config.getNodeAmount().toString()))));
        } else if(config.getOldFlavor()){
            resizeRequest.setFlavor(new HashSet<>(new Flavor()
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("memory")
                            .withValue(config.getAllocatedMemoryInMB() + "m")
                            .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("subServiceType")
                            .withValue(subServiceTypeFlavorItemValue))));

            Set<Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
            if (ObjectUtils.equals("high_io", instance.getDiskIoType())) {
                flavors.add(new Flavor.FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 通用型ssd云磁盘
            } else if (ObjectUtils.equals("cloud_nor", instance.getDiskIoType())) {
                flavors.add(new Flavor.FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", instance.getDiskIoType())) {
                flavors.add(new Flavor.FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else if (ObjectUtils.equals("cloud_enha", config.getDiskIoType())) {
                flavors.add(new Flavor.FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else {
                flavors.add(new Flavor.FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            }

        } else {
            resizeRequest.setFlavor(new HashSet<>(new Flavor().addFlavorItem(new Flavor.FlavorItem()
                    .withName("cpu")
                    .withValue(config.getCpuCount() + "")
                    .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("memory")
                            .withValue(config.getAllocatedMemoryInGB() + "g")
                            .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("subServiceType")
                            .withValue(subServiceTypeFlavorItemValue))));

            Set<Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
            if (ObjectUtils.equals("high_io", instance.getDiskIoType())) {
                flavors.add(new Flavor.FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 通用型ssd云磁盘
            } else if (ObjectUtils.equals("cloud_nor", instance.getDiskIoType())) {
                flavors.add(new Flavor.FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", instance.getDiskIoType())) {
                flavors.add(new Flavor.FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else if (ObjectUtils.equals("cloud_enha", config.getDiskIoType())) {
                flavors.add(new Flavor.FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else {
                flavors.add(new Flavor.FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            }

        }
        if (ProductPayType.PRE_PAY.toString().equalsIgnoreCase(instance.getProductType())) {
            int resizeType = 0;
            Price priceResponse =
                    pricingService.getPriceDiff(config);
            if (priceResponse.getPrice().doubleValue() < 0) {
                resizeType = 1;
            }
            resizeRequest.setResizeType(resizeType);
        }
        try {
            resizeRequest.setExtra(new ObjectMapper().writeValueAsString(creatUpdateFlavorRequest(config, instance)));
        } catch (JsonProcessingException e) {
            throw new RDSExceptions.OrderPreparedException();
        }
        resizeRequest.setUserId(rdsClientFactory.getAccountId());
        return resizeRequest;
    }

    private ResizeRequest createResizeRequest(PriceDiffModel config, InstanceExtension instance) {
        String mutiEngine = "";
        if (StringUtils.isNotEmpty(instance.getResourceType()) &&
                RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType()) &&
                StringUtils.isNotEmpty(instance.getEngine()) &&
                RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instance.getEngine())) {
            if ((StringUtils.isNotEmpty(instance.getCategory())  // 单机版的情况
                    && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON))) {
                // 此处单机版价格暂无，默认与原单机版保持一致，TODO
                mutiEngine = instance.getEngine() + "_singleton";
            } else {
                mutiEngine = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
            }
        } else {
            if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER)
                    && instance.getApplicationType().equals(RDSConstant.APPLICATION_TYPE_SINGLE)) { // 单机版
                mutiEngine = instance.getEngine() + "_singleton";
            } else if (config.getIsEnhanced() != null && config.getIsEnhanced()) { // 处理mysql三节点增强版
                mutiEngine = instance.getEngine().toLowerCase() + "_finance";
            } else if (instance.getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)
                    && StringUtils.isNotEmpty(instance.getCategory())
                    && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // 单机版的情况
                mutiEngine = instance.getEngine() + "_singleton";
            } else if (instance.getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PG)
                    && StringUtils.isNotEmpty(instance.getCategory())
                    && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // pg单机版情况
                mutiEngine = instance.getEngine() + "_singleton";
            } else {
                mutiEngine = instance.getEngine();
            }
        }
        // 新增高可用只读实例，此处需兼容高可用只读计费项
        String subServiceTypeFlavorItemValue = "default";
        if (StringUtils.isEmpty(instance.getSourceInstanceId())) {
            subServiceTypeFlavorItemValue = mutiEngine;
        } else if (StringUtils.isNotEmpty(instance.getReplicaType()) &&
                RDSConstant.REPLICA_TYPE_BASIC.equalsIgnoreCase(instance.getReplicaType())) {
            // 单机版只读
            if (StringUtils.isNotEmpty(instance.getProductType())
                    && Payment.isPostpay(instance.getProductType())) {
                //后付费 单机版 独占型与通用型价格一致
                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
            } else {
                if (StringUtils.isNotEmpty(instance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                        instance.getResourceType())) {
                    // 预付费通用单机
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                } else {
                    // 预付费独占单机 默认
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                }
            }
        } else if (StringUtils.isNotEmpty(instance.getReplicaType()) &&
                RDSConstant.REPLICA_TYPE_HA.equalsIgnoreCase(instance.getReplicaType())) {
            // 双机版只读
            //后付费 双机版
            if (StringUtils.isNotEmpty(instance.getProductType())
                    && Payment.isPostpay(instance.getProductType())) {
                if (StringUtils.isNotEmpty(instance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                        instance.getResourceType())) {
                    // 通用型
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                } else {
                    // 独占型 默认
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                }
            } else {
                if (StringUtils.isNotEmpty(instance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                        instance.getResourceType())) {
                    // 预付费 通用
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                } else {
                    // 预付费 独占 默认
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                }
            }
        }
//        String subServiceTypeFlavorItemValue = StringUtils.isEmpty(instance.getSourceInstanceId())
//                ? mutiEngine : "default";
        // 此处旧逻辑有问题，当变配时磁盘类型发生变化，询价参数取的是当前实例的而非用户传值，更正
        // 兼容逻辑，为防止用户不传diskIoType（磁盘类型不变），可取资源中的参数
        String diskIoType = StringUtils.isNotEmpty(config.getDiskIoType()) ?
                config.getDiskIoType() : instance.getDiskIoType();
        ResizeRequest resizeRequest = new ResizeRequest();
        resizeRequest.setUuid(instance.getResourceUuid());
        if (InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getInstanceType())) {
            // 支持通用型代理
            if (StringUtils.isNotEmpty(instance.getResourceType())
                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType())) {
                resizeRequest.setFlavor(new HashSet<>(new Flavor()
                        .addFlavorItem(new Flavor.FlavorItem()
                                .withName("nodeAmount")
                                .withValue(config.getNodeAmount().toString()))
                        .addFlavorItem(new Flavor.FlavorItem()
                                .withName("subServiceType")
                                .withValue(RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL))));
            } else {
                resizeRequest.setFlavor(new HashSet<>(new Flavor()
                        .addFlavorItem(new Flavor.FlavorItem()
                                .withName("nodeAmount")
                                .withValue(config.getNodeAmount().toString()))));
            }
        } else if (config.getOldFlavor()){
            resizeRequest.setFlavor(new HashSet<>(new Flavor()
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("memory")
                            .withValue(config.getAllocatedMemoryInMB() + "m")
                            .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("subServiceType")
                            .withValue(subServiceTypeFlavorItemValue))));

            Set<Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
            if (ObjectUtils.equals("high_io", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 通用型ssd云磁盘
            } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else {
                flavors.add(new Flavor.FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            }

        } else {
            resizeRequest.setFlavor(new HashSet<>(new Flavor().addFlavorItem(new Flavor.FlavorItem()
                    .withName("cpu")
                    .withValue(config.getCpuCount() + "")
                    .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("memory")
                            .withValue(config.getAllocatedMemoryInGB() + "g")
                            .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("subServiceType")
                            .withValue(subServiceTypeFlavorItemValue))));

            Set<Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
            if (ObjectUtils.equals("high_io", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 通用型ssd云磁盘
            } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else {
                flavors.add(new Flavor.FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            }

        }
        if (ProductPayType.PRE_PAY.toString().equalsIgnoreCase(instance.getProductType())) {
            int resizeType = 0;
            Price priceResponse =
                    pricingService.getPriceDiff(config);
            if (priceResponse.getPrice().doubleValue() < 0) {
                resizeType = 1;
            }
            resizeRequest.setResizeType(resizeType);
        }
        try {
            resizeRequest.setExtra(new ObjectMapper().writeValueAsString(creatUpdateFlavorRequest(config, instance)));
        } catch (JsonProcessingException e) {
            throw new RDSExceptions.OrderPreparedException();
        }
        resizeRequest.setUserId(rdsClientFactory.getAccountId());
        return resizeRequest;
    }

    /**
     * 此方法用于在批量变配时，额外为一批订单打好标识
     * @param config
     * @param instance
     * @param request
     * @return
     */
    private ResizeRequest createResizeRequest(PriceDiffModel config, InstanceExtension instance,
                                              RdsCreateOrderRequestVo<PriceDiffModel> request) {
        // InstanceExtension instance = instanceService.detail(config.getInstanceId(), null);
        String mutiEngine = "";
//        if (instance.getEngineVersion()
//                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012)) { // 处理sqlserver 2012单机版
//            mutiEngine = instance.getEngine() + "_singleton";
        if (StringUtils.isNotEmpty(instance.getResourceType()) &&
                RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType()) &&
                StringUtils.isNotEmpty(instance.getEngine()) &&
                RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instance.getEngine())) {
            if ((StringUtils.isNotEmpty(instance.getCategory())  // 单机版的情况
                    && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON))) {
                // 此处单机版价格暂无，默认与原单机版保持一致，TODO
                mutiEngine = instance.getEngine() + "_singleton";
            } else {
                mutiEngine = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
            }
        } else {
            if (instance.getEngineVersion()
                    .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012)
                    || (instance.getEngineVersion()
                    .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016) &&
                    instance.getApplicationType().equals(RDSConstant.APPLICATION_TYPE_SINGLE))) { // sqlserver 新增2016单机版
                mutiEngine = instance.getEngine() + "_singleton";
            } else if (config.getIsEnhanced() != null && config.getIsEnhanced()) { // 处理mysql三节点增强版
                mutiEngine = instance.getEngine().toLowerCase() + "_finance";
            } else if (instance.getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_MYSQL)
                    && StringUtils.isNotEmpty(instance.getCategory())
                    && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // 单机版的情况
                mutiEngine = instance.getEngine() + "_singleton";
            } else if (instance.getEngine().equalsIgnoreCase(RDSConstant.RDS_ENGINE_PG)
                    && StringUtils.isNotEmpty(instance.getCategory())
                    && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON)) { // pg单机版情况
                mutiEngine = instance.getEngine() + "_singleton";
            } else {
                mutiEngine = instance.getEngine();
            }
        }
        // 新增高可用只读实例，此处需兼容高可用只读计费项
        String subServiceTypeFlavorItemValue = "default";
        if (StringUtils.isEmpty(instance.getSourceInstanceId())) {
            subServiceTypeFlavorItemValue = mutiEngine;
        } else if (StringUtils.isNotEmpty(instance.getReplicaType()) &&
                RDSConstant.REPLICA_TYPE_BASIC.equalsIgnoreCase(instance.getReplicaType())) {
            // 单机版只读
            if (StringUtils.isNotEmpty(instance.getProductType())
                    && Payment.isPostpay(instance.getProductType())) {
                //后付费 单机版 独占型与通用型价格一致
                subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
            } else {
                if (StringUtils.isNotEmpty(instance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                        instance.getResourceType())) {
                    // 预付费通用单机
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                } else {
                    // 预付费独占单机 默认
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                }
            }
        } else if (StringUtils.isNotEmpty(instance.getReplicaType()) &&
                RDSConstant.REPLICA_TYPE_HA.equalsIgnoreCase(instance.getReplicaType())) {
            // 双机版只读
            //后付费 双机版
            if (StringUtils.isNotEmpty(instance.getProductType())
                    && Payment.isPostpay(instance.getProductType())) {
                if (StringUtils.isNotEmpty(instance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                        instance.getResourceType())) {
                    // 通用型
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                } else {
                    // 独占型 默认
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                }
            } else {
                if (StringUtils.isNotEmpty(instance.getResourceType())
                        && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                        instance.getResourceType())) {
                    // 预付费 通用
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                } else {
                    // 预付费 独占 默认
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                }
            }
        }
//        String subServiceTypeFlavorItemValue = StringUtils.isEmpty(instance.getSourceInstanceId())
//                ? mutiEngine : "default";
        // 此处旧逻辑有问题，当变配时磁盘类型发生变化，询价参数取的是当前实例的而非用户传值，更正
        // 兼容逻辑，为防止用户不传diskIoType（磁盘类型不变），可取资源中的参数
        String diskIoType = StringUtils.isNotEmpty(config.getDiskIoType()) ?
                config.getDiskIoType() : instance.getDiskIoType();
        ResizeRequest resizeRequest = new ResizeRequest();
        resizeRequest.setUuid(instance.getResourceUuid());
        if (InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instance.getInstanceType())) {
            // 支持通用型代理
            if (StringUtils.isNotEmpty(instance.getResourceType())
                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType())) {
                resizeRequest.setFlavor(new HashSet<>(new Flavor()
                        .addFlavorItem(new Flavor.FlavorItem()
                                .withName("nodeAmount")
                                .withValue(config.getNodeAmount().toString()))
                        .addFlavorItem(new Flavor.FlavorItem()
                                .withName("subServiceType")
                                .withValue(RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL))));
            } else {
                resizeRequest.setFlavor(new HashSet<>(new Flavor()
                        .addFlavorItem(new Flavor.FlavorItem()
                                .withName("nodeAmount")
                                .withValue(config.getNodeAmount().toString()))));
            }
        } else if (config.getOldFlavor()){
            resizeRequest.setFlavor(new HashSet<>(new Flavor()
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("memory")
                            .withValue(config.getAllocatedMemoryInMB() + "m")
                            .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("subServiceType")
                            .withValue(subServiceTypeFlavorItemValue))));

            Set<Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
            if (ObjectUtils.equals("high_io", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 通用型ssd云磁盘
            } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else {
                flavors.add(new Flavor.FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            }

        } else {
            resizeRequest.setFlavor(new HashSet<>(new Flavor().addFlavorItem(new Flavor.FlavorItem()
                    .withName("cpu")
                    .withValue(config.getCpuCount() + "")
                    .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("memory")
                            .withValue(config.getAllocatedMemoryInGB() + "g")
                            .withScale(new BigDecimal(1)))
                    .addFlavorItem(new Flavor.FlavorItem()
                            .withName("subServiceType")
                            .withValue(subServiceTypeFlavorItemValue))));

            Set<Flavor.FlavorItem> flavors = resizeRequest.getFlavor();
            if (ObjectUtils.equals("high_io", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 通用型ssd云磁盘
            } else if (ObjectUtils.equals("cloud_nor", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("ssd").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else if (ObjectUtils.equals("cloud_enha", diskIoType)) {
                flavors.add(new Flavor.FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            } else {
                flavors.add(new Flavor.FlavorItem().withName("disk").withValue("1g")
                        .withScale(new BigDecimal(config.getAllocatedStorageInGB())));
            }

        }
        // 批量变配接口暂不需要
//        if (ProductPayType.PRE_PAY.toString().equalsIgnoreCase(instance.getProductType())) {
//            int resizeType = 0;
//            Price priceResponse =
//                    pricingService.getPriceDiff(config);
//            if (priceResponse.getPrice().doubleValue() < 0) {
//                resizeType = 1;
//            }
//            resizeRequest.setResizeType(resizeType);
//        }
        try {
            resizeRequest.setExtra(new ObjectMapper().writeValueAsString(
                    batchUpdateFlavorRequest(config, instance, request)));
        } catch (JsonProcessingException e) {
            throw new RDSExceptions.OrderPreparedException();
        }
        resizeRequest.setUserId(rdsClientFactory.getAccountId());
        return resizeRequest;
    }

    private void checkResizingMemoryAndStorage(PriceDiffModel config, InstanceExtension instanceExtension) {
        if (instanceExtension.getInstanceType().equals("rdsproxy")) {
            if (instanceExtension.getOldInstance() == 0
                    && instanceExtension.getNodeAmount() == config.getNodeAmount()) {
                throw new RDSExceptions.UnchangedException();
            }
            if (!RDSConstant.RDS_PROXY_NODEAMOUNT.contains(config.getNodeAmount())) {
                throw new RDSExceptions.ParamValidationException();
            }
        } else {
            String engine = instanceExtension.getEngine();
            String category = getCategory(instanceExtension);
            if (config.getAllocatedMemoryInGB() == null || config.getAllocatedStorageInGB() == null
                    || config.getCpuCount() == null) {
                throw new RDSExceptions.ParamValidationException();
            }
            int allocatedMemory = config.getAllocatedMemoryInGB() == null ? 0 : config.getAllocatedMemoryInGB();
            int allocatedStorage = config.getAllocatedStorageInGB();
            int cpuCount = config.getCpuCount();

            double preMemory = instanceExtension.getAllocatedMemoryInGB();
            int preStorage = instanceExtension.getAllocatedStorageInGB();
            int preCpuCount = instanceExtension.getCpuCount();
            String perDiskIoType = instanceExtension.getDiskIoType();

//            // 通用型实例不允许变更磁盘
//            if (StringUtils.isNotEmpty(instanceExtension.getResourceType())
//                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instanceExtension.getResourceType())
//                    && StringUtils.isNotEmpty(config.getDiskIoType())
//                    && !RDSConstant.DISK_OF_NORMAL_IO.equalsIgnoreCase(config.getDiskIoType())) {
//                throw new RDSBusinessExceptions.GeneralInstanceNotSupportException();
//            }

            // 此处为内存大小限制，目前只针对PG引擎类型做限制
            if (RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(instanceExtension.getEngine())) {
                checkMemOfMasterReplica(config, instanceExtension);
            }

            // 此处为磁盘大小限制
            // 针对所有的变配操作，需满足只读实例的磁盘大于等于主实例磁盘
            // 1. 若变配主实例且存在只读实例，则要求主实例磁盘小于等于只读磁盘
            if (instanceExtension.getTopology().getReadReplica().size() != 0
                    && RDSConstant.INSTANCE_TYPE_MASTER.equalsIgnoreCase(instanceExtension.getInstanceType())) {
//                List<String> replicas = instanceExtension.getTopology().getReadReplica();
//                for (String replica : replicas) {
//                    // 依次拿出每个只读实例
//                    Instance instance = rdsClientFactory.createRdsClient2ByInstanceId(replica)
//                            .instanceDescribe(replica).getInstance();
//                    if (allocatedStorage > instance.getAllocatedStorageInGB()) {
//                        throw new RDSBusinessExceptions.ResizeMasterReplicaDiskException();
//                    }
//                }

                List<Instance.ReadReplicaIdMapping> readReplicaIdMapping =
                        instanceExtension.getTopology().getReadReplicaIdMapping();
                for (Instance.ReadReplicaIdMapping mapping : readReplicaIdMapping) {
                    if (allocatedStorage > mapping.getAllocatedStorageInGB()) {
                        throw new RDSBusinessExceptions.ResizeMasterReplicaDiskException();
                    }
                }

            }
            // 2. 若变配只读实例，则要求主实例磁盘小于等于只读磁盘
            if (RDSConstant.INSTANCE_TYPE_REPLICA.equalsIgnoreCase(instanceExtension.getInstanceType())) {
//                String masterId = instanceExtension.getTopology().getMaster().get(0);
//                Instance instance = rdsClientFactory.createRdsClient2ByInstanceId(masterId)
//                        .instanceDescribe(masterId).getInstance();
//                if (allocatedStorage < instance.getAllocatedStorageInGB()) {
//                    throw new RDSBusinessExceptions.ResizeMasterReplicaDiskException();
//                }
                Instance.MasterIdMapping masterIdMapping = instanceExtension.getTopology().getMasterIdMapping();
                if (allocatedStorage < masterIdMapping.getAllocatedStorageInGB()) {
                    throw new RDSBusinessExceptions.ResizeMasterReplicaDiskException();
                }
            }


            // 针对缩容操作，要确保引擎类型以及缩容之后的最小存储
            // 1. 缩容操作只支持MySQL双机主实例（本地盘、云盘）、MySQL只读实例（本地盘、云盘）
            // 2. 存储降配时，目标存储size必须大于等于使用量*1.2；
            // 3. 对于双机主实例，存储降配时，目标存储size必须小于等于集群内的只读实例存储size；
            // 4. 对于只读实例，存储降配时，目标存储size必须大于等于主实例存储size；
            if (preStorage > allocatedStorage) {
                // 当前在进行缩容操作
                // 1. 缩容操作只支持MySQL双机主实例（本地盘、云盘）、MySQL只读实例（本地盘、云盘）
                if (RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(instanceExtension.getEngine()) ||
                        (RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instanceExtension.getEngine())
                        && "single".equalsIgnoreCase(instanceExtension.getApplicationType())
                                && StringUtils.isNotEmpty(config.getCategory())
                                && !RDSConstant.APPLICATION_TYPE_NORMAL.equalsIgnoreCase(config.getCategory())) ||
                        (RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(instanceExtension.getEngine())
                                && "single".equalsIgnoreCase(instanceExtension.getApplicationType())
                                && StringUtils.isNotEmpty(config.getCategory())
                                && !RDSConstant.APPLICATION_TYPE_NORMAL.equalsIgnoreCase(config.getCategory()))) {
                    throw new RDSBusinessExceptions.InstanceShrinkageEngineException();
                }

                // 2. 存储降配时，目标存储size必须大于等于使用量*1.2；
                double usedStorageInGB = instanceExtension.getUsedStorageInGB();
                double lowestStorage = usedStorageInGB * 1.2;
                if (allocatedStorage < lowestStorage) {
                    throw new RDSBusinessExceptions.InstanceShrinkageSizeException();
                }

                // 3. 对于双机主实例，存储降配时，目标存储size必须小于等于集群内的只读实例存储size；
                if ("master".equalsIgnoreCase(instanceExtension.getInstanceType())
                        && instanceExtension.getTopology().getReadReplica().size() != 0) {
//                    List<String> replicas = instanceExtension.getTopology().getReadReplica();
//                    for (String replica : replicas) {
//                        // 依次拿出每个只读实例
//                        Instance instance = rdsClientFactory.createRdsClient2ByInstanceId(replica)
//                                .instanceDescribe(replica).getInstance();
//                        if (allocatedStorage > instance.getAllocatedStorageInGB()) {
//                            throw new RDSBusinessExceptions.InstanceShrinkageUpperReplicaException();
//                        }
//                    }

                    List<Instance.ReadReplicaIdMapping> replicaIdMapping =
                            instanceExtension.getTopology().getReadReplicaIdMapping();
                    for (Instance.ReadReplicaIdMapping mapping : replicaIdMapping) {
                        if (allocatedStorage > mapping.getAllocatedStorageInGB()) {
                            throw new RDSBusinessExceptions.InstanceShrinkageUpperReplicaException();
                        }
                    }
                }

                // 4. 对于只读实例，存储降配时，目标存储size必须大于等于主实例存储size；
                if ("readReplica".equals(instanceExtension.getInstanceType())) {
//                    List<String> masters = instanceExtension.getTopology().getMaster();
//                    // 只读实例只挂一个主实例
//                    String master = masters.get(0);
//                    Instance instance = rdsClientFactory.createRdsClient2ByInstanceId(master)
//                            .instanceDescribe(master).getInstance();
//                    if (allocatedStorage < instance.getAllocatedStorageInGB()) {
//                        throw new RDSBusinessExceptions.InstanceShrinkageLowerMasterException();
//                    }
                    Instance.MasterIdMapping masterIdMapping = instanceExtension.getTopology().getMasterIdMapping();
                    if (allocatedStorage < masterIdMapping.getAllocatedStorageInGB()) {
                        throw new RDSBusinessExceptions.InstanceShrinkageLowerMasterException();
                    }
                }
            }

            if (StringUtils.isNotEmpty(config.getCategory())
                    && RDSConstant.APPLICATION_TYPE_NORMAL.equalsIgnoreCase(config.getCategory())
                    && RDSConstant.APPLICATION_TYPE_SINGLE.equalsIgnoreCase(instanceExtension.getApplicationType())
                    && RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instanceExtension.getEngine())) {
                // 单机转双机的场景下，需要支持计算、存储类型、存储大小都不变的情况
                if (preStorage > allocatedStorage
                        && preMemory > allocatedMemory && preCpuCount > cpuCount){
                    // 当前接口不允许同时降配计算 && 存储
                    throw new RDSBusinessExceptions.ShrinkageComputeAndStorageException();
                }
            } else {
                if (config.getDiskIoType() != null && perDiskIoType != null
                        && !config.getDiskIoType().equalsIgnoreCase(perDiskIoType)) {
                    if (preStorage > allocatedStorage
                            && preMemory > allocatedMemory && preCpuCount > cpuCount){
                        // 当前接口不允许同时降配计算 && 存储
                        throw new RDSBusinessExceptions.ShrinkageComputeAndStorageException();
                    }
                    // 只读实例变配不支持云盘转本地盘
                    if ("normal_io".equalsIgnoreCase(config.getDiskIoType())
                            && "cloud_enha".equalsIgnoreCase(perDiskIoType)
                            && RDSConstant.INSTANCE_TYPE_REPLICA.equalsIgnoreCase(instanceExtension.getInstanceType())) {
                        throw new RDSExceptions.UnchangedException();
                    }
                } else {
                    if (preStorage == allocatedStorage
                            && preMemory == allocatedMemory && preCpuCount == cpuCount) {
                        throw new RDSExceptions.UnchangedException();
                    }
                }
            }
            FlavorPO flavorPO = new FlavorPO();
            flavorPO.setApplicationType(category);
            flavorPO.setCpuCount(cpuCount);
            flavorPO.setMemoryCapacity(allocatedMemory);
            // 此处同样缺少对磁盘大小限制的检查
            flavorPO.setVolumeCapacity(allocatedStorage);
            flavorPO.setEngine(engine);

            int countByUniqueKey = flavorMapper.selectFlavorCountByUniqueKey(flavorPO);
            if (countByUniqueKey == 0) {
                throw new RDSExceptions.CpuCountValidationException();
            }
            // 套餐限制
            flavorPO = flavorMapper.selectFlavorByUniqueKey(flavorPO);
            if (allocatedStorage % 5 != 0 || allocatedStorage < flavorPO.getMinVolumeCapacity()) {
                throw new RDSExceptions.StorageValidationException();
            }
//                if (StringUtils.isNotEmpty(instanceExtension.getAzone())) {
//                    for (String zone : instanceExtension.getAzone().split(",")) {
//                        ZoneFlavorLimitPO zoneFlavorLimitPO
//                                = flavorMapper.getZoneFlavorLimitPO(zone, rdsClientFactory.getAccountId());
//                        if (zoneFlavorLimitPO == null) {
//                            throw new RDSExceptions.ResourceServerException();
//                        }
//                    }
//                }
            if (!othersService.checkDiskLimit() && allocatedStorage > flavorPO.getMaxVolumeCapacity()) {
                throw new RDSExceptions.StorageValidationException();
            }
//            }

//            if (StringUtils.isNotEmpty(instanceExtension.getSourceInstanceId())
//                    && !instanceExtension.getInstanceType().equals("rdsproxy")) {
//                checkReplicaConfig(instanceExtension.getSourceInstanceId(), config.getAllocatedStorageInGB(), null);
//            }
        }
        // 子网IP剩余量前置检查接口
        instanceService.precheckResourceResize(config, instanceExtension);
    }

    /**
     * 此接口用于批量变配的条件检查，后续会将检查项收敛至此 TODO
     * @param config
     * @param instanceExtension
     */
    private void checkBatchResizingMemoryAndStorage(PriceDiffModel config, InstanceExtension instanceExtension) {
        if (instanceExtension.getInstanceType().equals("rdsproxy")) {
            if (instanceExtension.getOldInstance() == 0
                    && instanceExtension.getNodeAmount() == config.getNodeAmount()) {
                throw new RDSExceptions.UnchangedException();
            }
            if (!RDSConstant.RDS_PROXY_NODEAMOUNT.contains(config.getNodeAmount())) {
                throw new RDSExceptions.ParamValidationException();
            }
        } else {
            String engine = instanceExtension.getEngine();
            String category = getCategory(instanceExtension);
            if (config.getAllocatedMemoryInGB() == null || config.getAllocatedStorageInGB() == null
                    || config.getCpuCount() == null) {
                throw new RDSExceptions.ParamValidationException();
            }
            int allocatedMemory = config.getAllocatedMemoryInGB() == null ? 0 : config.getAllocatedMemoryInGB();
            int allocatedStorage = config.getAllocatedStorageInGB();
            int cpuCount = config.getCpuCount();

            double preMemory = instanceExtension.getAllocatedMemoryInGB();
            int preStorage = instanceExtension.getAllocatedStorageInGB();
            int preCpuCount = instanceExtension.getCpuCount();
            String perDiskIoType = instanceExtension.getDiskIoType();

            // cds 最小 50G
            if (!"normal_io".equalsIgnoreCase(config.getDiskIoType())) {
                if (config.getAllocatedStorageInGB() < 50) {
                    throw new RDSBusinessExceptions.DiskSizeInvalidException(instanceExtension.getInstanceShortId());
                }
            }

            if (config.getDiskIoType() != null && perDiskIoType != null
                    && !config.getDiskIoType().equalsIgnoreCase(perDiskIoType)) {
                if (preStorage > allocatedStorage
                        && preMemory > allocatedMemory && preCpuCount > cpuCount){
                    throw new RDSExceptions.UnchangedException();
                }
                // 只读实例变配不支持云盘转本地盘
                if ("normal_io".equalsIgnoreCase(config.getDiskIoType())
                        && "cloud_enha".equalsIgnoreCase(perDiskIoType)
                        && RDSConstant.INSTANCE_TYPE_REPLICA.equalsIgnoreCase(instanceExtension.getInstanceType())) {
                    throw new RDSExceptions.UnchangedException();
                }
            } else {
                if (preStorage == allocatedStorage
                        && preMemory == allocatedMemory && preCpuCount == cpuCount
                        && config.getDiskIoType().equals(perDiskIoType)) {
                    throw new RDSExceptions.UnchangedException();
                }
            }
//            if (Payment.isPrepay(instanceExtension.getProductType())
//                    && ((!config.getOldFlavor() && preMemory > config.getAllocatedMemoryInGB())
//                    || (config.getOldFlavor() && preMemory * 1024 > config.getAllocatedMemoryInMB())
//                    || preCpuCount > config.getCpuCount())) {
//                throw new RDSExceptions.NotSupportOperation("prepaid products downgrade");
//            }

            // 检查cpu memory和storage是否在套餐范围内
            if (config.getOldFlavor()) {
                if (RdsFlavorManager.getInstanceClass(engine,
                        config.getAllocatedMemoryInMB(), allocatedStorage) == null){
                    throw new RDSExceptions.MemoryValidationException();
                }
            } else {
                FlavorPO flavorPO = new FlavorPO();
                flavorPO.setApplicationType(category);
                flavorPO.setCpuCount(cpuCount);
                flavorPO.setMemoryCapacity(allocatedMemory);
                // 此处同样缺少对磁盘大小限制的检查
                flavorPO.setVolumeCapacity(allocatedStorage);
                flavorPO.setEngine(engine);

                int countByUniqueKey = flavorMapper.selectFlavorCountByUniqueKey(flavorPO);
                if (countByUniqueKey == 0) {
                    throw new RDSExceptions.CpuCountValidationException();
                }
                // 套餐限制
                flavorPO = flavorMapper.selectFlavorByUniqueKey(flavorPO);
                if (allocatedStorage % 5 != 0 || allocatedStorage < flavorPO.getMinVolumeCapacity()
                                              || allocatedStorage > flavorPO.getMaxVolumeCapacity()) {
                    throw new RDSExceptions.StorageValidationException();
                }
//                if (StringUtils.isNotEmpty(instanceExtension.getAzone())) {
//                    for (String zone : instanceExtension.getAzone().split(",")) {
//                        ZoneFlavorLimitPO zoneFlavorLimitPO
//                                = flavorMapper.getZoneFlavorLimitPO(zone, rdsClientFactory.getAccountId());
//                        if (zoneFlavorLimitPO == null) {
//                            throw new RDSExceptions.ResourceServerException();
//                        }
//                    }
//                }

//                if (!othersService.checkDiskLimit() && allocatedStorage > flavorPO.getMaxVolumeCapacity()) {
//                    throw new RDSExceptions.StorageValidationException();
//                }
            }

//            if (StringUtils.isNotEmpty(instanceExtension.getSourceInstanceId())
//                    && !instanceExtension.getInstanceType().equals("rdsproxy")) {
//                checkReplicaConfig(instanceExtension.getSourceInstanceId(), config.getAllocatedStorageInGB(), null);
//            }
        }
        // 子网IP剩余量前置检查接口
        instanceService.precheckResourceResize(config, instanceExtension);
    }

    // 此处检查 PG 主实例与只读实例的内存大小，目前需严格限制下主实例内存需要小于等于只读实例内存
    private void checkMemOfMasterReplica(PriceDiffModel config, InstanceExtension instanceExtension) {
        // 1.若当前变配的是主实例且主实例内存大于只读实例内存，不符合预期
        int memoryInGB = config.getAllocatedMemoryInGB() * 1024;
        if (instanceExtension.getTopology().getReadReplica().size() != 0
                && RDSConstant.INSTANCE_TYPE_MASTER.equalsIgnoreCase(instanceExtension.getInstanceType())
                && RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(instanceExtension.getEngine())) {
            List<Instance.ReadReplicaIdMapping> readReplicaIdMapping =
                    instanceExtension.getTopology().getReadReplicaIdMapping();
            for (Instance.ReadReplicaIdMapping mapping : readReplicaIdMapping) {
                if (memoryInGB > mapping.getAllocatedMemoryInMB()) {
                    throw new RDSBusinessExceptions.ResizeMasterMemoryException();
                }
            }
        }

        // 2.若当前变配的是只读实例且只读实例内存小于主实例，不符合预期
        if (RDSConstant.INSTANCE_TYPE_REPLICA.equalsIgnoreCase(instanceExtension.getInstanceType())
                 && RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(instanceExtension.getEngine())) {
            Instance.MasterIdMapping masterIdMapping = instanceExtension.getTopology().getMasterIdMapping();
            if (memoryInGB < masterIdMapping.getAllocatedMemoryInMB()) {
                throw new RDSBusinessExceptions.ResizeReplicaMemoryException();
            }
        }

    }

    private String getCategory(InstanceExtension instanceExtension) {
        if (instanceExtension.getEngineVersion()
                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012)
                || instanceExtension.getEngineVersion()
                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2016)
                ||(instanceExtension.getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER)
                && instanceExtension.getApplicationType().equals(RDSConstant.APPLICATION_TYPE_SINGLE))) {
            return RDSConstant.CATEGORY_BASIC;  // sqlserver单机版
        } else if (instanceExtension.getApplicationType().equalsIgnoreCase(RDSConstant.CATEGORY_ENHANCED)) {
            // MySQL三节点增强版
            return RDSConstant.CATEGORY_ENHANCED;
        } else {
            return RDSConstant.CATEGORY_STANDARD;
        }
    }

    // rds后端需要的变配请求
    private InstanceUpdateFlavorRequest creatUpdateFlavorRequest(PriceDiffModel config, InstanceExtension instance) {
        InstanceUpdateFlavorRequest updateFlavorRequest = new InstanceUpdateFlavorRequest();

        String instanceClass = "rds";

        Boolean isEnhanced = false;
        if (StringUtils.isNotEmpty(instance.getApplicationType())) {
            isEnhanced = instance.getApplicationType().equalsIgnoreCase("enhanced");
        }

        InstanceUpdateFlavorRequest.InstanceParameters instanceParameters = updateFlavorRequest.getInstanceParameters();
        instanceParameters.setInstanceClass(instanceClass);
        if (instance.getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_PROXY)) {
            instanceParameters.setNodeAmount(config.getNodeAmount());
        } else {
            instanceParameters.setAllocatedMemoryInMB(config.getAllocatedMemoryInGB() * 1024);
            instanceParameters.setAllocatedStorageInGB(config.getAllocatedStorageInGB());
            instanceParameters.setCpuCount(config.getCpuCount());
            Map<String, String> subnetMap = new HashMap<>();
            if (!StringUtils.isEmpty(config.getSubnetId())) {
                String[] subnetItems = config.getSubnetId().split(",");
                for (String item : subnetItems) {
                    if (!item.trim().isEmpty()) {
                        String[] part = item.trim().split(":");
                        if (part.length == 2) {
                            subnetMap.put(part[0], part[1]);
                        } else if (part.length == 1) {
                            subnetMap.put(part[0], "");
                        }
                    }
                }
            }
            instanceParameters.setSubnetId(subnetMap);
            instanceParameters.setForceHotUpgrade(config.getForceHotUpgrade());
            instanceParameters.setMasterAzone(config.getMasterAzone());
            instanceParameters.setBackupAzone(config.getBackupAzone());
            if (config.getEffectiveTime() != null){
                instanceParameters.setEffectiveTime(config.getEffectiveTime());
            }
            if (config.getDiskType() != null){
                instanceParameters.setDiskType(config.getDiskType());
            }
            if (config.getCdsType() != null){
                instanceParameters.setCdsType(config.getCdsType());
            }
            if (config.getDiskIoType() != null){
                instanceParameters.setDiskIoType(config.getDiskIoType());
            }
            if (config.getCategory() != null) {
                instanceParameters.setCategory(config.getCategory());
            }

            instanceParameters.setEdgeSubnetId(config.getEdgeSubnetId());
        }
        instanceParameters.setIsEnhanced(isEnhanced);
        // 设置env
        updateFlavorRequest.setEnv(env);

        return updateFlavorRequest;
    }

    /**
     * 此方法用来为批量创建的订单打标记
     * @param config
     * @param instance
     * @param request
     * @return
     */
    private InstanceUpdateFlavorRequest batchUpdateFlavorRequest(PriceDiffModel config, InstanceExtension instance,
                                                                 RdsCreateOrderRequestVo<PriceDiffModel> request) {
        InstanceUpdateFlavorRequest updateFlavorRequest = new InstanceUpdateFlavorRequest();

        String instanceClass = "rds";

        Boolean isEnhanced = false;
        if (StringUtils.isNotEmpty(instance.getApplicationType())) {
            isEnhanced = instance.getApplicationType().equalsIgnoreCase("enhanced");
        }

        InstanceUpdateFlavorRequest.InstanceParameters instanceParameters = updateFlavorRequest.getInstanceParameters();
        instanceParameters.setInstanceClass(instanceClass);
        if (instance.getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_PROXY)) {
            instanceParameters.setNodeAmount(config.getNodeAmount());
        } else {
            instanceParameters.setAllocatedMemoryInMB(config.getAllocatedMemoryInGB() * 1024);
            instanceParameters.setAllocatedStorageInGB(config.getAllocatedStorageInGB());
            instanceParameters.setCpuCount(config.getCpuCount());
            Map<String, String> subnetMap = new HashMap<>();
            if (!StringUtils.isEmpty(config.getSubnetId())) {
                String[] subnetItems = config.getSubnetId().split(",");
                for (String item : subnetItems) {
                    if (!item.trim().isEmpty()) {
                        String[] part = item.trim().split(":");
                        if (part.length == 2) {
                            subnetMap.put(part[0], part[1]);
                        } else if (part.length == 1) {
                            subnetMap.put(part[0], "");
                        }
                    }
                }
            }
            instanceParameters.setSubnetId(subnetMap);
            instanceParameters.setForceHotUpgrade(config.getForceHotUpgrade());
            instanceParameters.setMasterAzone(config.getMasterAzone());
            instanceParameters.setBackupAzone(config.getBackupAzone());
            if (config.getEffectiveTime() != null){
                instanceParameters.setEffectiveTime(config.getEffectiveTime());
            }
            if (config.getDiskType() != null){
                instanceParameters.setDiskType(config.getDiskType());
            }
            if (config.getCdsType() != null){
                instanceParameters.setCdsType(config.getCdsType());
            }
            if (config.getDiskIoType() != null){
                instanceParameters.setDiskIoType(config.getDiskIoType());
            }
            instanceParameters.setEdgeSubnetId(config.getEdgeSubnetId());
        }
        instanceParameters.setIsEnhanced(isEnhanced);
        // 设置env
        updateFlavorRequest.setEnv(env);
        // 为批量变配的订单打好标识
        List<RdsCreateOrderRequestVo.Item<PriceDiffModel>> items = request.getItems();
        StringBuilder instanceIds = new StringBuilder();
        // 1.将本次批量变配的实例id 全部取出。此时订单还未生成，所以实例 ID 作为批量操作的标识
        for (RdsCreateOrderRequestVo.Item<PriceDiffModel> item : items) {
            String instanceId = item.getConfig().getInstanceId();
            instanceIds.append(instanceId).append(",");
        }
        // 2.将最后一个逗号删除掉
        instanceIds.deleteCharAt(instanceIds.length() - 1);
        updateFlavorRequest.setBatchInstances(String.valueOf(instanceIds));

        return updateFlavorRequest;
    }

    public Set<FlavorItem> getFlavorItemsForOrder(InstanceCreateModel.DashCreateInstance instance) {
        if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_PROXY)) {
            return new HashSet<>(Arrays.asList(
                    new FlavorItem().withName("azone").withValue(instance.getAzone()),
                    new FlavorItem().withName("nodeAmount").withValue(instance.getNodeAmount().toString()).withScale(1)
            ));
        } else {
            String mutiEngine = getMutiEngine(instance);
            // 新增高可用只读实例，此处需兼容高可用只读计费项
            String subServiceTypeFlavorItemValue = "default";
            if (StringUtils.isEmpty(instance.getSourceInstanceId())) {
                subServiceTypeFlavorItemValue = mutiEngine;
            } else {
                if (StringUtils.isNotEmpty(instance.getReplicaType()) &&
                        RDSConstant.REPLICA_TYPE_BASIC.equalsIgnoreCase(instance.getReplicaType())) {
                    subServiceTypeFlavorItemValue = "default";
                } else if (StringUtils.isNotEmpty(instance.getReplicaType()) &&
                        RDSConstant.REPLICA_TYPE_HA.equalsIgnoreCase(instance.getReplicaType())) {
                    subServiceTypeFlavorItemValue = "MySQL";
                } else {
                    logger.error("instance param error, cannot get replicaType...");
                }
            }


            String initialInstanceId = getInitialInstanceId(instance);
            String initialInstanceShortId = idMapperService.getInstanceId(initialInstanceId);
            if (StringUtils.isNotEmpty(initialInstanceShortId)) {
                initialInstanceId = initialInstanceShortId;
            }
            if (StringUtils.isEmpty(instance.getInstanceName())
                    && StringUtils.isNotEmpty(initialInstanceId)) {
                instance.setInstanceName(initialInstanceId + "_replica");
            }

            Set<FlavorItem> flavors = new HashSet<>(Arrays.asList(
                    new FlavorItem().withName("cpu").withValue(instance.getCpuCount() + "").withScale(1),
                    new FlavorItem().withName("memory").withValue(instance.getAllocatedMemoryInGB() + "g").withScale(1),
                    new FlavorItem().withName("subServiceType").withValue(subServiceTypeFlavorItemValue),
                    new FlavorItem().withName("sourceInstanceId").withValue(initialInstanceId),
                    new FlavorItem().withName("azone").withValue(instance.getAzone()),
                    new FlavorItem().withName("vpcId").withValue(instance.getVpcId()),
                    new FlavorItem().withName("subnetId").withValue(instance.getSubnetId())));

            if (ObjectUtils.equals("high_io", instance.getDiskIoType())) {
                flavors.add(new FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(instance.getAllocatedStorageInGB()));
                // 通用ssd云磁盘
            } else if (ObjectUtils.equals("cloud_nor", instance.getDiskIoType())) {
                flavors.add(new FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(instance.getAllocatedStorageInGB()));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", instance.getDiskIoType())) {
                flavors.add(new FlavorItem().withName("ssd").withValue("1g")
                        .withScale(instance.getAllocatedStorageInGB()));
            } else if (ObjectUtils.equals("cloud_enha", instance.getDiskIoType())) {
                flavors.add(new FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(instance.getAllocatedStorageInGB()));
            } else {
                flavors.add(new FlavorItem().withName("disk").withValue("1g")
                        .withScale(instance.getAllocatedStorageInGB()));
            }
            return flavors;
        }
    }

    /**
     * 为避免影响就逻辑，此处单独封装通用型实例相关
     * @param instance
     * @param productType
     * @return
     */
    public Set<FlavorItem> getGeneralFlavorItemsForOrder(InstanceCreateModel.DashCreateInstance instance,
                                                         String productType) {
        if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_PROXY)) {
            if (StringUtils.isNotEmpty(instance.getResourceType())
                    && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType())) {
                return new HashSet<>(Arrays.asList(
                        new FlavorItem().withName("azone").withValue(instance.getAzone()),
                        new FlavorItem().withName("nodeAmount").
                                withValue(instance.getNodeAmount().toString()).withScale(1),
                        new FlavorItem().withName("subServiceType").
                                withValue(RDSConstant.BILLING_ITEMS_OF_PROXY_GENERAL).withScale(1)
                ));
            } else {
                return new HashSet<>(Arrays.asList(
                        new FlavorItem().withName("azone").withValue(instance.getAzone()),
                        new FlavorItem().withName("nodeAmount").withValue(instance.getNodeAmount().toString())
                                .withScale(1)
                ));
            }
        } else {
            String mutiEngine = getMutiEngine(instance);
            // 新增高可用只读实例，此处需兼容高可用只读计费项
            String subServiceTypeFlavorItemValue = "default";
            if (StringUtils.isEmpty(instance.getSourceInstanceId())) {
                subServiceTypeFlavorItemValue = mutiEngine;
            } else if (StringUtils.isNotEmpty(instance.getReplicaType()) &&
                    RDSConstant.REPLICA_TYPE_BASIC.equalsIgnoreCase(instance.getReplicaType())) {
                // 单机版只读
                if (StringUtils.isNotEmpty(productType)
                        && Payment.isPostpay(productType)) {
                    //后付费 单机版 独占型与通用型价格一致
                    subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                } else {
                    if (StringUtils.isNotEmpty(instance.getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                            instance.getResourceType())) {
                        // 预付费通用单机
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    } else {
                        // 预付费独占单机 默认
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_DEFAULT_SINGLE;
                    }
                }
            } else if (StringUtils.isNotEmpty(instance.getReplicaType()) &&
                    RDSConstant.REPLICA_TYPE_HA.equalsIgnoreCase(instance.getReplicaType())) {
                // 双机版只读
                //后付费 双机版
                if (StringUtils.isNotEmpty(productType)
                        && Payment.isPostpay(productType)) {
                    if (StringUtils.isNotEmpty(instance.getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                            instance.getResourceType())) {
                        // 通用型
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                    } else {
                        // 独占型 默认
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                    }
                } else {
                    if (StringUtils.isNotEmpty(instance.getResourceType())
                            && RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(
                            instance.getResourceType())) {
                        // 预付费 通用
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
                    } else {
                        // 预付费 独占 默认
                        subServiceTypeFlavorItemValue = RDSConstant.BILLING_ITEMS_OF_MYSQL;
                    }
                }
            } else {
                logger.error("instance param error, cannot get replicaType...");
            }
            String initialInstanceId = getInitialInstanceId(instance);
            String initialInstanceShortId = idMapperService.getInstanceId(initialInstanceId);
            if (StringUtils.isNotEmpty(initialInstanceShortId)) {
                initialInstanceId = initialInstanceShortId;
            }
            if (StringUtils.isEmpty(instance.getInstanceName())
                    && StringUtils.isNotEmpty(initialInstanceId)) {
                instance.setInstanceName(initialInstanceId + "_replica");
            }

            Set<FlavorItem> flavors = new HashSet<>(Arrays.asList(
                    new FlavorItem().withName("cpu").withValue(instance.getCpuCount() + "").withScale(1),
                    new FlavorItem().withName("memory").withValue(instance.getAllocatedMemoryInGB() + "g").withScale(1),
                    new FlavorItem().withName("subServiceType").withValue(subServiceTypeFlavorItemValue),
                    new FlavorItem().withName("sourceInstanceId").withValue(initialInstanceId),
                    new FlavorItem().withName("azone").withValue(instance.getAzone()),
                    new FlavorItem().withName("vpcId").withValue(instance.getVpcId()),
                    new FlavorItem().withName("subnetId").withValue(instance.getSubnetId())));

            if (ObjectUtils.equals("high_io", instance.getDiskIoType())) {
                flavors.add(new FlavorItem().withName("NVMedisk").withValue("1g")
                        .withScale(instance.getAllocatedStorageInGB()));
                // 通用ssd云磁盘
            } else if (ObjectUtils.equals("cloud_nor", instance.getDiskIoType())) {
                flavors.add(new FlavorItem().withName("premium_ssd").withValue("1g")
                        .withScale(instance.getAllocatedStorageInGB()));
                // 高性能云磁盘
            } else if (ObjectUtils.equals("cloud_high", instance.getDiskIoType())) {
                flavors.add(new FlavorItem().withName("ssd").withValue("1g")
                        .withScale(instance.getAllocatedStorageInGB()));
            } else if (ObjectUtils.equals("cloud_enha", instance.getDiskIoType())) {
                flavors.add(new FlavorItem().withName("enhanced_ssd_pl1").withValue("1g")
                        .withScale(instance.getAllocatedStorageInGB()));
            } else {
                flavors.add(new FlavorItem().withName("disk").withValue("1g")
                        .withScale(instance.getAllocatedStorageInGB()));
            }
            return flavors;
        }
    }

    private CreateOrderRequest<CreateNewTypeOrderItem> getCreateOrderRequest(
            BaseCreateOrderRequestVo<InstanceCreateModel> request, String from) {
        InstanceCreateModel config = request.getItems().get(0).getConfig();
        InstanceCreateModel.DashCreateInstance instance = config.getInstance();

        // 参数校验
        checkInstanceCreateRequest(config);
        if (!RDSConstant.FROM_CONSOLE.equalsIgnoreCase(from)) {
            // 子网IP剩余量前置检查接口
            instanceService.precheckResourceCreate(config);
        }

        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            checkInstanceCreateRequestForApiExtra(config);
        }

        CreateOrderRequest<CreateNewTypeOrderItem> createOrderRequest = new CreateOrderRequest<>();
        CreateNewTypeOrderItem createNewTypeOrderItem = new CreateNewTypeOrderItem();

        createNewTypeOrderItem.setServiceType(getServiceType(config.getInstance().getEngine(),
                config.getInstance().getSourceInstanceId()));
        createNewTypeOrderItem.setProductType(LogicRdsPaymentHandle.isConsolePay(config.getProductType())
                ? config.getProductType() : Payment.getValueByShowInApi(config.getProductType()));
        createNewTypeOrderItem.setPurchaseOrder(request.getItems().get(0).getPurchaseOrder());
        createNewTypeOrderItem.setPaymentMethod(request.getItems().get(0).getPaymentMethod());

        createNewTypeOrderItem.setCount(config.getNumber());
        Integer time = config.getDuration();
        createNewTypeOrderItem.setTime(new BigDecimal(time == null ? 0 : time));
        createNewTypeOrderItem.setTimeUnit(Payment
                .isPrepay(config.getProductType()) ? TimeUnit.MONTH.toString() : null);
        createNewTypeOrderItem.setKey("RDS_123");
        createNewTypeOrderItem.setFlavor(getGeneralFlavorItemsForOrder(config.getInstance(), config.getProductType()));

        // 单机版，临时处理，后续前后端改造都要使用category字段
        if (instance.getCategory() != null
                && ObjectUtils.equals(instance.getCategory(), RDSConstant.CATEGORY_SINGLETON)) {
            instance.setIsSingle(Boolean.TRUE);
        }

        Map<String, String> zoneMap = null;
        if (!RDSConstant.REGION_EDGE.equals(regionConfiguration.getCurrentRegion())) {
            // 只有中心云需要 zoneMap
            zoneMap = othersService.zoneMap();
        }
        OrderItemExtraInfo extraInfo = config.convertToOrderItemExtraInfo(zoneMap);
        // tags 摘到外部
        extraInfo.setTags(instance.getTags());
        // 资源信息
        extraInfo.setResourceGroupId(instance.getResourceGroupId());
        // 设置env
        extraInfo.setEnv(env);
        try {
            createNewTypeOrderItem.setExtra(new ObjectMapper()
                    .writeValueAsString(extraInfo));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        createOrderRequest.setOrderType(OrderType.NEW.name());
        createOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
        createOrderRequest.setPaymentMethod(request.getPaymentMethod());
        createOrderRequest.getItems().add(createNewTypeOrderItem);
        createOrderRequest.setTicketId(request.getTicketId());
        createOrderRequest.setTotal(request.getTotal());

        // 增加是否直接付款，API的话直接付款
        if (config != null && config.getIsDirectPay() != null
                && config.getIsDirectPay() && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            createOrderRequest.setIsDirectPay(Boolean.TRUE);
        }
        return createOrderRequest;
    }

    /**
     * 查询向 Billing 侧发送的创建新购实例订单请求
     *
     * @param request 前端创建新购实例订单请求
     * @return 向 Billing 侧发送的创建新购实例订单请求
     * @throws Exception exception
     */
    public CreateOrderRequest<CreateNewTypeOrderItem> getCreateOrderParam(
            BaseCreateOrderRequestVo<InstanceCreateModel> request) throws Exception {
        String from = RDSConstant.FROM_CONSOLE;
        instanceService.checkAndSupplyInstanceCreateModel(request.getItems().get(0).getConfig(), from);
        return getCreateOrderRequest(request, from);
    }

    public OrderUuidResult createNewOrder(BaseCreateOrderRequestVo<InstanceCreateModel> request, String from) {
        stsServiceRoleActivateWithCheck();
        OrderUuidResult result;
        try {
            result = rdsClientFactory.createServiceCatalogOrderClient().
                    createOrder(getCreateOrderRequest(request, from));
        } catch (BceInternalResponseException ex) {
            if ("InsufficientBalance".equals(ex.getCode())) {
                throw new RDSExceptions.GiveMeMoreMoneyException();
            } else {
                throw ex;
            }
        }
        return result;
    }

    private String getMutiEngine(InstanceCreateModel.DashCreateInstance instance) {
        String mutiEngine = "";
        if (StringUtils.isNotEmpty(instance.getResourceType()) &&
                RDSConstant.SHARED_INSTANCE.equalsIgnoreCase(instance.getResourceType()) &&
                StringUtils.isNotEmpty(instance.getEngine()) &&
                RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(instance.getEngine())) {
            if ((StringUtils.isNotEmpty(instance.getCategory())  // 单机版的情况
                    && instance.getCategory().equals(RDSConstant.CATEGORY_SINGLETON))) {
                // 此处单机版价格暂无，默认与原单机版保持一致，TODO
                mutiEngine = instance.getEngine() + "_singleton";
            } else {
                mutiEngine = RDSConstant.BILLING_ITEMS_OF_GENERAL_NORMAL;
            }
        } else {
            if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER)
                    && StringUtils.isNotEmpty(instance.getCategory())  // 单机版的情况
                    && instance.getCategory().equalsIgnoreCase(RDSConstant.CATEGORY_SINGLETON)) {
                mutiEngine = instance.getEngine() + "_singleton"; // 处理sqlserver单机版的情况
                // 设置单机版标记
                instance.setIsSingle(Boolean.TRUE);
            } else if ((instance.getIsEnhanced() != null
                    && instance.getIsEnhanced())  // 处理MySQL三节点增强版的情况
                    || (StringUtils.isNotEmpty(instance.getInstanceType())  // raft版的情况
                    && instance.getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT))) {
                mutiEngine = instance.getEngine().toLowerCase() + "_finance";
            } else if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_MYSQL)
                    && StringUtils.isNotEmpty(instance.getCategory())  // 单机版的情况
                    && instance.getCategory().equalsIgnoreCase(RDSConstant.CATEGORY_SINGLETON)) {
                // 只有mysql才支持单机版
                // 这里没用RDSConstant.INSTANCE_TYPE_RDS_SINGLE
                mutiEngine = instance.getEngine() + "_singleton";
                // 设置单机版标记
                instance.setIsSingle(Boolean.TRUE);
            } else if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_PG)
                    && StringUtils.isNotEmpty(instance.getCategory()) // pg单机版情况
                    && instance.getCategory().equalsIgnoreCase(RDSConstant.CATEGORY_SINGLETON)) {
                mutiEngine = instance.getEngine() + "_singleton";
                // 设置pg单机版
                instance.setIsSingle(Boolean.TRUE);
            } else {
                mutiEngine = instance.getEngine();
            }
        }
        return mutiEngine;
    }

    private String getInitialInstanceId(InstanceCreateModel.DashCreateInstance instance) {
        String initialInstanceId = "";
        if (instance.getInitialDataReference() != null
                && StringUtils.isNotEmpty(instance.getInitialDataReference().getInstanceId())) {
            initialInstanceId = instance.getInitialDataReference().getInstanceId();
        }
        return initialInstanceId;
    }

    /**
     * 支持实例列表和回收站中的预付费实例通过API续费
     * @param request
     * @param from
     * @return
     */
    public OrderUuidResult createRenewOrderFromApi(ApiRenewModel request, String from) {
        BaseCreateOrderRequestVo<RenewModel> orderRequestVo = new BaseCreateOrderRequestVo<>();
        List<String> instanceIds = request.getInstanceIds();
        List<BaseCreateOrderRequestVo.Item<RenewModel>> items = new ArrayList<>();

        // 该接口用于续费RUNNING、STOPPED状态的预付费实例，所以不对实例是否处于回收站列表或实例列表进行校验

        // 根据实例id 查询对应的资源
        for (String instanceId : instanceIds) {
            logger.info("renew is ready, instanceId is {}", instanceId);
            Resource resource = recyclerService.getResourceByInstanceId(instanceId);
            // 先判断当前实例是否是预付费实例
            if (resource.getProductType() != null) {
                if (Payment.isPostpay(resource.getProductType())) {
                    throw new RDSExceptions.RenewNotSupportToPostpayException(instanceId);
                }
            }else {
                logger.info("resource.getProductType is null, resource is {}" , resource);
            }

            BaseCreateOrderRequestVo.Item<RenewModel> item = new BaseCreateOrderRequestVo.Item<>();
            RenewModel config = new RenewModel();
            config.setDuration(request.getDuration());
            config.setInstanceId(instanceId);
            config.setUuid(resource.getUuid());

            item.setConfig(config);
            item.setPaymentMethod(Collections.<PaymentModel>emptySet());

            items.add(item);

        }

        orderRequestVo.setItems(items);
        orderRequestVo.setPaymentMethod(Collections.<PaymentModel>emptySet());
        return createRenewOrder(orderRequestVo, from);

    }

    public OrderUuidResult createRenewOrder(BaseCreateOrderRequestVo<RenewModel> request, String from) {
        OrderUuidResult result = null;

        List<CreateRenewTypeOrderItem> createRenewTypeOrderItems = new LinkedList();
        for (BaseCreateOrderRequestVo.Item<RenewModel> item : request.getItems()) {
            RenewModel config = item.getConfig();
            Resource resource = instanceService.getResourceByInstanceId(config.getInstanceId());

            if (resource == null) {
                throw new RDSExceptions.ResourceNotExistException();
            }
            CreateRenewTypeOrderItem createRenewTypeOrderItem = new CreateRenewTypeOrderItem();
            createRenewTypeOrderItem.setServiceType(resource.getServiceType());
            createRenewTypeOrderItem.setPaymentMethod(item.getPaymentMethod());
            createRenewTypeOrderItem.setProductType(ProductPayType.PRE_PAY.alias);
            createRenewTypeOrderItem.setResourceUuid(resource.getUuid());
            createRenewTypeOrderItem.setDuration(config.getDuration());

            createRenewTypeOrderItems.add(createRenewTypeOrderItem);
        }

        CreateOrderRequest createOrderRequest = new CreateOrderRequest();
        createOrderRequest.setOrderType(OrderType.RENEW.name());
        createOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
        createOrderRequest.setPaymentMethod(request.getPaymentMethod());
        createOrderRequest.setItems(createRenewTypeOrderItems);
        // api 直接支付
        if (RDSConstant.FROM_API.equals(from)) {
            createOrderRequest.setIsDirectPay(true);
        }

        result = rdsClientFactory.createServiceCatalogOrderClient().createOrder(createOrderRequest);

        for (BaseCreateOrderRequestVo.Item<RenewModel> item : request.getItems()) {
            RenewModel config = item.getConfig();
            // 续费时，若当前实例已在回收站中，需下发开机操作 避免退款&&续费操作时间间隔太短导致的资源状态不一致问题
            InstanceGetResponse instanceGetResponse = rdsClientFactory.createRdsClient2ByInstanceId(config.getInstanceId())
                    .instanceDescribe(config.getInstanceId());
            if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getInstanceStatus()) &&
                    RdsInstanceStatus.recoverableStatus.
                            contains(instanceGetResponse.getInstance().getInstanceStatus())) {
                RDSClient client = rdsClientFactory.createRdsClient();
                client.rebootInstance(config.getInstanceId());
            }
        }
        return result;
    }

    public OrderUuidResult createToPrepayOrder(RdsCreateOrderRequestVo<RenewModel> request) {
        OrderUuidResult result = null;

        try {
            List<CreateRenewTypeOrderItem> createRenewTypeOrderItems = new LinkedList();
            for (RdsCreateOrderRequestVo.Item<RenewModel> item : request.getItems()) {
                RenewModel config = item.getConfig();
                Resource resource = instanceService.getResourceByInstanceId(config.getInstanceId());

                if (resource == null) {
                    throw new RDSExceptions.ResourceNotExistException();
                }
                CreateRenewTypeOrderItem createRenewTypeOrderItem = new CreateRenewTypeOrderItem();
                createRenewTypeOrderItem.setServiceType(resource.getServiceType());
                createRenewTypeOrderItem.setPaymentMethod(item.getPaymentMethod());
                createRenewTypeOrderItem.setProductType(ProductPayType.PRE_PAY.alias);
                createRenewTypeOrderItem.setResourceUuid(resource.getUuid());
                createRenewTypeOrderItem.setDuration(config.getDuration());

                createRenewTypeOrderItems.add(createRenewTypeOrderItem);
            }

            CreateOrderRequest createOrderRequest = new CreateOrderRequest();
            createOrderRequest.setOrderType(OrderType.TO_PREPAY.name());
            createOrderRequest.setRegion(regionConfiguration.getCurrentRegion());
            createOrderRequest.setPaymentMethod(request.getPaymentMethod());
            createOrderRequest.setItems(createRenewTypeOrderItems);

            result = rdsClientFactory.createServiceCatalogOrderClient().createOrder(createOrderRequest);
        } catch (BceInternalResponseException ex) {
            if ("OrderExceptions.ResourceInTaskException".equals(ex.getCode())) {
                throw new RDSExceptions.ResourceInTaskException();
            } else {
                LogicRdsExceptionHandler.handle(ex);
            }
        }
        return result;
    }
//
//    private String convertToString(Map<String, String> subnet) {
//        StringBuilder result = null;
//        for (Map.Entry<String, String> key: subnet.entrySet()) {
//            result.append(key.getKey() + ":" + key.getValue() + ",");
//        }
//        return result.toString().substring(0, result.length() - 1);
//    }

    private void checkProductType(String productType) {
        if (!(Payment.isPrepay(productType)
                || Payment.isPostpay(productType))) {
            throw new RDSExceptions.ProductTypeValidationException();
        }
    }

    private void checkDuration(InstanceCreateModel request) {
        if (Payment.isPrepay(request.getProductType())
                && !RDSConstant.RDS_DURATION_RANGE.contains(request.getDuration())) {
            throw new RDSExceptions.DurationValidationException();
        }
    }

    private void checkEngineAndVersion(String engine, String version, boolean isMaster) {
        if (RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(engine)) {
            if (!RDSConstant.RDS_ENGINE_MYSQL_VERSIONS.contains(version)) {
                throw new RDSExceptions.EngineVersionValidationException();
            }
            // 白名单控制只读实例mysql engine版本
            if (RDSConstant.RDS_ENGINE_MYSQL_VERSION_NEED_WHITELIST.equals(version) && !isMaster
                    && !othersService.isWhiteAccount(null).isMysqlReplica()) {
                throw new RDSExceptions.EngineVersionValidationException();
            }
        } else if (RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(engine)) {
            // 白名单控制是否可以创建sqlserver实例
//            if (!rdsValidator.isWhiteAccount().isSqlserver()) {
//                throw new RDSExceptions.EngineValidationException();
//            }
            if (!RDSConstant.RDS_ENGINE_SQLSERVER_VERSIONS.contains(version)) {
                throw new RDSExceptions.EngineVersionValidationException();
            }
        } else if (RDSConstant.RDS_ENGINE_PG.equalsIgnoreCase(engine)) {
            if (!RDSConstant.RDS_ENGINE_PG_VERSION.contains(version)) {
                throw new RDSExceptions.EngineVersionValidationException();
            }
        } else if (RDSConstant.RDS_ENGINE_PROXY.equalsIgnoreCase(engine)) {
            return;
        } else if (InstanceType.INSTANCE_TYPE_REPLICA.getValue().equalsIgnoreCase(engine)) {
            return;
        } else {
            throw new RDSExceptions.EngineValidationException();
        }
    }

    private void checkInstanceCreateRequestForApiExtra(InstanceCreateModel request) {

        InstanceCreateModel.DashCreateInstance config = request.getInstance();

        String pattern = "^[0-9a-zA-Z\u4E00-\u9FA5-_/.]{1,65}$";

        // tag简单校验，
        // 键：1-65个字符（实际是数字、字母、中文，及-_/.特殊字符）
        // key：1-65个字符（实际是数字、字母、中文，及-_/.特殊字符），不能包含关键字
        List<Tag> tags = config.getTags();
        for (Tag each : tags) {
            String tagKey = each.getTagKey();
            String tagValue = each.getTagValue();

            if (StringUtils.isBlank(tagKey) || StringUtils.isBlank(tagValue)) {
                throw new RDSExceptions.TagFormatException();
            }

            boolean isMatchKey = Pattern.matches(pattern, tagKey);

            if (!isMatchKey) {
                throw new RDSExceptions.TagFormatException();
            }

            boolean isMatchValue = Pattern.matches(pattern, tagValue);

            // value不能包含key
            if (tagValue.contains(tagKey)) {
                isMatchValue = Boolean.FALSE;
            }

            if (!isMatchValue) {
                throw new RDSExceptions.TagFormatException();
            }
        }

        // 自动续费校验
        if (StringUtils.isNotBlank(request.getAutoRenewTimeUnit())) {
            String autoRenewTimeUnit = request.getAutoRenewTimeUnit();

            if (StringUtils.equals(autoRenewTimeUnit, "month")) {
                if (request.getAutoRenewTime() < 0 || request.getAutoRenewTime() > 9) {
                    logger.error("auto renew params error 'autoRenewTime' : {}", request.getAutoRenewTime());
                    throw new RDSExceptions.AutoRenewParamsValidationException();
                }
            } else if (StringUtils.equals(autoRenewTimeUnit, "year")) {
                if (request.getAutoRenewTime() < 0 || request.getAutoRenewTime() > 3) {
                    logger.error("auto renew params error 'autoRenewTime' : {}", request.getAutoRenewTime());
                    throw new RDSExceptions.AutoRenewParamsValidationException();
                }
            } else {
                logger.error("auto renew params error 'autoRenewTimeUnit' : {}", autoRenewTimeUnit);
                throw new RDSExceptions.AutoRenewParamsValidationException();
            }

        }
        request.getAutoRenewTimeUnit();
    }

    private void checkInstanceCreateRequest(InstanceCreateModel request) {
//        InstanceCreateRequest.InstanceParameters config = request.getInstanceParameters();
        InstanceCreateModel.DashCreateInstance config = request.getInstance();
        // 上海金融专区RDS参数校验
        checkRdsFinanceParameter(config);
        InstanceGetResponse masterInstance = null;
        if (StringUtils.isNotEmpty(request.getInstance().getSourceInstanceId())) {
            masterInstance = rdsClientFactory.createRdsClient()
                    .instanceDescribe(request.getInstance().getSourceInstanceId());
            if (InstanceType.INSTANCE_TYPE_REPLICA.getValue().equalsIgnoreCase(config.getEngine())) {
                config.setEngine(masterInstance.getInstance().getEngine());
            }
        }
        // 配额校验
        checkQuota(request, masterInstance);
        // 产品类型校验
        checkProductType(request.getProductType());
        // 时长校验
        checkDuration(request);
        config.setEngine(EngineType.getValue(config.getEngine()));
        checkEngineAndVersion(config.getEngine(), config.getEngineVersion(),
                StringUtils.isEmpty(config.getSourceInstanceId()));
        setCategory(config);

        if (!config.getEngine().equals(RDSConstant.RDS_ENGINE_PROXY)) {
            // 检查内存和磁盘是否满足配置规格
            checkMemoryAndStorage(request);
//            if (config.getCpuCount() != 0
//                    && RDSConstant.MEMORY_CPU_MAP.get(config.getAllocatedMemoryInMB()) != config.getCpuCount()) {
//                throw new RDSExceptions.CpuCountValidationException();
//            } else {
//                config.setCpuCount(RDSConstant.MEMORY_CPU_MAP.get(config.getAllocatedMemoryInMB()));
//            }
            if (StringUtils.isNotEmpty(config.getSourceInstanceId())) {
                // checkReplicaConfig(config.getSourceInstanceId(), config.getAllocatedStorageInGB(), masterInstance);
                if ((request.getInstance().getEdgeVpcId() == null || request.getInstance().getEdgeVpcId().isEmpty())
                        && masterInstance != null
                        && !request.getInstance().getVpcId().equals(masterInstance.getInstance().getVpcId())) {
                    throw new RDSExceptions.VpcIdValidationException();
                }
//                if (masterInstance != null
//                        && !RDSConstant.RDS_ENGINE_MYSQL_CAN_CREATE_REPLICA.contains(
//                                masterInstance.getInstance().getEngineVersion())) {
//                    throw new RDSExceptions.MasterInstanceValidationError();
//                }
            }
        } else {
            if (config.getNodeAmount() == null || !RDSConstant.RDS_PROXY_NODEAMOUNT.contains(config.getNodeAmount())) {
                throw new RDSExceptions.ParamValidationException();
            }
            if (StringUtils.isNotEmpty(request.getInstance().getVpcId())
                    && masterInstance != null
                    && !request.getInstance().getVpcId().equals(masterInstance.getInstance().getVpcId())) {
                throw new RDSExceptions.VpcIdValidationException();
            }
//            if (masterInstance != null
//                    && !RDSConstant.RDS_ENGINE_MYSQL_CAN_CREATE_PROXY
//                    .contains(masterInstance.getInstance().getEngineVersion())) {
//                throw new RDSExceptions.MasterInstanceValidationError();
//            }
        }
//        // 检测克隆实例时原实例存不存在，是不是本用户创建
//        if (config.getInitialDataReference() != null
//                && StringUtils.isNotEmpty(config.getInitialDataReference().getInstanceId())) {
//            try {
//                if (BasisUtils.isShortId(config.getInitialDataReference().getInstanceId())) {
//                    config.getInitialDataReference().setInstanceId(
//                            instanceService.findInsntaceUUidByShortId(
//                                    config.getInitialDataReference().getInstanceId())
//                    );
//                }
//                Instance referInstance = rdsClientFactory.createRdsClient()
//                        .instanceDescribe(config.getInitialDataReference().getInstanceId()).getInstance();
//                if (referInstance == null || StringUtils.isEmpty(referInstance.getInstanceId())) {
//                    throw new RDSExceptions.ResourceNotExistException();
//                }
//            } catch (BceInternalResponseException ex) {
//                if (HttpStatus.FORBIDDEN.value() == ex.getHttpStatus()) {
//                    throw new BackendExceptions.AccessDenied(ex);
//                } else {
//                    throw ex;
//                }
//            }
//        }

        // 检查BLB专属集群
        instanceService.checkExclusiveBLB(config.getBgwGroupId());
    }
    private void setCategory(InstanceCreateModel.DashCreateInstance config) {
        if (StringUtils.isNotEmpty(config.getCategory())) {
            return;
        }
        if (config.getEngineVersion()
                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012)
                || config.getEngineVersion()
                .equals(RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2016)
                || (config.getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER)
                && (config.getIsSingle() != null && config.getIsSingle()))) { // sqlserver单机版
            config.setCategory(RDSConstant.CATEGORY_BASIC);
        } else if (config.getIsEnhanced() != null && config.getIsEnhanced()) {  // MySQL三节点增强版
            config.setCategory(RDSConstant.CATEGORY_ENHANCED);
        } else if (config.getEngineVersion().equals(RDSConstant.RDS_ENGINE_MYSQL)
                && config.getInstanceType() != null && config.getInstanceType().equals(RDSConstant.INSTANCE_TYPE_RDS_SINGLE)) {
            // mysql 单机版
            config.setCategory(RDSConstant.CATEGORY_SINGLETON);
            // pg 单机版
        } else if (config.getEngine().equals(RDSConstant.RDS_ENGINE_PG)
                && config.getInstanceType() != null && config.getInstanceType()
                .equals(RDSConstant.INSTANCE_TYPE_RDS_SINGLE)) {
            config.setCategory(RDSConstant.CATEGORY_SINGLETON);
        } else {
            config.setCategory(RDSConstant.CATEGORY_STANDARD);
        }
    }

//    private void checkReplicaConfig(String instanceId,
//                                    Integer allocatedStorageInGB, InstanceGetResponse masterInstance) {
//        if (masterInstance == null) {
//            masterInstance = rdsClientFactory.createRdsClient().instanceDescribe(instanceId);
//        }
//    }

    private void checkQuota(InstanceCreateModel request, InstanceGetResponse masterInstance) {
        boolean isValid = false;
        String sourceInstanceId = request.getInstance().getSourceInstanceId();
        QuotaValidator quotaValidator = null;
        if (sourceInstanceId == null
                || sourceInstanceId.isEmpty()) {
            quotaValidator = quotaValidatorFactory.getQuotaValidator(ServiceType.RDS);
            isValid = quotaValidator.validate(request.getNumber(), null);
        } else {
            quotaValidator = quotaValidatorFactory.getQuotaValidator(ServiceType.RDS_REPLICA);
            isValid = quotaValidator.validate(request.getNumber(), sourceInstanceId);
        }
        if (!isValid) {
//            throw new RDSExceptions.ExceedQuotaException(quotaValidator.getQuotaType().getDesc());
            throw new RDSBusinessExceptions.ProductExceedQuotaException(quotaValidator.getQuotaType().getDesc());
        }
    }

    private void checkStorageLimitByAz(String logicalZone, int storage, String engine) {
        ZoneClient zoneClient = rdsClientFactory.createZoneClient();
        if (StringUtils.isNotEmpty(logicalZone)) {
            ZoneMapDetail zoneMapDetail = zoneClient.createZoneByLogicalZone(logicalZone);
            if (storageLimitedZones.contains(zoneMapDetail.getPhysicalZone())) {
                if (RDSConstant.RDS_ENGINE_MYSQL.equals(engine)
                        || RDSConstant.RDS_ENGINE_PROXY.equals(engine)
                        || RDSConstant.RDS_ENGINE_PG.equals(engine)) {
                    if (storage > 300) {
                        throw new RDSExceptions.StorageLimitByAzException("MySQL", "300");
                    }
                } else if (RDSConstant.RDS_ENGINE_SQLSERVER.equals(engine)) {
                    if (storage > 250) {
                        throw new RDSExceptions.StorageLimitByAzException("SQL Server", "250");
                    }
                }
            }
        }
    }

    private void checkMemoryAndStorage(InstanceCreateModel request) {

        InstanceCreateModel.DashCreateInstance instance = request.getInstance();

        String engine = request.getInstance().getEngine();
        if (request.getInstance().getAllocatedMemoryInGB() == null
                || request.getInstance().getAllocatedStorageInGB() == null
                || request.getInstance().getCpuCount() == 0) {
            throw new RDSExceptions.ParamValidationException();
        }
        int allocatedMemoryInGB = request.getInstance().getAllocatedMemoryInGB();
        int allocatedStorageInGB = request.getInstance().getAllocatedStorageInGB();
        int cpuCount = request.getInstance().getCpuCount();

        // 检查cpu memory和storage是否在套餐范围内
        FlavorPO flavorPO = new FlavorPO();
        if (StringUtils.isNotEmpty(request.getInstance().getEngine())
                && RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(request.getInstance().getEngine())
                && StringUtils.isNotEmpty(request.getInstance().getCategory())
                && RDSConstant.CATEGORY_SINGLETON.equalsIgnoreCase(request.getInstance().getCategory())) {
            // 若为 sqlserver 单机版，需将传入的 Singleton 转为 Basic 与库中一致
            flavorPO.setApplicationType(RDSConstant.CATEGORY_BASIC);
        } else {
            flavorPO.setApplicationType(request.getInstance().getCategory());
        }
        flavorPO.setCpuCount(cpuCount);
        flavorPO.setMemoryCapacity(allocatedMemoryInGB);
        // 之前没有对磁盘规格进行限制，现加上
        flavorPO.setVolumeCapacity(allocatedStorageInGB);
        flavorPO.setEngine(request.getInstance().getEngine());

        // 此处判断当前创建实例时输入的配置规格是否可以在数据库中匹配到相关数据
        int flavorCountByUniqueKey = flavorMapper.selectFlavorCountByUniqueKey(flavorPO);
        if (flavorCountByUniqueKey == 0) {
            throw new RDSExceptions.CpuCountValidationException();
        }
        // 套餐限制
        flavorPO = flavorMapper.selectFlavorByUniqueKey(flavorPO);
        if (allocatedStorageInGB % 5 != 0 || allocatedStorageInGB < flavorPO.getMinVolumeCapacity()) {
            throw new RDSExceptions.StorageValidationException();
        }
        if (!othersService.checkDiskLimit() && allocatedStorageInGB > flavorPO.getMaxVolumeCapacity()) {
            throw new RDSExceptions.StorageValidationException();
        }
//        if (StringUtils.isNotEmpty(request.getInstance().getAzone())) {
//            for (String zone : request.getInstance().getAzone().split(",")) {
//                ZoneFlavorLimitPO zoneFlavorLimitPO
//                        = flavorMapper.getZoneFlavorLimitPO(zone, rdsClientFactory.getAccountId());
//                if (zoneFlavorLimitPO == null) {
//                    throw new RDSExceptions.ResourceServerException();
//                }
//
//                // 单机版磁盘是云磁盘， 不涉及zone限制
//                if ((!ObjectUtils.equals(RDSConstant.CATEGORY_SINGLETON, instance.getCategory())) // mysql云磁盘
//                        && !ObjectUtils.equals("cloud_nor", instance.getDiskIoType()) // 通用型ssd云磁盘
//                        && !ObjectUtils.equals("cloud_high", instance.getDiskIoType()) // 高性能云磁盘
//                        && !othersService.checkDiskLimit()) { //白名单
//                    if (allocatedStorageInGB > zoneFlavorLimitPO.getMaxVolumeCapacity()) {
//                        throw new RDSExceptions.StorageValidationException();
//                    }
//                } else {
//                    logger.info("[console-rds] is {} type, not limit by zone", RDSConstant.CATEGORY_SINGLETON);
//                }
//
//            }
//        }
    }

    // 上海金融专区RDS参数的校验
    private void checkRdsFinanceParameter(InstanceCreateModel.DashCreateInstance config) {
        // 上海金融专区金融版RDS的判断
        if (config == null) {
            return ;
        }
        if ((config.getIsEnhanced() != null && config.getIsEnhanced()
                && StringUtils.isEmpty(config.getSourceInstanceId())) // isEnhanced为true表示为三节点增强版
                || (StringUtils.isNotEmpty(config.getInstanceType())
                && config.getInstanceType().equalsIgnoreCase("financial"))) { // financial为raft版
            // region判断  上海专区的标识是fsh
            if (!ArrayUtils.contains(financialRegion, regionConfiguration.getCurrentRegion())) {
                throw new RDSExceptions.RegionNotMatchException();
            }
            // 数据库类型和版本号判断
            if (!RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(config.getEngine())) {  // MySQL
                throw new RDSExceptions.EngineValidationException();
            }
            if (!RDSConstant.RDS_FINANCE_ENGINE_MYSQL_VERSIONS.contains(config.getEngineVersion())) { // 5.6，5.7
                throw new RDSExceptions.EngineVersionValidationException();
            }
            // 可用区判断
            if (StringUtils.isEmpty(config.getAzone())) { // 单可用区A
                config.setAzone("zoneA");
            } else if (!config.getAzone().equalsIgnoreCase("zoneA")) {
                throw new RDSExceptions.ParamValidationException();
            }
        }
    }

    public String getServiceType(String engine, String sourceInstanceId) {
        if (StringUtils.isEmpty(sourceInstanceId)) {
            return ServiceType.RDS.toString();
        } else if (engine.equals(RDSConstant.RDS_ENGINE_PROXY)) {
            return ServiceType.RDS_PROXY.toString();
        } else {
            return ServiceType.RDS_REPLICA.toString();
        }
    }

    /**
     * 开启rds
     */
    public void activateService() {
        try {
            // BceServiceRole_console_rds
            IAMClient iamClient = rdsClientFactory.createIamClient();
            rdsIamService.stsServiceRoleActivateWithCheck("RdsDefaultRole",
                    rdsClientFactory.getAccountId(), rdsServicePolicyId, rdsServiceAccountId);

            rdsIamService.stsServiceRoleActivateWithCheck("BceServiceRole_console_rds", rdsClientFactory.getAccountId(),
                    consoleRdsServicePolicyId, consoleRdsServiceAccountId);
        } catch (Exception ex) {
            logger.debug("activateService failed.", ex);
            LogicRdsExceptionHandler.handle(ex);
        }
    }

    private void stsServiceRoleActivateWithCheck() {
        try {
            // 新加坡region不需要抛异常
            if (regionConfiguration.getCurrentRegion().equals("sin")) {
                logger.warn("Current retion is 'sin', don't stsServiceRoleActivate().");
            } else {
                // 为后端服务号激活sts鉴权role
                rdsIamService.stsServiceRoleActivateWithCheck("RdsDefaultRole", rdsClientFactory.getAccountId(),
                        rdsServicePolicyId, rdsServiceAccountId);
            }

            // roleName：BceServiceRole_console_rds；-- console-rds
            // 为console－rds服务号激活sts鉴权role，新订单执行器的代码在console
            rdsIamService.stsServiceRoleActivateWithCheck("BceServiceRole_console_rds", rdsClientFactory.getAccountId(),
                    consoleRdsServicePolicyId, consoleRdsServiceAccountId);
        } catch (Exception ex) {
            logger.warn("set sts failed.", ex);

            // 新加坡region不需要抛异常
            if (regionConfiguration.getCurrentRegion().equals("sin")) {
                logger.warn("Current retion is 'sin', not throws.");
            } else {
                throw new RDSExceptions.AddOrUpdateRoleException();
            }
        }
    }

}
