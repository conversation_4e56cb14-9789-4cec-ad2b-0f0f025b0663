package com.baidu.bce.logic.rds.service.datasync.service;

import com.baidu.bce.internalsdk.order.model.OrderType;
import com.baidu.bce.logic.rds.dao.model.OrderNeedToSyncPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by luping03 on 18/1/9.
 */
@Service
public class OrderSyncServiceFactory {

    @Autowired
    NewOrderCreatingService newOrderCreatingService;

    @Autowired
    NewOrderUnknownService newOrderUnknownService;

    @Autowired
    ResizeOrderCreatingService resizeOrderCreatingService;

    @Autowired
    ResizeOrderUnknownService resizeOrderUnknownService;

    public OrderSyncService getOrderService(OrderNeedToSyncPO needToSyncPO) {
        if (OrderType.RESIZE.name().equalsIgnoreCase(needToSyncPO.getOrderType())) {
            switch (needToSyncPO.getOrderStatus()) {
                case "creating" :
                    return resizeOrderCreatingService;
                case "unknown" :
                    return resizeOrderUnknownService;
                default:
                    return null;
            }
        } else {
            switch (needToSyncPO.getOrderStatus()) {
                case "creating" :
                    return newOrderCreatingService;
                case "unknown" :
                    return newOrderUnknownService;
                default:
                    return null;
            }
        }
    }

}
