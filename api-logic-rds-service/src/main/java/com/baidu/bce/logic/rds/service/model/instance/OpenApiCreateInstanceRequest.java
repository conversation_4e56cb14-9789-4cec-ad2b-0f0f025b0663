package com.baidu.bce.logic.rds.service.model.instance;

import com.baidu.bce.externalsdk.logical.network.common.annotation.TagPermisson;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCreateRequest;
import com.baidu.bce.internalsdk.rds.model.instance.RecoveryToSourceInstanceRequest;
import com.baidu.bce.internalsdk.rds.util.PatternString;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OpenApiCreateInstanceRequest {

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static final class Billing {
        // 付费方式。预付费：Prepaid，后付费：Postpaid
        private String paymentTiming;

        // 保留信息，支付方式为后支付时不需要设置，预支付时必须设置
        private Reservation reservation;

        public String getPaymentTiming() {
            return paymentTiming;
        }

        public void setPaymentTiming(String paymentTiming) {
            this.paymentTiming = paymentTiming;
        }

        public Reservation getReservation() {
            return reservation;
        }

        public void setReservation(Reservation reservation) {
            this.reservation = reservation;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static final class Reservation {
        // 时长，[1,2,3,4,5,6,7,8,9,12,24,36]
        private Integer reservationLength;

        // 时间单位，Month，当前仅支持按月
        private String reservationTimeUnit;

        public Integer getReservationLength() {
            return reservationLength;
        }

        public void setReservationLength(Integer reservationLength) {
            this.reservationLength = reservationLength;
        }

        public String getReservationTimeUnit() {
            return reservationTimeUnit;
        }

        public void setReservationTimeUnit(String reservationTimeUnit) {
            this.reservationTimeUnit = reservationTimeUnit;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static final class SubnetMap {
        private String zoneName;
        private String subnetId;

        public String getZoneName() {
            return zoneName;
        }

        public void setZoneName(String zoneName) {
            this.zoneName = zoneName;
        }

        public String getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(String subnetId) {
            this.subnetId = subnetId;
        }
    }

    private Billing billing;

    private String autoRenewTimeUnit;

    private Integer autoRenewTime;

    private Integer purchaseCount;

    @Pattern(regexp = PatternString.PATTERN_INSTANCE_NAME)
    private String instanceName;

    private String category;

    private String engine;

    private String engineVersion;

    private String characterSetName;

    private Integer cpuCount;

    private Integer memoryCapacity;

    private Integer volumeCapacity;

    private List<String> zoneNames;

    private String vpcId;

    private Boolean isDirectPay;

    private List<SubnetMap> subnets;

    @TagPermisson
    private List<Tag> tags;

    private Boolean relationTag;

    private String sourceInstanceId;

    private Integer nodeAmount;

    private String bgwGroupId;

    private boolean bgwGroupExclusive;

    private InstanceCreateRequest.InitialDataReference initialDataReference;

    private List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> data;

    private String diskType;

    private String cdsType;

    private Integer lowerCaseTableNames;

    private String diskIoType;

    private String parameterTemplateId;

    // Map<edgeRegionId, Count> 每个节点购买的数量
    private Map<String, Integer> edgeRegion;

    private Map<String, String> edgeVpcId;

    private Map<String, String> edgeSubnetId;

    private Integer entryPort;

    private String replicationType;

    private String resourceGroupId;

    private String ovip;

    // 用于控制高可用只读实例
    private String replicaType;

    private String bcmGroupName;

    private String leaderInstanceId;

    private String leaderAppId;

    private String resourceType;

    private Boolean isInheritMasterAuthip;

    private String resourcePlatform;

    private String supportStorageEngine;

    private boolean isDataBackupCopy;

    public boolean getIsDataBackupCopy() {
        return isDataBackupCopy;
    }

    public void setIsDataBackupCopy(boolean dataBackupCopy) {
        isDataBackupCopy = dataBackupCopy;
    }

    public String getResourcePlatform() {
        return resourcePlatform;
    }

    public void setResourcePlatform(String resourcePlatform) {
        this.resourcePlatform = resourcePlatform;
    }

    public String getSupportStorageEngine() {
        return supportStorageEngine;
    }

    public void setSupportStorageEngine(String supportStorageEngine) {
        this.supportStorageEngine = supportStorageEngine;
    }

    public Boolean getIsInheritMasterAuthip() {
        return isInheritMasterAuthip;
    }

    public void setIsInheritMasterAuthip(Boolean inheritMasterAuthip) {
        isInheritMasterAuthip = inheritMasterAuthip;
    }

    public String getLeaderInstanceId() {
        return leaderInstanceId;
    }

    public void setLeaderInstanceId(String leaderInstanceId) {
        this.leaderInstanceId = leaderInstanceId;
    }

    public String getLeaderAppId() {
        return leaderInstanceId;
    }

    public void setLeaderAppId(String leaderAppId) {
        this.leaderAppId = leaderInstanceId;
    }



    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getBcmGroupName() {
        return bcmGroupName;
    }

    public void setBcmGroupName(String bcmGroupName) {
        this.bcmGroupName = bcmGroupName;
    }

    public String getReplicaType() {
        return replicaType;
    }

    public void setReplicaType(String replicaType) {
        this.replicaType = replicaType;
    }

    public Map<String, Integer> getEdgeRegion() {
        return edgeRegion;
    }

    public void setEdgeRegion(Map<String, Integer> edgeRegion) {
        this.edgeRegion = edgeRegion;
    }

    public Map<String, String> getEdgeVpcId() {
        return edgeVpcId;
    }

    public void setEdgeVpcId(Map<String, String> edgeVpcId) {
        this.edgeVpcId = edgeVpcId;
    }

    public Map<String, String> getEdgeSubnetId() {
        return edgeSubnetId;
    }

    public void setEdgeSubnetId(Map<String, String> edgeSubnetId) {
        this.edgeSubnetId = edgeSubnetId;
    }

    public InstanceCreateRequest.InitialDataReference getInitialDataReference() {
        return initialDataReference;
    }

    public void setInitialDataReference(InstanceCreateRequest.InitialDataReference initialDataReference) {
        this.initialDataReference = initialDataReference;
    }

    public boolean isBgwGroupExclusive() {
        if (StringUtils.isNotBlank(bgwGroupId)) {
            return true;
        }
        return bgwGroupExclusive;
    }

    public void setBgwGroupExclusive(boolean bgwGroupExclusive) {
        this.bgwGroupExclusive = bgwGroupExclusive;
    }

    public String getBgwGroupId() {
        return bgwGroupId;
    }

    public void setBgwGroupId(String bgwGroupId) {
        this.bgwGroupId = bgwGroupId;
    }

    public Integer getNodeAmount() {
        return nodeAmount;
    }

    public void setNodeAmount(Integer nodeAmount) {
        this.nodeAmount = nodeAmount;
    }

    public String getSourceInstanceId() {
        return sourceInstanceId;
    }

    public void setSourceInstanceId(String sourceInstanceId) {
        this.sourceInstanceId = sourceInstanceId;
    }

    public Billing getBilling() {
        return billing;
    }

    public void setBilling(Billing billing) {
        this.billing = billing;
    }

    public String getAutoRenewTimeUnit() {
        return autoRenewTimeUnit;
    }

    public void setAutoRenewTimeUnit(String autoRenewTimeUnit) {
        this.autoRenewTimeUnit = autoRenewTimeUnit;
    }

    public Integer getAutoRenewTime() {
        return autoRenewTime;
    }

    public void setAutoRenewTime(Integer autoRenewTime) {
        this.autoRenewTime = autoRenewTime;
    }

    public Integer getPurchaseCount() {
        return purchaseCount;
    }

    public void setPurchaseCount(Integer purchaseCount) {
        this.purchaseCount = purchaseCount;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getEngineVersion() {
        return engineVersion;
    }

    public void setEngineVersion(String engineVersion) {
        this.engineVersion = engineVersion;
    }

    public String getCharacterSetName() {
        return characterSetName;
    }

    public void setCharacterSetName(String characterSetName) {
        this.characterSetName = characterSetName;
    }

    public Integer getCpuCount() {
        return cpuCount;
    }

    public void setCpuCount(Integer cpuCount) {
        this.cpuCount = cpuCount;
    }

    public Integer getMemoryCapacity() {
        return memoryCapacity;
    }

    public void setMemoryCapacity(Integer memoryCapacity) {
        this.memoryCapacity = memoryCapacity;
    }

    public Integer getVolumeCapacity() {
        return volumeCapacity;
    }

    public void setVolumeCapacity(Integer volumeCapacity) {
        this.volumeCapacity = volumeCapacity;
    }

    public List<String> getZoneNames() {
        return zoneNames;
    }

    public void setZoneNames(List<String> zoneNames) {
        this.zoneNames = zoneNames;
    }

    public String getVpcId() {
        return vpcId;
    }

    public void setVpcId(String vpcId) {
        this.vpcId = vpcId;
    }

    public Boolean getIsDirectPay() {
        return isDirectPay;
    }

    public void setIsDirectPay(Boolean isDirectPay) {
        this.isDirectPay = isDirectPay;
    }

    public List<SubnetMap> getSubnets() {
        return subnets;
    }

    public void setSubnets(List<SubnetMap> subnets) {
        this.subnets = subnets;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Boolean getRelationTag() {
        return relationTag;
    }

    public void setRelationTag(Boolean relationTag) {
        this.relationTag = relationTag;
    }

    public String getDiskType() {
        return diskType;
    }

    public void setDiskType(String diskType) {
        this.diskType = diskType;
    }

    public String getCdsType() {
        return cdsType;
    }

    public void setCdsType(String cdsType) {
        this.cdsType = cdsType;
    }

    public Integer getLowerCaseTableNames() {
        return lowerCaseTableNames;
    }

    public void setLowerCaseTableNames(Integer lowerCaseTableNames) {
        this.lowerCaseTableNames = lowerCaseTableNames;
    }

    public String getDiskIoType() {
        return diskIoType;
    }

    public void setDiskIoType(String diskIoType) {
        this.diskIoType = diskIoType;
    }

    public List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> getData() {
        return data;
    }

    public void setData(List<RecoveryToSourceInstanceRequest.RecoveryToSourceInstanceModel> data) {
        this.data = data;
    }

    public String getParameterTemplateId() {
        return parameterTemplateId;
    }

    public void setParameterTemplateId(String parameterTemplateId) {
        this.parameterTemplateId = parameterTemplateId;
    }

    public Integer getEntryPort() {
        return entryPort;
    }

    public void setEntryPort(Integer entryPort) {
        this.entryPort = entryPort;
    }

    public String getReplicationType() {
        return replicationType;
    }

    public void setReplicationType(String replicationType) {
        this.replicationType = replicationType;
    }

    public String getResourceGroupId() {
        return resourceGroupId;
    }

    public void setResourceGroupId(String resourceGroupId) {
        this.resourceGroupId = resourceGroupId;
    }

    public String getOvip() {
        return ovip;
    }

    public void setOvip(String ovip) {
        this.ovip = ovip;
    }
}
