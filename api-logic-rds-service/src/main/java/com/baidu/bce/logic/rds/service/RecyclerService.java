package com.baidu.bce.logic.rds.service;


import com.baidu.bce.billing.resourcemanager.service.ChargeResourceService;
import com.baidu.bce.billing.resourcemanager.service.request.ReleaseRequest;
import com.baidu.bce.console.home.service.util.HomeServiceRequestContext;
import com.baidu.bce.console.settings.service.UserSettingsService;
import com.baidu.bce.fbi.common.exception.FbiException;
import com.baidu.bce.finance.service.FinanceServiceV3;
import com.baidu.bce.finance.service.FinanceV2ClientFactory;
import com.baidu.bce.finance.service.model.PurchaseValidationResponse;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.ResourceClientV2;
import com.baidu.bce.internalsdk.order.model.ExtendedGetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.GetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.ManualOpResponse;
import com.baidu.bce.internalsdk.order.model.ProductPayType;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.ResourceKey;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceListResponse;
import com.baidu.bce.internalsdk.rds.model.security.OpenApiSecurityGroup;
import com.baidu.bce.internalsdk.rds.model.security.OpenApiSecurityGroupResponse;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupUpdate;
import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.logic.core.authentication.IamLogicService;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.service.constant.RdsInstanceStatus;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.ListRequest;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.resource.ResourceGroupService;
import com.baidu.bce.logic.rds.service.util.InstanceForApiUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.plat.webframework.iam.service.IAMService;
import com.baidu.bce.plat.webframework.model.edp.EdpPageResultResponse;
import endpoint.EndpointManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;


@Service
public class RecyclerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecyclerService.class);

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private RdsAsyncService rdsAsyncService;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private ResourceGroupService resourceGroupService;

    @Autowired
    private IdMapperService idMapperService;

    @Autowired
    InstanceForApiUtils instanceForApiUtils;

    @Autowired
    RegionConfiguration regionConfiguration;

    @Autowired
    private IamLogicService iamLogicService;

    @Autowired
    FinanceV2ClientFactory financeV2ClientFactory;

    @Autowired
    SecurityGroupService securityGroupService;

    private String getFinanceV3Endpoint() {
        return EndpointManager.getEndpoint("FinanceV3");
    }

    private FinanceServiceV3 getFinanceServiceV3Client() {
        // StsCredential sts = iamService.getStsCredential();
        StsCredential sts = iamLogicService.getUserStsAccessKey(LogicUserService.getUserId());
        FinanceServiceV3 client =  financeV2ClientFactory.createFinanceClient(this.getFinanceV3Endpoint(),
                sts.getAccessKeyId(), sts.getSecretAccessKey(), sts.getSessionToken());
        return client;
    }


    /**
     * 产品服务购买校验
     *
     * @param serviceType
     * @param productType
     * @param region
     * @return
     */
    public PurchaseValidationResponse accountPurchaseValidation(
            String serviceType, String productType, String region) throws FbiException {
        return getFinanceServiceV3Client().purchaseValidation(serviceType, region, productType);
    }

    public Resource getResourceByInstanceId(String instanceId) {
        return getResourceByInstanceId(clientFactory.getAccountId(), instanceId);
    }

    /**
     * 根据实例ID查询对应的资源，若资源不存在，则抛出异常。
     * 该方法查询条件中设置了资源名，故正常情况下，返回结果只有一条。
     *
     * @param accountId  账号ID
     * @param instanceId 实例ID，
     * @return 资源
     */
    public Resource getResourceByInstanceId(String accountId, String instanceId) {
        ResourceClient resourceClient = clientFactory.createResourceClientWithAccountId(accountId);
        ExtendedGetResourcesRequest extendedGetResourcesRequest = new ExtendedGetResourcesRequest();
        extendedGetResourcesRequest.setAccountId(accountId);
        extendedGetResourcesRequest.setServiceTypeList(Collections.singletonList(RDSConstant.SERVICE_NAME));
        extendedGetResourcesRequest.setRegion(regionConfiguration.getCurrentRegion());
        extendedGetResourcesRequest.setLimit(Integer.MAX_VALUE);
        extendedGetResourcesRequest.setName(instanceId);
        Resources resources = resourceClient.queryList(extendedGetResourcesRequest);
        Resource resource = null;
        if (resources != null) {
            for (Resource item : resources) {
                if (item.getName().equals(instanceId)) {
                    resource = item;
                    break;
                }
            }
        }
        if (resource == null) {
            throw new RDSExceptions.ResourceNotExistException();
        }
        return resource;
    }

    public Resources getResourceList() {
        Resources resources;
        try {
            GetResourcesRequest resourcesRequest = new GetResourcesRequest();
            resourcesRequest.setAccountId(clientFactory.getAccountId());
            resourcesRequest.setServiceType(ServiceType.RDS.toString());
            resourcesRequest.setRegion(UserSettingsService.getRegion());
            resourcesRequest.setLimit(Integer.MAX_VALUE);
            ResourceClient resourceClient = clientFactory.createResourceClient();
            resources = resourceClient.list(resourcesRequest);

            return resources;
        } catch (BceInternalResponseException ex) {
            LOGGER.warn("rds get resource list fail! {}", ex);
            throw new RDSExceptions.ResourceNotExistException();
        }
    }

    public LogicMarkerResultResponse<InstanceAbstract> listForPageByMarker(List<InstanceAbstract> instanceAbstracts,
                                                                           String marker,
                                                                           int maxKeys) {
        LogicMarkerResultResponse<InstanceAbstract> response = new LogicMarkerResultResponse<>();
        response.setMarker(marker);
        response.setMaxKeys(maxKeys);

        if (instanceAbstracts == null || instanceAbstracts.size() == 0) {
            return response;
        }
        if (StringUtils.isEmpty(marker) || maxKeys < 1) {
            return response;
        }
        String nextMarker;
        int start = 0;
        int end = 0;
        int masterCount = 0;
        int markerIndex = 1;
        boolean find = false;
        if ("-1".equals(marker)) {
            find = true;
        }
        InstanceAbstract instance;
        for (int index = 0; index < instanceAbstracts.size(); index++) {
            instance = instanceAbstracts.get(index);
            if (StringUtils.isEmpty(instance.getSourceInstanceId())) {
                masterCount++;
                if ((!"-1".equals(marker)) && instance.getInstanceShortId().equals(marker)) {
                    start = index;
                    find = true;
                    markerIndex = masterCount;
                }
                if (masterCount == markerIndex + maxKeys) {
                    end = index;
                }
            }
        }
        if (!find) {
            response.setIsTruncated(false);
            return response;
        } else if (end == 0) {
            end = instanceAbstracts.size();
            response.setIsTruncated(false);
        } else {
            nextMarker = instanceAbstracts.get(end).getInstanceShortId();
            response.setNextMarker(nextMarker);
            response.setIsTruncated(true);
        }
        LOGGER.info("startIndex : " + start + " ,endIndex : " + end);
        List<InstanceAbstract> instanceForPage = instanceAbstracts.subList(start, end);

        response.setResult(instanceForPage);
        return response;
    }


    public EdpPageResultResponse<InstanceAbstract> getRecyclerInstances(ListRequest listRequest) {

        EdpPageResultResponse<InstanceAbstract> result = new EdpPageResultResponse<>();
//        CommonPageResult<InstanceAbstract> result = new CommonPageResult<>();
        result.getPage().setOrderBy(listRequest.getOrderBy());
        result.getPage().setOrder(listRequest.getOrder());
        result.getPage().setPageNo(listRequest.getPageNo());
        result.getPage().setPageSize(listRequest.getPageSize());

        // RDSClient rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2);
        RDSClient2 rdsClient = clientFactory.createRdsClient2();


        InstanceListResponse recyclerInstances = rdsClient.getRecyclerInstances();
        List<InstanceAbstract> instances = null;
        instances = instanceService.bindTagsToInstance(recyclerInstances.getInstances());

        // 匹配短id
        List<String> longIds = new ArrayList<>();
        for (InstanceAbstract instance : instances) {
            longIds.add(instance.getInstanceId());
            if (StringUtils.isNotEmpty(instance.getSourceInstanceId())) {
                longIds.add(instance.getSourceInstanceId());
            }
        }
        Map<String, String> idMap = idMapperService.idMapByLongId(longIds);
        for (InstanceAbstract instance : instances) {
            String shortId = idMap.get(instance.getInstanceId());
            if (StringUtils.isEmpty(shortId)) {
                LOGGER.warn("api list can not find shortId,instanceUuid:{}", instance.getInstanceId());
                continue;
            }
            instance.setInstanceShortId(shortId);
        }


        if (instances == null || instances.size() == 0) {
            result.getPage().setTotalCount(0);
            return result;
        }

        List<InstanceAbstract> displayClusters = new ArrayList<>();
        // 获取resource
        Future<Map<String, Resource>> resourceHashMapFuture;
        Map<String, Resource> resourceHashMap;
        resourceHashMapFuture = getStatusResourceMap(null);

        try {
            resourceHashMap = resourceHashMapFuture.get();
        } catch (Exception e) {
            LOGGER.warn("error when get rds resources from order", e);
            resourceHashMap = new HashMap<>();
        }

        if (MapUtils.isNotEmpty(resourceHashMap)) {
            for (InstanceAbstract cache : instances) {
                String resourceKey = cache.getInstanceId();
                Resource resource = resourceHashMap.get(resourceKey);
                if (resource == null) {
                    continue;
                }
                cache.setProductType(resource.getProductType());
                // 支持根据实例名称、实例id过滤
                List<ListRequest.Filter> filters = listRequest.getFilters();
                if (instanceService.isFiltered(cache, filters)) {
                    continue;
                }
                displayClusters.add(cache);
            }
        } else {
            LOGGER.warn("no resources");
        }

        // 更换为过滤后的实例列表
        instances = displayClusters;
        displayClusters = null;

        if (instances.size() == 0) {
            result.getPage().setTotalCount(0);
            return result;
        }

        result.getPage().setTotalCount(instances.size());

        int startIndex = (result.getPage().getPageNo() - 1) * result.getPage().getPageSize();
        int endIndex = Math.min(result.getPage().getPageNo() * result.getPage().getPageSize(), instances.size());
        LOGGER.debug("pageNo = " + result.getPage().getPageNo() + "\tpageSize = " + result.getPage().getPageSize());
        if (startIndex < endIndex) {
            instances = instances.subList(startIndex, endIndex);
        } else {
            instances.clear();
        }
        // instanceService.packTags(instances);
        LOGGER.debug("instance.size() = " + instances.size());
        result.getPage().setResult(instances);
        return result;
    }

    private Future<Map<String, Resource>> getStatusResourceMap(ResourceStatus status) {
        GetResourcesRequest resourcesRequest = new GetResourcesRequest();
        resourcesRequest.setAccountId(clientFactory.getUserId());
        resourcesRequest.setServiceType(ServiceType.RDS.toString());
        resourcesRequest.setRegion(HomeServiceRequestContext.getRegion());
        resourcesRequest.setLimit(1000);
        if (status != null) {
            resourcesRequest.setStatus(status.name());
        }
        ResourceClient resourceClient = clientFactory.createResourceClient();
        return rdsAsyncService.resourceList(resourceClient, resourcesRequest, BceInternalRequest.getThreadRequestId());
    }

    private Map<String, InstanceAbstract> getRecyclerClusterMap() {
        // RDSClient rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2);
        RDSClient2 rdsClient = clientFactory.createRdsClient2();
        InstanceListResponse recyclerInstances1 = rdsClient.getRecyclerInstances();
        LOGGER.info("recycler list size : {}", recyclerInstances1.getInstances().size());
        List<InstanceAbstract> recyclerInstances = null;
        recyclerInstances = instanceService.bindTagsToInstance(recyclerInstances1.getInstances());

        if (recyclerInstances == null || recyclerInstances.size() == 0) {
            return Collections.emptyMap();
        }

        Map<String, InstanceAbstract> recyclerMap = new ConcurrentHashMap<>(recyclerInstances.size());
        for (InstanceAbstract instanceAbstract : recyclerInstances) {
            recyclerMap.put(instanceAbstract.getInstanceId(), instanceAbstract);
        }
        LOGGER.info("recyclerMap result size is  : {}", recyclerMap.size());
        return recyclerMap;
    }



    public void recoverInstance(List<String> instanceIds) {

        Map<String, Resource> resourceMap = new HashMap<>();


        for (String instanceId : instanceIds) {
            Resource resource = getResourceByInstanceId(instanceId);
            resourceMap.put(instanceId, resource);
        }

        Map<String, InstanceAbstract> recyclerMap = getRecyclerClusterMap();
        // 检验Id和状态 处于隔离态的实例可以进行开机操作
        for (String instanceId : instanceIds) {
            InstanceAbstract instanceAbstract = recyclerMap.get(instanceId);
            if (instanceAbstract != null) {
                LOGGER.info("instanceAbstract detail is : Id : {}, status : {}" , instanceAbstract.getInstanceId(),
                        instanceAbstract.getInstanceStatus());
                if (RdsInstanceStatus.recoverableStatus != null &&
                        !RdsInstanceStatus.recoverableStatus.contains(instanceAbstract.getInstanceStatus())) {
                    throw new RDSExceptions.NotSupportOperation(); // 所恢复的实例状态 异常
                }
            } else {
                LOGGER.error("The current instance {} does not belong to recycle list.", instanceId);
                throw new RDSBusinessExceptions.InstanceNotBelongRecycleException(instanceId);
            }
        }

        RDSClient client = clientFactory.createRdsClient();

        List<String> prePayIds = new ArrayList<>();
        List<String> failedIds = new ArrayList<>();
        ResourceClientV2 resourceClientV2 = null;
        Map<String, ResourceKey> postPayResourceMap = new HashMap<>();


        for (String instanceId : instanceIds) {
            InstanceAbstract cache = recyclerMap.get(instanceId);
            // resource使用id
            Resource resource = resourceMap.get(cache.getInstanceId());
            if (resource != null) {
                // 预付费需要通过续费才能恢复
                if (!ProductPayType.PRE_PAY.alias.equals(resource.getProductType())) {

                    // 判断当前后付费实例所在账户是否欠费
                    String chargeType = cache.getProductType() ==
                            null ? resource.getProductType() : cache.getProductType();
                    String region = cache.getRegion();
                    String productType = RDSConstant.SERVICE_NAME;
                    PurchaseValidationResponse validationResponse = null;
                    try {
                        validationResponse =
                                getFinanceServiceV3Client().purchaseValidation(productType, region, chargeType);
                        if (!validationResponse.getStatus()) {
                            // 当前不可以新购
                            LOGGER.info("instanceId:{} is not allowed recover because of INSUFFICIENT or DEBT",
                                    cache.getInstanceId());
                            throw new RDSBusinessExceptions.InsufficientBalanceException();
                        }
                    } catch (FbiException e) {
                        // 此时实例不可正常开机 账户欠费
                        if (!validationResponse.getStatus()) {
                            LOGGER.info("instanceId:{} is not allowed recover because of INSUFFICIENT or DEBT",
                                    cache.getInstanceId());
                            throw new RDSBusinessExceptions.InsufficientBalanceException();
                        }
                    }

                    // 后付费加入启服列表
                    ResourceKey resourceKey = new ResourceKey();
                    resourceKey.setServiceType(resource.getServiceType());
                    resourceKey.setRegion(resource.getRegion());
                    resourceKey.setResourceName(resource.getName());
                    postPayResourceMap.put(instanceId, resourceKey);
                } else {
                    prePayIds.add(instanceId);
                }
            }


            if (CollectionUtils.isNotEmpty(prePayIds)) {
                throw new RDSExceptions.PrepayNeedRenewException(prePayIds);
            }

            // 后付费实例批量手动启服
            for (Map.Entry<String, ResourceKey> entry : postPayResourceMap.entrySet()) {
                String showId = entry.getKey();

                ManualOpResponse opResponse = null;
                try {
                    if (resourceClientV2 == null) {
                        resourceClientV2 = clientFactory.createResourceClientV2();
                    }
                    opResponse = resourceClientV2.manualStart(entry.getValue());
                    if (!opResponse.isSuccess()) {
                        throw new BceInternalResponseException(opResponse.getReason());
                    }
                } catch (BceInternalResponseException e) {
                    LOGGER.error("recover resource error, resource uuid:{}, instanceId:{}",
                            entry.getValue().getResourceName(), showId);
                    if (!opResponse.isSuccess()) {
                        throw new BceInternalResponseException(opResponse.getReason());
                    }
                    failedIds.add(showId);
                    throw new RDSBusinessExceptions.
                            ReleaseInstanceException(showId);
                }

                try {
                    // rds后端
                    client.rebootInstance(showId);
                } catch (BceInternalResponseException e) {
                    LOGGER.error("Stop rds error, instanceId:{}", showId);
                }

            }
        }
    }

    public void deleteIsolatedInstance(String instanceId) {
        Map<String, Resource> resourceMap = new HashMap<>();
        ResourceClient resourceClient = clientFactory.createResourceClient();
//        try {
//            resourceMap = instanceService.getResourceMap().get();
//        } catch (Exception e) {
//            LOGGER.error("Get resource error", e);
//            throw new RDSExceptions.ResourceServerException();
//        }
        Resource resource1 = getResourceByInstanceId(instanceId);
        resourceMap.put(instanceId,  resource1);
        ChargeResourceService chargeResourceService = clientFactory.createChargeResourceService();
        ReleaseRequest releaseRequest = new ReleaseRequest();
        releaseRequest.setServiceType(resource1.getServiceType());
        releaseRequest.setAccountId(LogicUserService.getAccountId());
        releaseRequest.setName(instanceId);
        releaseRequest.setRegion(regionConfiguration.getCurrentRegion());

        Map<String, InstanceAbstract> recyclerMap = getRecyclerClusterMap();
        // 检验Id和状态 处于隔离态的实例可以进行删除操作
        InstanceAbstract instanceAbstract = recyclerMap.get(instanceId);
        LOGGER.info("instanceAbstract detail is : Id : {}, status : {}" , instanceAbstract.getInstanceId(),
                instanceAbstract.getInstanceStatus());
        if ((instanceAbstract != null &&
                !RdsInstanceStatus.recoverableStatus.contains(instanceAbstract.getInstanceStatus()))
                || (resourceMap.containsKey(instanceId) &&
                !ResourceStatus.STOPPED.equals(resourceMap.get(instanceId).getStatus()))) {
            throw new RDSBusinessExceptions.InstanceNotSatisfiableException(); // 所恢复的实例状态 异常

        }


        RDSClient client = clientFactory.createRdsClient();

        InstanceAbstract cache = recyclerMap.get(instanceId);
        // 用户同步资源
        List<Instance> instanceGroup = new ArrayList<>();
        instanceGroup.add(cache);
        // resource使用id
        Resource resource = resourceMap.get(cache.getInstanceId());

        // 若实例已绑定安全组的话，需要同步释放下安全组
        afterHandlerSecurityGroup(instanceId, LogicUserService.getAccountId());

        if (resource != null) {
            try {
                chargeResourceService.release(releaseRequest);
                // resourceClient.delete(resource.getUuid());
            } catch (BceInternalResponseException e) {
                LOGGER.error("Delete resource error, resource uuid:{}, instanceId:{}",
                        resource.getUuid(), instanceId);
                // 删除资源出错的情况下不删除后端，否则会资源泄露
            }
        }

        try {
            // scs后端使用showId
            client.deleteIsolatedInstance(instanceId);
        } catch (BceInternalResponseException e) {
            LOGGER.error("Delete rds error, instanceId:{}", instanceId);
        }


        // 同步删除的资源信息
        String accountId = clientFactory.getAccountId();

        instanceService.changeResource(
                instanceGroup,
                RDSConstant.SYNC_RES_MANAGER_DELETE,
                accountId
                );

//        resourceGroupService.syncResource(
//                RDSConstant.SYNC_RES_MANAGER_DELETE,
//                instanceId,
//                accountId,
//                resource.getServiceType()
//        );
    }

    public void afterHandlerSecurityGroup(String instanceId, String accountId) {
        // 查询当前实例下的安全组列表
        OpenApiSecurityGroupResponse securityGroupResponse = securityGroupService.listSecurityGroup(instanceId, accountId);
        if (securityGroupResponse != null && securityGroupResponse.getGroups().isEmpty()) {
            LOGGER.info("当前实例未绑定安全组，无需在销毁前操作解绑，实例 ID 为 {}", instanceId);
        }
        if (securityGroupResponse != null && !securityGroupResponse.getGroups().isEmpty()) {
            // 解绑对应的安全组
            List<String> groupId = new ArrayList<>();
            for (OpenApiSecurityGroup securityGroup : securityGroupResponse.getGroups()) {
                groupId.add(securityGroup.getSecurityGroupId());
            }
            SecurityGroupUpdate securityGroupUpdate = new SecurityGroupUpdate();
            securityGroupUpdate.setInstanceId(instanceId);
            securityGroupUpdate.setSecurityGroupIds(groupId);
            securityGroupService.unbindSecurityGroup(securityGroupUpdate, accountId, "");
        }
    }

    public void deleteBatchInstances(List<String> instanceIds) {

        Map<String, Resource> resourceMap = new HashMap<>();
        ResourceClient resourceClient = clientFactory.createResourceClient();
//        try {
//            resourceMap = instanceService.getResourceMap().get();
//        } catch (Exception e) {
//            LOGGER.error("Get resource error", e);
//            throw new RDSExceptions.ResourceServerException();
//        }

        for (String instanceId : instanceIds) {
            Resource resource = getResourceByInstanceId(instanceId);
            resourceMap.put(instanceId, resource);
        }
        Map<String, InstanceAbstract> recyclerMap = getRecyclerClusterMap();
        // 检验Id和状态 处于隔离态的实例可以进行删除操作
        for (String instanceId : instanceIds) {
            InstanceAbstract instanceAbstract = recyclerMap.get(instanceId);
            LOGGER.info("instanceAbstract detail is : Id : {}, status : {}" , instanceAbstract.getInstanceId(),
                    instanceAbstract.getInstanceStatus());
            if ((instanceAbstract != null &&
                    !RdsInstanceStatus.recoverableStatus.contains(instanceAbstract.getInstanceStatus()))
                    || (resourceMap.containsKey(instanceId) &&
                        !ResourceStatus.STOPPED.equals(resourceMap.get(instanceId).getStatus()))) {
                    throw new RDSBusinessExceptions.InstanceNotSatisfiableException(); // 所恢复的实例状态 异常

            }
        }
        List<Instance> instanceGroup = new ArrayList<>();
        BceInternalResponseException lastException = null;
        RDSClient client = clientFactory.createRdsClient();
        for (String showId : instanceIds) {
            InstanceAbstract cache = recyclerMap.get(showId);
            // 同步删除资源使用
            instanceGroup.add(cache);


            // resource使用id
            Resource resource = resourceMap.get(cache.getInstanceId());
            ChargeResourceService chargeResourceService = clientFactory.createChargeResourceService();
            ReleaseRequest releaseRequest = new ReleaseRequest();
            releaseRequest.setServiceType(resource.getServiceType());
            releaseRequest.setAccountId(LogicUserService.getAccountId());
            releaseRequest.setName(showId);
            releaseRequest.setRegion(regionConfiguration.getCurrentRegion());

            // 销毁时同步解绑安全组
            afterHandlerSecurityGroup(showId, LogicUserService.getAccountId());

            if (resource != null) {
                try {
//                    resourceClient.delete(resource.getUuid());
                    chargeResourceService.release(releaseRequest);
                } catch (BceInternalResponseException e) {
                    lastException = e;
                    LOGGER.error("Delete resource error, resource uuid:{}, instanceId:{}",
                            resource.getUuid(), showId);
                    // 删除资源出错的情况下不删除后端，否则会资源泄露
                    continue;
                }
            }

            try {
                // 调用平台侧删除接口
                if (StringUtils.isNotEmpty(cache.getEngine())
                        && !RDSConstant.RDS_ENGINE_MYSQL.equalsIgnoreCase(cache.getEngine())) {
                    client.deleteNonMySQLIsolatedInstance(showId);
                } else {
                    client.deleteIsolatedInstance(showId);
                }
            } catch (BceInternalResponseException e) {
                lastException = e;
                LOGGER.error("Delete rds error, instanceId:{}", showId);
            }



            // 同步删除的资源信息
            String accountId = clientFactory.getAccountId();

            instanceService.changeResource(
                    instanceGroup,
                    RDSConstant.SYNC_RES_MANAGER_DELETE,
                    accountId
            );

//            resourceGroupService.syncResource(
//                    RDSConstant.SYNC_RES_MANAGER_DELETE,
//                    showId,
//                    accountId,
//                    resource.getServiceType()
//            );
        }
        if (lastException != null) {
            throw lastException;
        }
    }

    public LogicMarkerResultResponse<InstanceAbstract> getRecyclerInstancesFromAPI(String marker, Integer maxKeys) {

        RDSClient2 rdsClient = clientFactory.createRdsClient2();


        InstanceListResponse recyclerInstances = rdsClient.getRecyclerInstances();
        List<InstanceAbstract> instances = null;
        instances = instanceService.bindTagsToInstance(recyclerInstances.getInstances());

        // 匹配短id
        List<String> longIds = new ArrayList<>();
        for (InstanceAbstract instance : instances) {
            longIds.add(instance.getInstanceId());
            if (StringUtils.isNotEmpty(instance.getSourceInstanceId())) {
                longIds.add(instance.getSourceInstanceId());
            }
        }
        Map<String, String> idMap = idMapperService.idMapByLongId(longIds);
        for (InstanceAbstract instance : instances) {
            String shortId = idMap.get(instance.getInstanceId());
            if (StringUtils.isEmpty(shortId)) {
                LOGGER.warn("api list can not find shortId,instanceUuid:{}", instance.getInstanceId());
                continue;
            }
            instance.setInstanceShortId(shortId);
        }


        // 获取resource
//        Future<Map<String, Resource>> resourceHashMapFuture;
//        Map<String, Resource> resourceHashMap;
//        resourceHashMapFuture = getStatusResourceMap(null);
//
//        try {
//            resourceHashMap = resourceHashMapFuture.get();
//        } catch (Exception e) {
//            LOGGER.warn("error when get rds resources from order", e);
//            resourceHashMap = new HashMap<>();
//        }
        Resources resources = getResourceList();

        for (Resource resource : resources) {
            for (InstanceAbstract instanceAbstract : instances) {
                if (instanceAbstract.getInstanceId().equals(resource.getName())) {
                    // 预付费/后付费
                    instanceAbstract.setProductType(resource.getProductType());
                }
            }
        }

        LogicMarkerResultResponse<InstanceAbstract> response = new LogicMarkerResultResponse<>();
        response.setMarker(marker);
        response.setMaxKeys(maxKeys);
        if (instances.size() == 0) {
            // false则表示最后一页
            response.setIsTruncated(false);
            // 获取下一页所需要传递的marker值，当isTruncated为false时，该域不出现
            response.setNextMarker(null);
            response.setResult(new ArrayList<InstanceAbstract>(0));
            return response;
        }


        if (marker == null || marker.isEmpty()) {
            marker = instances.get(0).getInstanceShortId();
        }
        if (maxKeys == null || maxKeys > 1000 || maxKeys == 0) {
            maxKeys = 1000;
        }

        response.setMarker(marker);
        response.setMaxKeys(maxKeys);

        Iterator<InstanceAbstract> iter = instances.iterator();
        while (iter.hasNext()) {
            InstanceAbstract next = iter.next();
            // 展示需从当前marker开始
            if (next.getInstanceShortId().equals(marker)) {
                break;
            }
            iter.remove();
        }

        if (instances.size() <= maxKeys) {
            // false则表示最后一页
            response.setIsTruncated(false);
            // 获取下一页所需要传递的marker值，当isTruncated为false时，该域不出现
            response.setNextMarker(null);
            response.setResult(instances);
            return response;
        }

        response.setIsTruncated(true);
        response.setNextMarker(instances.get(maxKeys).getInstanceShortId());
        response.setResult(instances.subList(0, maxKeys));

        // 绑定实例付费方式

        return response;
    }
}
