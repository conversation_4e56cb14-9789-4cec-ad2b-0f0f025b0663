package com.baidu.bce.logic.rds.service.datasync;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Calendar;
import java.util.Date;

/**
 * 自动数据同步 调度器
 */
public abstract class AbstractAutoScheduler {

    public static final Logger LOGGER = LoggerFactory.getLogger(AbstractAutoScheduler.class);

    protected static final int FIXED_DELAY = 3000 * 15;

    protected static final long ONE_MINUTE = 1000 * 60;

    protected static final long QUARTER_HOUR_MILLISECOND = 1000 * 60 * 15;

    protected static final long HALF_HOUR_MILLISECOND = 1000 * 60 * 30;

    protected static final long TWO_HOUR_MILLISECOND = 1000 * 60 * 120;

    protected static final long EIGHT_HOUR_MILLISECOND = 1000 * 60 * 60 * 8;


    @Value("${datasync.seize.lock.seconds:100}")
    private String tooLongSync;

//    @Autowired(required = false)
//    private ZookeeperClient zookeeperClient;

    @Value("${bcc.logical.region:bj}")
    public String region;

    @Value("${data.sync.deploy.location:qa-sandbox00}")
    public String deployRegion;

    @Value("${data.sync.start.time:2016-04-05 10:00:00}")
    String lastActionTimeStr;

    @Value("${logic.rds.data.sync:true}")
    Boolean dataSync;

    /**
     * 固定delay时间执行
     */
    @Scheduled(fixedDelay = FIXED_DELAY)
    public void runFixedDelayTask() {
        Thread.currentThread().setName(currentThreadName());

        LOGGER.info("begin logic rds data sync: {} ", currentThreadName());
        if (dataSync) {
            doFixedDelayJob();
        }
    }

    /**
     * 固定delay时间执行的任务
     */
    public void doFixedDelayJob() {
    }

    /**
     * 固定速率
     */
//    @Scheduled(fixedRate = 900000)
    public void runFixedRateTask() {
        Thread.currentThread().setName(currentThreadName());

        if (dataSync) {
            LOGGER.info("do 900000 cron task start.");
            doFixedRateJob();
            LOGGER.info("do 900000 cron task end.");
        }
    }

    /**
     * 按固定速率执行的任务
     */
    public void doFixedRateJob() {
    }

    /**
     * 按固定速率轮询批量变配取消支付的订单
     */

    @Scheduled(fixedRate = ONE_MINUTE)
    public void cancelBatchResizeOrder() {
        Thread.currentThread().setName(currentThreadName());
        LOGGER.info("begin rds batch resize order sync: {} ", currentThreadName());
        doCancelBatchOrderJob();
    }

    public void doCancelBatchOrderJob() {
    }

    /**
     * 当前线程的名称
     *
     * @return
     */
    public abstract String currentThreadName();


    /**
     * 根据当前lockDate 得到tooLongSyncLong秒之前的lock时间
     *
     * @param lockDate
     * @return
     */
    public Date getOutLockDate(Date lockDate) {
        Integer tooLongSyncLong = Integer.parseInt(tooLongSync);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(lockDate);
        calendar.add(Calendar.MILLISECOND, tooLongSyncLong * -1000);
        Date outLockDate = calendar.getTime();

        return outLockDate;
    }
}
