package com.baidu.bce.logic.rds.service.datasync.service;

import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.OrderNeedToSyncPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * Created by luping03 on 18/1/9.
 */
@Service
public class NewOrderCreatingService extends OrderSyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(NewOrderCreatingService.class);


    @Override
    public int syncData(OrderNeedToSyncPO orderSyncPO) {
        LOGGER.debug(LOG_PREFIX + "synchronize creating rds start.order id is {}", orderSyncPO.getOrderUuid());
        List<InstancePO> creatingInstanceList = instanceDao
                .getInstanceListByOrderUuid(orderSyncPO.getOrderUuid(), orderSyncPO.getUserId());
        if (creatingInstanceList == null || creatingInstanceList.isEmpty()) {
            return 0;
        }
        OrderClient orderClient = clientFactory.getOrderClient();
        ResourceClient resourceClient = clientFactory.getResourceClient();

        try {
            String orderId = orderSyncPO.getOrderUuid();
            if (StringUtils.isEmpty(orderId)) {
                return 0;
            }

            Order instanceOrder = null;
            try {
                instanceOrder = orderClient.get(orderId);
                if (instanceOrder == null) {
                    LOGGER.warn(LOG_PREFIX + "Query order return null, order id is {}", orderId);
                    return 0;
                }
            } catch (Exception e) {
                LOGGER.warn(LOG_PREFIX + "Query order catch exception, order id is {}", orderId);
                return 0;
            }

            OrderStatus orderStatus = instanceOrder.getStatus();
            LOGGER.debug("Query order is {}, order status is {}", orderId, orderStatus.name());
            // 创建失败，删除本地资源：将所有订单id为orderId的资源都删除，包括虚机和磁盘
            if (orderStatus == OrderStatus.CREATE_FAILED || orderStatus == OrderStatus.REFUND_SUCC
                    || orderStatus == OrderStatus.REFUND_FAILED || orderStatus == OrderStatus.EXPIRED
                    || orderStatus == OrderStatus.CANCELLED) {
                instanceDao.deleteCreateFailedInstance(orderId, orderSyncPO.getUserId());
                orderNeedToSyncService.updateOrderStatus(orderSyncPO.withOrderStatus(orderStatus.name()));
            }
//            LOGGER.debug(LOG_PREFIX + "Query order is {}, order id is {}", instanceOrder, orderId);
            if (orderStatus == OrderStatus.CREATED) {
                try {
                    updateInstance(instanceOrder, resourceClient, creatingInstanceList);
                    orderNeedToSyncService.updateOrderStatus(orderSyncPO.withOrderStatus(orderStatus.name()));
                } catch (Exception e) {
                    LOGGER.error(LOG_PREFIX + "sync in creating rds failed, order id is {}, exception is {}",
                            instanceOrder.getUuid(), e);
                    return 0;
                }
            }
        } catch (Exception e) {
            LOGGER.error("synchronize in creating rds failed, exception is {}", e);
            return 0;
        }
//        LOGGER.debug(LOG_PREFIX + "sync in creating rds end");
        return 1;
    }
}
