package com.baidu.bce.logic.rds.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.DiskInfoResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.PostSqlflowRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaInstanceResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaPageRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaPageResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaResultResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaSubnet;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaSubnetRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SmartDbaTopoResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSlowSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SqlIdResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SqlIdRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlListResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlExplainResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTemplateResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlStatsDuration;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTable;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTrend;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlExplainListResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlStatsDurationList;
import com.baidu.bce.internalsdk.rds.model.smartdba.SlowsqlTableList;
import com.baidu.bce.internalsdk.rds.model.smartdba.GetSessionKillTypesResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionSummaryRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionDetailResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionDetailRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.OpenAPIGetSlowSqlflowResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionKillAuthorityRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionKillHistoryResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.SessionStatisticsResponse;
import com.baidu.bce.internalsdk.rds.model.smartdba.ErrorLogDetailRequest;
import com.baidu.bce.internalsdk.rds.model.smartdba.ErrorLogDetailResponse;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import com.baidu.bce.logic.rds.service.util.InstanceForApiUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SmartDbaService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SmartDbaService.class);

    @Autowired
    LogicRdsClientFactory rdsClientFactory;

    @Autowired
    InstanceService instanceService;

    @Autowired
    InstanceForApiUtils instanceForApiUtils;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private InstanceDao instanceDao;

    public DiskInfoResponse instanceDisk(String instanceId) {
        return rdsClientFactory.createRdsClient().instanceDisk(instanceId);
    }

    public  SmartDbaInstanceResponse instance(String instanceId) {
        SmartDbaInstanceResponse response = rdsClientFactory.createRdsClient().instance(instanceId);
        // SmartDbaInstanceResponse response = new SmartDbaInstanceResponse();
        List<SmartDbaSubnet> smartDbaSubnetList = new ArrayList<>();

        List<SmartDbaSubnetRequest> smartDbaSubnetRequestList = null;
        if (response.getResult() != null) {
            JSONObject result = response.getResult();
            smartDbaSubnetRequestList
                    = JSON.parseArray(result.getJSONArray("intanceSimpleList").toJSONString(),
                    SmartDbaSubnetRequest.class);
        }
        if (smartDbaSubnetRequestList != null && smartDbaSubnetRequestList.size() > 0) {

            for (SmartDbaSubnetRequest smartDbaSubnetRequest : smartDbaSubnetRequestList) {
                InstanceExtension instanceExtension = new InstanceExtension();
                LOGGER.debug("subnet is " + smartDbaSubnetRequest.getSubnetId());
                Instance.RoleInfo nodeMaster = new Instance.RoleInfo();
                Instance.RoleInfo nodeSlave = new Instance.RoleInfo();
                if (smartDbaSubnetRequest.getSubnetId().size() > 1) {
                    // List<String> subnetIds = new ArrayList<>(smartDbaSubnetRequest.getSubnetId().values());
                    List<String> key = new ArrayList<>(smartDbaSubnetRequest.getSubnetId().keySet());
                    nodeMaster.setSubnetId(smartDbaSubnetRequest.getSubnetId().get(key.get(1)));
                    nodeMaster.setAzone(key.get(1));
                    nodeSlave.setSubnetId(smartDbaSubnetRequest.getSubnetId().get(key.get(0)));
                    nodeSlave.setAzone(key.get(0));
                } else if (smartDbaSubnetRequest.getSubnetId().size() > 0) {
                    List<String> key = new ArrayList<>(smartDbaSubnetRequest.getSubnetId().keySet());
                    nodeMaster.setSubnetId(smartDbaSubnetRequest.getSubnetId().get(key.get(0)));
                    nodeMaster.setAzone(key.get(0));
                    nodeSlave.setSubnetId(smartDbaSubnetRequest.getSubnetId().get(key.get(0)));
                    nodeSlave.setAzone(key.get(0));
                }
                if ("master".equals(smartDbaSubnetRequest.getRole())) {
                    instanceExtension.setInstanceType(smartDbaSubnetRequest.getRole());
                    instanceExtension.setNodeMaster(nodeMaster);
                    instanceExtension.setNodeSlave(nodeSlave);
                } else {
                    instanceExtension.setInstanceType(smartDbaSubnetRequest.getRole());
                }
                instanceExtension.setSubnetId(smartDbaSubnetRequest.getSubnetId());
                instanceExtension.setInstanceId(smartDbaSubnetRequest.getInstance());
                /*InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(id)
                        .instanceDescribe(id);*/
                instanceService.setSubnetDetailForInstanceExtension2(instanceExtension);

                SmartDbaSubnet smartDbaSubnet = new SmartDbaSubnet();
                smartDbaSubnet.setInstanceId(instanceExtension.getInstanceId());
                smartDbaSubnet.setInstanceType(instanceExtension.getInstanceType());
                smartDbaSubnet.setNodeMaster(instanceExtension.getNodeMaster());
                smartDbaSubnet.setNodeSlave(instanceExtension.getNodeSlave());

                List<Instance.RoleInfo> subnets = new ArrayList<>();
                if (instanceExtension.getSubnets() != null && instanceExtension.getSubnets().size() > 0) {
                    for (InstanceExtension.Subnet subnet : instanceExtension.getSubnets()) {
                        Instance.RoleInfo roleInfo = new Instance.RoleInfo();
                        roleInfo.setAzone(subnet.getAz());
                        roleInfo.setName(subnet.getName());
                        roleInfo.setSubnetId(subnet.getSubnetId());
                        roleInfo.setVpcCidr(subnet.getCidr());
                        subnets.add(roleInfo);
                    }
                    smartDbaSubnet.setSubnets(subnets);
                }

                smartDbaSubnetList.add(smartDbaSubnet);
            }
        }
        response.getResult().put("smartDbaSubnetList", smartDbaSubnetList);
        return response;
    }

    public SmartDbaResultResponse getDbList(String instanceId, Integer listNo) {
        return rdsClientFactory.createRdsClient().getDbList(instanceId, listNo);
    }

    public SmartDbaPageResponse getSmartDbaPage(String instanceId, SmartDbaPageRequest smartDbaPageRequest) {
        return rdsClientFactory.createRdsClient().getSmartDbaPage(instanceId, smartDbaPageRequest);
    }

    public SmartDbaInstanceResponse getConnList(String instanceId) {
        return rdsClientFactory.createRdsClient().getConnList(instanceId);
    }


    public SmartDbaTopoResponse getTopoList(String instanceId) {
        return rdsClientFactory.createRdsClient().getTopoList(instanceId);
    }

    public SmartDbaInstanceResponse getConnStati(String instanceId) {
        return rdsClientFactory.createRdsClient().getConnStati(instanceId);
    }

    public GetSqlflowResponse getSqlflow(String instanceId) {
        checkInstancePermission(instanceId);
        return rdsClientFactory.createSmartDbaClient().getSqlflow(instanceId);
    }

    public GetSqlflowResponse postSqlflow(PostSqlflowRequest postSqlflowRequest) {
        checkInstancePermission(postSqlflowRequest.getInstanceId());
        return rdsClientFactory.createSmartDbaClient().postSqlflow(postSqlflowRequest);
    }

    public GetSqlflowResponse deleteSqlflow(String instanceId) {
        checkInstancePermission(instanceId);
        return rdsClientFactory.createSmartDbaClient().deleteSqlflow(instanceId);
    }

    public GetSqlResponse getSql(GetSqlRequest getSqlRequest) {
        checkInstancePermission(getSqlRequest.getInstanceId());
        return rdsClientFactory.createSmartDbaClient().getSql(getSqlRequest);
    }

    public GetSlowSqlflowResponse getSlowSqlflow(String instanceId) {
        checkInstancePermission(instanceId);
        return rdsClientFactory.createSmartDbaClient().getSlowSqlflow(instanceId);
    }

    public GetSlowSqlflowResponse putSlowSqlflow(String instanceId) {
        checkInstancePermission(instanceId);
        return rdsClientFactory.createSmartDbaClient().putSlowSqlflow(instanceId);
    }

    public GetSlowSqlflowResponse deleteSlowSqlflow(String instanceId) {
        checkInstancePermission(instanceId);
        return rdsClientFactory.createSmartDbaClient().deleteSlowSqlflow(instanceId);
    }

    public SqlIdResponse getSlowSqlBySqlId(String instanceId, SqlIdRequest request) {
        return rdsClientFactory.createSmartDbaClient().getSlowSqlBySqlId(
                instanceId, request.getSqlId(), request.getEngine());
    }

    public SlowsqlListResponse getSlowSqlList(String instanceId, SlowsqlListRequest request) {
        return rdsClientFactory.createUserDbscClient().getSlowSqlList(
                instanceId, request);
    }

    public SlowsqlExplainListResponse getSlowSqlExplain(String instanceId, SlowsqlListRequest request) {

        String slowsqlExplain = rdsClientFactory.createSmartDbaClient().getSlowSqlExplain(
                instanceId, request);
        SlowsqlExplainListResponse res = new SlowsqlExplainListResponse();
        res.setList(JSON.parseArray(slowsqlExplain, SlowsqlExplainResponse.class));
        return res;
    }

    public SlowsqlTemplateResponse getSlowSqlTemplate(String instanceId, SlowsqlListRequest request) {
        return rdsClientFactory.createUserDbscClient().getSlowSqlTemplate(
                instanceId, request);
    }

    public SlowsqlStatsDurationList getSlowSqlStatsDuration(String instanceId, SlowsqlListRequest request) {
        String duration  = rdsClientFactory.createSmartDbaClient().getSlowSqlStatsDuration(
                instanceId, request);
        SlowsqlStatsDurationList res = new SlowsqlStatsDurationList();
        res.setList(JSON.parseArray(duration, SlowsqlStatsDuration.class));
        return res;

    }

    public SlowsqlStatsDurationList getSlowSqlStatsSource(String instanceId, SlowsqlListRequest request) {
        String duration  =  rdsClientFactory.createSmartDbaClient().getSlowSqlStatsSource(
                instanceId, request);
        SlowsqlStatsDurationList res = new SlowsqlStatsDurationList();
        res.setList(JSON.parseArray(duration, SlowsqlStatsDuration.class));
        return res;
    }

    public SlowsqlTableList getSlowSqlTable(String instanceId, SlowsqlListRequest request) {
        String slowsqlTable = rdsClientFactory.createSmartDbaClient().getSlowSqlTable(
                instanceId, request);
        SlowsqlTableList res = new SlowsqlTableList();
        res.setList(JSON.parseArray(slowsqlTable, SlowsqlTable.class));
        return res;
    }

    public SlowsqlTableList getSlowSqlTableColumn(String instanceId, SlowsqlListRequest request) {
        String slowsqlTable =  rdsClientFactory.createSmartDbaClient().getSlowSqlTableColumn(
                instanceId, request);
        SlowsqlTableList res = new SlowsqlTableList();
        res.setList(JSON.parseArray(slowsqlTable, SlowsqlTable.class));
        return res;
    }

    public SlowsqlTableList getSlowSqlTableIndex(String instanceId, SlowsqlListRequest request) {
        String slowsqlTable =  rdsClientFactory.createSmartDbaClient().getSlowSqlTableIndex(
                instanceId, request);
        SlowsqlTableList res = new SlowsqlTableList();
        res.setList(JSON.parseArray(slowsqlTable, SlowsqlTable.class));
        return res;
    }

    public SlowsqlTrend getSlowSqlTrend(String instanceId, SlowsqlListRequest request) {
        return rdsClientFactory.createSmartDbaClient().getSlowSqlTrend(
                instanceId, request);
    }

    public SlowsqlTrend getSlowSqlTuning(String instanceId, SlowsqlListRequest request) {
        return rdsClientFactory.createSmartDbaClient().getSlowSqlTuning(
                instanceId, request);
    }

    public GetSessionKillTypesResponse getSessionKillTypesRequest(String instanceId, String type) {
        return rdsClientFactory.createSmartDbaClient().getSessionKillTypesRequest(
                instanceId, type);
    }

    public SessionSummaryRequest getSessionSummaryRequest(String instanceId) {
        return rdsClientFactory.createSmartDbaClient().getSessionSummaryRequest(instanceId);
    }

    public SessionDetailResponse getSessionDetailRequest(String instanceId, SessionDetailRequest request) {
        return rdsClientFactory.createSmartDbaClient().getSessionDetailRequest(
                instanceId, request);
    }

    public OpenAPIGetSlowSqlflowResponse postSessionKillAuthorityRequest(String instanceId,
                                                                         SessionKillAuthorityRequest request) {
        return rdsClientFactory.createSmartDbaClient().postSessionKillAuthorityRequest(
                instanceId, request);
    }
    public SessionKillHistoryResponse getSessionKillHistoryRequest(String instanceId, SessionDetailRequest request) {
        return rdsClientFactory.createSmartDbaClient().getSessionKillHistoryRequest(
                instanceId, request);
    }
    public OpenAPIGetSlowSqlflowResponse postSessionKillRequest(String instanceId,
                                                                SessionKillAuthorityRequest request) {
        return rdsClientFactory.createSmartDbaClient().postSessionKillRequest(
                instanceId, request);
    }
    public SessionStatisticsResponse getSessionStatisticsRequest(String instanceId) {
        return rdsClientFactory.createSmartDbaClient().getSessionStatisticsRequest(
                instanceId);
    }
    public GetSlowSqlflowResponse getErrorLogFlowRequest(String instanceId) {
        return rdsClientFactory.createSmartDbaClient().getErrorLogFlowRequest(
                instanceId);
    }
    public GetSlowSqlflowResponse putErrorLogFlowRequest(String instanceId) {
        return rdsClientFactory.createSmartDbaClient().putErrorLogFlowRequest(
                instanceId);
    }
    public GetSlowSqlflowResponse deleteErrorLogFlowRequest(String instanceId) {
        return rdsClientFactory.createSmartDbaClient().deleteErrorLogFlowRequest(
                instanceId);
    }
    public ErrorLogDetailResponse getErrorLogDetailRequest(String instanceId,  ErrorLogDetailRequest request) {
        return rdsClientFactory.createSmartDbaClient().getErrorLogDetailRequest(
                instanceId, request);
    }

    /**
     * 1. 检查实例是否属于此用户；
     * 2. 检查实例是否已在后端存在；
     *
     * @param instanceId RDS 实例长 ID
     */
    private void checkInstancePermission(String instanceId) {
        InstancePO instancePO = instanceDao.queryInstanceByInstanceUuid(instanceId, clientFactory.getAccountId());
        if (instancePO == null) {
            throw new RDSExceptions.ResourceNotExistException();
        }
        // 下面的 instanceUuid 和 instanceId 一样，都是 RDS 实例长 ID
        String instanceUuid = instancePO.getInstanceUuid();
        InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(instanceUuid)
                .instanceDescribe(instanceUuid);
        if (instanceGetResponse == null || instanceGetResponse.getInstance() == null) {
            throw new RDSExceptions.AvailableInstanceException();
        }
    }
}
