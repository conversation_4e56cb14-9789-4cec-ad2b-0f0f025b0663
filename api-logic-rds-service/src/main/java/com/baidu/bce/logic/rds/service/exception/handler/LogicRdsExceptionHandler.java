package com.baidu.bce.logic.rds.service.exception.handler;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.trail.EventBuilder;
import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.rds.service.exception.BackendExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;

import java.lang.reflect.Constructor;
import java.util.Map;

/**
 * Created by luping03 on 17/10/10.
 */
public class LogicRdsExceptionHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogicRdsExceptionHandler.class);

    private static final String ERROR_CODE_RESOURCE_IN_TASK = "ResourceInTaskException";
    private static final String ERROR_CODE_ORDER_PAY_FAILED = "NotEnoughBalanceForPayOrder";
    private static final String INSUFFICIENT_BALANCE = "InsufficientBalance";

    private static void handleBceInternalResponseException(EventBuilder eventBuilder,
                                                           BceInternalResponseException exception) {
        eventBuilder.error(exception);
        eventBuilder.errorCode(String.valueOf(exception.getHttpStatus()));
        eventBuilder.errorMessage(exception.getRequestId());
        handleBceInternalResponseException(exception);
    }

    private static BackendExceptions handleBceInternalResponseException(BceInternalResponseException exception) {

        LOGGER.debug("catch BceInternalResponseException message: {}", exception);
        if ("BceSignatureValidateException.UnauthorizedException".equals(exception.getCode())) {
            throw new RDSExceptions.UnauthorizedException();
        }

        if ("OrderExceptions.ResourceInTaskException".equals(exception.getCode())) {
            throw new RDSExceptions.ResourceInTaskException();
        }
        if ("RDSGroupForceChangeFailed".equals(exception.getCode())) {
            Map<String, Object> behindMaster = (Map) exception.getAdditionalProperties().get("additionalProperties");
            if (behindMaster != null) {
                Integer behind = (Integer) behindMaster.get("behindMaster");
                throw new RDSExceptions.RDSGroupForceChangeException(behind);
            } else {
                throw new RDSExceptions.RDSGroupForceChangeException();
            }
        }
        if ("InstanceStatusError".equals(exception.getCode())) {
            throw new RDSExceptions.InstanceStatusErrorException();
        }
        try {
            String upperCode = exception.getCode().substring(0, 1).toUpperCase() + exception.getCode().substring(1);
            Constructor con = Class.forName(BackendExceptions.class.getCanonicalName() + "$" + upperCode)
                    .getConstructor(BceInternalResponseException.class);
            return (BackendExceptions) con.newInstance(exception);
        } catch (Exception ignore) {
            throw new CommonExceptions.InternalServerErrorException();
        }
    }

    public static void handle(Exception e) {
        if (e instanceof BceInternalResponseException) {
            throw handleBceInternalResponseException((BceInternalResponseException) e);
        } else if (e instanceof BceException) {
            throw (BceException) e;
        } else if (e instanceof DataAccessException) {
            LOGGER.error("logical database error, the info is :", e);
            throw new CommonExceptions.InternalServerErrorException();
        } else {
            LOGGER.error("logical internal error, the info is :", e);
            throw new CommonExceptions.InternalServerErrorException();
        }
    }

    public static void handle(EventBuilder event, Exception e) {
        if (e instanceof BceInternalResponseException) {
            handleBceInternalResponseException(event, (BceInternalResponseException) e);
        } else if (e instanceof BceException) {
            throw (BceException) e;
        } else if (e instanceof DataAccessException) {
            throw new CommonExceptions.InternalServerErrorException();
        } else {
            throw new CommonExceptions.InternalServerErrorException();
        }
    }
    public static void throwPermissionDeniedExceptionIfAppropriate(Exception ex) {
        if (ex instanceof BceInternalResponseException) {
            BceInternalResponseException e = (BceInternalResponseException) ex;
            if (e.getHttpStatus() == HttpStatus.ERROR_OPERATION_DENY) {
                throw new CommonExceptions.OperationDeniedException();
            } else if (e.getHttpStatus() == HttpStatus.ERROR_OPERATION_NOT_AVAILABLE) {
                throw new CommonExceptions.ResourceInTaskException();
            } else if (ERROR_CODE_ORDER_PAY_FAILED.equalsIgnoreCase(e.getCode())) {
                throw new CommonExceptions.PaymentFailedException();
            } else if (INSUFFICIENT_BALANCE.equalsIgnoreCase(e.getCode())) {
                throw new CommonExceptions.InsufficientBalanceException();
            }
        }
    }
}
