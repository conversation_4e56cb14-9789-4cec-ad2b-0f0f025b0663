/*
 * Copyright (C) 2015 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.service.model.instance;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceCreateRequest;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotPolicy;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.MachinePO;
import com.baidu.bce.logic.rds.dao.model.SubnetPO;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.plat.resource.manager.sdk.model.ResourceGroupsDetailFull;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.*;

public class InstanceExtension extends Instance {

    private String phpAdminUrl;

    private String dmsUrl;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Date timestamp;

    private String vpcName;

    /**
     * vpc网段及子网掩码
     */
    private String vpcCidr;

    private String vpcShortId;

    List<Subnet> subnets = new ArrayList<>();

    private List<String> zoneNames = new ArrayList<>();

    private String orderStatus;

    // 资源的唯一标示
    private String resourceUuid;

    private List<MachinePO> dccHosts;

    private String category;

    private ResourceGroupsDetailFull resourceGroup;

    private String resourceGroupId;

    private String resourceGroupName;

    private String unitPrice;

    private String orderId;

    private List<Tag> tags;

    private String zoneType;

    private boolean isChargeProxy = true;



    public InstanceExtension(Instance instance) {
        super(instance);
//        if ("dcc".equalsIgnoreCase(this.getMachineType())) {
//            this.withDccHosts(dccHostConvert(instance.getDccHostIds()));
//        }
    }

    public InstanceExtension() {
    }

    public InstanceExtension(InstancePO instancePO) {
        this.withInstanceId(instancePO.getInstanceUuid())
                .withInstanceShortId(instancePO.getInstanceId())
                .withInstanceName(instancePO.getInstanceName())
                .withEngine(instancePO.getEngine())
                .withEngineVersion(instancePO.getEngineVersion())
                .withEndpoint(new Endpoint(instancePO.getEndpoint().getPort(), instancePO.getEndpoint().getAddress(),
                        instancePO.getEndpoint().getVnetIp(), instancePO.getEndpoint().getInetIp()))
                .withAllocatedMemoryInMB((int) (instancePO.getMemoryCapacity() * 1024))
                .withAllocatedMemoryInGB(instancePO.getMemoryCapacity())
                .withAllocatedStorageInGB(instancePO.getVolumeCapacity())
                .withUsedStorageInMB((int) (instancePO.getUsedStorage() * 1024))
                .withUsedStorageInGB(instancePO.getUsedStorage())
                .withCpuCount(instancePO.getCpuCount())
                .withInstanceType(instancePO.getInstanceType())
                .withSourceInstanceId(instancePO.getSourceInstanceId())
                .withInstanceStatus(instancePO.getInstanceStatus())
                .withEipStatus(instancePO.getEipStatus())
                .withBackupPolicy(new SnapshotPolicy(instancePO.getBackupPolicy().getBackupDays(),
                        instancePO.getBackupPolicy().getBackupTime(),
                        instancePO.getBackupPolicy().getPersistent(),
                        instancePO.getBackupPolicy().getExpireInDays(),
                        instancePO.getBackupPolicy().getBackupFreeSpace(),
                        instancePO.getBackupPolicy().getLogBackupRetainDays(),
                        instancePO.getBackupPolicy().getDataBackupType(),
                        instancePO.getBackupPolicy().getIncrementalDataBackupInterval(),
                        instancePO.getBackupPolicy().getIncrementalDataBackupEnable(),
                        instancePO.getBackupPolicy().getLatestDataBackupRetainDays()))
                .withPubliclyAccessible(instancePO.isPubliclyAccessible())
                .withInstanceCreateTime(instancePO.getInstanceCreateTime())
                .withInstanceExpireTime(instancePO.getInstanceExpireTime())
                .withTopoly(new Topology(instancePO.getTopology().getMaster(),
                        instancePO.getTopology().getReadReplica(),
                        instancePO.getTopology().getRdsproxy()))
                .withProductType(instancePO.getProductType())
                .withAzone(instancePO.getZoneNames())
                .withSuperUserFlag(instancePO.getSuperUserFlag())
                .withVpcId(instancePO.getVpcUuid())
                .withSubnetId(subnetListToMap(instancePO.getSubnetIds()))
                .withReplicationType(instancePO.getReplicationType())
                .withApplicationType(instancePO.getApplicationType())
                .withNodeAmount(instancePO.getNodeAmount())
                .withOldInstance(instancePO.getOldInstance())
                .withTotalStorageInGB(instancePO.getTotalVolumeCapacity());
        this.withDccHosts(instancePO.getDccHosts());
    }

    @Override
    public String toString() {
        return "InstanceExtension{"
                   + "phpAdminUrl='" + phpAdminUrl + '\''
                   + "} " + super.toString();
    }

    public Map<String, String> subnetListToMap(List<SubnetPO> subnetPOs) {
        Map<String, String> subnetId = new HashMap<>();
        for (SubnetPO subnetPO : subnetPOs) {
            subnetId.put(subnetPO.getLogicalZone(), subnetPO.getSubnetUuid());
        }
        return subnetId;
    }

    public static List<MachinePO> dccHostConvert(InstanceCreateRequest.DccHostInfo dccHostInfo){
        List<MachinePO> list = new ArrayList<>();
        MachinePO master = new MachinePO(dccHostInfo.getMaster().getHostId(), "Master");
        MachinePO backup = new MachinePO(dccHostInfo.getBackup().getHostId(), "Backup");
        list.add(master);
        list.add(backup);
        return list;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public String getResourceGroupId() {
        return resourceGroupId;
    }

    public void setResourceGroupId(String resourceGroupId) {
        this.resourceGroupId = resourceGroupId;
    }

    public String getResourceGroupName() {
        return resourceGroupName;
    }

    public void setResourceGroupName(String resourceGroupName) {
        this.resourceGroupName = resourceGroupName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(String unitPrice) {
        this.unitPrice = unitPrice;
    }

    public List<MachinePO> getDccHosts() {
        return dccHosts;
    }

    public Instance withDccHosts(List<MachinePO> dccHosts) {
        this.dccHosts = dccHosts;
        return this;
    }

    public String getVpcShortId() {
        return vpcShortId;
    }

    public void setVpcShortId(String vpcShortId) {
        this.vpcShortId = vpcShortId;
    }

    public List<String> getZoneNames() {
        return zoneNames;
    }

    public void setZoneNames(List<String> zoneNames) {
        this.zoneNames = zoneNames;
    }

    public String getPhpAdminUrl() {
        return phpAdminUrl;
    }

    public void setPhpAdminUrl(String phpAdminUrl) {
        this.phpAdminUrl = phpAdminUrl;
    }

    public InstanceExtension withPhpAdminUrl(final String phpAdminUrl) {
        this.phpAdminUrl = phpAdminUrl;
        return this;
    }

    public String getZoneType() {
        return zoneType;
    }

    public void setZoneType(String zoneType) {
        this.zoneType = zoneType;
    }

    public boolean getIsChargeProxy() {
        return isChargeProxy;
    }

    public void setIsChargeProxy(boolean chargeProxy) {
        isChargeProxy = chargeProxy;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getVpcName() {
        return vpcName;
    }

    public void setVpcName(String vpcName) {
        this.vpcName = vpcName;
    }

    public String getVpcCidr() {
        return vpcCidr;
    }

    public void setVpcCidr(String vpcCidr) {
        this.vpcCidr = vpcCidr;
    }

    public List<Subnet> getSubnets() {
        return subnets;
    }

    public void setSubnets(List<Subnet> subnets) {
        this.subnets = subnets;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getResourceUuid() {
        return resourceUuid;
    }

    public void setResourceUuid(String resourceUuid) {
        this.resourceUuid = resourceUuid;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDmsUrl() {
        return dmsUrl;
    }

    public void setDmsUrl(String dmsUrl) {
        this.dmsUrl = dmsUrl;
    }
    public InstanceExtension withDmsUrl(final String dmsUrl) {
        this.dmsUrl = dmsUrl;
        return this;
    }

    public static class Subnet {
        private String name = "";

        private String subnetId = "";

        private String az = "";

        // OpenAPI 专用参数
        private String zoneName;

        private String cidr = "";

        private String shortId = "";

        public Subnet(String name, String subnetId, String az, String cidr, String shortId) {
            this.name = name;
            this.subnetId = subnetId;
            this.az = az ;
            this.cidr = cidr;
            this.shortId = shortId;
        }

        public String getShortId() {
            return shortId;
        }

        public void setShortId(String shortId) {
            this.shortId = shortId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getSubnetId() {
            return subnetId;
        }

        public void setSubnetId(String subnetId) {
            this.subnetId = subnetId;
        }

        public String getAz() {
            return az;
        }

        public void setAz(String az) {
            this.az = az;
        }

        public String getZoneName() {
            return zoneName;
        }

        public void setZoneName(String zoneName) {
            this.zoneName = zoneName;
        }

        public String getCidr() {
            return cidr;
        }

        public void setCidr(String cidr) {
            this.cidr = cidr;
        }
    }

    public ResourceGroupsDetailFull getResourceGroup() {
        return resourceGroup;
    }

    public void setResourceGroup(ResourceGroupsDetailFull resourceGroup) {
        this.resourceGroup = resourceGroup;
    }

}
