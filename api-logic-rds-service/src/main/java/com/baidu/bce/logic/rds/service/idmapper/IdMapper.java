package com.baidu.bce.logic.rds.service.idmapper;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by luping03 on 17/12/28.
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface IdMapper {

    // 参照哪个字段转换id
    String value() default "";

    // 分类，该字段被转化成长id还是短id
    Category category() default Category.SHORT;

    public enum Category {
        SHORT, LONG;
    }
}

