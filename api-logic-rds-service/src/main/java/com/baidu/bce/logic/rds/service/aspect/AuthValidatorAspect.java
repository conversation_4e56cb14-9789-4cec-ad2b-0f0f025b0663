package com.baidu.bce.logic.rds.service.aspect;

import com.baidu.bce.internalsdk.qualify.QualifyClientV2;
import com.baidu.bce.internalsdk.qualify.model.RealNameInfoResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
@ConditionalOnExpression("${bce.logical.rds.logic.realname:true}")
public class AuthValidatorAspect {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuthValidatorAspect.class);

    @Autowired
    private LogicRdsClientFactory logicRdsClientFactory;

    public AuthValidatorAspect() {
    }

    @Pointcut("execution(* com.baidu.bce..*(..))" +
            " && @annotation(org.springframework.web.bind.annotation.RequestMapping)" +
            " && @annotation(com.baidu.bce.logic.rds.service.aspect.AuthValidator) ")
    public void authValidatorAspect() {
    }

    @Around("authValidatorAspect()")
    public Object authValidatorInterceptor(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        Method method = methodSignature.getMethod();
        AuthValidator idempotentAnnotation = (AuthValidator) method.getAnnotation(AuthValidator.class);
        boolean checkRealName = idempotentAnnotation.checkRealName();
        if (checkRealName) {
            QualifyClientV2 qualifyClient = logicRdsClientFactory.createQualifyClient();
            RealNameInfoResponse realNameInfoResponse =
                    qualifyClient.getRealNameWithWhiteList(LogicUserService.getAccountId());
            if (!realNameInfoResponse.getPrimePassFlag()) {
                LOGGER.error("realName not pass {}.", LogicUserService.getAccountId());
                throw new RDSExceptions.RealNameNotPassException("未通过实名认证，请先进行认证");
            }
        }
        return proceedingJoinPoint.proceed();
    }
}
