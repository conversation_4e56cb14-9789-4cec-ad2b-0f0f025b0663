package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.EdgeClient;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeCity;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeRegion;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeServiceProvider;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeSubnet;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeVpc;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeVpcListResponse;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EdgeService {

    public static final String[] RDS_EDGE_REGION_TYPES = {"vm", "app_lb", "vpc", "rds"};

    private static final String SUBNET_ID_PREFIX = "sbn-";

    @Autowired
    private LogicRdsClientFactory clientFactory;

    /**
     * 查询多个 types 交集的可用节点
     *
     * @param types types
     * @return edgeRegions
     */
    public List<EdgeRegion> getRegions(List<String> types) {
        EdgeClient edgeClient = clientFactory.createEdgeClient();
        return edgeClient.getRegionList(types).getRegionList();
    }

    /**
     * 查询 Map<regionId, regionName>，该方法主要是为了前端显示节点名称，所以 regionName 已经组装为前端显示的格式
     *
     * @return Map<regionId, regionName>
     */
    public Map<String, String> getRegionIdRegionNameMap() {
        List<EdgeRegion> edgeRegions = getRegions(Arrays.asList(RDS_EDGE_REGION_TYPES));
        Map<String, String> regionIdRegionNameMap = new HashMap<>();
        for (EdgeRegion edgeRegion : edgeRegions) {
            String regionName = edgeRegion.getName();
            for (EdgeCity edgeCity : edgeRegion.getCityList()) {
                String cityName = edgeCity.getName();
                for (EdgeServiceProvider edgeServiceProvider : edgeCity.getServiceProviderList()) {
                    String serviceProviderName = edgeServiceProvider.getName();
                    regionIdRegionNameMap.put(edgeServiceProvider.getRegionId(),
                            String.format("%s-%s-%s", regionName, cityName, serviceProviderName));
                }
            }
        }
        return regionIdRegionNameMap;
    }

    /**
     * 查询 VPC 详情，同时会返回 VPC 下的子网信息
     *
     * @param vpcId vpcId
     * @return edgeVpc
     */
    public EdgeVpc getVpc(String vpcId) {
        return clientFactory.createEdgeClient().getVpc(vpcId).getVpc();
    }

    /**
     * 查询 VPC 详情，同时会返回 VPC 下的子网信息
     *
     * @param vpcUuid vpcUuid
     * @return edgeVpc
     */
    public EdgeVpc getVpcByVpcUuid(String vpcUuid) {
        return clientFactory.createEdgeClient().getVpcByVpcUuid(vpcUuid).getVpc();
    }

    /**
     * 查询用户所有 VPC 实例
     *
     * @param regionId 节点 ID，可为 null，表示不需要该过滤条件
     * @return edgeVpcs
     */
    private List<EdgeVpc> getVpcs(String regionId) {
        int maxKeys = 100;
        EdgeVpcListResponse edgeVpcListResponse = clientFactory.createEdgeClient().getVpcList(null, maxKeys,
                regionId);
        List<EdgeVpc> edgeVpcs = new ArrayList<>(edgeVpcListResponse.getVpcs());
        while (edgeVpcListResponse.getIsTruncated()) {
            edgeVpcListResponse = clientFactory.createEdgeClient().getVpcList(edgeVpcListResponse.getNextMarker(),
                    maxKeys, regionId);
            edgeVpcs.addAll(edgeVpcListResponse.getVpcs());
        }
        return edgeVpcs;
    }

    /**
     * 批量查询 VPC 详情（批量个数无限制），同时会返回 VPC 下的子网信息
     *  1. 若 vpcIds 为 null，则该方法返回 null；
     *  2. 若 vpcIds 元素个数为 0，则该方法返回元素个数为 0 的 List；
     *
     * @param vpcIds vpcIds
     * @return edgeVpcs
     */
    public List<EdgeVpc> getVpcs(List<String> vpcIds) {
        if (vpcIds == null) {
            return null;
        }
        int batchSize = 10;
        List<EdgeVpc> edgeVpcs = new ArrayList<>(vpcIds.size());
        List<String> tmpVpcIds = new ArrayList<>(batchSize);
        EdgeClient edgeClient = clientFactory.createEdgeClient();
        for (String vpcId : vpcIds) {
            tmpVpcIds.add(vpcId);
            if (tmpVpcIds.size() == batchSize) {
                edgeVpcs.addAll(edgeClient.getVpcs(tmpVpcIds).getVpcs());
                tmpVpcIds.clear();
            }
        }
        if (tmpVpcIds.size() > 0) {
            edgeVpcs.addAll(edgeClient.getVpcs(tmpVpcIds).getVpcs());
        }
        return edgeVpcs;
    }

    public Map<String, EdgeVpc> getVpcIdMap(List<String> vpcIds) {
        if (vpcIds == null) {
            return null;
        }
        List<EdgeVpc> vpcs = getVpcs(vpcIds);
        Map<String, EdgeVpc> vpcIdMap = new HashMap<>(vpcs.size());
        for (EdgeVpc vpc : vpcs) {
            vpcIdMap.put(vpc.getVpcId(), vpc);
        }
        return vpcIdMap;
    }

    /**
     * 批量查询 VPC 详情（批量个数无限制），同时会返回 VPC 下的子网信息
     *  1. 若 vpcUuids 为 null，则该方法返回 null；
     *  2. 若 vpcUuids 元素个数为 0，则该方法返回元素个数为 0 的 List；
     *
     * @param vpcUuids vpcUuids
     * @return edgeVpcs
     */
    public List<EdgeVpc> getVpcsByVpcUuids(Collection<String> vpcUuids) {
        if (vpcUuids == null) {
            return null;
        }
        int batchSize = 10;
        List<EdgeVpc> edgeVpcs = new ArrayList<>(vpcUuids.size());
        List<String> tmpVpcUuids = new ArrayList<>(batchSize);
        EdgeClient edgeClient = clientFactory.createEdgeClient();
        for (String vpcId : vpcUuids) {
            tmpVpcUuids.add(vpcId);
            if (tmpVpcUuids.size() == batchSize) {
                edgeVpcs.addAll(edgeClient.getVpcsByVpcUuids(tmpVpcUuids).getVpcs());
                tmpVpcUuids.clear();
            }
        }
        if (tmpVpcUuids.size() > 0) {
            edgeVpcs.addAll(edgeClient.getVpcsByVpcUuids(tmpVpcUuids).getVpcs());
        }
        return edgeVpcs;
    }

    public Map<String, EdgeVpc> getVpcUuidMap(Collection<String> vpcUuids) {
        if (vpcUuids == null) {
            return null;
        }
        List<EdgeVpc> vpcs = getVpcsByVpcUuids(vpcUuids);
        Map<String, EdgeVpc> vpcUuidMap = new HashMap<>(vpcs.size());
        for (EdgeVpc vpc : vpcs) {
            vpcUuidMap.put(vpc.getVpcUuid(), vpc);
        }
        return vpcUuidMap;
    }



    /**
     * 查询用户所有 VPC 实例
     *
     * @param regionId 节点 ID，可为 null，表示不需要该过滤条件，但从方法名可知一般不为 null
     * @return edgeVpcs
     */
    public List<EdgeVpc> getVpcsByRegionId(String regionId) {
        return getVpcs(regionId);
    }

    /**
     * 查询子网详情
     *
     * @return edgeSubnet
     */
    public EdgeSubnet getSubnet(String subnetId) {
        return clientFactory.createEdgeClient().getSubnet(subnetId).getSubnet();
    }

    /**
     * 查询子网详情
     *
     * @return edgeVpc
     */
    public EdgeSubnet getSubnetBySubnetUuid(String subnetUuid) {
        return clientFactory.createEdgeClient().getSubnetBySubnetUuid(subnetUuid).getSubnet();
    }

    /**
     * 批量查询 Subnet 详情（批量个数无限制）
     *  1. 若 subnetIds 为 null，则该方法返回 null；
     *  2. 若 subnetIds 元素个数为 0，则该方法返回元素个数为 0 的 List；
     *
     * @param subnetIds subnetIds
     * @return edgeVpcs
     */
    public List<EdgeSubnet> getSubnets(List<String> subnetIds) {
        if (subnetIds == null) {
            return null;
        }
        int batchSize = 10;
        List<EdgeSubnet> edgeSubnets = new ArrayList<>(subnetIds.size());
        List<String> tmpSubnetIds = new ArrayList<>(batchSize);
        EdgeClient edgeClient = clientFactory.createEdgeClient();
        for (String subnetId : subnetIds) {
            tmpSubnetIds.add(subnetId);
            if (tmpSubnetIds.size() == batchSize) {
                edgeSubnets.addAll(edgeClient.getSubnets(tmpSubnetIds).getSubnets());
                tmpSubnetIds.clear();
            }
        }
        if (tmpSubnetIds.size() > 0) {
            edgeSubnets.addAll(edgeClient.getSubnets(tmpSubnetIds).getSubnets());
        }
        return edgeSubnets;
    }

    /**
     * 批量查询 Subnet 详情（批量个数无限制）
     *  1. 若 subnetUuids 为 null，则该方法返回 null；
     *  2. 若 subnetUuids 元素个数为 0，则该方法返回元素个数为 0 的 List；
     *
     * @param subnetUuids subnetUuids
     * @return edgeVpcs
     */
    public List<EdgeSubnet> getSubnetsBySubnetUuids(Collection<String> subnetUuids) {
        if (subnetUuids == null) {
            return null;
        }
        int batchSize = 10;
        List<EdgeSubnet> edgeSubnets = new ArrayList<>(subnetUuids.size());
        List<String> tmpSubnetUuids = new ArrayList<>(batchSize);
        EdgeClient edgeClient = clientFactory.createEdgeClient();
        for (String subnetUuid : subnetUuids) {
            tmpSubnetUuids.add(subnetUuid);
            if (tmpSubnetUuids.size() == batchSize) {
                edgeSubnets.addAll(edgeClient.getSubnets(tmpSubnetUuids).getSubnets());
                tmpSubnetUuids.clear();
            }
        }
        if (tmpSubnetUuids.size() > 0) {
            edgeSubnets.addAll(edgeClient.getSubnets(tmpSubnetUuids).getSubnets());
        }
        return edgeSubnets;
    }

    public Map<String, EdgeSubnet> getSubnetUuidMap(Collection<String> subnetUuids) {
        if (subnetUuids == null) {
            return null;
        }
        List<EdgeSubnet> subnets = getSubnetsBySubnetUuids(subnetUuids);
        Map<String, EdgeSubnet> subnetUuidMap = new HashMap<>(subnets.size());
        for (EdgeSubnet subnet : subnets) {
            subnetUuidMap.put(subnet.getSubnetUuid(), subnet);
        }
        return subnetUuidMap;
    }



    /**
     * 用于区分 subnetId, subnetUuid。
     *  1. subnetId 格式如："subnetId": "sbn-qstqimne"
     *  2. subnetUuid 格式如："subnetUuid": "dc81f47f-e803-4f93-9d99-14f89493abfc"
     *
     * @param id id
     * @return true or false
     */
    public static boolean isSubnetId(String id) {
        if (id == null) {
            return false;
        }
        if (id.startsWith(SUBNET_ID_PREFIX)) {
            return true;
        }
        return false;
    }
}
