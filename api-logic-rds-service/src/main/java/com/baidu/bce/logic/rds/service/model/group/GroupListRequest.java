package com.baidu.bce.logic.rds.service.model.group;

import com.baidu.bce.logic.core.request.ListRequest;
import com.baidu.bce.logic.core.request.OrderModel;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;

/**
 * Created by luping03 on 17/11/5.
 */
public class GroupListRequest extends ListRequest {
    private int daysToExpiration = -1; // 最近多少天过期，<0为不限制

    private String resourceName; // 服务名称
    private String resourceId; // 服务id
    private String serviceType;

    public GroupListRequest(int daysToExpiration, String order, String orderBy, Integer pageNo, Integer pageSize) {
        if (StringUtils.isNotEmpty(order) && StringUtils.isNotEmpty(orderBy)) {
            OrderModel orderModel = new OrderModel();
            orderModel.setOrder(order);
            orderModel.setOrderBy(orderBy);
            this.orders = Collections.singletonList(orderModel);
        }
        this.pageNo = pageNo == null ? 1 : pageNo;
        this.pageSize = pageSize == null ? 10 : pageSize;
        this.daysToExpiration = daysToExpiration;
    }

    public GroupListRequest(int daysToExpiration, String order, String orderBy, Integer pageNo, Integer pageSize,
                            String resourceId, String resourceName) {
        this(daysToExpiration, order, orderBy, pageNo, pageSize);
        this.resourceId = resourceId;
        this.resourceName = resourceName;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public int getDaysToExpiration() {
        return daysToExpiration;
    }

    public void setDaysToExpiration(int daysToExpiration) {
        this.daysToExpiration = daysToExpiration;
    }
}
