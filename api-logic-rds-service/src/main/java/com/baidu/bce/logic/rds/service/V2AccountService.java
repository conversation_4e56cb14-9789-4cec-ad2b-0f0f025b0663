package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelAccountDetailRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelDbPrivilege;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelDeleteAccountRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelListResponse;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelPrivilegeScopeResponse;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelTbPrivilege;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelUpdateDescRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.TableLevelUpdatePrivilegeRequest;
import com.baidu.bce.internalsdk.rds.model.account.V2Account;
import com.baidu.bce.internalsdk.rds.model.account.V2AccountListResponse;
import com.baidu.bce.internalsdk.rds.model.account.V2AccountUpdatePrivilegesRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.instance.AccountShowResponse;
import com.baidu.bce.internalsdk.rds.model.iam.IamDecryptRequest;
import com.baidu.bce.internalsdk.rds.model.iam.IamDecryptResponse;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.logic.rds.service.constant.AccountType;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Service
public class V2AccountService {

    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(V2AccountService.class);

    @Autowired
    LogicRdsClientFactory logicRdsClientFactory;

    @Autowired
    InstanceService instanceService;

    /*public void createAccount(String instanceId, AccountCreateRequest request){
        logicRdsClientFactory.createRdsClient2()
                .createAccount(instanceId, request);
    }

    public AccountShowResponse getAccountShow(String instanceId, String accountName){
        return logicRdsClientFactory.createRdsClient2()
                .getAccountShow(instanceId, accountName);
    }*/
    public V2AccountListResponse list(String instanceId, String from) {
        instanceService.checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);
        V2AccountListResponse response =
                logicRdsClientFactory.createRdsClient2ByInstanceId(instanceId).accountList(instanceId);
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            standardForApi(response.getAccounts());
        }
        return response;
    }

    private void standardForApi(Collection<V2Account> accounts) {
        if (accounts == null || accounts.size() == 0) {
            return;
        }
        for (V2Account account : accounts) {
            standardForApi(account);
        }
    }

    private void standardForApi(V2Account account) {
        if (account != null) {
            account.setSuperUserFlag(BasisUtils.upperCaseFirstChar(account.getSuperUserFlag()));
            account.setAccountStatus(BasisUtils.upperCaseFirstChar(account.getAccountStatus()));
            account.setType(AccountType.getApiValue(account.getType()));
        }
    }

    public void create(String instanceId, Account account, String from, String ak) {
        Instance instance = instanceService
                .checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);

        String pw = account.getPassword();
        if (from.equalsIgnoreCase(RDSConstant.FROM_API)) {
            pw = decryptPasswordFromIam(account.getPassword(), ak);
            account.setPassword(pw);
        }
//        if (!Pattern.matches(PatternString.PATTERN_ACCOUNT_PW, pw)) {
//            LOGGER.error("create account password invaild {}", pw);
//            throw new RDSExceptions.ParamValidationException();
//        }

        checkCreateAccount(instance, account);

        RDSClient2 rdsClient2 = logicRdsClientFactory.createRdsClient2ByInstanceId(instanceId);
        rdsClient2.accountCreate(instanceId, account);
    }

    public void checkCreateAccount(Instance instance, Account account) {
        // SuperUserFlag 参数校验
        if (StringUtils.isEmpty(account.getSuperUserFlag())) {
            account.setSuperUserFlag(RDSConstant.SUPER_USER_FLAG_COMMON);
        } else {
            account.setSuperUserFlag(BasisUtils.lowerCaseFirstChar(account.getSuperUserFlag()));
            if (!account.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_SUPER)
                    && !account.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_COMMON)) {
                throw new RDSExceptions.ParamValidationException();
            }
        }
        // Type 参数校验
        if (StringUtils.isEmpty(account.getType())) {
            account.setType(AccountType.MASTER.getValue());
        } else {
            account.type(AccountType.getValue(account.getType()));
            if (!account.getType().equals(AccountType.MASTER.getValue())
                    && !account.getType().equals(AccountType.PROXY.getValue())) {
                throw new RDSExceptions.ParamValidationException();
            }
        }
        // DatabasePrivileges 参数校验
        if (account.getDatabasePrivileges() != null && account.getDatabasePrivileges().size() != 0) {
            for (Account.DatabasePrivilege privilege : account.getDatabasePrivileges()) {
                privilege.setAuthType(BasisUtils.lowerCaseFirstChar(privilege.getAuthType()));
            }
        }

        if (RDSConstant.INSTANCE_TYPE_MASTER.equalsIgnoreCase(instance.getInstanceType())
                || RDSConstant.INSTANCE_TYPE_RAFT.equalsIgnoreCase(instance.getInstanceType())) {
            RDSClient rdsClient = logicRdsClientFactory.createRdsClientByInstanceId(instance.getInstanceId());
            Collection<Account> accounts = rdsClient.accountList(instance.getInstanceId()).getAccounts();
            // super账号只允许创建一个
            if (account.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_SUPER)) {
                for (Account ac : accounts) {
                    if (ac.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_SUPER)) {
//                        throw new RDSExceptions.SuperUserExistException();
                        throw new RDSBusinessExceptions.RDSSuperUserExistException();
                    }
                }
            }
            // 若实例的数据库引擎为PostgreSQL，则只允许创建Super账号
            if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_PG) && accounts.size() > 0) {
                throw new RDSExceptions.PGAccountExistException();
            }
            // 若实例的数据库引擎为SQLServer，则只允许创建Common账号
            if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER)
                    && !RDSConstant.SUPER_USER_FLAG_COMMON.equalsIgnoreCase(account.getSuperUserFlag())) {
                throw new RDSExceptions.InvalidAction();
            }
        } else {
            throw new RDSExceptions.InvalidAction();
        }
    }

    public AccountShowResponse detail(String instanceId, String accountName, String from) {
        instanceService.checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);
        AccountShowResponse response = logicRdsClientFactory
                .createRdsClient2ByInstanceId(instanceId).accountDescribe(instanceId, accountName);
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            standardForApi(response.getAccounts());
        }
        return response;
    }

    public void updatePrivileges(String instanceId,
                                 V2AccountUpdatePrivilegesRequest request, String from) {
        instanceService.checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);
        logicRdsClientFactory.createRdsClient2()
                    .accountModifyPrivilege(instanceId, request);

    }

    public void deleteAccount(String instanceId, String accountName, String from) {

        if (ObjectUtils.equals(from, "api")
                && BasisUtils.isShortId(instanceId)) {

            String instanceUUId = instanceService.findInsntaceUUidByShortId(instanceId);

            LOGGER.info("deleteAccount findInsntaceUUidByShortId instanceId : {}, instanceUUId : {}",
                    instanceId, instanceUUId);

            // 替换成uuid
            instanceId = instanceUUId;

            logicRdsClientFactory.createRdsClient2(RDSConstant.SERVICE_NAME_2).accountDelete(instanceId, accountName);
        } else {
            logicRdsClientFactory.createRdsClient2ByInstanceId(instanceId).accountDelete(instanceId, accountName);
        }
    }

    public void updatePW(String instanceId, String accountName, AccountUpdatePasswordRequest pwRequest,
                         String from, String ak) {
        if (logicRdsClientFactory.getInstanceTypeByUuid(instanceId).equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            // raft版操作
            logicRdsClientFactory.createRdsClient(RDSConstant.SERVICE_NAME_V2)
                    .accountModifyPasswordRaft(instanceId, accountName, pwRequest.getPassword()
                            , pwRequest.getEncryptedPassword());
        } else {
            String pw = pwRequest.getPassword();
            if (from.equalsIgnoreCase(RDSConstant.FROM_API)) {
                pw = decryptPasswordFromIam(pwRequest.getPassword(), ak);
                pwRequest.setPassword(pw);
            }
            logicRdsClientFactory.createRdsClient2()
                    .accountModifyPassword(instanceId, accountName, pwRequest);
        }
    }

    private String decryptPasswordFromIam(String encryptPassword, String ak) {
        String password = null;
        try {
            IamDecryptRequest decryptRequest =
                    new IamDecryptRequest().withCipherHex(encryptPassword).withAccesskeyId(ak);
            IamDecryptResponse decryptResponse = logicRdsClientFactory.createIamApiClient().decrypt(decryptRequest);
            password = decryptResponse.getRawHex();

            byte[] bytes = BasisUtils.parseHexStr2Byte(decryptResponse.getRawHex());

            if (ArrayUtils.isNotEmpty(bytes)) {
                password = new String(bytes);
            }

            LOGGER.info("decrypt string : source {},  pw {}", encryptPassword, password);
        } catch (Exception e) {
            LOGGER.error("decrypt password from iam error = {}", e);
            throw new RDSExceptions.AdminPassDecryptionException();
        }

        if (StringUtils.isEmpty(password)) {
            throw new RDSExceptions.AdminPassDecryptionException();
        }

        return password;
    }

    /*-----------------------------------table level service----------------------------------*/
    public TableLevelListResponse listTableLevel(String instanceId, String from) {
        RDSClient2 client2 = logicRdsClientFactory.createRdsClient2();
        return client2.listTableLevel(instanceId);
    }


    public void createTableLevel(String instanceId, TableLevelAccountDetailRequest account, String from, String ak) {
        Instance instance = instanceService
                .checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);

        String pw = account.getPassword();
        if (from.equalsIgnoreCase(RDSConstant.FROM_API)) {
            pw = decryptPasswordFromIam(account.getPassword(), ak);
            account.setPassword(pw);
        }
//        if (!Pattern.matches(PatternString.PATTERN_ACCOUNT_PW, pw)) {
//            LOGGER.error("create account password invaild {}", pw);
//            throw new RDSExceptions.ParamValidationException();
//        }

        checkCreateAccountTableLevel(instance, account);

        RDSClient2 rdsClient2 = logicRdsClientFactory.createRdsClient2ByInstanceId(instanceId);
        rdsClient2.accountCreateTableLevel(instanceId, account);
    }

    public TableLevelAccountDetailRequest detailTableLevel(TableLevelDeleteAccountRequest request, String from) {
        RDSClient2 client2 = logicRdsClientFactory.createRdsClient2();
        return client2.detailTableLevel(request);
    }

    public void updateRemarkTableLevel(TableLevelUpdateDescRequest request) {
        RDSClient2 client2 = logicRdsClientFactory.createRdsClient2();
        client2.updateRemarkTableLevel(request);
    }

    public void updatePWTableLevel(TableLevelUpdatePasswordRequest request, String from, String ak) {

        String pw = request.getPassword();
        if (from.equalsIgnoreCase(RDSConstant.FROM_API)) {
            pw = decryptPasswordFromIam(request.getPassword(), ak);
            request.setPassword(pw);
        }
        logicRdsClientFactory.createRdsClient2()
                .accountModifyPasswordTableLevel(request);
    }

    public void updatePrivilegesTableLevel(TableLevelUpdatePrivilegeRequest request, String from) {
        instanceService.checkInstanceByStatusForBackend(request.getInstanceId(), RDSConstant.INSTANCE_STATUS_WHITELIST);
        // 参数校验
        if (request.getPrivilege().getDatabases() != null && !request.getPrivilege().getDatabases().isEmpty()) {
            List<TableLevelDbPrivilege> databases = request.getPrivilege().getDatabases();
            for (TableLevelDbPrivilege dbPrivilege : databases) {
                if (!BasisUtils.checkKeyExists(dbPrivilege)) {
                    throw new RDSBusinessExceptions.MissingRequiredKeyException("databases");
                }
            }
        }
        if (request.getPrivilege().getTables() != null && !request.getPrivilege().getTables().isEmpty()) {
            List<TableLevelTbPrivilege> tables = request.getPrivilege().getTables();
            for (TableLevelTbPrivilege dbPrivilege : tables) {
                if (!BasisUtils.checkKeyExists(dbPrivilege)) {
                    throw new RDSBusinessExceptions.MissingRequiredKeyException("tables");
                }
            }
        }
        logicRdsClientFactory.createRdsClient2()
                .accountModifyPrivilegeTableLevel(request);
    }

    public void deleteAccountTableLevel(TableLevelDeleteAccountRequest request, String from) {
        logicRdsClientFactory.createRdsClient2()
                .deleteAccountTableLevel(request);
    }

    public TableLevelPrivilegeScopeResponse permissionScope(String instanceId, String from) {
        return logicRdsClientFactory.createRdsClient2().permissionScope(instanceId);
    }


    private void checkCreateAccountTableLevel(Instance instance, TableLevelAccountDetailRequest account) {
        // SuperUserFlag 参数校验
        if (StringUtils.isEmpty(account.getSuperUserFlag())) {
            account.setSuperUserFlag(RDSConstant.SUPER_USER_FLAG_COMMON);
        } else {
            account.setSuperUserFlag(BasisUtils.lowerCaseFirstChar(account.getSuperUserFlag()));
            if (!account.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_SUPER)
                    && !account.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_COMMON)) {
                throw new RDSExceptions.ParamValidationException();
            }
        }
        // Type 参数校验
        if (StringUtils.isEmpty(account.getType())) {
            account.setType(AccountType.MASTER.getValue());
        } else {
            account.setType(AccountType.getValue(account.getType()));
            if (!account.getType().equals(AccountType.MASTER.getValue())
                    && !account.getType().equals(AccountType.PROXY.getValue())) {
                throw new RDSExceptions.ParamValidationException();
            }
        }
        if (RDSConstant.INSTANCE_TYPE_MASTER.equalsIgnoreCase(instance.getInstanceType())
                || RDSConstant.INSTANCE_TYPE_RAFT.equalsIgnoreCase(instance.getInstanceType())) {
            RDSClient rdsClient = logicRdsClientFactory.createRdsClientByInstanceId(instance.getInstanceId());
            Collection<Account> accounts = rdsClient.accountList(instance.getInstanceId()).getAccounts();
            // super账号只允许创建一个
            if (account.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_SUPER)) {
                for (Account ac : accounts) {
                    if (ac.getSuperUserFlag().equals(RDSConstant.SUPER_USER_FLAG_SUPER)) {
//                        throw new RDSExceptions.SuperUserExistException();
                        throw new RDSBusinessExceptions.RDSSuperUserExistException();
                    }
                }
            }
            // 若实例的数据库引擎为PostgreSQL，则只允许创建Super账号
            if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_PG) && accounts.size() > 0) {
                throw new RDSExceptions.PGAccountExistException();
            }
            // 若实例的数据库引擎为SQLServer，则只允许创建Common账号
            if (instance.getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER)
                    && !RDSConstant.SUPER_USER_FLAG_COMMON.equalsIgnoreCase(account.getSuperUserFlag())) {
                throw new RDSExceptions.InvalidAction();
            }
        } else {
            throw new RDSExceptions.InvalidAction();
        }
    }


}
