package com.baidu.bce.logic.rds.service.group;

import com.baidu.bce.internalsdk.rds.model.account.Account;
import com.baidu.bce.internalsdk.rds.model.account.AccountGetResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountListResponse;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePasswordRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdatePrivilegesRequest;
import com.baidu.bce.internalsdk.rds.model.account.AccountUpdateRemarkRequest;
import com.baidu.bce.internalsdk.rds.model.group.CreateGroupRequest;
import com.baidu.bce.internalsdk.rds.model.group.FollowerIdRequest;
import com.baidu.bce.internalsdk.rds.model.group.GroupInfo;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupAccouontCheckNameResponse;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupDetailResponse;
import com.baidu.bce.internalsdk.rds.model.group.InstanceGroupListResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.service.model.group.GroupListRequest;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by luping03 on 17/11/3.
 */
@Service
public class InstanceGroupAccountService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceGroupAccountService.class);

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    InstanceDao instanceDao;

    public LogicPageResultResponse<GroupInfo> list(GroupListRequest listRequest) {

        InstanceGroupListResponse listResponse = clientFactory.createRdsClient().instanceGroupList();

        LogicPageResultResponse<GroupInfo> page =  listForPage(listResponse.getGroups(), null,
                null, listRequest.getPageNo(), listRequest.getPageSize());

        return page;
    }

    
    
    public LogicPageResultResponse<GroupInfo> listForPage(List<GroupInfo> groupInfos,
                                                                 String order,
                                                                 String orderBy,
                                                                 int pageNo,
                                                                 int pageSize) {
        LogicPageResultResponse<GroupInfo> response = new LogicPageResultResponse<>();
        response.setOrder(order);
        response.setOrderBy(orderBy);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);

        if (groupInfos == null || groupInfos.size() == 0) {
            return response;
        }
        if (pageNo < 1 || pageSize < 1) {
            return response;
        }

        int pageStart = (pageNo - 1) * pageSize;
        int pageEnd = pageNo * pageSize - 1;

        int start = pageStart;
        int end = pageEnd > groupInfos.size() ? groupInfos.size() - 1 : pageEnd;

        LOGGER.info("group page start {}, end {}", start, end);

        List<GroupInfo> instanceForPage = groupInfos.subList(start, end);

        response.setTotalCount(groupInfos.size());
        response.setResult(instanceForPage);
        return response;
    }

    public InstanceGroupDetailResponse detail(String groupId) {
        InstanceGroupDetailResponse listResponse = clientFactory.createRdsClient().instanceGroupDetail(groupId);
        return listResponse;
    }

    public void checkGtid(String instanceId) {
        clientFactory.createRdsClient().instanceGroupCheckGtid(instanceId);
    }

    public void checkPing(String sourceId, String targetId) {
        clientFactory.createRdsClient().instanceGroupCheckPing(sourceId, targetId);
    }

    public void checkData(String instanceId) {
        clientFactory.createRdsClient().instanceGroupCheckData(instanceId);
    }

    public void createGroup(CreateGroupRequest request) {
        clientFactory.createRdsClient().instanceGroupCreate(request);
    }

    public void joinGroup(FollowerIdRequest request) {
        clientFactory.createRdsClient().instanceGroupJoin(request);
    }

    public void changeName(String groupId, String name) {
        clientFactory.createRdsClient().instanceGroupChangeName(groupId, name);
    }

    public void delete(String groupId) {
        clientFactory.createRdsClient().instanceGroupDelete(groupId);
    }

    public void changeLeader(String groupId, String newLeader) {
        clientFactory.createRdsClient().instanceGroupChangeLeader(groupId, newLeader);
    }

    public void signOut(String groupId, String instanceId) {
        clientFactory.createRdsClient().instanceGroupSignOut(groupId, instanceId);
    }

    public AccountListResponse accountList(String groupId) {
        return clientFactory.createRdsClient().instanceGroupAccountList(groupId);
    }

    public AccountGetResponse accountDetail(String groupId, String accountName) {
        return clientFactory.createRdsClient().instanceGroupAccountDetail(groupId, accountName);
    }

    public InstanceGroupAccouontCheckNameResponse accountCheckName(String groupId, String accountName) {
        return null;
    }

    public void accountCreate(String groupId, Account request, String ak) {
    }

    public void accountUpdatePW(String groupId, String accountName, AccountUpdatePasswordRequest pwRequest) {
    }

    public void accountUpdateRemark(String groupId, String accountName, AccountUpdateRemarkRequest remarkRequest) {
    }

    public void accountUpdatePrivileges(String groupId, String accountName, AccountUpdatePrivilegesRequest request) {
    }
}
