package com.baidu.bce.logic.rds.service.model.tag;

import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by luping03 on 17/7/7.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssignTagRequest {
    /**
     * 资源列表
     */
    @IdPermission
    private List<LogicalAssignResource> resources;

    /**
     * 是否关联绑定
     */
    private boolean relationTag;

    public List<LogicalAssignResource> getResources() {
        return resources;
    }

    public void setResources(List<LogicalAssignResource> resources) {
        this.resources = resources;
    }

    public boolean isRelationTag() {
        return relationTag;
    }

    public void setRelationTag(boolean relationTag) {
        this.relationTag = relationTag;
    }

    @Override
    public String toString() {
        return "AssignTagRequest{"
                + "resources=" + resources
                + ", relationTag=" + relationTag
                + '}';
    }
}
