package com.baidu.bce.logic.rds.service;

import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by luping03 on 17/7/6.
 */
@Service("logicRdsTagService")
public class TagLogicalService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TagLogicalService.class);

    @Autowired
    LogicRdsClientFactory rdsClientFactory;

    /**
     * 获取以resourceUuid为key的tag列表Map
     * @param resourceUuids
     * @return
     */
    public Map<String, List<Tag>> queryTagsByResourceUuids(List<String> resourceUuids) {
        Map<String, List<Tag>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(resourceUuids)) {
            return result;
        }
        // 限制单次查询数量，入参不可多于1000
        List<List<String>> batchesUuids = new ArrayList<>();
        List<String> currentUuids = new ArrayList<>();
        int batchSize = 500;
        int sizeOfResource = resourceUuids.size();
        if (resourceUuids.size() >= 1000) {
            LOGGER.debug("current get tag count is more than 1000...");
            for (int i = 0; i < resourceUuids.size(); i++) {
                currentUuids.add(resourceUuids.get(i));
                if (currentUuids.size() == batchSize || i == sizeOfResource - 1) {
                    batchesUuids.add(currentUuids);
                    currentUuids = new ArrayList<>();
                }
            }
            // 处理结果集batchesUuids
            for (List<String> batchesUuid : batchesUuids) {
                try {
                    FullTagListRequest request = new FullTagListRequest();
                    request.setResourceUuids(batchesUuid);
                    LogicalTagClient tagClient = rdsClientFactory.createLogicalTagClient();
                    TagAssociationFulls response = tagClient.listFullTags(request);
                    for (TagAssociationFull tagFull : response.getTagAssociationFulls()) {
                        String resourceUuid  = tagFull.getResourceUuid();
                        Tag tag = new Tag();
                        tag.setTagKey(tagFull.getTagKey());
                        tag.setTagValue(tagFull.getTagValue());
                        if (result.containsKey(resourceUuid)) {
                            result.get(resourceUuid).add(tag);
                        } else {
                            List<Tag> temp = new ArrayList<>();
                            temp.add(tag);
                            result.put(resourceUuid, temp);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("queryTagsByResourceUuids error:{}", e);
                }
            }
        } else {
            try {
                FullTagListRequest request = new FullTagListRequest();
                request.setResourceUuids(resourceUuids);
                LogicalTagClient tagClient = rdsClientFactory.createLogicalTagClient();
                TagAssociationFulls response = tagClient.listFullTags(request);
                for (TagAssociationFull tagFull : response.getTagAssociationFulls()) {
                    String resourceUuid  = tagFull.getResourceUuid();
                    Tag tag = new Tag();
                    tag.setTagKey(tagFull.getTagKey());
                    tag.setTagValue(tagFull.getTagValue());
                    if (result.containsKey(resourceUuid)) {
                        result.get(resourceUuid).add(tag);
                    } else {
                        List<Tag> temp = new ArrayList<>();
                        temp.add(tag);
                        result.put(resourceUuid, temp);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("queryTagsByResourceUuids error:{}", e);
            }
        }
        return result;
    }
}
