package com.baidu.bce.logic.rds.service.config;

import com.baidu.bce.logic.rds.service.util.RDSConstant;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Locale;

@Component
public class I18nConfiguration {

    private static final String I18N_RESOURCE_BUNDLE_FOLDER = "i18n/messages";
    private static final Logger LOGGER = LoggerFactory.getLogger(I18nConfiguration.class);

    private static class RDSLocaleResolver implements LocaleResolver {

        @Override
        public Locale resolveLocale(HttpServletRequest httpServletRequest) {
            Locale locale = Locale.SIMPLIFIED_CHINESE;
            if (httpServletRequest.getRequestURI().startsWith(RDSConstant.CONSOLE_API_PREFIX)) {
                locale = Locale.SIMPLIFIED_CHINESE;
            } else if (httpServletRequest.getRequestURI().startsWith(RDSConstant.OPEN_API_PREFIX)) {
                locale = Locale.US;
            }
            String localeParam = httpServletRequest.getParameter(RDSConstant.I18nConstants.LOCALE_QUERY_PARAM_NAME);
            LOGGER.debug("Before resolveLocale: {} => {}",
                    RDSConstant.I18nConstants.LOCALE_QUERY_PARAM_NAME, localeParam);
            // 现在前端是传入query参数locale=zh-cn/en-us
            if (StringUtils.isNotBlank(localeParam)
                    && localeParam.contains(RDSConstant.I18nConstants.LOCALE_PARAM_SEPARATOR)) {
                String[] split = localeParam.split(RDSConstant.I18nConstants.LOCALE_PARAM_SEPARATOR);
                if (split.length > 1) {
                    locale = new Locale(split[0], split[1]);
                }
            }
            LOGGER.info("After resolveLocale: {} => {}", RDSConstant.I18nConstants.LOCALE_QUERY_PARAM_NAME, locale);
            return locale;
        }

        @Override
        public void setLocale(HttpServletRequest request, HttpServletResponse response, Locale locale) {
            throw new UnsupportedOperationException(
                    "Cannot set locale - this is meaningless");
        }
    }

    /**
     * 配置自定义LocaleResolver以解析query参数中的locale字符串
     *
     * @return bean
     */
    @Bean
    public LocaleResolver localeResolver() {
        return new RDSLocaleResolver();
    }

    @Bean(name = "messageSource")
    public MessageSource messageSource() {
        ResourceBundleMessageSource rbMessageSource = new ResourceBundleMessageSource();
        rbMessageSource.setBasename(I18N_RESOURCE_BUNDLE_FOLDER);
        rbMessageSource.setDefaultEncoding(StandardCharsets.UTF_8.name());
        return rbMessageSource;
    }

}
