package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.model.errorlog.ErrorlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.errorlog.ErrorlogListResponse;
import com.baidu.bce.internalsdk.rds.model.errorlog.OpenapiErrorlogListResponse;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogErrorDetails;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogSlowDetails;
import com.baidu.bce.internalsdk.rds.model.errorlog.LogDetailRequest;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by luping03 on 17/11/3.
 */
@Service
public class ErrorlogService {

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private InstanceService instanceService;

    public ErrorlogListResponse list(String instanceId, String datetime) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClientByInstanceId(instanceId).errorlogList(instanceId, datetime);
    }

    public OpenapiErrorlogListResponse list2(String instanceId, String datetime) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClientByInstanceId(instanceId).errorlogList2(instanceId, datetime);
    }

    public ErrorlogGetResponse detail(String instanceId, String logId, Integer downloadValidTimeInSec) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClient().errorlogGet(instanceId, logId, downloadValidTimeInSec);
    }

//    public void check(String instanceId, BinlogDateTime dateTime) {
//        RDSClient2 client2 = clientFactory.createRdsClient2();
//        SnapshotListWithTimeResponse snapshotResponse = client2.snapshotList(instanceId);
//        if (dateTime.getDatetime().compareTo(snapshotResponse.getPeriod().getBegin()) < 0
//                || dateTime.getDatetime().compareTo(snapshotResponse.getPeriod().getEnd()) > 0) {
//            throw new RDSExceptions.BinlogTimeInvalidException();
//        }
//        RDSClient client = clientFactory.createRdsClient();
//        client.binlogCheck(instanceId, dateTime.getDatetime());
//    }
public LogErrorDetails getErrorLogDetails(LogDetailRequest logDetailRequest) {
    if (BasisUtils.isShortId(logDetailRequest.getInstanceId())) {
        logDetailRequest.setInstanceId(
                instanceService.findInsntaceUUidByShortId(
                        logDetailRequest.getInstanceId())
        );
    }
    LogErrorDetails logErrorDetails
            = clientFactory.createUserDbscClient()
            .getErrorLogDetails(logDetailRequest);
    return logErrorDetails;
}
    public LogSlowDetails getSlowLogDetails(LogDetailRequest logDetailRequest) {
        if (BasisUtils.isShortId(logDetailRequest.getInstanceId())) {
            logDetailRequest.setInstanceId(
                    instanceService.findInsntaceUUidByShortId(
                            logDetailRequest.getInstanceId())
            );
        }
        LogSlowDetails logSlowDetails
                = clientFactory.createUserDbscClient()
                .getSlowLogDetails(logDetailRequest);
        return logSlowDetails;
    }
}
