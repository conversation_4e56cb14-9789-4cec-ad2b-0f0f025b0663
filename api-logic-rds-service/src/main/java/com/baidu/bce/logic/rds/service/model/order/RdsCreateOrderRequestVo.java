package com.baidu.bce.logic.rds.service.model.order;

import com.baidu.bce.plat.servicecatalog.model.order.PaymentModel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

/**
 * Created by luping03 on 18/6/26.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RdsCreateOrderRequestVo<T> {
    @Valid
    private Set<PaymentModel> paymentMethod;

    /**
     * 标记是属于同一个订单的活动订单
     */
    @Length(max = 64)
    private String ticketId;

    /**
     * 标记活动订单由多少订单组成
     */
    @Max(10)
    private int total;

    @Valid
    @Size(min = 1)
//    @IdPermission
    private List<Item<T>> items;

    public List<Item<T>> getItems() {
        return items;
    }

    public void setItems(List<Item<T>> items) {
        this.items = items;
    }

    public Set<PaymentModel> getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(Set<PaymentModel> paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Item<T> {

//        @IdPermission
        @Valid
        private T config;

        @Valid
        private Set<PaymentModel> paymentMethod;

        private int purchaseOrder;

        public T getConfig() {
            return config;
        }

        public void setConfig(T config) {
            this.config = config;
        }

        public Set<PaymentModel> getPaymentMethod() {
            return paymentMethod;
        }

        public void setPaymentMethod(Set<PaymentModel> paymentMethod) {
            this.paymentMethod = paymentMethod;
        }

        public int getPurchaseOrder() {
            return purchaseOrder;
        }

        public void setPurchaseOrder(int purchaseOrder) {
            this.purchaseOrder = purchaseOrder;
        }
    }
}
