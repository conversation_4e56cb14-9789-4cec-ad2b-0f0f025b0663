package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.binlog.BinlogDateTime;
import com.baidu.bce.internalsdk.rds.model.slowlog.PgLogListResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.PglogDownloadResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogListResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.OpenapiSlowlogListResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogSummaryRequest;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogSummaryResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotListWithTimeResponse;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * Created by luping03 on 17/11/3.
 */
@Service
public class SlowlogService {

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private InstanceService instanceService;

    public SlowlogListResponse list(String instanceId, String datetime) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClientByInstanceId(instanceId).slowlogList(instanceId, datetime);
    }

    public OpenapiSlowlogListResponse list2(String instanceId, String datetime) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClientByInstanceId(instanceId).slowlogList2(instanceId, datetime);
    }

    public SlowlogGetResponse detail(String instanceId, String logId, Integer downloadValidTimeInSec) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClientByInstanceId(instanceId)
                .slowlogGet(instanceId, logId, downloadValidTimeInSec);
    }

    public SlowlogSummaryResponse slowlogSummary(SlowlogSummaryRequest request) {
        if (BasisUtils.isShortId(request.getInstanceId())) {
            request.setInstanceId(
                    instanceService.findInsntaceUUidByShortId(request.getInstanceId())
            );
        }
        return clientFactory.createRdsClientByInstanceId(request.getInstanceId())
                .slowlogSummary(request);
    }

    public PgLogListResponse pgList(String instanceId, String date) {
        return clientFactory.createRdsClient3().pglogList(instanceId, date);
    }

    public PglogDownloadResponse downloadPglog(String instanceId, String pglogId, Integer downloadValidTimeInSec) {
        return clientFactory.createRdsClient3().downloadPglog(instanceId, pglogId, downloadValidTimeInSec);
    }

    public void checkPgWal(String instanceId, BinlogDateTime dateTime) {
        RDSClient2 client2 = clientFactory.createRdsClient2();
        SnapshotListWithTimeResponse snapshotResponse = client2.snapshotList(instanceId);
        if (dateTime.getDatetime().compareTo(snapshotResponse.getPeriod().getBegin()) < 0
                || dateTime.getDatetime().compareTo(snapshotResponse.getPeriod().getEnd()) > 0) {
            throw new RDSExceptions.BinlogTimeInvalidException();
        }
        RDSClient client = clientFactory.createRdsClient();
        client.checkPgWal(instanceId, dateTime.getDatetime());
    }
}
