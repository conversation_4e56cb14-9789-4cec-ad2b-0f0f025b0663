package com.baidu.bce.logic.rds.service.constant;

import org.apache.commons.lang.StringUtils;

/**
 * Created by luping03 on 17/12/28.
 */
public enum InstanceType {
    INSTANCE_TYPE_MASTER ("master", "Master"),
    INSTANCE_TYPE_REPLICA ("readReplica", "ReadReplica"),
    INSTANCE_TYPE_PROXY ("rdsproxy", "RdsProxy");

    private String value;
    private String valueInApi;

    InstanceType(String value, String valueInApi) {
        this.value = value;
        this.valueInApi = valueInApi;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValueInApi() {
        return valueInApi;
    }

    public static String getValueInApi(String value) {
        if (StringUtils.isNotEmpty(value)) {
            InstanceType[] arr = values();
            int len = arr.length;

            for (int i = 0; i < len; ++i) {
                InstanceType instanceType = arr[i];
                if (instanceType.getValueInApi().equalsIgnoreCase(value)) {
                    return instanceType.getValueInApi();
                }
            }
        }
        return null;
    }
    
    public void setValueInApi(String valueInApi) {
        this.valueInApi = valueInApi;
    }
}
