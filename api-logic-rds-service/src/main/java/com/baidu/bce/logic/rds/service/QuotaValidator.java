/*
 * Copyright (C) 2015 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.order.model.ExtendedGetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class QuotaValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(RdsQuotaService.class);

    @Autowired
    private RdsQuotaService quotaService;

    @Autowired
    private LogicRdsClientFactory rdsClientFactory;

    @Autowired
    private RegionConfiguration regionConfiguration;

    public abstract ServiceType getServiceType();

    public abstract RdsQuotaService.QuotaType getQuotaType();

    public Integer getCurrentUserQuota() {
        String accountId = rdsClientFactory.getAccountId();
        return quotaService.getUserQuota(accountId, getQuotaType());
    }

    public abstract Integer getActiveInstanceCount(String sourceInstanceId);

    public Boolean validate(String sourceInstanceId) {
        return validate(Integer.valueOf(0), sourceInstanceId);
    }

    public Boolean validate(Integer readyCreateInstanceCount, String sourceInstanceId) {
        Boolean valid = false;
        Integer quota = getCurrentUserQuota();
        Integer count = getActiveInstanceCount(sourceInstanceId);
        Integer allUseCount = getActiveInstanceCount(null);
        valid = quota.compareTo(count) > 0 ? Boolean.TRUE : Boolean.FALSE;

        if (valid) {
            count += readyCreateInstanceCount == null ? 0 : readyCreateInstanceCount;
            valid = quota.compareTo(count) < 0 ? Boolean.FALSE : Boolean.TRUE;
        }
        if (valid && StringUtils.isNotEmpty(sourceInstanceId)) {
            Integer allReplica = quotaService.getUserQuota(rdsClientFactory.getAccountId(),
                    RdsQuotaService.QuotaType.allReplica);
            LOGGER.info("allReplica count:{} ", allReplica);
            valid = allReplica.compareTo(allUseCount) > 0 ? Boolean.TRUE : Boolean.FALSE;
            if (valid) {
                allUseCount += readyCreateInstanceCount == null ? 0 : readyCreateInstanceCount;
                valid = allReplica.compareTo(allUseCount) < 0 ? Boolean.FALSE : Boolean.TRUE;
            }
        }

        return valid;
    }

    protected Resources getActiveResourceList() {
        String accountId = rdsClientFactory.getAccountId();
        ExtendedGetResourcesRequest getResourcesRequest =
                new ExtendedGetResourcesRequest()
                        .withLimit(Integer.MAX_VALUE)
                        .withAccountId(accountId)
                        .withServiceType(getServiceType().name())
                        .withRegion(regionConfiguration.getCurrentRegion())
                        .withStatusList(ResourceStatus.RUNNING, ResourceStatus.INIT);
        return rdsClientFactory.createResourceClient().queryList(getResourcesRequest);
    }
}
