package com.baidu.bce.logic.rds.service;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.SimpleVpcVo;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeSubnet;
import com.baidu.bce.internalsdk.rds.model.edge.EdgeVpc;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用于封装中心云区域（例如：北京、保定、广州等区域）和边缘计算区域公共的 VPC、子网等逻辑
 *
 * <AUTHOR>
 * @since 12/5/22
 */
@Service
public class RdsVpcWrapperService {

    @Autowired
    private RegionConfiguration regionConfiguration;

    /**
     * 中心云 VPC、子网等
     */
    @Autowired
    private RdsVpcService rdsVpcService;

    /**
     * 边缘云计算节点、VPC、子网等
     */
    @Autowired
    private EdgeService edgeService;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    /**
     * 沙盒环境，edge 区域服务需配置 region.currentRegion=bj，不然无法创建新购实例订单，使用 Billing 服务时甚至会有更多问题。
     *  所以，这里先增加一个配置项用于区分 edge 区域。
     */
    @Value("${region.isEdge:false}")
    private boolean isEdgeRegion;

    public boolean isEdgeRegion() {
        // 目前，仅仅根据这一个判断条件即可区分，后续可以按需增加更多区分条件
        if (isEdgeRegion) {
            return true;
        }
        return RDSConstant.REGION_EDGE.equals(regionConfiguration.getCurrentRegion());
    }

    /**
     * SimpleVpcVo 是中心云 VPC 服务返回的实体类，RDS 代码中已经耦合了该类。
     * 这里重新封装一层，使其同时支持中心云和边缘云。
     *
     * @param vpcUuids vpcUuids 批量个数不限
     * @return List<SimpleVpcVo>
     */
    public List<SimpleVpcVo> getSimpleVpcVosByVpcUuids(Collection<String> vpcUuids) {
        if (!isEdgeRegion()) {
            return rdsVpcService.getSimpleVpcVosByVpcUuids(vpcUuids);
        }

        List<EdgeVpc> edgeVpcs = edgeService.getVpcsByVpcUuids(vpcUuids);
        List<SimpleVpcVo> simpleVpcVos = new ArrayList<>(edgeVpcs.size());
        for (EdgeVpc edgeVpc : edgeVpcs) {
            simpleVpcVos.add(convertEdgeVpc(edgeVpc));
        }
        return simpleVpcVos;
    }

    /**
     * SimpleVpcVo 是中心云 VPC 服务返回的实体类，RDS 代码中已经耦合了该类。
     * 这里重新封装一层，使其同时支持中心云和边缘云。
     *
     * @param vpcUuids vpcUuids 批量个数不限
     * @return Map<String, SimpleVpcVo>
     */
    public Map<String, SimpleVpcVo> getVpcUuidSimpleVpcVoMap(Collection<String> vpcUuids) {
        if (vpcUuids == null) {
            return null;
        }
        List<SimpleVpcVo> simpleVpcVos = getSimpleVpcVosByVpcUuids(vpcUuids);
        Map<String, SimpleVpcVo> vpcUuidSimpleVpcVoMap = new HashMap<>(simpleVpcVos.size());
        for (SimpleVpcVo simpleVpcVo : simpleVpcVos) {
            vpcUuidSimpleVpcVoMap.put(simpleVpcVo.getVpcId(), simpleVpcVo);
        }
        return vpcUuidSimpleVpcVoMap;
    }

    /**
     * SubnetVo 是中心云 VPC 服务返回的实体类，RDS 代码中已经耦合了该类。
     * 这里重新封装一层，使其同时支持中心云和边缘云。
     *
     * @param subnetUuids subnetUuids 批量个数不限
     * @return List<SubnetVo>
     */
    public List<SubnetVo> getSubnetVosBySubnetUuids(Collection<String> subnetUuids) {
        if (!isEdgeRegion()) {
            return rdsVpcService.getSubnetVosBySubnetUuids(subnetUuids);
        }

        List<EdgeSubnet> edgeSubnets = edgeService.getSubnetsBySubnetUuids(subnetUuids);
        List<SubnetVo> subnetVos = new ArrayList<>(edgeSubnets.size());
        for (EdgeSubnet edgeSubnet : edgeSubnets) {
            subnetVos.add(convertEdgeSubnet(edgeSubnet));
        }
        return subnetVos;
    }

    /**
     * SubnetVo 是中心云 VPC 服务返回的实体类，RDS 代码中已经耦合了该类。
     * 这里重新封装一层，使其同时支持中心云和边缘云。
     *
     * @param subnetUuids subnetUuids 批量个数不限
     * @return Map<String, SubnetVo>
     */
    public Map<String, SubnetVo> getSubnetUuidSubnetVoMap(Collection<String> subnetUuids) {
        if (subnetUuids == null) {
            return null;
        }
        List<SubnetVo> subnetVos = getSubnetVosBySubnetUuids(subnetUuids);
        Map<String, SubnetVo> subnetUuidSubnetVoMap = new HashMap<>(subnetVos.size());
        for (SubnetVo subnetVo : subnetVos) {
            subnetUuidSubnetVoMap.put(subnetVo.getSubnetUuid(), subnetVo);
        }
        return subnetUuidSubnetVoMap;
    }

    private SimpleVpcVo convertEdgeVpc(EdgeVpc edgeVpc) {
        SimpleVpcVo simpleVpcVo = new SimpleVpcVo();
        simpleVpcVo.setVpcId(edgeVpc.getVpcUuid());
        simpleVpcVo.setShortId(edgeVpc.getVpcId());
        simpleVpcVo.setName(edgeVpc.getName());
        simpleVpcVo.setCidr(edgeVpc.getCidr());
        // 边缘云 VPC 没有 status 属性，这里暂时默认其为：可用
        simpleVpcVo.setStatus(0);
        simpleVpcVo.setCreateTime(edgeVpc.getCreatedTime());
        simpleVpcVo.setDescription(edgeVpc.getDescription());
        simpleVpcVo.setDefaultVpc(edgeVpc.getIsDefault());
        // 下面一些属性，边缘云 VPC 没有，暂时不设置
        return simpleVpcVo;
    }

    private SubnetVo convertEdgeSubnet(EdgeSubnet edgeSubnet) {
        SubnetVo subnetVo = new SubnetVo();
        subnetVo.setName(edgeSubnet.getName());
        subnetVo.setSubnetId(edgeSubnet.getSubnetId());
        subnetVo.setSubnetUuid(edgeSubnet.getSubnetUuid());
        subnetVo.setShortId(edgeSubnet.getSubnetId());
        subnetVo.setCidr(edgeSubnet.getCidr());
//        subnetVo.setVpcId(edgeSubnet.getVpcId());
        subnetVo.setVpcShortId(edgeSubnet.getVpcId());
        subnetVo.setDescription(edgeSubnet.getDescription());
        // 下面一些属性，边缘云子网没有，暂时不设置
        return subnetVo;
    }



}
