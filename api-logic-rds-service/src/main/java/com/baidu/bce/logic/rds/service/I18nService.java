package com.baidu.bce.logic.rds.service;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.Locale;

/**
 * 国际化服务,根据locale获取对应的message
 */
@Service
public class I18nService {

    private static final Logger LOGGER = LoggerFactory.getLogger(I18nService.class);

    @Autowired
    private MessageSource messageSource;

    public Locale getLocale() {
        return LocaleContextHolder.getLocale();
    }

    public String getMessage(String i18nKey) {
        return getMessage(i18nKey, null, null);
    }

    public String getMessage(String i18nKey, String defaultMsg) {
        return getMessage(i18nKey, defaultMsg, null);
    }

    public String getMessage(String i18nKey, Object[] args) {
        return getMessage(i18nKey, null, args);
    }

    public String getMessage(String i18nKey, String defaultMsg, Object[] args) {
        Locale locale = LocaleContextHolder.getLocale();
        LOGGER.info("Locale => {}", locale);
        String i18nMsg = messageSource.getMessage(i18nKey, args, defaultMsg, locale);
        LOGGER.debug("i18nKey = {}, i18nMsg = {}", i18nKey, i18nMsg);
        if (StringUtils.isBlank(i18nMsg)) {
            return defaultMsg;
        }
        LOGGER.info("i18n returned => {}", i18nMsg);
        return i18nMsg;
    }
}
