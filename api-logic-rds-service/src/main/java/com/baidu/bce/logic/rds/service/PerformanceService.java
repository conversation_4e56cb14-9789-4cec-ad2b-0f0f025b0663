package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.model.instance.InstanceInnodbStatusResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceProcesslistResponse;
import com.baidu.bce.internalsdk.rds.model.performance.ConnectionListResponse;
import com.baidu.bce.internalsdk.rds.model.performance.KillProcessRequest;
import com.baidu.bce.internalsdk.rds.model.performance.TransactionListResponse;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PerformanceService {

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    /**
     * 查询实例 processlist
     *
     * @param instanceId 实例短 ID 或 长 ID
     * @return instanceProcesslistResponse
     */
    public InstanceProcesslistResponse getProcesslist(String instanceId) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClient()
                .getInstanceProcesslist(instanceId);
    }

    /**
     * 查询实例 innodbstatus
     *
     * @param instanceId 实例短 ID 或 长 ID
     * @return instanceInnodbStatusResponse
     */
    public InstanceInnodbStatusResponse getInnodbstatus(String instanceId) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClient()
                .getInstanceInnodbStatus(instanceId);
    }

    /**
     * 杀死指定指定会话session ID，该接口为异步接口，会立即下发到MySQL上返回，需要通过processlist接口check是否执行完成
     *
     * @param instanceId
     * @param request
     */
    public void killProcess(String instanceId, KillProcessRequest request) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        clientFactory.createRdsClient().killProcess(instanceId, request);
    }

    /**
     * 获取指定实例的事务快照列表
     *
     * @param instanceId
     * @return
     */
    public TransactionListResponse getTransactionList(String instanceId) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClient().transactionList(instanceId);
    }

    /**
     * 获取指定实例的connnectlist快照
     *
     * @param instanceId
     * @return
     */
    public ConnectionListResponse getConnectionList(String instanceId) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        return clientFactory.createRdsClient().connectionList(instanceId);
    }
}
