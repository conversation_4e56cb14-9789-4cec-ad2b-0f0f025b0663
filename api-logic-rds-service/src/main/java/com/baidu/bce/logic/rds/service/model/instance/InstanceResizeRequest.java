package com.baidu.bce.logic.rds.service.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.Min;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by luping03 on 17/11/8.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class InstanceResizeRequest {
//    @Min(1)
    private Integer allocatedMemoryInGB;

    // OpenAPI 专用参数
    private Integer memoryCapacity;

    @Min(1)
    private Integer allocatedMemoryInMB;

    @Min(1)
    private Integer allocatedStorageInGB;

    // OpenAPI 专用参数
    private Integer volumeCapacity;

    @Min(1)
    private Integer cpuCount;

    private Integer nodeAmount;

    private Boolean oldFlavor = false; // 是否是老套餐（上一代）

    private Boolean isDirectPay = false; // 是否直接付款（跳过支付环节）

    private String diskIoType;

    private Integer forceHotUpgrade;

    private String masterAzone;

    private String backupAzone;

    private String subnetId = "";

    private String effectiveTime;

    private String diskType;

    private String cdsType;

    private String edgeSubnetId;

    // openAPI专用参数 代金券 ID
    private List<Long> couponId;

    private String resourceType;

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    private List<InstanceCreateModel.SubnetMap> subnets = new ArrayList<>(); // 接收openapi传过来的子网信息

    public Boolean getOldFlavor() {
        return oldFlavor;
    }

    public void setOldFlavor(Boolean oldFlavor) {
        this.oldFlavor = oldFlavor;
    }

    public Integer getAllocatedMemoryInMB() {
        return allocatedMemoryInMB;
    }

    public void setAllocatedMemoryInMB(Integer allocatedMemoryInMB) {
        this.allocatedMemoryInMB = allocatedMemoryInMB;
    }

    public Integer getNodeAmount() {
        return nodeAmount;
    }

    public void setNodeAmount(Integer nodeAmount) {
        this.nodeAmount = nodeAmount;
    }

    public Integer getAllocatedMemoryInGB() {
        return allocatedMemoryInGB;
    }

    public void setAllocatedMemoryInGB(Integer allocatedMemoryInGB) {
        this.allocatedMemoryInGB = allocatedMemoryInGB;
    }

    public Integer getAllocatedStorageInGB() {
        return allocatedStorageInGB;
    }

    public void setAllocatedStorageInGB(Integer allocatedStorageInGB) {
        this.allocatedStorageInGB = allocatedStorageInGB;
    }

    public Integer getCpuCount() {
        return cpuCount;
    }

    public void setCpuCount(Integer cpuCount) {
        this.cpuCount = cpuCount;
    }

    public Boolean getIsDirectPay() {
        return isDirectPay;
    }

    public void setIsDirectPay(Boolean isDirectPay) {
        this.isDirectPay = isDirectPay;
    }

    public String getDiskIoType() {
        return diskIoType;
    }

    public void setDiskIoType(String diskIoType) {
        this.diskIoType = diskIoType;
    }

    public Integer getMemoryCapacity() {
        return memoryCapacity;
    }

    public void setMemoryCapacity(Integer memoryCapacity) {
        this.memoryCapacity = memoryCapacity;
    }

    public Integer getVolumeCapacity() {
        return volumeCapacity;
    }

    public void setVolumeCapacity(Integer volumeCapacity) {
        this.volumeCapacity = volumeCapacity;
    }

    public Integer getForceHotUpgrade() {
        return forceHotUpgrade;
    }

    public void setForceHotUpgrade(Integer forceHotUpgrade) {
        this.forceHotUpgrade = forceHotUpgrade;
    }

    public String getMasterAzone() {
        return masterAzone;
    }

    public void setMasterAzone(String masterAzone) {
        this.masterAzone = masterAzone;
    }

    public String getBackupAzone() {
        return backupAzone;
    }

    public void setBackupAzone(String backupAzone) {
        this.backupAzone = backupAzone;
    }

    public String getSubnetId() {
        return subnetId;
    }

    public void setSubnetId(String subnetId) {
        this.subnetId = subnetId;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public String getDiskType() {
        return diskType;
    }

    public void setDiskType(String diskType) {
        this.diskType = diskType;
    }

    public String getCdsType() {
        return cdsType;
    }

    public void setCdsType(String cdsType) {
        this.cdsType = cdsType;
    }

    public String getEdgeSubnetId() {
        return edgeSubnetId;
    }

    public void setEdgeSubnetId(String edgeSubnetId) {
        this.edgeSubnetId = edgeSubnetId;
    }

    public List<InstanceCreateModel.SubnetMap> getSubnets() {
        return subnets;
    }

    public void setSubnets(List<InstanceCreateModel.SubnetMap> subnets) {
        this.subnets = subnets;
    }

    public List<Long> getCouponId() {
        return couponId;
    }

    public void setCouponId(List<Long> couponId) {
        this.couponId = couponId;
    }
}
