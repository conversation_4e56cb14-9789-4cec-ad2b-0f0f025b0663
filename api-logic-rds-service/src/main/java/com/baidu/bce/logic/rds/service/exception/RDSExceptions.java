package com.baidu.bce.logic.rds.service.exception;

import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.plat.webframework.exception.BceException;
import org.apache.commons.lang.StringUtils;

import java.util.List;

public class RDSExceptions {
    public static class ResourceInTaskException extends BceException {
        public ResourceInTaskException() {
            super("You have other order(s) need to payed first.",
                    HttpStatus.ERROR_OPERATION_NOT_AVAILABLE, "ResourceInTask");
        }
    }

    public static class ClusterNotFoundWithCurrentRegion extends BceException {
        public ClusterNotFoundWithCurrentRegion() {
            super("The instance does not exist in the current region.",
                    HttpStatus.ERROR_INPUT_INVALID, "CurrentRegionNotFound");
        }
    }

    public static class BadRequestException extends BceException {
        public BadRequestException() {
            super("Bad request parameters or illegal request.", 400, "BadRequest");
        }
    }

    public static class InstanceNotExistException extends BceException {
        public InstanceNotExistException() {
            super("Instance not found.",
                    com.baidu.bce.logic.core.constants.HttpStatus.ERROR_RESOURCE_NOT_EXIST, "InstanceNotExist");
        }

        public InstanceNotExistException(String id) {
            super("Instance not found:" + id,
                    com.baidu.bce.logic.core.constants.HttpStatus.ERROR_RESOURCE_NOT_EXIST, "InstanceNotExist");
        }
    }

    public static class NotSupportOperation extends BceException {
        public NotSupportOperation(String desc) {
            super(String.format("You are not allowed to execute %s operation", desc),
                    HttpStatus.ERROR_OPERATION_NOT_AVAILABLE, "NotSupportOperation");
        }
        public NotSupportOperation() {
            super(String.format("You are not allowed to execute this operation"),
                    HttpStatus.ERROR_OPERATION_NOT_AVAILABLE, "NotSupportOperation");
        }
    }

    public static class NotSupportLocalDiskConvertDisk extends BceException {
        public NotSupportLocalDiskConvertDisk() {
            super(String.format("The engine version does not support local disk to cloud disk"),
                    HttpStatus.ERROR_OPERATION_NOT_AVAILABLE, "NotSupportOperation");
        }
    }
    public static class TopoCheckException extends BceException {
        public TopoCheckException(String desc) {
            super(("Precheck failed: " + desc),
                    HttpStatus.ERROR_COMPUTER, "TopoCheckFailed");

        }
    }

    public static class ResourceServerException extends BceException {
        public ResourceServerException() {
            super("Internal Server Error.", HttpStatus.ERROR_SERVICE, "InternalServerError");
        }
    }

    public static class ResourceNotExistException extends BceException {
        public ResourceNotExistException() {
            super("实例不存在.",
                    HttpStatus.ERROR_RESOURCE_NOT_EXIST, "InstanceNotExist");
        }
    }

    public static class UnauthorizedException extends BceException {
        public UnauthorizedException() {
            super("您没有当前操作的访问权限", HttpStatus.ERROR_OPERATION_DENY, "UnauthorizedException");
        }
    }

    public static class AutoRenewParamException extends BceException {
        public AutoRenewParamException() {
            super("Existing autoRenewRule instance",
                    HttpStatus.ERROR_RESOURCE_NOT_EXIST, "AutoRenewParamException");
        }
    }

    public static class ResourceNotCreatedException extends BceException {
        public ResourceNotCreatedException() {
            super("Instance not created.",
                    HttpStatus.ERROR_RESOURCE_NOT_EXIST, "InstanceNotCreated");
        }
    }

    public static class OrderPreparedException extends BceException {
        public OrderPreparedException() {
            super("Internal server error.",
                    HttpStatus.ERROR_SERVICE, "OrderPreparedException");
        }
    }

    public static class ParamValidationException extends BceException {
        public ParamValidationException(String detail) {
            super("Parameter validate error: " + detail + ".",
                    HttpStatus.ERROR_INPUT_INVALID, "ParamValidationFailed");
        }

        public ParamValidationException() {
            super("Parameter validate error.",
                    HttpStatus.ERROR_INPUT_INVALID, "ParamValidationFailed");
        }
    }

    public static class BatchDeleteParamValidationException extends BceException {
        public BatchDeleteParamValidationException(String detail) {
            super("批量操作实例时，个数限制最多三个！" + detail + ".",
                    HttpStatus.ERROR_INPUT_INVALID, "ParamValidationFailed");
        }

        public BatchDeleteParamValidationException() {
            super("批量操作实例时，个数限制最多三个！",
                    HttpStatus.ERROR_INPUT_INVALID, "ParamValidationFailed");
        }
    }

    public static class RenewNotSupportToPostpayException extends BceException {
        public RenewNotSupportToPostpayException(String id) {
            super("后付费实例不支持续费:" + id, HttpStatus.ERROR_INPUT_INVALID);
        }

        public RenewNotSupportToPostpayException() {
            super("后付费实例不支持续费，请检查操作！", HttpStatus.ERROR_INPUT_INVALID);
        }

    }

    public static class RechargeException extends BceException {
        public RechargeException() {
            super("Recharge failed, please try again after a moment.",
                    HttpStatus.ERROR_OPERATION_DENY, "RechargeException");
        }
    }

    public static class MoreThanOneMigrationTask extends BceException {
        public MoreThanOneMigrationTask() {
            super("More Than One Migration Task is executing.",
                    HttpStatus.ERROR_OPERATION_NOT_AVAILABLE, "MoreThanOneMigrationTask");
        }
    }

    public static class ReplicaExpirationTimeAfterMaster extends BceException {
        public ReplicaExpirationTimeAfterMaster(String date) {
            super("Replica instance's Expiration time can not after Master instance:" + date,
                    HttpStatus.ERROR_OPERATION_DENY, "ReplicaExpirationTimeAfterMaster");
        }
    }

    public static class DurationValidationException extends BceException {
        public DurationValidationException() {
            super("Instance Duration Validate Error.",
                    HttpStatus.ERROR_INPUT_INVALID, "DurationValidationError");
        }
    }

    public static class MemoryValidationException extends BceException {
        public MemoryValidationException() {
            super("Instance Memory Size Validate Error.",
                    HttpStatus.ERROR_INPUT_INVALID, "MemoryValidationException");
        }
    }

    public static class StorageValidationException extends BceException {
        public StorageValidationException() {
            super("Instance Disk Size Validate Error.",
                    HttpStatus.ERROR_INPUT_INVALID, "StorageValidationException");
        }
    }

    public static class CpuCountValidationException extends BceException {
        public CpuCountValidationException() {
            super("Instance Cpu Size Validate Error.",
                    HttpStatus.ERROR_INPUT_INVALID, "CpuCountValidationException");
        }
    }

    public static class EngineValidationException extends BceException {
        public EngineValidationException() {
            super("Instance Engine Validate Error.",
                    HttpStatus.ERROR_INPUT_INVALID, "EngineValidationException");
        }
    }

    public static class EngineVersionValidationException extends BceException {
        public EngineVersionValidationException() {
            super("Instance Engine Version Validate Error.",
                    HttpStatus.ERROR_INPUT_INVALID, "EngineVersionValidationException");
        }
    }

    public static class MasterInstanceValidationError extends BceException {
        public MasterInstanceValidationError() {
            super("Master instance engine version validate error.",
                    HttpStatus.ERROR_INPUT_INVALID, "MasterInstanceValidationError");
        }
    }

    public static class VpcIdValidationException extends BceException {
        public VpcIdValidationException() {
            super("VpcId should be the same as its master instance.",
                    HttpStatus.ERROR_INPUT_INVALID, "VpcIdValidationException");
        }
    }

    public static class StorageLimitByAzException extends BceException {
        public StorageLimitByAzException(String engine, String storage) {
            super("尊敬的客户，非常抱歉！当前该可用区可创建 RDS for " +
                    engine + " 最大磁盘空间为" + storage + "G，请缩减磁盘大小或尝试其它可用区！",
                    HttpStatus.ERROR_OPERATION_DENY, "StorageLimitByAzException");
        }
    }

    public static class ExceedQuotaException extends BceException {
        public ExceedQuotaException(String message) {
            super(String.format("The %s instances you want to create exceeded the system limit.", message),
                    HttpStatus.ERROR_OPERATION_DENY, "ExceedQuotaException");
        }
    }

    public static class ExceedProxyQuotaException extends BceException {
        public ExceedProxyQuotaException() {
            super("The rdsProxy instances you want to create exceeded the system limit.",
                    HttpStatus.ERROR_OPERATION_DENY, "ExceedProxyQuotaException");
        }
    }

    public static class CreateReplicaInstanceException extends BceException {
        public CreateReplicaInstanceException() {
            super("You can not create more than one replica instance at the same time. ",
                    HttpStatus.ERROR_OPERATION_DENY, "CreateReplicaInstanceException");
        }
    }

    public static class ProductTypeValidationException extends BceException {
        public ProductTypeValidationException() {
            super("Product type validate error. ",
                    HttpStatus.ERROR_OPERATION_DENY, "ProductTypeValidationException");
        }
    }

    public static class AvailableInstanceException extends BceException {
        public AvailableInstanceException() {
            super("Instance status is invalid for this operation. ",
                    HttpStatus.ERROR_OPERATION_DENY, "InvalidInstanceStatus");
        }
    }

    public static class GiveMeMoreMoneyException extends BceException {
        public GiveMeMoreMoneyException() {
            super("Balance less than 100, please recharge！",
                    HttpStatus.ERROR_OPERATION_DENY, "NotEnoughBalanceForPayOrder");
        }
    }

    public static class InvalidOrderException extends BceException {
        public InvalidOrderException() {
            super("Invalid order please check after retry. ",
                    HttpStatus.ERROR_OPERATION_DENY, "InvalidOrderException");
        }
    }

    public static class InvalidReadReplicaOrderException extends BceException {
        public InvalidReadReplicaOrderException() {
            super("The readReplica instance configuration can not be smaller than the Master instance's configuration！",
                    HttpStatus.ERROR_OPERATION_DENY, "InvalidReadReplicaOrderFailed");
        }
    }

    public static class InvalidCsrfToken extends BceException {
        public InvalidCsrfToken() {
            super("Csrf token validation error may be cross-site attack!",
                    HttpStatus.ERROR_OPERATION_DENY, "InvalidCsrfToken");
        }
    }

    public static class RegionNotExistException extends BceException {
        public RegionNotExistException() {
            super("This area does not exist, please check and try again. ",
                    HttpStatus.ERROR_OPERATION_DENY, "RegionNotExistException");
        }
    }

    public static class MemoryStorageRatioException extends BceException {
        public MemoryStorageRatioException() {
            super("由于磁盘容量过大，与当前内存值不匹配，可缩小磁盘或扩大内存，请检查后重试！",
                    HttpStatus.ERROR_OPERATION_DENY, "MemoryStorageRatioException");
        }
    }

    public static class UnchangedException extends BceException {
        public UnchangedException() {
            super("Unreasonable resize parameter.", HttpStatus.ERROR_OPERATION_DENY, "UnreasonableParameter");
        }
    }

    public static class SuperUserExistException extends BceException {
        public SuperUserExistException() {
            super("Super privilege account allowed to create only one.",
                    HttpStatus.ERROR_OPERATION_DENY, "SuperAccountExist");
        }
    }

    public static class PGAccountExistException extends BceException {
        public PGAccountExistException() {
            super("PostgreSQL can only create one account. ",
                    HttpStatus.ERROR_OPERATION_DENY, "PGAccountExist");
        }
    }

    public static class BinlogTimeInvalidException extends BceException {
        public BinlogTimeInvalidException() {
            super("The time point outside the valid time range, please re-select. ",
                    HttpStatus.ERROR_OPERATION_DENY, "BinlogTimeInvalidException");
        }
    }

    public static class BinlogInvalidException extends BceException {
        public BinlogInvalidException() {
            super("非常抱歉！由于原实例在备份时间点到目标时间点之间可能进行了以下操作：" +
                    "升级/数据恢复/实例自修复，克隆实例无法恢复数据到该时间点。" +
                    "请您选择其它数据还原方式，或者直接发工单联系op支持",
                    HttpStatus.ERROR_OPERATION_DENY, "BinlogTimeInvalidException");
        }
    }

    public static class DownloadException extends BceException {
        public DownloadException() {
            super("下载出现错误，请稍候重试，或者直接发工单联系人工支持",
                    HttpStatus.ERROR_OPERATION_DENY, "DownloadException");
        }
    }

    public static class InstanceExpiredException extends BceException {
        public InstanceExpiredException() {
            super("This instance is already expired！",
                    HttpStatus.ERROR_OPERATION_DENY, "InstanceExpiredException");
        }
    }

    public static class HasTaskException extends BceException {
        public HasTaskException(String instanceId, String url) {
            super(String.format("The current instance %s has been enabled for automatic renew, please cancel.", instanceId),
                    HttpStatus.ERROR_OPERATION_DENY, "HasTaskException");
        }
    }

    public static class HasChangeBillingException extends BceException {
        public HasChangeBillingException() {
            super("This instance is undergoing billing changes. ",
                    HttpStatus.ERROR_OPERATION_DENY, "HasChangeBillingException");
        }
    }

    public static class NoChangeBillingException extends BceException {
        public NoChangeBillingException() {
            super("This instance has no ongoing billing changes.",
                    HttpStatus.ERROR_OPERATION_DENY, "NoChangeBillingException");
        }
    }

    public static class CreateToPrepostOrderExcption extends BceException {
        public CreateToPrepostOrderExcption() {
            super("Failed to create billing change order.",
                    HttpStatus.ERROR_OPERATION_DENY, "CreateToPrepostOrderExcption");
        }
    }

    public static class WrongFormatUrlExcption extends BceException {
        public WrongFormatUrlExcption() {
            super("url格式不对",
                    HttpStatus.ERROR_OPERATION_DENY, "WrongFormatUrlExcption");
        }
    }

    public static class RdsProxyValidationException extends BceException {
        public RdsProxyValidationException() {
            super("Instance type error: Non proxy instance",
                HttpStatus.ERROR_OPERATION_DENY, "RdsProxyValidationException");
        }
    }

    public static class DbfwStateValidationException extends BceException {
        public DbfwStateValidationException() {
            super("DbfwState parameter error！",
                HttpStatus.ERROR_OPERATION_DENY, "DbfwStateValidationException");
        }
    }

    public static class DateFormatException extends BceException {
        public DateFormatException() {
            super("Json format error. Please use the time in the UTC format",
                HttpStatus.ERROR_OPERATION_DENY, "DateFormatException");
        }
    }

    public static class FutuerDateException extends BceException {
        public FutuerDateException() {
            super("Unreasonable date",
                HttpStatus.ERROR_OPERATION_DENY, "FutuerDateException");
        }
    }

    public static class IllegalDateRangeException extends BceException {
        public IllegalDateRangeException() {
            super("Unreasonable time range",
                HttpStatus.ERROR_OPERATION_DENY, "IllegalDateRangeException");
        }
    }

    public static class JsonFormatException extends BceException {
        public JsonFormatException() {
            super("Json format error",
                HttpStatus.ERROR_OPERATION_DENY, "JsonFormatException");
        }
    }

    public static class RegionNotMatchException extends BceException {
        public RegionNotMatchException() {
            super("Wrong region to create instance",
                    HttpStatus.ERROR_OPERATION_DENY, "RegionNotMatchException");
        }
    }

    public static class NoDefaultZoneException extends BceException {
        public NoDefaultZoneException() {
            super("Have no available zone for your operation.",
                    HttpStatus.ERROR_OPERATION_DENY, "NoDefaultZoneException");
        }
    }

    public static class MissingSubnetException extends BceException {
        public MissingSubnetException() {
            super("Missing subnet information.",
                    HttpStatus.ERROR_OPERATION_DENY, "MissingSubnetException");
        }
    }

    public static class InstanceDeleteFailedException extends BceException {
        public InstanceDeleteFailedException(String s) {
            super("Instance(ID:" + s + ") delete failed.",
                    HttpStatus.ERROR_OPERATION_DENY, "InstanceDeleteFailedException");
        }
    }

    public static class InvalidAction extends BceException {
        public InvalidAction() {
            super("Invalid Action.",
                    HttpStatus.ERROR_OPERATION_DENY, "InvalidAction");
        }
    }

    public static class AdminPassDecryptionException extends BceException {
        public AdminPassDecryptionException() {
            super("Invalid encryption code found for the admin password.",
                    HttpStatus.ERROR_INPUT_INVALID, "AdminPassDecryptionException");
        }
    }

    public static class InvalidWeightException extends BceException {

        public InvalidWeightException(String message) {
            super(message);
        }
    }

    public static class NotSupportSubUserOperation extends BceException {

        public NotSupportSubUserOperation(String message) {
            super(message);
        }
    }

    public static class AddOrUpdateRoleException extends BceException {
        public AddOrUpdateRoleException() {
            super("Add or update role exception.",
                    HttpStatus.ERROR_SERVICE, "AddOrUpdateRoleException");
        }
    }
    public static class TagFormatException extends BceException {
        public TagFormatException() {
            super("Internal 'Tag' format error.",
                    HttpStatus.ERROR_SERVICE, "TagFormatException");
        }
    }

    public static class AutoRenewParamsValidationException extends BceException {
        public AutoRenewParamsValidationException() {
            super("Auto renew params error.",
                    HttpStatus.ERROR_INPUT_INVALID, "AutoRenewParamsValidationException");
        }
    }

    public static class RegionOrCreaterException extends BceException {
        public RegionOrCreaterException() {
            super("Region or creater error.",
                    HttpStatus.ERROR_INPUT_INVALID, "RegionOrCreaterException");
        }
    }

    public static class AutoRenewStatusValidationException extends BceException {
        public AutoRenewStatusValidationException() {
            super("Creating Instance not allow.",
                    HttpStatus.ERROR_INPUT_INVALID, "AutoRenewStatusValidationException");
        }
    }

    public static class PrepayInstanceDeleteException extends BceException {
        public PrepayInstanceDeleteException() {
            super("Prepay instance can't be deleted.",
                    HttpStatus.ERROR_SERVICE, "PrepayInstanceDeleteException");
        }
    }

    public static class CoverIdException extends BceException {
        public CoverIdException() {
            super("CoverId error.",
                    HttpStatus.ERROR_SERVICE, "CoverIdException");
        }
    }

    public static class CheckPermissionException extends BceException {
        public CheckPermissionException() {
            super("CheckPermissionException error.",
                    HttpStatus.ERROR_SERVICE, "CheckPermissionException");
        }
    }

    public static class CheckInstanceStatusAndOrderStatus extends BackendExceptions {
        public CheckInstanceStatusAndOrderStatus() {
            super("please check InstanceStatus And orderStatus",
                    HttpStatus.ERROR_COMPUTER, "CheckInstanceStatusAndOrderStatus");
        }
    }

    public static class DeleteFailInstanceException extends BceException {
        public DeleteFailInstanceException() {
            super("DeleteInstance error.",
                    HttpStatus.ERROR_COMPUTER, "DeleteFailInstanceException");
        }
    }

    public static class UnDeleteInstanceCountLimit extends BceException {
        public UnDeleteInstanceCountLimit() {
            super("DeleteInstanceException error.",
                    HttpStatus.ERROR_SERVICE, "UnDeleteInstanceCountLimit");
        }
    }

    public static class RDSGroupForceChangeException extends BceException {
        public RDSGroupForceChangeException(Integer behindMaster) {
            super("check Behind_Master failed, behindMaster:" + behindMaster,
                    HttpStatus.ERROR_SERVICE, "RDSGroupForceChangeException");
        }
        public RDSGroupForceChangeException() {
            super("check Behind_Master failed.",
                    HttpStatus.ERROR_SERVICE, "RDSGroupForceChangeException");
        }
    }
    public static class IpCheckException extends BceException {
        public IpCheckException(String detail) {
            super("Parameter validate error: " + detail + ".",
                    HttpStatus.ERROR_INPUT_INVALID, "ParamValidationException");
        }

        public IpCheckException() {
            super("Parameter validate error.",
                    HttpStatus.ERROR_INPUT_INVALID, "ParamValidationException");
        }
    }

    public static class RoInstancesMAXException extends BceException {
        public RoInstancesMAXException() {
            super("每个只读组里最多加入5个只读实例.",
                    HttpStatus.ERROR_RESOURCE_NOT_EXIST, "RoInstancesMAXException");
        }
    }

    public static class InstanceTypeError extends BceException {
        public InstanceTypeError() {
            super("The instance type does not support this operation!",
                    HttpStatus.ERROR_INPUT_INVALID, "InstanceTypeError");
        }
    }

    public static class InitialDataReferenceEngineVersion extends BceException {
        public InitialDataReferenceEngineVersion() {
            super("The clone instance engionVersion should be the same as the original instance engionVersion!",
                    HttpStatus.ERROR_INPUT_INVALID, "InitialDataReferenceEngineVersion");
        }
    }

    public static class RealNameNotPassException extends BceException {
        public RealNameNotPassException(String detail) {
            super("RealNameNotPassFailed: " + detail + ".",
                    HttpStatus.ERROR_INPUT_INVALID, "RealNameNotPassFailed");
        }
        public RealNameNotPassException() {
            super("未通过实名认证，请先进行认证",
                    HttpStatus.ERROR_INPUT_INVALID, "RealNameNotPassFailed");
        }
    }

    public static class InvalidInstanceStatus extends BceException {
        public InvalidInstanceStatus() {
            super("instance status is invalid", HttpStatus.ERROR_OPERATION_DENY, "InvalidInstanceStatus");
        }
    }

    public static class InstanceStatusErrorException extends BceException {
        public InstanceStatusErrorException() {
            super("实例状态不允许发起当前操作", HttpStatus.ERROR_OPERATION_DENY, "InstanceStatusError");
        }
    }

    public static class ReleaseInstanceException extends BceException {
        public ReleaseInstanceException(List<String> failedIds, String msg) {
            super("release instance failed: " + StringUtils.join(failedIds, ","),
                    org.apache.http.HttpStatus.SC_INTERNAL_SERVER_ERROR, "ReleaseInstanceFailed");
        }

        public ReleaseInstanceException(String message) {
            super("release instances failed: " + message,
                    org.apache.http.HttpStatus.SC_INTERNAL_SERVER_ERROR, "ReleaseInstanceFailed");
        }
    }

    public static class PrepayNeedRenewException extends BceException {
        public PrepayNeedRenewException(List<String> failedIds) {
            super("prepay instances cannot recover directly: " + StringUtils.join(failedIds, ","),
                    org.apache.http.HttpStatus.SC_INTERNAL_SERVER_ERROR, "PrepayNeedRenew");
        }
    }

    public static class PrecheckResourceException extends BceException {
        public PrecheckResourceException(String detail) {
            super("subnet ip is not enough！" + detail + ".",
                    HttpStatus.ERROR_INPUT_INVALID, "PrecheckResourceFailed");
        }

        public PrecheckResourceException() {
            super("subnet ip is not enough！",
                    HttpStatus.ERROR_INPUT_INVALID, "PrecheckResourceFailed");
        }
    }

    public static class BackupException extends BceException {
        public BackupException() {
            super("The local disk instance does not allow for snapshot backup initiation.");
        }
    }
}
