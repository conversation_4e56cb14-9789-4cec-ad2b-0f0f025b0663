package com.baidu.bce.logic.rds.service.exception;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.plat.webframework.exception.BceException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 参考文档 <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/PA2gxLX6I5/pklgqwcAHE/dEER1uR0Z37RhB">...</a>，
 * 本类主要作用是处理异常信息国际化问题。
 *
 * 本类的子类主要是 api-logic-rds 模块自身的业务逻辑异常，
 *  在 {@link com.baidu.bce.logic.rds.controller.exception.handling.service.ServiceExceptionHandler#handle(
 * HttpServletRequest, HttpServletResponse, RDSBusinessException)} 中进行拦截处理。
 * api-logic-rds 模块调用 RDS 后端服务以及其他服务（比如 Billing 服务）API 收到的错误响应异常 BceInternalResponseException，
 *  在 {@link com.baidu.bce.logic.rds.controller.exception.handling.service.ServiceExceptionHandler#handle(
 * HttpServletRequest, HttpServletResponse, BceInternalResponseException)} 中进行拦截处理。
 * 若 RDS 后端服务以及其他服务（比如 Billing 服务）API 收到的错误响应错误码重复冲突了，不方便进行国际化处理时，可新建一个该类的子类对其
 *  进行包装处理。
 */
public class RDSBusinessException extends BceException {

    /**
     * 该前缀用于 api-logic-rds 模块自身业务逻辑抛出的异常，使用场景有：
     *  1. 用户输入参数非法；
     */
    private static final String EXCEPTION_RDS_I18N_KEY_PREFIX = "exception.rds.";

    /**
     * OtherService 表示 api-logic-rds 模块依赖的所有其他服务（包括：RDS 后端服务、Billing服务等）。
     *
     * 该前缀用于 api-logic-rds 模块请求其他服务（包括：RDS 后端服务、IAM、Billing 等服务）时，其他服务返回错误的 HTTP 响应，会被
     *  HTTP Client（BceClient）封装为 Java 异常（BceInternalResponseException）抛出，没有特殊请求，Controller 和 Service 中均
     *  无需捕获这种异常，这种异常会在 在
     *  {@link com.baidu.bce.logic.rds.controller.exception.handling.service.ServiceExceptionHandler#handle(
     *  HttpServletRequest, HttpServletResponse, BceInternalResponseException)} 中进行拦截处理，只需要在
     *  配置文件 messages_zh_CN.properties 定义合适的中文文案。
     */
    public static final String EXCEPTION_OTHER_SERVICE_I18N_KEY_PREFIX = "exception.other.service.";

    /**
     * 其他服务（包括：RDS 后端服务、Billing服务等）的错误码有重复冲突时，才考虑使用下面的细分服务前缀变量。
     */
    private static final String EXCEPTION_RDS_BACKEND_I18N_KEY_PREFIX = "exception.rds.backend.";
    private static final String EXCEPTION_ORDER_I18N_KEY_PREFIX = "exception.order.";
    private static final String EXCEPTION_PRICE_I18N_KEY_PREFIX = "exception.price.";
    public static final String EXCEPTION_I18N_KEY_PREFIX = "exception.";

    private String i18nKey;

    private Object[] i18nArgs;

    public RDSBusinessException(String message, int httpStatus, String code) {
        super(message, httpStatus, code);
        this.i18nKey = EXCEPTION_RDS_I18N_KEY_PREFIX + this.getClass().getSimpleName();
    }

    public RDSBusinessException(String message, int httpStatus, String code, Object[] i18nArgs) {
        super(message, httpStatus, code);
        this.i18nKey = EXCEPTION_RDS_I18N_KEY_PREFIX + this.getClass().getSimpleName();
        this.i18nArgs = i18nArgs;
    }

    public String getI18nKey() {
        return i18nKey;
    }

    public void setI18nKey(String i18nKey) {
        this.i18nKey = i18nKey;
    }

    public Object[] getI18nArgs() {
        return i18nArgs;
    }

    public void setI18nArgs(Object[] i18nArgs) {
        this.i18nArgs = i18nArgs;
    }
}
