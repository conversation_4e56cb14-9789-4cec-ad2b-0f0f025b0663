package com.baidu.bce.logic.rds.service.datasync.service;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.GetResourcesRequest;
import com.baidu.bce.internalsdk.order.model.Resource;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.NeedToSyncPO;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.constant.InstanceType;
import com.baidu.bce.logic.rds.service.constant.RdsInstanceStatus;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.IDGenerator;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import endpoint.EndpointManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Collections;

/**
 * Created by luping03 on 18/1/1.
 */
@Service
public class NonOrderDataSyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(NonOrderDataSyncService.class);
    private static final String LOG_PREFIX = "[NonOrder Rds DataSync]";
    private static final String LOG_PREFIX_ERROR = "[NonOrder Rds DataSync]ERROR:";

    @Autowired
    InstanceDao instanceDao;

    @Autowired
    InstanceService instanceService;

    @Autowired
    IDGenerator idGenerator;

    @Autowired
    LogicRdsClientFactory clientFactory;

    @Autowired
    NewOrderCreatingService orderSyncService;

    /**
     * 数据同步
     *
     * @param needToSync
     * @return
     */
    public int syncData(NeedToSyncPO needToSync) {
        InstancePO instancePO = instanceDao
                .getAllStatusInstanceByUuid(needToSync.getInstanceUuid(), needToSync.getUserId());

        if (instancePO == null) {
            if (RdsInstanceStatus.CREATING.getValue().equalsIgnoreCase(needToSync.getInstanceStatus())) {
                return 1;
            } else {
                if (RdsInstanceStatus.FAILED.getValue().equalsIgnoreCase(needToSync.getInstanceStatus())) {
                    Instance instance = null;
                    try {
                        instance = clientFactory.createRdsClient2ByUserId(needToSync.getUserId())
                                .instanceDescribe(needToSync.getInstanceUuid()).getInstance();
                    } catch (BceInternalResponseException ex) {
                        if ("InstanceAlreadyDeleted".equalsIgnoreCase(ex.getCode())) {
                            instanceDao.deleteInstanceByInstanceUuids(Collections.singletonList(
                                    needToSync.getInstanceUuid()), needToSync.getUserId());
                            LOGGER.info("NoData in rds logic DB, rds backend create failed, user:{}, uuid:{}, status:{}",
                                    needToSync.getUserId(), needToSync.getInstanceUuid(), needToSync.getInstanceStatus());
                            return 1;
                        }
                    }
                }
                LOGGER.warn(LOG_PREFIX_ERROR + "NoData in rds logic DB, WARNING, user:{}, uuid:{}, status:{}",
                        needToSync.getUserId(), needToSync.getInstanceUuid(), needToSync.getInstanceStatus());
                return 0;
            }
        } else if (RdsInstanceStatus.DELETED.getValue().equalsIgnoreCase(instancePO.getInstanceStatus())) {
            LOGGER.warn(LOG_PREFIX_ERROR + "Deleted RDS in rds logic DB, OVER, user:{}, uuid:{}, status:{}",
                    needToSync.getUserId(), needToSync.getInstanceUuid(), needToSync.getInstanceStatus());
            return 1;
        } else {
            Instance instance = null;
            try {
                instance = clientFactory.createRdsClient2ByUserId(needToSync.getUserId())
                        .instanceDescribe(needToSync.getInstanceUuid()).getInstance();
            } catch (BceInternalResponseException ex) {
                if ("InstanceAlreadyDeleted".equalsIgnoreCase(ex.getCode())) {
                    instanceDao.deleteInstanceByInstanceUuids(Collections.singletonList(needToSync.getInstanceUuid()),
                            needToSync.getUserId());
                    LOGGER.warn("rds instanceId: {} not has deleted in backend. ",
                            needToSync.getInstanceUuid());
                    return 1;
                } else {
                    throw ex;
                }
            }
            Resource resource = getResourceByInstanceId(instance.getInstanceId(), needToSync.getUserId(),
                    instance.getInstanceType());
            if (null == resource) {
//                throw new RDSExceptions.ResourceNotExistException();
                instanceDao.deleteInstanceByInstanceUuids(Collections.singletonList(instance.getInstanceId()),
                        needToSync.getUserId());
                LOGGER.warn(LOG_PREFIX_ERROR + "rds instanceId: {} not in resources, deleted this instance.",
                        instance.getInstanceId());
                return 1;
            }
            if (syncDataToLogicDB(instancePO, instance, needToSync.getUserId())) {
                return 1;
            } else {
                return 0;
            }
        }
    }

    /**
     * 数据同步
     *
     * @param needToSync
     * @return
     */
    private int syncData1(NeedToSyncPO needToSync) {


        InstancePO instancePO = instanceDao
                .queryInstanceByInstanceUuid(needToSync.getInstanceUuid(), needToSync.getUserId());
        if (instancePO == null) {
//            if (RDSConstant.INSTANCE_STATUS_OLD_SYNC.contains(needToSync.getInstanceStatus())) {
//                InstancePO oldInstance = new InstancePO();
//                try {
//                    Instance instance = clientFactory.createRdsClient2ByUserId(needToSync.getUserId())
//                            .instanceDescribe(needToSync.getInstanceUuid()).getInstance();
//
//                    oldInstance.setInstanceId(idGenerator.createExternalId(RDSConstant.API_RDS_PREFIX));
//                    oldInstance.setInstanceUuid(instance.getInstanceId());
//                    oldInstance.setSourceInstanceId(instance.getSourceInstanceId());
//                    oldInstance.setInstanceStatus(instance.getInstanceStatus());
//                    if (instance.getInstanceStatus().equalsIgnoreCase("available")) {
//                        if (!instance.getLockMode().equalsIgnoreCase("unlock")) {
//                            oldInstance.setInstanceStatus(instance.getLockMode());
//                        }
//                    }
//                    oldInstance.setUserId(needToSync.getUserId());
//                    instanceDao.insertOldInstance(oldInstance);
//                } catch (Exception ex) {
//                    LOGGER.error(LOG_PREFIX_ERROR + "insertOldInstance failed, user:{}, uuid:{}, status:{}",
//                            needToSync.getUserId(), needToSync.getInstanceUuid(), needToSync.getInstanceStatus());
//                    LOGGER.error(ex.getMessage());
//                    return 0;
//                }
//                try {
//                    List<ShortResourceVo> shortResourceVoList = Lists.newArrayList();
//                    ShortResourceVo shortResourceVo = new ShortResourceVo();
//                    shortResourceVo.setShortId(oldInstance.getInstanceId());
//                    shortResourceVo.setUuid(oldInstance.getInstanceUuid());
//                    shortResourceVo.setRegion(EndpointManager.getThreadRegion());
//                    shortResourceVo.setServiceType(orderSyncService
//                            .getServiceTypeByInstanceType(oldInstance.getInstanceType()));
//                    shortResourceVoList.add(shortResourceVo);
//                    orderSyncService.bindResourceShortIds(needToSync.getUserId(), shortResourceVoList);
//                } catch (Exception ex) {
//                    LOGGER.error(LOG_PREFIX_ERROR + "bind resource shortId failed, user:{}, uuid:{}, shortId:{}",
//                            needToSync.getUserId(), oldInstance.getInstanceUuid(), oldInstance.getInstanceId());
//                    LOGGER.error(ex.getMessage());
//                }
//            }
//            return 1;
            LOGGER.error(LOG_PREFIX_ERROR + "insertOldInstance failed, user:{}, uuid:{}, status:{}",
                    needToSync.getUserId(), needToSync.getInstanceUuid(), needToSync.getInstanceStatus());
            return 0;
        } else {
            Instance instance = clientFactory.createRdsClient2ByUserId(needToSync.getUserId())
                    .instanceDescribe(needToSync.getInstanceUuid()).getInstance();
            if (syncDataToLogicDB(instancePO, instance, needToSync.getUserId())) {
                return 1;
            } else {
                return 0;
            }
        }
    }

    public boolean syncDataToLogicDB(InstancePO logicInstance, Instance backendInstance, String userId) {
        boolean flag = true;
        if (backendInstance == null) {
            return false;
        }

        if (!RDSConstant.INSTANCE_STATUS_SYNC.contains(backendInstance.getInstanceStatus())) {
            return true;
        }
        InstancePO instancePO = new InstancePO();
        instancePO.setUserId(userId);
        instancePO.setInstanceUuid(backendInstance.getInstanceId());
        instancePO.setEngineVersion("");
        if (!InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(backendInstance.getInstanceType())) {
            instancePO.setEngineVersion(backendInstance.getEngineVersion());
        }

        instancePO.setInstanceStatus(backendInstance.getInstanceStatus());
        if (backendInstance.getInstanceStatus().equalsIgnoreCase("available")) {
            if (!backendInstance.getLockMode().equalsIgnoreCase("unlock")) {
                instancePO.setInstanceStatus(backendInstance.getLockMode());
            }
        }
        instancePO.setUsedStorage(backendInstance.getUsedStorageInMB() / 1024.00);
        instancePO.setEipStatus(backendInstance.getEipStatus());
        instancePO.setReplicationType(backendInstance.getReplicationType());
        instancePO.getEndpoint().setVnetIp(backendInstance.getEndpoint()
                .getVnetIp() == null ? "" : backendInstance.getEndpoint().getVnetIp());
        instancePO.getEndpoint().setInetIp(backendInstance.getEndpoint()
                .getInetIp() == null ? "" : backendInstance.getEndpoint().getInetIp());
        instancePO.setInstanceType(backendInstance.getInstanceType());
        instancePO.setVolumeCapacity(backendInstance.getAllocatedStorageInGB());

        try {
            instanceDao.updateSyncData(instancePO);
        } catch (Exception e) {
            flag = false;
            LOGGER.error(LOG_PREFIX + "instanceId: {}, exception is {}", backendInstance.getInstanceId(), e);
        }

        return flag;
    }

    public InstancePO initOldInstancePO(Instance instance, String userId) {
        InstancePO instancePO = new InstancePO();
        Resource resource = getResourceByInstanceId(instance.getInstanceId(), userId, instance.getInstanceType());
        if (null == resource) {
            LOGGER.warn("rds instanceId: {} not in resources", instance.getInstanceId());
            throw new RDSExceptions.ResourceNotExistException();
        }

        instancePO.setInstanceExpireTime(new Timestamp(resource.getExpireTime().getTime()));
        instancePO.setResourceUuid(resource.getUuid());

        if (instance.getInstanceStatus().equals("available")) {
            if (!instance.getLockMode().equals("unlock")) {
                instancePO.setInstanceStatus(instance.getLockMode());
            }
        }
        instancePO.setProductType(resource.getProductType());
        if (instance.getAzone() == null || instance.getAzone().equals("default")) {
            instancePO.setZoneNames("zoneA");
        }
        if (instance.getAzone().contains("+")) {
            instancePO.setZoneNames(instance.getAzone().replaceAll("\\+", ","));
        }

        return instancePO;
    }

    public Resource getResourceByInstanceId(String instanceUuid, String userId, String instanceType) {
        Resources resources;
        try {
            GetResourcesRequest resourcesRequest = new GetResourcesRequest();
            resourcesRequest.setAccountId(userId);
            resourcesRequest.setServiceType(orderSyncService.getServiceTypeByInstanceType(instanceType));
            resourcesRequest.setRegion(EndpointManager.getThreadRegion());
            resourcesRequest.setLimit(Integer.MAX_VALUE);
            ResourceClient resourceClient = clientFactory.getResourceClient();
            resources = resourceClient.list(resourcesRequest);

            for (Resource tmp : resources) {
                if (tmp.getName().equals(instanceUuid)) {
                    return tmp;
                }
            }
            return null;
        } catch (BceInternalResponseException ex) {
            LOGGER.warn("RDS get resource list fail! {}", ex);
            throw new RDSExceptions.ResourceNotExistException();
        } catch (Exception ex) {
            LOGGER.warn("RDS get resource list fail! {}", ex);
            throw new RDSExceptions.ResourceServerException();
        }
    }
}
