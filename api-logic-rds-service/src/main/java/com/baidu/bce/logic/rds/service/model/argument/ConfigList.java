package com.baidu.bce.logic.rds.service.model.argument;

import com.baidu.bce.internalsdk.rds.model.config.ConfigItem;

import java.util.ArrayList;

/**
 * Created by luping03 on 17/10/12.
 */
public class ConfigList {
    private ArrayList<ConfigItem> items = new ArrayList<ConfigItem>();
    private int totalCount = 0;

    private String ETag;

    public ArrayList<ConfigItem> getItems() {
        return items;
    }

    public void setItems(ArrayList<ConfigItem> items) {
        this.items = items;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public String getETag() {
        return ETag;
    }

    public void setETag(String ETag) {
        this.ETag = ETag;
    }
}
