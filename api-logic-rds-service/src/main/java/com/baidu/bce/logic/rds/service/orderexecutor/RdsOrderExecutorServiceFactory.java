package com.baidu.bce.logic.rds.service.orderexecutor;

import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderType;
import com.baidu.bce.order.executor.sdk.service.OrderExecutorService;
import com.baidu.bce.order.executor.sdk.service.OrderExecutorServiceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by luping03 on 2018/03/09.
 */
@Component
public class RdsOrderExecutorServiceFactory implements OrderExecutorServiceFactory {

    private static final Logger LOGGER = LoggerFactory.getLogger(RdsOrderExecutorServiceFactory.class);

    @Autowired
    private OrderExecutorService rdsNewOrderExecuteService;

    @Autowired
    private OrderExecutorService rdsResizeOrderExecuteService;

    @Autowired
    private OrderExecutorService rdsToPostpayOrderExecuteService;

    @Autowired
    private OrderExecutorService rdsRenewOrderExecuteService;

//    @Autowired
//    private UnknownTypeOrderExecutorService unknownOrderExecutorService;

    @Override
    public OrderExecutorService createOrderExecutorService(Order order) throws Exception {
        OrderType type = order.getType();
        LOGGER.info("Order Type : {}.", type);
        switch (type) {
            case NEW:
                return rdsNewOrderExecuteService;
            case DILATATION:
            case SHRINKAGE:
            case RESIZE:
                return rdsResizeOrderExecuteService;
            case TO_POSTPAY:
                return rdsToPostpayOrderExecuteService;
            case RENEW:
                return rdsRenewOrderExecuteService;
            default:
                // 过期未知类型订单置为失败
                return null;
        }
    }
}