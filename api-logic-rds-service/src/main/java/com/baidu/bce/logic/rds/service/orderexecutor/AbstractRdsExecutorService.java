package com.baidu.bce.logic.rds.service.orderexecutor;

import com.baidu.bce.finance.sdk.finance.model.AccountFinanceStatus;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.order.executor.sdk.model.ExecutionResult;
import com.baidu.bce.order.executor.sdk.model.ExecutionStatus;
import com.baidu.bce.order.executor.sdk.model.FaultTrace;
import com.baidu.bce.order.executor.sdk.model.MessageCenterModel;
import com.baidu.bce.order.executor.sdk.model.ReceiverType;
import com.baidu.bce.order.executor.sdk.service.OrderExecutorService;
import com.baidu.bce.service.bus.sdk.util.InetAddressHelper;
import com.baidubce.util.JsonUtils;
import com.google.common.base.Joiner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.net.UnknownHostException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangxinxiang on 2017/7/20.
 */
public abstract class AbstractRdsExecutorService implements OrderExecutorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractRdsExecutorService.class);

    private static final BigDecimal LIMIT_BALANCE_DEFAULT = new BigDecimal(100);

    @Autowired
    private LogicRdsClientFactory clientFactory;

    void setExecutionResult(
            ExecutionResult executionResult, Order order, Instance instance, String summary, Exception e,
            Boolean isSucceed) {
//        LOGGER.error("[Order failed] " + summary + ". OrderId: " + order.getUuid(), e);
        FaultTrace faultTrace = new FaultTrace();
        faultTrace.setSummary(summary);
        if (e != null) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(e).append("\n");  // 错误异常
            stringBuilder.append(e.getMessage()).append("\n");  // 错误异常

            StackTraceElement[] seArr = e.getStackTrace();
            for (StackTraceElement ste : seArr) {
                stringBuilder.append(ste).append("\n");  // 错误代码堆栈
            }

            faultTrace.setDetail(stringBuilder.toString());
        } else {
            faultTrace.setDetail("");
        }
        try {
            faultTrace.setExtra("Ip:" + InetAddressHelper.getHostAddress());
        } catch (UnknownHostException e1) {
            LOGGER.warn("Unknown host", e1);
        }
        executionResult.setFaultTrace(faultTrace);

        if (isSucceed != null) {
            executionResult.setExecutionStatus(isSucceed ? ExecutionStatus.SUCCESS : ExecutionStatus.FAILURE);
            executionResult.setMessageCenterModels(getMessage(order, instance, isSucceed));
        }
    }


    protected List<MessageCenterModel> getMessage(Order order, Instance instance, boolean succeed) {
        // 合并购买失败不发短信，等待人工介入
        if ("EVENTS".equalsIgnoreCase(order.getSource()) && !succeed) {
            return null;
        }
        ServiceType serviceType = ServiceType.valueOf(order.getServiceType());
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("serviceType", serviceType.getFullName());
        contentMap.put("orderId", order.getUuid());
        String[] receivers = {order.getAccountId(), order.getUserId()};
        MessageCenterModel smsMessage = new MessageCenterModel(getMessageTpl(succeed),
                JsonUtils.toJsonString(contentMap), Joiner.on(',').join(receivers), ReceiverType.UserId);

        return Collections.singletonList(smsMessage);
    }

    protected abstract String getMessageTpl(boolean succeed);

    public BigDecimal getPostPayBalance(String accountId, String serviceType, String region) {
        AccountFinanceStatus accountFinanceStatus = clientFactory.createAccountClient(accountId)
                .getAccountFinanceStatus(serviceType, region);
        return accountFinanceStatus.getAvailableBalance();
    }

    public boolean validateBalance(Order order) {
        String region = order.getItems().get(0).getRegion();
        BigDecimal postPayBalance = BigDecimal.valueOf(-1d);
        boolean flag = false;
        try {
            postPayBalance = getPostPayBalance(order.getAccountId(), order.getServiceType(), region);
        } catch (Exception e) {
            flag = true;
            LOGGER.info("getPostPayBalance error with : {}", e);
        }
        LOGGER.info("Post pay balance : {}", postPayBalance);
        // 服务降级，一旦查询余额出错，跳过余额校验
        return flag || postPayBalance.compareTo(LIMIT_BALANCE_DEFAULT) >= 0;
    }
}
