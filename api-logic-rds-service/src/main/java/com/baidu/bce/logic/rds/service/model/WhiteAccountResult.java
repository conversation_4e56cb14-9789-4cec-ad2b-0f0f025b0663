package com.baidu.bce.logic.rds.service.model;

import java.util.Map;

/**
 * Created by gengguangming on 2016/2/23.
 */
public class WhiteAccountResult {

    // pg原地覆盖
    private boolean pgRebase = false;

    // 控制能否创建mysql5.5只读实例，对应白名单中的RdsWhiteList
    private boolean mysqlReplica = false;

    // 控制能否创建sqlserver实例，对应白名单中的RdsSqlserverWhiteList
    private boolean sqlserver = false;

    // 原：控制能否使用较大存储和内存套餐，对应白名单中的RdsStorageAndMemoryWhiteList
    // 18.2.27 控制能否使用较大存储空间，对应白名单中的RdsDiskMaxLimitWhiteList
    // 包含essd支持6t白名单
    private boolean storageAndMemory = false;

    // 控制用户是否能使用慢日志
    private boolean slowquery = false;

    // 实例组
    private boolean instanceGroup = false;

    private boolean dms = false;

    // 备份恢复
    private boolean rebase = false;

    private boolean mysql8 = false;

    // 控制能否创建postgresql 11 12 13实例，对应白名单中的RdsPostgresqlWhiteList
    private boolean postgresql = false;

    private boolean account = false;

    private boolean monitor = false;

    private boolean summary = false;

    private boolean essd = false;

    private boolean sqlflow = false;

    private boolean newDms  = false;

    private boolean maintaintime = false;

    private boolean startStopInstance = false;

    private boolean instancePrepayShrinkage = false;

    private boolean isBaiduIntAccount = false;

    private boolean isLocalDiskAccount = false;

    private boolean isMinorVersion = false;

    private boolean isMasterSlaveSwitch = false;

    private boolean pgMasterSlaveSwitch = false;

    private boolean blbServicePoint = false;

    private boolean mysqlSsdToEssd = false;

    private boolean haReadReplica = false;

    private boolean switchReboot = false;

    private boolean pgSingleton = false;

    private boolean pgResizeDisk = false;

    private boolean pgAutoExpansion = false;

    private boolean supportGeneralInstance = false;

    private boolean tableLevelAccountManage = false;

    private boolean pgLogManagement = false;

    private boolean customizedPackageAndStorage = false;

    private boolean supportEpc = false;

    private boolean supportMultiProxy = false;

    private boolean support128C512G = false;

    private boolean cpuAutoResize = false;

    private boolean honorPoc;


    public boolean getSupport128C512G() {
        return support128C512G;
    }

    public void setSupport128C512G(boolean support128C512G) {
        this.support128C512G = support128C512G;
    }


    public boolean getCpuAutoResize() {
        return cpuAutoResize;
    }

    public void setCpuAutoResize(boolean cpuAutoResize) {
        this.cpuAutoResize = cpuAutoResize;
    }

    public boolean getHonorPoc() {
        return honorPoc;
    }

    public void setHonorPoc(boolean honorPoc) {
        this.honorPoc = honorPoc;
    }

    public boolean getSupportEpc() {
        return supportEpc;
    }

    public void setSupportEpc(boolean supportEpc) {
        this.supportEpc = supportEpc;
    }



    public boolean getSupportMultiProxy() {
        return supportMultiProxy;
    }

    public void setSupportMultiProxy(boolean supportMultiProxy) {
        this.supportMultiProxy = supportMultiProxy;
    }

    public boolean getCustomizedPackageAndStorage() {
        return customizedPackageAndStorage;
    }

    public void setCustomizedPackageAndStorage(boolean customizedPackageAndStorage) {
        this.customizedPackageAndStorage = customizedPackageAndStorage;
    }

    /* 自定义白名单列表,仅用于新增的单个白名单 返回示例: <"RdsExclusiveBLB", true> */
    private Map<String, Boolean> customizedWhiteAccount;

    public boolean getSupportGeneralInstance() {
        return supportGeneralInstance;
    }

    public void setSupportGeneralInstance(boolean supportGeneralInstance) {
        this.supportGeneralInstance = supportGeneralInstance;
    }

    public boolean getTableLevelAccountManage() {
        return tableLevelAccountManage;
    }

    public void setTableLevelAccountManage(boolean tableLevelAccountManage) {
        this.tableLevelAccountManage = tableLevelAccountManage;
    }

    public boolean getPgAutoExpansion() {
        return pgAutoExpansion;
    }

    public void setPgAutoExpansion(boolean pgAutoExpansion) {
        this.pgAutoExpansion = pgAutoExpansion;
    }

    public boolean getPgSingleton() {
        return pgSingleton;
    }

    public void setPgSingleton(boolean pgSingleton) {
        this.pgSingleton = pgSingleton;
    }

    public boolean getPgResizeDisk() {
        return pgResizeDisk;
    }

    public void setPgResizeDisk(boolean pgResizeDisk) {
        this.pgResizeDisk = pgResizeDisk;
    }

    public boolean isHaReadReplica() {
        return haReadReplica;
    }

    public boolean getPgMasterSlaveSwitch() {
        return pgMasterSlaveSwitch;
    }

    public void setPgMasterSlaveSwitch(boolean pgMasterSlaveSwitch) {
        this.pgMasterSlaveSwitch = pgMasterSlaveSwitch;
    }

    public void setHaReadReplica(boolean haReadReplica) {
        this.haReadReplica = haReadReplica;
    }

    public boolean getSwitchReboot() {
        return switchReboot;
    }

    public void setSwitchReboot(boolean switchReboot) {
        this.switchReboot = switchReboot;
    }

    public boolean getIsMinorVersion() {
        return isMinorVersion;
    }

    public void setIsMinorVersion(boolean minorVersion) {
        isMinorVersion = minorVersion;
    }

    public boolean isMysqlReplica() {
        return mysqlReplica;
    }

    public void setMysqlReplica(boolean mysqlReplica) {
        this.mysqlReplica = mysqlReplica;
    }

    public boolean isSqlserver() {
        return sqlserver;
    }

    public void setSqlserver(boolean sqlserver) {
        this.sqlserver = sqlserver;
    }

    public boolean isStorageAndMemory() {
        return storageAndMemory;
    }

    public void setStorageAndMemory(boolean storageAndMemory) {
        this.storageAndMemory = storageAndMemory;
    }

    public boolean getIsMasterSlaveSwitch() {
        return isMasterSlaveSwitch;
    }

    public void setIsMasterSlaveSwitch(boolean masterSlaveSwitch) {
        isMasterSlaveSwitch = masterSlaveSwitch;
    }

    public boolean getMysqlSsdToEssd() {
        return mysqlSsdToEssd;
    }

    public void setMysqlSsdToEssd(boolean mysqlSsdToEssd) {
        this.mysqlSsdToEssd = mysqlSsdToEssd;
    }

    public boolean getBlbServicePoint() {
        return blbServicePoint;
    }

    public void setBlbServicePoint(boolean blbServicePoint) {
        this.blbServicePoint = blbServicePoint;
    }

    public boolean isSlowquery() {
        return slowquery;
    }

    public void setSlowquery(boolean slowquery) {
        this.slowquery = slowquery;
    }

    public boolean isInstanceGroup() {
        return instanceGroup;
    }

    public void setInstanceGroup(boolean instanceGroup) {
        this.instanceGroup = instanceGroup;
    }

    public boolean isDms() {
        return dms;
    }

    public void setDms(boolean dms) {
        this.dms = dms;
    }

    public boolean isRebase() {
        return rebase;
    }

    public void setRebase(boolean rebase) {
        this.rebase = rebase;
    }

    public boolean isMysql8() {
        return mysql8;
    }

    public void setMysql8(boolean mysql8) {
        this.mysql8 = mysql8;
    }

    public boolean isPostgresql() {
        return postgresql;
    }

    public void setPostgresql(boolean postgresql) {
        this.postgresql = postgresql;
    }

    public boolean isAccount() {
        return account;
    }

    public void setAccount(boolean account) {
        this.account = account;
    }

    public Map<String, Boolean> getCustomizedWhiteAccount() {
        return customizedWhiteAccount;
    }

    public void setCustomizedWhiteAccount(Map<String, Boolean> customizedWhiteAccount) {
        this.customizedWhiteAccount = customizedWhiteAccount;
    }

    public boolean isMonitor() {
        return monitor;
    }

    public void setMonitor(boolean monitor) {
        this.monitor = monitor;
    }

    public boolean isSummary() {
        return summary;
    }

    public void setSummary(boolean summary) {
        this.summary = summary;
    }

    public boolean isEssd() {
        return essd;
    }

    public void setEssd(boolean essd) {
        this.essd = essd;
    }

    public boolean isSqlflow() {
        return sqlflow;
    }

    public void setSqlflow(boolean sqlflow) {
        this.sqlflow = sqlflow;
    }

    public boolean isNewDms() {
        return newDms;
    }

    public void setNewDms(boolean newDms) {
        this.newDms = newDms;
    }

    public boolean isMaintaintime() {
        return maintaintime;
    }

    public void setMaintaintime(boolean maintaintime) {
        this.maintaintime = maintaintime;
    }

    public boolean isStartStopInstance() {
        return startStopInstance;
    }

    public void setStartStopInstance(boolean startStopInstance) {
        this.startStopInstance = startStopInstance;
    }

    public boolean isInstancePrepayShrinkage() {
        return instancePrepayShrinkage;
    }

    public void setInstancePrepayShrinkage(boolean instancePrepayShrinkage) {
        this.instancePrepayShrinkage = instancePrepayShrinkage;
    }

    public boolean getIsLocalDiskAccount() {
        return isLocalDiskAccount;
    }

    public void setIsLocalDiskAccount(boolean localDiskAccount) {
        isLocalDiskAccount = localDiskAccount;
    }

    public boolean getIsBaiduIntAccount() {
        return isBaiduIntAccount;
    }

    public void setIsBaiduIntAccount(boolean isBaiduIntAccount) {
        this.isBaiduIntAccount = isBaiduIntAccount;
    }

    public boolean getPgLogManagement() {
        return pgLogManagement;
    }

    public void setPgLogManagement(boolean pgLogManagement) {
        this.pgLogManagement = pgLogManagement;
    }

    public boolean getPgRebase() {
        return pgRebase;
    }

    public void setPgRebase(boolean pgRebase) {
        this.pgRebase = pgRebase;
    }
}
