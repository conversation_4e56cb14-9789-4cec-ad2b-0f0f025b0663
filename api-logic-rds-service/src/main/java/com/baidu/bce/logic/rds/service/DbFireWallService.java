package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.core.BceFormat;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwModifyStateRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwState;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwStateGetResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.DbfwStateListResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInject;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectGetResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectListRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlInjectListResponse;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteDetail;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteListCreateRequest;
import com.baidu.bce.internalsdk.rds.model.dbfirewall.SqlWhiteListResponse;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.rds.service.model.ListFilter;
import com.baidu.bce.logic.rds.service.model.LogicCommonListRequest;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.ParseException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * Created by liuruisen on 2017/11/6.
 */
@Service
public class DbFireWallService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DbFireWallService.class);


    @Autowired
    LogicRdsClientFactory logicRdsClientFactory;

    // 获取当前用户的数据库防火墙状态列表
    public LogicPageResultResponse<DbfwState> dbfwList(LogicCommonListRequest request) {
        LogicPageResultResponse<DbfwState> response = new LogicPageResultResponse<>();

        DbfwStateListResponse dbfwStateResponse = logicRdsClientFactory.createRdsClient().dbfwStateList();
        List<DbfwState> dbfwStateList = (List<DbfwState>) dbfwStateResponse.getDbfwStates();
        response.setTotalCount(dbfwStateList.size());

        if (request != null) {
            // sort dbfwStateList
            sortDbfwStateList(dbfwStateList, request.getOrderBy(), request.getOrder());
            // 分页
            dbfwStateList = (List<DbfwState>) listByPage(dbfwStateList, request.getPageNo(), request.getPageSize());
        }
        composeLogicPageResultResponse(response, request);
        response.setResult(dbfwStateList);
        return response;
    }

    // 查看某代理实例或raft版实例防火墙状态
    public DbfwStateGetResponse dbfwState(String instanceId) {
        if (StringUtils.isEmpty(instanceId)) {
            throw new RDSExceptions.ParamValidationException("Proxy instanceId is null");
        }
        // 实例是否可操作数据库防火墙
        isDbfwOperateLegal(instanceId);

        return logicRdsClientFactory.createRdsClientByInstanceId(instanceId).dbfwStateDescribe(instanceId);
    }

    // 修改db防火墙状态 0关闭 1告警 2阻断
    public void updateState(String instanceId, DbfwModifyStateRequest request) {
        if (StringUtils.isEmpty(instanceId)) {
            throw new RDSExceptions.ParamValidationException("Proxy instanceId is null");
        }
        List<Integer> stateRange = Arrays.asList(0, 1, 2);
        if (!stateRange.contains(request.getDbfwState())) {
            throw new RDSExceptions.DbfwStateValidationException();
        }
        // 实例是否可操作数据库防火墙
        isDbfwOperateLegal(instanceId);

        logicRdsClientFactory.createRdsClientByInstanceId(instanceId)
                .dbfwModifyState(instanceId, request);
    }

    // sql注入列表
    public LogicPageResultResponse<SqlInject> sqlInjectList(String instanceId, LogicCommonListRequest request) {
        LogicPageResultResponse<SqlInject> response = new LogicPageResultResponse<>();
        if (StringUtils.isEmpty(instanceId)) {
            throw new RDSExceptions.ParamValidationException("Proxy instanceId is null");
        }
        // 实例是否可操作数据库防火墙
        isDbfwOperateLegal(instanceId);
        // compose SQLInjectListRequest
        SqlInjectListRequest sqlInjectRequest =
                composeSqlInjectListRequest(request == null ? null : request.getFilters());
        SqlInjectListResponse sqlInjectResponse = logicRdsClientFactory.createRdsClientByInstanceId(instanceId)
                .sqlInjectList(instanceId, sqlInjectRequest);
        if (sqlInjectResponse == null || sqlInjectResponse.getSqlInjectList().isEmpty()) {
            response.setTotalCount(0);
            composeLogicPageResultResponse(response, request);
            return response;
        }
        List<SqlInject> sqlInjectList = (List) sqlInjectResponse.getSqlInjectList();
        response.setTotalCount(sqlInjectList.size());
        if (request != null) {
            // 排序
            sortSqlInjectList(sqlInjectList, request.getOrderBy(), request.getOrder());
            // 分页
            sqlInjectList = (List<SqlInject>) listByPage(sqlInjectList, request.getPageNo(), request.getPageSize());
        }
        composeLogicPageResultResponse(response, request);
        response.setResult(sqlInjectList);
        LOGGER.debug("SQL注入列表：" + response);

        return response;
    }

    // 单个SQL注入详情
    public SqlInjectGetResponse sqlInjectDetail(String instanceId, String sqlId) {
        if (StringUtils.isEmpty(instanceId) || StringUtils.isEmpty(sqlId)) {
            throw new RDSExceptions.ParamValidationException("Proxy instanceId or SqlId is null");
        }
        // 实例是否可操作数据库防火墙
        isDbfwOperateLegal(instanceId);

        return logicRdsClientFactory.createRdsClientByInstanceId(instanceId).sqlInjectDescribe(instanceId, sqlId);
    }

    // SQL白名单列表
    public LogicPageResultResponse<SqlWhiteDetail> sqlWhiteList(String instanceId, LogicCommonListRequest request) {
        LogicPageResultResponse<SqlWhiteDetail> response = new LogicPageResultResponse<>();
        if (StringUtils.isEmpty(instanceId)) {
            throw new RDSExceptions.ParamValidationException("Proxy instanceId is null");
        }
        // 实例是否可操作数据库防火墙
        isDbfwOperateLegal(instanceId);

        SqlWhiteListResponse sqlWhitResponse = logicRdsClientFactory.createRdsClientByInstanceId(instanceId)
                .sqlWhiteList(instanceId);
        if (sqlWhitResponse == null || sqlWhitResponse.getSqlWhiteList().isEmpty()) {
            response.setTotalCount(0);
            composeLogicPageResultResponse(response, request);
            return response;
        }
        List<SqlWhiteDetail> sqlWhiteList = (List<SqlWhiteDetail>) sqlWhitResponse.getSqlWhiteList();
        response.setTotalCount(sqlWhiteList.size());

        if (request != null) {
            // 筛选符合过滤条件的数据  支持 sqlId, sqlFingerprint
            sqlWhiteListFilter(sqlWhiteList, request.getFilters());
            // sort sqlWhiteList
            sortSqlWhiteList(sqlWhiteList, request.getOrderBy(), request.getOrder());
            // 分页
            sqlWhiteList = (List<SqlWhiteDetail>) listByPage(sqlWhiteList, request.getPageNo(), request.getPageSize());
        }

        composeLogicPageResultResponse(response, request);
        response.setResult(sqlWhiteList);
        return response;
    }

    // 添加sql白名单
    public void sqlWhiteListAdd(String instanceId, SqlWhiteListCreateRequest request) {
        if (StringUtils.isEmpty(instanceId)) {
            throw new RDSExceptions.ParamValidationException("Proxy instanceId is null");
        }
        // 实例是否可操作数据库防火墙
        isDbfwOperateLegal(instanceId);

        logicRdsClientFactory.createRdsClientByInstanceId(instanceId).sqlWhiteListCreate(instanceId, request);
    }

    // 删除sql白名单
    public void sqlWhiteListDelete(String instanceId, String sqlMd5) {
        if (StringUtils.isEmpty(instanceId) || StringUtils.isEmpty(sqlMd5)) {
            throw new RDSExceptions.ParamValidationException();
        }
        // 实例是否可操作数据库防火墙
        isDbfwOperateLegal(instanceId);

        logicRdsClientFactory.createRdsClientByInstanceId(instanceId).sqlWhiteListDelete(instanceId, sqlMd5);
    }


    private SqlInjectListRequest composeSqlInjectListRequest(String sqlInjectListFilters) {
        SqlInjectListRequest request = new SqlInjectListRequest();

        if (StringUtils.isNotEmpty(sqlInjectListFilters)) {

            List<ListFilter> filters = convertToList(sqlInjectListFilters);
            if (filters == null || filters.isEmpty()) {
                return request;
            }

            Date startDate = null;
            Date endDate = null;

            for (ListFilter filter : filters) {
                switch (filter.getKeywordType()) {
                    case "startTime" :
                        try {
                            startDate = BceFormat.getDateTimeFormat().parse(filter.getKeyword());
                            request.setStartTime(filter.getKeyword());
                        } catch (ParseException e) {
                            throw new RDSExceptions.DateFormatException();
                        }
                        break;
                    case "endTime" :
                        try {
                            endDate = BceFormat.getDateTimeFormat().parse(filter.getKeyword());
                            request.setEndTime(filter.getKeyword());
                        } catch (ParseException e) {
                            throw new RDSExceptions.DateFormatException();
                        }
                        break;
                    case "dbName" :
                        request.setDbName(filter.getKeyword());
                        break;
                    case "accountName":
                        request.setAccountName(filter.getKeyword());
                        break;
                    default:
                        break;
                }

            }
            // 时间范围检验
            if (startDate != null && startDate.after(new Date())) {
                throw new RDSExceptions.FutuerDateException();
            }
            if (endDate != null && endDate.after(new Date())) {
                throw new RDSExceptions.FutuerDateException();
            }
            if (startDate != null && endDate != null
                    && startDate.after(endDate)) {
                throw new RDSExceptions.IllegalDateRangeException();
            }

        }
        return request;
    }

    private void sortSqlInjectList(List<SqlInject> sqlInjectList, String orderBy, String order) {
        if (sqlInjectList == null || sqlInjectList.isEmpty()) {
            return ;
        }

        if (StringUtils.isEmpty(orderBy) || StringUtils.isEmpty(order)) {  // 默认使用时间降序排序
            Collections.sort(sqlInjectList, new Comparator<SqlInject>() {
                @Override
                public int compare(SqlInject o1, SqlInject o2) {
                    return o2.getCreateTime().compareTo(o1.getCreateTime());
                }
            });
            return ;
        }

        if (orderBy.equalsIgnoreCase("accountname")) {
            if (order.equalsIgnoreCase("asc")) {
                Collections.sort(sqlInjectList, new Comparator<SqlInject>() {
                    @Override
                    public int compare(SqlInject o1, SqlInject o2) {
                        return o1.getAccountName().compareTo(o2.getAccountName());
                    }
                });
            } else if (order.equalsIgnoreCase("desc")) {
                Collections.sort(sqlInjectList, new Comparator<SqlInject>() {
                    @Override
                    public int compare(SqlInject o1, SqlInject o2) {
                        return o2.getAccountName().compareTo(o1.getAccountName());
                    }
                });
            }
            return ;
        }

        if (orderBy.equalsIgnoreCase("dbname")) {
            if (order.equalsIgnoreCase("asc")) {
                Collections.sort(sqlInjectList, new Comparator<SqlInject>() {
                    @Override
                    public int compare(SqlInject o1, SqlInject o2) {
                        return o1.getDbName().compareTo(o2.getDbName());
                    }
                });
            } else if (order.equalsIgnoreCase("desc")) {
                Collections.sort(sqlInjectList, new Comparator<SqlInject>() {
                    @Override
                    public int compare(SqlInject o1, SqlInject o2) {
                        return o2.getDbName().compareTo(o1.getDbName());
                    }
                });
            }
            return ;
        }

        if (orderBy.equalsIgnoreCase("createtime")) {
            if (order.equalsIgnoreCase("asc")) {
                Collections.sort(sqlInjectList, new Comparator<SqlInject>() {
                    @Override
                    public int compare(SqlInject o1, SqlInject o2) {
                        return o1.getCreateTime().compareTo(o2.getCreateTime());
                    }
                });
            } else if (order.equalsIgnoreCase("desc")) {
                Collections.sort(sqlInjectList, new Comparator<SqlInject>() {
                    @Override
                    public int compare(SqlInject o1, SqlInject o2) {
                        return o2.getCreateTime().compareTo(o1.getCreateTime());
                    }
                });
            }
            return ;
        }
    }

    private List<?> listByPage(List<?> allDataList, int pageNo, int pageSize) {
        if (allDataList == null || allDataList.isEmpty()) {
            return allDataList;
        }
        // 有一个为0，表示不需要分页
        if (pageNo * pageSize == 0) {
            return allDataList;
        }
        int start = (pageNo - 1) * pageSize;
        int end = pageNo * pageSize > allDataList.size() ? allDataList.size() : pageNo * pageSize;

        allDataList = allDataList.subList(start, end);
        return allDataList;
    }

    private void composeLogicPageResultResponse(LogicPageResultResponse<?> response, LogicCommonListRequest request) {
        if (request == null) {
            return ;
        }
        response.setPageSize(request.getPageSize());
        response.setPageNo(request.getPageNo());
        response.setOrder(request.getOrder());
        response.setOrderBy(request.getOrderBy());
    }

    private void sortDbfwStateList(List<DbfwState> dbfwStateList, String orderBy, String order) {
        if (dbfwStateList == null || dbfwStateList.isEmpty()) {
            return ;
        }
        if (StringUtils.isEmpty(orderBy) || StringUtils.isEmpty(order)) {  // 默认按照时间降序排序
            Collections.sort(dbfwStateList, new Comparator<DbfwState>() {
                @Override
                public int compare(DbfwState o1, DbfwState o2) {
                    return o2.getUpdateTime().compareTo(o1.getUpdateTime());
                }
            });
            return ;
        }

        if (orderBy.equalsIgnoreCase("id")) {
            if (order.equalsIgnoreCase("asc")) {
                Collections.sort(dbfwStateList, new Comparator<DbfwState>() {
                    @Override
                    public int compare(DbfwState o1, DbfwState o2) {
                        return ((Integer) o1.getId()).compareTo(o2.getId());
                    }
                });
            } else if (order.equalsIgnoreCase("desc")) {
                Collections.sort(dbfwStateList, new Comparator<DbfwState>() {
                    @Override
                    public int compare(DbfwState o1, DbfwState o2) {
                        return ((Integer) o2.getId()).compareTo(o1.getId());
                    }
                });
            }
            return ;
        }

        if (orderBy.equalsIgnoreCase("appid")) {
            if (order.equalsIgnoreCase("asc")) {
                Collections.sort(dbfwStateList, new Comparator<DbfwState>() {
                    @Override
                    public int compare(DbfwState o1, DbfwState o2) {
                        return o1.getAppId().compareTo(o2.getAppId());
                    }
                });
            } else if (order.equalsIgnoreCase("desc")) {
                Collections.sort(dbfwStateList, new Comparator<DbfwState>() {
                    @Override
                    public int compare(DbfwState o1, DbfwState o2) {
                        return o2.getAppId().compareTo(o1.getAppId());
                    }
                });
            }
            return ;
        }

        if (orderBy.equalsIgnoreCase("updatetime")) {
            if (order.equalsIgnoreCase("asc")) {
                Collections.sort(dbfwStateList, new Comparator<DbfwState>() {
                    @Override
                    public int compare(DbfwState o1, DbfwState o2) {
                        return o1.getUpdateTime().compareTo(o2.getUpdateTime());
                    }
                });
            } else if (order.equalsIgnoreCase("desc")) {
                Collections.sort(dbfwStateList, new Comparator<DbfwState>() {
                    @Override
                    public int compare(DbfwState o1, DbfwState o2) {
                        return o2.getUpdateTime().compareTo(o1.getUpdateTime());
                    }
                });
            }
            return ;
        }
    }

    private void sortSqlWhiteList(List<SqlWhiteDetail> sqlWhiteLists, String orderBy, String order) {
        if (sqlWhiteLists == null || sqlWhiteLists.isEmpty()) {
            return ;
        }

        if (StringUtils.isEmpty(orderBy) || StringUtils.isEmpty(order)) {   // 默认使用sqlId升序排序
            Collections.sort(sqlWhiteLists, new Comparator<SqlWhiteDetail>() {
                @Override
                public int compare(SqlWhiteDetail o1, SqlWhiteDetail o2) {
                    return ((Integer) o1.getSqlId()).compareTo(o2.getSqlId());
                }
            });
            return ;
        }

        if (orderBy.equalsIgnoreCase("sqlid")) {
            if (order.equalsIgnoreCase("asc")) {
                Collections.sort(sqlWhiteLists, new Comparator<SqlWhiteDetail>() {
                    @Override
                    public int compare(SqlWhiteDetail o1, SqlWhiteDetail o2) {
                        return ((Integer) o1.getSqlId()).compareTo(o2.getSqlId());
                    }
                });
            } else if (order.equalsIgnoreCase("desc")) {
                Collections.sort(sqlWhiteLists, new Comparator<SqlWhiteDetail>() {
                    @Override
                    public int compare(SqlWhiteDetail o1, SqlWhiteDetail o2) {
                        return ((Integer) o2.getSqlId()).compareTo(o1.getSqlId());
                    }
                });
            }
            return ;
        }
    }

    private Map<String, Object> convertJsonToMap(String jsonString) {
        Map<String, Object> map = new HashMap();

        if (StringUtils.isNotEmpty(jsonString)) {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                map = objectMapper.readValue(jsonString, Map.class);
            } catch (IOException e) {
                throw new RDSExceptions.JsonFormatException();
            }
        }
        return map;
    }

    private List<ListFilter> convertToList(String jsonString) {
        List<ListFilter> list = new LinkedList<>();
        if (StringUtils.isNotEmpty(jsonString)) {
            ObjectMapper objectMapper = new ObjectMapper();
            JavaType javaType = objectMapper.getTypeFactory()
                    .constructParametricType(LinkedList.class, ListFilter.class);
            try {
                list = objectMapper.readValue(jsonString, javaType);
            } catch (IOException e) {
                throw new RDSExceptions.JsonFormatException();
            }
        }
        return list;
    }


    private void sqlWhiteListFilter(List<SqlWhiteDetail> allDataList, String filtersStr) {
        if (allDataList == null || allDataList.isEmpty() || StringUtils.isEmpty(filtersStr)) {
            return ;
        }
        List<ListFilter> filters = convertToList(filtersStr);
        if (filters == null || filters.isEmpty()) {
            return ;
        }

        for (ListFilter filter : filters) {
            switch (filter.getKeywordType()) {
                case "sqlId" :      // 筛选条件为sqlId
                    Iterator<SqlWhiteDetail> it = allDataList.iterator();
                    while (it.hasNext()) {
                        SqlWhiteDetail sqlWhite = it.next();
                        if (sqlWhite.getSqlId() != Integer.valueOf(filter.getKeyword())) {
                            it.remove();
                        }
                    }
                    break;
                case "sqlFingerprint" :     // 筛选条件为sqlFingerprint
                    Iterator<SqlWhiteDetail> it2 = allDataList.iterator();
                    while (it2.hasNext()) {
                        SqlWhiteDetail sqlWhite = it2.next();
                        if (!sqlWhite.getSqlFingerprint().contains(filter.getKeyword())) {
                            it2.remove();
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }

    // 某实例是否可以操作数据库防火墙（数据库引擎为mysql，普通版三节点版的代理实例可以、raft主实例可以）
    private void isDbfwOperateLegal(String instanceId) {
        RDSClient2 rdsClient2 = logicRdsClientFactory.createRdsClient2ByInstanceId(instanceId);
        InstanceGetResponse instanceResponse = rdsClient2.instanceDescribe(instanceId);
        if (instanceResponse == null || instanceResponse.getInstance() == null) {
            throw new RDSExceptions.ResourceNotExistException();
        }
        String instanceType = instanceResponse.getInstance().getInstanceType();
        // 代理实例才能管理DB防火墙,raft版RDS自带代理实例其主实例可以进行数据库防火墙管理
        if (!instanceType.equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_PROXY)
                && !instanceType.equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            throw new RDSExceptions.RdsProxyValidationException();
        }
        // DB防火墙功能应用于Rds for mysql
        if (!instanceResponse.getInstance().getEngine().equals(RDSConstant.RDS_ENGINE_MYSQL)) {
            throw new RDSExceptions.EngineValidationException();
        }
    }
}
