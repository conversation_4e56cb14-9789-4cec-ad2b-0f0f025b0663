package com.baidu.bce.logic.rds.service.permission;

import com.baidu.bce.common.network.common.config.IConfigUserTokenService;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.iam.model.Token;
import com.baidu.bce.logic.core.authentication.IamLogicService;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.iam.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by luping03 on 18/6/21.
 */
@Component
public class RdsConfigUserTokenService implements IConfigUserTokenService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RdsConfigUserTokenService.class);

    @Autowired
    IamLogicService iamLogicService;

    @Override
    public String getRequestId() {
        return BceInternalRequest.getThreadRequestId();
    }

    @Override
    public Token getToken() {
        LOGGER.debug("get token from LogicUserService");

        Token token = LogicUserService.getSubjectToken();
        UserService.setSubjectToken(token);

        return token;
    }

    @Override
    public String getLocale() {
        // get locale in interceptor in common module
        return null;
    }
}
