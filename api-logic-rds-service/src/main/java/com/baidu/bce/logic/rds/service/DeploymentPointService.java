package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.DeploymentPointRequest;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DeploymentPointService {

    @Autowired
    private LogicRdsClientFactory clientFactory;



    public void createDeploymentPoint(String instanceId, DeploymentPointRequest request, String from) {

        RDSClient rdsClient = clientFactory.createRdsClient();
        rdsClient.operateDeploymentPoint(instanceId, request);
    }

    public void deleteDeploymentPoint(String instanceId, DeploymentPointRequest request, String from) {
        RDSClient rdsClient = clientFactory.createRdsClient();
        rdsClient.operateDeploymentPoint(instanceId, request);
    }
}
