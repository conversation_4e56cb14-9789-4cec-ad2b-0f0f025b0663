package com.baidu.bce.logic.rds.service.model.dbfirewall;

import com.baidu.bce.logic.rds.service.model.LogicCommonListRequest;

import javax.validation.constraints.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/8.
 */
public class DbfwSqlInjectListRequest extends LogicCommonListRequest {

    private String startTime;

    private String endTime;

    @Pattern(regexp = "[a-zA-Z]\\w{0,15}")
    private String dbName;

    @Pattern(regexp = "[a-zA-Z]\\w{0,15}")
    private String accountName;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
}
