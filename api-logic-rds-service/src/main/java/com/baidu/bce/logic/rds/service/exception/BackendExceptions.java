package com.baidu.bce.logic.rds.service.exception;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.core.constants.HttpStatus;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.exception.BceException;

/**
 * Created by luping03 on 17/10/10.
 */
/**
 * RDS后端异常的封装
 * <p/>
 * 错误码（Error Code）为该异常的类名，见BceException构造方法
 */
public class BackendExceptions extends BceException {
    public BackendExceptions (String mag, BceInternalResponseException exception) {
        super(mag);
        setCode(exception.getCode());
        setHttpStatus(exception.getHttpStatus());
        setRequestId(LogicUserService.getRequestId());
    }

    public BackendExceptions (String mag, int httpStatus, String code) {
        super(mag);
        setCode(code);
        setHttpStatus(httpStatus);
        setRequestId(LogicUserService.getRequestId());
    }

    public static class MissingParameter extends BackendExceptions {
        public MissingParameter(BceInternalResponseException exception) {
            super("Missing parameters.", exception);
        }
    }

    public static class MalformedJSON extends BackendExceptions {
        public MalformedJSON(BceInternalResponseException exception) {
            super("The JSON you provided was not well-formed.", exception);
        }
    }

    public static class MissingAuthToken extends BackendExceptions {
        public MissingAuthToken(BceInternalResponseException exception) {
            super("Request must have a \"authorization\" header.", exception);
        }
    }

    public static class InvalidHTTPAuthHeader extends BackendExceptions {
        public InvalidHTTPAuthHeader(BceInternalResponseException exception) {
            super("Internal authentication failed. ", exception);
        }
    }

    public static class AccessDenied extends BackendExceptions {
        public AccessDenied(BceInternalResponseException exception) {
            super("Access denied.", exception);
        }
    }

    public static class InternalDBError extends BackendExceptions {
        public InternalDBError(BceInternalResponseException exception) {
            super("Internal DBError", exception);
        }
    }

    public static class DbinstanceNotFound extends BackendExceptions {
        public DbinstanceNotFound(BceInternalResponseException exception) {
            super("RDS Instance not found.", exception);
        }
    }

    public static class InternalFailure extends BackendExceptions {
        public InternalFailure(BceInternalResponseException exception) {
            super("We encountered an internal error. Please try again", exception);
        }
    }

    public static class InvalidParameterValue extends BackendExceptions {
        public InvalidParameterValue(BceInternalResponseException exception) {
            super("Invalid parameter value. ", exception);
        }
    }

    public static class DbinstanceContainersNotFound extends BackendExceptions {
        public DbinstanceContainersNotFound(BceInternalResponseException exception) {
            super("RDS instance is not exist", exception);
        }
    }

    public static class DbinstanceStateChange extends BackendExceptions {
        public DbinstanceStateChange(BceInternalResponseException exception) {
            super("Operation not allowed.", exception);
        }
    }


    public static class ValidationError extends BackendExceptions {
        public ValidationError(BceInternalResponseException exception) {
            super("Validation Error.", exception);
        }
    }

    public static class AlreadyMaxLimit extends BackendExceptions {
        public AlreadyMaxLimit(BceInternalResponseException exception) {
            super("Already Max Limit.", exception);
        }
    }

    public static class PreconditionFailed extends BackendExceptions {
        public PreconditionFailed(BceInternalResponseException exception) {
            super("The specified If-Match header doesn\\'t match the ETag header.", exception);
        }
    }

    public static class DatabaseAlreadyExists extends BackendExceptions {
        public DatabaseAlreadyExists(BceInternalResponseException exception) {
            super("You already has a database with the given name.", exception);
        }
    }

    public static class AccountAlreadyExists extends BackendExceptions {
        public AccountAlreadyExists(BceInternalResponseException exception) {
            super("You already has a user with the given name.", exception);
        }
    }

    public static class DatabaseNameReserved extends BackendExceptions {
        public DatabaseNameReserved(BceInternalResponseException exception) {
            super("The database name can not be reserved keywords.", exception);
        }
    }

    public static class AccountNameReserved extends BackendExceptions {
        public AccountNameReserved(BceInternalResponseException exception) {
            super("The account name can not be reserved keywords.", exception);
        }
    }

    public static class SnapshotNotFound extends BackendExceptions {
        public SnapshotNotFound(BceInternalResponseException exception) {
//            super("The specified backup is not exist.", exception);
            super("The specified backup is not exist.", HttpStatus.ERROR_INPUT_INVALID, "BackupNotFound");
        }
    }

    public static class BinlogNotFound extends BackendExceptions {
        public BinlogNotFound(BceInternalResponseException exception) {
            super("The specified binlog is not exist.", exception);
        }
    }

    public static class DatabaseNotFound extends BackendExceptions {
        public DatabaseNotFound(BceInternalResponseException exception) {
            super("The specified database is not exist.", exception);
        }
    }

    public static class AccountNotFound extends BackendExceptions {
        public AccountNotFound(BceInternalResponseException exception) {
            super("The specified account is not exist.", exception);
        }
    }

    public static class CreateExceedLimit extends BackendExceptions {
        public CreateExceedLimit(BceInternalResponseException exception) {
            super("Create Instances number larger than 10.", exception);
        }
    }

    public static class EipProcessing extends BackendExceptions {
        public EipProcessing(BceInternalResponseException exception) {
            super("IP is in process, try again later. ", exception);
        }
    }

    public static class Unauthorized extends BackendExceptions {
        public Unauthorized(BceInternalResponseException exception) {
            super("Auth code incorrect, please try again. ", exception);
        }
    }

    public static class RDSInstanceNotFound extends BackendExceptions {
        public RDSInstanceNotFound(BceInternalResponseException exception) {
            super("当前地域不存在该实例.",
                    HttpStatus.ERROR_RESOURCE_NOT_EXIST, "RDSInstanceNotFound");
        }
    }

    public static class VerifyCountExceedLimitError extends BackendExceptions {
        public VerifyCountExceedLimitError(BceInternalResponseException exception) {
            super("Verify count exceed limit error", exception);
        }
    }

    public static class MigrationNotFound extends BackendExceptions {
        public MigrationNotFound(BceInternalResponseException exception) {
            super("MigrationId can not be found. ", exception);
        }
    }

    public static class NotConnectionDb extends BackendExceptions {
        public NotConnectionDb(BceInternalResponseException exception) {
            super("Connect database failed. ", exception);
        }
    }

    public static class MigrationStateChange extends BackendExceptions {
        public MigrationStateChange(BceInternalResponseException exception) {
            super("Migration status error. ", exception);
        }
    }

    public static class MigrationCheckStatus extends BackendExceptions {
        public MigrationCheckStatus(BceInternalResponseException exception) {
            super("Migration status check error. ", exception);
        }
    }

    public static class SnapshotMaxLimit extends BackendExceptions {
        public SnapshotMaxLimit(BceInternalResponseException exception) {
            super("Today's backup has reached the upper limit.", exception);
        }
    }

    public static class InstanceStateError extends BackendExceptions {
        public InstanceStateError(BceInternalResponseException exception) {
            super("Instance status error", exception);
        }
    }

    public static class IllegalRangeParameterValue extends BackendExceptions {
        public IllegalRangeParameterValue(BceInternalResponseException exception) {
            super("The parameter is not within the specified range.", exception);
        }
    }

    public static class PermisssionError extends BackendExceptions {
        public PermisssionError(BceInternalResponseException exception) {
            super("This user is forbidden to this operation, please change other account and try again.", exception);
        }
    }

    public static class NotEnoughBalanceForPayOrder extends BackendExceptions {
        public NotEnoughBalanceForPayOrder(BceInternalResponseException exception) {
            super("You have not enough balance for pay order.", exception);
        }
    }

    public static class InsufficientBalance extends BackendExceptions {
        public InsufficientBalance(BceInternalResponseException exception) {
            super("You have not enough balance for pay order.", exception);
        }
    }

    public static class InternalError extends BackendExceptions {
        public InternalError(BceInternalResponseException exception) {
            super("Internal Server Error", exception);
        }
    }
    public static class ParameterBindError extends BackendExceptions {
        public ParameterBindError(BceInternalResponseException exception) {
            super("Error occurred while binding the request body to the struct", exception);
        }
    }
    public static class ValidateFailed extends BackendExceptions {
        public ValidateFailed(BceInternalResponseException exception) {
            super("Validation failed", exception);
        }
    }
    public static class PageNotFound extends BackendExceptions {
        public PageNotFound(BceInternalResponseException exception) {
            super("Page not found", exception);
        }
    }
    public static class DBError extends BackendExceptions {
        public DBError(BceInternalResponseException exception) {
            super("Database error", exception);
        }
    }
    public static class ServiceAlreadyOpened extends BackendExceptions {
        public ServiceAlreadyOpened(BceInternalResponseException exception) {
            super("Service has already been opened", exception);
        }
    }
    public static class ServiceDisabled extends BackendExceptions {
        public ServiceDisabled(BceInternalResponseException exception) {
            super("Service has already been disabled or not opened", exception);
        }
    }
    public static class ServerStatusError extends BackendExceptions {
        public ServerStatusError(BceInternalResponseException exception) {
            super("Service status is Error", exception);
        }
    }
    public static class InstanceNotSatisfiable extends BackendExceptions {
        public InstanceNotSatisfiable(BceInternalResponseException exception) {
            super("RDS Instance is unsatisfialbe.", exception);
        }
    }
    public static class InstanceStatusError extends BackendExceptions {
        public InstanceStatusError(BceInternalResponseException exception) {
            super("RDS instance not available.", exception);
        }
    }
    public static class RoGroupAppStatusNotSatisfy extends BackendExceptions {
        public RoGroupAppStatusNotSatisfy(BceInternalResponseException exception) {
            super("read replica status in ro_group is not expect! ", exception);
        }
    }

    public static class RoGroupNotFound extends BackendExceptions {
        public RoGroupNotFound(BceInternalResponseException exception) {
            super("RoGroupNotFound. ", exception);
        }
    }

    public static class RoGroupStatusNotSatisfy extends BackendExceptions {
        public RoGroupStatusNotSatisfy(BceInternalResponseException exception) {
            super("RoGroupStatusNotSatisfy. ", exception);
        }
    }

    public static class ReadReplicaStatusNotSatisfy extends BackendExceptions {
        public ReadReplicaStatusNotSatisfy(BceInternalResponseException exception) {
            super("ReadReplicaStatusNotSatisfy. ", exception);
        }
    }

    public static class RoGroupMaxLimit extends BackendExceptions {
        public RoGroupMaxLimit(BceInternalResponseException exception) {
            super("RoGroupMaxLimit. ", exception);
        }
    }

    public static class RoGroupAppNotFound extends BackendExceptions {
        public RoGroupAppNotFound(BceInternalResponseException exception) {
            super("RoGroupAppNotFound. ", exception);
        }
    }

    public static class SourceApplicationStatusNotSatisfy extends BackendExceptions {
        public SourceApplicationStatusNotSatisfy(BceInternalResponseException exception) {
            super("SourceApplicationStatusNotSatisfy. ", exception);
        }
    }

    public static class ReadReplicaAlreadyInRoGroup extends BackendExceptions {
        public ReadReplicaAlreadyInRoGroup(BceInternalResponseException exception) {
            super("ReadReplicaAlreadyInRoGroup. ", exception);
        }
    }

    public static class RoGroupAppMaxLimit extends BackendExceptions {
        public RoGroupAppMaxLimit(BceInternalResponseException exception) {
            super("RoGroupAppMaxLimit. ", exception);
        }
    }


    public static class EntryPortInvailid extends BackendExceptions {
        public EntryPortInvailid(BceInternalResponseException exception) {
            super("port is invalid.", exception);
        }
    }

    public static class InstanceTypeError extends BackendExceptions {
        public InstanceTypeError(BceInternalResponseException exception) {
            super("The instance type does not support this operation!", exception);
        }
    }

    public static class InstanceAlreadyDeleted extends BackendExceptions {
        public InstanceAlreadyDeleted(BceInternalResponseException exception) {
            super("RDS实例已删除!", exception);
        }
    }

    public static class PermissionDeny extends BackendExceptions {
        public PermissionDeny(BceInternalResponseException exception) {
            super("权限拒绝，子用户需要主用户赋予完全控制权限!", exception);
        }
    }

    public static class InvalidRequest extends BackendExceptions {
        public InvalidRequest(BceInternalResponseException exception) {
            super("Invalid Request!", exception);
        }
    }

    public static class NotSupportOperation extends BackendExceptions {
        public NotSupportOperation(BceInternalResponseException exception) {
            super("You are not allowed to execute this operation!", exception);
        }
    }
    public static class AccountDependent extends BackendExceptions {
        public AccountDependent(BceInternalResponseException exception) {
            super("Please check whether the account is dependent!", exception);
        }
    }
}
