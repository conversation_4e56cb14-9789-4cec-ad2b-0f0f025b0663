package com.baidu.bce.logic.rds.service.util;

import com.baidu.bce.logic.core.constants.Payment;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.OthersService;
import com.baidu.bce.logic.rds.service.constant.EngineType;
import com.baidu.bce.logic.rds.service.constant.InstanceType;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.instance.InstanceExtension;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Created by luping03 on 17/12/28.
 */
@Component
public class InstanceForApiUtils {
    private static final Logger logger = LoggerFactory.getLogger(InstanceForApiUtils.class);
    @Autowired
    private OthersService othersService;

    @Autowired
    private InstanceService instanceService;

    @Autowired
    private InstanceDao instanceDao;

    @Autowired
    private IdMapperService idMapperService;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    public InstanceExtension standardInstanceExtension(String instanceId, InstanceExtension instance) {
//        instance.setInstanceId(instanceId);
        instance.setEngine(EngineType.getApiValue(instance.getEngine()));
//        if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012.equals(instance.getEngineVersion())) {
//            instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012SP3);
//            instance.setApplicationType(RDSConstant.CATEGORY_BASIC);
//        } else {
//            instance.setApplicationType(RDSConstant.CATEGORY_STANDARD);
//        }
        // sqlServer新增2016单机版
        if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012.equals(instance.getEngineVersion())) {
            instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012SP3);
            instance.setApplicationType(RDSConstant.CATEGORY_BASIC);
        } else if (RDSConstant.RDS_ENGINE_SQLSERVER_SINGLEVERSION2016.equals(instance.getEngineVersion())
                || (instance.getEngine().equals(RDSConstant.RDS_ENGINE_SQLSERVER)
                && instance.getApplicationType().equals(RDSConstant.APPLICATION_TYPE_SINGLE))) {
            instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016);
            instance.setApplicationType(RDSConstant.CATEGORY_BASIC);
        } else {
            instance.setApplicationType(RDSConstant.CATEGORY_STANDARD);
        }
        instance.setInstanceStatus(BasisUtils.upperCaseFirstChar(instance.getInstanceStatus()));
        // TODO 内存转化GB
        instance.setEipStatus(BasisUtils.upperCaseFirstChar(instance.getEipStatus()));
        instance.setReplicationType(BasisUtils.upperCaseFirstChar(instance.getReplicationType()));
        instance.setInstanceType(InstanceType.getValueInApi(instance.getInstanceType()));
        if (StringUtils.isNotEmpty(instance.getSourceInstanceId())) {
            instance.setSourceInstanceId(instanceDao.queryInstanceId(instance.getSourceInstanceId()));
        }

        String[] azones = instance.getAzone().split(",");
        for (String azone : azones) {
            instance.getZoneNames().add(othersService.logicalZoneTolApiZone(azone));
        }
        for (InstanceExtension.Subnet subnet : instance.getSubnets()) {
            subnet.setAz(othersService.logicalZoneTolApiZone(subnet.getAz()));
        }

        List<String> longIds = new ArrayList<>();
        if (instance.getTopology().getMaster() != null && instance.getTopology().getMaster().size() != 0) {
            longIds.addAll(instance.getTopology().getMaster());
        }
        if (instance.getTopology().getReadReplica() != null && instance.getTopology().getReadReplica().size() != 0) {
            longIds.addAll(instance.getTopology().getReadReplica());
        }
        if (instance.getTopology().getRdsproxy() != null && instance.getTopology().getRdsproxy().size() != 0) {
            longIds.addAll(instance.getTopology().getRdsproxy());
        }
        Map<String, String> idMap = new HashMap<>();
        if (longIds.size() != 0) {
            idMap = idMapperService.idMapByLongId(longIds);
        }
        int index = 0;
        if (instance.getTopology().getMaster() != null && instance.getTopology().getMaster().size() != 0) {
            for (String instanceUuid : instance.getTopology().getMaster()) {
                if (BasisUtils.isLongId(instanceUuid)) {
                    instance.getTopology().getMaster().set(index, idMap.get(instanceUuid));
                }
                index ++;
            }
        }
        index = 0;
        if (instance.getTopology().getReadReplica() != null && instance.getTopology().getReadReplica().size() != 0) {
            for (String instanceUuid : instance.getTopology().getReadReplica()) {
                if (BasisUtils.isLongId(instanceUuid)) {
                    instance.getTopology().getReadReplica().set(index, idMap.get(instanceUuid));
                }
                index ++;
            }
        }
        index = 0;
        if (instance.getTopology().getRdsproxy() != null && instance.getTopology().getRdsproxy().size() != 0) {
            for (String instanceUuid : instance.getTopology().getRdsproxy()) {
                if (BasisUtils.isLongId(instanceUuid)) {
                    instance.getTopology().getRdsproxy().set(index, idMap.get(instanceUuid));
                }
                index ++;
            }
        }
        instance.setProductType(Payment.getShowInApiName(instance.getProductType()));
        return instance;
    }

    private void fiterByPermission(Collection<InstanceAbstract> instanceAbstracts) {
        logger.debug("instanceAbstracts.size = {}", instanceAbstracts.size());
        List<InstanceAbstract> masterInstanceAbstracts = new ArrayList<>();
        for (InstanceAbstract instanceAbstract : instanceAbstracts) {
            if (instanceAbstract.getSourceInstanceId() == null || instanceAbstract.getSourceInstanceId().isEmpty()) {
                masterInstanceAbstracts.add(instanceAbstract);
            }
        }
        logger.debug("masterInstanceAbstracts.size = {}", masterInstanceAbstracts.size());

        Collection<InstanceAbstract> permissionMasterInstanceAbstracts = instanceService
                .batchPermission(masterInstanceAbstracts);
        logger.debug("permissionMasterInstanceAbstracts.size = {}", permissionMasterInstanceAbstracts.size());
        Set<String> permissionMasterInstanceShortIds = new HashSet<>(permissionMasterInstanceAbstracts.size());
        Set<String> permissionMasterInstanceIds = new HashSet<>(permissionMasterInstanceAbstracts.size());
        for (InstanceAbstract permissionMasterInstanceAbstract : permissionMasterInstanceAbstracts) {
            permissionMasterInstanceShortIds.add(permissionMasterInstanceAbstract.getInstanceShortId());
            permissionMasterInstanceIds.add(permissionMasterInstanceAbstract.getInstanceId());
        }
        logger.debug("permissionMasterInstanceShortIds.size = {}", permissionMasterInstanceShortIds.size());
        logger.debug("permissionMasterInstanceIds.size = {}", permissionMasterInstanceIds.size());
        Iterator<InstanceAbstract> iter = instanceAbstracts.iterator();
        while (iter.hasNext()) {
            InstanceAbstract instanceAbstract = iter.next();
            if (instanceAbstract.getSourceInstanceId() == null || instanceAbstract.getSourceInstanceId().isEmpty()) {
                // 主实例
                if (!permissionMasterInstanceShortIds.contains(instanceAbstract.getInstanceShortId())) {
                    logger.debug("remove master instance, instanceShortId = {}", instanceAbstract.getInstanceShortId());
                    iter.remove();
                }
            } else {
                // 只读实例、代理实例
                if (!permissionMasterInstanceShortIds.contains(instanceAbstract.getSourceInstanceId())
                        && !permissionMasterInstanceIds.contains(instanceAbstract.getSourceInstanceId())) {
                    logger.debug("remove readOnly or proxy instance, instanceShortId = {}",
                            instanceAbstract.getInstanceShortId());
                    iter.remove();
                }
            }
        }
    }

    public Collection<InstanceAbstract> standardInstanceAbstractList(Collection<InstanceAbstract> result,
                                                                     String from, Map<String, String> filterMap,
                                                                     boolean onlyFilterMaster) {
        List<String> longIds = new ArrayList<>();
        for (InstanceAbstract instance : result) {
            longIds.add(instance.getInstanceId());
            if (StringUtils.isNotEmpty(instance.getSourceInstanceId())) {
                longIds.add(instance.getSourceInstanceId());
            }
        }
        Map<String, String> idMap = idMapperService.idMapByLongId(longIds);
        for (InstanceAbstract instance : result) {
            String shortId = idMap.get(instance.getInstanceId());
            if (StringUtils.isEmpty(shortId)) {
//                throw new RDSExceptions.ResourceServerException();
                logger.warn("api list can not find shortId,instanceUuid:{}", instance.getInstanceId());
                continue;
            }
            instance.setInstanceShortId(shortId);
        }
        logger.debug("before fiterByPermission result.size = {}", result.size());
        fiterByPermission(result);
        logger.debug("after fiterByPermission result.size = {}", result.size());


        Collection<InstanceAbstract> newInstanceList = new ArrayList<>();
        for (InstanceAbstract instance : result) {
            String shortId = idMap.get(instance.getInstanceId());
            if (StringUtils.isEmpty(shortId)) {
//                throw new RDSExceptions.ResourceServerException();
                logger.warn("api list can not find shortId,instanceUuid:{}", instance.getInstanceId());
                continue;
            }
            instance.setInstanceShortId(shortId);
            if (instanceService.filterInstance(instance, filterMap, onlyFilterMaster)) {
                logger.warn("list can not filter,instanceUuid:{}", instance.getInstanceId());
                continue;
            }
            if (filterMap == null || filterMap.get("instanceType") == null
                    || !filterMap.get("instanceType").equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
                // 不含有instanceType = financial过滤，则不展示creating状态的raft 实例
                if (instance.getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
                    continue;
                }
            }

            if (filterMap != null && filterMap.get("vnetIp") != null) {
                String vnetIp = filterMap.get("vnetIp");
                if (instance.getEndpoint().getVnetIp() == null
                        || !instance.getEndpoint().getVnetIp().contains(vnetIp)) {
                    continue;
                }
            }

            if (filterMap != null && filterMap.get("inetIp") != null) {
                String inetIp = filterMap.get("inetIp");
                if (instance.getEndpoint().getInetIp() == null
                        || !instance.getEndpoint().getInetIp().contains(inetIp)) {
                    continue;
                }
            }

            if (filterMap != null && filterMap.get("address") != null) {
                String address = filterMap.get("address");
                if (instance.getEndpoint().getAddress() == null
                        || !instance.getEndpoint().getAddress().contains(address)) {
                    continue;
                }
            }

            if (filterMap != null && filterMap.get("vpcId") != null) {

                String filterVpcId = filterMap.get("vpcId");
                // 过滤vpcId
                if (instance.getVpcId() == null || !instance.getVpcId().equals(filterVpcId)) {
                    continue;
                }
            }

            if (filterMap != null && filterMap.get("status") != null) {

                String filterStatus = filterMap.get("status");
                // 过滤vpcId
                if (instance.getInstanceStatus() == null
                        || !instance.getInstanceStatus().toLowerCase()
                            .equals(filterStatus.toLowerCase())) {
                    continue;
                }
            }
            logger.warn("api list can add newInstanceList,instanceUuid:{}", instance.getInstanceId());
            newInstanceList.add(instance);
            if (!RDSConstant.FROM_API.equalsIgnoreCase(from)) {
                continue;
            }
            instance.setEngine(EngineType.getApiValue(instance.getEngine()));
            if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012.equals(instance.getEngineVersion())) {
                instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2012SP3);
                instance.setApplicationType(RDSConstant.CATEGORY_BASIC);
                // sqlServer新增2016单机版
            } else if (RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016.equals(instance.getEngineVersion())
                && RDSConstant.APPLICATION_TYPE_SINGLE.equals(instance.getApplicationType())) {
                // instance.setEngineVersion(RDSConstant.RDS_ENGINE_SQLSERVER_VERSION2016);
                instance.setApplicationType(RDSConstant.CATEGORY_BASIC);
            } else {
                instance.setApplicationType(RDSConstant.CATEGORY_STANDARD);
            }
            instance.setInstanceStatus(BasisUtils.upperCaseFirstChar(instance.getInstanceStatus()));
            // TODO 内存转化GB
            instance.setEipStatus(BasisUtils.upperCaseFirstChar(instance.getEipStatus()));
            instance.setInstanceType(InstanceType.getValueInApi(instance.getInstanceType()));
            if (StringUtils.isNotEmpty(instance.getSourceInstanceId())) {
                instance.setSourceInstanceId(idMap.get(instance.getSourceInstanceId()));
            }
            String[] azones = instance.getAzone().split(",");
            for (String azone : azones) {
                instance.getZoneNames().add(othersService.logicalZoneTolApiZone(azone));
            }
        }
        return newInstanceList;
    }
}
