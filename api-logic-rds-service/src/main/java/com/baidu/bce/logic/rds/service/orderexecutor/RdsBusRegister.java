package com.baidu.bce.logic.rds.service.orderexecutor;

import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.plat.webframework.endpoint.SDKEndpointConfiguration;
import com.baidu.bce.service.bus.sdk.BusClient;
import com.baidu.bce.service.bus.sdk.util.BusConstants;
import com.baidu.bce.service.bus.sdk.util.InetAddressHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

/**
 * Created by luping03 on 2018/03/09.
 */
@Service
@ConditionalOnExpression("${rds.bus.enabled:true}")
public class RdsBusRegister implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(RdsBusRegister.class);

    @Value("${rds.bus.registered:true}")
    private boolean rdsBusRegistered;

    @Value("${server.port}")
    private String port;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    // 保证初始化此实例时，endpoint已初始化完毕
    @Autowired
    private SDKEndpointConfiguration sdkEndpointConfiguration;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Override
    public void afterPropertiesSet() throws Exception {
        BusClient busClient = clientFactory.createBusClientForConsole();

        String endpoint = InetAddressHelper.getHostAddress() + ":" + port;
//        LOGGER.info("Register - endpoint" + endpoint );
        if (rdsBusRegistered) {
//            LOGGER.info("Register - endpoint" + endpoint );
            busClient.registerService(BusConstants.SERVICE_TYPE_CONSOLE, "RDS", endpoint,
                    regionConfiguration.getCurrentRegion(), null);
            LOGGER.info("Register success : {}.", "console, RDS, " + endpoint);
            busClient.registerService(BusConstants.SERVICE_TYPE_CONSOLE, "RDS_REPLICA", endpoint,
                    regionConfiguration.getCurrentRegion(), null);
            LOGGER.info("Register success : {}.", "console, RDS_REPLICA, " + endpoint);
            busClient.registerService(BusConstants.SERVICE_TYPE_CONSOLE, "RDS_PROXY", endpoint,
                    regionConfiguration.getCurrentRegion(), null);
            LOGGER.info("Register success : {}.", "console, RDS_PROXY, " + endpoint);
        } else {
//            LOGGER.info("Unregister - endpoint" + endpoint );
            busClient.unregisterService(BusConstants.SERVICE_TYPE_CONSOLE, "RDS", endpoint);
            LOGGER.info("Unregister success : {}.", "console, RDS, " + endpoint);
            busClient.unregisterService(BusConstants.SERVICE_TYPE_CONSOLE, "RDS_REPLICA", endpoint);
            LOGGER.info("Unregister success : {}.", "console, RDS_REPLICA, " + endpoint);
            busClient.unregisterService(BusConstants.SERVICE_TYPE_CONSOLE, "RDS_PROXY", endpoint);
            LOGGER.info("Unregister success : {}.", "console, RDS_PROXY, " + endpoint);
        }
    }

}
