package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogGetResponse;
import com.baidu.bce.internalsdk.rds.model.slowlog.SlowlogListResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryBaseResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryChartRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryChartResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryCreateTaskRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryCreateTaskResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDetailRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDetailResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDownloadRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryDownloadResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryGetSqlRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryGetSqlResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryInstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryOpenStatusResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlExplainResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerFeedbackRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySqlOptimizerResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryStartOrStopResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySummaryRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowquerySummaryResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskDetailResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskListRequest;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskListResponse;
import com.baidu.bce.internalsdk.rds.model.slowquery.SlowqueryTaskModel;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.service.constant.RdsInstanceStatus;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.logic.rds.service.util.RdsDateUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by luping03 on 17/11/3.
 */
@Service
public class SlowqueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SlowqueryService.class);


    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    InstanceDao instanceDao;

    public SlowlogListResponse list(String instanceId, String datetime) {
        return clientFactory.createRdsClientByInstanceId(instanceId).slowlogList(instanceId, datetime);
    }

    public SlowqueryChartResponse chart(SlowqueryChartRequest requestBody, String from) {

        // 请求后端用长id
        requestBody.setInstanceId(transShortIdToLongId(requestBody.getInstanceId()));

        return clientFactory.createSlowqueryClient().chart(requestBody, from);
    }

    public SlowlogGetResponse detail(String instanceId, String logId, Integer downloadValidTimeInSec) {

        // 请求后端用长id
        instanceId = transShortIdToLongId(instanceId);

        return clientFactory.createRdsClientByInstanceId(instanceId)
                .slowlogGet(instanceId, logId, downloadValidTimeInSec);
    }

    public SlowqueryDetailResponse detail(SlowqueryDetailRequest requestBody, String from) {

        // 请求后端用长id
        requestBody.setInstanceId(transShortIdToLongId(requestBody.getInstanceId()));

        return clientFactory.createSlowqueryClient().detail(requestBody, from);
    }

    public SlowquerySummaryResponse summary(SlowquerySummaryRequest requestBody, String from) {

        // 临时解决遗留问题
        if (ObjectUtils.equals(requestBody.getOrderBy(), "execute_time")) {
            requestBody.setOrderBy("execute_times");
        }

        // 请求后端用长id
        requestBody.setInstanceId(transShortIdToLongId(requestBody.getInstanceId()));

        return clientFactory.createSlowqueryClient().summary(requestBody, from);
    }

    public SlowqueryGetSqlResponse getSql(SlowqueryGetSqlRequest requestBody) {

        // 请求后端用长id
        requestBody.setInstanceId(transShortIdToLongId(requestBody.getInstanceId()));

        return clientFactory.createSlowqueryClient().getSql(requestBody);
    }

    public SlowqueryDownloadResponse downloadfile(SlowqueryDownloadRequest request) {

        // 请求后端用长id
        request.setInstanceId(transShortIdToLongId(request.getInstanceId()));

        return clientFactory.createSlowqueryClient().downloadfile(request);
    }

    public SlowqueryOpenStatusResponse getOpenStatus(String instanceId, String from) {

        // api 需要转成短ID
        if (StringUtils.isNotBlank(from) && RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            instanceId = findInstanceIdByInstanceShortId(instanceId);
        }

        // 请求后端用长id
        instanceId = transShortIdToLongId(instanceId);

        return clientFactory.createSlowqueryClient().getOpenStatus(instanceId);
    }

    public SlowqueryStartOrStopResponse start(SlowqueryInstanceIdRequest requestBody) {

        // 请求后端用长id
        requestBody.setInstanceId(transShortIdToLongId(requestBody.getInstanceId()));

        return clientFactory.createSlowqueryClient().start(requestBody);
    }

    public SlowqueryStartOrStopResponse stop(SlowqueryInstanceIdRequest requestBody) {

        // 请求后端用长id
        requestBody.setInstanceId(transShortIdToLongId(requestBody.getInstanceId()));

        return clientFactory.createSlowqueryClient().stop(requestBody);
    }


    public SlowqueryCreateTaskResponse createTask(SlowqueryCreateTaskRequest requestBody) {

        // 短id转长id
        requestBody.setInstanceId(findInstanceIdByInstanceShortId(requestBody.getInstanceId()));

        return clientFactory.createSlowqueryClient().createTask(requestBody);
    }

    public SlowqueryTaskListResponse taskList(SlowqueryTaskListRequest requestBody) {

        // 短id转长id
        String instanceUuid = findInstanceIdByInstanceShortId(requestBody.getInstanceId());

        requestBody.setInstanceId(instanceUuid);

        // 补充过滤时间，从当前往前推24
        if (StringUtils.isBlank(requestBody.getFilterStartTime())
                && StringUtils.isBlank(requestBody.getFilterEndTime()) ) {

            requestBody.setFilterStartTime(RdsDateUtils.getAddDayCurrentUtcStr(-1));

            requestBody.setFilterEndTime(RdsDateUtils.getCurrentUtcStr());
        }

        return clientFactory.createSlowqueryClient().taskList(requestBody);
    }

    public SlowqueryTaskDetailResponse taskDetail(String instanceId, String taskId) {

        String instanceUuid = null;

        // 短id转长id
        instanceUuid = findInstanceIdByInstanceShortId(instanceId);

        if (StringUtils.isBlank(instanceId) || StringUtils.isBlank(taskId)) {
            throw new RDSExceptions.ParamValidationException();
        }

        return clientFactory.createSlowqueryClient().taskDetail(instanceUuid, taskId);
    }

    public SlowquerySqlOptimizerResponse sqlOptimizer(SlowquerySqlOptimizerRequest request) {

        String instanceUuid = null;

        // 不是长id，则转换成长id
        if (!BasisUtils.isLongId(request.getInstanceId())) {
            instanceUuid = findInstanceIdByInstanceShortId(request.getInstanceId());

            // 替换成长id
            request.setInstanceId(instanceUuid);
        }

        if (StringUtils.isBlank(request.getInstanceId()) ) {
            throw new RDSExceptions.ParamValidationException();
        }

        return clientFactory.createSlowqueryClient().sqlOptimizer(request);
    }

    public SlowquerySqlExplainResponse sqlExplain(SlowquerySqlOptimizerRequest request) {

        String instanceUuid = null;

        // 不是长id，则转换成长id
        if (!BasisUtils.isLongId(request.getInstanceId())) {
            instanceUuid = findInstanceIdByInstanceShortId(request.getInstanceId());

            // 替换成长id
            request.setInstanceId(instanceUuid);
        }

        if (StringUtils.isBlank(request.getInstanceId()) ) {
            throw new RDSExceptions.ParamValidationException();
        }

        return clientFactory.createSlowqueryClient().sqlExplain(request);
    }

    public SlowqueryBaseResponse sqlOptimizerFeedback(SlowquerySqlOptimizerFeedbackRequest request) {

        String instanceUuid = null;

        // 不是长id，则转换成长id
        if (!BasisUtils.isLongId(request.getInstanceId())) {
            instanceUuid = findInstanceIdByInstanceShortId(request.getInstanceId());

            // 替换成长id
            request.setInstanceId(instanceUuid);
        }

        if (StringUtils.isBlank(request.getInstanceId()) ) {
            throw new RDSExceptions.ParamValidationException();
        }

        return clientFactory.createSlowqueryClient().sqlOptimizerFeedback(request);
    }

    public LogicMarkerResultResponse<SlowqueryTaskModel> listForPageByMarker
                                        (SlowqueryTaskListResponse responseSource,
                                         String marker,
                                         int maxKeys) {
        LogicMarkerResultResponse<SlowqueryTaskModel> response = new LogicMarkerResultResponse<>();
        response.setMarker(marker);
        response.setMaxKeys(maxKeys);

        // 记录
        List<SlowqueryTaskModel> models = responseSource.getPage().getResult();

        List<SlowqueryTaskModel> resultModels = new ArrayList<SlowqueryTaskModel>();

        // 总记录数
        int totalCount = responseSource.getPage().getTotalCount();

        // 最多支持100条记录
        int maxCount = Math.min(totalCount, 100);

        // 当前页数
        int pageNo = Integer.valueOf(responseSource.getPage().getPageNo());

        // 每页最大数量
        int pageSize = Integer.valueOf(responseSource.getPage().getPageSize());

        int currentPageSize = models.size();

        // 是否已经达到末尾，默认后面还有实例
        boolean isTruncated = Boolean.TRUE;

        // 当前记录总数
        int currentSize = (pageNo - 1) * pageSize + currentPageSize;

        String nextMarker = null;
        // 截断数据
        if (StringUtils.isBlank(marker) || ObjectUtils.equals(marker, "-1")) {
            // 声明没有这个参数的值
            nextMarker = null;

            for (int i = 0; i < models.size() && i < maxKeys; i++) {
                resultModels.add(models.get(i));
            }

            if (maxKeys < models.size()) {
                // 取下一个
                SlowqueryTaskModel model = models.get(maxKeys);
                nextMarker = String.valueOf(model.getTaskId());
                isTruncated = Boolean.TRUE;
            } else if (maxKeys >= models.size()){
                isTruncated = Boolean.FALSE;
            }

        } else if (models.size() > 0) {
            int disMarkerIndex = 0;
            // -1 默认是没有下一个marker
            int nextMarkerIndex = -1;

            for (int i = 0; i < models.size(); i++) {

                SlowqueryTaskModel each = models.get(i);

                if (ObjectUtils.equals(marker, String.valueOf(each.getTaskId()))) {
                    // 找到目标index
                    disMarkerIndex = i;
                    // 说明已经到末尾
                    int nextMarkerIndexTemp = disMarkerIndex + maxKeys;
                    if (nextMarkerIndexTemp < models.size()) {
                        nextMarkerIndex = nextMarkerIndexTemp;
                    }

                    break;
                }
            }

            int endIndex = nextMarkerIndex == -1 ? models.size() -1 : nextMarkerIndex - 1;

            // 最后一页
            if (endIndex == maxCount - 1) {
                isTruncated = Boolean.FALSE;
            }

            resultModels = models.subList(disMarkerIndex, endIndex + 1);

            LOGGER.info("slow query list listForPageByMarker, org {}, result {}, disMarkerIndex {}, endIndex {}",
                    models.size(), resultModels.size(), disMarkerIndex, endIndex);

            if (nextMarkerIndex > 0) {
                SlowqueryTaskModel model = models.get(nextMarkerIndex);
                if (model != null) {
                    nextMarker = String.valueOf(model.getTaskId());
                }
            }
        }

        response.setIsTruncated(isTruncated);
        response.setNextMarker(nextMarker);
        response.setResult(resultModels);
        return response;
    }

    private String findInstanceIdByInstanceShortId(String shortId) {
        // 兼容长ID 不报错
        if (BasisUtils.isLongId(shortId)) {
            return shortId;
        }
        InstancePO instancePO = instanceDao.queryInstanceByInstanceId(shortId, clientFactory.getAccountId());
        if (instancePO == null) {
            throw new RDSExceptions.ResourceNotExistException();
        }
        if (instancePO.getInstanceStatus().equalsIgnoreCase(RdsInstanceStatus.CREATING.getValue())) {
            throw new RDSExceptions.ResourceNotCreatedException();
        }

        return instancePO.getInstanceUuid();
    }

    private String transShortIdToLongId(String instanceId) {
        if (BasisUtils.isShortId(instanceId)) {
            String shortId = instanceId;
            String uuid = findInstanceIdByInstanceShortId(instanceId);

            LOGGER.debug("slowquer transShortIdToLongId() from " + shortId + " to " + uuid);

            return findInstanceIdByInstanceShortId(instanceId);
        } else {
            LOGGER.debug("slowquer transShortIdToLongId() is instanceUuid " + instanceId);

            return instanceId;
        }
    }
}
