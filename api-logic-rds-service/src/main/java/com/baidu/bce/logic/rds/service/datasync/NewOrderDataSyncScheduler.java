package com.baidu.bce.logic.rds.service.datasync;

/**
 * Created by luping03 on 18/1/4.
 */

import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.ResourceMapping;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.internalsdk.trail.util.StringUtil;
import com.baidu.bce.logic.core.constants.Payment;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.dao.model.BatchOrderSyncPO;
import com.baidu.bce.logic.rds.dao.model.OrderNeedToSyncPO;
import com.baidu.bce.logic.rds.dao.mybatis.BatchOrderSyncMapper;
import com.baidu.bce.logic.rds.service.datasync.service.OrderNeedToSyncService;
import com.baidu.bce.logic.rds.service.datasync.service.OrderSyncService;
import com.baidu.bce.logic.rds.service.datasync.service.OrderSyncServiceFactory;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import endpoint.EndpointManager;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 订单类(异步操作)数据同步的执行器
 * <p>
 * Created by luping03 on 2017/12/29.
 */
@EnableScheduling
@Configuration
@Profile("default")
public class NewOrderDataSyncScheduler extends AbstractAutoScheduler {

    private static final Logger LOGGER = LoggerFactory.getLogger(NewOrderDataSyncScheduler.class);

    @Autowired
    OrderNeedToSyncService orderSyncService;

    @Autowired
    RegionConfiguration regionConfiguration;

    @Autowired
    OrderSyncServiceFactory orderSyncServiceFactory;

    @Autowired
    BatchOrderSyncMapper batchOrderSyncMapper;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    private static final List<String> ORDER_STATUS = Arrays.asList("NEED_PURCHASE", "NEED_CONFIRM");

    public static final String ROLLBACK_STATUS = "CANCELLED,EXPIRED,CREATE_FAILED";
    /**
     * 此任务用于对批量变配的关联订单做回滚，保证操作的原子性
     */
    @Override
    public void doCancelBatchOrderJob() {
        LOGGER.debug("Rollback batch resize order...");
        OrderClient orderClient = clientFactory.getOrderClient();
        // 先从库中取出订单类型为预付费且当前并未支付的订单
        List<BatchOrderSyncPO> batchOrderSyncPOList = new ArrayList<>();
        for (String orderS : ORDER_STATUS) {
            batchOrderSyncPOList.addAll(batchOrderSyncMapper.selectNeedPurchaseOrder(orderS));
        }
//        List<BatchOrderSyncPO> batchOrderSyncPOList = batchOrderSyncMapper.selectNeedPurchaseOrder(ORDER_STATUS);
        // 去Billing 侧查询当前订单最新状态（由于 billing 侧对该订单是支付还是取消，均不回调我们）

        if (batchOrderSyncPOList != null && batchOrderSyncPOList.size() != 0) {
            String[] split1 = ROLLBACK_STATUS.split(",");
            List<String> rollbackStatus = new ArrayList<>(Arrays.asList(split1));
            for (BatchOrderSyncPO orderSyncPO : batchOrderSyncPOList) {

                if (StringUtils.isNotEmpty(orderSyncPO.getOrderUuid())) {
                    // 当前预付费订单 最新状态
                    Order order = orderClient.get(orderSyncPO.getOrderUuid());
                    String status = order.getStatus().name();
                    LOGGER.debug("db order status is {}, current order status is {}",
                            orderSyncPO.getOrderStatus(), status);
                    // 若当前订单仍为待支付状态，则跳过，否则需要将该订单的关联订单一起回滚
                    if (rollbackStatus.contains(status)) {
                        // 更新库中主订单状态，防止下次定时任务再次回调
                         batchOrderSyncMapper.destroyOrderStatus(OrderStatus.CREATE_FAILED.name(), order.getUuid());

                        // 回滚掉所有关联订单
                        String[] split = orderSyncPO.getBatchOrders().split(",");
                        for (int i = 0; i < split.length; i++) {
                            // 首条订单过滤掉
                            if (Payment.isPrepay(orderSyncPO.getOrderType())
                                    && orderSyncPO.getOrderUuid().equalsIgnoreCase(split[i])) {
                                LOGGER.debug("the first order is {}", orderSyncPO.getOrderUuid());
                                continue;
                            }
                            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
                            List<ResourceMapping> resources = new ArrayList<ResourceMapping>();
                            updateOrderRequest.setResources(resources);
                            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
                            orderClient.update(split[i], updateOrderRequest);

                            // 更新库中关联实例订单状态，维护正确数据
                            batchOrderSyncMapper.destroyOrderStatus(OrderStatus.CREATE_FAILED.name(), split[i]);
                        }
                    }
                }
            }
        }


    }

    /**
     * 1.遍历每一条记录并尝试锁定, lock_time为默认值（或lockId为空），或lock_time超过锁定上限
     * 2.进行处理每条记录
     * 3.释放锁或删除记录
     */
    @Override
    public void doFixedDelayJob() {
        LOGGER.debug("NewOrderDataSync Begin.");
        EndpointManager.setThreadRegion(regionConfiguration.getCurrentRegion());

        Date lockDate = new Date();
        Date outLockDate = getOutLockDate(lockDate);
        List<OrderNeedToSyncPO> orderSyncPOList = orderSyncService.findOneSyncOrder(outLockDate);
        if (CollectionUtils.isEmpty(orderSyncPOList)) {
            return;
        }

        for (OrderNeedToSyncPO orderSyncPO : orderSyncPOList) {
            String orderUuid = orderSyncPO.getOrderUuid();
            String lockId = UUID.randomUUID().toString();
            lockDate = new Date();
            try {
                int result;
                if (StringUtil.isBlank(orderSyncPO.getLockId())) {
                    result = orderSyncService.lockOneLine(
                            orderSyncPO.getId(), lockDate, lockId, orderSyncPO.getLockTime());
                    if (result > 0) {
                        LOGGER.debug("#lock One NeedToSync uuid:{},recordDate:{},lockId:{},result:{}",
                                orderUuid, orderSyncPO.getCreateTime(), lockId, result);
                    }
                } else {
                    outLockDate = getOutLockDate(lockDate);
                    result = orderSyncService.seizeLockOneLine(orderSyncPO.getId(), outLockDate, lockDate, lockId);
                    if (result > 0) {
                        LOGGER.debug("#seizeLock One NeedToSync uuid:{},recordDate:{},lockDate:{},outLockDate:{},"
                                        + "lockId:{},result:{}", orderUuid, orderSyncPO.getCreateTime(),
                                lockDate, outLockDate, lockId, result);
                    }
                }
                if (result <= 0) {
                    continue;
                }
                orderSyncPO.setLockTime(new Timestamp(lockDate.getTime()));
                orderSyncPO.setLockId(lockId);
                result = continueJobAfterLock(orderSyncPO);
                unLockRecord(result, orderSyncPO, orderUuid);
            } catch (Exception e) {
                LOGGER.error("doFixedDelayJob:ERROR uuid:{},recordDate:{},lockDate:{},"
                        + "lockId:{}, ERROR:{}", orderSyncPO, orderSyncPO.getCreateTime(), lockDate, lockId, e);
            }
        }
        LOGGER.debug("NewOrderDataSync End.");
    }

    /**
     * result == 1:订单执行完毕释放锁
     * result != 1:订单未执行完毕不释放锁，等待下次抢锁执行
     *
     * @param needToSync
     * @param resourceUUid
     */
    private void unLockRecord(int result, OrderNeedToSyncPO needToSync, String resourceUUid) {
        if (result == 1) {
            result = orderSyncService.unLockOneLine(needToSync);
            needToSync.setUpdatedTime(new Timestamp(Calendar.getInstance().getTimeInMillis()));
            LOGGER.debug("NewOrderDataSync#SYNC_NEW_ORDER_OVER synId:{},recordDate:{},lockId:{},result:{}",
                    resourceUUid, needToSync.getCreateTime(), needToSync.getLockId(), result);
        }
    }

    /**
     * 获得锁之后，该做具体的数据同步了
     * <p>
     * 结果:释放锁
     *
     * @param orderSyncPO
     * @return
     */
    private int continueJobAfterLock(OrderNeedToSyncPO orderSyncPO) {
        //  TODO
        OrderSyncService orderSyncService = orderSyncServiceFactory.getOrderService(orderSyncPO);
        return orderSyncService.syncData(orderSyncPO);
    }

    @Override
    public String currentThreadName() {
        return "NewOrderDataSyncScheduler";
    }
}
