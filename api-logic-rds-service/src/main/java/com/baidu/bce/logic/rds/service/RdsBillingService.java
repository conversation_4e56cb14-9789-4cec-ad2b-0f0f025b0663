package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.sdk.renew.AutoRenewClient;
import com.baidu.bce.sdk.renew.model.AutoRenewResource;
import com.baidu.bce.sdk.renew.model.AutoRenewRule;
import com.baidu.bce.sdk.renew.model.AutoRenewRules;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 封装与 Billing 交互相关的逻辑，该类仅用于封装 Billing 相关逻辑，然后由其他 Service 类调用，所以本类尽量不要依赖其他 Service 类，
 * 仅依赖 Billing API。
 *
 * <AUTHOR>
 * @since 9/10/21
 */
@Service
public class RdsBillingService {

    @Autowired
    private LogicRdsClientFactory rdsClientFactory;

    @Autowired
    private RegionConfiguration regionConfiguration;


    /**
     * 根据实例ID列表查询对应的自动续费规则
     *
     * @param instanceIds 实例ID列表，批量个数无上限
     * @return instanceIdAutoRenewRuleMap
     */
    public Map<String, AutoRenewRule> getAutoRenewRuleMapByInstanceIds(Collection<InstanceAbstract> instanceIds) {
        if (instanceIds == null) {
            return null;
        }
        if (instanceIds.size() == 0) {
            return new HashMap<>(0);
        }

        Map<String, AutoRenewRule> instanceIdAutoRenewRuleMap = new HashMap<>();

        int batchSize = 100;
        List<AutoRenewResource> autoRenewResources = new ArrayList<>(batchSize);
        for (InstanceAbstract instance : instanceIds) {
            AutoRenewResource autoRenewResource = new AutoRenewResource();
            autoRenewResource.setAccountId(rdsClientFactory.getAccountId());
            if (StringUtils.isEmpty(instance.getSourceInstanceId())) {
                autoRenewResource.setServiceType(ServiceType.RDS.getName());
            } else if (StringUtils.isNotEmpty(instance.getSourceInstanceId())
                    && StringUtils.isNotEmpty(instance.getInstanceType())
                    && RDSConstant.INSTANCE_TYPE_REPLICA.equalsIgnoreCase(instance.getInstanceType())) {
                autoRenewResource.setServiceType(ServiceType.RDS_REPLICA.getName());
            }
//            else if (StringUtils.isNotEmpty(instance.getSourceInstanceId())
//                    && StringUtils.isNotEmpty(instance.getInstanceType())
//                    && RDSConstant.INSTANCE_TYPE_PROXY.equalsIgnoreCase(instance.getInstanceType())) {
//                autoRenewResource.setServiceType(ServiceType.RDS_PROXY.getName());
//            }
            autoRenewResource.setRegion(regionConfiguration.getCurrentRegion());
            autoRenewResource.setServiceId(instance.getInstanceId());

            autoRenewResources.add(autoRenewResource);
            if (autoRenewResources.size() == batchSize) {
                AutoRenewClient autoRenewClient = rdsClientFactory.createAutoRenewClient();
                AutoRenewRules autoRenewRules = autoRenewClient.getBatchRules(autoRenewResources);
                for (AutoRenewRule autoRenewRule : autoRenewRules) {
                    instanceIdAutoRenewRuleMap.put(autoRenewRule.getServiceId(), autoRenewRule);
                }

                autoRenewResources.clear();
            }
        }
        if (autoRenewResources.size() > 0) {
            AutoRenewClient autoRenewClient = rdsClientFactory.createAutoRenewClient();
            AutoRenewRules autoRenewRules = autoRenewClient.getBatchRules(autoRenewResources);
            for (AutoRenewRule autoRenewRule : autoRenewRules) {
                instanceIdAutoRenewRuleMap.put(autoRenewRule.getServiceId(), autoRenewRule);
            }
        }

        return instanceIdAutoRenewRuleMap;
    }
}
