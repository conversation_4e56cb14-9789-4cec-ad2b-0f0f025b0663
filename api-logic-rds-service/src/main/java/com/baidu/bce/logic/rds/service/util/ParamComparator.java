package com.baidu.bce.logic.rds.service.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * Created by luping03 on 17/10/26.
 */
public class ParamComparator<T> implements Comparator<T> {
    private static Logger log = LoggerFactory.getLogger(ParamComparator.class);

    private static final String ASC_STRING = "asc";
    boolean isAscend = true;
    String paramName;

    public ParamComparator(String paramName, String order) {
        this.paramName = paramName;
        this.isAscend = ASC_STRING.equalsIgnoreCase(order);
    }

    public int compare(T o1, T o2) {
        if (paramName == null || paramName.isEmpty() || o1 == o2) {
            return 0;
        }

        String upperParamName = paramName.substring(0, 1).toUpperCase() + paramName.substring(1);
//        log.info("upperParamName : " + upperParamName);
        try {
            String paramType = getClassField(o1, paramName).getGenericType().toString();
//            log.info("paramType : " + paramType);
            Method m = o1.getClass().getMethod("get" + upperParamName);
            int result;
            if (paramType.equals("class java.lang.String")) {
                String valueParam1 = (String) m.invoke(o1);
                String valueParam2 = (String) m.invoke(o2);
                if ((result = value(valueParam1, valueParam2)) != 0) {
                    return result;
                } else {
                    return isAscend ? valueParam1.compareTo(valueParam2) : valueParam2.compareTo(valueParam1);
                }
            } else if (paramType.equals("class java.util.Date")) {
                Date valueParam1 = (Date) m.invoke(o1);
                Date valueParam2 = (Date) m.invoke(o2);

                if ((result = value(valueParam1, valueParam2)) != 0) {
                    return result;
                } else {
                    return isAscend ? valueParam1.compareTo(valueParam2) : valueParam2.compareTo(valueParam1);
                }
            } else {
                log.info("ParamComparator do not support to compare paramType : '" + paramType + "'.");
                return 0;
            }
        } catch (Exception ex) {
            log.warn("ParamComparator.compare Exception : " + ex.getMessage());
            log.warn("paramName : '" + paramName + "', isAscend : " + isAscend + ", o1 : " + o1 + ", o2 : " + o2);
//            throw ex;
            return 0;
        }
    }

    private <T> Field getClassField(T object, String paramName) throws NoSuchFieldException {
        Field[] fieldsSuper = object.getClass().getSuperclass().getDeclaredFields();
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.getName().equals(paramName)) {
                return field;
            }
        }
        for (Field field : fieldsSuper) {
            if (field.getName().equals(paramName)) {
                return field;
            }
        }
        throw new NoSuchFieldException(paramName);
    }

    private <U> int value(U value1, U value2) {
        if (value1 == null) {
            return isAscend ? -1 : 1;
        } else if (value2 == null) {
            return isAscend ? 1 : -1;
        }
        return 0;
    }

//    public static void main(String[] args) {
//        List<InstanceAbstract> clusters = new ArrayList<>();
//        InstanceAbstract c1 = new InstanceAbstract();
//        c1.setInstanceCreateTime(new Date(System.currentTimeMillis()));
//        clusters.add(c1);
//
//        InstanceAbstract c2 = new InstanceAbstract();
//        c2.setInstanceCreateTime(new Date(System.currentTimeMillis() + 1000));
//        clusters.add(c2);
//
//        // 默认创建时间降序排序
//        Collections.sort(clusters, new ParamComparator<InstanceAbstract>("instanceCreateTime", "desc"));
//    }
}
