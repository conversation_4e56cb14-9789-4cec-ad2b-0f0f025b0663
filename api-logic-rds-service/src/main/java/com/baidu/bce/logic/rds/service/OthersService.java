package com.baidu.bce.logic.rds.service;

import com.baidu.bce.apiservice.client.model.GetAclNameListRequest;
import com.baidu.bce.apiservice.client.model.GetAclNameListResponse;
import com.baidu.bce.externalsdk.logical.network.subnet.ExternalSubnetClient;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.externalsdk.logical.network.subnet.model.request.ListSubnetRequest;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.ProductPayType;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.rds.model.InsertCustomMonitorReq;
import com.baidu.bce.internalsdk.rds.model.ZoneListDetails;
import com.baidu.bce.internalsdk.rds.model.bcchan.GetIsInternalUserResponse;
import com.baidu.bce.internalsdk.rds.model.blb.GetLbdcClusterResponse;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceListResponse;
import com.baidu.bce.internalsdk.rds.model.msg.GroupMessage;
import com.baidu.bce.internalsdk.rds.model.msg.GroupMessageItem;
import com.baidu.bce.internalsdk.rds.model.msg.SendGroupMsgRequest;
import com.baidu.bce.internalsdk.zone.model.UsedZoneList;
import com.baidu.bce.internalsdk.zone.model.ZoneMapDetail;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.result.LogicPageResultResponse;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.CustomMonitorPO;
import com.baidu.bce.logic.rds.dao.model.ZoneFlavorLimitPO;
import com.baidu.bce.logic.rds.dao.mybatis.CustomMonitorMapper;
import com.baidu.bce.logic.rds.dao.mybatis.FlavorMapper;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.idmapper.IdMapperService;
import com.baidu.bce.logic.rds.service.model.GroupWhiteAccountResult;
import com.baidu.bce.logic.rds.service.model.RdsListRequest;
import com.baidu.bce.logic.rds.service.model.WhiteAccountResult;
import com.baidu.bce.logic.rds.service.model.instance.InstanceAbstract;
import com.baidu.bce.logic.rds.service.model.otherservice.AutoRenewDetail;
import com.baidu.bce.logic.rds.service.model.otherservice.ServiceParam;
import com.baidu.bce.logic.rds.service.model.otherservice.ZoneDetailList;
import com.baidu.bce.logic.rds.service.model.otherservice.DiskIoTypeResponse;
import com.baidu.bce.logic.rds.service.model.tag.AssignTagRequest;
import com.baidu.bce.logic.rds.service.model.tag.LogicalAssignResource;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.logic.rds.service.util.ThreadPool;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.DeleteTagAssociationRequest;
import com.baidu.bce.logical.tag.sdk.model.DeltaAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import com.baidu.bce.plat.webframework.exception.BceException;
import com.baidu.bce.sdk.renew.AutoRenewClient;
import com.baidu.bce.sdk.renew.model.AutoRenewRule;
import com.baidu.bce.sdk.renew.model.AutoRenewRules;
import com.baidu.bce.sdk.renew.model.ListAutoRenewRequest;
import com.baidu.bce.user.settings.sdk.UserSettingsClient;
import com.baidu.bce.user.settings.sdk.model.FeatureAclRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.regex.Pattern;

/**
 * Created by luping03 on 17/10/14.
 */
@Service
public class OthersService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OthersService.class);

    @Autowired
    InstanceService instanceService;

    @Autowired
    InstanceDao instanceDao;

    @Autowired
    LogicRdsClientFactory rdsClientFactory;

    public static final int RESOURCE_NUM = 100000;

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private FlavorMapper flavorMapper;

    @Autowired
    IdMapperService idMapperService;

    @Autowired
    private CustomMonitorMapper customMonitorMapper;

    @Value("${rds.unavailable.physicalzone:}")
    private String unavailablePhysicalZone;

    @Value("${rds.su.whitelist.physicalzone:}")
    private String suWhiteListZone;

    @Value("${rds.whitelist.physicalzone.account:}")
    private String suWhiteListAccount;

    @Value("${rds.special.physicalzone:}")
    private String specialPhysicalZone;

    @Value("${rds.unavailable.localdisk.physicalzone:}")
    private String unavailableLocaldiskPhysicalZone;

    @Value("${rds.available.general.instance.physicalzone:}")
    private String availableGeneralInstance;

    @Value("${console.rds.service.accountId:}")
    String consoleRdsServiceAccountId;

    @Value("${console.rds.service.policyId:}")
    String consoleRdsServicePolicyId;

    @Value("${rds.service.accountId:}")
    String rdsServiceAccountId;

    @Value("${rds.service.policyId:}")
    String rdsServicePolicyId;

    @Value("#{'${rds.financial.region:fsh}'.split(',')}")
    private String[] financialRegion;

    public static final String API_LOGICAL_ZONE_RESPONSE_PATTERN = "^zone[0-9a-zA-Z]+$";
    public static final Pattern LOGICAL_ZONE_RESPONSE_PATTERN = Pattern.compile(API_LOGICAL_ZONE_RESPONSE_PATTERN);

    public static final String FWFT_RDSMYSQL8 = "RdsMysql8";
    public static final String FWFT_RDSSLOWQUERYWHITELIST = "RdsSlowqueryWhitelist";
    public static final String FWFT_RDSDISKMAXLIMITWHITELIST = "RdsDiskMaxLimitWhiteList";
    public static final String FWFT_RDSDMSWHITELIST = "RdsDmsWhitelist";
    public static final String FWFT_RDSREBASEWHITELIST = "RdsRebaseWhitelist";
    public static final String FWFT_RDSPOSTGRESQLWHITELIST = "RdsPostgresqlWhiteList";
    public static final String FWFT_RDSACCOUNTWHITELIST = "RdsAccountWhiteList";
    public static final String FWFT_RDSMONITORWHITELIST = "RdsMonitorWhiteList";
    public static final String FWFT_RDSSUMMARYWHITELIST = "RdsSummaryWhiteList";
    public static final String FWFT_RDSCHECKESSDWHITELIST = "RdscheckEssdWhiteList";
    public static final String FWFT_RDSSQLFLOWWHITELIST = "RdsSqlflowWhiteList";
    public static final String FWFT_RDSNEWDMSWHITELIST = "RdsNewDmsWhitelist";
    public static final String FWFT_RDSMAINTAINTIMEWHITELIST = "RdsMaintaintimeWhiteList";
    public static final String FWFT_RDSSTARTSTOPINSTANCEWHITELIST = "RdsStartStopInstanceWhiteList";
    public static final String FWFT_RDSINSTANCEPREPAYSHRINKAGE = "RdsInstancePrepayShrinkage";
    public static final String FWFT_RDSISBAIDUINTACCOUNT = "RdsIsBaiduIntAccount";
    public static final String FWFT_RDSLOCALDISK = "RdsIsEnableLocalDisk";
    public static final String FWFT_RDSMINORVERSION = "RdsMinorVersion";
    public static final String FWFT_RDSMASTERSLAVESWITCH = "RdsIsMasterSlaveSwitch";
    public static final String FWFT_RDSSERVICEPOINT = "RdsServicePoint";
    public static final String FWFT_MYSQLSSDTOESSD = "MysqlSsdToEssd";
    public static final String FWFT_HAREADREPLICA = "HaReadReplica";
    public static final String FWFT_SWITCHREBOOT = "SwitchReboot";
    public static final String FWFT_PG_SINGLETON = "PgSingleton";
    public static final String FWFT_PG_RESIZE_DISK = "PgResizeDisk";
    public static final String FWFT_PG_AUTO_EXPANSION = "PgAutoExpansion";
    public static final String FWFT_SUPPORT_GENERAL_INSTANCE = "SupportGeneralInstance";
    public static final String FWFT_TABLE_LEVEL_ACCOUNT_MANAGE = "TableLevelAccountManage";
    public static final String FWFT_PG_LOG_MANAGEMENT = "PgLogManagement";
    public static final String FWFT_PG_REBASE = "PgRebase";
    public static final String FWFT_PG_MASTER_SLAVE_SWITCH = "PgMasterSlaveSwitch";
    public static final String FWFT_CUSTOMED_PACKAGE_AND_STORAGE = "CustomizedPackageAndStorage";
    public static final String FWFT_SUPPORT_EPC = "SupportEpc";
    public static final String FWFT_MULTI_PROXY = "SupportMultiProxy";
    public static final String FWFT_SUPPORT_128C512G = "Support128C512G";
    public static final String FWFT_CPU_AUTO_RESIZE = "CPUAutoResize";
    public static final String FWFT_HONOR_POC = "honorPoc";



    public static final String[] FUNCTION_WHITELIST_FEATURE_TYPES = new String[]{FWFT_RDSMYSQL8,
            FWFT_RDSSLOWQUERYWHITELIST,
            FWFT_RDSDISKMAXLIMITWHITELIST,  FWFT_RDSDMSWHITELIST,  FWFT_RDSREBASEWHITELIST,
            FWFT_RDSPOSTGRESQLWHITELIST,
            FWFT_RDSACCOUNTWHITELIST,  FWFT_RDSMONITORWHITELIST,  FWFT_RDSSUMMARYWHITELIST,  FWFT_RDSCHECKESSDWHITELIST,
            FWFT_RDSSQLFLOWWHITELIST,  FWFT_RDSNEWDMSWHITELIST,  FWFT_RDSMAINTAINTIMEWHITELIST,
            FWFT_RDSSTARTSTOPINSTANCEWHITELIST,  FWFT_RDSINSTANCEPREPAYSHRINKAGE,  FWFT_RDSISBAIDUINTACCOUNT,
            FWFT_RDSLOCALDISK, FWFT_RDSMINORVERSION, FWFT_RDSMASTERSLAVESWITCH,
            FWFT_RDSSERVICEPOINT, FWFT_MYSQLSSDTOESSD, FWFT_HAREADREPLICA, FWFT_SWITCHREBOOT, FWFT_PG_SINGLETON,
            FWFT_PG_RESIZE_DISK, FWFT_PG_AUTO_EXPANSION, FWFT_SUPPORT_GENERAL_INSTANCE, FWFT_TABLE_LEVEL_ACCOUNT_MANAGE,
            FWFT_PG_LOG_MANAGEMENT, FWFT_PG_REBASE, FWFT_PG_MASTER_SLAVE_SWITCH, FWFT_CUSTOMED_PACKAGE_AND_STORAGE,
            FWFT_SUPPORT_EPC, FWFT_MULTI_PROXY, FWFT_SUPPORT_128C512G, FWFT_CPU_AUTO_RESIZE, FWFT_HONOR_POC};

    public void assignTags(AssignTagRequest request) {
        if (CollectionUtils.isEmpty(request.getResources())) {
            throw new BceException("resources for tags must not be null");
        }
        // 1. 只有主实例可以绑定标签，只读实例、代理实例不允许绑定标签；
        // 2. 主实例绑定标签时，其只读实例、代理实例同步绑定相同标签；
        // 3. 这么做的原因是多用户访问控制鉴权的逻辑里，支持了标签鉴权，且只读实例、代理实例不可以直接分配权限，其权限与主实例一致；
        Map<String, Instance> instanceMap = new HashMap<>();
        for (LogicalAssignResource resource : request.getResources()) {
            // 这里前端设置的 instanceId 是实例长 ID
            String instanceId = resource.getInstanceId();
            InstanceGetResponse instanceGetResponse = rdsClientFactory.createRdsClient2ByInstanceId(instanceId)
                    .instanceDescribe(instanceId);
            if (instanceGetResponse == null || instanceGetResponse.getInstance() == null) {
                throw new BceException(String.format("Instance ID(%s) is invalid.", instanceId));
            }
            if (instanceGetResponse.getInstance().getSourceInstanceId() != null
                    && !instanceGetResponse.getInstance().getSourceInstanceId().isEmpty()) {
                LOGGER.debug("instanceId = {}, instanceType = {}, sourceInstanceId = {}", instanceId,
                        instanceGetResponse.getInstance().getInstanceType(),
                        instanceGetResponse.getInstance().getSourceInstanceId());
                throw new BceException("Only the master instance can set tags.");
            }
            instanceMap.put(instanceId, instanceGetResponse.getInstance());
        }
        String currentRegion = regionConfiguration.getCurrentRegion();

        List<AssignResource> assignResources = new ArrayList<>();
        for (LogicalAssignResource requestResource : request.getResources()) {
            String instanceId = requestResource.getInstanceId();
            AssignResource assignResource = new AssignResource();
            assignResource.setResourceId(BasisUtils.isShortId(requestResource.getInstanceId())
                    ? requestResource.getInstanceId()
                    : instanceService.findShortIdByInstanceUUid(requestResource.getInstanceId()));
            assignResource.setResourceUuid(BasisUtils.isShortId(requestResource.getInstanceId())
                    ? instanceService.findInsntaceUUidByShortId(requestResource.getInstanceId())
                    : requestResource.getInstanceId());
            assignResource.setRegion(currentRegion);
            assignResource.setServiceType(ServiceType.RDS.name());
            assignResource.setTags(requestResource.getTags());
            assignResources.add(assignResource);

            Instance instance = instanceMap.get(instanceId);
            if (instance.getTopology() == null) {
                continue;
            }
            if (instance.getTopology().getReadReplica() != null) {
                for (String readReplicaInstanceId : instance.getTopology().getReadReplica()) {
                    assignResource = new AssignResource();
                    assignResource.setResourceId(instanceService.findShortIdByInstanceUUid(readReplicaInstanceId));
                    assignResource.setResourceUuid(readReplicaInstanceId);
                    assignResource.setRegion(currentRegion);
                    assignResource.setServiceType(ServiceType.RDS_REPLICA.name());
                    assignResource.setTags(requestResource.getTags());
                    assignResources.add(assignResource);
                }
            }
            if (instance.getTopology().getRdsproxy() != null) {
                for (String proxyInstanceId : instance.getTopology().getRdsproxy()) {
                    assignResource = new AssignResource();
                    assignResource.setResourceId(instanceService.findShortIdByInstanceUUid(proxyInstanceId));
                    assignResource.setResourceUuid(proxyInstanceId);
                    assignResource.setRegion(currentRegion);
                    assignResource.setServiceType(ServiceType.RDS_PROXY.name());
                    assignResource.setTags(requestResource.getTags());
                    assignResources.add(assignResource);
                }
            }
        }

        LogicalTagClient tagClient = rdsClientFactory.createLogicalTagClient();
        CreateAndAssignTagRequest tagRequest = new CreateAndAssignTagRequest();
        tagRequest.setResources(assignResources);
        tagClient.createAndAssignTag(tagRequest);
    }

    @Deprecated
    public void deleteTags(AssignTagRequest request) {

        if (CollectionUtils.isEmpty(request.getResources())) {
            throw new BceException("resources for tags must not be null");
        }
        String currentRegion = regionConfiguration.getCurrentRegion();

        List<AssignResource> assignResources = new ArrayList<>();
        for (LogicalAssignResource requestResource : request.getResources()) {
            AssignResource assignResource = new AssignResource();
            assignResource.setResourceId(requestResource.getInstanceId());
            assignResource.setResourceUuid(requestResource.getInstanceId());
            assignResource.setRegion(currentRegion);
            assignResource.setServiceType(requestResource.getServiceType() == null ? ServiceType.RDS.name() :
                    requestResource.getServiceType());
            assignResource.setTags(requestResource.getTags());
            assignResources.add(assignResource);
        }

        LogicalTagClient tagClient = rdsClientFactory.createLogicalTagClient();
        DeleteTagAssociationRequest tagRequest = new DeleteTagAssociationRequest();

        for (AssignResource each : assignResources) {

            com.baidu.bce.logical.tag.sdk.model.Resource resource
                    = new com.baidu.bce.logical.tag.sdk.model.Resource();

            resource.setRegion(currentRegion);
            resource.setResourceId(each.getResourceId());
            resource.setResourceUuid(each.getResourceUuid());
            resource.setServiceType(each.getServiceType());

            tagRequest.setResource(resource);
            tagClient.deleteTagAssociation(tagRequest);
        }

    }

    @Deprecated
    public void deltaTags(AssignTagRequest request) {
        LogicalTagClient tagClient = rdsClientFactory.createLogicalTagClient();
        DeltaAssignTagRequest deltaAssignTagRequest = new DeltaAssignTagRequest();

        if (CollectionUtils.isEmpty(request.getResources())) {
            throw new BceException("resources for tags must not be null");
        }
        String currentRegion = regionConfiguration.getCurrentRegion();

        List<AssignResource> assignResources = new ArrayList<>();
        for (LogicalAssignResource requestResource : request.getResources()) {
            AssignResource assignResource = new AssignResource();
            assignResource.setResourceId(requestResource.getInstanceId());
            assignResource.setResourceUuid(requestResource.getInstanceId());
            assignResource.setRegion(currentRegion);
            assignResource.setServiceType(requestResource.getServiceType() == null ? ServiceType.RDS.name() :
                    requestResource.getServiceType());
            assignResource.setTags(requestResource.getTags());
            assignResources.add(assignResource);
        }



//        deltaAssignTagRequest.setInsertTags();
        tagClient.deltaAssignTags(deltaAssignTagRequest);
    }

    /**
     * 设置实例标签
     *
     * @param region 区域
     * @param serviceType serviceType
     * @param accountId 账户 ID
     * @param instanceIdTagsMap Map<RDS 实例长ID，标签列表>
     */
    public void createAndAssignTag(String region, String serviceType, String accountId, Map<String,
            List<Tag>> instanceIdTagsMap, Map<String, String> instanceLongId2ShortIdMaps) {
        LOGGER.debug("region = {}, accountId = {}, instanceIdTagsMap = {}", region, accountId, instanceIdTagsMap);

        if (StringUtils.isEmpty(region) || StringUtils.isEmpty(accountId) || MapUtils.isEmpty(instanceIdTagsMap)) {
            return;
        }

        LogicalTagClient logicalTagClient = rdsClientFactory.createLogicalTagClient(accountId);
        CreateAndAssignTagRequest createAndAssignTagRequest = new CreateAndAssignTagRequest();
        createAndAssignTagRequest.setResources(new ArrayList<AssignResource>());
        for (Map.Entry<String, List<Tag>> entry : instanceIdTagsMap.entrySet()) {
            String instanceId = entry.getKey();
            List<Tag> tags = entry.getValue();

            AssignResource assignResource = new AssignResource();
            assignResource.setRegion(region);
            // RDS 只有主实例支持绑定标签，只读实例、代理实例不允许自由绑定标签。所以这里 ServiceType 只能为 ServiceType.RDS，而不能为
            // ServiceType.RDS_REPLICA 或 ServiceType.RDS_PROXY。
            assignResource.setServiceType(serviceType);
            // instanceId 为 RDS 实例长 ID
            assignResource.setResourceId(instanceLongId2ShortIdMaps.get(instanceId));
            assignResource.setResourceUuid(instanceId);
            assignResource.setTags(tags);
            createAndAssignTagRequest.getResources().add(assignResource);
        }

        logicalTagClient.createAndAssignTag(createAndAssignTagRequest);
    }

    /**
     * 查询实例标签
     *
     * @param region 区域
     * @param accountId 账户ID
     * @param instanceId RDS实例长ID
     *
     * @return 标签列表
     */
    public List<Tag> queryTagsByInstanceId(String region, String accountId, String instanceId) {
        LOGGER.debug("region = {}, accountId = {}, instanceId = {}", region, accountId, instanceId);

        if (StringUtils.isEmpty(region) || StringUtils.isEmpty(accountId) || StringUtils.isEmpty(instanceId)) {
            return null;
        }

        Map<String, List<Tag>> instanceIdTagsMap = queryTagsByInstanceIds(region, accountId,
                Collections.singletonList(instanceId));
        if (instanceIdTagsMap.containsKey(instanceId)) {
            return instanceIdTagsMap.get(instanceId);
        }
        return null;
    }

    /**
     * 查询实例标签
     *
     * @param region 区域
     * @param accountId 账户ID
     * @param instanceIds RDS实例长ID列表
     *
     * @return Map<RDS实例长ID,标签列表>
     */
    public Map<String, List<Tag>> queryTagsByInstanceIds(String region, String accountId, List<String> instanceIds) {
        LOGGER.debug("region = {}, accountId = {}, instanceIds = {}", region, accountId, instanceIds);

        if (StringUtils.isEmpty(region) || StringUtils.isEmpty(accountId) || CollectionUtils.isEmpty(instanceIds)) {
            return null;
        }

        FullTagListRequest fullTagListRequest = new FullTagListRequest();
        fullTagListRequest.setResourceUuids(instanceIds);
        LogicalTagClient logicalTagClient = rdsClientFactory.createLogicalTagClient(accountId);
        TagAssociationFulls tagAssociationFulls = logicalTagClient.listFullTags(fullTagListRequest);

        Map<String, List<Tag>> instanceIdTagsMap = new HashMap<>();
        for (TagAssociationFull tagAssociationFull : tagAssociationFulls.getTagAssociationFulls()) {
            String resourceUuid  = tagAssociationFull.getResourceUuid();
            Tag tag = new Tag();
            tag.setTagKey(tagAssociationFull.getTagKey());
            tag.setTagValue(tagAssociationFull.getTagValue());
            if (!instanceIdTagsMap.containsKey(resourceUuid)) {
                instanceIdTagsMap.put(resourceUuid, new ArrayList<Tag>());
            }
            instanceIdTagsMap.get(resourceUuid).add(tag);
        }

        return instanceIdTagsMap;
    }

    public WhiteAccountResult isWhiteAccount(String feature) {
        WhiteAccountResult whiteAccountResult = new WhiteAccountResult();

        // 新增白名单接口,减少请求数量，单个白名单请求
        if (StringUtils.isNotBlank(feature)) {
            HashMap<String, Boolean> featureMap = new HashMap<>(1);
            featureMap.put(feature, checkWhiteAccountFeature(feature));
            whiteAccountResult.setCustomizedWhiteAccount(featureMap);
            return whiteAccountResult;
        }

        // 控制能否创建mysql5.5只读实例，对应白名单中的RdsWhiteList
        // 参数下线
//        try {
//            GetAclNameListRequest request = new GetAclNameListRequest();
//            request.setScope("RdsWhiteList");
//            request.setType("AccountId");
//            request.setAclName(rdsClientFactory.getAccountId());
//            GetAclNameListResponse response = rdsClientFactory.createApiServiceClient().isInAclNameList(request);
//            whiteAccountResult.setMysqlReplica(response != null && response.getIsExist().booleanValue());
//        } catch (Exception e) {
//            LOGGER.warn("Query RdsWhiteList exception.", e);
//        }
//        // 控制能否创建sqlserver实例，对应白名单中的RdsSqlserverWhiteList
//        try {
//            GetAclNameListRequest request = new GetAclNameListRequest();
//            request.setScope("RdsSqlserverWhiteList");
//            request.setType("AccountId");
//            request.setAclName(rdsClientFactory.getAccountId());
//            GetAclNameListResponse response = rdsClientFactory.createApiServiceClient().isInAclNameList(request);
//            whiteAccountResult.setSqlserver(response != null && response.getIsExist().booleanValue());
//        } catch (Exception e) {
//            LOGGER.warn("Query RdsSqlserverWhiteList exception.", e);
//        }

        final String accountId = rdsClientFactory.getAccountId();
        final UserSettingsClient userSettingsClient = rdsClientFactory.createUserSettingsClient();
        Map<String, Future<Boolean>> futureMap = new HashMap<>(FUNCTION_WHITELIST_FEATURE_TYPES.length);
        for (final String featureType : FUNCTION_WHITELIST_FEATURE_TYPES) {
            Future<Boolean> featureTypeExistFuture = ThreadPool.submit(new Callable<Boolean>() {
                @Override
                public Boolean call() {
                    FeatureAclRequest aclRequest = new FeatureAclRequest(featureType,
                            regionConfiguration.getCurrentRegion(), "AccountId", accountId);
                    return userSettingsClient.isInFeatureAcl(aclRequest).getIsExist();
                }
            });
            futureMap.put(featureType, featureTypeExistFuture);
        }
        for (Map.Entry<String, Future<Boolean>> entry : futureMap.entrySet()) {
            String featureType = entry.getKey();
            Future<Boolean> future = entry.getValue();
            try {
                Boolean res = future.get();
                switch (featureType) {
                    case FWFT_RDSMYSQL8:
                        // 控制能否创建mysql8.0实例
                        whiteAccountResult.setMysql8(res);
                        break;
                    case FWFT_RDSSLOWQUERYWHITELIST:
                        // 控制有慢日志白名单，RdsSlowqueryWhitelist
                        whiteAccountResult.setSlowquery(res);
                        break;
                    case FWFT_RDSDISKMAXLIMITWHITELIST:
                        whiteAccountResult.setStorageAndMemory(res);
                        break;
                    case FWFT_RDSDMSWHITELIST:
                        // 控制有实例组白名单
                        whiteAccountResult.setDms(res);
                        break;
                    case FWFT_RDSREBASEWHITELIST:
                        // 控制有备份恢复白名单
                        whiteAccountResult.setRebase(res);
                        break;
                    case FWFT_RDSPOSTGRESQLWHITELIST:
                        // 控制能否创建postgresql 11 12 13实例，对应白名单中的RdsPostgresqlWhiteList
                        whiteAccountResult.setPostgresql(res);
                        break;
                    case FWFT_RDSACCOUNTWHITELIST:
                        // 控制能否支持细粒度的权限管理，对应白名单中的RdsAccountWhiteList
                        whiteAccountResult.setAccount(res);
                        break;
                    case FWFT_RDSMONITORWHITELIST:
                        // 监控白名单，对应白名单中的RdsMonitorWhiteList
                        whiteAccountResult.setMonitor(res);
                        break;
                    case FWFT_RDSSUMMARYWHITELIST:
                        // 慢日志统计报表
                        whiteAccountResult.setSummary(res);
                        break;
                    case FWFT_RDSCHECKESSDWHITELIST:
                        // 控制能否支持essd类型的磁盘
                        whiteAccountResult.setEssd(res);
                        break;
                    case FWFT_RDSSQLFLOWWHITELIST:
                        whiteAccountResult.setSqlflow(res);
                        break;
                    case FWFT_RDSNEWDMSWHITELIST:
                        whiteAccountResult.setNewDms(res);
                        break;
                    case FWFT_RDSMAINTAINTIMEWHITELIST:
                        whiteAccountResult.setMaintaintime(res);
                        break;
                    case FWFT_RDSSTARTSTOPINSTANCEWHITELIST:
                        // 控制启停实例白名单
                        whiteAccountResult.setStartStopInstance(res);
                        break;
                    case FWFT_RDSINSTANCEPREPAYSHRINKAGE:
                        // 预付费降配白名单
                        whiteAccountResult.setInstancePrepayShrinkage(res);
                        break;
                    case FWFT_RDSISBAIDUINTACCOUNT:
                        // 是否为集团云账号
                        whiteAccountResult.setIsBaiduIntAccount(res);
                        break;
                    case FWFT_RDSMINORVERSION:
                        // 小版本升级功能白名单
                        whiteAccountResult.setIsMinorVersion(res);
                        break;
                    case FWFT_RDSLOCALDISK:
                        // 内部使用本地盘账号
                        whiteAccountResult.setIsLocalDiskAccount(res);
                        break;
                    case FWFT_RDSMASTERSLAVESWITCH:
                        whiteAccountResult.setIsMasterSlaveSwitch(res);
                        break;
                    case FWFT_RDSSERVICEPOINT:
                        whiteAccountResult.setBlbServicePoint(res);
                        break;
                    case FWFT_MYSQLSSDTOESSD:
                        whiteAccountResult.setMysqlSsdToEssd(res);
                        break;
                    case FWFT_HAREADREPLICA:
                        whiteAccountResult.setHaReadReplica(res);
                        break;
                    case FWFT_SWITCHREBOOT:
                        whiteAccountResult.setSwitchReboot(res);
                        break;
                    case FWFT_PG_SINGLETON:
                        whiteAccountResult.setPgSingleton(res);
                        break;
                    case FWFT_PG_RESIZE_DISK:
                        whiteAccountResult.setPgResizeDisk(res);
                        break;
                    case FWFT_PG_AUTO_EXPANSION:
                        whiteAccountResult.setPgAutoExpansion(res);
                        break;
                    case FWFT_SUPPORT_GENERAL_INSTANCE:
                        whiteAccountResult.setSupportGeneralInstance(res);
                        break;
                    case FWFT_TABLE_LEVEL_ACCOUNT_MANAGE:
                        whiteAccountResult.setTableLevelAccountManage(res);
                        break;
                    case FWFT_PG_LOG_MANAGEMENT:
                        whiteAccountResult.setPgLogManagement(res);
                        break;
                    case FWFT_PG_REBASE:
                        whiteAccountResult.setPgRebase(res);
                        break;
                    case FWFT_PG_MASTER_SLAVE_SWITCH:
                        whiteAccountResult.setPgMasterSlaveSwitch(res);
                        break;
                    case FWFT_CUSTOMED_PACKAGE_AND_STORAGE:
                        whiteAccountResult.setCustomizedPackageAndStorage(res);
                        break;
                    case FWFT_SUPPORT_EPC:
                        whiteAccountResult.setSupportEpc(res);
                        break;
                    case FWFT_MULTI_PROXY:
                        whiteAccountResult.setSupportMultiProxy(res);
                        break;
                    case FWFT_SUPPORT_128C512G:
                        whiteAccountResult.setSupport128C512G(res);
                        break;
                    case FWFT_CPU_AUTO_RESIZE:
                        whiteAccountResult.setCpuAutoResize(res);
                        break;
                    case FWFT_HONOR_POC:
                        whiteAccountResult.setHonorPoc(res);
                        break;
                    default:
                        break;
                }
            } catch (ExecutionException | InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        return whiteAccountResult;
    }

    public GroupWhiteAccountResult isGroupWhiteAccount() {
        GroupWhiteAccountResult whiteAccountResult = new GroupWhiteAccountResult();

        // 控制有慢日志白名单，RdsSlowqueryWhitelist
        whiteAccountResult.setInstanceGroup(checkInstanceGroup());

        return whiteAccountResult;
    }

    /**
     * 新加白名单统一用此方法检查是否加白
     * @param feature 新加白名单,应以Rds开头
     * @return
     */
    public Boolean checkWhiteAccountFeature(String feature) {
        if (StringUtils.isBlank(feature) || !feature.startsWith("Rds")) {
            return false;
        }
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest(feature,
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            LOGGER.warn("Query {} whiteList exception.", feature, e);
            throw e;
        }
    }


    public Boolean checkMysql8() {
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsMysql8",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            LOGGER.warn("Query RdsMysql8WhiteList exception.", e);
            throw e;
        }
    }

    public Boolean checkSlowquery() {
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsSlowqueryWhitelist",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            LOGGER.warn("Query RdsSlowqueryWhitelist exception.", e);
            throw e;
        }
    }

    public Boolean checkInstanceGroup() {
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsInstanceGroupWhitelist",
                    null, "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            LOGGER.warn("Query RdsInstanceGroupWhitelist exception.", e);
            throw e;
        }
    }

    public Boolean checkDms() {
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsDmsWhitelist",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            LOGGER.warn("Query RdsInstanceGroupWhitelist exception.", e);
            throw e;
        }
    }

    public Boolean checkNewDms() {
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsNewDmsWhitelist",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            LOGGER.warn("Query RdsInstanceGroupWhitelist exception.", e);
            throw e;
        }
    }

    public Boolean checkDiskLimit() {
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsDiskMaxLimitWhiteList",
                    null, "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    public Boolean checkRebase() {
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsRebaseWhitelist",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }
    public Boolean checkPostgresql(){
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsPostgresqlWhiteList",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    public Boolean checkAccount(){
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsAccountWhiteList",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    public Boolean checkMonitor(){
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsMonitorWhiteList",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    public Boolean checkSummary(){
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsSummaryWhiteList",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    public Boolean checkRdsAZONEBjddWhiteList() {
        FeatureAclRequest featureAclRequest = new FeatureAclRequest("RdsAZONEBjddWhiteList",
                regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
        return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(featureAclRequest).getIsExist();
    }

    public Boolean checkEssd(){
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdscheckEssdWhiteList",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    public Boolean checkSqlflow(){
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsSqlflowWhiteList",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    public Boolean checkMaintaintime(){
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsMaintaintimeWhiteList",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    public Boolean checkStartStopInstance(){
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsStartStopInstanceWhiteList",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    public Boolean checkInstancePrepayShrinkage(){
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsInstancePrepayShrinkage",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    public Boolean checkIsBaiduIntAccount(){
        try {
            FeatureAclRequest aclRequest = new FeatureAclRequest("RdsIsBaiduIntAccount",
                    regionConfiguration.getCurrentRegion(), "AccountId", rdsClientFactory.getAccountId());
            return rdsClientFactory.createUserSettingsClient().isInFeatureAcl(aclRequest).getIsExist();
        } catch (BceInternalResponseException e) {
            throw e;
        }
    }

    // 账号有权限的vpc list
    public List<VpcVo> vpcList() {
        return rdsClientFactory.createVpcClientByUserId().listSimpleVpcs();
    }

    public LogicPageResultResponse<AutoRenewDetail> listInstanceForAutoRenew(RdsListRequest listRequest) {
        LogicPageResultResponse<InstanceAbstract> list
                = instanceService.listInstanceWithPageByMultiKey(listRequest, null, Boolean.TRUE);
        LogicPageResultResponse<InstanceAbstract> list2 = new LogicPageResultResponse<>();
        // TODO console先上线，执行此断代码会报异常，先捕获异常内部消化
        try {
            if (ArrayUtils.contains(financialRegion, regionConfiguration.getCurrentRegion())) {
                listRequest.getFilterMap().put("instanceType", RDSConstant.INSTANCE_TYPE_RAFT);
                list2 = instanceService.listInstanceWithPageByMultiKey(listRequest, null, Boolean.TRUE);
            }
        } catch (Exception e) {
            LOGGER.error("Raft backend not online" + e.getMessage());
        }
        list.getResult().addAll(list2.getResult());
        list.setTotalCount(list.getTotalCount() + list2.getTotalCount());
        return convertToAutoRenewListResponse(list);
    }

    public LogicPageResultResponse<AutoRenewDetail>
    convertToAutoRenewListResponse(LogicPageResultResponse<InstanceAbstract> instanceResponse) {
        Collection<InstanceAbstract> dResponse = instanceResponse.getResult();
        Map<String, AutoRenewRule> rdsAutoRenew = getAutoRenews("rds");

        LogicPageResultResponse<AutoRenewDetail> response = new LogicPageResultResponse<>();
        response.setPageSize(instanceResponse.getPageSize());
        response.setPageNo(instanceResponse.getPageNo());
        response.setOrder(instanceResponse.getOrder());
        response.setOrderBy(instanceResponse.getOrderBy());
        response.setTotalCount(instanceResponse.getTotalCount());

        List<AutoRenewDetail> autoRenewList = new ArrayList<>();
        for (InstanceAbstract instance : dResponse) {
            AutoRenewDetail autoRenewDetail = new AutoRenewDetail();
            autoRenewDetail.setExpiredTime(instance.getInstanceExpireTime());
            autoRenewDetail.setName(instance.getInstanceName());
            autoRenewDetail.setRegion(regionConfiguration.getCurrentRegion());
            autoRenewDetail.setResourceUuid(instance.getResourceUuid());
            autoRenewDetail.setServiceId(instance.getInstanceId());
            autoRenewDetail.setShortId(instance.getInstanceShortId());
            autoRenewDetail.setServiceType("RDS");
            autoRenewDetail.setStatus(instance.getInstanceStatus());
            autoRenewDetail.setOrderStatus(instance.getOrderStatus());

            AutoRenewRule autoRenewRule = rdsAutoRenew.get(instance.getInstanceId());
            if (autoRenewRule != null) {
                autoRenewDetail.setUuid(autoRenewRule.getUuid());
                autoRenewDetail.setRenewTimeUnit(autoRenewRule.getRenewTimeUnit());
                autoRenewDetail.setRenewTime(autoRenewRule.getRenewTime());
            }
            autoRenewList.add(autoRenewDetail);
        }

        response.setResult(autoRenewList);
        return response;
    }

    public Map<String, String> generateFilters(RdsListRequest request) {
        Map<String, String> filterMap = new HashMap<String, String>();

        filterMap.put("productType", ProductPayType.PRE_PAY.toString());

        if (request.getResourceId() != null && !request.getResourceId().equalsIgnoreCase("")) {
            filterMap.put("instanceId", request.getResourceId());
        }
        if (request.getResourceName() != null && !request.getResourceName().equalsIgnoreCase("")) {
            filterMap.put("instanceName", request.getResourceName());
        }
        return filterMap;
    }

    public Map<String, AutoRenewRule> getAutoRenews(String serviceType) {
        AutoRenewClient autoRenewClient = rdsClientFactory.createAutoRenewClient();
        ListAutoRenewRequest renewRequest = new ListAutoRenewRequest();
        renewRequest.setServiceType(serviceType);
        renewRequest.setBegin(0);
        renewRequest.setLimit(RESOURCE_NUM);
        renewRequest.setRegion(regionConfiguration.getCurrentRegion());
        AutoRenewRules autoRenewRules = autoRenewClient.getAutoRenewRules(renewRequest);
        Map<String, AutoRenewRule> cdsAutoRenew = new HashMap<>();
        for (AutoRenewRule renewRule : autoRenewRules) {
            cdsAutoRenew.put(renewRule.getServiceId(), renewRule);
        }
        return cdsAutoRenew;
    }

    /**
     * 获取可用区，包括单可用区和多可用区，及对应机房资源情况
     * @return
     */
    public ZoneDetailList zoneList(String from) {
        UsedZoneList usedZoneList = rdsClientFactory.createZoneClient().listOrInitZone();
        List<ZoneMapDetail> zoneMapDetails = usedZoneList.getZoneMapDetailList();
        String accountId = LogicUserService.getAccountId();
        ZoneDetailList zoneDetailList = new ZoneDetailList();
         // 获取单可用区
        for (ZoneMapDetail zoneMapDetail : zoneMapDetails) {
            ZoneDetailList.ZoneDetail zoneDetail = new ZoneDetailList.ZoneDetail();
            zoneDetail.getZoneNames().add(zoneMapDetail.getLogicalZone());
            zoneDetail.getApiZoneNames().add(logicalZoneTolApiZone(zoneMapDetail.getLogicalZone()));
            zoneDetail.setZoneNameStr(zoneMapDetail.getLogicalZone());
            zoneDetail.setDefaultSubnetId("");

            // 临时，只有bjrs支持高性能磁盘，全面支持后去掉
            if (StringUtils.isNotBlank(zoneMapDetail.getPhysicalZone())) {
                if (ObjectUtils.equals(zoneMapDetail.getPhysicalZone(), "AZONE-bjrs")) {
                    zoneDetail.setSupportNvmeDisk(Boolean.TRUE);
                }
            }

            Map<String, ZoneFlavorLimitPO> flavorLimitPOMap = getFlavorLimitPOMap();
            ZoneFlavorLimitPO flavorLimitPO = flavorLimitPOMap.get(zoneMapDetail.getPhysicalZone());
            if (flavorLimitPO != null) {
                zoneDetail.setMaxCpuCount(flavorLimitPO.getMaxCpuCount());
                zoneDetail.setMaxMemory(flavorLimitPO.getMaxMemoryCapacity());
                zoneDetail.setMaxStorage(flavorLimitPO.getMaxVolumeCapacity());
            }

            String[] physicalZones = unavailablePhysicalZone.split(",");
            List<String> suWhiteZones = Arrays.asList(suWhiteListZone.split(","));
            List<String> suWhiteAccounts = Arrays.asList(suWhiteListAccount.split(","));
            List<String> specialZones = Arrays.asList(specialPhysicalZone.split(","));
            if (!suWhiteAccounts.contains(accountId)) {
                for (String physicalZone : physicalZones) {
                    if (physicalZone.equalsIgnoreCase(zoneMapDetail.getPhysicalZone())) {
                        zoneDetail.setAvailable(false);
                        break;
                    }
                }
            } else {
                // 针对特殊账号需开白
                for (String physicalZone : physicalZones) {
                    if (physicalZone.equalsIgnoreCase(zoneMapDetail.getPhysicalZone())
                            && !suWhiteZones.contains(physicalZone)) {
                        zoneDetail.setAvailable(false);
                        break;
                    }
                }
            }

            if (specialZones.contains(zoneMapDetail.getPhysicalZone())) {
                zoneDetail.setZoneType(RDSConstant.SPECIAL_PHYSICAL_ZONE_LCC);
            }


            // 临时处理逻辑，硬编码
            if ("AZONE-gzhxy".equals(zoneMapDetail.getPhysicalZone())) {
                if ("e146680bb9ac40f288f62cb0cc674d2f".equals(LogicUserService.getAccountId())) {
                    zoneDetail.setAvailable(true);
                }
            }

            if (!RDSConstant.FROM_API.equals(from) || zoneDetail.isAvailable()) {
                zoneDetailList.getZones().add(zoneDetail);
            }
        }

        // 搭配多可用区
        int singleZoneCount = zoneDetailList.getZones().size();
        for (int i = 0 ; i < singleZoneCount - 1; i ++) {
            for (int j = i + 1 ; j < singleZoneCount; j ++) {
                ZoneDetailList.ZoneDetail zoneDetail = new ZoneDetailList.ZoneDetail();
                zoneDetail.getZoneNames().add(zoneDetailList.getZones().get(i).getZoneNames().get(0));
                zoneDetail.getApiZoneNames().add(zoneDetailList.getZones().get(i).getApiZoneNames().get(0));
                zoneDetail.getZoneNames().add(zoneDetailList.getZones().get(j).getZoneNames().get(0));
                zoneDetail.getApiZoneNames().add(zoneDetailList.getZones().get(j).getApiZoneNames().get(0));
                zoneDetail.setAvailable(zoneDetailList.getZones().get(i).isAvailable()
                        && zoneDetailList.getZones().get(j).isAvailable());

                zoneDetail.setMaxCpuCount(Math.min(zoneDetailList.getZones().get(i).getMaxCpuCount(),
                        zoneDetailList.getZones().get(j).getMaxCpuCount()));
                zoneDetail.setMaxMemory(Math.min(zoneDetailList.getZones().get(i).getMaxMemory(),
                        zoneDetailList.getZones().get(j).getMaxMemory()));
                zoneDetail.setMaxStorage(Math.min(zoneDetailList.getZones().get(i).getMaxStorage(),
                        zoneDetailList.getZones().get(j).getMaxStorage()));

                if (!RDSConstant.FROM_API.equals(from) || zoneDetail.isAvailable()) {
                    zoneDetailList.getZones().add(zoneDetail);
                }
            }
        }
        return  zoneDetailList;
    }

    public String apiZoneTologicalZone(String apiZone){
        if (StringUtils.isEmpty(apiZone)) {
            return null;
        }
        String[] zoneSplit = apiZone.split("-");
        if (zoneSplit.length != 3 || !zoneSplit[0].equals("cn")
                || !zoneSplit[1].equals(regionConfiguration.getCurrentRegion())
                || (!zoneSplit[2].equals("a") && !zoneSplit[2].equals("b") && !zoneSplit[2].equals("c")
                && !zoneSplit[2].equals("d") && !zoneSplit[2].equals("e") && !zoneSplit[2].equals("f")
                && !zoneSplit[2].equals("g") && !zoneSplit[2].equals("h") && !zoneSplit[2].equals("i"))) {
            throw new RDSExceptions.ParamValidationException("zoneName is not exist");
        } else {
            return "zone" + zoneSplit[2].toUpperCase();
        }
    }

    public String logicalZoneTolApiZone(String logicalZone) {
        if (null != logicalZone && LOGICAL_ZONE_RESPONSE_PATTERN.matcher(logicalZone).matches()) {
            StringBuilder apiZone = new StringBuilder("cn-").append(regionConfiguration.getCurrentRegion()).append("-");
            apiZone.append(logicalZone.substring(4).toLowerCase());
            return apiZone.toString();
        }
        return logicalZone;
    }

    public DiskIoTypeResponse getDiskIoTypeByZone(String zone) {
        DiskIoTypeResponse response = new DiskIoTypeResponse();
        List<String> diskIoType = new ArrayList<>();
        if (zone.contains("zoneG")) {
            UsedZoneList usedZoneList = rdsClientFactory.createZoneClient().listOrInitZone();
            List<ZoneMapDetail> zoneMapDetails = usedZoneList.getZoneMapDetailList();
            for (ZoneMapDetail zoneMapDetail : zoneMapDetails) {
                if (zoneMapDetail.getLogicalZone().equalsIgnoreCase("zoneG")
                        && zoneMapDetail.getPhysicalZone().equalsIgnoreCase("AZONE-sulcc02")) {
                    diskIoType.add("cloud_high");
                    diskIoType.add("cloud_nor");
                    diskIoType.add("cloud_enha");
                    response.setDiskIoType(diskIoType);
                    return response;
                }
            }
        }
        diskIoType.add("normal_io");
        diskIoType.add("cloud_high");
        diskIoType.add("cloud_nor");
        diskIoType.add("cloud_enha");
        response.setDiskIoType(diskIoType);
        return response;
    }

    /**
     * listSubnet接口支持vpc短ID，zoneA，不支持cn-bj-a的zone
     * @param zoneName
     * @param vpcId
     * @return
     */
    public List<SubnetVo> subnetList(String zoneName, String vpcId, String from) throws Exception {
        String azone = "";
        if (RDSConstant.FROM_API.equals(from)) {
            azone = apiZoneTologicalZone(zoneName);
        }
        ExternalSubnetClient subnetClient = rdsClientFactory.createSubnetClientByUserId();
        ListSubnetRequest subnetRequest  = new ListSubnetRequest();
        subnetRequest.setSubnetType(new Short("1")); // 1.BCC 2.BBC
        if (StringUtils.isNotEmpty(azone)) {
            subnetRequest.setZone(azone);
        }
        if (StringUtils.isNotEmpty(vpcId)) {
            subnetRequest.setVpcId(vpcId);
        }
        List<SubnetVo> subnetVos = subnetClient.listSubnet(subnetRequest);
        if (RDSConstant.FROM_API.equals(from) && subnetVos != null && subnetVos.size() > 0) {
            for (SubnetVo subnet : subnetVos) {
                subnet.setAz(logicalZoneTolApiZone(subnet.getAz()));
            }
        }
        return subnetVos;
    }

    /**
     * 查询剩余IP数量
     * @return
     */
    public SubnetVo subnetIpUsed(String subnetId) {

        ExternalSubnetClient subnetClient = rdsClientFactory.createSubnetClientByUserId();

        return subnetClient.findSubnetWithIpUsage(subnetId);
    }

    public Map<String, String> getIdMap(List<String> instanceUuids) {
        return instanceDao.getIdMapper(instanceUuids);
    }

    public Map<String, String> getIdMapV2(List<String> instanceUuids) {
        return instanceDao.getIdMapperV2(LogicUserService.getAccountId(),
                instanceUuids);
    }

    public Map<String, ZoneFlavorLimitPO> getFlavorLimitPOMap() {
        Map<String, ZoneFlavorLimitPO> flavorMap = new HashMap();
        List<ZoneFlavorLimitPO> flavorLimitPOS = flavorMapper.getZoneFlavorLimitPOList();
        for (ZoneFlavorLimitPO po : flavorLimitPOS) {
            flavorMap.put(po.getPhysicalZone(), po);
        }
        return flavorMap;
    }

    public Map<String, String> zoneMap() {
        UsedZoneList usedZoneList = rdsClientFactory.createZoneClient().listOrInitZone();
        List<ZoneMapDetail> zoneMapDetails = usedZoneList.getZoneMapDetailList();

        Map<String, String> map = new HashMap<>();
        for (ZoneMapDetail zoneMapDetail : zoneMapDetails) {
            map.put(zoneMapDetail.getLogicalZone(), zoneMapDetail.getPhysicalZone());
        }
        return map;
    }

    public ServiceParam getServiceParam() {
        ServiceParam result = new ServiceParam();
        result.setPolicyId(consoleRdsServicePolicyId);
        result.setRoleName("BceServiceRole_console_rds");
        result.setServiceId(consoleRdsServiceAccountId);
        return result;
    }

    public ServiceParam getServiceParamForRdsBackend() {
        ServiceParam result = new ServiceParam();
        result.setPolicyId(rdsServicePolicyId);
        result.setRoleName("RdsDefaultRole");
        result.setServiceId(rdsServiceAccountId);
        return result;
    }

    public Map<String,String> idMapperOnDcc(String hostId) {
        InstanceListResponse response = rdsClientFactory.createRdsClient().instanceListByDccId(hostId);
        List<String> longIds = new ArrayList<>();
        for (Instance instance : response.getInstances()) {
            longIds.add(instance.getInstanceId());
        }
        Map<String, String> idMap = getIdMap(longIds);
        return idMap;
    }

    public boolean checkInternalUser() {
        boolean isInternalUser = false;
        GetIsInternalUserResponse getIsInternalUserResponse = rdsClientFactory.createBccHanClient()
                .getIsInternalUser(LogicUserService.getAccountId());
        if (getIsInternalUserResponse.getInternalUser() != null) {
            isInternalUser = getIsInternalUserResponse.getInternalUser();
        }
        return isInternalUser;
    }

    public void sendGroupMessage(String userName) {
        LOGGER.debug("userName = {}", userName);
        if (userName == null && LogicUserService.getSubjectToken() != null) {
            userName = LogicUserService.getSubjectToken().getUserName();
        }
        String content = String.format("##### <font color=\"green\">%s</font> \n" +
                "用户accountID：%s \n" +
                "用户名：%s \n" +
                "用户userID：%s",
                "【RDS】新用户激活成功",
                LogicUserService.getAccountId(),
                userName,
                LogicUserService.getUserId()
                );
        GroupMessageItem groupMessageItem = new GroupMessageItem();
        groupMessageItem.setType("MD");
        groupMessageItem.setContent(content);
        SendGroupMsgRequest sendGroupMsgRequest = new SendGroupMsgRequest();
        sendGroupMsgRequest.setMessage(new GroupMessage());
        sendGroupMsgRequest.getMessage().setBody(new ArrayList<GroupMessageItem>(0));
        sendGroupMsgRequest.getMessage().getBody().add(groupMessageItem);
        rdsClientFactory.createMsgGroupClient().sendGroupMessage(sendGroupMsgRequest);
    }

    public String getInstnaceUuidByShortId(String instanceUuid) {
        return instanceDao.queryInstanceUuid(instanceUuid);
    }

    public List<ServiceParam> getRoleName() {
        List<ServiceParam> serviceParamList = new ArrayList<>();
        serviceParamList.add(getServiceParam());
        serviceParamList.add(getServiceParamForRdsBackend());
        return serviceParamList;
    }

    public GetLbdcClusterResponse getBlbLbdc(String name) {
        return rdsClientFactory.createBlbClient().getLbdc(name);
    }

    public ZoneListDetails getZoneConfiguration() {
        UsedZoneList usedZoneList = rdsClientFactory.createZoneClient().listOrInitZone();
        ZoneListDetails zoneListDetails = new ZoneListDetails();
        List<ZoneListDetails.ZoneMapDetail> zoneMapDetailList = new LinkedList();
        if (CollectionUtils.isNotEmpty(usedZoneList.getZoneMapDetailList())) {
            for (ZoneMapDetail zoneMapDetail : usedZoneList.getZoneMapDetailList()) {
                ZoneListDetails.ZoneMapDetail mapDetail = new ZoneListDetails.ZoneMapDetail();
                mapDetail.setZoneId(zoneMapDetail.getZoneId());
                mapDetail.setSubnetUuid("");
                mapDetail.setLogicalZone(zoneMapDetail.getLogicalZone());
                mapDetail.setType(zoneMapDetail.getType());
                Map<String, String> unavailableFunc = new HashMap<>();
                // supportLocalDisk标识当前可用区是否支持本地盘
                unavailableFunc.put("supportLocalDisk", "true");
                if (StringUtils.isNotEmpty(unavailableLocaldiskPhysicalZone)) {
                    if (unavailableLocaldiskPhysicalZone.contains(",")) {
                        List<String> localDiskLists = Arrays.asList(unavailableLocaldiskPhysicalZone.split(","));
                        if (localDiskLists.contains(zoneMapDetail.getPhysicalZone())) {
                            unavailableFunc.put("supportLocalDisk", "false");
                        } else {
                            unavailableFunc.put("supportLocalDisk", "true");
                        }
                    } else {
                        if (unavailableLocaldiskPhysicalZone.equalsIgnoreCase(zoneMapDetail.getPhysicalZone())) {
                            unavailableFunc.put("supportLocalDisk", "false");
                        } else {
                            unavailableFunc.put("supportLocalDisk", "true");
                        }
                    }
                }
                // supportGeneralInstance标识当前可用区是否支持通用型实例
                unavailableFunc.put("supportGeneralInstance", "false");
                if (StringUtils.isNotEmpty(availableGeneralInstance)) {
                    if (availableGeneralInstance.contains(",")) {
                        List<String> generalInstances = Arrays.asList(availableGeneralInstance.split(","));
                        if (generalInstances.contains(zoneMapDetail.getPhysicalZone())) {
                            unavailableFunc.put("supportGeneralInstance", "true");
                        } else {
                            unavailableFunc.put("supportGeneralInstance", "false");
                        }
                    } else {
                        if (availableGeneralInstance.equalsIgnoreCase(zoneMapDetail.getPhysicalZone())) {
                            unavailableFunc.put("supportGeneralInstance", "true");
                        } else {
                            unavailableFunc.put("supportGeneralInstance", "false");
                        }
                    }
                }
                mapDetail.setUnavailableFunc(unavailableFunc);
                zoneMapDetailList.add(mapDetail);
            }
        }
        zoneListDetails.setZoneMapDetailList(zoneMapDetailList);
        return zoneListDetails;
    }

    public void insertCustomMonitor(InsertCustomMonitorReq request) {
        CustomMonitorPO customMonitorPO = new CustomMonitorPO();
        if (StringUtils.isEmpty(request.getAccountId())) {
            request.setAccountId(LogicUserService.getAccountId());
        }
        // 数据映射
        customMonitorPO.setAccountId(request.getAccountId());
        customMonitorPO.setEngine(request.getEngine());
        // 此处用数据表中自动获取的时间即可
//        customMonitorPO.setCreateTime(Timestamp.valueOf(request.getCreateTime()));
//        customMonitorPO.setUpdateTime(Timestamp.valueOf(request.getUpdateTime()));
        customMonitorPO.setExtra(request.getExtra());

        // 数据入库
        customMonitorMapper.insertCustomMonitor(customMonitorPO);
    }

    public CustomMonitorPO getCustomMonitor(String accountId, String engine) {
        if (StringUtils.isEmpty(accountId)) {
            accountId = LogicUserService.getAccountId();
        }
       return customMonitorMapper.getCustomMonitor(accountId, engine);
    }

    public void updateCustomMonitor(InsertCustomMonitorReq customMonitorReq) {
        customMonitorMapper.updateCustomMonitor(customMonitorReq.getAccountId(),
                customMonitorReq.getEngine(), customMonitorReq.getExtra());
    }
}
