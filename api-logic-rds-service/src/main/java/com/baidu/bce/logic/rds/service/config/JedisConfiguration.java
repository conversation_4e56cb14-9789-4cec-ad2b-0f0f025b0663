/*
 * Copyright (C) 2020 Baidu, Inc. All Rights Reserved.
 */

package com.baidu.bce.logic.rds.service.config;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

@Configuration
public class JedisConfiguration {

    @Value("${jedis.maxActive:-1}")
    private int maxActive;

    @Value("${jedis.maxIdle:-1}")
    private int maxIdle;

    @Value("${jedis.maxWait:10}")
    private long maxWait;

    @Value("${jedis.testOnBorrow:true}")
    private boolean testOnBorrow;

    @Value("${jedis.host:127.0.0.1}")
    private String host;

    @Value("${jedis.port:6379}")
    private int port;

    @Value("${jedis.password:}")
    private String password;

    /**
     * Initialize one JedisPoolConfig
     */
    @Bean
    public JedisPoolConfig jedisPoolConfig() {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(maxActive);
        jedisPoolConfig.setMaxIdle(maxIdle);
        jedisPoolConfig.setMaxWaitMillis(maxWait);
        jedisPoolConfig.setTestOnBorrow(testOnBorrow);
        return jedisPoolConfig;
    }

    /**
     * Initialize one JedisPool
     */
    @Bean
    public JedisPool jedisPool(JedisPoolConfig jedisPoolConfig) {
        if (StringUtils.isEmpty(password)) {
            return new JedisPool(jedisPoolConfig, host, port);
        } else {
            return new JedisPool(jedisPoolConfig, host, port, 2000, password);
        }
    }
}