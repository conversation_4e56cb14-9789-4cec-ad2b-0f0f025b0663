/*
 * Copyright (C) 2020 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.service;

import com.baidu.bce.common.network.common.config.IConfigUserTokenService;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.iam.model.Token;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.plat.webframework.iam.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class RDSConfigUserTokenService implements IConfigUserTokenService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RDSConfigUserTokenService.class);

    @Override
    public String getRequestId() {
        return BceInternalRequest.getThreadRequestId();
    }

    @Override
    public Token getToken() {
        Token token = LogicUserService.getSubjectToken();
        UserService.setSubjectToken(token);
        return token;
    }

    @Override
    public String getLocale() {
        return null;
    }
}
