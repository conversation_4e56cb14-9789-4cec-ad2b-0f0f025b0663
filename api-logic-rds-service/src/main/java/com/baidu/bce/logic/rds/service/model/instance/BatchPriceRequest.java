package com.baidu.bce.logic.rds.service.model.instance;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BatchPriceRequest {

    @NotNull
    @Valid
    private List<BatchCreateInstance> instances;

    private Integer duration;

    @NotNull
    private String productType;

    @NotNull
    @Range(min = 1L, max = 10L, message = "一次最多购买10个rds实例！")
    private Integer number = 1;

    public List<BatchCreateInstance> getInstances() {
        return instances;
    }

    public void setInstances(List<BatchCreateInstance> instances) {
        this.instances = instances;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }


    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BatchCreateInstance {
        @NotNull
        private String engine;

        private String engineVersion;

        @Min(1)
        private Integer allocatedMemoryInGB;

        @Min(1024)
        private Integer allocatedMemoryInMB;

        private Integer allocatedStorageInGB;

        private String sourceInstanceId;


        private Boolean isEnhanced; // 上海金融专区三节点增强版标识

        // 单机版，临时用这个标识，鉴于有些部分本次改不动了，以后这些都要去掉
        private Boolean isSingle;

        private int cpuCount;

        private Integer nodeAmount;

        private String category; // 实例系列，单机双机三节点

        private String diskIoType;

        // 用于控制是否为高可用只读实例
        private String replicaType;

        private String instanceType;

        private String resourceType;


        public String getResourceType() {
            return resourceType;
        }

        public void setResourceType(String resourceType) {
            this.resourceType = resourceType;
        }

        public String getInstanceType() {
            return instanceType;
        }

        public void setInstanceType(String instanceType) {
            this.instanceType = instanceType;
        }

        public String getEngine() {
            return engine;
        }

        public void setEngine(String engine) {
            this.engine = engine;
        }

        public String getEngineVersion() {
            return engineVersion;
        }

        public void setEngineVersion(String engineVersion) {
            this.engineVersion = engineVersion;
        }

        public Integer getAllocatedMemoryInGB() {
            return allocatedMemoryInGB;
        }

        public void setAllocatedMemoryInGB(Integer allocatedMemoryInGB) {
            this.allocatedMemoryInGB = allocatedMemoryInGB;
        }

        public Integer getAllocatedMemoryInMB() {
            return allocatedMemoryInMB;
        }

        public void setAllocatedMemoryInMB(Integer allocatedMemoryInMB) {
            this.allocatedMemoryInMB = allocatedMemoryInMB;
        }

        public Integer getAllocatedStorageInGB() {
            return allocatedStorageInGB;
        }

        public void setAllocatedStorageInGB(Integer allocatedStorageInGB) {
            this.allocatedStorageInGB = allocatedStorageInGB;
        }

        public String getSourceInstanceId() {
            return sourceInstanceId;
        }

        public void setSourceInstanceId(String sourceInstanceId) {
            this.sourceInstanceId = sourceInstanceId;
        }


        public Boolean getIsEnhanced() {
            return isEnhanced;
        }

        public void setIsEnhanced(Boolean enhanced) {
            isEnhanced = enhanced;
        }

        public Boolean getIsSingle() {
            return isSingle;
        }

        public void setIsSingle(Boolean single) {
            isSingle = single;
        }

        public int getCpuCount() {
            return cpuCount;
        }

        public void setCpuCount(int cpuCount) {
            this.cpuCount = cpuCount;
        }

        public Integer getNodeAmount() {
            return nodeAmount;
        }

        public void setNodeAmount(Integer nodeAmount) {
            this.nodeAmount = nodeAmount;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }


        public String getDiskIoType() {
            return diskIoType;
        }

        public void setDiskIoType(String diskIoType) {
            this.diskIoType = diskIoType;
        }


        public String getReplicaType() {
            return replicaType;
        }

        public void setReplicaType(String replicaType) {
            this.replicaType = replicaType;
        }
    }
}
