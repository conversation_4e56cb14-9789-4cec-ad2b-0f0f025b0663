package com.baidu.bce.logic.rds.service.datasync.service;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.*;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.OrderNeedToSyncPO;
import com.baidu.bce.logic.rds.service.InstanceService;
import com.baidu.bce.logic.rds.service.constant.InstanceType;
import com.baidu.bce.logic.rds.service.datasync.model.ShortResourceVo;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.OrderEnvUtil;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.google.common.collect.Lists;
import endpoint.EndpointManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by luping03 on 18/1/9.
 */
public abstract class OrderSyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderSyncService.class);
    public static final String LOG_PREFIX = "[Logical Rds Data Synchronize]: ";

    @Autowired
    LogicRdsClientFactory clientFactory;

    @Autowired
    InstanceDao instanceDao;

    @Autowired
    OrderNeedToSyncService orderNeedToSyncService;

    @Autowired
    InstanceService instanceService;

    @Autowired
    RegionConfiguration regionConfiguration;

    public abstract int syncData(OrderNeedToSyncPO orderSyncPO);

    public String getServiceTypeByInstanceType(String instanceType) {
        if (InstanceType.INSTANCE_TYPE_MASTER.getValue().equalsIgnoreCase(instanceType)
                || instanceType.equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            return ServiceType.RDS.toString();
        } else if (InstanceType.INSTANCE_TYPE_PROXY.getValue().equalsIgnoreCase(instanceType)) {
            return ServiceType.RDS_PROXY.toString();
        } else if (InstanceType.INSTANCE_TYPE_REPLICA.getValue().equalsIgnoreCase(instanceType)) {
            return ServiceType.RDS_REPLICA.toString();
        } else {
            throw new RDSExceptions.ResourceServerException();
        }
    }
    public void updateInstance(Order instanceOrder,
                                ResourceClient resourceClient,
                                List<InstancePO> instancePOS) {
        LOGGER.debug("Ordersync update instance start: " + instanceOrder.getUuid());
        List<String> resourceIds = null; // = instanceOrder.getResourceIds();
        for (Order.Item item : instanceOrder.getItems()) {
            if (ServiceType.RDS.getName().equalsIgnoreCase(item.getServiceType())
                    || ServiceType.RDS_REPLICA.getName().equalsIgnoreCase(item.getServiceType())
                    || ServiceType.RDS_PROXY.getName().equalsIgnoreCase(item.getServiceType())) {
                resourceIds = item.getResourceIds();
                break;
            }
        }
        List<InstancePO> updateInstancePOs = new ArrayList<>();    // 存储需要更新的InstancePO

        // 判断订单里的env标记是哪个环境，调用对应环境的后端接口
        String env = OrderEnvUtil.getEnvFromNewOrder(instanceOrder);
        RDSClient2 rdsClient = StringUtils.isNotEmpty(env) ?
                clientFactory.createRdsClient2ByUserId(instanceOrder.getAccountId(), env) :
                clientFactory.createRdsClient2ByUserId(instanceOrder.getAccountId());

        List<ShortResourceVo> shortResourceVoList = Lists.newArrayList();

        for (int i = 0, size = resourceIds.size(); i < size; i++) {
            boolean isRaft = false;
            Resource resource = resourceClient.get(resourceIds.get(i));
            Instance instance = null;
            try {
                // instancePO中instanceType=financial时为raft版，走新版接口  (暂不处理线上测试环境raft实例）
                // if (instancePOS.get(i).getInstanceType().equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
                LOGGER.debug("Ordersync resource name :" + resource.getName());
                if (resource.getName() != null && resource.getName().length() == 12) { // raft的resourceName为短的12位
                    rdsClient = clientFactory.createRdsClient2ByUserId2(instanceOrder.getAccountId());
                }
                instance = rdsClient.instanceDescribe(resource.getName()).getInstance();
                LOGGER.debug("Ordersync get backend instance: " + instance);
                if (instance == null) {
                    LOGGER.error(LOG_PREFIX + "get instance is null, orderId is {}, instanceUuid is {}",
                            instanceOrder.getUuid(), resource.getName());
                    throw new RDSExceptions.ResourceNotExistException();
                }
            } catch (Exception e) {
                LOGGER.error(LOG_PREFIX
                                + "get instance is null, orderId is {}, instanceUuid is {}, exception is {},",
                        instanceOrder.getUuid(), resource.getName(), e);
                throw e;
            }

            // 先假设是raft，检索一遍，是否有匹配的(依据为后端返回id和数据库的inatnceId一样），有则进行下一轮，无则按照普通版随机匹配
            for (int j = 0; j < instancePOS.size(); j++) {
                LOGGER.debug("Raft orderSync count: " + j);
                if (instancePOS.get(j).getInstanceId().equals(instance.getInstanceId())) {
                    packageInstancePO(instancePOS.get(j), instance, resource);

                    updateInstancePOs.add(instancePOS.get(j));

                    ShortResourceVo shortResourceVo = new ShortResourceVo();
                    shortResourceVo.setShortId(instancePOS.get(j).getInstanceId());
                    shortResourceVo.setUuid(instancePOS.get(j).getInstanceUuid());
                    shortResourceVo.setRegion(EndpointManager.getThreadRegion());
                    shortResourceVo.setServiceType(
                            getServiceTypeByInstanceType(instancePOS.get(j).getInstanceType()));
                    shortResourceVoList.add(shortResourceVo);

                    instancePOS.remove(j); // 从列表中删除已经匹配上的PO
                    isRaft = true;
                    break;
                }
            }
            // 按照raft检索有对应的，则跳出循环，执行下一轮
            if (isRaft) {
                LOGGER.debug("Ordersync instance type is raft");
                continue;
            }

            // 为raft的假设不成立，则按照其他版本的随机匹配
            if (instancePOS != null && instancePOS.size() > 0
                    && instancePOS.get(0) != null) { // list的第一个元素一定有值
                LOGGER.debug("Normal orderSync instancePO: " + instancePOS.get(0));
                packageInstancePO(instancePOS.get(0), instance, resource);

                updateInstancePOs.add(instancePOS.get(0));

                ShortResourceVo shortResourceVo = new ShortResourceVo();
                shortResourceVo.setShortId(instancePOS.get(0).getInstanceId());
                shortResourceVo.setUuid(instancePOS.get(0).getResourceUuid());
                shortResourceVo.setRegion(EndpointManager.getThreadRegion());
                shortResourceVo.setServiceType(
                        getServiceTypeByInstanceType(instancePOS.get(0).getInstanceType()));
                shortResourceVoList.add(shortResourceVo);

                instancePOS.remove(0);
            }
        }

        if (updateInstancePOs.size() != resourceIds.size()) {
            LOGGER.warn(LOG_PREFIX + " orderId is {}, instancePOS is {}, vmResourceList is {}",
                    instanceOrder.getUuid(), instancePOS.size(), resourceIds.size());
            return;
        }

        // 更新本地数据库
        if (updateInstancePOs != null && updateInstancePOs.size() > 0) {
            instanceDao.batchUpdateInstanceById(updateInstancePOs);
            // 这块即将废弃，改为创建的时候就绑定短ID（订单执行逻辑）
//            bindResourceShortIds(instanceOrder.getUserId(), instanceOrder, shortResourceVoList);
            List<com.baidu.bce.plat.resource.manager.sdk.model.Resource> resourceList = new ArrayList<>();
            String serviceType = ServiceType.RDS.name();
            for (InstancePO updateInstancePO : updateInstancePOs) {
                com.baidu.bce.plat.resource.manager.sdk.model.Resource resource
                        = new com.baidu.bce.plat.resource.manager.sdk.model.Resource();
                resource.setAccountId(instanceOrder.getAccountId());
                resource.setUserId(instanceOrder.getUserId());
                resource.setName(updateInstancePO.getInstanceName());
                resource.setId(updateInstancePO.getInstanceId());
                resource.setUuid(updateInstancePO.getInstanceUuid());
                resource.setRegion(regionConfiguration.getCurrentRegion());
                serviceType = instanceService.getServiceTypeByInstanceType(updateInstancePO.getInstanceType());
                resource.setType(serviceType);
                resourceList.add(resource);
                LOGGER.debug("resource sync orderId is {}, instancePOS is {}",
                        instanceOrder.getUuid(), updateInstancePO.getInstanceUuid());
            }
            instanceService.syncInsertGroup(resourceList, "update", serviceType);
            LOGGER.debug("resource sync orderId is {}, instancePOS size {}",
                    instanceOrder.getUuid(), updateInstancePOs.size());
        }
        return;
    }

    /**
     * 绑定资源主要是添加shortId
     * @param userId
     * @param shortResourceVoList
     */
    public void bindResourceShortIds(String userId, Order order, List<ShortResourceVo> shortResourceVoList) {

        if (CollectionUtils.isEmpty(shortResourceVoList)) {
            return;
        }

        LOGGER.debug("bind Order Resource, userId is {}, shortResourceVoList size is {}",
                userId, shortResourceVoList.size());

        OrderClient orderClient = null;
        try {
            orderClient = clientFactory.getOrderClient();
        } catch (Exception e) {
            LOGGER.error("bind Order Resource get orderClient error userId is {},exception is {}", userId, e);
        }

        int count = 3;
        ResourceClient resourceClient = clientFactory.getResourceClient();

        for (ShortResourceVo shortResourceVo : shortResourceVoList) {
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            ResourceMapping sourceMapping = new ResourceMapping();
            Resource resource = resourceClient.get(shortResourceVo.getUuid());
            sourceMapping.setKey("RDS_123");
            sourceMapping.setId(resource.getName());
            sourceMapping.setShortId(shortResourceVo.getShortId());
            sourceMapping.setStatus(ResourceStatus.RUNNING);

            List<ResourceMapping> resourceMappings = new ArrayList<ResourceMapping>();
            resourceMappings.add(sourceMapping);
            updateOrderRequest.setResources(resourceMappings);

            while (count-- > 0) {
                try {
                    orderClient.update(order.getUuid(), updateOrderRequest);
                    break;
                } catch (BceInternalResponseException ex) {
                    LOGGER.warn("bind Order Resource error, retry {}, userId is {},exception is {}", count,
                            userId, ex);
                }
            }
        }
    }

    /**
     * 组装InstancePO
     *
     * @param instancePO
     * @param instance
     * @param resource
     */
    private void packageInstancePO(InstancePO instancePO, Instance instance, Resource resource) {
        instancePO.setInstanceUuid(instance.getInstanceId());
        instancePO.setInstanceName(instance.getInstanceName());
        instancePO.setInstanceStatus(instance.getInstanceStatus());
        instancePO.setUsedStorage(instance.getUsedStorageInMB() / 1024.00);
        instancePO.setInstanceCreateTime(new Timestamp(instance.getInstanceCreateTime().getTime()));
        if (resource.getExpireTime() != null) {
            instancePO.setInstanceExpireTime(new Timestamp(resource.getExpireTime().getTime()));
        }
        instancePO.getEndpoint().setAddress(instance.getEndpoint().getAddress());
        instancePO.getEndpoint().setPort(instance.getEndpoint().getPort());
        instancePO.getEndpoint().setInetIp(instance.getEndpoint().getInetIp());
        instancePO.getEndpoint().setVnetIp(instance.getEndpoint().getVnetIp());
        instancePO.setReplicationType(instance.getReplicationType());
        instancePO.getBackupPolicy().setBackupFreeSpace(instance.getBackupPolicy().getFreeSpaceInGB());
        instancePO.setResourceUuid(resource.getUuid());
    }

    public void updateResizedInstance(Order instanceOrder, ResourceClient resourceClient, InstancePO instancePO) {
        List<String> resourceIds = null; // = instanceOrder.getResourceIds();
        String resourceId = null;
        for (Order.Item item : instanceOrder.getItems()) {
            if (ServiceType.RDS.getName().equalsIgnoreCase(item.getServiceType())
                    || ServiceType.RDS_REPLICA.getName().equalsIgnoreCase(item.getServiceType())
                    || ServiceType.RDS_PROXY.getName().equalsIgnoreCase(item.getServiceType())) {
                resourceIds = item.getResourceIds();
                break;
            }
        }
        if (resourceIds == null || resourceIds.isEmpty()) {
            return;
        } else {
            resourceId = resourceIds.get(0);
        }

        // 判断订单里的env标记是哪个环境，调用对应环境的后端接口
        String env = OrderEnvUtil.getEnvFromResizeOrder(instanceOrder);
        RDSClient2 rdsClient = StringUtils.isNotEmpty(env) ?
                clientFactory.createRdsClient2ByUserId(instanceOrder.getAccountId(), env) :
                clientFactory.createRdsClient2ByUserId(instanceOrder.getAccountId());

//        RDSClient2 rdsClient = clientFactory.createRdsClient2ByUserId(instanceOrder.getAccountId());
        List<ShortResourceVo> shortResourceVoList = Lists.newArrayList();

        Resource resource = resourceClient.get(resourceId);
        Instance instance = null;
        try {
            instance = rdsClient.instanceDescribe(resource.getName()).getInstance();
            if (instance == null) {
                LOGGER.error(LOG_PREFIX + "get instance is null, orderId is {}, instanceUuid is {}",
                        instanceOrder.getUuid(), resource.getName());
                throw new RDSExceptions.ResourceNotExistException();
            }
        } catch (Exception e) {
            LOGGER.error(LOG_PREFIX
                            + "get instance is null, orderId is {}, instanceUuid is {}, exception is {},",
                    instanceOrder.getUuid(), resource.getName(), e);
            throw e;
        }

        instancePO.setInstanceUuid(instance.getInstanceId());
        instancePO.setInstanceName(instance.getInstanceName());
        instancePO.setInstanceStatus(instance.getInstanceStatus());
        instancePO.setVolumeCapacity(instance.getAllocatedStorageInGB());
        instancePO.setResourceUuid(resource.getUuid());
        instancePO.setCpuCount(instance.getCpuCount());
        instancePO.setMemoryCapacity(instance.getAllocatedMemoryInMB() / 1024.00);
        instancePO.setNodeAmount(instance.getNodeAmount());
        instancePO.setOrderStatus("");

        // 更新本地数据库
        if (instancePO != null) {
            instanceDao.batchUpdateInstanceById(Arrays.asList(instancePO));
        }
        return;
    }
}
