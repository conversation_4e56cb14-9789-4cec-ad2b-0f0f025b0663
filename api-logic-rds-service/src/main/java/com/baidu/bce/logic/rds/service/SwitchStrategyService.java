package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.model.strategy.ExchangeStrategyResponse;
import com.baidu.bce.internalsdk.rds.model.strategy.SwitchPrecheckResponse;
import com.baidu.bce.internalsdk.rds.model.strategy.TaskStatusResponses;
import com.baidu.bce.internalsdk.rds.model.strategy.UpdateExchangeStrategyRequest;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SwitchStrategyService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SwitchStrategyService.class);

    @Autowired
    private LogicRdsClientFactory rdsClientFactory;



    public ExchangeStrategyResponse detail(String instanceId) {
        return rdsClientFactory.createRdsClient().detail(instanceId);

    }

    public void updateExchangeStrategy(String instanceId, UpdateExchangeStrategyRequest request) {

        rdsClientFactory.createRdsClient().updateExchangeStrategy(instanceId, request);

    }

    public SwitchPrecheckResponse strategyService(String instanceId) {
        return rdsClientFactory.createRdsClient().strategyService(instanceId);
    }

    public TaskStatusResponses taskStatus(Integer taskId) {
        return rdsClientFactory.createRdsClient().taskStatus(taskId);
    }
}