package com.baidu.bce.logic.rds.service;

import com.baidu.bce.externalsdk.logical.network.common.model.BooleanResponse;
import com.baidu.bce.externalsdk.logical.network.common.request.IDRequest;
import com.baidu.bce.externalsdk.logical.network.common.request.QuotaRequest;
import com.baidu.bce.externalsdk.logical.network.common.response.IntResponse;
import com.baidu.bce.externalsdk.logical.network.common.response.SimpleQuotaResponse;
import com.baidu.bce.externalsdk.logical.network.common.response.WhiteListResponse;
import com.baidu.bce.externalsdk.logical.network.common.utils.InternalClientUtil;
import com.baidu.bce.externalsdk.logical.network.common.utils.VpcClientConstant;
import com.baidu.bce.externalsdk.logical.network.vpc.model.InternalVpc;
import com.baidu.bce.externalsdk.logical.network.vpc.model.SimpleVpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.VpcVo;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.BelongSameVpcRequest;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.CreateVpcRequest;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.UpdateVpcRequest;
import com.baidu.bce.externalsdk.logical.network.vpc.model.request.VpcIdsRequest;
import com.baidu.bce.externalsdk.logical.network.vpc.model.response.SimpleVpcMapResponse;
import com.baidu.bce.externalsdk.logical.network.vpc.model.response.VpcCheckResponse;
import com.baidu.bce.externalsdk.logical.network.vpc.model.response.VpcsResponse;
import com.baidu.bce.internalsdk.core.BceClient;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.rds.model.security.PageSecurityResponse;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupBindRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupListResp;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupPageRequest;
import com.baidu.bce.internalsdk.rds.model.security.SecurityGroupUpdate;
import endpoint.EndpointManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VpcClient extends BceClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(VpcClient.class);

    public VpcClient(String accessKey, String secretKey) {
        super(EndpointManager.getEndpoint(VpcClientConstant.SERVICE_NAME), accessKey, secretKey);
    }

    @Deprecated
    public VpcClient(String endpoint, String accessKey, String secretKey, String token) {
        super(endpoint, accessKey, secretKey);
        this.token = token;
    }

    public BceInternalRequest createInternalRequest() {
        return createAuthorizedRequestWithSignedHeaders(Arrays.asList(BceConstant.HOST,
                BceConstant.X_BCE_DATE)).token(token);
    }

    public BceInternalRequest createAdvacedRequest() {
        return InternalClientUtil.createAdvancedInternalRequest(endpoint,
                accessKey, secretKey, token, 100 * 1000, 1, securityToken);
    }

    public static final String VPC_BASE_URL = "/v1/api/logical/vpc";

    public static final String VPC_BASE_URL_V2 = "/v1/api/logical/network";

    public static final String DEFAULT_VPC = "/vpc/default";

    public static final String VPC_NAME = "/vpc/name/{name}";

    public static final String INTERNAL_VPC = "/vpc/internal";

    public static final String VPCS = "/vpcs";
    public static final String SIMPLE_VPCS = "/simplevpcs";

    public static final String VPCS_MAP = "/vpcs/map";

    public static final String VPC = "/vpc";

    public static final String VPC_SAME = "/vpc/validateSameVpc";

    public static final String VPC_QUOTA = "/vpc/quota";

    public static final String WHITE_LIST = "/whiteList";

    public static final String COMMON_WHITE_LIST = "/commonWhiteList";

    public static final String COMMON_QUOTA = "/commonQuota";

    public static final String CAN_EDIT_DEFAULT_VPC = "/canEditDefaultVpc";

    public static final String SHORT_ID_MAP = "/short_id_map";

    public static final String LONG_ID_MAP = "/long_id_map";

    public static final String OPEN_RELAY = "/vpc/openRelay/{vpcId}";

    public static final String CHECK_SHUTDOWN_RELAY_VPC = "/vpc/check_shutdown_realy_vpc/{vpcId}";

    public static final String SHUTDOWN_RELAY = "/vpc/shutdownRelay/{vpcId}";

    public static final String VPC_SECRRITY_URL = "/api/logical/network/v1/security";

    @Deprecated
    private String token;

    /********************************************************************/

    public VpcVo getDefaultVpc() {
        List<VpcVo> vpcs = listSimpleVpcs();
        if (CollectionUtils.isEmpty(vpcs)) {
            LOGGER.info("vpcs is empty, mock one.");
            VpcVo vpc = new VpcVo();
            vpc.setName("");
            vpc.setVpcId("");
            vpc.setDefaultVpc(true);

            return vpc;
        }

        for (VpcVo vpc : vpcs) {
            if (vpc.isDefaultVpc()) {
                LOGGER.info("get default vpc from vpcs");
                return vpc;
            }
        }

        LOGGER.error("can't get default vpc from vpcs, return null");
        return null;
    }

    public VpcVo getAndCreateDefaultVpc() {
        VpcVo response = createAdvacedRequest().path(VpcClient.VPC_BASE_URL_V2 + DEFAULT_VPC).get(VpcVo.class);

        return response;
    }

    /**
     * 返回数据库有的vpc，没有的话，也不创建默认vpc
     *
     * @return
     */
    public List<VpcVo> listVpcs() {
        VpcsResponse response = createAdvacedRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + VPCS).get(VpcsResponse.class);
        return response.getVpcs();
    }

    /**
     * 返回数据库有的vpc，没有的话，也不创建默认vpc，带过滤条件
     *
     * @return
     */
    public List<VpcVo> listFilterVpcs(String order, String orderBy, String keyword, String keywordType,
                                      String subKeywordType) {
        BceInternalRequest request = createAdvacedRequest().path(VpcClient.VPC_BASE_URL_V2 + VPCS);
        if (StringUtils.isNotBlank(keywordType)) {
            request.queryParam("keyword", keyword);
            request.queryParam("subKeywordType", subKeywordType);
            request.queryParam("keywordType", keywordType);
        }
        if (StringUtils.isNotBlank(orderBy)) {
            request.queryParam("order", order);
            request.queryParam("orderBy", orderBy);
        }

        VpcsResponse response = request.get(VpcsResponse.class);

        return response.getVpcs();
    }

    /**
     * 返回数据库有的vpc，没有的话，创建默认vpc
     *
     * @return
     */
    public List<VpcVo> listSimpleVpcs() {
        VpcsResponse response = createAdvacedRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + SIMPLE_VPCS).get(VpcsResponse.class);

        return response.getVpcs();
    }

    /**
     * 更新 vpc
     *
     * @param request vpc
     * @return 更新后的vpc
     */
    public SimpleVpcVo update(UpdateVpcRequest request) {
        SimpleVpcVo response = createInternalRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + VPC)
                .put(Entity.json(request), SimpleVpcVo.class);

        return response;
    }

    /**
     * 批量删除vpc
     *
     * @param request 需要删除的vpc id
     */
    public void delete(VpcIdsRequest request) {
        createInternalRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + VPCS)
                .post(Entity.json(request));
    }

    /**
     * 创建vpc
     *
     * @param request 需要删除的vpc id
     */
    public SimpleVpcVo create(CreateVpcRequest request) {
        return createAdvacedRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + VPC)
                .post(Entity.json(request), SimpleVpcVo.class);
    }

    /**
     * 获取简单vpc的散列表，key是vpc id，value是vpc信息
     *
     * @param request vpc id
     */
    public Map<String, SimpleVpcVo> get(VpcIdsRequest request) {
        SimpleVpcMapResponse response = createInternalRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + VPCS_MAP)
                .postWithResponse(Entity.json(request)).getEntity(SimpleVpcMapResponse.class);
        return response.getVpcMap();
    }

    public List<VpcVo> findByName(String name) {
        VpcsResponse response = createInternalRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + "/vpc/name" + "/" + name)
                .get(VpcsResponse.class);
        return response.getVpcs();
    }

    public WhiteListResponse isInWhiteList() {
        return createInternalRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + WHITE_LIST)
                .get(WhiteListResponse.class);
    }

    public WhiteListResponse isInCommonWhiteList(IDRequest request) {
        return createInternalRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + COMMON_WHITE_LIST)
                .post(Entity.json(request), WhiteListResponse.class);
    }

    public IntResponse commonQuota(QuotaRequest request) {
        return createInternalRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + COMMON_QUOTA)
                .post(Entity.json(request), IntResponse.class);
    }

    public WhiteListResponse canEditDefaultVpc() {
        return createInternalRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + CAN_EDIT_DEFAULT_VPC)
                .get(WhiteListResponse.class);
    }

    public SimpleQuotaResponse getQuotaInfo() {
        return createInternalRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + VPC_QUOTA)
                .get(SimpleQuotaResponse.class);
    }

    public boolean validateBelongSameVpc(BelongSameVpcRequest request) {
        BooleanResponse response = createInternalRequest()
                .path(VpcClient.VPC_BASE_URL_V2 + VPC_SAME)
                .post(Entity.json(request), BooleanResponse.class);

        return response.getValue();
    }

    public InternalVpc getInternalVpc() {
        return createInternalRequest().path(VpcClient.VPC_BASE_URL_V2 + INTERNAL_VPC)
                .get(InternalVpc.class);
    }

    // 开启中继vpc
    public void openRelayVpc(String vpcId) {
        StringBuilder sb = new StringBuilder(VPC_BASE_URL_V2);
        sb.append("/vpc/openRelay/").append(vpcId);
        createInternalRequest().path(sb.toString()).put();
    }

    // 关闭中继vpc
    public void shutDownRelayVpc(String vpcId) {
        StringBuilder sb = new StringBuilder(VPC_BASE_URL_V2);
        sb.append("/vpc/shutdownRelay/").append(vpcId);
        createInternalRequest().path(sb.toString()).put();
    }

    // check 关闭中继vpc
    public VpcCheckResponse checkCanShutDownRealyVpc(String vpcId) {
        StringBuilder sb = new StringBuilder(VPC_BASE_URL_V2);
        sb.append("/vpc/check_shutdown_realy_vpc/").append(vpcId);
        return createInternalRequest().path(sb.toString()).get(VpcCheckResponse.class);
    }

//    public SecurityGroupResultResponse listSecurityGroup(String blbId, String scsLbType) {
//        Map<String, String> map = new HashMap<>();
//        map.put("instanceId", blbId);
//        map.put("instanceType", "SCS");
//        map.put("subInstanceType", scsLbType);
//        return createInternalRequest()
//                .path(VPC_SECRRITY_URL + "/" + "getSgListByInstanceId")
//                .post(Entity.json(map), SecurityGroupResultResponse.class);
//    }

    public PageSecurityResponse listSecurityGroupByVpc(SecurityGroupPageRequest groupPageRequest) {
        BceInternalRequest request = createInternalRequest()
                .path(VPC_SECRRITY_URL + "/" + "list");
        request.queryParam("pageNo", groupPageRequest.getPageNo());
        request.queryParam("pageSize", groupPageRequest.getPageSize());
        request.queryParam("vpcId", groupPageRequest.getVpcId());
        request.queryParam("orderBy", groupPageRequest.getOrderBy());
        request.queryParam("order", groupPageRequest.getOrder());
        return request.get(PageSecurityResponse.class);
    }

    public void unbindSecurityGroup(SecurityGroupUpdate securityGroupUpdate) {
        createInternalRequest()
                .path(VPC_SECRRITY_URL + "/" + "instance/batch/unbind")
                .post(Entity.json(securityGroupUpdate));
    }
//
//    public void unbindSecurityGroupApi(SecurityGroupUpdate securityGroupUpdate) {
//        createInternalRequest()
//                .path(VPC_SECRRITY_URL + "/" + "batch/unbind")
//                .post(Entity.json(securityGroupUpdate));
//    }
//
    public void bindSecurityGroup(SecurityGroupBindRequest securityGroupBindRequest) {
        createInternalRequest()
                .path(VPC_SECRRITY_URL + "/" + "append/bindInstance")
                .put(Entity.json(securityGroupBindRequest));
    }
//
//    public void updateSecurityGroup(SecurityGroupUpdate securityGroupUpdate) {
//        createInternalRequest()
//                .path(VPC_SECRRITY_URL + "/" + "instance/batch/bind")
//                .post(Entity.json(securityGroupUpdate));
//    }

    public SecurityGroupListResp listSecurityGroup(String blbId, String lbType) {
        Map<String, String> map = new HashMap<>();
        map.put("instanceId", blbId);
        map.put("instanceType", "RDS");
        map.put("subInstanceType", lbType);
        return createInternalRequest()
                .path(VPC_SECRRITY_URL + "/" + "getSgListByInstanceId")
                .post(Entity.json(map), SecurityGroupListResp.class);
    }

    public void updateSecurityGroup(SecurityGroupUpdate securityGroupUpdate) {
        createInternalRequest()
                .path(VPC_SECRRITY_URL + "/" + "instance/batch/bind")
                .post(Entity.json(securityGroupUpdate));
    }

}

