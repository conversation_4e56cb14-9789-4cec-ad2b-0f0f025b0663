package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.model.config.ConfigItem;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyHistoryResponse;
import com.baidu.bce.internalsdk.rds.model.config.ConfigModifyRequest;
import com.baidu.bce.logic.rds.service.model.LogicRdsListRequest;
import com.baidu.bce.logic.rds.service.model.argument.ConfigList;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * Created by luping03 on 17/10/12.
 */
@Service
public class ArgumentService {

    @Autowired
    LogicRdsClientFactory logicRdsClientFactory;

    @Autowired
    InstanceService instanceService;

    public ConfigList list(String instanceId, String keyword, String from) {
        instanceService.checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);
        ConfigItem.ConfigItemList configItems = logicRdsClientFactory.createRdsClient().getConfigItems(instanceId);
        ConfigList result = new ConfigList();

        if (!StringUtils.isEmpty(keyword)) {
            for (ConfigItem item : configItems) {
                if (item.getName().contains(keyword.toLowerCase())) {
                    result.getItems().add(item);
                }
            }
        } else {
            result.setItems(configItems);
        }
        if (RDSConstant.FROM_API.equalsIgnoreCase(from)) {
            for (ConfigItem item : configItems) {
                if (!StringUtils.isEmpty(item.getDescription()) && item.getDescription().contains("\n")) {
                    item.setDescription(item.getDescription().replaceAll("\\n", ""));
                }
                if (!StringUtils.isEmpty(item.getAttention()) && item.getAttention().contains("\n")) {
                    item.setAttention(item.getAttention().replaceAll("\\n", ""));
                }
            }
        }

        Collections.sort(result.getItems());
        result.setTotalCount(configItems.size());

        return result;
    }


    public void modify(String instanceId, ConfigModifyRequest request, String eTag) {
        instanceService.checkInstanceByStatusForBackend(instanceId, RDSConstant.INSTANCE_STATUS_WHITELIST);
        logicRdsClientFactory.createRdsClient().modifyConfigItem(instanceId, request, eTag);
    }

    public ConfigModifyHistoryResponse history(String instanceId) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        ConfigModifyHistoryResponse response
                = logicRdsClientFactory.createRdsClient().getConfigModifyHistory(instanceId);
        Collections.sort(response.getParameters());
        return response;
    }
}
