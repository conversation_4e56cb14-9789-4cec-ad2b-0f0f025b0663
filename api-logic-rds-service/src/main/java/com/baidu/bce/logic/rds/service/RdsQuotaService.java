/*
 * Copyright (C) 2015 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.user.settings.sdk.model.QuotaRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by xush<PERSON><PERSON><PERSON> on 2015/4/28.
 */
@Service
@Configuration
public class RdsQuotaService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RdsQuotaService.class);

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Value("${rds.master.instance.quota:10}")
    protected Integer masterInstanceQuota;

    @Value("${rds.replica.instance.quota:5}")
    protected Integer replicaInstanceQuota;

    public Integer getUserQuota(String userId, QuotaType quotaType) {
        try {
            QuotaRequest request = new QuotaRequest();
            request.setServiceType(ServiceType.RDS.toString().toLowerCase());
            request.setQuotaType(quotaType.toString());
            request.setUserType(QuotaRequest.UserType.AccountId);
            request.setUserValue(userId);
            return Integer.parseInt(clientFactory.createUserSettingsClient().getQuota(request).getQuota());
        } catch (Exception ex) {
            LOGGER.error("[QuotaService] Query quota fail.", ex);
            return (Integer) getDefaultQuota().get(quotaType.toString());
        }
    }

    private Map<String, Object> getDefaultQuota() {
        Map<String, Object> result = new HashMap<String, Object>();
        result.put(QuotaType.master.toString(), masterInstanceQuota);
        result.put(QuotaType.replica.toString(), replicaInstanceQuota);
        return result;
    }

    public enum QuotaType {
        master("master"),
        replica("readReplica"),
        allReplica("allReadReplica");

        private String desc;

        private QuotaType(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

}
