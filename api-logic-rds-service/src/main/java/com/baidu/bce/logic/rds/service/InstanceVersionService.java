package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.model.MajorVersionPrecheckResponse;
import com.baidu.bce.internalsdk.rds.model.PreCheckVersionRequest;
import com.baidu.bce.internalsdk.rds.model.RdsMinorVersionList;
import com.baidu.bce.internalsdk.rds.model.SourceCheckResponse;
import com.baidu.bce.internalsdk.rds.model.UpdateVersionRequest;
import com.baidu.bce.internalsdk.rds.model.UpgradeMajorVersion;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class InstanceVersionService {

    @Autowired
    private LogicRdsClientFactory clientFactory;



    public RdsMinorVersionList getVersionList(String instanceId) {
        RDSClient rdsClient = clientFactory.createRdsClient(RDSConstant.SERVICE_NAME_2);
        return rdsClient.getVersionList(instanceId);
    }

    public void updateVersion(String instanceId, UpdateVersionRequest request, String from) {
        // 检查当前实例类型是否为 mysql 提前拦截
        if (RDSConstant.FROM_API.equalsIgnoreCase(from) || BasisUtils.isShortId(instanceId)) {
            InstanceGetResponse instanceGetResponse = clientFactory.createRdsClient2ByInstanceId(instanceId)
                    .instanceDescribe(instanceId);
            if (RDSConstant.RDS_ENGINE_SQLSERVER.equals(instanceGetResponse.getInstance().getEngine())) {
                throw new RDSBusinessExceptions.InstanceEngineException();
            }
        }
        RDSClient rdsClient = clientFactory.createRdsClient();
        rdsClient.updateVersion(instanceId, request);
    }

    public void preCheckOfGroupVersion(PreCheckVersionRequest request) {
        RDSClient rdsClient = clientFactory.createRdsClient();
        rdsClient.preCheckOfGroupVersion(request);
    }

    public void upgradeMajorVerison(String instanceId, UpgradeMajorVersion request) {
        clientFactory.createRdsClient().upgradeMajorVerison(instanceId, request);
    }

    public MajorVersionPrecheckResponse precheckOfMajorVersion(String instanceId, String checkType) {
        return clientFactory.createRdsClient2().precheckOfMajorVersion(instanceId, checkType);
    }

    public SourceCheckResponse dtsSourceCheck(String instanceId) {
        return clientFactory.createRdsClient2().dtsSourceCheck(instanceId);
    }
}
