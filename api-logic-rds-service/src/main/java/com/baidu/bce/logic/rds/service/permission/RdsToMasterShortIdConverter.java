package com.baidu.bce.logic.rds.service.permission;

import com.baidu.bce.common.network.common.permission.IdConverter;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidubce.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by luping03 on 18/6/21.
 */
@Component
public class RdsToMasterShortIdConverter implements IdConverter {

    private static final Logger LOGGER = LoggerFactory.getLogger(RdsToMasterShortIdConverter.class);

    @Autowired
    private InstanceDao instanceDao;

    @Override
    public Map<String, Set<String>> convertIds(Map<String, Set<String>> instanceIdMap) {
        LOGGER.debug(String.format("instanceIdMap = %s", JsonUtils.toJsonString(instanceIdMap)));

        // 1. 取得所有的 id
        Set<String> instanceIds = new HashSet<>();
        for (String key : instanceIdMap.keySet()) {
            for (String instanceId : instanceIdMap.get(key)) {
                Collections.addAll(instanceIds, instanceId.split(","));
            }
        }
        LOGGER.debug(String.format("instanceIds = %s", JsonUtils.toJsonString(instanceIds)));
        // 2. 查询实例信息
        List<InstancePO> instancePOs = instanceDao.selectByIds(instanceIds);
        LOGGER.debug(String.format("instancePOs = %s", JsonUtils.toJsonString(instancePOs)));
        Map<String, InstancePO> instanceIdInstancePOMap = new HashMap<>(instancePOs.size());
        Map<String, InstancePO> instanceUuidInstancePOMap = new HashMap<>(instancePOs.size());
        for (InstancePO instancePO : instancePOs) {
            instanceIdInstancePOMap.put(instancePO.getInstanceId(), instancePO);
            instanceUuidInstancePOMap.put(instancePO.getInstanceUuid(), instancePO);
        }
        // 3. 查询主实例信息
        Set<String> masterInstanceIds = new HashSet<>();
        for (InstancePO instancePO : instancePOs) {
            if (instancePO.getSourceInstanceId() != null
                    && !instancePO.getSourceInstanceId().isEmpty()
                    && !instanceIdInstancePOMap.containsKey(instancePO.getSourceInstanceId())
            ) {
                masterInstanceIds.add(instancePO.getSourceInstanceId());
            }
        }
        LOGGER.debug(String.format("masterInstanceIds = %s", JsonUtils.toJsonString(masterInstanceIds)));
        if (masterInstanceIds.size() > 0) {
            List<InstancePO> masterInstancePOs = instanceDao.selectByIds(masterInstanceIds);
            for (InstancePO instancePO : masterInstancePOs) {
                instanceIdInstancePOMap.put(instancePO.getInstanceId(), instancePO);
                instanceUuidInstancePOMap.put(instancePO.getInstanceUuid(), instancePO);
            }
        }
        LOGGER.debug(String.format("instanceIdInstancePOMap = %s", JsonUtils.toJsonString(instanceIdInstancePOMap)));
        LOGGER.debug(String.format("instanceUuidInstancePOMap = %s",
                JsonUtils.toJsonString(instanceUuidInstancePOMap)));
        // 4. 修改入参 instanceIdMap，同时构造返回值
        Map<String, Set<String>> result = new HashMap<>();
        for (String key : instanceIdMap.keySet()) {
            Set<String> value = instanceIdMap.get(key);
            Set<String> newValues = new LinkedHashSet<>();

            result.put(key, new LinkedHashSet<String>());
            for (String id : value) {
                InstancePO instancePO = instanceIdInstancePOMap.get(id);
                if (instancePO == null) {
                    instancePO = instanceUuidInstancePOMap.get(id);
                }
                if (instancePO.getSourceInstanceId() != null && !instancePO.getSourceInstanceId().isEmpty()) {
                    instancePO = instanceIdInstancePOMap.get(instancePO.getSourceInstanceId());
                }

                if (BasisUtils.isLongId(id)) {
                    newValues.add(instancePO.getInstanceUuid());
                    result.get(key).add(instancePO.getInstanceId());
                } else {
                    newValues.add(instancePO.getInstanceId());
                    result.get(key).add(instancePO.getInstanceId() + "|A|A|" + instancePO.getInstanceUuid());
                }
            }

            instanceIdMap.put(key, newValues);
        }
        LOGGER.debug(String.format("instanceIdMap = %s", JsonUtils.toJsonString(instanceIdMap)));
        LOGGER.debug(String.format("result = %s", JsonUtils.toJsonString(result)));

        return result;
    }

}
