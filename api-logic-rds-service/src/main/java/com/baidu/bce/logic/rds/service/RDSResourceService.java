package com.baidu.bce.logic.rds.service;

import com.baidu.bce.billing.auditing.sdk.ResourceService;
import com.baidu.bce.billing.auditing.sdk.domain.ClearResult;
import com.baidu.bce.billing.auditing.sdk.domain.DeleteResult;
import com.baidu.bce.billing.auditing.sdk.domain.OperatorContext;
import com.baidu.bce.billing.auditing.sdk.domain.ResourceDetail;
import com.baidu.bce.billing.auditing.sdk.domain.ResourceInfo;
import com.baidu.bce.billing.auditing.sdk.domain.ResourceStatus;
import com.baidu.bce.billing.auditing.sdk.domain.StartResult;
import com.baidu.bce.billing.auditing.sdk.domain.StopResult;
import com.baidu.bce.billing.resourcemanager.service.ResourceQueryService;
import com.baidu.bce.billing.resourcemanager.service.request.ResourceQueryRequest;
import com.baidu.bce.fbi.common.PageResult;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.iam.model.Token;
import com.baidu.bce.internalsdk.order.model.Resources;
import com.baidu.bce.internalsdk.order.model.ServiceType;
import com.baidu.bce.internalsdk.rds.RDSClient;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.OrderItemExtraInfo;
import com.baidu.bce.logic.core.region.RegionConfiguration;
import com.baidu.bce.logic.core.user.LogicUserService;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.model.instance.AuditGetDetailResponse;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Billing 启停 API。
 * 1. Billing 文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/ugqEjHgBv4/4Gmld5Stw3/KaUJE1tvNmwKkU
 */
@Service
public class RDSResourceService implements ResourceService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RDSResourceService.class);

    private static final String BILLING_SERVICE_NAME = "billing";

    private static final String DEFAULT = "default";

    @Autowired
    private RegionConfiguration regionConfiguration;

    @Autowired
    private LogicRdsClientFactory clientFactory;

    @Autowired
    private InstanceService instanceService;

    @Autowired RecyclerService recyclerService;



    public String getEnv4Resource(String resourceId, String accountId, String serviceType) {
        Resources resources = null;
        OrderItemExtraInfo orderItemExtraInfo = null;
//        ResourceClient resourceClient = clientFactory.createResourceClient();
        PageResult<com.baidu.bce.billing.resourcemanager.model.ResourceDetail> resourceDetailPageResult = null;
        ResourceQueryService queryService = clientFactory.createResourceQueryService();
        Set<String> serviceTypes = new HashSet<>(
                Arrays.asList("RUNNING", "STOPPED", "CLEAR", "DESTROYED", "CLEARING", "DESTROYING", "DESTROYED"));
        try {
            ResourceQueryRequest resourcesRequest = new ResourceQueryRequest();
            resourcesRequest.setAccountId(accountId);
            resourcesRequest.setRegion(regionConfiguration.getCurrentRegion());
            resourcesRequest.setStatus(serviceTypes);
            resourcesRequest.setNameOrShortIds(Arrays.asList(resourceId));
            switch (serviceType.toLowerCase()) {
                case "rds":
                    resourcesRequest.setServiceType(ServiceType.RDS.toString());
                    resourceDetailPageResult
                            = queryService.queryResourceDetails(resourcesRequest);
                    break;
                case "rdsreplica":
                    resourcesRequest.setServiceType(ServiceType.RDS_REPLICA.toString());
                    resourceDetailPageResult
                            = queryService.queryResourceDetails(resourcesRequest);
                    break;
                case "rdsproxy":
                    resourcesRequest.setServiceType(ServiceType.RDS_PROXY.toString());
                    resourceDetailPageResult
                            = queryService.queryResourceDetails(resourcesRequest);
                    break;
                default:
                    LOGGER.warn("RDS get resource list fail, serviceType is not support!");
                    resourcesRequest.setServiceType(ServiceType.RDS.toString());
                    resourceDetailPageResult
                            = queryService.queryResourceDetails(resourcesRequest);
                    break;
            }

            if (resourceDetailPageResult == null || resourceDetailPageResult.getTotalCount() == 0) {
                LOGGER.warn("RDS get resource list fail, serviceType is not support!");
                return "";
            } else {
                for (com.baidu.bce.billing.resourcemanager.model.ResourceDetail resource
                        : resourceDetailPageResult.getResult()) {
                    if (resource.getName().equals(resourceId)) {
                        String conf = resource.getExtra();
                        ObjectMapper objectMapper = new ObjectMapper();
                        try {
                            orderItemExtraInfo = objectMapper.readValue(conf, OrderItemExtraInfo.class);
                        } catch (IOException e) {
                            LOGGER.error("get env error", e);
                            return "";
                        }
                    }
                }
            }
        } catch (Exception ex) {
            LOGGER.warn("RDS get resource list fail! {}", ex);
            return "";
//            throw new RDSExceptions.ResourceServerException();
        }

        String env = null;
        if (orderItemExtraInfo != null) {
            env = orderItemExtraInfo.getEnv();
        } else {
            LOGGER.warn("RDS get resource list fail, extra info is empty!");
            return "";
        }
        LOGGER.debug("rds resource env:" + env);
//        RDSClient rdsClient = StringUtils.isNotEmpty(env) ?
//                clientFactory.createRdsClientByAccountId(accountId, env) :
//                clientFactory.createRdsClientByAccountId(accountId);
//        RDSClient2 rdsClient2 = StringUtils.isNotEmpty(env) ?
//                clientFactory.createRdsClient2ByUserId(accountId, env) :
//                clientFactory.createRdsClient2ByUserId(accountId);
        return env;
    }
    @Override
    public ResourceDetail get(String accountId, String serviceType, String resourceId, String region) {
//        validateRequest(resourceId, serviceType, region);

        ResourceDetail detail = packResource(resourceId, "", ResourceStatus.RUNNING, serviceType, accountId,
                region);
        InstanceGetResponse instanceGetResponse = null;
        AuditGetDetailResponse detailResponse = new AuditGetDetailResponse();
        String env4Order = getEnv4Resource(resourceId, accountId, serviceType);
        RDSClient2 rdsClient2 = StringUtils.isNotEmpty(env4Order) ?
                clientFactory.createRdsClient2ByUserId(accountId, env4Order) :
                clientFactory.createRdsClient2ByUserId(accountId);
        try {
            instanceGetResponse = rdsClient2.instanceDescribe(resourceId);
        } catch (Exception e) {
            LOGGER.error("audit get instance detail err", e);
        }
        if (instanceGetResponse == null) {
            detail.setName(resourceId);
            detail.setStatus(ResourceStatus.DESTROYED);
            detailResponse.setRdsProxy(new ArrayList<String>());
            detailResponse.setReadReplica(new ArrayList<String>());
            detail.setExtra(detailResponse);
            return detail;
        }
        detail.setName(instanceGetResponse.getInstance().getInstanceId());
        if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getEipStatus())) {
            detailResponse.setEipStatus(instanceGetResponse.getInstance().getEipStatus());
        }
        if (CollectionUtils.isNotEmpty(instanceGetResponse.getInstance().getTopology().getReadReplica())) {
            detailResponse.setReadReplica(instanceGetResponse.getInstance().getTopology().getReadReplica());
        } else {
            detailResponse.setReadReplica(new ArrayList<String>());
        }
        if (CollectionUtils.isNotEmpty(instanceGetResponse.getInstance().getTopology().getRdsproxy())) {
            detailResponse.setRdsProxy(instanceGetResponse.getInstance().getTopology().getRdsproxy());
        } else {
            detailResponse.setRdsProxy(new ArrayList<String>());
        }
        detail.setExtra(detailResponse);
        if ("deleted".equalsIgnoreCase(instanceGetResponse.getInstance().getInstanceStatus())
             || "deleting".equalsIgnoreCase(instanceGetResponse.getInstance().getInstanceStatus())) {
            detail.setStatus(ResourceStatus.DESTROYED);
            return detail;
        }

        if ("isolated".equalsIgnoreCase(instanceGetResponse.getInstance().getInstanceStatus())) {
            detail.setStatus(ResourceStatus.STOPPED);
            return detail;
        }

        if ("flushing".equalsIgnoreCase(instanceGetResponse.getInstance().getInstanceStatus())) {
            detail.setStatus(ResourceStatus.CLEAR);
            return detail;
        }

        detail.setStatus(ResourceStatus.RUNNING);
        return detail;
    }

    @Override
    public StartResult start(ResourceInfo resourceInfo, OperatorContext context) {
        String resourceId = resourceInfo.getResourceId();
        String serviceType = resourceInfo.getService();
        String region = resourceInfo.getRegion();
        String accountId = resourceInfo.getAccountId();
        String instanceStatus = "available";
        InstanceGetResponse instanceGetResponse = null;
        String env4Order = getEnv4Resource(resourceId, accountId, serviceType);
        RDSClient rdsClient = StringUtils.isNotEmpty(env4Order) ?
                clientFactory.createRdsClientByAccountId(accountId, env4Order) :
                clientFactory.createRdsClientByAccountId(accountId);
        RDSClient2 rdsClient2 = StringUtils.isNotEmpty(env4Order) ?
                clientFactory.createRdsClient2ByUserId(accountId, env4Order) :
                clientFactory.createRdsClient2ByUserId(accountId);
        try {
             instanceGetResponse = rdsClient2.instanceDescribe(resourceId);
            instanceStatus = instanceGetResponse.getInstance().getInstanceStatus();
        } catch (BceInternalResponseException e) {
            LOGGER.error("audit get instance detail err..");
            if (StringUtils.isNotEmpty(e.getCode()) && "InstanceAlreadyDeleted".equalsIgnoreCase(e.getCode())) {
                instanceStatus = "deleted";
            }
        }
        if (!RDSConstant.RDS_STATUS_CAN_START.contains(instanceStatus)) {
            // 此时 console 主动下发的开机任务已到达后端 无需再次下发
            return StartResult.STARTED;
        }
        LOGGER.info("billing callback start(): " + resourceId);
//        validateRequest(resourceId, serviceType, region);
//        AuditStopRequest auditStopRequest = new AuditStopRequest();
//        auditStopRequest.setRegion(region);
//        auditStopRequest.setStatus(resourceInfo.getStatus().name());
//        auditStopRequest.setProductType(resourceInfo.getProductType().name());
//        auditStopRequest.setReason(context.getReason());
//        clientFactory.createRdsAuditClientByAccountId(accountId).startInstance(accountId,
//                resourceId, resourceInfo.getService(), auditStopRequest);

        // rds后端

        rdsClient.rebootInstance(resourceId);
        return StartResult.STARTED;
    }

    @Override
    public StopResult stop(ResourceInfo resourceInfo, OperatorContext context) {
        String resourceId = resourceInfo.getResourceId();
        String serviceType = resourceInfo.getService();
        String region = resourceInfo.getRegion();
        String accountId = resourceInfo.getAccountId();
        String instanceStatus = "available";
        InstanceGetResponse instanceGetResponse = null;
        String env4Order = getEnv4Resource(resourceId, accountId, serviceType);
        RDSClient rdsClient = StringUtils.isNotEmpty(env4Order) ?
                clientFactory.createRdsClientByAccountId(accountId, env4Order) :
                clientFactory.createRdsClientByAccountId(accountId);
        RDSClient2 rdsClient2 = StringUtils.isNotEmpty(env4Order) ?
                clientFactory.createRdsClient2ByUserId(accountId, env4Order) :
                clientFactory.createRdsClient2ByUserId(accountId);
        try {
            instanceGetResponse = rdsClient2.instanceDescribe(resourceId);
            instanceStatus = instanceGetResponse.getInstance().getInstanceStatus();
        } catch (BceInternalResponseException e) {
            LOGGER.error("audit get instance detail err..");
            if (StringUtils.isNotEmpty(e.getCode()) && "InstanceAlreadyDeleted".equalsIgnoreCase(e.getCode())) {
                instanceStatus = "deleted";
            }
        }
        if ("deleted".equalsIgnoreCase(instanceStatus)
                || "deleting".equalsIgnoreCase(instanceStatus)
                || "isolated".equalsIgnoreCase(instanceStatus)
                || "isolating".equalsIgnoreCase(instanceStatus)) {
            // 此时 console 主动下发的释放、删除任务已到达后端 无需再次下发
            return StopResult.STOPPED;
        }

        LOGGER.info("billing callback stop(): " + resourceId);
//        validateRequest(resourceId, serviceType, region);
//        // 注释原停服逻辑
//        AuditStopRequest auditStopRequest = new AuditStopRequest();
//        auditStopRequest.setRegion(region);
//        auditStopRequest.setStatus(resourceInfo.getStatus().name());
//        auditStopRequest.setProductType(resourceInfo.getProductType().name());
//        auditStopRequest.setReason(context.getReason());
//        clientFactory.createRdsAuditClientByAccountId(accountId).stopInstance(accountId,
//                resourceId, resourceInfo.getService(), auditStopRequest);
        // 统一释放接口 只允许主实例释放后保留7天 故此接口只有主实例才会使用
        if (instanceGetResponse != null) {
            if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getInstanceType()) &&
                    RDSConstant.INSTANCE_TYPE_MASTER.equals(instanceGetResponse.getInstance().getInstanceType())) {
                // 当前为主实例
                instanceService.lockInstance4Audit(instanceGetResponse.getInstance(), accountId, rdsClient);
                try {
                    // 释放后端实例资源
//                    RDSClient client = clientFactory.createRdsClientByAccountId(accountId);
                    rdsClient.stopSingleInstance(instanceGetResponse.getInstance().getInstanceId());
                } catch (BceInternalResponseException e) {
                    LOGGER.error("Delete master instance error, resource uuid:{}, showId:{}",
                            resourceId, instanceGetResponse.getInstance().getInstanceId());
                    if ("InternalFailure".equals(e.getCode())) {
                        throw new RDSExceptions.AvailableInstanceException();
                    }
                }
            } else {
                // 只读 代理 无需任何操作
                LOGGER.info("non master billing callback stop(): " + resourceId);
                return StopResult.STOPPED;
            }
        } else {
            LOGGER.error("instanceGetResponse.getInstance() is null, instance id is {}", resourceId);
            throw new RDSBusinessExceptions.InstanceDetailException(resourceId);
        }

        return StopResult.STOPPED;
    }

    @Override
    public DeleteResult delete(ResourceInfo resourceInfo, OperatorContext context) {
        String resourceId = resourceInfo.getResourceId();
        String serviceType = resourceInfo.getService();
        String region = resourceInfo.getRegion();
        String accountId = resourceInfo.getAccountId();
        String instanceStatus = "available";
        InstanceGetResponse instanceGetResponse = null;
        String env4Order = getEnv4Resource(resourceId, accountId, serviceType);
        RDSClient rdsClient = StringUtils.isNotEmpty(env4Order) ?
                clientFactory.createRdsClientByAccountId(accountId, env4Order) :
                clientFactory.createRdsClientByAccountId(accountId);
        RDSClient2 rdsClient2 = StringUtils.isNotEmpty(env4Order) ?
                clientFactory.createRdsClient2ByUserId(accountId, env4Order) :
                clientFactory.createRdsClient2ByUserId(accountId);
        try {
            instanceGetResponse = rdsClient2.instanceDescribe(resourceId);
            instanceStatus = instanceGetResponse.getInstance().getInstanceStatus();
        } catch (BceInternalResponseException e) {
            LOGGER.error("audit get instance detail err..");
            if (StringUtils.isNotEmpty(e.getCode()) && "InstanceAlreadyDeleted".equalsIgnoreCase(e.getCode())) {
                instanceStatus = "deleted";
            }
        }
        if ("deleted".equalsIgnoreCase(instanceStatus)
                || "deleting".equalsIgnoreCase(instanceStatus)) {
            // 此时 console 主动下发的释放、删除任务已到达后端 无需再次下发
            return DeleteResult.DELETED;
        }
        LOGGER.info("billing callback delete(): " + resourceId);
//        validateRequest(resourceId, serviceType, region);
//        AuditStopRequest auditStopRequest = new AuditStopRequest();
//        auditStopRequest.setRegion(region);
//        auditStopRequest.setStatus(resourceInfo.getStatus().name());
//        auditStopRequest.setProductType(resourceInfo.getProductType().name());
//        auditStopRequest.setReason(context.getReason());
//        clientFactory.createRdsAuditClientByAccountId(accountId).deleteInstance(accountId,
//                resourceId, resourceInfo.getService(), auditStopRequest);

        // 此处需区分处理 主、只读、代理
        if (instanceGetResponse != null) {
            if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getInstanceType()) &&
                    RDSConstant.INSTANCE_TYPE_MASTER.equals(instanceGetResponse.getInstance().getInstanceType())) {
                // 销毁时同步解绑安全组
                recyclerService.afterHandlerSecurityGroup(resourceId, accountId);
                // 当前为主实例
                rdsClient.deleteIsolatedInstance(resourceId);
            } else {
                List<String> nonMasterInstance = new ArrayList<>();
                nonMasterInstance.add(resourceId);
                // 当前为只读、代理实例
                instanceService.lockInstanceBatch2Audit(nonMasterInstance, accountId, rdsClient2);
                instanceService.deleteInstanceBatch2Audit(nonMasterInstance, accountId, rdsClient);
            }
        } else {
            LOGGER.error("instanceGetResponse.getInstance() is null, instance id is {}", resourceId);
            throw new RDSBusinessExceptions.InstanceDetailException(resourceId);
        }
        return DeleteResult.DELETED;
    }

    @Override
    public ClearResult clear(ResourceInfo resourceInfo, OperatorContext context) {
        String resourceId = resourceInfo.getResourceId();
        String serviceType = resourceInfo.getService();
        String region = resourceInfo.getRegion();
        String accountId = resourceInfo.getAccountId();
        LOGGER.info("billing callback clear(): " + resourceId);

//        validateRequest(resourceId, serviceType, region);

        return ClearResult.CLEAR;
    }

    /**
     * 校验请求来源 必须为 Billing
     *
     * @param resourceId 资源 ID
     * @param serviceType serviceType
     * @param region region
     */

    private void validateRequest(String resourceId, String serviceType, String region) {

        Token token = LogicUserService.getSubjectToken();
        String userDomainId = token.getUser().getDomain().getId();
        String userName = token.getUser().getName();

        LOGGER.info("operate rds with token Domain Id:{}, User Name:{}",
                userDomainId, userName);
        if (!DEFAULT.equals(userDomainId)
                || !BILLING_SERVICE_NAME.equals(userName)) {
            throw new RDSBusinessExceptions.InvalidAction();
        }
    }

    private ResourceDetail packResource(String volumeId, String volueName, ResourceStatus status,
                                        String serviceType, String accountId, String region) {
        ResourceDetail detail = new ResourceDetail();
        detail.setId(volumeId);
        detail.setName(volueName);
        detail.setStatus(status);
        detail.setService(serviceType);
        detail.setAccountId(accountId);
        detail.setRegion(region);
        return detail;
    }
}
