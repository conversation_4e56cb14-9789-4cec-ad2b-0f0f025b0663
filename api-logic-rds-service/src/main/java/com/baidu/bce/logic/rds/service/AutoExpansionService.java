package com.baidu.bce.logic.rds.service;


import com.baidu.bce.internalsdk.rds.model.AutoResizeConfigResponse;
import com.baidu.bce.internalsdk.rds.model.CpuAutoResizeConfigResp;
import com.baidu.bce.internalsdk.rds.model.CpuAutoResizeEnableResp;
import com.baidu.bce.internalsdk.rds.model.ModifyCpuAutoResizeReq;
import com.baidu.bce.internalsdk.rds.model.SupportEnabledDiskAutoResizeResponse;
import com.baidu.bce.internalsdk.rds.model.UpdateAutoExpansionConfigRequest;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AutoExpansionService {

    @Autowired
    private LogicRdsClientFactory clientFactory;

    public SupportEnabledDiskAutoResizeResponse supportAutoExpansion(String instanceId) {
        return clientFactory.createRdsClient2().supportAutoExpansion(instanceId);
    }

    public AutoResizeConfigResponse getAutoExpansionConfig(String instanceId) {
        return clientFactory.createRdsClient2().getAutoExpansionConfig(instanceId);
    }

    public void updateAutoExpansionConfig(String instanceId, String action, UpdateAutoExpansionConfigRequest request) {
        clientFactory.createRdsClient2().updateAutoExpansionConfig(instanceId, action, request);
    }

    public CpuAutoResizeEnableResp enableCpuAutoResize(String instanceId) {
       return clientFactory.createRdsClient2().enableCpuAutoResize(instanceId);
    }

    public CpuAutoResizeConfigResp getCpuAutoResize(String instanceId) {
        return clientFactory.createRdsClient2().getCpuAutoResize(instanceId);
    }

    public void modifyCpuAutoResize(String instanceId, String action, ModifyCpuAutoResizeReq request) {
        clientFactory.createRdsClient2().modifyCpuAutoResize(instanceId, action, request);
    }
}
