package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.StsRoleListRequest;
import com.baidu.bce.internalsdk.iam.model.StsRoles;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RdsIamService {

    @Autowired
    private LogicRdsClientFactory rdsClientFactory;

    /**
     * 若用户未激活服务角色，则激活用户服务角色；若用户已激活，则直接返回，避免重复激活服务角色而触发 IAM 告警
     *
     * @param roleName 服务角色
     * @param accountId 账号 ID
     * @param policyId 策略 ID
     * @param serviceId 服务号 ID
     */
    public void stsServiceRoleActivateWithCheck(String roleName, String accountId, String policyId, String serviceId) {
        IAMClient iamClient = rdsClientFactory.createIamClient();
        StsRoleListRequest stsRoleListRequest = new StsRoleListRequest()
                .withDomainId(accountId).withName(roleName);
        StsRoles stsRoles = iamClient.stsRoleList(stsRoleListRequest);
        if (stsRoles != null && CollectionUtils.isNotEmpty(stsRoles.getList())
                && roleName.equals(stsRoles.getList().get(0).getName())) {
            return;
        }

        iamClient.stsServiceRoleActivate(roleName, accountId, policyId, serviceId);
    }
}
