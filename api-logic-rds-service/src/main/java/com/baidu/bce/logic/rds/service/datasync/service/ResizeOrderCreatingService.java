package com.baidu.bce.logic.rds.service.datasync.service;

import com.baidu.bce.internalsdk.order.OrderClient;
import com.baidu.bce.internalsdk.order.ResourceClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.OrderNeedToSyncPO;
import com.baidu.bce.logic.rds.service.constant.InstanceTableColums;
import com.baidu.bce.logic.rds.service.constant.RdsInstanceStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by luping03 on 18/1/9.
 */
@Service
public class ResizeOrderCreatingService extends OrderSyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResizeOrderCreatingService.class);

    @Override
    public int syncData(OrderNeedToSyncPO orderSyncPO) {
        LOGGER.debug(LOG_PREFIX + "synchronize creating rds start.order id is {}", orderSyncPO.getOrderUuid());
        List<InstancePO> resizingInstanceList = instanceDao
                .getInstanceListByOrderUuid(orderSyncPO.getOrderUuid(), orderSyncPO.getUserId());
        if (resizingInstanceList == null || resizingInstanceList.isEmpty()) {
            return 0;
        }
        OrderClient orderClient = clientFactory.getOrderClient();
        ResourceClient resourceClient = clientFactory.getResourceClient();

        try {
            String orderId = orderSyncPO.getOrderUuid();
            if (StringUtils.isEmpty(orderId)) {
                return 0;
            }

            Order instanceOrder = null;
            try {
                instanceOrder = orderClient.get(orderId);
                if (instanceOrder == null) {
                    LOGGER.warn(LOG_PREFIX + "Query order return null, order id is {}", orderId);
                    return 0;
                }
            } catch (Exception e) {
                LOGGER.warn(LOG_PREFIX + "Query order catch exception, order id is {}", orderId);
                return 0;
            }

            OrderStatus orderStatus = instanceOrder.getStatus();
            LOGGER.debug("Query order is {}, order status is {}", orderId, orderStatus.name());
            // 变配失败，重置实例
            if (orderStatus == OrderStatus.CREATE_FAILED || orderStatus == OrderStatus.REFUND_SUCC
                    || orderStatus == OrderStatus.REFUND_FAILED || orderStatus == OrderStatus.EXPIRED
                    || orderStatus == OrderStatus.CANCELLED) {
                Map<String, Object> map = new HashMap<>();
                map.put(InstanceTableColums.instanceStatus, RdsInstanceStatus.AVAILABLE.getValue());
                map.put(InstanceTableColums.orderStatus, "");
                instanceDao.updateInstanceByInstanceUuid(map, resizingInstanceList.get(0).getInstanceUuid(),
                        clientFactory.getAccountId());
                orderNeedToSyncService.updateOrderStatus(orderSyncPO.withOrderStatus(orderStatus.name()));
            }
//            LOGGER.debug(LOG_PREFIX + "Query order is {}, order id is {}", instanceOrder, orderId);
            if (orderStatus == OrderStatus.CREATED) {
                try {
                    updateResizedInstance(instanceOrder, resourceClient, resizingInstanceList.get(0));
                    orderNeedToSyncService.updateOrderStatus(orderSyncPO.withOrderStatus(orderStatus.name()));
                } catch (Exception e) {
                    LOGGER.error(LOG_PREFIX + "sync in creating rds failed, order id is {}, exception is {}",
                            instanceOrder.getUuid(), e);
                    return 0;
                }
            }
        } catch (Exception e) {
            LOGGER.error("synchronize in creating rds failed, exception is {}", e);
            return 0;
        }
//        LOGGER.debug(LOG_PREFIX + "sync in creating rds end");
        return 1;
    }
}
