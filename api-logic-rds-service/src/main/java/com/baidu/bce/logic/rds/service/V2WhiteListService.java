package com.baidu.bce.logic.rds.service;


import com.baidu.bce.internalsdk.rds.model.security.SecurityIpPutRequest;
import com.baidu.bce.internalsdk.rds.model.security.V2ListRequest;
import com.baidu.bce.internalsdk.rds.model.security.V2SecurityIpResponse;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * Created by luping03 on 17/11/3.
 */
@Service
public class V2WhiteListService {

    public static final String IP = "(^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.)" +
            "{3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(/(\\d|[1-2]\\d|3[0-2]))?$)|(^%$)";
    public static final Pattern IPPATTERN = Pattern.compile(IP);

    @Autowired
    LogicRdsClientFactory rdsClientFactory;

    @Autowired
    InstanceService instanceService;

    public V2SecurityIpResponse getWhiteList(String instanceId, V2ListRequest requestBody) {
        return rdsClientFactory.createRdsClient2ByInstanceId(instanceId).v2QueryWhiteList(instanceId, requestBody);
    }

    public void setWhiteList(String instanceId, SecurityIpPutRequest request) {
        StringBuilder stringBuilder = new StringBuilder();
        for (String ip : request.getSecurityIps()) {
            if (!IPPATTERN.matcher(ip).find()) {
                stringBuilder.append(ip).append(" ");
            }
        }
        if (!stringBuilder.toString().equals("")) {
            throw new RDSExceptions.ParamValidationException(stringBuilder.toString());
        }
        // rdsClientFactory.createRdsClient().updateWhiteList(instanceId, request.getSecurityIps(), eTag);
        rdsClientFactory.createRdsClient2ByInstanceId(instanceId)
                .v2updateWhiteList(instanceId, request);
    }

    public void setWhiteListNew(String instanceId, SecurityIpPutRequest request) {
        rdsClientFactory.createRdsClient2ByInstanceId(instanceId)
                .updateWhiteListNew(instanceId, request);
    }

    public void deleteWhiteList(String instanceId, SecurityIpPutRequest request) {
        rdsClientFactory.createRdsClient2ByInstanceId(instanceId)
                .deleteWhiteList(instanceId, request);
    }
}
