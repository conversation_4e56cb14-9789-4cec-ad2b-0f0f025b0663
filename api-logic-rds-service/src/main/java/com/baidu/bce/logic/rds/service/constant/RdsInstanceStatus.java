package com.baidu.bce.logic.rds.service.constant;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by luping03 on 17/12/19.
 */
public enum RdsInstanceStatus {
    AVAILABLE("available"),
    DELETED("deleted"),
    FAILED("failed"),
    UNKNOWN("unknown"),
    CREATING("creating"),
    MODIFYING("modifying"),
    REBOOTING("rebooting"),
    ISOLATED("isolated");



    public static final Set<String> deletableStatus = new HashSet<String>() {
        {
            add(RdsInstanceStatus.AVAILABLE.getValue());
        }
    };

    public static final Set<String> recoverableStatus = new HashSet<String>() {
        {
            add(RdsInstanceStatus.ISOLATED.getValue());
            // add(RdsInstanceStatus.AVAILABLE.getValue());
        }
    };



    RdsInstanceStatus(String value) {
        this.value = value;
    }

//    public static List<String> orderInstanceStatus = Arrays.asList(CREATING.getValue(), RESIZING.getValue());

    private String value;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
