package com.baidu.bce.logic.rds.service;

import com.baidu.bce.internalsdk.core.BceFormat;
import com.baidu.bce.internalsdk.rds.RDSClient2;
import com.baidu.bce.internalsdk.rds.model.instance.Instance;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceGetResponse;
import com.baidu.bce.internalsdk.rds.model.instance.InstanceIdRequest;
import com.baidu.bce.internalsdk.rds.model.snapshot.BackupCrossRegionListResponses;
import com.baidu.bce.internalsdk.rds.model.snapshot.BackupTargetRegionResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.CopyRegionSnapshotDetail;
import com.baidu.bce.internalsdk.rds.model.snapshot.DashboardbackupListRequest;
import com.baidu.bce.internalsdk.rds.model.snapshot.ListAllCrossRegions;
import com.baidu.bce.internalsdk.rds.model.snapshot.Snapshot;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotGetResponse;
import com.baidu.bce.internalsdk.rds.model.snapshot.SnapshotListWithTimeResponse;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.exception.RDSExceptions;
import com.baidu.bce.logic.rds.service.util.LogicRdsClientFactory;
import com.baidu.bce.logic.rds.service.util.RDSConstant;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logic.core.result.LogicMarkerResultResponse;
import com.baidu.bce.logic.rds.dao.InstanceDao;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * Created by luping03 on 17/10/14.
 */
@Service
public class BackupService {
    private static final Logger logger = LoggerFactory.getLogger(InstanceService.class);

    @Autowired
    InstanceDao instanceDao;

    @Autowired
    LogicRdsClientFactory rdsClientFactory;

    @Autowired
    private InstanceService instanceService;

    public SnapshotListWithTimeResponse list(String instanceId) {
        SnapshotListWithTimeResponse snapshotListResponse =
                rdsClientFactory.createRdsClient2().snapshotList(instanceId);
        Collections.sort((ArrayList)snapshotListResponse.getSnapshots(), new Comparator<Snapshot>() {
            @Override
            public int compare(Snapshot o1, Snapshot o2) {
                return o2.getSnapshotStartTime().compareTo(o1.getSnapshotStartTime());
            }
        });
        return snapshotListResponse;
    }

    // raft版后端接口提供了按照时间顺序查询
    public SnapshotListWithTimeResponse list(String instanceId, String startDatetime, String stopDatetime) {
        // 校验时间是否合法
        dateCheck(startDatetime, stopDatetime);

        SnapshotListWithTimeResponse snapshotListResponse =
                rdsClientFactory.createRdsClient2(RDSConstant.SERVICE_NAME_V2)
                        .snapshotList(instanceId, startDatetime, stopDatetime);
        Collections.sort((ArrayList) snapshotListResponse.getSnapshots(), new Comparator<Snapshot>() {
            @Override
            public int compare(Snapshot o1, Snapshot o2) {
                return o2.getSnapshotStartTime().compareTo(o1.getSnapshotStartTime());
            }
        });
        return snapshotListResponse;
    }

    public SnapshotListWithTimeResponse list(DashboardbackupListRequest request) {
        SnapshotListWithTimeResponse snapshotListResponse =
                rdsClientFactory.createRdsClient2().snapshotList(request);
        Collections.sort((ArrayList) snapshotListResponse.getSnapshots(), new Comparator<Snapshot>() {
            @Override
            public int compare(Snapshot o1, Snapshot o2) {
                return o2.getSnapshotStartTime().compareTo(o1.getSnapshotStartTime());
            }
        });
        return snapshotListResponse;
    }

    public SnapshotGetResponse detail(String instanceId, String snapshotId, Integer downloadValidTimeInSec) {
//        InstancePO instancePO = instanceDao.querySimpleInstanceById(instanceId, rdsClientFactory.getUserId());

        return rdsClientFactory.createRdsClient2().snapshotGet(instanceId, snapshotId, downloadValidTimeInSec);
    }

    public void create(InstanceIdRequest request) {
        if (BasisUtils.isShortId(request.getInstanceId())) {
            request.setInstanceId(instanceService.findInsntaceUUidByShortId(request.getInstanceId()));
        }

        // 手动备份时 若使用快照备份，需限制下必须为云盘类型的实例
        Instance instance = rdsClientFactory.createRdsClient2ByInstanceId(request.getInstanceId())
                .instanceDescribe(request.getInstanceId()).getInstance();
        if (StringUtils.isNotEmpty(request.getDataBackupType())
                && "snapshot".equalsIgnoreCase(request.getDataBackupType())) {
            if (StringUtils.isNotEmpty(instance.getDiskType()) && "ssd".equalsIgnoreCase(instance.getDiskType())) {
                throw new RDSExceptions.BackupException();
            }
        }

        if (rdsClientFactory.getInstanceTypeByUuid(request.getInstanceId())
                .equalsIgnoreCase(RDSConstant.INSTANCE_TYPE_RAFT)) {
            rdsClientFactory.createRdsClient2(RDSConstant.SERVICE_NAME_V2).snapshotCreateRaft(request.getInstanceId());
        } else {
            rdsClientFactory.createRdsClient2().snapshotCreate(request);
        }
    }

    private void dateCheck(String startTime, String endTime) {
        Date startDate = null;
        Date stopDate = null;
        try {
            startDate = BceFormat.getDateTimeFormat().parse(startTime);
            stopDate = BceFormat.getDateTimeFormat().parse(endTime);
        } catch (ParseException e) {
            throw new RDSExceptions.DateFormatException();
        }
        // 时间范围检验
        if (startDate != null && startDate.after(new Date())) {
            throw new RDSExceptions.FutuerDateException();
        }
        if (stopDate != null && stopDate.after(new Date())) {
            throw new RDSExceptions.FutuerDateException();
        }
        if (startDate != null && stopDate != null
                && startDate.after(stopDate)) {
            throw new RDSExceptions.IllegalDateRangeException();
        }
    }

    public LogicMarkerResultResponse listWithMarker(String instanceId, String marker, Integer maxKeys) {
        instanceId = instanceDao.queryInstanceUuid(instanceId);
        SnapshotListWithTimeResponse snapshotListResponse =
                rdsClientFactory.createRdsClient2().snapshotList(instanceId);
        Collections.sort((ArrayList) snapshotListResponse.getSnapshots(), new Comparator<Snapshot>() {
            @Override
            public int compare(Snapshot o1, Snapshot o2) {
                return o2.getSnapshotStartTime().compareTo(o1.getSnapshotStartTime());
            }
        });
        LogicMarkerResultResponse<Snapshot> response =
                listForPageByMarker((ArrayList) snapshotListResponse.getSnapshots(), marker, maxKeys);
        return response;
    }

    public LogicMarkerResultResponse<Snapshot> listForPageByMarker(List<Snapshot> snapshots,
                                                                           String marker,
                                                                           int maxKeys) {
        LogicMarkerResultResponse<Snapshot> response = new LogicMarkerResultResponse<>();
        response.setMarker(marker);
        response.setMaxKeys(maxKeys);

        if (snapshots == null || snapshots.size() == 0) {
            return response;
        }
        if (StringUtils.isEmpty(marker) || maxKeys < 1) {
            return response;
        }
        String nextMarker;
        int start = 0;
        int end = 0;
        boolean find = false;
        if ("-1".equals(marker)) {
            find = true;
        }
        Snapshot snapshot;
        for (int index = 0; index < snapshots.size(); index++) {
            snapshot = snapshots.get(index);
            if ((!"-1".equals(marker)) && snapshot.getSnapshotId().equals(marker)) {
                start = index;
                find = true;
                break;
            }
        }
        end = start + maxKeys;
        if (!find) {
            response.setIsTruncated(false);
            return response;
        } else if (end >= snapshots.size() - 1) {
            end = snapshots.size();
            response.setIsTruncated(false);
        } else {
            nextMarker = snapshots.get(end).getSnapshotId();
            response.setNextMarker(nextMarker);
            response.setIsTruncated(true);
        }
        logger.info("startIndex : " + start + " ,endIndex : " + end);
        List<Snapshot> instanceForPage = snapshots.subList(start, end);

        response.setResult(instanceForPage);
        return response;
    }
    public void snapshotDelete(String instanceId, String snapshotId) {
        if (BasisUtils.isShortId(instanceId)) {
            instanceId = instanceService.findInsntaceUUidByShortId(instanceId);
        }
        RDSClient2 client2 = rdsClientFactory.createRdsClient2();
        // 禁止删除自动备份的备份集
        SnapshotGetResponse snapshotGetResponse = client2.snapshotGet(instanceId, snapshotId, 43200);
        if (StringUtils.isNotEmpty(snapshotGetResponse.getSnapshot().getSnapshotType())) {
            if ("automated".equalsIgnoreCase(snapshotGetResponse.getSnapshot().getSnapshotType())) {
                throw new RDSBusinessExceptions.DeleteAutomatedBackupException();
            }
        }
        client2.snapshotDelete(instanceId, snapshotId);
    }

    public BackupCrossRegionListResponses getCrossRegionList(String instanceId, String startDataTime,
                                                             String endDataTime, String marker, String maxKeys,
                                                             String storageRegion) {

        RDSClient2 rdsClient = rdsClientFactory.createRdsClient2();
        InstanceGetResponse instanceGetResponse = rdsClient.instanceDescribe(instanceId);
        // 校验实例类型，拦截sqlserver
        if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getEngine())
                && RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(instanceGetResponse.getInstance().getEngine())) {
            throw new RDSBusinessExceptions.InstanceEngineException();
        }
        return rdsClient.getCrossRegionList(instanceId, startDataTime, endDataTime, marker, maxKeys, storageRegion);

    }

    public CopyRegionSnapshotDetail getCrossRegionDetail(String instanceId, String snapshotId) {
        RDSClient2 rdsClient = rdsClientFactory.createRdsClient2();
        InstanceGetResponse instanceGetResponse = rdsClient.instanceDescribe(instanceId);
        // 校验实例类型，拦截sqlserver
        if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getEngine())
                && RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(instanceGetResponse.getInstance().getEngine())) {
            throw new RDSBusinessExceptions.InstanceEngineException();
        }
        return rdsClient.getCrossRegionDetail(instanceId, snapshotId);
    }

    public BackupTargetRegionResponse getTargetRegionList(String instanceId) {
        RDSClient2 rdsClient = rdsClientFactory.createRdsClient2();
        InstanceGetResponse instanceGetResponse = rdsClient.instanceDescribe(instanceId);
        // 校验实例类型，拦截sqlserver
        if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getEngine())
                && RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(instanceGetResponse.getInstance().getEngine())) {
            throw new RDSBusinessExceptions.InstanceEngineException();
        }
        return rdsClient.getTargetRegionList(instanceId);
    }

    public ListAllCrossRegions getListCopyRegions(String instanceId, String startDataTime,
                                                  String endDataTime, String dataBackupType) {
        RDSClient2 rdsClient = rdsClientFactory.createRdsClient2();
        InstanceGetResponse instanceGetResponse = rdsClient.instanceDescribe(instanceId);
        // 校验实例类型，拦截sqlserver
        if (StringUtils.isNotEmpty(instanceGetResponse.getInstance().getEngine())
                && RDSConstant.RDS_ENGINE_SQLSERVER.equalsIgnoreCase(instanceGetResponse.getInstance().getEngine())) {
            throw new RDSBusinessExceptions.InstanceEngineException();
        }
        return rdsClient.getListCopyRegions(instanceId, startDataTime, startDataTime, dataBackupType);
    }
}
