//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rnflower decompiler)
//

package com.baidu.bce.common.network.common.permission;

import com.baidu.bce.common.network.common.permission.PermissionExceptionUtil.PermissionDenyException;
import com.baidu.bce.common.network.common.permission.annotation.PermissionVertify;
import com.baidu.bce.common.network.common.service.BeanFactory;
import com.baidu.bce.common.network.common.service.IamLogicalService;
import com.baidu.bce.common.network.common.tag.service.TagLogicalService;
import com.baidu.bce.common.network.common.util.RegionUtil;
import com.baidu.bce.common.network.common.util.UserUtils;
import com.baidu.bce.externalsdk.logical.network.common.annotation.IdPermission;
import com.baidu.bce.externalsdk.logical.network.common.annotation.TagPermisson;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.BatchPermissionRequest;
import com.baidu.bce.internalsdk.iam.model.BatchPermissionRequest.Request;
import com.baidu.bce.internalsdk.iam.model.PermissionRequest.RequestContext;
import com.baidu.bce.internalsdk.iam.model.SignatureValidator;
import com.baidu.bce.logic.rds.service.exception.RDSBusinessExceptions;
import com.baidu.bce.logic.rds.service.interceptor.RdsRequestParam;
import com.baidu.bce.logic.rds.service.util.BasisUtils;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import endpoint.EndpointManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

@Aspect
@Component
@ConditionalOnExpression("${bce.logical.permissionvertify.enabled:false}")
@Order(2147483547)
public class PermissionAspect {
    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionAspect.class);
    @Autowired
    private IamLogicalService iamLogicalService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private TagLogicalService tagLogicalService;

    @Autowired
    private com.baidu.bce.logic.rds.service.TagLogicalService rdsTagLogicalService;

    public PermissionAspect() {
    }

    @Pointcut("execution(* com.baidu.bce..*(..)) && @annotation(org.springframework.web.bind.annotation.RequestMapping) && @annotation(com.baidu.bce.common.network.common.permission.annotation.PermissionVertify) ")
    public void permissionVertifyPointcut() {
    }

    @Around("permissionVertifyPointcut()")
    public Object permissionVertifyInterceptor(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        boolean permissionVertifySuccess = true;
        new HashMap();
        Set<String> instanceIdSet = new LinkedHashSet<>();
        LOGGER.debug("1. PermissionAspect: handle annotation and vertify permission");

        MethodSignature result;
        try {
            result = (MethodSignature)proceedingJoinPoint.getSignature();
            Method method = result.getMethod();
            PermissionVertify permissionVertifyAnnotation = (PermissionVertify)method.getAnnotation(PermissionVertify.class);
            if(permissionVertifyAnnotation == null) {
                throw new Exception("PermissionAspect not valid annotation");
            }

            boolean isForbidden = permissionVertifyAnnotation.isForbidden();
            String[] serviceIds = permissionVertifyAnnotation.service();
            String[] permissions = permissionVertifyAnnotation.permission();
            Class idConverter = permissionVertifyAnnotation.idConverter();
            String commonType = permissionVertifyAnnotation.type();
            String[] ids = permissionVertifyAnnotation.ids();
            if(isForbidden) {
                permissionVertifySuccess = false;
            } else if(ArrayUtils.isNotEmpty(ids)) {
                CollectionUtils.addAll(instanceIdSet, ids);
                List<Tag> tags = this.collectTagsFromParameters(proceedingJoinPoint);
                permissionVertifySuccess = this.permissionVertifySuccess(null, instanceIdSet, serviceIds, permissions, tags);
            } else {
                Map<String, Set<String>> instanceIdMap = this.handleMethodParameters(proceedingJoinPoint);
//
//                // 变成一个有序的，方便后面做映射
//                Map<String, Set<String>> instanceIdMapSort = new HashMap<>();
//                Iterator instanceIdMapItr = instanceIdMap.keySet().iterator();
//
//                while(instanceIdMapItr.hasNext()) {
//                    String key = (String) instanceIdMapItr.next();
//
//                    Set set = instanceIdMap.get(key);
//                    instanceIdMapSort.put(key, new LinkedHashSet<String>(set));
//                }
//                instanceIdMap = instanceIdMapSort;

                // 维护一个map
                Map<String, String> longIdShortIdMap = new HashMap<String, String>();

                if(MapUtils.isNotEmpty(instanceIdMap) && idConverter != IdConverter.class) {
                    IdConverter converter = (IdConverter)BeanFactory.getBean(idConverter);


                    Map<String, Set<String>> instanceIdConMap = converter.convertIds(instanceIdMap);
                    Object[] longIdSet = instanceIdMap.get("").toArray();
                    Object[] shortIdSet = instanceIdConMap.get("").toArray();

                    for(int i = 0; i< longIdSet.length; i++) {

                        String longId = (String) longIdSet[i];
                        String shortId = (String) shortIdSet[i];

                        // open api 特殊处理
                        if (StringUtils.contains(shortId, "|A|A|")) {
                            String[] shortIdLongidArray = shortId.split("\\|A\\|A\\|");
                            longIdShortIdMap.put(shortIdLongidArray[1], shortIdLongidArray[0]);

                        } else {
                            longIdShortIdMap.put(longId, shortId);
                        }

                    }

//                    if (instanceIdMap.get("bce:rds") != null) {
//                        String[] idsPreCovered = (String[]) instanceIdMapSort.get("bce:rds").toArray();
//
//                        String[] idsCovered = (String[]) instanceIdMap.get("bce:rds").toArray();
//
//                        for (int i = 0; i < idsPreCovered.length; i++) {
//                            longIdShortIdMap.put(idsPreCovered[i], idsCovered[i]);
//                        }
//                    }

                }

                Iterator i$ = instanceIdMap.entrySet().iterator();

                while(true) {
                    while(i$.hasNext()) {
                        Entry entry = (Entry)i$.next();

                        Iterator iInner;

                        String id;
                        if(StringUtils.isNotBlank((String)entry.getKey())) {
                            iInner = ((Set)entry.getValue()).iterator();

                            while(iInner.hasNext()) {
                                id = (String)iInner.next();
                                instanceIdSet.add((String)entry.getKey() + "/" + id);
                            }
                        } else if(StringUtils.isNotBlank(commonType)) {
                            iInner = ((Set)entry.getValue()).iterator();

                            while(iInner.hasNext()) {
                                id = (String)iInner.next();
                                instanceIdSet.add(commonType + "/" + id);
                            }
                        } else {
                            iInner = ((Set)entry.getValue()).iterator();

                            while(iInner.hasNext()) {
                                id = (String)iInner.next();
                                instanceIdSet.add(id);
                            }
                        }
                    }

                    permissionVertifySuccess = this.permissionVertifySuccess(longIdShortIdMap, instanceIdSet, serviceIds, permissions, (List)null);
                    break;
                }
            }
        } catch (Throwable var19) {
            LOGGER.debug("1. PermissionAspect error!", var19);
        }

        if(!permissionVertifySuccess) {
            LOGGER.debug("permission vertify deny! resouce={}", instanceIdSet);
            throw new RDSBusinessExceptions.RdsPermissionDenyException();
        } else {
            result = null;
            Object[] originalArgs = proceedingJoinPoint.getArgs();
            LOGGER.debug("2. PermissionAspect: execute method");

            try {
                return proceedingJoinPoint.proceed(originalArgs);
            } catch (Exception var18) {
                throw var18;
            }
        }
    }

    private boolean permissionVertifySuccess(Map<String, String> longIdShortIdMap, Set<String> instanceIdSet, String[] serviceIds, String[] permissions, List<Tag> tags) throws UnsupportedEncodingException {
        if (longIdShortIdMap == null) {
            longIdShortIdMap = new HashMap();
        }

        String region = RegionUtil.getCurrentRegion();
        if(StringUtils.isBlank(region)) {
            region = "bj";
        }

        String accountId = UserUtils.getAccountId();
        String userId = UserUtils.getUserId();
        LOGGER.debug("VertifyInfo: instanceIdSet={}, serviceIds={}, permissions={}, region={}, accountId={}, userId={}", new Object[]{instanceIdSet, serviceIds, permissions, region, accountId, userId});
        Map<String, List<Tag>> instanceTagMap = null;
        if(CollectionUtils.isEmpty((Collection)instanceIdSet)) {
            LOGGER.debug("resource id is empty");
            instanceIdSet = new HashSet();
            ((Set)instanceIdSet).add("*");
        }

        Map<String, String> shortIdLongIdMap = new HashMap<>();
        //
        boolean requireAllInstancePermission = Boolean.TRUE;
        if (longIdShortIdMap != null && longIdShortIdMap.size() > 0) {

            Iterator ite = longIdShortIdMap.keySet().iterator();
            while (ite.hasNext()) {
                String key = (String)ite.next();
                shortIdLongIdMap.put(longIdShortIdMap.get(key), key);
                LOGGER.debug("[Permission] shortIdLongIdMap put {}, {}", longIdShortIdMap.get(key), key);
            }
            requireAllInstancePermission = Boolean.FALSE;
        }

        LOGGER.debug("requireAllInstancePermission is {}",
                requireAllInstancePermission ? "true" : "false");

        // RDS 请求tag时候用的是长id，一个历史遗留问题，这里需要做兼容
        if(null == tags) {
            List instanceIdList = new ArrayList();

//            instanceIdList = Lists.newArrayList(instanceIdSet.toArray());

            Iterator<String> instanceIdIte = instanceIdSet.iterator();
            while (instanceIdIte.hasNext()) {
                String each = instanceIdIte.next();

                // 长id转成短id
                if (!BasisUtils.isLongId(each.replaceFirst("instance/", ""))) {

                    String key = each.replaceFirst("instance/", "");

                    instanceIdList.add(shortIdLongIdMap.get(key));

                } else {
                    instanceIdList.add(each.replaceFirst("instance/", ""));
                }

            }
            if (instanceIdList.size() == 1 && instanceIdList.get(0) == null) {
                // 实例 ID 为 null，目前已知原因是由于：/v1/rds/resource/getGroup, /v1/rds/qualify/realname 等不该进行资源鉴权
                //  的 API 使用了 @PermissionVertify 注解。这里直接返回 true，表示直接通过鉴权。
                return true;
            }
            instanceTagMap = rdsTagLogicalService.queryTagsByResourceUuids(instanceIdList);
        }

        IAMClient iamClient = this.createIamClient(region);
        String authToken = this.iamLogicalService.getConsoleToken().getId();
        iamClient.setxAuthToken(authToken);
        List<BatchPermissionRequest> batchPermissionRequests = new ArrayList();
        String[] arr$ = serviceIds;
        int len$ = serviceIds.length;

        for(int i$ = 0; i$ < len$; ++i$) {
            String serviceId = arr$[i$];
            BatchPermissionRequest batchPermissionRequest = new BatchPermissionRequest();
            List<Request> requests = new ArrayList();
            Iterator iInner = ((Set)instanceIdSet).iterator();

            while(iInner.hasNext()) {
                String instanceId = (String)iInner.next();
                Request request = new Request();
                RequestContext requestContext = new RequestContext();
                if(null == tags) {
                    String[] array = instanceId.split("/");

                    if(array.length > 1) {

                        if (instanceTagMap.get(array[1]) != null) {
                            tags = (List) instanceTagMap.get(array[1]);
                        } else if (longIdShortIdMap != null
                                && instanceTagMap.get(longIdShortIdMap.get(array[1])) != null) {
                            tags = (List) instanceTagMap.get(longIdShortIdMap.get(array[1]));
                        } else if (shortIdLongIdMap != null
                                && instanceTagMap.get(shortIdLongIdMap.get(array[1])) != null) {
                            tags = (List) instanceTagMap.get(shortIdLongIdMap.get(array[1]));
                        }

                    } else {

                        if (longIdShortIdMap != null && longIdShortIdMap.get(instanceId) != null) {
                            tags = (List)instanceTagMap.get(longIdShortIdMap.get(instanceId));
                        } else if (shortIdLongIdMap != null && shortIdLongIdMap.get(instanceId) != null) {
                            tags = (List)instanceTagMap.get(shortIdLongIdMap.get(instanceId));

                        }

                    }
                }

                if(CollectionUtils.isNotEmpty(tags)) {
                    Iterator iInner2 = tags.iterator();

                    while(iInner2.hasNext()) {
                        Tag tag = (Tag)iInner2.next();
                        requestContext.withCondition(tag.getTagKey(), tag.getTagValue());
                    }
                }

                request.setRequestContext(requestContext);
                request.setPermission(Arrays.asList(permissions));
                request.setRegion(region);
                request.setResourceOwner(accountId);

                // TODO 如果是长id，统一替换成短id
                String resourceId = instanceId;
                if (!requireAllInstancePermission) {
                    String prefix = "instance/";

                    resourceId = resourceId.replace(prefix, "");
                    LOGGER.debug("[trans resourceId] resourceId start : " + resourceId);
                    if (BasisUtils.isLongId(resourceId)) {

                        LOGGER.debug("[trans resourceId] resourceId is long: " + resourceId);

                        if (longIdShortIdMap != null && longIdShortIdMap.get(resourceId) != null) {
                            LOGGER.debug("[trans resourceId] resourceId longId {} to shrotId {}",
                                    resourceId, longIdShortIdMap.get(resourceId));
                            resourceId = longIdShortIdMap.get(resourceId);

                        }

                    }

                    // 最后再补上prefix
                    resourceId = prefix + resourceId;

                    LOGGER.debug("requireAllInstancePermission is {}, use resourceId {} ",
                            requireAllInstancePermission ? "true" : "false", resourceId);
                } else {
                    resourceId = "*";

                    LOGGER.debug("requireAllInstancePermission is {}, use resourceId {} ",
                            requireAllInstancePermission ? "true" : "false", resourceId);
                }

                LOGGER.debug("[trans resourceId] resourceId end: " + resourceId);
                request.setResource(Collections.singletonList(resourceId));
                request.setService(serviceId);
                requests.add(request);
            }
            batchPermissionRequest.setVerifyList(requests);
            batchPermissionRequest.setSecurityToken(RdsRequestParam.getThreadSecurityToken());
            batchPermissionRequests.add(batchPermissionRequest);
        }

        boolean verifySuccess = false;
        Iterator i$ = batchPermissionRequests.iterator();

        BatchPermissionRequest batchPermissionRequest;
        do {
            if(!i$.hasNext()) {
                return true;
            }

            batchPermissionRequest = (BatchPermissionRequest)i$.next();
        } while(verifySuccess != this.permissionService.isVerifySuccess(iamClient, userId, batchPermissionRequest));

        return verifySuccess;
    }

    public Map<String, Set<String>> handleMethodParameters(ProceedingJoinPoint proceedingJoinPoint) throws IllegalAccessException, ClassNotFoundException {
        Map<String, Set<String>> resourceIdMap = new HashMap();
        Object[] args = proceedingJoinPoint.getArgs();
        MethodSignature methodSignature = (MethodSignature)proceedingJoinPoint.getSignature();
        Method method = methodSignature.getMethod();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();
        Type[] parameterType = method.getGenericParameterTypes();

        for(int argIndex = 0; argIndex < args.length; ++argIndex) {
            Annotation[] arr$ = parameterAnnotations[argIndex];
            int len$ = arr$.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                Annotation annotation = arr$[i$];
                if(annotation instanceof IdPermission) {
                    this.handleParameter(parameterType[argIndex], args[argIndex], resourceIdMap, (IdPermission)annotation);
                }
            }
        }

        return resourceIdMap;
    }

    public void handleParameter(Type paramType, Object obj, Map<String, Set<String>> resourceIdMap, IdPermission idPermission) throws IllegalAccessException, ClassNotFoundException {
        if(obj != null) {
            if(paramType instanceof ParameterizedType) {
                String paramRawType = ((Class)((ParameterizedType)paramType).getRawType()).getName();
                if(StringUtils.isBlank(paramRawType)) {
                    return;
                }

                if(paramRawType.startsWith("com.baidu")) {
                    this.handleParameterClassValue(Class.forName(paramRawType), obj, resourceIdMap);
                    return;
                }

                Class<?> paramClazz = Class.forName(paramRawType);
                HashSet tmpResourceIdSet;
                ArrayList objectList;
                Iterator i$;
                Object value;
                if(this.checkType(paramClazz, Map.class)) {
                    tmpResourceIdSet = new HashSet();
                    objectList = new ArrayList();
                    this.processMapParameter(obj, tmpResourceIdSet, objectList);
                    if(CollectionUtils.isNotEmpty(tmpResourceIdSet)) {
                        if(resourceIdMap.get(idPermission.value()) == null) {
                            resourceIdMap.put(idPermission.value(), new HashSet());
                        }

                        ((Set)resourceIdMap.get(idPermission.value())).addAll(tmpResourceIdSet);
                    }

                    if(CollectionUtils.isNotEmpty(objectList)) {
                        i$ = objectList.iterator();

                        while(i$.hasNext()) {
                            value = i$.next();
                            this.handleParameter(value.getClass(), value, resourceIdMap, idPermission);
                        }
                    }
                } else if(this.checkType(paramClazz, Collection.class)) {
                    tmpResourceIdSet = new HashSet();
                    objectList = new ArrayList();
                    this.processCollectionParameter(obj, tmpResourceIdSet, objectList);
                    if(CollectionUtils.isNotEmpty(tmpResourceIdSet)) {
                        if(resourceIdMap.get(idPermission.value()) == null) {
                            resourceIdMap.put(idPermission.value(), new HashSet());
                        }

                        ((Set)resourceIdMap.get(idPermission.value())).addAll(tmpResourceIdSet);
                    }

                    if(CollectionUtils.isNotEmpty(objectList)) {
                        i$ = objectList.iterator();

                        while(i$.hasNext()) {
                            value = i$.next();
                            this.handleParameter(value.getClass(), value, resourceIdMap, idPermission);
                        }
                    }
                } else {
                    LOGGER.debug("not valid parameter type. {}", paramType);
                }
            } else if(this.isPrimitiveType((Class)paramType)) {
                LOGGER.debug("the type of parameter is primitive, " + paramType);
                if(resourceIdMap.get(idPermission.value()) == null) {
                    resourceIdMap.put(idPermission.value(), new HashSet());
                }

                ((Set)resourceIdMap.get(idPermission.value())).addAll(this.processResources(obj.toString()));
            } else if(((Class)paramType).getName().startsWith("com.baidu")) {
                LOGGER.debug("the type of parameter is user-defined, " + paramType);
                this.handleParameterClassValue((Class)paramType, obj, resourceIdMap);
            }

        }
    }

    public void handleParameterClassValue(Class<?> clazz, Object obj, Map<String, Set<String>> resourceIdMap) throws IllegalAccessException, ClassNotFoundException {
        if(obj != null) {
            Field[] arr$ = clazz.getDeclaredFields();
            int len$ = arr$.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                Field field = arr$[i$];
                field.setAccessible(true);
                if(!field.isSynthetic() || !"this$0".equals(field.getName())) {
                    String fieldName = field.getName();
                    Object fieldValue = field.get(obj);
                    Class fieldClazz = field.getType();
                    LOGGER.debug("handleParameterClassValue fieldName={}, fieldValue={}, fieldClazz={}", new Object[]{fieldName, fieldValue, fieldClazz});
                    if(fieldValue != null) {
                        fieldClazz = fieldValue.getClass();
                        Annotation annotation = field.getAnnotation(IdPermission.class);
                        if(annotation instanceof IdPermission) {
                            IdPermission idPermission = (IdPermission)annotation;
                            if(this.isPrimitiveType(fieldClazz)) {
                                if(resourceIdMap.get(idPermission.value()) == null) {
                                    resourceIdMap.put(idPermission.value(), new HashSet());
                                }

                                ((Set)resourceIdMap.get(idPermission.value())).addAll(this.processResources(fieldValue.toString()));
                            } else {
                                HashSet tmpResourceIdSet;
                                ArrayList objectList;
                                if(this.checkType(fieldClazz, Collection.class)) {
                                    tmpResourceIdSet = new HashSet();
                                    objectList = new ArrayList();
                                    this.processCollectionParameter(fieldValue, tmpResourceIdSet, objectList);
                                    if(CollectionUtils.isNotEmpty(tmpResourceIdSet)) {
                                        if(resourceIdMap.get(idPermission.value()) == null) {
                                            resourceIdMap.put(idPermission.value(), new HashSet());
                                        }

                                        ((Set)resourceIdMap.get(idPermission.value())).addAll(tmpResourceIdSet);
                                    }

                                    if(CollectionUtils.isNotEmpty(objectList)) {
                                        Iterator iInner = objectList.iterator();

                                        while(iInner.hasNext()) {
                                            Object value = iInner.next();
                                            this.handleParameter(value.getClass(), value, resourceIdMap, idPermission);
                                        }
                                    }
                                } else if(this.checkType(fieldClazz, Map.class)) {
                                    tmpResourceIdSet = new HashSet();
                                    objectList = new ArrayList();
                                    this.processMapParameter(fieldValue, tmpResourceIdSet, objectList);
                                    if(CollectionUtils.isNotEmpty(tmpResourceIdSet)) {
                                        if(resourceIdMap.get(idPermission.value()) == null) {
                                            resourceIdMap.put(idPermission.value(), new HashSet());
                                        }

                                        ((Set)resourceIdMap.get(idPermission.value())).addAll(tmpResourceIdSet);
                                    }
                                }
                            }
                        } else if(!this.isPrimitiveType(fieldClazz)) {
                            HashSet tmpResourceIdSet;
                            ArrayList objectList;
                            Iterator iInner;
                            Object value;
                            if(this.checkType(fieldClazz, Collection.class)) {
                                tmpResourceIdSet = new HashSet();
                                objectList = new ArrayList();
                                this.processCollectionParameter(fieldValue, tmpResourceIdSet, objectList);
                                if(CollectionUtils.isNotEmpty(objectList)) {
                                    iInner = objectList.iterator();

                                    while(iInner.hasNext()) {
                                        value = iInner.next();
                                        this.handleParameterClassValue(value.getClass(), value, resourceIdMap);
                                    }
                                }
                            } else if(this.checkType(fieldClazz, Map.class)) {
                                tmpResourceIdSet = new HashSet();
                                objectList = new ArrayList();
                                this.processMapParameter(fieldValue, tmpResourceIdSet, objectList);
                                if(CollectionUtils.isNotEmpty(objectList)) {
                                    iInner = objectList.iterator();

                                    while(iInner.hasNext()) {
                                        value = iInner.next();
                                        this.handleParameterClassValue(value.getClass(), value, resourceIdMap);
                                    }
                                }
                            } else if(fieldClazz.getName().startsWith("com.baidu") && !fieldClazz.isEnum()) {
                                this.handleParameterClassValue(fieldClazz, fieldValue, resourceIdMap);
                            }
                        }
                    }
                }
            }

        }
    }

    private List<Tag> collectTagsFromParameters(ProceedingJoinPoint proceedingJoinPoint) throws IllegalAccessException, ClassNotFoundException {
        List<Tag> tags = new ArrayList();
        Object[] args = proceedingJoinPoint.getArgs();
        MethodSignature methodSignature = (MethodSignature)proceedingJoinPoint.getSignature();
        Method method = methodSignature.getMethod();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();
        Type[] parameterType = method.getGenericParameterTypes();

        for(int argIndex = 0; argIndex < args.length; ++argIndex) {
            Annotation[] arr$ = parameterAnnotations[argIndex];
            int len$ = arr$.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                Annotation annotation = arr$[i$];
                if(annotation instanceof TagPermisson) {
                    if(args[argIndex] == null) {
                        break;
                    }

                    try {
                        this.getTags(tags, args[argIndex].getClass(), args[argIndex]);
                    } catch (Exception var14) {
                        LOGGER.debug("Collect Tag failed", var14.getMessage());
                    }
                }
            }
        }

        return tags;
    }

    public void getTags(List<Tag> tags, Class clazz, Object obj) throws Exception {
        if(obj != null) {
            Field[] arr$ = clazz.getDeclaredFields();
            int len$ = arr$.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                Field field = arr$[i$];
                field.setAccessible(true);
                if(!field.isSynthetic() || !"this$0".equals(field.getName())) {
                    Object filedValue = field.get(obj);
                    if(filedValue != null) {
                        String classNmae = filedValue.getClass().getName();
                        Type type = field.getGenericType();
                        Annotation tagPermisson = field.getAnnotation(TagPermisson.class);
                        if(tagPermisson instanceof TagPermisson) {
                            if(field.getGenericType() instanceof ParameterizedType && this.checkType(field.getType(), List.class)) {
                                Class clazzz = (Class)((ParameterizedType)type).getActualTypeArguments()[0];
                                if("com.baidu.bce.logical.tag.sdk.model.Tag".equalsIgnoreCase(clazzz.getName())) {
                                    tags.addAll((List)filedValue);
                                }
                            }
                        } else if(type instanceof ParameterizedType) {
                            String paramRawType = ((Class)((ParameterizedType)type).getRawType()).getName();
                            if(paramRawType.startsWith("com.baidu")) {
                                this.getTags(tags, filedValue.getClass(), filedValue);
                            } else if(paramRawType.startsWith("java.util.List")) {
                                Method getM = filedValue.getClass().getDeclaredMethod("size", new Class[0]);
                                int size = ((Integer)getM.invoke(filedValue, new Object[0])).intValue();

                                for(int i = 0; i < size; ++i) {
                                    getM = filedValue.getClass().getDeclaredMethod("get", new Class[]{Integer.TYPE});
                                    if(!getM.isAccessible()) {
                                        getM.setAccessible(true);
                                    }

                                    Object object = getM.invoke(filedValue, new Object[]{Integer.valueOf(i)});
                                    this.getTags(tags, object.getClass(), object);
                                }
                            }
                        } else if(classNmae.startsWith("com.baidu")) {
                            this.getTags(tags, filedValue.getClass(), filedValue);
                        }
                    }
                }
            }

        }
    }

    private void processMapParameter(Object srcObj, Set<String> primitiveSet, List<Object> objList) throws ClassNotFoundException, IllegalAccessException {
        Map<?, ?> mapValue = (Map)srcObj;
        if(mapValue != null) {
            LOGGER.debug("map keySet={}, valueSet={}", mapValue.keySet(), mapValue.values());
            Iterator i$ = mapValue.values().iterator();

            while(i$.hasNext()) {
                Object value = i$.next();
                Class valueClazz = value.getClass();
                if(this.isPrimitiveType(valueClazz)) {
                    primitiveSet.addAll(this.processResources(value.toString()));
                } else {
                    objList.add(value);
                }
            }
        }

    }

    private void processCollectionParameter(Object srcObj, Set<String> primitiveSet, List<Object> objList) throws ClassNotFoundException, IllegalAccessException {
        Collection<Object> values = (Collection)srcObj;
        if(values != null) {
            LOGGER.debug("list values={}", values);
            Iterator i$ = values.iterator();

            while(i$.hasNext()) {
                Object value = i$.next();
                Class valueClazz = value.getClass();
                if(this.isPrimitiveType(valueClazz)) {
                    primitiveSet.addAll(this.processResources(value.toString()));
                } else {
                    objList.add(value);
                }
            }
        }

    }

    private List<String> processResources(String resourceIdStr) {
        List<String> resourceIds = new ArrayList();
        if(StringUtils.isBlank(resourceIdStr)) {
            return resourceIds;
        } else {
            if(!resourceIdStr.contains(",")) {
                resourceIds.add(resourceIdStr);
            } else {
                resourceIds.addAll(Arrays.asList(resourceIdStr.split(",")));
            }

            return resourceIds;
        }
    }

    private boolean checkType(Class paramClazz, Class destClazz) {
        boolean result = false;
        if(paramClazz != null && destClazz != null) {
            if(destClazz.isAssignableFrom(paramClazz)) {
                result = true;
            }

            return result;
        } else {
            return result;
        }
    }

    private boolean isPrimitiveType(Class<?> paramClazz) {
        return paramClazz.isPrimitive() || paramClazz.getName().startsWith("java.lang");
    }

    public IAMClient createIamClient(String region) {
        String endpoint = EndpointManager.getInstance().getRegion(region).getEndpoint("IAM");
        IAMClient iamClient = new IAMClient(endpoint);
        iamClient.setSubuserEnabled(true);
        return iamClient;
    }

    private SignatureValidator getSignatureValidator(HttpServletRequest request) throws UnsupportedEncodingException {
        LOGGER.debug("PermissionAspect : getSignatureValidator start");
        String securityToken = null;
        String authorization = request.getHeader("Authorization");
        String method = request.getMethod();
        String uri = URLDecoder.decode(request.getRequestURI(), "UTF-8");
        Map<String, String> signatureHeaders = new HashMap();
        Enumeration headerNames = request.getHeaderNames();

        while(headerNames.hasMoreElements()) {
            String header = (String)headerNames.nextElement();
            signatureHeaders.put(header, request.getHeader(header));
            if(header.equalsIgnoreCase("X-Bce-Security-Token")) {
                securityToken = request.getHeader(header);
            }
        }

        Map<String, Object> parameters = new HashMap();
        Iterator i$ = request.getParameterMap().entrySet().iterator();

        while(i$.hasNext()) {
            Entry<String, String[]> entry = (Entry)i$.next();
            if(((String[])entry.getValue()).length == 1) {
                parameters.put(entry.getKey(), ((String[])entry.getValue())[0]);
            } else {
                parameters.put(entry.getKey(), Arrays.asList((Object[])entry.getValue()));
            }

            if(((String)entry.getKey()).equalsIgnoreCase("X-Bce-Security-Token")) {
                securityToken = ((String[])entry.getValue())[0];
            }
        }

        LOGGER.debug("PermissionAspect : getSignatureValidator success.");
        return new SignatureValidator(authorization, method, uri, signatureHeaders, parameters, securityToken);
    }
}
