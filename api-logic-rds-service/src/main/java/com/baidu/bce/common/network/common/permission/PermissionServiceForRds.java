//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rnflower decompiler)
//

package com.baidu.bce.common.network.common.permission;

import com.baidu.bce.common.network.common.service.IamLogicalService;
import com.baidu.bce.common.network.common.service.LogicalClientFactory;
import com.baidu.bce.common.network.common.tag.service.TagLogicalService;
import com.baidu.bce.internalsdk.iam.IAMClient;
import com.baidu.bce.internalsdk.iam.model.BatchPermissionRequest;
import com.baidu.bce.internalsdk.iam.model.BatchPermissionRequest.Request;
import com.baidu.bce.internalsdk.iam.model.BatchVerifyResults;
import com.baidu.bce.internalsdk.iam.model.BatchVerifyResults.Result;
import com.baidu.bce.internalsdk.iam.model.PermissionRequest.RequestContext;
import com.baidu.bce.internalsdk.iam.model.VerifyResult;
import com.baidu.bce.logic.rds.service.interceptor.RdsRequestParam;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.google.common.collect.Lists;
import endpoint.EndpointManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Service
public class PermissionServiceForRds {
    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionServiceForRds.class);
    @Autowired
    private LogicalClientFactory logicalClientFactory;
    @Autowired
    private IamLogicalService iamLogicalService;
    @Autowired
    TagLogicalService tagLogicalService;

    @Autowired
    private com.baidu.bce.logic.rds.service.TagLogicalService rdsTagLogicalService;

    public PermissionServiceForRds() {
    }

    public boolean unionBuyPermissionVerify(String accountId, String userId, String[] serviceIds, List<String> permissions) {
        String region = EndpointManager.getThreadRegion();
        if(StringUtils.isBlank(region)) {
            region = "bj";
        }

        IAMClient iamClient = this.logicalClientFactory.createIamClientByRegion(region);
        iamClient.setxAuthToken(this.iamLogicalService.getConsoleToken().getId());
        List<BatchPermissionRequest> batchPermissionRequests = new ArrayList();
        String[] arr$ = serviceIds;
        int len$ = serviceIds.length;

        for(int i$ = 0; i$ < len$; ++i$) {
            String serviceId = arr$[i$];
            BatchPermissionRequest batchPermissionRequest = new BatchPermissionRequest();
            List<Request> requests = new ArrayList();
            Request request = new Request();
            request.setPermission(permissions);
            request.setRegion(region);
            request.setResourceOwner(accountId);
            request.setResource(Collections.singletonList("*"));
            request.setService(serviceId);
            requests.add(request);
            batchPermissionRequest.setSecurityToken(RdsRequestParam.getThreadSecurityToken());
            batchPermissionRequest.setVerifyList(requests);
            batchPermissionRequests.add(batchPermissionRequest);
        }

        boolean verifySuccess = true;
        Iterator i$ = batchPermissionRequests.iterator();

        while(i$.hasNext()) {
            BatchPermissionRequest batchPermissionRequest = (BatchPermissionRequest)i$.next();
            verifySuccess = this.isVerifySuccess(iamClient, userId, batchPermissionRequest);
            if(!verifySuccess) {
                break;
            }
        }

        LOGGER.info("unionBuyPermissionVerify result:{}", Boolean.valueOf(verifySuccess));
        return verifySuccess;
    }

    public boolean isVerifySuccess(IAMClient iamClient, String userId, BatchPermissionRequest batchPermissionRequest) {
        LOGGER.debug("isVerifySuccess: userId={}, batchPermissionRequest={}", userId, batchPermissionRequest);
        BatchVerifyResults verifyResults = null;

        try {
            verifyResults = iamClient.batchVerify(userId, batchPermissionRequest);
        } catch (Exception var10) {
            LOGGER.debug("authAndVerify error! ", var10.getMessage());
            return false;
        }

        if(verifyResults != null && !CollectionUtils.isEmpty(verifyResults.getVerifyResults())) {
            List<Result> results = verifyResults.getVerifyResults();
            Iterator i$ = results.iterator();

            while(i$.hasNext()) {
                Result result = (Result)i$.next();
                if(result == null || CollectionUtils.isEmpty(result.getResult())) {
                    return false;
                }

                Iterator i2 = result.getResult().iterator();

                while(i2.hasNext()) {
                    VerifyResult subResult = (VerifyResult)i2.next();
                    if(subResult == null) {
                        return false;
                    }

                    if(!"ALLOW".equals(subResult.getEffect())) {
                        LOGGER.debug("permission not allow. result = {}", subResult);
                        return false;
                    }
                }
            }

            LOGGER.debug("verify success!");
            return true;
        } else {
            LOGGER.debug("verify result null.");
            return false;
        }
    }

    public List<String> filterResourceList(
                    String accountId,
                    String userId,
                    String service,
                    Map<String, String> shortIdLongIdMap,
                    List<String> permissions) {
        if(shortIdLongIdMap == null || CollectionUtils.isEmpty(shortIdLongIdMap.keySet())) {
            return Lists.newArrayList();
        } else {
            String region = EndpointManager.getThreadRegion();
            if(StringUtils.isBlank(region)) {
                region = "bj";
            }

            // 反向map，忽略非1对1的情况
            Map<String, String> longIdShortIdMap = new HashMap<String, String>();
            Iterator idMapIte = shortIdLongIdMap.keySet().iterator();
            while (idMapIte.hasNext()) {
                String key = (String) idMapIte.next();
                longIdShortIdMap.put(shortIdLongIdMap.get(key), key);
                LOGGER.debug("[batch per] put {}, {}", shortIdLongIdMap.get(key), key);
            }

            LOGGER.debug("[permiss-test] longIdShortIdMap {}", longIdShortIdMap.size());
            LOGGER.debug("[permiss-test] shortIdLongIdMap {}", shortIdLongIdMap.size());

            List<String> instanceUuidList = Lists.newArrayList();
            Iterator i$;
            String instanceId;
            Map instanceTagMap;

            // 请求tag用长id
            instanceUuidList = Lists.newArrayList(longIdShortIdMap.keySet().iterator());

            LOGGER.debug("[permiss-test] instanceUuidList {}", instanceUuidList.size());

            instanceTagMap = rdsTagLogicalService.queryTagsByResourceUuids(instanceUuidList);
            BatchPermissionRequest batchPermissionRequest = new BatchPermissionRequest();
            List<Request> requests = Lists.newArrayList();
            i$ = ((List)instanceUuidList).iterator();

            LOGGER.debug("[permiss-test] instanceUuidList 2 {}", instanceUuidList.size());

            int j = 0;

            String[] array;
            while(i$.hasNext()) {
                j++;
                instanceId = (String)i$.next();
                Request request = new Request();
                request.setPermission(permissions);
                request.setRegion(region);
                request.setResourceOwner(accountId);
                // 请求iam的时候反解成短id
                request.setResource(Collections.singletonList("instance/" + longIdShortIdMap.get(instanceId)));
                request.setService(service);
                RequestContext requestContext = new RequestContext();
                new ArrayList();
                array = instanceId.split("/");
                List tags;
                if(array.length > 1) {
                    tags = (List)instanceTagMap.get(array[1]);
                } else {
                    tags = (List)instanceTagMap.get(instanceId);
                }

                if(CollectionUtils.isNotEmpty(tags)) {
                    Iterator tagIterator = tags.iterator();

                    while(tagIterator.hasNext()) {
                        Tag tag = (Tag)tagIterator.next();
                        requestContext.withCondition(tag.getTagKey(), tag.getTagValue());
                    }
                }

                request.setRequestContext(requestContext);
                requests.add(request);
            }

            LOGGER.debug("[permiss-test] j {}", j);

            batchPermissionRequest.setVerifyList(requests);
            batchPermissionRequest.setSecurityToken(RdsRequestParam.getThreadSecurityToken());
            IAMClient iamClient = this.logicalClientFactory.createIamClientByRegion(region);
            iamClient.setxAuthToken(this.iamLogicalService.getConsoleToken().getId());
            BatchVerifyResults batchVerifyResults = iamClient.batchVerify(userId, batchPermissionRequest);
            List<String> resultInstanceList = Lists.newArrayList();
            if(batchVerifyResults != null && !CollectionUtils.isEmpty(batchVerifyResults.getVerifyResults())) {
                List<Result> results = batchVerifyResults.getVerifyResults();

                for(int i = 0; i < results.size(); ++i) {
                    if("ALLOW".equals(((VerifyResult)((Result)results.get(i)).getResult().get(0)).getEffect())) {
                        array = ((String)((List)instanceUuidList).get(i)).split("/");
                        if(array.length > 1) {
                            resultInstanceList.add(array[1]);
                        } else {
                            resultInstanceList.add(array[0]);
                        }
                    }
                }

                LOGGER.debug("batch verify: allow instance uuid = {}", resultInstanceList);
                return resultInstanceList;
            } else {
                LOGGER.debug("verify result is null.");
                return resultInstanceList;
            }
        }
    }
}
