<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>api-logic-rds-root</artifactId>
        <groupId>com.baidu.bce</groupId>
        <version>${api-logic-rds-version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>api-logic-rds-service</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-rds-internalsdk</artifactId>
            <version>${api-logic-rds-version}</version>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <version>1.10.19</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito</artifactId>
            <version>1.6.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.fbi</groupId>
            <artifactId>bp-auditing-sdk</artifactId>
            <version>2.0.228.124648</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>1.6.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.196</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-plat-web-framework-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.16</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-trail</artifactId>
            <version>1.0.12.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-plat-servicecatalog-sdk</artifactId>
            <version>1.0.48.1</version>
        </dependency>

        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-plat-web-framework-iam</artifactId>
            <version>1.3.52.2_jdk7</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-core</artifactId>
            <version>1.0.31.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baidu.bce</groupId>
                    <artifactId>bce-internal-sdk-iam</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-service-renew-sdk</artifactId>
            <version>1.0.105.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-zone-sdk</artifactId>
            <version>1.0.7.1</version>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>com.baidu.bce</groupId>-->
            <!--<artifactId>bce-logical-vpc-external-sdk</artifactId>-->
            <!--<version>1.0.743.19</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.baidu.bce-plat</groupId>
            <artifactId>resource-manager-sdk</artifactId>
            <version>1.0.111.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-api-accessor-client</artifactId>
            <version>1.0.0.66739</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.fbi</groupId>
            <artifactId>bp-resource-manager-client</artifactId>
            <version>2.0.master.105</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-internal-sdk-billing</artifactId>
            <version>2.7.375.581</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.fbi</groupId>
            <artifactId>product-domain-model</artifactId>
            <version>2.0.194.125586</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.baidu.bce.fbi</groupId>-->
<!--            <artifactId>sp-pricing-client</artifactId>-->
<!--            <version>2.0.71.114533</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.baidu.bce.pricing</groupId>
            <artifactId>pricing-sdk-java</artifactId>
            <version>2.0.1.67</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-rds-dao</artifactId>
            <version>${api-logic-rds-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-console-billing-service</artifactId>
            <version>1.0.2045.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>plat-log-trace-sdk</artifactId>
            <version>1.0.42.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-aop</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-vpc-common</artifactId>
            <version>1.1.6.1</version>
            <exclusions>
                <exclusion>
                        <groupId>com.baidu.bce</groupId>
                        <artifactId>bce-logical-tag-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>Baidu_Local.cglib</groupId>
                    <artifactId>cglib-nodep</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-tag-sdk</artifactId>
            <version>1.0.45.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-service-bus-sdk</artifactId>
            <version>1.0.7.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-order-executor-sdk</artifactId>
            <version>1.0.50.1</version>
        </dependency>
        <dependency>
            <groupId>com.baidubce</groupId>
            <artifactId>bce-java-sdk</artifactId>
            <version>0.10.41</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baidu.bce</groupId>
                    <artifactId>bce-plat-web-framework</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jcl-over-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>joda-time</groupId>
                    <artifactId>joda-time</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>