#!/usr/bin/env bash
if [ $# -eq 0 ] ; then
  mvn -Dspring.profiles.active=test clean package -U
else
  if [ $1 = "-q" ] ; then
    mvn -Dmaven.test.skip=true -T 1C clean package -U
  else
    echo "build.sh [-q]"
    exit 0
  fi
fi

if [ $? -ne 0 ] ; then
  echo "mvn package error"
  exit -1
fi

rm -rf output
mkdir output

mkdir -p output/bin
mkdir -p output/conf
mkdir -p output/lib

tar -xvzf api-logic-rds/deploy/java8.tar.gz -C output/bin
cp bcedeploywhitelist.txt output

cp api-logic-rds/target/api-logic-rds-version.jar output/lib/api-logic-rds.jar
cp api-logic-rds/target/api-logic-rds-version.jar output/bin/api-logic-rds.jar
cp api-logic-rds/deploy/start.sh output/bin
cp api-logic-rds/deploy/stop.sh output/bin
cp api-logic-rds/src/main/resources/*.properties output/conf
cp api-logic-rds/src/main/resources/endpoint.json output/conf