<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.bce.logic.rds.dao.mybatis.ActionTimeMapper">
    <select id="queryActionTime" resultType="java.sql.Timestamp">
        SELECT last_update_time FROM t_action_time WHERE resource_type = #{resourceType}
    </select>

    <update id="updateActionTimeByResourceType">
        UPDATE t_action_time
        SET last_update_time = #{lastUpdateTime}, resource_type = #{resourceType} , updated_time = now()
        WHERE resource_type = #{resourceType}
    </update>

    <insert id="addActionTime">
        INSERT INTO t_action_time(last_update_time, resource_type, updated_time)
        VALUES (#{actionTime}, #{resourceType}, now())
    </insert>

</mapper>