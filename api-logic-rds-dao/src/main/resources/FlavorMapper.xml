<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.bce.logic.rds.dao.mybatis.FlavorMapper">
    <select id="selectFlavorCountByUniqueKey" resultType="int">
        select count(1) from t_rds_flavor
        where cpu_count = #{flavorPO.cpuCount} and memory_capacity = #{flavorPO.memoryCapacity} and
        min_volume_capacity &lt;= #{flavorPO.volumeCapacity} and max_volume_capacity &gt;= #{flavorPO.volumeCapacity} and
        engine = #{flavorPO.engine} and application_type = #{flavorPO.applicationType}
        and deleted = 0
    </select>

    <select id="selectFlavorByUniqueKey" resultType="com.baidu.bce.logic.rds.dao.model.FlavorPO">
        select id, cpu_count, memory_capacity, min_volume_capacity, max_volume_capacity, engine, application_type
        from t_rds_flavor
        where cpu_count = #{flavorPO.cpuCount} and memory_capacity = #{flavorPO.memoryCapacity} and
        engine = #{flavorPO.engine} and application_type = #{flavorPO.applicationType}
        and deleted = 0
    </select>

    <select id="getZoneFlavorLimitPOList" resultType="com.baidu.bce.logic.rds.dao.model.ZoneFlavorLimitPO">
        select id, physical_zone, max_cpu_count, max_memory_capacity, max_volume_capacity
        from t_rds_flavor_limit
        where deleted = 0
    </select>

    <select id="getZoneFlavorLimitPO" resultType="com.baidu.bce.logic.rds.dao.model.ZoneFlavorLimitPO">
        select tf.id, tf.physical_zone, max_cpu_count, max_memory_capacity, max_volume_capacity
        from t_rds_flavor_limit tf
        left join t_zone tz on tf.physical_zone = tz.physical_zone and tz.account_id = #{userId}
        where tz.logical_zone = #{logicalZoneName}
    </select>
</mapper>