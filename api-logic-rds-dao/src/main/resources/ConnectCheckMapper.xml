<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.bce.logic.rds.dao.mybatis.ConnectCheckMapper">

    <!--    插入内网连接检测   -->
    <insert id="insertInnerConnect">
        INSERT IGNORE INTO t_rds_check_inner_connection(rds_instance_id,rds_instance_status, rds_vpc_id, bcc_instance_id,
        bcc_instance_name, bcc_instance_status, bcc_vpc_id, bcc_vpc_name, bcc_vpc_cidr, connect_id, connect_status, create_time, update_time, user_id, bcc_vnet_ip, bcc_instance_uuid)
        VALUES(
        #{innerConnectCheckPO.rdsInstanceId}, #{innerConnectCheckPO.rdsInstanceStatus}, #{innerConnectCheckPO.rdsVpcId},
        #{innerConnectCheckPO.bccInstanceId}, #{innerConnectCheckPO.bccInstanceName}, #{innerConnectCheckPO.bccInstanceStatus},
        #{innerConnectCheckPO.bccVpcId}, #{innerConnectCheckPO.bccVpcName}, #{innerConnectCheckPO.bccVpcCidr}, #{innerConnectCheckPO.connectId},
        #{innerConnectCheckPO.connectStatus}, #{innerConnectCheckPO.createTime}, #{innerConnectCheckPO.updateTime}, #{innerConnectCheckPO.userId}, #{innerConnectCheckPO.bccVnetIp}, #{innerConnectCheckPO.bccInstanceUuid})
    </insert>

    <!-- 查询内网连接列表   -->
    <select id="getInnerConnectList" resultType="com.baidu.bce.logic.rds.dao.model.InnerConnectCheckPO">
        SELECT bcc_instance_id, bcc_instance_name, connect_status, bcc_vpc_name, bcc_vpc_cidr, bcc_vnet_ip, update_time, connect_id, bcc_instance_uuid
        FROM t_rds_check_inner_connection
        WHERE rds_instance_id = #{instanceId}
        <if test="pageSize != null">
            LIMIT #{pageSize}
        </if>
        FOR UPDATE
    </select>

    <!-- 查询指定内网连接检测详情   -->
    <select id="getInnerDetail" resultType="com.baidu.bce.logic.rds.dao.model.InnerConnectCheckPO">
        SELECT *
        FROM t_rds_check_inner_connection
        WHERE connect_id = #{connectId}
    </select>

    <!--    更新内网连接检测-->
    <update id="updateInnerConnectStatus">
        UPDATE t_rds_check_inner_connection
        SET update_time = now(), connect_status = #{connectStauts}, identical_vpc = #{identicalVpc}, bcc_in_white_list = #{bccInWhiteList}
        WHERE connect_id = #{connectId}
    </update>

    <!--    删除指定内网连接检测-->
    <delete id="deleteConnect">
        DELETE FROM t_rds_check_inner_connection
        WHERE connect_id = #{connectId}
    </delete>


    <!--    插入外网连接检测   -->
    <insert id="insertPublicIp">
        INSERT IGNORE INTO t_rds_check_outer_connection(connect_id, rds_instance_id, inet_ip, inet_status,
        rds_instance_status, update_time, create_time, connect_status, user_id, bcc_in_white_list)
        VALUES(
        #{publicNetworkConnectCheckPO.connectId}, #{publicNetworkConnectCheckPO.rdsInstanceId}, #{publicNetworkConnectCheckPO.inetIp},
        #{publicNetworkConnectCheckPO.inetStatus}, #{publicNetworkConnectCheckPO.rdsInstanceStatus}, #{publicNetworkConnectCheckPO.updateTime},
        #{publicNetworkConnectCheckPO.createTime}, #{publicNetworkConnectCheckPO.connectStatus}, #{publicNetworkConnectCheckPO.userId},
        #{publicNetworkConnectCheckPO.bccInWhiteList})
    </insert>

    <!-- 查询外网连接列表   -->
    <select id="getOuterConnectList" resultType="com.baidu.bce.logic.rds.dao.model.PublicNetworkConnectCheckPO">
        SELECT inet_ip, connect_status, update_time, connect_id
        FROM t_rds_check_outer_connection
        WHERE rds_instance_id = #{instanceId}
        <if test="pageSize != null">
            LIMIT #{pageSize}
        </if>
        FOR UPDATE
    </select>

    <!-- 查询指定外网连接检测详情   -->
    <select id="getOuterDetail" resultType="com.baidu.bce.logic.rds.dao.model.PublicNetworkConnectCheckPO">
        SELECT *
        FROM t_rds_check_outer_connection
        WHERE connect_id = #{connectId}
    </select>

    <!--    更新外网连接检测-->
    <update id="updateOuterConnectStatus">
        UPDATE t_rds_check_outer_connection
        SET update_time = now(), connect_status = #{connectStauts}, inet_status = #{inetStatus}, bcc_in_white_list = #{bccInWhiteList}
        WHERE connect_id = #{connectId}
    </update>


    <!--    删除指定外网连接检测-->
    <delete id="deleteOuterConnect">
        DELETE FROM t_rds_check_outer_connection
        WHERE connect_id = #{connectId}
    </delete>


</mapper>