<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.bce.logic.rds.dao.mybatis.OrderNeedToSyncMapper">
    <select id="findOneSyncOrder" resultType="com.baidu.bce.logic.rds.dao.model.OrderNeedToSyncPO">
        SELECT id, updated_time, create_time, user_id, order_uuid, order_status, order_type, lock_time, lock_id
        FROM t_rds_ordersync
        WHERE ((lock_time = '1971-01-01 00:00:01' and lock_id = '')
        or (lock_time &lt; #{outLockDate} and lock_time &gt; '1971-01-01 00:00:01' and lock_id != ''))
        and order_status IN ('creating', 'unknown')
        order BY id ASC limit 500
    </select>

    <update id="lockOneLine">
        update t_rds_ordersync
        set lock_time = #{lockDate}, lock_id = #{lockId}
        where id = #{id} and lock_time = #{oldLockDate} and lock_id ='' and order_status IN ('creating', 'unknown')
    </update>

    <update id="seizeLockOneLine">
        update t_rds_ordersync set lock_time = #{lockDate}, lock_id = #{lockId}
        where id = #{id} and lock_time &lt; #{outLockDate} and lock_time &gt; '1971-01-01 00:00:01'
    </update>

    <update id="unLockOneLineByResourceUUid">
        update t_rds_ordersync set lock_id = '', lock_time = '1971-01-01 00:00:01'
        where order_uuid = #{orderUuid}
    </update>

    <insert id="insertOneOrder">
        INSERT INTO t_rds_ordersync(updated_time, create_time, user_id, order_uuid, order_status, order_type)
        VALUES(
        #{orderNeedToSyncPO.updatedTime}, now(), #{orderNeedToSyncPO.userId},
        #{orderNeedToSyncPO.orderUuid},#{orderNeedToSyncPO.orderStatus}, #{orderNeedToSyncPO.orderType})
    </insert>

    <update id="updateOrderStatus">
        update t_rds_ordersync
        set order_status = #{orderNeedToSyncPO.orderStatus}, updated_time = #{orderNeedToSyncPO.updatedTime}
        where order_uuid = #{orderNeedToSyncPO.orderUuid}
    </update>
</mapper>