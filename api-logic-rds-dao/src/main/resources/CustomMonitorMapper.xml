<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.bce.logic.rds.dao.mybatis.CustomMonitorMapper">

    <!--    插入自定义监控视图   -->
    <insert id="insertCustomMonitor">
        INSERT INTO t_rds_custommonitor(account_id, engine, create_time, update_time, extra)
        VALUES(
        #{customMinitorPO.accountId}, #{customMinitorPO.engine}, #{customMinitorPO.createTime},
        #{customMinitorPO.updateTime}, #{customMinitorPO.extra})
        ON DUPLICATE KEY UPDATE
            extra = VALUES(extra)
    </insert>

    <!-- 获取指定账户 && 指定实例引擎类型的自定义监控视图   -->
    <select id="getCustomMonitor" resultType="com.baidu.bce.logic.rds.dao.model.CustomMonitorPO">
        SELECT account_id, engine, create_time, update_time, extra
        FROM t_rds_custommonitor
        WHERE account_id = #{accountId} AND engine = #{engine}
        FOR UPDATE
    </select>

    <!--    更新指定账户 && 指定引擎类型的自定义监控视图-->
    <update id="updateCustomMonitor">
        UPDATE t_rds_custommonitor
        SET update_time = now(), extra = #{extra}
        WHERE account_id = #{accountId} AND engine = #{engine}
    </update>

    <!--    删除指定自定义监控视图-->
    <delete id="deleteCustomMonitor">
        DELETE FROM t_rds_custommonitor
        WHERE  account_id = #{accountId} AND engine = #{engine}
    </delete>


</mapper>