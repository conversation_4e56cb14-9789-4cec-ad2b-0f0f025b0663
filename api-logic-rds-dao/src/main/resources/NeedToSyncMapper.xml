<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.bce.logic.rds.dao.mybatis.NeedToSyncMapper">
    <select id="findAll" resultType="com.baidu.bce.logic.rds.dao.model.NeedToSyncPO">
        SELECT id, t0, t1, user_id, instance_uuid, instance_status, eip_status, replication_type, lock_time, lock_id
        FROM t_rds_datasync
        WHERE t0 &gt; #{lastActionTime}
    </select>

    <!--<update id="lockOneLine">-->
        <!--update t_rds_datasync-->
        <!--set lock_time = #{lockDate}, lock_id = #{lockId}-->
        <!--where id = #{id} and lock_time = '1971-01-01 00:00:01' and lock_id =''-->
    <!--</update>-->

    <!--<update id="seizeLockOneLine">-->
        <!--update t_rds_datasync set lock_time = #{lockDate}, lock_id = #{lockId}-->
        <!--where id = #{id} and lock_time &lt; #{outLockDate} and lock_time &gt; '1971-01-01 00:00:01'-->
    <!--</update>-->

    <!--<update id="unLockOneLineByResourceUUid">-->
        <!--update t_rds_datasync set lock_time = '1971-01-01 00:00:01',lock_id = ''-->
        <!--where instance_uuid = #{instanceUuid}-->
    <!--</update>-->


    <select id="findSyncData" resultType="com.baidu.bce.logic.rds.dao.model.NeedToSyncPO">
        SELECT id, t0, t1, user_id, instance_uuid, instance_status, eip_status, replication_type, lock_time, lock_id
        FROM t_rds_datasync
        WHERE (t0 &gt; lock_time and lock_id = '')
        or (lock_time &lt; #{outLockDate} and lock_time &gt; '1971-01-01 00:00:01' and lock_id != '')
        order BY t0 ASC limit 50
    </select>

    <!--<select id="findOneOutTime" resultType="com.baidu.bce.logic.rds.dao.model.NeedToSyncPO">-->
        <!--SELECT id, t0, t1, user_id, instance_uuid, instance_status, eip_status, replication_type, lock_time, lock_id-->
        <!--FROM t_rds_datasync-->
        <!--WHERE lock_time &lt; #{outLockDate} and lock_time &gt; '1971-01-01 00:00:01' and lock_id != ''-->
        <!--order BY t0 ASC limit 1-->
    <!--</select>-->

    <update id="lockOneLine">
        update t_rds_datasync
        set lock_time = #{lockDate}, lock_id = #{lockId}
        where id = #{id} and lock_time = #{oldLockDate} and lock_id =''
    </update>

    <update id="seizeLockOneLine">
        update t_rds_datasync set lock_time = #{lockDate}, lock_id = #{lockId}
        where id = #{id} and lock_time &lt; #{outLockDate} and lock_time &gt; '1971-01-01 00:00:01'
    </update>

    <update id="unLockOneLineByResourceUUid">
        update t_rds_datasync set lock_id = ''
        where instance_uuid = #{instanceUuid}
    </update>
</mapper>