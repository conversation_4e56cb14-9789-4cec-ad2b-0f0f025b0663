<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.bce.logic.rds.dao.mybatis.BatchOrderSyncMapper">
    <!-- 查询指定标识的订单数据   -->
    <select id="selectSyncOrder" resultType="com.baidu.bce.logic.rds.dao.model.BatchOrderSyncPO">
        SELECT id, updated_time, extra_orders, execute_step, create_time, user_id, order_uuid, order_status, order_type, instance_id, batch_orders
        FROM t_rds_batchordersync
        WHERE extra_orders = #{extraOrders}
        AND batch_orders = #{batchOrders}
        AND execute_step = 1
        ORDER BY id DESC FOR UPDATE
    </select>

    <!--    查询指定订单标识项且订单未进入执行流程的订单个数-->
    <select id="selectUnExecuteOrderCountByExtra" resultType="int">
        SELECT count(1)
        FROM t_rds_batchordersync
        WHERE extra_orders = #{extraOrders}
        AND batch_orders = #{batchOrders}
        AND execute_step = 1 FOR UPDATE
    </select>


    <!--    查询批量变配的订单-->
    <select id="selectBatOrdersByInstanceIds" resultType="java.lang.String">
        SELECT batch_orders
        FROM t_rds_batchordersync
        WHERE extra_orders = #{instanceIds}
        AND order_uuid = #{orderUuid}
        FOR UPDATE
    </select>

    <!--    根据订单ID查询批量变配的订单-->
    <select id="selectBatOrdersByOrderId" resultType="java.lang.String">
        SELECT batch_orders
        FROM t_rds_batchordersync
        WHERE order_uuid = #{orderUuid}
        LIMIT 1 FOR UPDATE
    </select>

    <!-- 查询所有未支付的预付费订单   -->
    <select id="selectNeedPurchaseOrder" resultType="com.baidu.bce.logic.rds.dao.model.BatchOrderSyncPO">
        SELECT id, updated_time, extra_orders, execute_step, create_time, user_id, order_uuid, order_status, order_type, instance_id, batch_orders
        FROM t_rds_batchordersync
        WHERE order_status = #{orderStatus}
        LIMIT 50
    </select>

    <!--    查询指定订单标识项且订单已进入执行器流程的订单个数-->
    <select id="selectExecuteOrderCountByExtra" resultType="int">
        SELECT count(1)
        FROM t_rds_batchordersync
        WHERE extra_orders = #{extraOrders}
        AND execute_step = 2
    </select>

    <!--    插入订单数据，目前是单条插入   -->
    <insert id="insertOneOrder">
        INSERT IGNORE INTO t_rds_batchordersync(instance_id,updated_time, create_time, extra_orders, execute_step, user_id, order_uuid, order_status, order_type)
        VALUES(
        #{batchOrderSyncPO.instanceId}, now(), #{batchOrderSyncPO.createTime}, #{batchOrderSyncPO.extraOrders}, 1, #{batchOrderSyncPO.userId},
        #{batchOrderSyncPO.orderUuid},#{batchOrderSyncPO.orderStatus}, #{batchOrderSyncPO.orderType})
    </insert>

    <!--    更新订单状态，进入订单执行器流程-->
    <update id="updateOrderStatus">
        UPDATE t_rds_batchordersync
        SET order_status = #{batchOrderSyncPO.orderStatus}, updated_time = now(), execute_step = 1
        WHERE order_uuid = #{batchOrderSyncPO.orderUuid}
    </update>

    <!--    更新订单状态，进入订单执行器流程-->
    <update id="updateCreatingStatus">
        UPDATE t_rds_batchordersync
        SET order_status = #{batchOrderSyncPO.orderStatus}, updated_time = now(), execute_step = 2
        WHERE order_uuid = #{batchOrderSyncPO.orderUuid}
    </update>

    <!--    回滚订单状态-->
    <update id="destroyOrderStatus">
        UPDATE t_rds_batchordersync
        SET order_status = #{orderStatus}, updated_time = now(), execute_step = 0
        WHERE order_uuid = #{orderUuid}
    </update>


    <!--    批量插入订单-->
    <insert id="insertBatchOrders">
        INSERT IGNORE INTO t_rds_batchordersync (instance_id,updated_time, create_time, extra_orders, execute_step, user_id,
        order_uuid, order_status, order_type, batch_orders)
        VALUES
        <foreach collection="batchOrderSyncPOs"  item="batchOrderSyncPO" separator=",">
            (#{batchOrderSyncPO.instanceId}, now(), #{batchOrderSyncPO.createTime}, #{batchOrderSyncPO.extraOrders}, 0, #{batchOrderSyncPO.userId},
            #{batchOrderSyncPO.orderUuid},#{batchOrderSyncPO.orderStatus}, #{batchOrderSyncPO.orderType}, #{batchOrderSyncPO.batchOrders})
        </foreach>
    </insert>


</mapper>