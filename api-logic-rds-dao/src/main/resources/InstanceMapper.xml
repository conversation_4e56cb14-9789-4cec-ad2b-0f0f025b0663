<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.bce.logic.rds.dao.mybatis.InstanceMapper">
    <resultMap type="com.baidu.bce.logic.rds.dao.model.InstancePO" id="instanceOnDcc">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="instance_id" property="instanceId"/>
        <result column="instance_uuid" property="instanceUuid"/>
        <result column="instance_name" property="instanceName"/>
        <result column="engine" property="engine"/>
        <result column="engine_version" property="engineVersion"/>
        <result column="application_type" property="applicationType"/>
        <result column="instance_status" property="instanceStatus"/>
        <result column="cpu_count" property="cpuCount"/>
        <result column="memory_capacity" property="memoryCapacity"/>
        <result column="volume_capacity" property="volumeCapacity"/>
        <result column="total_volume_capacity" property="totalVolumeCapacity"/>
        <result column="node_amount" property="nodeAmount"/>
        <result column="used_storage" property="usedStorage"/>
        <result column="instance_create_time" property="instanceCreateTime"/>
        <result column="instance_expire_time" property="instanceExpireTime"/>
        <result column="address" property="endpoint.address"/>
        <result column="port" property="endpoint.port"/>
        <result column="vnetIp" property="endpoint.vnetIp"/>
        <result column="inetIp" property="endpoint.inetIp"/>
        <result column="publicly_accessible" property="publiclyAccessible"/>
        <result column="eip_status" property="eipStatus"/>
        <result column="replication_type" property="replicationType"/>
        <result column="backup_days" property="backupPolicy.backupDays"/>
        <result column="backup_time" property="backupPolicy.backupTime"/>
        <result column="persistent" property="backupPolicy.persistent"/>
        <result column="expire_in_days" property="backupPolicy.expireInDays"/>
        <result column="backup_free_space" property="backupPolicy.backupFreeSpace"/>
        <result column="instance_type" property="instanceType"/>
        <result column="source_instance_id" property="sourceInstanceId"/>
        <result column="zone_names" property="zoneNames"/>
        <result column="vpc_uuid" property="vpcUuid"/>
        <result column="product_type" property="productType"/>
        <result column="super_user_flag" property="superUserFlag"/>
        <result column="resource_uuid" property="resourceUuid"/>
        <result column="order_uuid" property="orderUuid"/>
        <result column="order_status" property="orderStatus"/>
        <result column="source" property="source"/>
        <collection property="dccHosts" ofType="com.baidu.bce.logic.rds.dao.model.MachinePO">
            <result property="machineInstanceId"            column="machine_instance_id"/>
            <result property="instanceRole"            column="instance_role"/>
            <result property="machineInstanceName"            column="machine_instance_name"/>
        </collection>
    </resultMap>
    <insert id="batchInsertInstances">
        INSERT INTO t_rds_instance (updated_time, user_id, instance_id, instance_uuid, instance_name, engine,
        engine_version, instance_status, cpu_count,
        memory_capacity, volume_capacity, node_amount, instance_create_time,
        instance_expire_time, application_type, port, publicly_accessible,
        eip_status, replication_type, backup_days, backup_time, persistent, expire_in_days,
        instance_type, source_instance_id, zone_names, vpc_uuid,
        product_type, super_user_flag, order_uuid, source, machine_type, total_volume_capacity)
        VALUES
        <foreach collection="instancePOs"  item="instancePO" separator=",">
            (now(), #{instancePO.userId}, #{instancePO.instanceId}, #{instancePO.instanceUuid}, #{instancePO.instanceName},
            #{instancePO.engine}, #{instancePO.engineVersion}, #{instancePO.instanceStatus}, #{instancePO.cpuCount},
            #{instancePO.memoryCapacity}, #{instancePO.volumeCapacity}, #{instancePO.nodeAmount},
            #{instancePO.instanceCreateTime}, #{instancePO.instanceExpireTime}, #{instancePO.applicationType},
            #{instancePO.endpoint.port},#{instancePO.publiclyAccessible},
            #{instancePO.eipStatus}, #{instancePO.replicationType},
            #{instancePO.backupPolicy.backupDays}, #{instancePO.backupPolicy.backupTime},
            #{instancePO.backupPolicy.persistent}, #{instancePO.backupPolicy.expireInDays},
            #{instancePO.instanceType}, #{instancePO.sourceInstanceId},#{instancePO.zoneNames}, #{instancePO.vpcUuid},
            #{instancePO.productType}, #{instancePO.superUserFlag},#{instancePO.orderUuid}, #{instancePO.source},
            #{instancePO.machineType}, #{instancePO.totalVolumeCapacity})
        </foreach>
    </insert>

    <update id="updateInstanceByInstanceUuid" parameterType="java.util.Map">
        update t_rds_instance
        SET
        <foreach collection="valueMap" item="value" index="key" separator=",">
            ${key} = #{value}
        </foreach>
        where deleted = 0 and instance_uuid = #{instanceUuid} and user_id = #{userId}
    </update>
    <!--<select id="queryInstance" resultType="com.baidu.bce.logic.rds.dao.model.InstancePO">-->
        <!--SELECT updated_time, user_id, instance_id, instance_uuid, instance_name, engine, engine_version,-->
        <!--instance_status, cpu_count, memory_capacity, volume_capacity, node_amount, used_storage,-->
        <!--instance_create_time, instance_expire_time, application_type, address AS "endpoint.address",-->
        <!--port AS "endpoint.address", vnet_ip AS "endpoint.address", vnet_ip AS "endpoint.address",-->
        <!--publicly_accessible, eip_status, replication_type,-->
        <!--backup_days AS "backupPolicy.backupDays", backup_time AS "backupPolicy.backupTime",-->
        <!--persistent AS "backupPolicy.persistent", expire_in_days AS "backupPolicy.expireInDays",-->
        <!--backup_free_space AS "backupPolicy.backupFreeSpace",-->
        <!--instance_type, source_instance_id, zone_names, vpc_uuid,-->
        <!--product_type, super_user_flag, resource_uuid, order_uuid, order_status, source-->
        <!--FROM t_rds_instance-->
        <!--WHERE deleted = 0 and instance_uuid = #{instanceUuid} and user_id = #{userId}-->
    <!--</select>-->

    <select id="getAllStatusInstanceByUuid" resultType="com.baidu.bce.logic.rds.dao.model.InstancePO">
        SELECT updated_time, user_id, ins.instance_id, instance_uuid, instance_name, engine, engine_version,
        instance_status, cpu_count, memory_capacity, volume_capacity, node_amount, used_storage,
        instance_create_time, instance_expire_time, application_type, address,
        port, vnet_ip, vnet_ip,
        publicly_accessible, eip_status, replication_type,
        backup_days AS "backupPolicy.backupDays", backup_time AS "backupPolicy.backupTime",
        persistent AS "backupPolicy.persistent", expire_in_days AS "backupPolicy.expireInDays",
        backup_free_space AS "backupPolicy.backupFreeSpace",
        instance_type, source_instance_id, zone_names, vpc_uuid,
        product_type, super_user_flag, resource_uuid, order_uuid, order_status, source, machine_type, total_volume_capacity
--         sub.id as sub_id, sub.subnet_uuid, sub.logical_zone, sub.instance_id as sub_instance_id
        FROM t_rds_instance ins
--         left join t_rds_subnet sub on ins.instance_id = sub.instance_id
        WHERE instance_uuid = #{instanceUuid} and user_id = #{userId}
    </select>


    <select id="queryInstanceByInstanceUuid" resultType="com.baidu.bce.logic.rds.dao.model.InstancePO">
        SELECT updated_time, user_id, ins.instance_id, instance_uuid, instance_name, engine, engine_version,
        instance_status, cpu_count, memory_capacity, volume_capacity, node_amount, used_storage,
        instance_create_time, instance_expire_time, application_type, address AS "endpoint.address",
        port AS "endpoint.address", vnet_ip AS "endpoint.address", vnet_ip AS "endpoint.address",
        publicly_accessible, eip_status, replication_type,
        backup_days AS "backupPolicy.backupDays", backup_time AS "backupPolicy.backupTime",
        persistent AS "backupPolicy.persistent", expire_in_days AS "backupPolicy.expireInDays",
        backup_free_space AS "backupPolicy.backupFreeSpace",
        instance_type, source_instance_id, zone_names, vpc_uuid,
        product_type, super_user_flag, resource_uuid, order_uuid, order_status, source, machine_type, total_volume_capacity
--         sub.id as sub_id, sub.subnet_uuid, sub.logical_zone, sub.instance_id as sub_instance_id
        FROM t_rds_instance ins
--         left join t_rds_subnet sub on ins.instance_id = sub.instance_id
        WHERE deleted = 0 and instance_uuid = #{instanceUuid} and user_id = #{userId}
    </select>

    <!--<select id="queryInstanceByInstanceId" resultMap="instanceWithSubnet">-->
        <!--SELECT updated_time, user_id, ins.instance_id, instance_uuid, instance_name, engine, engine_version,-->
        <!--instance_status, cpu_count, memory_capacity, volume_capacity, node_amount, used_storage,-->
        <!--instance_create_time, instance_expire_time, application_type, address, port, vnet_ip, inet_ip,-->
        <!--publicly_accessible, eip_status, replication_type,-->
        <!--backup_days AS "backupPolicy.backupDays", backup_time AS "backupPolicy.backupTime",-->
        <!--persistent AS "backupPolicy.persistent", expire_in_days AS "backupPolicy.expireInDays",-->
        <!--backup_free_space AS "backupPolicy.backupFreeSpace",-->
        <!--instance_type, source_instance_id, zone_names, vpc_uuid,-->
        <!--product_type, super_user_flag, resource_uuid, order_uuid, order_status, source,-->
        <!--sub.id as sub_id, sub.subnet_uuid, sub.logical_zone, sub.instance_id as sub_instance_id-->
        <!--FROM t_rds_instance ins-->
        <!--left join t_rds_subnet sub on ins.instance_id = sub.instance_id-->
        <!--WHERE deleted = 0 and ins.instance_id = #{instanceId} and user_id = #{userId}-->
    <!--</select>-->

    <select id="queryInstanceByInstanceId" resultType="com.baidu.bce.logic.rds.dao.model.InstancePO">
        SELECT updated_time, user_id, ins.instance_id, instance_uuid, instance_name, engine, engine_version,
        instance_status, cpu_count, memory_capacity, volume_capacity, node_amount, used_storage,
        instance_create_time, instance_expire_time, application_type, address, port, vnet_ip, inet_ip,
        publicly_accessible, eip_status, replication_type,
        backup_days AS "backupPolicy.backupDays", backup_time AS "backupPolicy.backupTime",
        persistent AS "backupPolicy.persistent", expire_in_days AS "backupPolicy.expireInDays",
        backup_free_space AS "backupPolicy.backupFreeSpace",
        instance_type, source_instance_id, zone_names, vpc_uuid,
        product_type, super_user_flag, resource_uuid, order_uuid, order_status, source, machine_type, total_volume_capacity
--         sub.id as sub_id, sub.subnet_uuid, sub.logical_zone, sub.instance_id as sub_instance_id
        FROM t_rds_instance ins
--         left join t_rds_subnet sub on ins.instance_id = sub.instance_id
        WHERE deleted = 0 and ins.instance_id = #{instanceId} and user_id = #{userId}
    </select>

    <select id="queryAllInstanceByInstanceId" resultType="com.baidu.bce.logic.rds.dao.model.InstancePO">
        SELECT updated_time, user_id, ins.instance_id, instance_uuid, instance_name, engine, engine_version,
        instance_status, cpu_count, memory_capacity, volume_capacity, node_amount, used_storage,
        instance_create_time, instance_expire_time, application_type, address, port, vnet_ip, inet_ip,
        publicly_accessible, eip_status, replication_type,
        backup_days AS "backupPolicy.backupDays", backup_time AS "backupPolicy.backupTime",
        persistent AS "backupPolicy.persistent", expire_in_days AS "backupPolicy.expireInDays",
        backup_free_space AS "backupPolicy.backupFreeSpace",
        instance_type, source_instance_id, zone_names, vpc_uuid,
        product_type, super_user_flag, resource_uuid, order_uuid, order_status, source, machine_type, total_volume_capacity
--         sub.id as sub_id, sub.subnet_uuid, sub.logical_zone, sub.instance_id as sub_instance_id
        FROM t_rds_instance ins
--         left join t_rds_subnet sub on ins.instance_id = sub.instance_id
        WHERE ins.instance_id = #{instanceId} and user_id = #{userId}
    </select>

    <select id="selectSubnetListByInstanceId" resultType="com.baidu.bce.logic.rds.dao.model.SubnetPO">
        SELECT id, subnet_uuid, logical_zone, instance_id FROM t_rds_subnet
        WHERE instance_id = #{instanceId}
    </select>

    <select id="getMachineList" resultType="com.baidu.bce.logic.rds.dao.model.MachinePO">
        SELECT id, instance_id, machine_instance_id, instance_role, td.name as "machine_instance_name"
        FROM t_rds_machine tm
        left join t_dedicated_hypervisor td on tm.machine_instance_id=td.dedicated_hypervisor_uuid
        WHERE instance_id = #{instanceId}
    </select>

    <select id="getMachineListByUuid" resultType="com.baidu.bce.logic.rds.dao.model.MachinePO">
        SELECT tm.id, tm.instance_id, machine_instance_id, instance_role, td.name as "machine_instance_name",
        td.status as "machine_instance_status"
        FROM t_rds_instance ti
        left join t_rds_machine tm on ti.instance_id=tm.instance_id
        left join t_dedicated_hypervisor td on tm.machine_instance_id=td.dedicated_hypervisor_uuid
        WHERE ti.instance_uuid = #{instanceUuid}
    </select>

    <select id="getInstanceListByOrderUuid" resultType="com.baidu.bce.logic.rds.dao.model.InstancePO">
        SELECT updated_time, user_id, instance_id, instance_uuid, instance_name, engine, engine_version,
        instance_status, cpu_count, memory_capacity, volume_capacity, node_amount, used_storage,
        instance_create_time, instance_expire_time, application_type, address AS "endpoint.address",
        port AS "endpoint.address", vnet_ip AS "endpoint.address", vnet_ip AS "endpoint.address",
        publicly_accessible, eip_status, replication_type,
        backup_days AS "backupPolicy.backupDays", backup_time AS "backupPolicy.backupTime",
        persistent AS "backupPolicy.persistent", expire_in_days AS "backupPolicy.expireInDays",
        backup_free_space AS "backupPolicy.backupFreeSpace",
        instance_type, source_instance_id, zone_names, vpc_uuid,
        product_type, super_user_flag, resource_uuid, order_uuid, order_status, source, machine_type, total_volume_capacity
        FROM t_rds_instance
        WHERE deleted = 0 and order_uuid = #{orderUuid} and user_id = #{userId}
    </select>

    <select id="getInstanceListByInstanceStatus" resultType="com.baidu.bce.logic.rds.dao.model.InstancePO">
        SELECT updated_time, user_id, instance_id, instance_uuid, instance_name, engine, engine_version,
        instance_status, cpu_count, memory_capacity, volume_capacity, node_amount, used_storage,
        instance_create_time, instance_expire_time, application_type, address AS "endpoint.address",
        port AS "endpoint.address", vnet_ip AS "endpoint.address", vnet_ip AS "endpoint.address",
        publicly_accessible, eip_status, replication_type,
        backup_days AS "backupPolicy.backupDays", backup_time AS "backupPolicy.backupTime",
        persistent AS "backupPolicy.persistent", expire_in_days AS "backupPolicy.expireInDays",
        backup_free_space AS "backupPolicy.backupFreeSpace",
        instance_type, source_instance_id, zone_names, vpc_uuid,
        product_type, super_user_flag, resource_uuid, order_uuid, order_status, source, machine_type, total_volume_capacity
        FROM t_rds_instance
        WHERE deleted = 0 and instance_status = #{instanceStatus} and user_id = #{userId} and machine_type=''
    </select>

    <select id="getInstanceListOnDccByInstanceStatus" resultMap="instanceOnDcc">
        SELECT ti.id, ti.updated_time, ti.user_id, ti.instance_id, instance_uuid, instance_name, engine, engine_version,
        instance_status, cpu_count, memory_capacity, volume_capacity, node_amount, used_storage,
        instance_create_time, instance_expire_time, application_type, address AS "endpoint.address",
        port AS "endpoint.address", vnet_ip AS "endpoint.address", vnet_ip AS "endpoint.address",
        publicly_accessible, eip_status, replication_type,
        backup_days AS "backupPolicy.backupDays", backup_time AS "backupPolicy.backupTime",
        persistent AS "backupPolicy.persistent", expire_in_days AS "backupPolicy.expireInDays",
        backup_free_space AS "backupPolicy.backupFreeSpace",
        instance_type, source_instance_id, zone_names, vpc_uuid,
        ti.product_type, super_user_flag, ti.resource_uuid, ti.order_uuid, ti.order_status, ti.source, ti.machine_type,
        total_volume_capacity, tm.machine_instance_id, tm.instance_role, td.name as "machine_instance_name"
        FROM t_rds_instance ti
        left join t_rds_machine tm on ti.instance_id=tm.instance_id
        left join t_dedicated_hypervisor td on tm.machine_instance_id=td.dedicated_hypervisor_uuid
        WHERE ti.deleted = 0 and instance_status = #{instanceStatus} and ti.user_id = #{userId} and ti.machine_type='dcc'
    </select>

    <select id="queryInstanceId" resultType="string">
        SELECT instance_id FROM t_rds_instance
        WHERE instance_id = #{instanceId} or instance_uuid = #{instanceId} LIMIT 1
    </select>

    <select id="queryInstanceUuid" resultType="string">
        SELECT instance_uuid FROM t_rds_instance
        WHERE instance_id = #{instanceId} or instance_uuid = #{instanceId} LIMIT 1
    </select>

    <select id="idMapList" resultType="com.baidu.bce.logic.rds.dao.model.IdMapPO">
        SELECT id, instance_id, instance_uuid FROM t_rds_instance
        WHERE instance_uuid IN
        <foreach collection="longIds" item="longId" separator="," open="(" close=")">
            #{longId}
        </foreach>
    </select>

    <select id="idMapListV2" resultType="com.baidu.bce.logic.rds.dao.model.IdMapV2PO">
        SELECT id, instance_id, instance_uuid FROM t_rds_instance
        WHERE user_id = #{userId} and instance_uuid IN
        <foreach collection="longIds" item="longId" separator="," open="(" close=")">
            #{longId}
        </foreach>
    </select>
    <select id="getShortIdsByOrderId" resultType="java.lang.String">
        SELECT instance_id FROM t_rds_instance WHERE order_uuid = #{orderId}
    </select>
    <select id="getMasterShortIds" resultType="java.lang.String">
        SELECT instance_id FROM t_rds_instance
        WHERE (instance_uuid IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        or instance_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach> )
        and (instance_type = 'master' or instance_type = 'financial')
        UNION
        SELECT source_instance_id as instance_id FROM t_rds_instance
        WHERE (instance_uuid IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        or instance_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach> )
        and (instance_type = 'rdsproxy' or instance_type = 'rdsReplica' or instance_type = 'readReplica')
    </select>

    <update id="updateSyncData">
        update t_rds_instance
        set  engine_version = #{instancePO.engineVersion},
        instance_status = #{instancePO.instanceStatus},
        used_storage = #{instancePO.usedStorage},
        vnet_ip = #{instancePO.endpoint.vnetIp},
        inet_ip = #{instancePO.endpoint.inetIp},
        eip_status = #{instancePO.eipStatus},
        replication_type = #{instancePO.replicationType},
        instance_type = #{instancePO.instanceType},
        volume_capacity = #{instancePO.volumeCapacity}
        <if test="instancePO.instanceStatus == 'deleted' or instancePO.instanceStatus == 'deleting'">
            ,deleted = 1
        </if>
        where deleted = 0 and instance_uuid = #{instancePO.instanceUuid} and user_id = #{instancePO.userId}
    </update>

    <update id="deleteCreateFailedInstance">
        UPDATE t_rds_instance
        set deleted = 1, instance_status = 'failed', deleted_time = now()
        WHERE deleted = 0 AND order_uuid = #{orderUuid} and user_id = #{userId}
    </update>

    <update id="updateInstanceUuid">
        update t_rds_instance
        set instance_uuid = #{instanceUuid}
        where instance_id = #{instanceId} and instance_uuid = #{instanceId}
    </update>

    <update id="deleteInstanceByInstanceUuids">
        UPDATE t_rds_instance
        set deleted = 1, instance_status = 'deleted', deleted_time = now()
        WHERE deleted = 0 and user_id = #{userId}
        and (instance_uuid in
        <foreach collection="instanceUuids" item="uuid" separator="," open="(" close=")">
            #{uuid}
        </foreach>
        or source_instance_id IN
        <foreach collection="instanceUuids" item="uuid" separator="," open="(" close=")">
            #{uuid}
        </foreach>
        )
    </update>



    <update id="updateInstanceById">
        UPDATE t_rds_instance
        set instance_uuid = #{instancePO.instanceUuid},
        instance_name = #{instancePO.instanceName},
        instance_status = #{instancePO.instanceStatus},
        used_storage = #{instancePO.usedStorage},
        instance_create_time = #{instancePO.instanceCreateTime},
        <if test="instancePO.instanceExpireTime != null">
            instance_expire_time = #{instancePO.instanceExpireTime},
        </if>
        address = #{instancePO.endpoint.address},
        port = #{instancePO.endpoint.port},
        vnet_ip = #{instancePO.endpoint.vnetIp},
        inet_ip = #{instancePO.endpoint.inetIp},
        replication_type = #{instancePO.replicationType},
        backup_free_space = #{instancePO.backupPolicy.backupFreeSpace},
        resource_uuid = #{instancePO.resourceUuid}
        WHERE deleted = 0 AND instance_id = #{instancePO.instanceId}
        and user_id = #{instancePO.userId} and order_uuid = #{instancePO.orderUuid}
    </update>

    <update id="batchUpdateInstanceStatusByOrderId">
        UPDATE t_rds_instance
        set instance_status = #{status}
        WHERE deleted = 0 and user_id = #{userId} and order_uuid = #{orderUuid}
    </update>

    <insert id="insertOldInstance">
        INSERT INTO t_rds_instance (updated_time, user_id, instance_id, instance_uuid,
        instance_status,
        <if test="instancePO.sourceInstanceId != null and instancePO.sourceInstanceId !=''">
            source_instance_id,
        </if>
        source)
        VALUES
        (now(), #{instancePO.userId}, #{instancePO.instanceId}, #{instancePO.instanceUuid},
        #{instancePO.instanceStatus},
        <if test="instancePO.sourceInstanceId != null and instancePO.sourceInstanceId !=''">
            #{instancePO.sourceInstanceId},
        </if>
        'sync')
    </insert>

    <insert id="batchInsertSubnetPOs">
        INSERT INTO t_rds_subnet (subnet_uuid, logical_zone, instance_id)
        VALUES
        <foreach collection="subnetPOs"  item="subnetPO" separator=",">
            (#{subnetPO.subnetUuid},#{subnetPO.logicalZone}, #{subnetPO.instanceId})
        </foreach>
    </insert>

    <insert id="batchInsertMachinePOs">
        INSERT INTO t_rds_machine (instance_id, machine_instance_id, instance_role, machine_type)
        VALUES
        <foreach collection="machinePOs"  item="machinePO" separator=",">
            (#{machinePO.instanceId},#{machinePO.machineInstanceId}, #{machinePO.instanceRole}, #{machinePO.machineType})
        </foreach>
    </insert>

    <select id="queryReadReplicaInstanceIds" resultType="string">
        SELECT instance_id FROM t_rds_instance
        WHERE source_instance_id = #{masterId} and instance_type = 'readReplica' and deleted = 0
    </select>

    <select id="queryProxyInstanceIds" resultType="string">
        SELECT instance_id FROM t_rds_instance
        WHERE source_instance_id = #{masterId} and instance_type = 'rdsproxy' and deleted = 0
    </select>

    <select id="countNotDeleted" resultType="int">
        SELECT count(1) FROM t_rds_instance
        WHERE (source_instance_id = #{instanceId} or instance_id = #{instanceId})
        and instance_status != 'available' and instance_status != 'lockExpiration'
        and deleted = 0 and user_id = #{userId}
    </select>

    <select id="countCreatingReplica" resultType="int">
        SELECT count(1) FROM t_rds_instance
        WHERE source_instance_id = #{instanceId} and instance_status = 'creating'
        and deleted = 0 and user_id = #{userId} and instance_type = 'readReplica'
    </select>

    <select id="countProxy" resultType="int">
        SELECT count(1) FROM t_rds_instance
        WHERE source_instance_id = #{instanceId} and instance_type = 'rdsproxy'
        and deleted = 0 and user_id = #{userId}
    </select>

    <select id="queryInstanceByInstanceIds" resultType="com.baidu.bce.logic.rds.dao.model.InstancePO">
        SELECT updated_time, user_id, ins.instance_id, instance_uuid, instance_name, engine, engine_version,
        instance_status, cpu_count, memory_capacity, volume_capacity, node_amount, used_storage,
        instance_create_time, instance_expire_time, application_type, address, port, vnet_ip, inet_ip,
        publicly_accessible, eip_status, replication_type,
        backup_days AS "backupPolicy.backupDays", backup_time AS "backupPolicy.backupTime",
        persistent AS "backupPolicy.persistent", expire_in_days AS "backupPolicy.expireInDays",
        backup_free_space AS "backupPolicy.backupFreeSpace",
        instance_type, source_instance_id, zone_names, vpc_uuid,
        product_type, super_user_flag, resource_uuid, order_uuid, order_status, source, machine_type, total_volume_capacity
--         sub.id as sub_id, sub.subnet_uuid, sub.logical_zone, sub.instance_id as sub_instance_id
        FROM t_rds_instance ins
--         left join t_rds_subnet sub on ins.instance_id = sub.instance_id
        WHERE deleted = 0 and ins.instance_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and user_id = #{userId}
    </select>

    <select id="listAllAccountId" resultType="string">
        SELECT distinct ins.user_id
        FROM t_rds_instance ins
        WHERE deleted = 0;
    </select>

    <select id="selectByIds" resultType="com.baidu.bce.logic.rds.dao.model.InstancePO">
        SELECT id, instance_id, instance_uuid, source_instance_id FROM t_rds_instance
        WHERE
        instance_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        OR
        instance_uuid IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

</mapper>