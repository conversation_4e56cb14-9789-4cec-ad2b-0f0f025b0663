package com.baidu.bce.logic.rds.dao.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.sql.Timestamp;

/**
 * 内网连接检查时，需要用到的实体类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class InnerConnectCheckPO {
    private long id;
    private String connectId;
    private String rdsInstanceId;
    private String rdsInstanceStatus;
    private String bccInstanceId;
    private String bccInstanceName;
    private String bccInstanceStatus;
    private Timestamp updateTime;
    private Timestamp createTime;
    private String connectStatus;
    private String userId;
    private String bccVpcId;
    private String bccVpcShortId;
    private String bccVpcName;
    private String bccVpcCidr;
    private String bccVnetIp;
    private String rdsVpcId;
    private String rdsVpcShortId;
    private String rdsVpcName;
    private String rdsVpcCidr;
    private String identicalVpc;
    private String bccInWhiteList;
    private String bccInstanceUuid;

    public String getRdsInstanceStatus() {
        return rdsInstanceStatus;
    }

    public void setRdsInstanceStatus(String rdsInstanceStatus) {
        this.rdsInstanceStatus = rdsInstanceStatus;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getConnectId() {
        return connectId;
    }

    public void setConnectId(String connectId) {
        this.connectId = connectId;
    }

    public String getRdsInstanceId() {
        return rdsInstanceId;
    }

    public void setRdsInstanceId(String rdsInstanceId) {
        this.rdsInstanceId = rdsInstanceId;
    }

    public String getBccInstanceId() {
        return bccInstanceId;
    }

    public void setBccInstanceId(String bccInstanceId) {
        this.bccInstanceId = bccInstanceId;
    }

    public String getBccInstanceName() {
        return bccInstanceName;
    }

    public void setBccInstanceName(String bccInstanceName) {
        this.bccInstanceName = bccInstanceName;
    }

    public String getBccInstanceStatus() {
        return bccInstanceStatus;
    }

    public void setBccInstanceStatus(String bccInstanceStatus) {
        this.bccInstanceStatus = bccInstanceStatus;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(String connectStatus) {
        this.connectStatus = connectStatus;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBccVpcId() {
        return bccVpcId;
    }

    public void setBccVpcId(String bccVpcId) {
        this.bccVpcId = bccVpcId;
    }

    public String getBccVpcShortId() {
        return bccVpcShortId;
    }

    public void setBccVpcShortId(String bccVpcShortId) {
        this.bccVpcShortId = bccVpcShortId;
    }

    public String getBccVpcName() {
        return bccVpcName;
    }

    public void setBccVpcName(String bccVpcName) {
        this.bccVpcName = bccVpcName;
    }

    public String getBccVpcCidr() {
        return bccVpcCidr;
    }

    public void setBccVpcCidr(String bccVpcCidr) {
        this.bccVpcCidr = bccVpcCidr;
    }

    public String getBccVnetIp() {
        return bccVnetIp;
    }

    public void setBccVnetIp(String bccVnetIp) {
        this.bccVnetIp = bccVnetIp;
    }

    public String getRdsVpcId() {
        return rdsVpcId;
    }

    public void setRdsVpcId(String rdsVpcId) {
        this.rdsVpcId = rdsVpcId;
    }

    public String getRdsVpcShortId() {
        return rdsVpcShortId;
    }

    public void setRdsVpcShortId(String rdsVpcShortId) {
        this.rdsVpcShortId = rdsVpcShortId;
    }

    public String getRdsVpcName() {
        return rdsVpcName;
    }

    public void setRdsVpcName(String rdsVpcName) {
        this.rdsVpcName = rdsVpcName;
    }

    public String getRdsVpcCidr() {
        return rdsVpcCidr;
    }

    public void setRdsVpcCidr(String rdsVpcCidr) {
        this.rdsVpcCidr = rdsVpcCidr;
    }

    public String getIdenticalVpc() {
        return identicalVpc;
    }

    public void setIdenticalVpc(String identicalVpc) {
        this.identicalVpc = identicalVpc;
    }

    public String getBccInWhiteList() {
        return bccInWhiteList;
    }

    public void setBccInWhiteList(String bccInWhiteList) {
        this.bccInWhiteList = bccInWhiteList;
    }

    public String getBccInstanceUuid() {
        return bccInstanceUuid;
    }

    public void setBccInstanceUuid(String bccInstanceUuid) {
        this.bccInstanceUuid = bccInstanceUuid;
    }
}
