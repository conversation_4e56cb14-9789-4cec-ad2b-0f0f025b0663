package com.baidu.bce.logic.rds.dao.mybatis;

import com.baidu.bce.logic.rds.dao.model.FlavorPO;
import com.baidu.bce.logic.rds.dao.model.ZoneFlavorLimitPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by luping03 on 18/2/9.
 */
@Component
public interface FlavorMapper {
    int selectFlavorCountByUniqueKey(@Param("flavorPO") FlavorPO flavorPO);

    FlavorPO selectFlavorByUniqueKey(@Param("flavorPO") FlavorPO flavorPO);

    List<ZoneFlavorLimitPO> getZoneFlavorLimitPOList();

    ZoneFlavorLimitPO getZoneFlavorLimitPO(@Param("logicalZoneName") String logicalZoneName,
                                           @Param("userId") String userId);
}
