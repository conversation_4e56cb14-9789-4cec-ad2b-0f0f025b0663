package com.baidu.bce.logic.rds.dao.mybatis;


import com.baidu.bce.logic.rds.dao.model.BatchOrderSyncPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BatchOrderSyncMapper {

    /**
     * 查询指定标识的订单数据
     * @return
     */
    List<BatchOrderSyncPO> selectSyncOrder(@Param("extraOrders") String extraOrders,
                                           @Param("batchOrders") String batchOrders);

    /**
     * 插入一条订单，目前是单条插入
     * @param batchOrderSyncPO
     */
    void insertOneOrder(@Param("batchOrderSyncPO")BatchOrderSyncPO batchOrderSyncPO);

    /**
     * 查询指定订单标识项且订单 未进入 执行器流程的订单个数
     * @param extraOrders
     * @return 指定标识的订单个数
     */
    int selectUnExecuteOrderCountByExtra(@Param("extraOrders") String extraOrders,
                                         @Param("batchOrders") String batchOrders);

    /**
     * 查询指定订单标识项且订单 已进入 执行器流程的订单个数
     * @param extraOrders
     * @return 指定标识的订单个数
     */
    int selectExecuteOrderCountByExtra(@Param("extraOrders") String extraOrders);

    /**
     * 查询批量操作的订单列表
     * @param instanceIds
     * @return
     */
    String selectBatOrdersByInstanceIds(@Param("instanceIds") String instanceIds,
                                        @Param("orderUuid") String orderUuid);

    /**
     * 根据订单 ID 查询批量订单
     * @param orderUuid
     * @return
     */
    String selectBatOrdersByOrderId(@Param("orderUuid") String orderUuid);

    /**
     * 更新订单状态，进入订单执行器流程 READY_FOR_CREATE
     * @param batchOrderSyncPO
     */
    void updateOrderStatus(@Param("batchOrderSyncPO")BatchOrderSyncPO batchOrderSyncPO);

    /**
     * 更新订单状态，进入订单执行器流程 CREATING
     * @param batchOrderSyncPO
     */
    void updateCreatingStatus(@Param("batchOrderSyncPO")BatchOrderSyncPO batchOrderSyncPO);

    /**
     * 订单批量入库，更新状态为 INIT
     * @param batchOrderSyncPOs
     */
    void insertBatchOrders(@Param("batchOrderSyncPOs")List<BatchOrderSyncPO> batchOrderSyncPOs);


    /**
     * 查询库中所有未支付的预付费订单
     * @param orderStatus
     * @return
     */
    List<BatchOrderSyncPO> selectNeedPurchaseOrder(@Param("orderStatus") String orderStatus);

    /**
     * 回滚订单状态
     * @param orderStatus
     * @param orderUuid
     */
    void destroyOrderStatus(@Param("orderStatus") String orderStatus, @Param("orderUuid") String orderUuid);


}

