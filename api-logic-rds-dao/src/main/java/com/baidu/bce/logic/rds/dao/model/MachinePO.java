package com.baidu.bce.logic.rds.dao.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.sql.Timestamp;

/**
 * Created by luping03 on 18/3/27.
 */
public class MachinePO {
    private int id;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;

    private String instanceId;
    private String machineInstanceId;
    private String machineInstanceName;
    private String machineInstanceStatus;
    private boolean available;
    private String instanceRole;
    private String machineType = "dcc";

    private String azone;

    public MachinePO() {
    }

    public MachinePO(String machineInstanceId, String instanceRole) {
        this.machineInstanceId = machineInstanceId;
        this.instanceRole = instanceRole;
    }

    public String getMachineInstanceStatus() {
        return machineInstanceStatus;
    }

    public void setMachineInstanceStatus(String machineInstanceStatus) {
        this.machineInstanceStatus = machineInstanceStatus;
        if (!"deleted".equalsIgnoreCase(machineInstanceStatus)) {
            setAvailable(true);
        }
    }

    public boolean getAvailable() {
        return available;
    }

    public void setAvailable(boolean available) {
        this.available = available;
    }

    public String getMachineInstanceName() {
        return machineInstanceName;
    }

    public void setMachineInstanceName(String machineInstanceName) {
        this.machineInstanceName = machineInstanceName;
    }

    public String getAzone() {
        return azone;
    }

    public void setAzone(String azone) {
        this.azone = azone;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Timestamp getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Timestamp updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getMachineInstanceId() {
        return machineInstanceId;
    }

    public void setMachineInstanceId(String machineInstanceId) {
        this.machineInstanceId = machineInstanceId;
    }

    public String getInstanceRole() {
        return instanceRole;
    }

    public void setInstanceRole(String instanceRole) {
        this.instanceRole = instanceRole;
    }

    public String getMachineType() {
        return machineType;
    }

    public void setMachineType(String machineType) {
        this.machineType = machineType;
    }
}
