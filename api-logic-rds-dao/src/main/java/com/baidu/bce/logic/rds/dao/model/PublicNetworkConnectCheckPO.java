package com.baidu.bce.logic.rds.dao.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.sql.Timestamp;

/**
 * 外网链接检测时 需要用到的实体类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PublicNetworkConnectCheckPO {
    private long id;
    private String connectId;
    private String rdsInstanceId;
    private String inetIp;
    private String inetStatus;
    private String rdsInstanceStatus;
    private Timestamp updateTime;
    private Timestamp createTime;
    private String connectStatus;
    private String userId;
    private String bccInWhiteList;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getConnectId() {
        return connectId;
    }

    public void setConnectId(String connectId) {
        this.connectId = connectId;
    }

    public String getRdsInstanceId() {
        return rdsInstanceId;
    }

    public void setRdsInstanceId(String rdsInstanceId) {
        this.rdsInstanceId = rdsInstanceId;
    }

    public String getInetIp() {
        return inetIp;
    }

    public void setInetIp(String inetIp) {
        this.inetIp = inetIp;
    }

    public String getInetStatus() {
        return inetStatus;
    }

    public void setInetStatus(String inetStatus) {
        this.inetStatus = inetStatus;
    }

    public String getRdsInstanceStatus() {
        return rdsInstanceStatus;
    }

    public void setRdsInstanceStatus(String rdsInstanceStatus) {
        this.rdsInstanceStatus = rdsInstanceStatus;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(String connectStatus) {
        this.connectStatus = connectStatus;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBccInWhiteList() {
        return bccInWhiteList;
    }

    public void setBccInWhiteList(String bccInWhiteList) {
        this.bccInWhiteList = bccInWhiteList;
    }
}
