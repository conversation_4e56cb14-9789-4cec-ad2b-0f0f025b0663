package com.baidu.bce.logic.rds.dao.model;

import javax.validation.constraints.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2014/6/6.
 */

public class SnapshotPolicy {

    @Pattern(regexp = "^((([0-1]?[0-9])|([2][0-3])):([0-5]?[0-9])(:([0-5]?[0-9]))?Z)$", message = "19:00:00Z")
    private String backupTime = "00:00:00Z";

    @Pattern(regexp = "^([0-6],){0,6}[0-6]$", message = "0,1,2,3,4,5,6")
    private String backupDays = "0,1,2,3,4,5,6";

    private Boolean persistent = false;

    private int expireInDays;

    private int backupFreeSpace;

    private int logBackupRetainDays;

    private String dataBackupType;

    // 是否开启高频备份，默认为 false
    private boolean incrementalDataBackupEnable;

    // 增量数据备份间隔（秒）最小粒度是小时级别
    private Integer incrementalDataBackupInterval;

    // 最新数据备份保留天数（实例删除后），天级别
    private Integer latestDataBackupRetainDays;

    // console 与 前端专用字段，管控不感知，
    private Integer retentionPeriod;

    // console 与 前端专用字段，管控不感知
    private String timeUnit = "week";

    public SnapshotPolicy() {
    }

    public SnapshotPolicy(String backupTime, String backupDays,
                          Boolean persistent, int expireInDays, int backupFreeSpace, int logBackupRetainDays) {
        this.backupTime = backupTime;
        this.backupDays = backupDays;
        this.backupFreeSpace = backupFreeSpace;
        this.expireInDays = expireInDays;
        this.persistent = persistent;
        this.logBackupRetainDays = logBackupRetainDays;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        SnapshotPolicy that = (SnapshotPolicy) o;

        if (!backupDays.equals(that.backupDays)) {
            return false;
        }
        if (!backupTime.equals(that.backupTime)) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = backupTime.hashCode();
        result = 31 * result + backupDays.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "BackupPolicy{"
                + "backupTime='" + backupTime + '\''
                + ", backupDays='" + backupDays + '\''
                + ", persistent='" + persistent + '\''
                + ", expireInDays='" + expireInDays + '\''
                + ", backupFreeSpace='" + backupFreeSpace + '\''
                + '}';
    }

    public boolean getIncrementalDataBackupEnable() {
        return incrementalDataBackupEnable;
    }

    public void setIncrementalDataBackupEnable(boolean incrementalDataBackupEnable) {
        this.incrementalDataBackupEnable = incrementalDataBackupEnable;
    }

    public Integer getIncrementalDataBackupInterval() {
        return incrementalDataBackupInterval;
    }

    public void setIncrementalDataBackupInterval(Integer incrementalDataBackupInterval) {
        this.incrementalDataBackupInterval = incrementalDataBackupInterval;
    }

    public Integer getLatestDataBackupRetainDays() {
        return latestDataBackupRetainDays;
    }

    public void setLatestDataBackupRetainDays(Integer latestDataBackupRetainDays) {
        this.latestDataBackupRetainDays = latestDataBackupRetainDays;
    }

    public Integer getRetentionPeriod() {
        return retentionPeriod;
    }

    public void setRetentionPeriod(Integer retentionPeriod) {
        this.retentionPeriod = retentionPeriod;
    }

    public String getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(String timeUnit) {
        this.timeUnit = timeUnit;
    }

    public int getBackupFreeSpace() {
        return backupFreeSpace;
    }

    public void setBackupFreeSpace(int backupFreeSpace) {
        this.backupFreeSpace = backupFreeSpace;
    }

    public String getBackupDays() {
        return backupDays;
    }

    public void setBackupDays(String backupDays) {
        this.backupDays = backupDays;
    }

    public Boolean getPersistent() {
        return persistent;
    }

    public void setPersistent(Boolean persistent) {
        this.persistent = persistent;
    }

    public String getBackupTime() {
        return backupTime;
    }

    public void setBackupTime(String backupTime) {
        this.backupTime = backupTime;
    }

    public int getExpireInDays() {
        return expireInDays;
    }

    public void setExpireInDays(int expireInDays) {
        this.expireInDays = expireInDays;
    }

    public SnapshotPolicy withBackupTime(final String backupTime) {
        this.backupTime = backupTime;
        return this;
    }

    public SnapshotPolicy withBackupDays(final String backupDays) {
        this.backupDays = backupDays;
        return this;
    }

    public int getLogBackupRetainDays() {
        return logBackupRetainDays;
    }

    public void setLogBackupRetainDays(int logBackupRetainDays) {
        this.logBackupRetainDays = logBackupRetainDays;
    }

    public String getDataBackupType() {
        return dataBackupType;
    }

    public void setDataBackupType(String dataBackupType) {
        this.dataBackupType = dataBackupType;
    }
}
