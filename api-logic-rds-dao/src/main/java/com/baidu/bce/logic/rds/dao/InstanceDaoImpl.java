package com.baidu.bce.logic.rds.dao;

import com.baidu.bce.internalsdk.trail.util.JsonUtil;
import com.baidu.bce.logic.rds.dao.model.IdMapPO;
import com.baidu.bce.logic.rds.dao.model.IdMapV2PO;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.MachinePO;
import com.baidu.bce.logic.rds.dao.model.SubnetPO;
import com.baidu.bce.logic.rds.dao.mybatis.InstanceMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by luping03 on 17/12/11.
 */
@Service
public class InstanceDaoImpl implements InstanceDao {

    private static final Logger LOGGER = LoggerFactory.getLogger(InstanceDaoImpl.class);

    public static final String DELETED_INSTANCE_STATUS = "deleted";

    @Autowired
    private InstanceMapper instanceMapper;

    @Override
    @Transactional
    public void batchInsertInstances(List<InstancePO> instancePOs) {
        instanceMapper.batchInsertInstances(instancePOs);
        List<SubnetPO> subnetPOs = new ArrayList<>();
        List<MachinePO> machinePOS = new ArrayList<>();
        for (InstancePO instancePO : instancePOs) {
            subnetPOs.addAll(instancePO.getSubnetIds());
            if ("dcc".equalsIgnoreCase(instancePO.getMachineType())) {
                machinePOS.addAll(instancePO.getDccHosts());
            }
        }
        if (subnetPOs.size() > 0) {
            instanceMapper.batchInsertSubnetPOs(subnetPOs);
        }
        if (machinePOS.size() > 0) {
            instanceMapper.batchInsertMachinePOs(machinePOS);
        }
    }

    @Override
    public String queryInstanceId(String instanceId) {
        return instanceMapper.queryInstanceId(instanceId);
    }

    @Override
    public String queryInstanceUuid(String instanceId) {
        return instanceMapper.queryInstanceUuid(instanceId);
    }

    @Override
    public List<IdMapPO> idMapList(List<String> longIds) {
        return instanceMapper.idMapList(longIds);
    }

    @Override
    public List<String> getMasterShortIds(Set<String> ids) {
        return instanceMapper.getMasterShortIds(ids);
    }

    @Override
    public List<String> getShortIdsByOrderId(String orderId) {
        return instanceMapper.getShortIdsByOrderId(orderId);
    }

    @Override
    public void updateSyncData(InstancePO instancePO) {
        instanceMapper.updateSyncData(instancePO);
    }

    @Override
    public InstancePO queryInstanceByInstanceId(String instanceId, String userId) {
        InstancePO instancePO = instanceMapper.queryInstanceByInstanceId(instanceId, userId);
        if (instancePO == null) {
            return null;
        }
        instancePO.setSubnetIds(instanceMapper.selectSubnetListByInstanceId(instancePO.getInstanceId()));
        packInstanceReadReplicaPo(instancePO, userId);
        if ("dcc".equalsIgnoreCase(instancePO.getMachineType())) {
            instancePO.setDccHosts(instanceMapper.getMachineList(instancePO.getInstanceId()));
        }
        return instancePO;
    }

    @Override
    public InstancePO queryAllInstanceByInstanceId(String instanceId, String userId) {
        InstancePO instancePO = instanceMapper.queryAllInstanceByInstanceId(instanceId, userId);
        if (instancePO == null) {
            return null;
        }
        instancePO.setSubnetIds(instanceMapper.selectSubnetListByInstanceId(instancePO.getInstanceId()));
        packInstanceReadReplicaPo(instancePO, userId);
        if ("dcc".equalsIgnoreCase(instancePO.getMachineType())) {
            instancePO.setDccHosts(instanceMapper.getMachineList(instancePO.getInstanceId()));
        }
        return instancePO;
    }

    @Override
    public InstancePO getAllStatusInstanceByUuid(String instanceUuid, String userId) {

        InstancePO instancePO = instanceMapper.getAllStatusInstanceByUuid(instanceUuid, userId);
        if (instancePO == null) {
            return null;
        }

        if (DELETED_INSTANCE_STATUS.equalsIgnoreCase(instancePO.getInstanceStatus())) {
            return instancePO;
        }

        instancePO.setSubnetIds(instanceMapper.selectSubnetListByInstanceId(instancePO.getInstanceId()));
        packInstanceReadReplicaPo(instancePO, userId);
        return instancePO;
    }

    @Override
    public InstancePO queryInstanceByInstanceUuid(String instanceUuid, String userId) {
        InstancePO instancePO = instanceMapper.queryInstanceByInstanceUuid(instanceUuid, userId);
        if (instancePO == null) {
            return null;
        }
        instancePO.setSubnetIds(instanceMapper.selectSubnetListByInstanceId(instancePO.getInstanceId()));
        packInstanceReadReplicaPo(instancePO, userId);
        return instancePO;
    }

    /**
     * 组装instance的read 和 replica部分
     *
     * @param instancePO
     * @param userId
     */
    private void packInstanceReadReplicaPo(InstancePO instancePO, String userId) {

        String instanceId = instancePO.getInstanceId();
        String sourceInstanceId = instancePO.getSourceInstanceId();

        if (StringUtils.isEmpty(sourceInstanceId)) {
            instancePO.getTopology().getMaster().add(instanceId);
            instancePO.getTopology().setReadReplica(instanceMapper
                    .queryReadReplicaInstanceIds(instanceId, userId));
            instancePO.getTopology().setRdsproxy(instanceMapper
                    .queryProxyInstanceIds(instanceId, userId));
        } else {
            instancePO.getTopology().getMaster().add(sourceInstanceId);
            instancePO.getTopology().setReadReplica(instanceMapper
                    .queryReadReplicaInstanceIds(sourceInstanceId, userId));
            instancePO.getTopology().setRdsproxy(instanceMapper
                    .queryProxyInstanceIds(sourceInstanceId, userId));
        }
    }

    @Override
    public List<InstancePO> getInstanceListByOrderUuid(String orderUuid, String userId) {
        return instanceMapper.getInstanceListByOrderUuid(orderUuid, userId);
    }

    @Override
    public void batchUpdateInstanceStatusByOrderId(String orderId, String status, String userId) {
        instanceMapper.batchUpdateInstanceStatusByOrderId(orderId, status, userId);
    }

    @Override
    public void insertOldInstance(InstancePO instancePO) {
        instanceMapper.insertOldInstance(instancePO);
    }

    @Override
    public List<InstancePO> getInstanceListByInstanceStatus(String instanceStatus, String userId, String machineType) {
        if ("DCC".equalsIgnoreCase(machineType)) {
            return instanceMapper.getInstanceListOnDccByInstanceStatus(instanceStatus, userId);
        } else {
            return instanceMapper.getInstanceListByInstanceStatus(instanceStatus, userId);
        }
    }

    @Override
    public int countNotDeleted(String instanceId, String userId) {
        return instanceMapper.countNotDeleted(instanceId, userId);
    }

    @Override
    public int countCreatingReplica(String instanceId, String userId) {
        return instanceMapper.countCreatingReplica(instanceId, userId);
    }

    @Override
    public int countProxy(String instanceId, String userId) {
        return instanceMapper.countProxy(instanceId, userId);
    }

    @Override
    public Map<String, String> getIdMapper(List<String> instanceUuids) {
        Map<String, String> result = new HashMap<>();
        if (instanceUuids == null || instanceUuids.size() == 0) {
            return result;
        }
        List<IdMapPO> idMapPOS = instanceMapper.idMapList(instanceUuids);
        for (IdMapPO idMapPO : idMapPOS) {
            result.put(idMapPO.getInstanceUuid(), idMapPO.getInstanceId());
        }
        return result;
    }

    // 只查询自己实例的id映射
    @Override
    public Map<String, String> getIdMapperV2(String userId,
                                             List<String> instanceUuids) {
        Map<String, String> result = new HashMap<>();
        if (instanceUuids == null || instanceUuids.size() == 0) {
            return result;
        }
        List<IdMapV2PO> idMapPOS = instanceMapper.idMapListV2(userId, instanceUuids);
        for (IdMapV2PO idMapPO : idMapPOS) {
            result.put(idMapPO.getInstanceUuid(), idMapPO.getInstanceId());
        }
        return result;
    }

    @Override
    public void deleteCreateFailedInstance(String orderId, String userId) {
        instanceMapper.deleteCreateFailedInstance(orderId, userId);
    }

    @Override
    @Transactional
    public void batchUpdateInstanceById(List<InstancePO> instancePOS) {
        for (InstancePO instancePO : instancePOS) {
            instanceMapper.updateInstanceById(instancePO);
        }
    }

    @Override
    public void deleteInstanceByInstanceUuids(List<String> instanceUuids, String userId) {
        instanceMapper.deleteInstanceByInstanceUuids(instanceUuids, userId);
    }



    @Override
    public InstancePO querySimpleInstanceById(String instanceId, String userId) {
        InstancePO instancePO = instanceMapper.queryInstanceByInstanceId(instanceId, userId);
        return instancePO;
    }

    @Override
    public List<MachinePO> getMachineListByUuid(String instanceUuid) {
        return instanceMapper.getMachineListByUuid(instanceUuid);
    }

    /**
     * 实例信息存储在后端，Console 服务仅需要存储实例长、短ID即可，上层业务逻辑对于该方法应避免调用并且适时删除已有调用
     *
     * @param valueMap valueMap
     * @param instanceUuid instanceUuid
     * @param userId userId
     */
    @Override
    @Deprecated
    public void updateInstanceByInstanceUuid(Map<String, Object> valueMap,
                                             String instanceUuid,
                                             String userId) {
        LOGGER.debug("updateInstanceByInstanceUuid, valueMap = {}, instanceUuid = {}, userId = {}",
                JsonUtil.toJson(valueMap), instanceUuid, userId);
        if (valueMap.containsKey("instance_status")) {
            return;
        }
        instanceMapper.updateInstanceByInstanceUuid(valueMap, instanceUuid, userId);
    }

    @Override
    public List<InstancePO> queryInstanceByInstanceIds(List<String> ids, String userId) {
        List<InstancePO> instancePOList = instanceMapper.queryInstanceByInstanceIds(ids, userId);
        if (instancePOList == null || instancePOList.size() <= 0) {
            return null;
        }
        for (InstancePO instancePO : instancePOList) {
            instancePO.setSubnetIds(instanceMapper.selectSubnetListByInstanceId(instancePO.getInstanceId()));
            packInstanceReadReplicaPo(instancePO, userId);
            if ("dcc".equalsIgnoreCase(instancePO.getMachineType())) {
                instancePO.setDccHosts(instanceMapper.getMachineList(instancePO.getInstanceId()));
            }
        }
        return instancePOList;
    }

    @Override
    public List<InstancePO> selectByIds(Collection<String> ids) {
        return instanceMapper.selectByIds(ids);
    }

    @Override
    public int updateInstanceUuid(String instanceId, String instanceUuid) {
        return instanceMapper.updateInstanceUuid(instanceId, instanceUuid);
    }
}
