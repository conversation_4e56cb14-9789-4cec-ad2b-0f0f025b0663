package com.baidu.bce.logic.rds.dao.mybatis;


import com.baidu.bce.logic.rds.dao.model.InnerConnectCheckPO;
import com.baidu.bce.logic.rds.dao.model.PublicNetworkConnectCheckPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/***
 *  连接检测列表
 */
@Component
public interface ConnectCheckMapper {

    /**
     * 插入一条内网连接检查
     * @param innerConnectCheckPO
     */
    void insertInnerConnect(@Param("innerConnectCheckPO") InnerConnectCheckPO innerConnectCheckPO);

    /**
     * 获取内网连接列表
     * @param instanceId
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<InnerConnectCheckPO> getInnerConnectList(@Param("instanceId") String instanceId,
                                                  @Param("pageNo")Integer pageNo,
                                                  @Param("pageSize")Integer pageSize);

    /**
     * 获取指定内网连接检查详情
     * @param connectId
     * @return
     */
    InnerConnectCheckPO getInnerDetail(@Param("connectId") String connectId);

    /**
     * 更新连接检查状态
     * @param connectId
     */
    void updateInnerConnectStatus(@Param("connectId") String connectId,
                                  @Param("connectStauts") String connectStatus,
                                  @Param("identicalVpc") String identicalVpc,
                                  @Param("bccInWhiteList") String bccInWhiteList);

    /**
     * 删除连接检查
     * @param connectId
     */
    void deleteConnect(@Param("connectId") String connectId);

    /**
     * 新增一条外网连接
     * @param publicNetworkConnectCheckPO
     */
    void insertPublicIp(@Param("publicNetworkConnectCheckPO") PublicNetworkConnectCheckPO publicNetworkConnectCheckPO);

    /**
     * 获取外网连接列表
     * @param instanceId
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<PublicNetworkConnectCheckPO> getOuterConnectList(@Param("instanceId") String instanceId,
                                                  @Param("pageNo")Integer pageNo,
                                                  @Param("pageSize")Integer pageSize);

    /**
     * 获取指定外网连接检查详情
     * @param connectId
     * @return
     */
    PublicNetworkConnectCheckPO getOuterDetail(@Param("connectId") String connectId);


    /**
     * 更新外网连接检测状态
     * @param connectId
     * @param connectStatus
     * @param inetStatus
     * @param bccInWhiteList
     */
    void updateOuterConnectStatus(@Param("connectId") String connectId,
                                  @Param("connectStauts") String connectStatus,
                                  @Param("inetStatus") String inetStatus,
                                  @Param("bccInWhiteList") String bccInWhiteList);

    /**
     * 删除外网连接检查
     * @param connectId
     */
    void deleteOuterConnect(@Param("connectId") String connectId);


}
