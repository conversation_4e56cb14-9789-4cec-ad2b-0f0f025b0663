package com.baidu.bce.logic.rds.dao.model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by luping03 on 17/12/29.
 */
public class NeedToSyncPO implements Serializable {
    private Long id;

    private Timestamp t0;

    private Timestamp t1;

    private String userId;

    private String instanceUuid;

    private String instanceStatus;

    private String eipStatus;

    private String replicationType;

    private Date lockTime;

    private String lockId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Timestamp getT0() {
        return t0;
    }

    public void setT0(Timestamp t0) {
        this.t0 = t0;
    }

    public Timestamp getT1() {
        return t1;
    }

    public void setT1(Timestamp t1) {
        this.t1 = t1;
    }

    public String getInstanceUuid() {
        return instanceUuid;
    }

    public void setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
    }

    public String getInstanceStatus() {
        return instanceStatus;
    }

    public void setInstanceStatus(String instanceStatus) {
        this.instanceStatus = instanceStatus;
    }

    public String getEipStatus() {
        return eipStatus;
    }

    public void setEipStatus(String eipStatus) {
        this.eipStatus = eipStatus;
    }

    public String getReplicationType() {
        return replicationType;
    }

    public void setReplicationType(String replicationType) {
        this.replicationType = replicationType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Date getLockTime() {
        return lockTime;
    }

    public void setLockTime(Date lockTime) {
        this.lockTime = lockTime;
    }

    public String getLockId() {
        return lockId;
    }

    public void setLockId(String lockId) {
        this.lockId = lockId == null ? null : lockId.trim();
    }

    @Override
    public String toString() {
        return "NeedToSync{"
                + "id=" + id
                + ", instanceUuid='" + instanceUuid + '\''
                + ", instanceStatus='" + instanceStatus + '\''
                + ", eipStatus='" + eipStatus + '\''
                + ", replicationType='" + replicationType + '\''
                + ", userId='" + userId + '\''
                + ", t0=" + t0
                + ", t1=" + t1
                + ", lockTime=" + lockTime
                + ", lockId='" + lockId + '\''
                + '}';
    }
}
