package com.baidu.bce.logic.rds.dao.mybatis;

import com.baidu.bce.logic.rds.dao.model.OrderNeedToSyncPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * Created by luping03 on 18/1/8.
 */
@Component
public interface OrderNeedToSyncMapper {

    List<OrderNeedToSyncPO> findOneSyncOrder(@Param("outLockDate") Date outLockDate);

    int unLockOneLineByResourceUUid(@Param("orderUuid") String orderUuid);

    int seizeLockOneLine(@Param("id") Long id, @Param("lockDate") Date lockDate,
                         @Param("outLockDate") Date outLockDate, @Param("lockId") String lockId);

    int lockOneLine(@Param("id") Long id, @Param("lockDate") Date lockDate,
                    @Param("lockId") String lockId, @Param("oldLockDate") Date oldLockDate);

    void updateOrderStatus(@Param("orderNeedToSyncPO")OrderNeedToSyncPO orderNeedToSyncPO);

    void insertOneOrder(@Param("orderNeedToSyncPO")OrderNeedToSyncPO orderNeedToSyncPO);
}
