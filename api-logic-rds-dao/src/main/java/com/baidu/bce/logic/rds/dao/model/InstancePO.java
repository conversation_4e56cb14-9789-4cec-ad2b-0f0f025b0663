package com.baidu.bce.logic.rds.dao.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by luping03 on 17/12/12.
 */

public class InstancePO implements Cloneable {
    private int id;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;

    private String userId = "";

    private String instanceId;
    private String instanceUuid;
    private String instanceName = "";

    private String engine = "";
    private String engineVersion = "";
    private Endpoint endpoint = new Endpoint();

    @Min(1)
    private double memoryCapacity;

    @Min(1)
    private int volumeCapacity;
    private int totalVolumeCapacity;
    private double usedStorage;
    private int cpuCount;

    private String instanceType = "";
    private String sourceInstanceId = "";
    private String instanceStatus = "";

    private String superUserFlag = "";
    private String replicationType = "";

    @Valid
    private SnapshotPolicy backupPolicy = new SnapshotPolicy();

    private boolean publiclyAccessible;
    private String eipStatus = "";

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp instanceCreateTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp instanceExpireTime;

    private Topology topology = new Topology();

    private String zoneNames = "";

    private String vpcUuid = "";

    private int nodeAmount;

    private String applicationType = ""; // 三节点增强版RDS：enhanced

    private String productType = "";

    private String resourceUuid = "";

    private String orderUuid = "";

    private String orderStatus = "";

    private String source = "";

    private List<SubnetPO> subnetIds = new ArrayList<>();

    private int oldInstance = 0; // 是否是历史代理实例  1：是－nodeAmount不展示

    private List<MachinePO> dccHosts;

    private String machineType = "";

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Endpoint {
        private Integer port = 0;
        private String address = "";
        private String vnetIp = "";
        private String inetIp = "";

        @Override
        public String toString() {
            return "Endpoint{"
                    + "port=" + port
                    + ", address='" + address + '\''
                    + ", vnetIp='" + vnetIp + '\''
                    + ", inetIp='" + inetIp + '\''
                    + '}';
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getVnetIp() {
            return vnetIp;
        }

        public void setVnetIp(String vnetIp) {
            this.vnetIp = vnetIp;
        }

        public String getInetIp() {
            return inetIp;
        }

        public void setInetIp(String inetIp) {
            this.inetIp = inetIp;
        }
    }

    public static class Topology {
        List<String> master = new ArrayList<>();
        List<String> readReplica = new ArrayList<>();
        List<String> rdsproxy = new ArrayList<>();

        public List<String> getMaster() {
            return master;
        }

        public void setMaster(List<String> master) {
            this.master = master;
        }

        public List<String> getReadReplica() {
            return readReplica;
        }

        public void setReadReplica(List<String> readReplica) {
            this.readReplica = readReplica;
        }

        public List<String> getRdsproxy() {
            return rdsproxy;
        }

        public void setRdsproxy(List<String> rdsproxy) {
            this.rdsproxy = rdsproxy;
        }
    }

    public int getTotalVolumeCapacity() {
        return totalVolumeCapacity;
    }

    public void setTotalVolumeCapacity(int totalVolumeCapacity) {
        this.totalVolumeCapacity = totalVolumeCapacity;
    }

    public List<MachinePO> getDccHosts() {
        return dccHosts;
    }

    public void setDccHosts(List<MachinePO> dccHosts) {
        this.dccHosts = dccHosts;
    }

    public String getMachineType() {
        return machineType;
    }

    public void setMachineType(String machineType) {
        this.machineType = machineType;
    }

    public int getOldInstance() {
        return oldInstance;
    }

    public void setOldInstance(int oldInstance) {
        this.oldInstance = oldInstance;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Timestamp getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Timestamp updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getInstanceUuid() {
        return instanceUuid;
    }

    public void setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getEngineVersion() {
        return engineVersion;
    }

    public void setEngineVersion(String engineVersion) {
        this.engineVersion = engineVersion;
    }

    public Endpoint getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(Endpoint endpoint) {
        this.endpoint = endpoint;
    }

    public int getVolumeCapacity() {
        return volumeCapacity;
    }

    public void setMemoryCapacity(double memoryCapacity) {
        this.memoryCapacity = memoryCapacity;
    }

    public double getMemoryCapacity() {
        return memoryCapacity;
    }

    public void setVolumeCapacity(int volumeCapacity) {
        this.volumeCapacity = volumeCapacity;
    }

    public double getUsedStorage() {
        return usedStorage;
    }

    public void setUsedStorage(double usedStorage) {
        this.usedStorage = usedStorage;
    }

    public void setUsedStorage(int usedStorage) {
        this.usedStorage = usedStorage;
    }

    public int getCpuCount() {
        return cpuCount;
    }

    public void setCpuCount(int cpuCount) {
        this.cpuCount = cpuCount;
    }

    public String getInstanceType() {
        return instanceType;
    }

    public void setInstanceType(String instanceType) {
        this.instanceType = instanceType;
    }

    public String getSourceInstanceId() {
        return sourceInstanceId;
    }

    public void setSourceInstanceId(String sourceInstanceId) {
        this.sourceInstanceId = sourceInstanceId;
    }

    public String getInstanceStatus() {
        return instanceStatus;
    }

    public void setInstanceStatus(String instanceStatus) {
        this.instanceStatus = instanceStatus;
    }

    public String getSuperUserFlag() {
        return superUserFlag;
    }

    public void setSuperUserFlag(String superUserFlag) {
        this.superUserFlag = superUserFlag;
    }

    public String getReplicationType() {
        return replicationType;
    }

    public void setReplicationType(String replicationType) {
        this.replicationType = replicationType;
    }

    public SnapshotPolicy getBackupPolicy() {
        return backupPolicy;
    }

    public void setBackupPolicy(SnapshotPolicy backupPolicy) {
        this.backupPolicy = backupPolicy;
    }

    public boolean isPubliclyAccessible() {
        return publiclyAccessible;
    }

    public void setPubliclyAccessible(boolean publiclyAccessible) {
        this.publiclyAccessible = publiclyAccessible;
    }

    public String getEipStatus() {
        return eipStatus;
    }

    public void setEipStatus(String eipStatus) {
        this.eipStatus = eipStatus;
    }

    public Timestamp getInstanceCreateTime() {
        return instanceCreateTime;
    }

    public void setInstanceCreateTime(Timestamp instanceCreateTime) {
        this.instanceCreateTime = instanceCreateTime;
    }

    public Timestamp getInstanceExpireTime() {
        return instanceExpireTime;
    }

    public void setInstanceExpireTime(Timestamp instanceExpireTime) {
        this.instanceExpireTime = instanceExpireTime;
    }

    public Topology getTopology() {
        return topology;
    }

    public void setTopology(Topology topology) {
        this.topology = topology;
    }

    public String getZoneNames() {
        return zoneNames;
    }

    public void setZoneNames(String zoneNames) {
        this.zoneNames = zoneNames;
    }

    public String getVpcUuid() {
        return vpcUuid;
    }

    public void setVpcUuid(String vpcUuid) {
        this.vpcUuid = vpcUuid;
    }

    public int getNodeAmount() {
        return nodeAmount;
    }

    public void setNodeAmount(int nodeAmount) {
        this.nodeAmount = nodeAmount;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getResourceUuid() {
        return resourceUuid;
    }

    public void setResourceUuid(String resourceUuid) {
        this.resourceUuid = resourceUuid;
    }

    public String getOrderUuid() {
        return orderUuid;
    }

    public void setOrderUuid(String orderUuid) {
        this.orderUuid = orderUuid;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public List<SubnetPO> getSubnetIds() {
        return subnetIds;
    }

    public void setSubnetIds(List<SubnetPO> subnetIds) {
        this.subnetIds = subnetIds;
    }
}
