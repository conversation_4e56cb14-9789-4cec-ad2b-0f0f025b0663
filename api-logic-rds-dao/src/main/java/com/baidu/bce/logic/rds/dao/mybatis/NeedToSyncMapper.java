package com.baidu.bce.logic.rds.dao.mybatis;

import com.baidu.bce.logic.rds.dao.model.NeedToSyncPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Created by luping03 on 17/12/29.
 */
@Component
public interface NeedToSyncMapper {
    List<NeedToSyncPO> findAll(@Param("lastActionTime") Timestamp lastActionTime);

//    int lockOneLine(@Param("id")Long id, @Param("lockDate")Date lockDate, @Param("lockId")String lockId);
//
//    int seizeLockOneLine(@Param("id")Long id, @Param("lockDate")Date lockDate,
//                         @Param("outLockDate")Date outLockDate, @Param("lockId")String lockId);

//    int unLockOneLineByResourceUUid(@Param("instanceUuid")String instanceUuid);

    List<NeedToSyncPO> findSyncData(@Param("outLockDate") Date outLockDate);

    int lockOneLine(@Param("id")Long id, @Param("lockDate")Date lockDate,
                    @Param("lockId")String lockId, @Param("oldLockDate")Date oldLockDate);

    int seizeLockOneLine(@Param("id")Long id, @Param("lockDate")Date lockDate,
                         @Param("outLockDate")Date outLockDate, @Param("lockId")String lockId);

    int unLockOneLineByResourceUUid(@Param("instanceUuid")String instanceUuid);
}
