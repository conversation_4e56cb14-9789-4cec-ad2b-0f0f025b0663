package com.baidu.bce.logic.rds.dao.model;

/**
 * Created by luping03 on 18/2/9.
 */
public class ZoneFlavorLimitPO {
    private int id;
    private String physicalZone;

    private int maxCpuCount;
    private int maxMemoryCapacity;
    private int maxVolumeCapacity;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPhysicalZone() {
        return physicalZone;
    }

    public void setPhysicalZone(String physicalZone) {
        this.physicalZone = physicalZone;
    }

    public int getMaxCpuCount() {
        return maxCpuCount;
    }

    public void setMaxCpuCount(int maxCpuCount) {
        this.maxCpuCount = maxCpuCount;
    }

    public int getMaxMemoryCapacity() {
        return maxMemoryCapacity;
    }

    public void setMaxMemoryCapacity(int maxMemoryCapacity) {
        this.maxMemoryCapacity = maxMemoryCapacity;
    }

    public int getMaxVolumeCapacity() {
        return maxVolumeCapacity;
    }

    public void setMaxVolumeCapacity(int maxVolumeCapacity) {
        this.maxVolumeCapacity = maxVolumeCapacity;
    }
}
