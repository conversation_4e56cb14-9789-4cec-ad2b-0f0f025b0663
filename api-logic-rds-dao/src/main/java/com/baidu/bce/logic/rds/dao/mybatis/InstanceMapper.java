package com.baidu.bce.logic.rds.dao.mybatis;

import com.baidu.bce.logic.rds.dao.model.IdMapPO;
import com.baidu.bce.logic.rds.dao.model.IdMapV2PO;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.MachinePO;
import com.baidu.bce.logic.rds.dao.model.SubnetPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by luping03 on 17/12/11.
 */
@Component
public interface InstanceMapper {
    void batchInsertInstances(@Param("instancePOs") List<InstancePO> instancePOs);

    String queryInstanceId(@Param("instanceId") String instanceId);

    String queryInstanceUuid(@Param("instanceId")String instanceId);

    List<IdMapPO> idMapList(@Param("longIds") List<String> longIds);

    List<IdMapV2PO> idMapListV2(@Param("userId") String userId,
                                @Param("longIds") List<String> longIds);

    void updateSyncData(@Param("instancePO")InstancePO instancePO);

    InstancePO queryInstanceByInstanceId(@Param("instanceId")String instanceId, @Param("userId")String userId);

    InstancePO queryAllInstanceByInstanceId(@Param("instanceId")String instanceId, @Param("userId")String userId);

    InstancePO getAllStatusInstanceByUuid(@Param("instanceUuid")String instanceUuid, @Param("userId")String userId);

    InstancePO queryInstanceByInstanceUuid(@Param("instanceUuid")String instanceUuid, @Param("userId")String userId);

    List<SubnetPO> selectSubnetListByInstanceId(@Param("instanceId")String instanceId);

    void deleteCreateFailedInstance(@Param("orderUuid") String orderId, @Param("userId")String userId);

    void updateInstanceById(@Param("instancePO") InstancePO instancePO);

    List<InstancePO> getInstanceListByOrderUuid(@Param("orderUuid") String orderUuid, @Param("userId")String userId);

    void batchUpdateInstanceStatusByOrderId(@Param("orderUuid") String orderId,
                                            @Param("status") String status,
                                            @Param("userId") String userId);

    void insertOldInstance(@Param("instancePO") InstancePO instancePO);

    void batchInsertSubnetPOs(@Param("subnetPOs") List<SubnetPO> subnetPOs);

    void batchInsertMachinePOs(@Param("machinePOs")List<MachinePO> machinePOs);

    List<InstancePO> getInstanceListByInstanceStatus(@Param("instanceStatus") String instanceStatus,
                                                     @Param("userId")String userId);

    List<InstancePO> getInstanceListOnDccByInstanceStatus(@Param("instanceStatus") String instanceStatus,
                                                          @Param("userId")String userId);

    List<String> queryReadReplicaInstanceIds(@Param("masterId") String masterId,
                                             @Param("userId")String userId);

    List<String> queryProxyInstanceIds(@Param("masterId") String masterId,
                                       @Param("userId")String userId);
    int countNotDeleted(@Param("instanceId") String instanceId,
                        @Param("userId")String userId);

    int countCreatingReplica(@Param("instanceId") String instanceId,
                             @Param("userId")String userId);

    int countProxy(@Param("instanceId") String instanceId,
                   @Param("userId")String userId);

    void deleteInstanceByInstanceUuids(@Param("instanceUuids") List<String> instanceUuids,
                                       @Param("userId")String userId);



    List<MachinePO> getMachineList(@Param("instanceId")String instanceId);

    List<MachinePO> getMachineListByUuid(@Param("instanceUuid")String instanceUuid);

    void updateInstanceByInstanceUuid(@Param("valueMap") Map<String, Object> valueMap,
                                      @Param("instanceUuid") String instanceUuid,
                                      @Param("userId")String userId);
    List<String> getMasterShortIds(@Param("ids") Set<String> ids);

    List<String> getShortIdsByOrderId(@Param("orderId") String orderId);

    List<InstancePO> queryInstanceByInstanceIds(@Param("ids") List<String> ids, @Param("userId") String userId);

    /**
     * 返回所有用户的accountId
     * @return
     */
    List<String> listAllAccountId();

    List<InstancePO> selectByIds(@Param("ids") Collection<String> ids);

    /**
     * 设置实例长 ID，仅在订单执行器 execute 阶段，得到实例长 ID 信息之后调用该方法
     *
     * @param instanceId 实例短 ID
     * @param instanceUuid 实例长 ID
     * @return affected rows
     */
    int updateInstanceUuid(@Param("instanceId") String instanceId, @Param("instanceUuid") String instanceUuid);

}
