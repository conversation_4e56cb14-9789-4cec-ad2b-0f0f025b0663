package com.baidu.bce.logic.rds.dao.mybatis;

import com.baidu.bce.logic.rds.dao.model.CustomMonitorPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface CustomMonitorMapper {

    /**
     * 插入一条自定义监控视图
     * @param customMonitorPO
     */
    void insertCustomMonitor(@Param("customMinitorPO") CustomMonitorPO customMonitorPO);

    /**
     * 获取指定账户 && 指定实例引擎类型的自定义监控视图
     * @param accountId
     * @param engine
     * @return
     */
    CustomMonitorPO getCustomMonitor(@Param("accountId") String accountId,
                                     @Param("engine")String engine);

    /**
     * 更新指定账户 && 指定引擎类型的自定义监控视图
     * @param accountId
     * @param engine
     * @Param extra
     */
    void updateCustomMonitor(@Param("accountId") String accountId,
                             @Param("engine") String engine,
                             @Param("extra") String extra);

    /**
     * 删除指定账户 && 指定引擎类型的自定义监控视图
     * @param accountId
     * @param engine
     */
    void deleteCustomMonitor(@Param("accountId") String accountId,
                             @Param("engine") String engine);
}
