package com.baidu.bce.logic.rds.dao;

import com.baidu.bce.logic.rds.dao.model.IdMapPO;
import com.baidu.bce.logic.rds.dao.model.InstancePO;
import com.baidu.bce.logic.rds.dao.model.MachinePO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by luping03 on 17/12/11.
 */
public interface InstanceDao {
    void batchInsertInstances(List<InstancePO> instancePOs);

    String queryInstanceId(String instanceId);

    String queryInstanceUuid(String instanceId);

    List<IdMapPO> idMapList(List<String> longIds);

    List<String> getMasterShortIds(Set<String> ids);

    List<String> getShortIdsByOrderId(String orderId);

    void updateSyncData(InstancePO instancePO);

    InstancePO getAllStatusInstanceByUuid(String instanceId, String userId);

    InstancePO queryInstanceByInstanceId(String instanceId, String userId);

    InstancePO queryAllInstanceByInstanceId(String instanceId, String userId);

    InstancePO queryInstanceByInstanceUuid(String instanceUuid, String userId);

    void deleteCreateFailedInstance(String orderId, String userId);

    void batchUpdateInstanceById(List<InstancePO> instancePOS);

    List<InstancePO> getInstanceListByOrderUuid(String orderUuid, String userId);

    void batchUpdateInstanceStatusByOrderId(String orderId, String status, String userId);

    void insertOldInstance(InstancePO instancePO);

    List<InstancePO> getInstanceListByInstanceStatus(String instanceStatus, String userId, String machineType);

    int countNotDeleted(String instanceId, String userId);

    int countCreatingReplica(String instanceId, String userId);

    int countProxy(String instanceId, String userId);

    Map<String, String> getIdMapper(List<String> instanceUuids);

    Map<String, String> getIdMapperV2(String userId, List<String> instanceUuids);

    void deleteInstanceByInstanceUuids(List<String> instanceUuids,
                                       String userId);



    InstancePO querySimpleInstanceById(String instanceId, String userId);

    List<MachinePO> getMachineListByUuid(String instanceUuid);

    @Deprecated
    void updateInstanceByInstanceUuid(Map<String, Object> valueMap, String instanceUuid, String userId);

    List<InstancePO> queryInstanceByInstanceIds(List<String> ids, String userId);

    List<InstancePO> selectByIds(Collection<String> ids);

    int updateInstanceUuid(String instanceId, String instanceUuid);

}