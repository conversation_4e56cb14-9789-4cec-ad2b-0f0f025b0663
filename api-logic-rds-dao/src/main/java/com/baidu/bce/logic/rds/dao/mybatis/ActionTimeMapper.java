package com.baidu.bce.logic.rds.dao.mybatis;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.sql.*;

/**
 * Created by luping03 on 18/1/1.
 */
@Component
public interface ActionTimeMapper {
    void updateActionTimeByResourceType(@Param("lastUpdateTime") final Timestamp lastUpdateTime,
                                        @Param("resourceType") final String resourceType);

    Timestamp queryActionTime(@Param("resourceType")final String resourceType);

    void addActionTime(@Param("actionTime")Timestamp actionTime,
                       @Param("resourceType")String resourceType);
}
