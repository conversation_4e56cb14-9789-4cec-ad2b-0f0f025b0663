package com.baidu.bce.logic.rds.dao.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.sql.Timestamp;

/**
 * Created by luping03 on 18/2/9.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlavorPO {
    private int id;

    private Timestamp insertTime;

    private int cpuCount;
    private double memoryCapacity;
    private int minVolumeCapacity;
    private int maxVolumeCapacity;
    private int volumeCapacity;

    private String engine;
    private String applicationType;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Timestamp getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Timestamp insertTime) {
        this.insertTime = insertTime;
    }

    public int getCpuCount() {
        return cpuCount;
    }

    public void setCpuCount(int cpuCount) {
        this.cpuCount = cpuCount;
    }

    public double getMemoryCapacity() {
        return memoryCapacity;
    }

    public void setMemoryCapacity(double memoryCapacity) {
        this.memoryCapacity = memoryCapacity;
    }

    public int getMinVolumeCapacity() {
        return minVolumeCapacity;
    }

    public void setMinVolumeCapacity(int minVolumeCapacity) {
        this.minVolumeCapacity = minVolumeCapacity;
    }

    public int getMaxVolumeCapacity() {
        return maxVolumeCapacity;
    }

    public void setMaxVolumeCapacity(int maxVolumeCapacity) {
        this.maxVolumeCapacity = maxVolumeCapacity;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }

    public int getVolumeCapacity() {
        return volumeCapacity;
    }

    public void setVolumeCapacity(int volumeCapacity) {
        this.volumeCapacity = volumeCapacity;
    }
}
