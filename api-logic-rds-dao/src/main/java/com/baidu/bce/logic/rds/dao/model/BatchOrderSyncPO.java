package com.baidu.bce.logic.rds.dao.model;

import java.sql.Timestamp;

/**
 * 批量变配时需要同步的订单
 */

public class BatchOrderSyncPO {
    private Long id;

    private Timestamp updatedTime;

    private Timestamp createTime;

    // 批量变配订单的标识，可通过实例 ID 组合而成
    private String extraOrders;

    // 1-刚记录为批量变配订单；2-订单已正确进入订单执行器流程
    private int executeStep;

    private String userId;

    private String orderUuid;

    private String orderStatus;

    private String orderType;

    // 新加实例 ID 字段是为了在订单执行处可直接通过db 分出主实例与只读实例
    private String instanceId;

    private String batchOrders;

    public String getBatchOrders() {
        return batchOrders;
    }

    public void setBatchOrders(String batchOrders) {
        this.batchOrders = batchOrders;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Timestamp getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Timestamp updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getExtraOrders() {
        return extraOrders;
    }

    public void setExtraOrders(String extraOrders) {
        this.extraOrders = extraOrders;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrderUuid() {
        return orderUuid;
    }

    public void setOrderUuid(String orderUuid) {
        this.orderUuid = orderUuid;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public int getExecuteStep() {
        return executeStep;
    }

    public void setExecuteStep(int executeStep) {
        this.executeStep = executeStep;
    }
}
