#!/bin/sh

PATH=/home/<USER>/usr/bin/ccache-gcc:/home/<USER>/usr/bin:/home/<USER>/buildkit/nodejs/nodejs-0.8.20/bin:/usr/maven/apache-maven-2.2.0/bin:/usr/ant/apache-ant-1.6.5/bin:/usr/ant/apache-ant-1.7.1/bin:/usr/maven/apache-maven-2.2.0/bin:/usr/ant/apache-ant-1.6.5/bin:/usr/maven/apache-maven-2.2.0/bin:/usr/ant/apache-ant-1.6.5/bin:/usr/kerberos/bin:/usr/local/bin:/bin:/usr/bin:/usr/X11R6/bin:/opt/bin:/home/<USER>/bin
export PATH
export JAVA_HOME=$BUILD_KIT_PATH/java/jdk-1.7u60
export MAVEN_3_0_4=/usr/maven/apache-maven-3.0.4/bin
export PATH=$BUILD_KIT_PATH/nodejs/nodejs-0.10.24/bin/:${JAVA_HOME}/bin:/usr/maven/apache-maven-3.0.4/bin:$PATH;
sh webbuild.sh && sh build.sh -q
